import React, { useState, useEffect } from 'react';
import { X, User, Star, MapPin, Clock, Phone, Truck, CheckCircle, AlertCircle } from 'lucide-react';
import orderApi from '../../utils/orderApi';

const ManualAgentSelector = ({ order, isOpen, onClose, onAssignSuccess }) => {
  const [agents, setAgents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [assigning, setAssigning] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isOpen && order) {
      loadAvailableAgents();
    }
  }, [isOpen, order]);

  const loadAvailableAgents = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await orderApi.getAvailableAgents(order.id);
      if (response.success) {
        setAgents(response.data.agents || []);
      } else {
        setError(response.error || 'Failed to load available agents');
      }
    } catch (err) {
      setError('Failed to load available agents');
      console.error('Error loading agents:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignAgent = async (agentId) => {
    setAssigning(true);
    setError(null);
    
    try {
      const response = await orderApi.assignAgent(order.id, agentId);
      if (response.success) {
        onAssignSuccess(response.data);
        onClose();
      } else {
        setError(response.error || 'Failed to assign agent');
      }
    } catch (err) {
      setError('Failed to assign agent');
      console.error('Error assigning agent:', err);
    } finally {
      setAssigning(false);
    }
  };

  const formatDistance = (distanceInfo) => {
    if (!distanceInfo) return 'Distance unknown';
    return `${distanceInfo.distance_km?.toFixed(1)} km`;
  };

  const formatETA = (distanceInfo) => {
    if (!distanceInfo || !distanceInfo.duration_minutes) return 'ETA unknown';
    const minutes = Math.round(distanceInfo.duration_minutes);
    return `${minutes} min`;
  };

  const getVehicleIcon = (vehicleType) => {
    switch (vehicleType) {
      case 'motorcycle':
        return '🏍️';
      case 'bicycle':
        return '🚲';
      case 'car':
        return '🚗';
      case 'scooter':
        return '🛵';
      default:
        return '🚚';
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Select Delivery Agent
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Order #{order?.id} - Choose the best agent for delivery
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
              <AlertCircle className="text-red-500 mr-2" size={20} />
              <span className="text-red-700">{error}</span>
            </div>
          )}

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Loading available agents...</span>
            </div>
          ) : agents.length === 0 ? (
            <div className="text-center py-12">
              <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Available Agents
              </h3>
              <p className="text-gray-600">
                There are currently no delivery agents available in your area.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                Found {agents.length} available agent{agents.length !== 1 ? 's' : ''} in your area
              </div>
              
              {agents.map((agent) => (
                <div
                  key={agent.id}
                  className={`border rounded-lg p-4 transition-all cursor-pointer hover:shadow-md ${
                    selectedAgent === agent.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedAgent(agent.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* Agent Info */}
                      <div className="flex items-center mb-3">
                        <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mr-3">
                          <User size={24} className="text-gray-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{agent.name}</h3>
                          <div className="flex items-center text-sm text-gray-600">
                            <span className="mr-2">{getVehicleIcon(agent.vehicle_type)}</span>
                            <span className="capitalize">{agent.vehicle_type}</span>
                            {agent.vehicle_model && (
                              <span className="ml-1">- {agent.vehicle_model}</span>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Stats Row */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                        <div className="flex items-center">
                          <Star className="text-yellow-500 mr-1" size={16} />
                          <span className="text-sm font-medium">{agent.rating.toFixed(1)}</span>
                        </div>
                        <div className="flex items-center">
                          <CheckCircle className="text-green-500 mr-1" size={16} />
                          <span className="text-sm">{agent.total_deliveries} deliveries</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="text-blue-500 mr-1" size={16} />
                          <span className="text-sm">{formatDistance(agent.distance_info)}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="text-orange-500 mr-1" size={16} />
                          <span className="text-sm">ETA: {formatETA(agent.distance_info)}</span>
                        </div>
                      </div>

                      {/* Additional Info */}
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <div className="flex items-center">
                          <Phone size={14} className="mr-1" />
                          <span>{agent.phone}</span>
                        </div>
                        <div className="flex items-center">
                          <Truck size={14} className="mr-1" />
                          <span>{agent.license_plate}</span>
                        </div>
                        <div className={`px-2 py-1 rounded-full text-xs ${
                          agent.is_online 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {agent.is_online ? 'Online' : 'Offline'}
                        </div>
                      </div>
                    </div>

                    {/* Select Button */}
                    <div className="ml-4">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAssignAgent(agent.id);
                        }}
                        disabled={assigning}
                        className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                          selectedAgent === agent.id
                            ? 'bg-blue-600 text-white hover:bg-blue-700'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        } ${assigning ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        {assigning ? 'Assigning...' : 'Select'}
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            Agents are sorted by distance from your restaurant
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={loadAvailableAgents}
              disabled={loading}
              className="px-4 py-2 text-blue-700 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
            >
              {loading ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManualAgentSelector;
