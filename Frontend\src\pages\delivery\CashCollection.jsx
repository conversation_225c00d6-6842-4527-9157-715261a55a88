import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import {
  mockDeliveryOrders,
  mockDeliveryAgents,
} from "../../data/deliveryAgents";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { Clock, DollarSign, Package, AlertCircle } from "lucide-react";

function CashCollection() {
  const { user } = useAuth();
  const [agent, setAgent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [summary, setSummary] = useState({
    totalCollected: 0,
    pendingCollection: 0,
    totalOrders: 0,
    collectedOrders: 0,
  });

  useEffect(() => {
    // Simulate API call to get agent data and orders
    setTimeout(() => {
      const foundAgent =
        mockDeliveryAgents.find((a) => a.userId === user.id) ||
        mockDeliveryAgents[0];
      setAgent(foundAgent);

      // Get orders for this agent
      const agentOrders = mockDeliveryOrders.filter(
        (o) => o.agentId === foundAgent.id
      );
      setOrders(agentOrders);

      // Calculate summary
      const collectedOrders = agentOrders.filter((o) => o.isCashCollected);
      const pendingOrders = agentOrders.filter(
        (o) => !o.isCashCollected && o.status === "delivered"
      );

      setSummary({
        totalCollected: collectedOrders.reduce(
          (sum, o) => sum + (o.orderDetails?.totalAmount || o.orderTotal || 0),
          0
        ),
        pendingCollection: pendingOrders.reduce(
          (sum, o) => sum + (o.orderDetails?.totalAmount || o.orderTotal || 0),
          0
        ),
        totalOrders: agentOrders.length,
        collectedOrders: collectedOrders.length,
      });

      setLoading(false);
    }, 1000);
  }, [user]);

  const formatCurrency = (amount) => {
    return `$${amount.toFixed(2)}`;
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleCashCollected = (order) => {
    // In a real app, this would be an API call
    const updatedOrders = orders.map((o) => {
      if (o.id === order.id) {
        return {
          ...o,
          isCashCollected: true,
          cashCollectedBy: agent.id,
          cashCollectedAt: new Date().toISOString(),
        };
      }
      return o;
    });

    setOrders(updatedOrders);

    // Update summary
    const collectedOrders = updatedOrders.filter((o) => o.isCashCollected);
    const pendingOrders = updatedOrders.filter(
      (o) => !o.isCashCollected && o.status === "delivered"
    );

    setSummary({
      totalCollected: collectedOrders.reduce(
        (sum, o) => sum + (o.orderDetails?.totalAmount || o.orderTotal || 0),
        0
      ),
      pendingCollection: pendingOrders.reduce(
        (sum, o) => sum + (o.orderDetails?.totalAmount || o.orderTotal || 0),
        0
      ),
      totalOrders: updatedOrders.length,
      collectedOrders: collectedOrders.length,
    });
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500'></div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
        <Card>
          <div className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <div className='text-sm text-gray-600'>Total Collected</div>
              <DollarSign size={20} className='text-green-500' />
            </div>
            <div className='text-2xl font-bold'>
              {formatCurrency(summary.totalCollected)}
            </div>
            <div className='text-sm text-gray-500 mt-1'>
              {summary.collectedOrders} orders
            </div>
          </div>
        </Card>

        <Card>
          <div className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <div className='text-sm text-gray-600'>Pending Collection</div>
              <AlertCircle size={20} className='text-yellow-500' />
            </div>
            <div className='text-2xl font-bold'>
              {formatCurrency(summary.pendingCollection)}
            </div>
            <div className='text-sm text-gray-500 mt-1'>
              {summary.totalOrders - summary.collectedOrders} orders
            </div>
          </div>
        </Card>

        <Card>
          <div className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <div className='text-sm text-gray-600'>Total Orders</div>
              <Package size={20} className='text-blue-500' />
            </div>
            <div className='text-2xl font-bold'>{summary.totalOrders}</div>
            <div className='text-sm text-gray-500 mt-1'>All time</div>
          </div>
        </Card>

        <Card>
          <div className='p-4'>
            <div className='flex items-center justify-between mb-2'>
              <div className='text-sm text-gray-600'>Collection Rate</div>
              <Clock size={20} className='text-purple-500' />
            </div>
            <div className='text-2xl font-bold'>
              {summary.totalOrders > 0
                ? `${Math.round(
                    (summary.collectedOrders / summary.totalOrders) * 100
                  )}%`
                : "0%"}
            </div>
            <div className='text-sm text-gray-500 mt-1'>Success rate</div>
          </div>
        </Card>
      </div>

      <Card>
        <div className='p-4'>
          <h2 className='text-lg font-semibold mb-4'>
            Cash Collection History
          </h2>
          <div className='overflow-x-auto'>
            <table className='min-w-full divide-y divide-gray-200'>
              <thead className='bg-gray-50'>
                <tr>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Order
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Amount
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Status
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Collected At
                  </th>
                  <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white divide-y divide-gray-200'>
                {orders.map((order) => (
                  <tr key={order.id}>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm font-medium text-gray-900'>
                        Order #
                        {order.orderId?.split("-")[1] || order.id || "N/A"}
                      </div>
                      <div className='text-sm text-gray-500'>
                        {order.restaurantName} → {order.customerName}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      <div className='text-sm font-medium text-gray-900'>
                        {formatCurrency(
                          order.orderDetails?.totalAmount ||
                            order.orderTotal ||
                            0
                        )}
                      </div>
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap'>
                      {order.isCashCollected ? (
                        <Badge className='bg-green-100 text-green-800'>
                          Collected
                        </Badge>
                      ) : order.status === "delivered" ? (
                        <Badge className='bg-yellow-100 text-yellow-800'>
                          Pending
                        </Badge>
                      ) : (
                        <Badge className='bg-gray-100 text-gray-800'>
                          {order.status}
                        </Badge>
                      )}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                      {order.cashCollectedAt
                        ? formatDate(order.cashCollectedAt)
                        : "-"}
                    </td>
                    <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
                      {!order.isCashCollected &&
                        order.status === "delivered" && (
                          <Button
                            variant='primary'
                            size='small'
                            onClick={() => handleCashCollected(order)}
                          >
                            Mark as Collected
                          </Button>
                        )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default CashCollection;
