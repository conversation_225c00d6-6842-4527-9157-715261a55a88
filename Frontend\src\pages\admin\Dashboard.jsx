import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { adminApi } from "../../services/adminApi";
import {
  Users,
  ShoppingBag,
  Home,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
  Package,
  BarChart2,
  Store,
  Building2,
  Truck,
  Star,
  Eye,
  ArrowRight,
  Calendar,
  Activity,
  Bell,
  Settings,
  RefreshCw,
  Download,
  Filter,
  Search,
  MapPin,
  Phone,
  Mail,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  Shield,
  Globe,
  Database,
  Server,
  Wifi,
} from "lucide-react";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { useAuth } from "../../context/AuthContext";
import { mockOrders } from "../../data/orders";
import { mockRestaurants } from "../../data/restaurants";
import { mockUsers } from "../../data/users";

// Chart data
const revenueData = [
  { name: "Jan", revenue: 4200, orders: 120, users: 45 },
  { name: "Feb", revenue: 5100, orders: 145, users: 52 },
  { name: "Mar", revenue: 6300, orders: 180, users: 68 },
  { name: "Apr", revenue: 7800, orders: 220, users: 75 },
  { name: "May", revenue: 6900, orders: 195, users: 82 },
  { name: "Jun", revenue: 8500, orders: 240, users: 91 },
  { name: "Jul", revenue: 9200, orders: 265, users: 98 },
  { name: "Aug", revenue: 10100, orders: 290, users: 105 },
  { name: "Sep", revenue: 11500, orders: 320, users: 112 },
  { name: "Oct", revenue: 10800, orders: 305, users: 118 },
  { name: "Nov", revenue: 12200, orders: 340, users: 125 },
  { name: "Dec", revenue: 15800, orders: 420, users: 135 },
];

const orderStatusData = [
  { name: "Delivered", value: 68, color: "#10B981" },
  { name: "In Progress", value: 22, color: "#F59E0B" },
  { name: "Cancelled", value: 7, color: "#EF4444" },
  { name: "Pending", value: 3, color: "#6B7280" },
];

const topRestaurantsData = [
  { name: "Kabul Kitchen", orders: 145, revenue: 2850 },
  { name: "Herat Delights", orders: 132, revenue: 2640 },
  { name: "Mazar Grill", orders: 118, revenue: 2360 },
  { name: "Kandahar Spice", orders: 95, revenue: 1900 },
  { name: "Afghan Palace", orders: 87, revenue: 1740 },
];

function AdminDashboard() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState("month");

  const [metrics, setMetrics] = useState({
    totalUsers: 0,
    totalRestaurants: 0,
    totalOrders: 0,
    totalRevenue: 0,
    pendingApprovals: 0,
    pendingDeliveryApprovals: 0,
    activeDeliveryAgents: 0,
    todayOrders: 0,
    todayRevenue: 0,
    avgOrderValue: 0,
    customerSatisfaction: 0,
    monthlyGrowth: {
      users: 12.5,
      restaurants: 8.3,
      orders: 15.7,
      revenue: 18.2,
    },
  });

  const [recentActivities, setRecentActivities] = useState([]);
  const [recentOrders, setRecentOrders] = useState([]);
  const [pendingRestaurants, setPendingRestaurants] = useState([]);
  const [topRestaurants, setTopRestaurants] = useState([]);
  const [systemStatus, setSystemStatus] = useState({
    status: "healthy",
    lastChecked: new Date(),
    issues: [],
    uptime: "99.9%",
    responseTime: "120ms",
    activeConnections: 1247,
    serverLoad: 45,
  });

  const [notifications, setNotifications] = useState([]);
  const [alerts, setAlerts] = useState([]);

  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load delivery agent statistics
      const deliveryStats = await adminApi.getDeliveryAgentStats();

      setMetrics({
        totalUsers: 3245,
        totalRestaurants: 128,
        totalOrders: 8420,
        totalRevenue: 156750,
        pendingApprovals: 7,
        pendingDeliveryApprovals: deliveryStats.success
          ? deliveryStats.data.pending
          : 0,
        activeDeliveryAgents: deliveryStats.success
          ? deliveryStats.data.active
          : 0,
        todayOrders: 186,
        todayRevenue: 4250,
        avgOrderValue: 18.6,
        customerSatisfaction: 4.7,
      });

      setRecentActivities([
        {
          id: 1,
          type: "order",
          message: "New order #ORD-8421 from Ahmad Khan",
          time: "2 minutes ago",
          icon: <ShoppingBag size={16} />,
          color: "text-blue-600",
          amount: "$24.50",
        },
        {
          id: 2,
          type: "restaurant",
          message: "Kabul Kitchen updated their menu",
          time: "15 minutes ago",
          icon: <Store size={16} />,
          color: "text-green-600",
        },
        {
          id: 3,
          type: "user",
          message: "12 new users registered today",
          time: "1 hour ago",
          icon: <Users size={16} />,
          color: "text-purple-600",
        },
        {
          id: 4,
          type: "delivery",
          message: "Delivery agent Mohammad completed 15 deliveries",
          time: "2 hours ago",
          icon: <Truck size={16} />,
          color: "text-orange-600",
        },
        {
          id: 5,
          type: "approval",
          message: "Restaurant 'Herat Delights' approved",
          time: "3 hours ago",
          icon: <CheckCircle size={16} />,
          color: "text-green-600",
        },
        {
          id: 6,
          type: "system",
          message: "System backup completed successfully",
          time: "4 hours ago",
          icon: <Database size={16} />,
          color: "text-gray-600",
        },
      ]);

      setSystemStatus({
        status: "healthy",
        lastChecked: new Date(),
        issues: [],
        uptime: "99.9%",
        responseTime: "120ms",
        activeConnections: 1247,
        serverLoad: 45,
      });

      setLoading(false);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage) => {
    const value = parseFloat(percentage);
    if (isNaN(value)) return "";
    const sign = value >= 0 ? "+" : "";
    const color = value >= 0 ? "text-green-600" : "text-red-600";
    const icon =
      value >= 0 ? <TrendingUp size={14} /> : <TrendingDown size={14} />;
    return (
      <span className={`flex items-center text-xs ${color}`}>
        {icon} {sign}
        {value.toFixed(1)}%
      </span>
    );
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString();
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case "restaurant_approval":
        return <Home size={20} className='text-blue-500' />;
      case "order":
        return <ShoppingBag size={20} className='text-green-500' />;
      case "user":
        return <Users size={20} className='text-purple-500' />;
      case "system":
        return <BarChart2 size={20} className='text-orange-500' />;
      default:
        return <AlertCircle size={20} className='text-gray-500' />;
    }
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500'></div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Dashboard Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>Dashboard</h1>
          <p className='mt-2 text-gray-600'>
            Welcome back, {user?.name}! Here's what's happening with your
            platform today.
          </p>
        </div>
        <div className='flex items-center space-x-4'>
          <select
            className='px-4 py-2 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
          >
            <option value='today'>Today</option>
            <option value='week'>Last 7 Days</option>
            <option value='month'>Last 30 Days</option>
            <option value='quarter'>Last 3 Months</option>
          </select>
          <Button
            variant='outline'
            icon={<RefreshCw size={18} />}
            onClick={loadDashboardData}
            disabled={loading}
            className='border-gray-300 text-gray-700 hover:bg-gray-50'
          >
            Refresh
          </Button>
        </div>
      </div>
      {/* Key Performance Indicators */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        {/* Total Revenue */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl'>
              <DollarSign size={24} className='text-white' />
            </div>
            <div className='flex items-center space-x-1 text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full'>
              <TrendingUp size={14} />
              <span className='text-sm font-medium'>+12.5%</span>
            </div>
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium text-gray-600'>Total Revenue</p>
            <p className='text-3xl font-bold text-gray-900'>
              {formatCurrency(metrics.totalRevenue)}
            </p>
            <p className='text-sm text-gray-500'>
              Today:{" "}
              <span className='font-medium text-gray-700'>
                {formatCurrency(metrics.todayRevenue)}
              </span>
            </p>
          </div>
        </div>

        {/* Total Orders */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl'>
              <ShoppingBag size={24} className='text-white' />
            </div>
            <div className='flex items-center space-x-1 text-blue-600 bg-blue-50 px-2 py-1 rounded-full'>
              <TrendingUp size={14} />
              <span className='text-sm font-medium'>+8.2%</span>
            </div>
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium text-gray-600'>Total Orders</p>
            <p className='text-3xl font-bold text-gray-900'>
              {metrics.totalOrders.toLocaleString()}
            </p>
            <p className='text-sm text-gray-500'>
              Today:{" "}
              <span className='font-medium text-gray-700'>
                {metrics.todayOrders}
              </span>
            </p>
          </div>
        </div>

        {/* Active Users */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl'>
              <Users size={24} className='text-white' />
            </div>
            <div className='flex items-center space-x-1 text-purple-600 bg-purple-50 px-2 py-1 rounded-full'>
              <TrendingUp size={14} />
              <span className='text-sm font-medium'>+15.3%</span>
            </div>
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium text-gray-600'>Active Users</p>
            <p className='text-3xl font-bold text-gray-900'>
              {metrics.totalUsers.toLocaleString()}
            </p>
            <p className='text-sm text-gray-500'>
              New today: <span className='font-medium text-gray-700'>24</span>
            </p>
          </div>
        </div>

        {/* Restaurants */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl'>
              <Store size={24} className='text-white' />
            </div>
            {metrics.pendingApprovals > 0 ? (
              <div className='flex items-center space-x-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-full'>
                <Clock size={14} />
                <span className='text-sm font-medium'>
                  {metrics.pendingApprovals} pending
                </span>
              </div>
            ) : (
              <div className='flex items-center space-x-1 text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full'>
                <CheckCircle size={14} />
                <span className='text-sm font-medium'>All approved</span>
              </div>
            )}
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium text-gray-600'>
              Active Restaurants
            </p>
            <p className='text-3xl font-bold text-gray-900'>
              {metrics.totalRestaurants}
            </p>
            <p className='text-sm text-gray-500'>
              <button
                onClick={() => navigate("/admin/restaurant-approvals")}
                className='font-medium text-orange-600 hover:text-orange-700 transition-colors'
              >
                Manage approvals →
              </button>
            </p>
          </div>
        </div>
      </div>

      {/* Delivery Agent Management Section */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Delivery Agent Approvals */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl'>
              <Truck size={24} className='text-white' />
            </div>
            {metrics.pendingDeliveryApprovals > 0 ? (
              <div className='flex items-center space-x-1 text-amber-600 bg-amber-50 px-2 py-1 rounded-full'>
                <Clock size={14} />
                <span className='text-sm font-medium'>
                  {metrics.pendingDeliveryApprovals} pending
                </span>
              </div>
            ) : (
              <div className='flex items-center space-x-1 text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full'>
                <CheckCircle size={14} />
                <span className='text-sm font-medium'>All approved</span>
              </div>
            )}
          </div>
          <div className='space-y-1'>
            <p className='text-sm font-medium text-gray-600'>
              Delivery Agent Approvals
            </p>
            <p className='text-3xl font-bold text-gray-900'>
              {metrics.activeDeliveryAgents}
            </p>
            <p className='text-sm text-gray-500'>
              <button
                onClick={() => navigate("/admin/delivery-agent-approvals")}
                className='font-medium text-indigo-600 hover:text-indigo-700 transition-colors'
              >
                Manage approvals →
              </button>
            </p>
          </div>
        </div>

        {/* Quick Actions for Delivery Agents */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
          <div className='flex items-center justify-between mb-4'>
            <div className='p-3 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-xl'>
              <Shield size={24} className='text-white' />
            </div>
            <div className='flex items-center space-x-1 text-cyan-600 bg-cyan-50 px-2 py-1 rounded-full'>
              <Activity size={14} />
              <span className='text-sm font-medium'>Active</span>
            </div>
          </div>
          <div className='space-y-4'>
            <div>
              <p className='text-sm font-medium text-gray-600 mb-2'>
                Quick Actions
              </p>
              <div className='space-y-2'>
                <button
                  onClick={() => navigate("/admin/delivery-agent-approvals")}
                  className='w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors'
                >
                  <div className='flex items-center space-x-2'>
                    <CheckCircle size={16} className='text-green-600' />
                    <span className='text-sm font-medium text-gray-700'>
                      Review Pending Agents
                    </span>
                  </div>
                  <ArrowRight size={14} className='text-gray-400' />
                </button>
                <button
                  onClick={() => navigate("/admin/delivery-agents")}
                  className='w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors'
                >
                  <div className='flex items-center space-x-2'>
                    <Users size={16} className='text-blue-600' />
                    <span className='text-sm font-medium text-gray-700'>
                      Manage All Agents
                    </span>
                  </div>
                  <ArrowRight size={14} className='text-gray-400' />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Analytics Charts */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Revenue Chart */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
          <div className='flex justify-between items-center mb-6'>
            <div>
              <h3 className='text-lg font-semibold text-gray-900'>
                Revenue Overview
              </h3>
              <p className='text-sm text-gray-500'>
                Monthly revenue performance
              </p>
            </div>
            <div className='flex items-center space-x-2'>
              <div className='flex items-center space-x-1 text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full'>
                <TrendingUp size={14} />
                <span className='text-sm font-medium'>+18.2%</span>
              </div>
            </div>
          </div>
          <div className='h-80'>
            <ResponsiveContainer width='100%' height='100%'>
              <AreaChart data={revenueData}>
                <defs>
                  <linearGradient id='colorRevenue' x1='0' y1='0' x2='0' y2='1'>
                    <stop offset='5%' stopColor='#10B981' stopOpacity={0.8} />
                    <stop offset='95%' stopColor='#10B981' stopOpacity={0.1} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray='3 3' stroke='#F3F4F6' />
                <XAxis
                  dataKey='name'
                  stroke='#9CA3AF'
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke='#9CA3AF'
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => `$${value}`}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "white",
                    border: "1px solid #E5E7EB",
                    borderRadius: "12px",
                    boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
                  }}
                  formatter={(value) => [`$${value}`, "Revenue"]}
                  labelStyle={{ color: "#374151" }}
                />
                <Area
                  type='monotone'
                  dataKey='revenue'
                  stroke='#10B981'
                  fillOpacity={1}
                  fill='url(#colorRevenue)'
                  strokeWidth={3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Order Status Distribution */}
        <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
          <div className='flex justify-between items-center mb-6'>
            <div>
              <h3 className='text-lg font-semibold text-gray-900'>
                Order Status
              </h3>
              <p className='text-sm text-gray-500'>
                Current order distribution
              </p>
            </div>
            <Button
              variant='outline'
              size='small'
              onClick={() => navigate("/admin/orders")}
              className='text-gray-600 border-gray-300 hover:bg-gray-50'
            >
              View All
            </Button>
          </div>
          <div className='h-80'>
            <ResponsiveContainer width='100%' height='100%'>
              <PieChart>
                <Pie
                  data={orderStatusData}
                  cx='50%'
                  cy='50%'
                  labelLine={false}
                  label={({ name, percent }) =>
                    `${name} ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={100}
                  fill='#8884d8'
                  dataKey='value'
                >
                  {orderStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: "white",
                    border: "1px solid #E5E7EB",
                    borderRadius: "12px",
                    boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
                  }}
                  formatter={(value) => [`${value}%`, "Percentage"]}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Top Restaurants & Recent Activity */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
        {/* Top Restaurants */}
        <div className='lg:col-span-2 bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
          <div className='flex justify-between items-center mb-6'>
            <div>
              <h3 className='text-lg font-semibold text-gray-900'>
                Top Restaurants
              </h3>
              <p className='text-sm text-gray-500'>
                Best performing restaurants this month
              </p>
            </div>
            <Button
              variant='outline'
              size='small'
              onClick={() => navigate("/admin/restaurants")}
              className='text-gray-600 border-gray-300 hover:bg-gray-50'
            >
              View All
            </Button>
          </div>
          <div className='space-y-4'>
            {topRestaurantsData.map((restaurant, index) => (
              <div
                key={index}
                className='flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors'
              >
                <div className='flex items-center space-x-4'>
                  <div className='relative'>
                    <div className='w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-xl flex items-center justify-center text-white font-semibold'>
                      {restaurant.name.charAt(0)}
                    </div>
                    <div className='absolute -top-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center text-xs font-bold text-gray-700 shadow-sm'>
                      #{index + 1}
                    </div>
                  </div>
                  <div>
                    <h4 className='font-semibold text-gray-900'>
                      {restaurant.name}
                    </h4>
                    <div className='flex items-center space-x-4 text-sm text-gray-500'>
                      <span>{restaurant.orders} orders</span>
                      <span>•</span>
                      <div className='flex items-center'>
                        <Star size={14} className='text-yellow-400 mr-1' />
                        <span>4.{Math.floor(Math.random() * 9) + 1}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='text-right'>
                  <div className='font-semibold text-gray-900'>
                    ${restaurant.revenue}
                  </div>
                  <div className='text-sm text-emerald-600 font-medium'>
                    +12.5%
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* System Health */}
        <div className='lg:col-span-1 bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
          <div className='mb-6'>
            <h3 className='text-lg font-semibold text-gray-900'>
              System Health
            </h3>
            <p className='text-sm text-gray-500'>Real-time system monitoring</p>
          </div>
          <div className='space-y-6'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center'>
                <div className='w-3 h-3 rounded-full bg-emerald-500 mr-3'></div>
                <span className='font-medium text-gray-700'>
                  Platform Status
                </span>
              </div>
              <span className='text-sm font-medium text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full'>
                Healthy
              </span>
            </div>

            <div className='space-y-4'>
              <div className='flex justify-between items-center'>
                <div className='flex items-center'>
                  <Server size={16} className='text-gray-400 mr-2' />
                  <span className='text-sm text-gray-600'>Uptime</span>
                </div>
                <span className='text-sm font-semibold text-gray-900'>
                  {systemStatus.uptime}
                </span>
              </div>

              <div className='flex justify-between items-center'>
                <div className='flex items-center'>
                  <Zap size={16} className='text-gray-400 mr-2' />
                  <span className='text-sm text-gray-600'>Response Time</span>
                </div>
                <span className='text-sm font-semibold text-gray-900'>
                  {systemStatus.responseTime}
                </span>
              </div>

              <div className='flex justify-between items-center'>
                <div className='flex items-center'>
                  <Activity size={16} className='text-gray-400 mr-2' />
                  <span className='text-sm text-gray-600'>Server Load</span>
                </div>
                <div className='flex items-center space-x-2'>
                  <span className='text-sm font-semibold text-gray-900'>
                    {systemStatus.serverLoad}%
                  </span>
                  <div className='w-16 h-2 bg-gray-200 rounded-full'>
                    <div
                      className='h-2 bg-emerald-500 rounded-full transition-all duration-300'
                      style={{ width: `${systemStatus.serverLoad}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className='flex justify-between items-center'>
                <div className='flex items-center'>
                  <Users size={16} className='text-gray-400 mr-2' />
                  <span className='text-sm text-gray-600'>Active Users</span>
                </div>
                <span className='text-sm font-semibold text-gray-900'>
                  {systemStatus.activeConnections}
                </span>
              </div>
            </div>

            <div className='pt-4 border-t border-gray-100'>
              <div className='text-xs text-gray-500'>
                Last updated: {formatDate(systemStatus.lastChecked)}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className='bg-white rounded-2xl p-6 shadow-sm border border-gray-100'>
        <div className='mb-6'>
          <h3 className='text-lg font-semibold text-gray-900'>
            Recent Activity
          </h3>
          <p className='text-sm text-gray-500'>
            Latest platform activities and updates
          </p>
        </div>
        <div className='space-y-4'>
          {recentActivities.length > 0 ? (
            recentActivities.slice(0, 6).map((activity) => (
              <div
                key={activity.id}
                className='flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors'
              >
                <div className='flex-shrink-0 mt-1'>
                  <div
                    className={`p-2 rounded-lg ${activity.color
                      .replace("text-", "bg-")
                      .replace("-600", "-100")}`}
                  >
                    {activity.icon}
                  </div>
                </div>
                <div className='flex-grow min-w-0'>
                  <p className='text-sm font-medium text-gray-900 truncate'>
                    {activity.message}
                  </p>
                  <div className='flex items-center justify-between mt-1'>
                    <p className='text-xs text-gray-500'>{activity.time}</p>
                    {activity.amount && (
                      <span className='text-xs font-medium text-emerald-600 bg-emerald-50 px-2 py-1 rounded-full'>
                        {activity.amount}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className='text-center text-gray-500 py-8'>
              <Activity size={48} className='mx-auto text-gray-300 mb-4' />
              <p>No recent activity</p>
            </div>
          )}
        </div>
        <div className='mt-6 pt-4 border-t border-gray-100'>
          <Button
            variant='outline'
            size='small'
            className='w-full text-gray-600 border-gray-300 hover:bg-gray-50'
          >
            View All Activities
          </Button>
        </div>
      </div>
    </div>
  );
}

export default AdminDashboard;
