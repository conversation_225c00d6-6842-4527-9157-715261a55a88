import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Store,
  CheckCircle,
  XCircle,
  Eye,
  AlertTriangle,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { mockRestaurants } from "../../data/restaurants";
import { mockUsers } from "../../data/users";

function ManageRestaurantsEnhanced() {
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [restaurants, setRestaurants] = useState([]);
  const [filteredRestaurants, setFilteredRestaurants] = useState([]);
  const [selectedRestaurant, setSelectedRestaurant] = useState(null);
  const [showRestaurantDetails, setShowRestaurantDetails] = useState(false);

  // Load restaurants data
  useEffect(() => {
    // In a real app, this would be an API call
    // For now, we'll use mock data
    setRestaurants(mockRestaurants);
  }, []);

  // Filter restaurants based on status and search query
  useEffect(() => {
    let filtered = [...restaurants];

    // Filter by status
    if (selectedStatus !== "all") {
      filtered = filtered.filter(
        (restaurant) => restaurant.status === selectedStatus
      );
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (restaurant) =>
          restaurant.name.toLowerCase().includes(query) ||
          restaurant.city?.toLowerCase().includes(query) ||
          restaurant.cuisines?.some((cuisine) =>
            cuisine.toLowerCase().includes(query)
          )
      );
    }

    setFilteredRestaurants(filtered);
  }, [restaurants, selectedStatus, searchQuery]);

  // Get restaurant owner details
  const getOwnerDetails = (ownerId) => {
    const owner = mockUsers.find((user) => user.id === ownerId);
    return owner || { name: "Unknown", email: "<EMAIL>" };
  };

  // Handle restaurant approval
  const handleApproveRestaurant = (restaurantId) => {
    // In a real app, this would be an API call
    setRestaurants((prevRestaurants) =>
      prevRestaurants.map((restaurant) =>
        restaurant.id === restaurantId
          ? { ...restaurant, status: "active" }
          : restaurant
      )
    );

    // Update user role
    const restaurant = restaurants.find((r) => r.id === restaurantId);
    if (restaurant) {
      const owner = getOwnerDetails(restaurant.ownerId);
      // In a real app, this would update the user in the database
      console.log(
        `User ${owner.name} role updated from restaurant_pending to restaurant`
      );
    }
  };

  // Handle restaurant rejection
  const handleRejectRestaurant = (restaurantId) => {
    // In a real app, this would be an API call
    setRestaurants((prevRestaurants) =>
      prevRestaurants.map((restaurant) =>
        restaurant.id === restaurantId
          ? { ...restaurant, status: "rejected" }
          : restaurant
      )
    );
  };

  // Handle restaurant suspension
  const handleSuspendRestaurant = (restaurantId) => {
    // In a real app, this would be an API call
    setRestaurants((prevRestaurants) =>
      prevRestaurants.map((restaurant) =>
        restaurant.id === restaurantId
          ? { ...restaurant, status: "suspended" }
          : restaurant
      )
    );
  };

  // View restaurant details
  const handleViewDetails = (restaurant) => {
    setSelectedRestaurant(restaurant);
    setShowRestaurantDetails(true);
  };

  // Get status badge
  const getStatusBadge = (status) => {
    switch (status) {
      case "active":
        return (
          <Badge variant='success' size='small'>
            Active
          </Badge>
        );
      case "pending":
        return (
          <Badge variant='warning' size='small'>
            Pending
          </Badge>
        );
      case "suspended":
        return (
          <Badge variant='danger' size='small'>
            Suspended
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant='danger' size='small'>
            Rejected
          </Badge>
        );
      default:
        return (
          <Badge variant='secondary' size='small'>
            {status}
          </Badge>
        );
    }
  };

  return (
    <div className='p-6'>
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6'>
        <h1 className='text-2xl font-bold mb-4 sm:mb-0'>Manage Restaurants</h1>
        <div className='flex gap-3'>
          <Button
            variant='success'
            icon={<CheckCircle size={18} />}
            to='/admin/restaurant-approval'
          >
            Restaurant Approvals
          </Button>
          <Button
            variant='primary'
            icon={<Store size={18} />}
            to='/admin/restaurants/add'
          >
            Add Restaurant
          </Button>
        </div>
      </div>

      {/* Pending Restaurants Alert */}
      {filteredRestaurants.filter((r) => r.status === "pending").length > 0 && (
        <Card className='mb-6 border-l-4 border-yellow-500 bg-yellow-50'>
          <div className='p-4 flex items-start'>
            <AlertTriangle
              size={20}
              className='text-yellow-500 mr-3 flex-shrink-0 mt-0.5'
            />
            <div>
              <h3 className='font-medium text-yellow-800'>
                Pending Restaurant Applications
              </h3>
              <p className='text-yellow-700 mt-1'>
                You have{" "}
                {
                  filteredRestaurants.filter((r) => r.status === "pending")
                    .length
                }{" "}
                restaurant applications waiting for approval.
              </p>
              <Button
                variant='warning'
                size='small'
                className='mt-2'
                to='/admin/restaurant-approval'
              >
                Review Applications
              </Button>
            </div>
          </div>
        </Card>
      )}

      <Card>
        <div className='flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6'>
          <div className='flex-1 relative'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={20}
            />
            <input
              type='text'
              placeholder='Search restaurants...'
              className='w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <div className='flex gap-4'>
            <select
              className='px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
            >
              <option value='all'>All Status</option>
              <option value='active'>Active</option>
              <option value='pending'>Pending</option>
              <option value='suspended'>Suspended</option>
              <option value='rejected'>Rejected</option>
            </select>

            <Button variant='secondary' icon={<Filter size={18} />}>
              Filters
            </Button>
          </div>
        </div>

        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead>
              <tr className='border-b'>
                <th className='text-left py-4 px-4 font-semibold'>
                  Restaurant
                </th>
                <th className='text-left py-4 px-4 font-semibold'>Owner</th>
                <th className='text-left py-4 px-4 font-semibold'>Cuisine</th>
                <th className='text-left py-4 px-4 font-semibold'>Status</th>
                <th className='text-left py-4 px-4 font-semibold'>Rating</th>
                <th className='text-right py-4 px-4 font-semibold'>Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredRestaurants.length === 0 ? (
                <tr>
                  <td colSpan='6' className='py-8 text-center text-gray-500'>
                    No restaurants found
                  </td>
                </tr>
              ) : (
                filteredRestaurants.map((restaurant) => {
                  const owner = getOwnerDetails(restaurant.ownerId);
                  return (
                    <tr
                      key={restaurant.id}
                      className='border-b hover:bg-gray-50'
                    >
                      <td className='py-4 px-4'>
                        <div className='flex items-center'>
                          <div className='w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center overflow-hidden'>
                            {restaurant.logo ? (
                              <img
                                src={restaurant.logo}
                                alt={restaurant.name}
                                className='w-full h-full object-cover'
                              />
                            ) : (
                              <Store size={24} className='text-gray-400' />
                            )}
                          </div>
                          <div className='ml-3'>
                            <p className='font-medium'>{restaurant.name}</p>
                            <p className='text-sm text-text-secondary'>
                              {restaurant.city || "Unknown location"}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className='py-4 px-4'>
                        <p>{owner.name}</p>
                        <p className='text-sm text-text-secondary'>
                          {owner.email}
                        </p>
                      </td>
                      <td className='py-4 px-4'>
                        <div className='flex flex-wrap gap-1'>
                          {restaurant.cuisines &&
                            restaurant.cuisines.map((cuisine) => (
                              <Badge
                                key={cuisine}
                                variant='secondary'
                                size='small'
                              >
                                {cuisine}
                              </Badge>
                            ))}
                        </div>
                      </td>
                      <td className='py-4 px-4'>
                        {getStatusBadge(restaurant.status)}
                      </td>
                      <td className='py-4 px-4'>
                        <div className='flex items-center'>
                          <span className='text-yellow-500'>★</span>
                          <span className='ml-1'>
                            {restaurant.rating || "N/A"}
                          </span>
                        </div>
                      </td>
                      <td className='py-4 px-4'>
                        <div className='flex justify-end gap-2'>
                          <button
                            className='p-1 hover:text-blue-500'
                            title='View Details'
                            onClick={() => handleViewDetails(restaurant)}
                          >
                            <Eye size={18} />
                          </button>

                          {restaurant.status === "pending" && (
                            <>
                              <button
                                className='p-1 hover:text-green-500'
                                title='Approve'
                                onClick={() =>
                                  handleApproveRestaurant(restaurant.id)
                                }
                              >
                                <CheckCircle size={18} />
                              </button>
                              <button
                                className='p-1 hover:text-red-500'
                                title='Reject'
                                onClick={() =>
                                  handleRejectRestaurant(restaurant.id)
                                }
                              >
                                <XCircle size={18} />
                              </button>
                            </>
                          )}

                          {restaurant.status === "active" && (
                            <button
                              className='p-1 hover:text-orange-500'
                              title='Suspend'
                              onClick={() =>
                                handleSuspendRestaurant(restaurant.id)
                              }
                            >
                              <XCircle size={18} />
                            </button>
                          )}

                          <button
                            className='p-1 hover:text-primary-500'
                            title='Edit'
                          >
                            <Edit size={18} />
                          </button>

                          <button className='p-1'>
                            <MoreVertical size={18} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        <div className='mt-4 flex items-center justify-between'>
          <p className='text-sm text-text-secondary'>
            Showing {filteredRestaurants.length} of {restaurants.length}{" "}
            restaurants
          </p>
          <div className='flex gap-2'>
            <Button variant='secondary' disabled>
              Previous
            </Button>
            <Button variant='secondary' disabled>
              Next
            </Button>
          </div>
        </div>
      </Card>

      {/* Restaurant Details Modal */}
      {showRestaurantDetails && selectedRestaurant && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
          <div className='bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-auto'>
            <div className='p-6 border-b flex justify-between items-center'>
              <h2 className='text-xl font-semibold'>Restaurant Details</h2>
              <button
                className='text-gray-400 hover:text-gray-600'
                onClick={() => setShowRestaurantDetails(false)}
              >
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  className='h-6 w-6'
                  fill='none'
                  viewBox='0 0 24 24'
                  stroke='currentColor'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M6 18L18 6M6 6l12 12'
                  />
                </svg>
              </button>
            </div>

            <div className='p-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <h3 className='font-semibold text-lg mb-4'>
                    Basic Information
                  </h3>

                  <div className='space-y-4'>
                    <div>
                      <p className='text-sm text-gray-500'>Restaurant Name</p>
                      <p className='font-medium'>{selectedRestaurant.name}</p>
                    </div>

                    <div>
                      <p className='text-sm text-gray-500'>Status</p>
                      <div>{getStatusBadge(selectedRestaurant.status)}</div>
                    </div>

                    <div>
                      <p className='text-sm text-gray-500'>Cuisine Types</p>
                      <div className='flex flex-wrap gap-1 mt-1'>
                        {selectedRestaurant.cuisines &&
                          selectedRestaurant.cuisines.map((cuisine) => (
                            <Badge
                              key={cuisine}
                              variant='secondary'
                              size='small'
                            >
                              {cuisine}
                            </Badge>
                          ))}
                      </div>
                    </div>

                    <div>
                      <p className='text-sm text-gray-500'>Address</p>
                      <p>{selectedRestaurant.address}</p>
                    </div>

                    <div>
                      <p className='text-sm text-gray-500'>
                        Contact Information
                      </p>
                      <p>Phone: {selectedRestaurant.phone || "N/A"}</p>
                      <p>Email: {selectedRestaurant.email || "N/A"}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className='font-semibold text-lg mb-4'>
                    Owner Information
                  </h3>

                  <div className='space-y-4'>
                    {(() => {
                      const owner = getOwnerDetails(selectedRestaurant.ownerId);
                      return (
                        <>
                          <div>
                            <p className='text-sm text-gray-500'>Owner Name</p>
                            <p className='font-medium'>{owner.name}</p>
                          </div>

                          <div>
                            <p className='text-sm text-gray-500'>Email</p>
                            <p>{owner.email}</p>
                          </div>

                          <div>
                            <p className='text-sm text-gray-500'>Phone</p>
                            <p>{owner.phone || "N/A"}</p>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                </div>
              </div>

              <div className='mt-8'>
                <h3 className='font-semibold text-lg mb-4'>Description</h3>
                <p className='text-gray-700'>
                  {selectedRestaurant.description || "No description provided."}
                </p>
              </div>

              <div className='mt-8 pt-6 border-t flex justify-end gap-3'>
                {selectedRestaurant.status === "pending" && (
                  <>
                    <Button
                      variant='success'
                      onClick={() => {
                        handleApproveRestaurant(selectedRestaurant.id);
                        setShowRestaurantDetails(false);
                      }}
                    >
                      Approve Restaurant
                    </Button>
                    <Button
                      variant='danger'
                      onClick={() => {
                        handleRejectRestaurant(selectedRestaurant.id);
                        setShowRestaurantDetails(false);
                      }}
                    >
                      Reject Application
                    </Button>
                  </>
                )}

                {selectedRestaurant.status === "active" && (
                  <Button
                    variant='warning'
                    onClick={() => {
                      handleSuspendRestaurant(selectedRestaurant.id);
                      setShowRestaurantDetails(false);
                    }}
                  >
                    Suspend Restaurant
                  </Button>
                )}

                <Button
                  variant='secondary'
                  onClick={() => setShowRestaurantDetails(false)}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ManageRestaurantsEnhanced;
