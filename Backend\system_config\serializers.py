# system_config/serializers.py
from rest_framework import serializers
from .models import SystemSetting, ChoiceOption, FilterConfiguration

class SystemSettingSerializer(serializers.ModelSerializer):
    typed_value = serializers.SerializerMethodField()
    
    class Meta:
        model = SystemSetting
        fields = ['id', 'key', 'name', 'description', 'value', 'typed_value', 'setting_type', 'category', 'is_public']
    
    def get_typed_value(self, obj):
        return obj.get_value()

class SystemSettingUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = SystemSetting
        fields = ['value']
    
    def validate_value(self, value):
        setting_type = self.instance.setting_type
        
        if setting_type == 'boolean':
            if value.lower() not in ('true', 'false', '1', '0', 'yes', 'no', 'on', 'off'):
                raise serializers.ValidationError("Boolean value must be true/false, 1/0, yes/no, or on/off")
        
        elif setting_type == 'integer':
            try:
                int(value)
            except ValueError:
                raise serializers.ValidationError("Value must be a valid integer")
        
        elif setting_type == 'decimal':
            try:
                float(value)
            except ValueError:
                raise serializers.ValidationError("Value must be a valid decimal number")
        
        elif setting_type == 'email':
            email_validator = serializers.EmailField()
            email_validator.run_validation(value)
        
        elif setting_type == 'url':
            url_validator = serializers.URLField()
            url_validator.run_validation(value)
        
        elif setting_type == 'json':
            try:
                import json
                json.loads(value)
            except json.JSONDecodeError:
                raise serializers.ValidationError("Value must be valid JSON")
        
        return value

class ChoiceOptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChoiceOption
        fields = [
            'id', 'option_type', 'value', 'label', 'description', 
            'icon', 'color', 'display_order', 'is_active', 'is_default'
        ]

class FilterConfigurationSerializer(serializers.ModelSerializer):
    class Meta:
        model = FilterConfiguration
        fields = [
            'id', 'name', 'key', 'filter_type', 'label', 'description', 'icon',
            'min_value', 'max_value', 'step', 'options',
            'display_order', 'is_active', 'is_featured'
        ]

class PublicSystemConfigSerializer(serializers.Serializer):
    """Serializer for public system configuration"""
    settings = SystemSettingSerializer(many=True)
    choice_options = serializers.SerializerMethodField()
    filter_configurations = FilterConfigurationSerializer(many=True)
    
    def get_choice_options(self, obj):
        # Group choice options by type
        choice_options = obj.get('choice_options', [])
        grouped = {}
        for option in choice_options:
            option_type = option['option_type']
            if option_type not in grouped:
                grouped[option_type] = []
            grouped[option_type].append(option)
        return grouped
