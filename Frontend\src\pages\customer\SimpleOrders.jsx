import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Clock, Package, Truck, CheckCircle, XCircle, Eye } from "lucide-react";
import { orderApi } from "../../utils/orderApi";
import { useAuth } from "../../contexts/AuthContext";

const SimpleOrders = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Simple fetch function
  const loadOrders = async () => {
    // Check authentication before making API call
    if (!user || !user.access_token) {
      console.log(
        "📋 SimpleOrders: User not authenticated, skipping order load"
      );
      setError("Please login to view your orders");
      setLoading(false);
      return;
    }

    try {
      console.log("📋 Loading user orders for:", user.role, user.name);
      const response = await orderApi.getOrders();

      if (response.success && response.data) {
        setOrders(response.data);
        setError(null);
        console.log("✅ Orders loaded:", response.data.length);
      } else {
        setError("Failed to load orders");
        console.error("❌ Failed to load orders");
      }
    } catch (err) {
      setError("Error loading orders");
      console.error("❌ Error loading orders:", err);
    } finally {
      setLoading(false);
    }
  };

  // Load orders when component mounts or user changes
  useEffect(() => {
    console.log("🔍 SimpleOrders - Loading orders for user:", user?.role);
    loadOrders();
  }, [user]); // Depend on user to reload when authentication changes

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "confirmed":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "preparing":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "ready":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "picked_up":
        return "bg-indigo-100 text-indigo-800 border-indigo-200";
      case "delivered":
        return "bg-green-100 text-green-800 border-green-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case "pending":
        return <Clock className='w-4 h-4' />;
      case "confirmed":
        return <Package className='w-4 h-4' />;
      case "preparing":
        return <Package className='w-4 h-4' />;
      case "ready":
        return <Package className='w-4 h-4' />;
      case "picked_up":
        return <Truck className='w-4 h-4' />;
      case "delivered":
        return <CheckCircle className='w-4 h-4' />;
      case "cancelled":
        return <XCircle className='w-4 h-4' />;
      default:
        return <Clock className='w-4 h-4' />;
    }
  };

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto'></div>
          <p className='mt-4 text-gray-600'>Loading your orders...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <div className='bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded'>
            <p className='font-bold'>Error</p>
            <p>{error}</p>
          </div>
          <button
            onClick={loadOrders}
            className='mt-4 bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600'
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 py-8'>
      <div className='max-w-6xl mx-auto px-4'>
        {/* Header */}
        <div className='flex items-center justify-between mb-6'>
          <h1 className='text-2xl font-bold text-gray-900'>My Orders</h1>
          <button
            onClick={loadOrders}
            disabled={loading}
            className='bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 disabled:opacity-50'
          >
            {loading ? "Refreshing..." : "Refresh"}
          </button>
        </div>

        {/* Orders List */}
        {orders.length === 0 ? (
          <div className='bg-white rounded-lg shadow-md p-8 text-center'>
            <Package className='w-16 h-16 text-gray-400 mx-auto mb-4' />
            <h2 className='text-xl font-semibold text-gray-600 mb-2'>
              No Orders Yet
            </h2>
            <p className='text-gray-500 mb-4'>
              You haven't placed any orders yet.
            </p>
            <button
              onClick={() => navigate("/")}
              className='bg-orange-500 text-white px-6 py-2 rounded hover:bg-orange-600'
            >
              Start Shopping
            </button>
          </div>
        ) : (
          <div className='space-y-4'>
            {orders.map((order) => (
              <div key={order.id} className='bg-white rounded-lg shadow-md p-6'>
                <div className='flex items-center justify-between mb-4'>
                  <div className='flex items-center'>
                    <h3 className='text-lg font-semibold mr-4'>
                      Order #{order.id}
                    </h3>
                    <div
                      className={`flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(
                        order.status
                      )}`}
                    >
                      {getStatusIcon(order.status)}
                      <span className='ml-2 capitalize'>
                        {order.status.replace("_", " ")}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => navigate(`/orders/${order.id}`)}
                    className='flex items-center text-orange-600 hover:text-orange-800'
                  >
                    <Eye className='w-4 h-4 mr-1' />
                    View Details
                  </button>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-4 gap-4 text-sm'>
                  <div>
                    <p className='text-gray-600'>Order Date</p>
                    <p className='font-medium'>
                      {new Date(order.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div>
                    <p className='text-gray-600'>Total Amount</p>
                    <p className='font-medium'>${order.total_amount}</p>
                  </div>
                  <div>
                    <p className='text-gray-600'>Payment Method</p>
                    <p className='font-medium capitalize'>
                      {order.payment_method?.replace("_", " ")}
                    </p>
                  </div>
                  <div>
                    <p className='text-gray-600'>Items</p>
                    <p className='font-medium'>
                      {order.items?.length || 0} item(s)
                    </p>
                  </div>
                </div>

                {/* Order Items Preview */}
                {order.items && order.items.length > 0 && (
                  <div className='mt-4 pt-4 border-t border-gray-200'>
                    <p className='text-sm text-gray-600 mb-2'>Items:</p>
                    <div className='space-y-1'>
                      {order.items.slice(0, 3).map((item, index) => (
                        <div
                          key={index}
                          className='flex justify-between text-sm'
                        >
                          <span>
                            {item.menu_item?.name} x{item.quantity}
                          </span>
                          <span className='font-medium'>
                            $
                            {(
                              parseFloat(item.price_at_order) * item.quantity
                            ).toFixed(2)}
                          </span>
                        </div>
                      ))}
                      {order.items.length > 3 && (
                        <p className='text-sm text-gray-500'>
                          + {order.items.length - 3} more item(s)
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleOrders;
