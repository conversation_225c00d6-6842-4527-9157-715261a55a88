"""
Notification system for delivery assignments and order updates
"""

import logging
from typing import Dict, Any, Optional
from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone

logger = logging.getLogger(__name__)


class DeliveryNotificationService:
    """Service for sending notifications to delivery agents and customers"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def notify_agent_assignment(self, order, agent) -> bool:
        """
        Notify delivery agent about new order assignment
        
        Args:
            order: The assigned order
            agent: The delivery agent
            
        Returns:
            bool: True if notification was sent successfully
        """
        try:
            # In a real application, you would send push notifications, SMS, or email
            # For now, we'll log the notification and could send email
            
            self.logger.info(
                f"Notifying agent {agent.email} about order assignment {order.id}"
            )
            
            # Prepare notification data
            notification_data = {
                'agent_name': agent.get_full_name() or agent.email,
                'order_id': order.id,
                'restaurant_name': order.restaurant.name,
                'customer_name': order.customer.get_full_name() or order.customer.email,
                'order_total': order.total_amount,
                'estimated_delivery_time': order.estimated_delivery_time,
                'assignment_time': timezone.now(),
            }
            
            # Send email notification (if email is configured)
            if self._should_send_email():
                self._send_assignment_email(agent, order, notification_data)
            
            # Here you would also send push notifications, SMS, etc.
            # self._send_push_notification(agent, notification_data)
            # self._send_sms_notification(agent, notification_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to notify agent {agent.email} about assignment: {str(e)}")
            return False
    
    def notify_customer_assignment(self, order, agent) -> bool:
        """
        Notify customer that their order has been assigned to a delivery agent
        
        Args:
            order: The order
            agent: The assigned delivery agent
            
        Returns:
            bool: True if notification was sent successfully
        """
        try:
            self.logger.info(
                f"Notifying customer {order.customer.email} about agent assignment for order {order.id}"
            )
            
            notification_data = {
                'customer_name': order.customer.get_full_name() or order.customer.email,
                'order_id': order.id,
                'agent_name': agent.get_full_name() or "Delivery Agent",
                'estimated_delivery_time': order.estimated_delivery_time,
                'restaurant_name': order.restaurant.name,
            }
            
            # Send email notification (if email is configured)
            if self._should_send_email():
                self._send_customer_assignment_email(order.customer, order, notification_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to notify customer {order.customer.email}: {str(e)}")
            return False
    
    def notify_assignment_failure(self, order, reason: str) -> bool:
        """
        Notify relevant parties when order assignment fails
        
        Args:
            order: The order that failed to be assigned
            reason: Reason for assignment failure
            
        Returns:
            bool: True if notification was sent successfully
        """
        try:
            self.logger.warning(
                f"Assignment failed for order {order.id}: {reason}"
            )
            
            # Notify restaurant owner about assignment failure
            if order.restaurant.owner:
                notification_data = {
                    'restaurant_name': order.restaurant.name,
                    'order_id': order.id,
                    'failure_reason': reason,
                    'customer_name': order.customer.get_full_name() or order.customer.email,
                    'order_total': order.total_amount,
                }
                
                if self._should_send_email():
                    self._send_assignment_failure_email(order.restaurant.owner, order, notification_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to notify about assignment failure for order {order.id}: {str(e)}")
            return False
    
    def _should_send_email(self) -> bool:
        """Check if email notifications are configured and enabled"""
        return (
            hasattr(settings, 'EMAIL_HOST') and
            settings.EMAIL_HOST and
            hasattr(settings, 'DEFAULT_FROM_EMAIL') and
            settings.DEFAULT_FROM_EMAIL
        )
    
    def _send_assignment_email(self, agent, order, notification_data: Dict[str, Any]) -> bool:
        """Send email notification to delivery agent about assignment"""
        try:
            subject = f"New Delivery Assignment - Order #{order.id}"
            
            # Create email content
            message = f"""
            Hello {notification_data['agent_name']},

            You have been assigned a new delivery order:

            Order ID: #{order.id}
            Restaurant: {notification_data['restaurant_name']}
            Customer: {notification_data['customer_name']}
            Order Total: ${notification_data['order_total']}
            
            Please check your delivery app for full order details and pickup instructions.

            Thank you for your service!
            
            Best regards,
            Afghan Sofra Team
            """
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[agent.email],
                fail_silently=False,
            )
            
            self.logger.info(f"Assignment email sent to agent {agent.email}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send assignment email to {agent.email}: {str(e)}")
            return False
    
    def _send_customer_assignment_email(self, customer, order, notification_data: Dict[str, Any]) -> bool:
        """Send email notification to customer about agent assignment"""
        try:
            subject = f"Your Order #{order.id} is Out for Delivery"
            
            message = f"""
            Hello {notification_data['customer_name']},

            Great news! Your order has been assigned to a delivery agent and will be delivered soon.

            Order ID: #{order.id}
            Restaurant: {notification_data['restaurant_name']}
            Estimated Delivery: {notification_data.get('estimated_delivery_time', 'Soon')}
            
            You can track your order status in your account.

            Thank you for choosing Afghan Sofra!
            
            Best regards,
            Afghan Sofra Team
            """
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[customer.email],
                fail_silently=False,
            )
            
            self.logger.info(f"Customer assignment email sent to {customer.email}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send customer email to {customer.email}: {str(e)}")
            return False
    
    def _send_assignment_failure_email(self, restaurant_owner, order, notification_data: Dict[str, Any]) -> bool:
        """Send email notification to restaurant owner about assignment failure"""
        try:
            subject = f"Delivery Assignment Failed - Order #{order.id}"
            
            message = f"""
            Hello,

            We were unable to assign a delivery agent to one of your orders:

            Order ID: #{order.id}
            Customer: {notification_data['customer_name']}
            Order Total: ${notification_data['order_total']}
            Failure Reason: {notification_data['failure_reason']}
            
            Please contact our support team if this issue persists.

            Best regards,
            Afghan Sofra Team
            """
            
            send_mail(
                subject=subject,
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[restaurant_owner.email],
                fail_silently=False,
            )
            
            self.logger.info(f"Assignment failure email sent to restaurant owner {restaurant_owner.email}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send failure email to {restaurant_owner.email}: {str(e)}")
            return False


# Global instance for easy access
notification_service = DeliveryNotificationService()
