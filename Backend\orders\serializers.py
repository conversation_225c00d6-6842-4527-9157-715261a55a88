# orders/serializers.py
from decimal import Decimal
from rest_framework import serializers

from restaurant.models import Address, MenuItem, Restaurant
from .models import Order, OrderItem, OrderStatusHistory, SavedCart, SavedCartItem, Rating
from restaurant.serializers import MenuItemSerializer, RestaurantSerializer, UserBasicSerializer

class OrderItemSerializer(serializers.ModelSerializer):
    menu_item_id = serializers.IntegerField(write_only=True)
    menu_item = MenuItemSerializer(read_only=True)

    class Meta:
        model = OrderItem
        fields = ['id', 'menu_item', 'menu_item_id', 'quantity', 'price_at_order', 'special_requests']
        read_only_fields = ['id', 'price_at_order']

from decimal import Decimal

class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True)
    customer = serializers.HiddenField(default=serializers.CurrentUserDefault())

    # Remove delivery_fee from writable fields since it will come from restaurant
    delivery_address = serializers.PrimaryKeyRelatedField(
        queryset=Address.objects.all(),
        required=True,
        error_messages={
            'does_not_exist': 'Address with ID {pk_value} does not exist.',
            'required': 'Delivery address is required.',
            'invalid': 'Invalid address ID provided.'
        }
    )
    restaurant = serializers.PrimaryKeyRelatedField(
        queryset=Restaurant.objects.filter(is_active=True),
        required=True
    )
    
    # Remove the delivery_fee field from serializer since it's not user-provided anymore
    
    class Meta:
        model = Order
        fields = [
            'id', 'customer', 'delivery_address', 'restaurant', 'delivery_agent',
            'status', 'total_amount', 'delivery_fee', 'tax_amount', 'special_instructions',
            'created_at', 'updated_at', 'estimated_delivery_time', 'actual_delivery_time',
            'payment_method', 'payment_status', 'transaction_id', 'items'
        ]
        read_only_fields = [
            'id', 'status', 'total_amount', 'tax_amount', 'delivery_fee',
            'created_at', 'updated_at', 'delivery_agent'
        ]

    def validate_delivery_address(self, value):
        """Ensure the delivery address belongs to the current user"""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            user = request.user
            if value.user != user:
                # Get user's available addresses for better error message
                user_addresses = Address.objects.filter(user=user)
                address_ids = list(user_addresses.values_list('id', flat=True))
                raise serializers.ValidationError(
                    f"You can only use your own addresses for delivery. "
                    f"Your available address IDs are: {address_ids}. "
                    f"You tried to use address ID: {value.id}"
                )
        return value

    def create(self, validated_data):
        items_data = validated_data.pop('items')
        restaurant = validated_data['restaurant']
        
        # Calculate items total
        items_total = sum(
            MenuItem.objects.get(id=item['menu_item_id']).price * item['quantity']
            for item in items_data
        )
        
        # Get delivery fee from restaurant
        delivery_fee = restaurant.delivery_fee
        
        # Calculate tax (10% of items only)
        tax_amount = items_total * Decimal('0.1')
        
        # Create order with all fields
        order = Order.objects.create(
            **validated_data,
            total_amount=items_total + delivery_fee + tax_amount,
            delivery_fee=delivery_fee,
            tax_amount=tax_amount
        )
        
        # Create order items
        for item_data in items_data:
            menu_item = MenuItem.objects.get(id=item_data['menu_item_id'])
            OrderItem.objects.create(
                order=order,
                menu_item=menu_item,
                quantity=item_data['quantity'],
                price_at_order=menu_item.price,
                special_requests=item_data.get('special_requests', '')
            )
        
        return order
    
    def update(self, instance, validated_data):
        # Remove items if present (we'll handle them separately)
        items_data = validated_data.pop('items', None)
        
        # Update regular fields
        instance = super().update(instance, validated_data)
        
        # Handle items update if provided
        if items_data:
            # Clear existing items
            instance.items.all().delete()
            
            # Create new items
            for item_data in items_data:
                menu_item = MenuItem.objects.get(id=item_data['menu_item_id'])
                OrderItem.objects.create(
                    order=instance,
                    menu_item=menu_item,
                    quantity=item_data['quantity'],
                    price_at_order=menu_item.price,
                    special_requests=item_data.get('special_requests', '')
                )
            
            # Recalculate totals
            instance.refresh_from_db()
            self._update_order_totals(instance)
        
        return instance


class OrderDetailSerializer(OrderSerializer):
    """Enhanced order serializer with customer and restaurant details for read operations"""
    customer = UserBasicSerializer(read_only=True)
    restaurant = RestaurantSerializer(read_only=True)

    class Meta(OrderSerializer.Meta):
        fields = OrderSerializer.Meta.fields
        read_only_fields = OrderSerializer.Meta.read_only_fields + ['customer', 'restaurant']

    def _update_order_totals(self, order):
        """Helper method to recalculate order totals"""
        items_total = sum(
            item.price_at_order * item.quantity
            for item in order.items.all()
        )
        order.total_amount = items_total + order.delivery_fee + order.tax_amount
        order.save()



class SavedCartItemSerializer(serializers.ModelSerializer):
    menu_item_id = serializers.IntegerField(write_only=True)
    menu_item = MenuItemSerializer(read_only=True)

    class Meta:
        model = SavedCartItem
        fields = ['id', 'menu_item', 'menu_item_id', 'quantity', 'special_requests']
        extra_kwargs = {
            'quantity': {'min_value': Decimal('1')}
        }

class SavedCartSerializer(serializers.ModelSerializer):
    items = SavedCartItemSerializer(many=True)
    restaurant_id = serializers.IntegerField(write_only=True)

    class Meta:
        model = SavedCart
        fields = ['id', 'restaurant', 'restaurant_id', 'items', 'created_at', 'updated_at']

    def update(self, instance, validated_data):
        from restaurant.models import MenuItem

        restaurant_id = validated_data.pop('restaurant_id', None)
        items_data = validated_data.pop('items', [])

        # Auto-detect restaurant from menu items if not provided
        if restaurant_id is None and items_data:
            # Get the restaurant from the first menu item
            first_menu_item_id = items_data[0]['menu_item_id']
            try:
                first_menu_item = MenuItem.objects.get(id=first_menu_item_id)
                restaurant_id = first_menu_item.restaurant_id
                print(f"🔍 Auto-detected restaurant ID {restaurant_id} from menu item {first_menu_item_id}")
            except MenuItem.DoesNotExist:
                print(f"❌ Menu item {first_menu_item_id} not found")

        # Update restaurant if we have one
        if restaurant_id is not None:
            instance.restaurant_id = restaurant_id
            print(f"🔍 Set cart restaurant to {restaurant_id}")

        # Handle items with update_or_create to avoid duplicates
        for item_data in items_data:
            SavedCartItem.objects.update_or_create(
                cart=instance,
                menu_item_id=item_data['menu_item_id'],
                defaults={
                    'quantity': item_data['quantity'],
                    'special_requests': item_data.get('special_requests', '')
                }
            )

        # Remove items not in the request
        kept_menu_item_ids = [item['menu_item_id'] for item in items_data]
        instance.items.exclude(menu_item_id__in=kept_menu_item_ids).delete()

        instance.save()
        return instance
    


class OrderStatusHistorySerializer(serializers.ModelSerializer):
    changed_by = serializers.StringRelatedField()
    
    class Meta:
        model = OrderStatusHistory
        fields = [
            'from_status',
            'to_status',
            'changed_by',
            'created_at',
            'notes'
        ]


class AssignDeliveryAgentSerializer(serializers.Serializer):
    agent_id = serializers.IntegerField()
    order_id = serializers.IntegerField()

class RejectDeliverySerializer(serializers.Serializer):
    order_id = serializers.IntegerField()
    reason = serializers.CharField(required=False)


class RatingSerializer(serializers.ModelSerializer):
    customer = serializers.HiddenField(default=serializers.CurrentUserDefault())
    restaurant = serializers.PrimaryKeyRelatedField(read_only=True)
    order = serializers.PrimaryKeyRelatedField(read_only=True)
    average_rating = serializers.ReadOnlyField()

    # Additional fields for better display
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    restaurant_name = serializers.CharField(source='restaurant.name', read_only=True)
    order_total = serializers.DecimalField(source='order.total_amount', max_digits=10, decimal_places=2, read_only=True)

    class Meta:
        model = Rating
        fields = [
            'id', 'order', 'customer', 'restaurant',
            'food_rating', 'delivery_rating', 'overall_rating',
            'review_text', 'average_rating', 'created_at', 'updated_at',
            'customer_name', 'restaurant_name', 'order_total'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'customer_name', 'restaurant_name', 'order_total']

    def validate(self, data):
        """Custom validation for rating data"""
        # Validate rating ranges
        for field in ['food_rating', 'delivery_rating', 'overall_rating']:
            if field in data:
                rating = data[field]
                if not (1 <= rating <= 5):
                    raise serializers.ValidationError(f'{field} must be between 1 and 5')

        # Validate review text length
        if 'review_text' in data and data['review_text']:
            if len(data['review_text']) > 1000:
                raise serializers.ValidationError('Review text cannot exceed 1000 characters')

        return data

    def validate(self, data):
        """Validate that all ratings are between 1 and 5"""
        for field in ['food_rating', 'delivery_rating', 'overall_rating']:
            if field in data:
                rating = data[field]
                if not (1 <= rating <= 5):
                    raise serializers.ValidationError(f"{field} must be between 1 and 5 stars")
        return data


class RatingDetailSerializer(RatingSerializer):
    """Enhanced rating serializer with full order and restaurant details"""
    order_details = serializers.SerializerMethodField()
    restaurant_details = serializers.SerializerMethodField()
    customer_details = serializers.SerializerMethodField()

    class Meta(RatingSerializer.Meta):
        fields = RatingSerializer.Meta.fields + ['order_details', 'restaurant_details', 'customer_details']

    def get_order_details(self, obj):
        """Get order details for the rating"""
        return {
            'id': obj.order.id,
            'total_amount': str(obj.order.total_amount),
            'created_at': obj.order.created_at,
            'status': obj.order.status,
            'items_count': obj.order.items.count()
        }

    def get_restaurant_details(self, obj):
        """Get restaurant details for the rating"""
        return {
            'id': obj.restaurant.id,
            'name': obj.restaurant.name,
            'cuisine_type': obj.restaurant.cuisine_type,
            'rating': obj.restaurant.rating
        }

    def get_customer_details(self, obj):
        """Get customer details for the rating"""
        return {
            'id': obj.customer.id,
            'name': obj.customer.name,
            'total_orders': obj.customer.orders.count()
        }


class RestaurantRatingsSummarySerializer(serializers.Serializer):
    """Serializer for restaurant ratings summary"""
    restaurant_id = serializers.IntegerField()
    restaurant_name = serializers.CharField()
    total_ratings = serializers.IntegerField()
    average_food_rating = serializers.DecimalField(max_digits=3, decimal_places=2)
    average_delivery_rating = serializers.DecimalField(max_digits=3, decimal_places=2)
    average_overall_rating = serializers.DecimalField(max_digits=3, decimal_places=2)
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=2)

    # Rating distribution
    five_star_count = serializers.IntegerField()
    four_star_count = serializers.IntegerField()
    three_star_count = serializers.IntegerField()
    two_star_count = serializers.IntegerField()
    one_star_count = serializers.IntegerField()