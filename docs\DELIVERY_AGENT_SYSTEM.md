# 🚚 Afghan Delivery Agent System - Complete Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture Diagrams](#architecture-diagrams)
3. [Registration Process](#registration-process)
4. [Mobile App Features](#mobile-app-features)
5. [Order Management](#order-management)
6. [Payment Collection](#payment-collection)
7. [Admin Dashboard](#admin-dashboard)
8. [Performance Tracking](#performance-tracking)
9. [Implementation Plan](#implementation-plan)
10. [Technical Specifications](#technical-specifications)

---

## System Overview

The Afghan Delivery Agent System is a comprehensive platform designed specifically for the Afghan market, providing end-to-end delivery management with cultural sensitivity and technical adaptations for local infrastructure challenges.

### Key Features
- **Bilingual Support**: Dari, Pashto, and English interfaces
- **Offline-First Design**: Works with poor internet connectivity
- **Cultural Adaptations**: Islamic considerations, family verification
- **Local Payment Methods**: Cash-focused with mobile wallet integration
- **Performance-Based Incentives**: Comprehensive earning system

---

## Architecture Diagrams

### System Architecture Flow

```mermaid
graph TB
    %% Agent Registration Flow
    subgraph "Agent Registration & Onboarding"
        A1[Agent Application] --> A2[Document Upload]
        A2 --> A3[Background Verification]
        A3 --> A4[Training Module]
        A4 --> A5[Equipment Assignment]
        A5 --> A6[Account Activation]
    end

    %% Mobile App Core Features
    subgraph "Mobile App Features"
        M1[Offline-First Login] --> M2[Task Assignment]
        M2 --> M3[GPS Tracking]
        M3 --> M4[Status Updates]
        M4 --> M5[Earnings Tracker]
        M5 --> M6[Support Chat]
    end

    %% Admin Dashboard
    subgraph "Admin Dashboard"
        AD1[Agent Management] --> AD2[Performance Analytics]
        AD2 --> AD3[Payment Processing]
        AD3 --> AD4[Zone Management]
        AD4 --> AD5[Training Oversight]
        AD5 --> AD6[Support System]
    end

    %% Afghan Market Adaptations
    subgraph "Afghan Market Features"
        AF1[Dari/Pashto Support] --> AF2[Cash Payment Focus]
        AF2 --> AF3[Offline Capability]
        AF3 --> AF4[Low-Data Mode]
        AF4 --> AF5[Voice Navigation]
        AF5 --> AF6[Family Verification]
    end

    %% Integration Points
    A6 --> M1
    M4 --> AD2
    AD3 --> M5
    AF1 --> M1
    AF2 --> AD3
```

### Complete Workflow Diagram

```mermaid
graph TB
    %% Registration Process
    subgraph "1. Agent Registration Process"
        A1[Agent Applies Online] --> A2[Fill Registration Form]
        A2 --> A3[Upload Documents]
        A3 --> A4[Submit Application]
        A4 --> A5[Admin Reviews Application]
        A5 --> A6{Documents Valid?}
        A6 -->|Yes| A7[Background Check]
        A6 -->|No| A8[Request Corrections]
        A8 --> A2
        A7 --> A9[Training Assignment]
        A9 --> A10[Equipment Provision]
        A10 --> A11[Account Activation]
    end

    %% Order Assignment
    subgraph "2. Order Assignment System"
        B1[Customer Places Order] --> B2[Restaurant Confirms]
        B2 --> B3[Order Ready for Pickup]
        B3 --> B4[System Finds Available Agents]
        B4 --> B5[Calculate Agent Scores]
        B5 --> B6[Assign to Best Agent]
        B6 --> B7[Send Notification to Agent]
        B7 --> B8{Agent Accepts?}
        B8 -->|Yes| B9[Order Assigned]
        B8 -->|No| B10[Try Next Agent]
        B10 --> B7
    end

    %% Agent Mobile Experience
    subgraph "3. Agent Mobile App Experience"
        C1[Agent Logs In] --> C2[Set Status: Available]
        C2 --> C3[Receive Order Notification]
        C3 --> C4[View Order Details]
        C4 --> C5{Accept Order?}
        C5 -->|Yes| C6[Navigate to Restaurant]
        C5 -->|No| C7[Decline Order]
        C6 --> C8[Pickup Order]
        C8 --> C9[Navigate to Customer]
        C9 --> C10[Deliver Order]
        C10 --> C11[Collect Payment]
        C11 --> C12[Complete Delivery]
    end

    %% Payment Collection
    subgraph "4. Payment Collection Process"
        D1[Customer Payment Due] --> D2{Payment Method?}
        D2 -->|Cash| D3[Collect Cash]
        D2 -->|Digital| D4[Process Digital Payment]
        D3 --> D5[Count & Verify Cash]
        D4 --> D6[Confirm Payment Received]
        D5 --> D7[Update App: Cash Collected]
        D6 --> D7
        D7 --> D8[Take Photo Receipt]
        D8 --> D9[Customer Signs/Confirms]
        D9 --> D10[Upload Proof of Delivery]
    end

    %% Agent Earnings
    subgraph "5. Agent Earnings & Settlement"
        E1[Daily Earnings Calculation] --> E2[Base Delivery Fee]
        E2 --> E3[Add Performance Bonus]
        E3 --> E4[Add Tips Received]
        E4 --> E5[Subtract Platform Fee]
        E5 --> E6[Calculate Net Earnings]
        E6 --> E7[Weekly Settlement]
        E7 --> E8{Settlement Method?}
        E8 -->|Bank| E9[Bank Transfer]
        E8 -->|Mobile Wallet| E10[Mobile Money]
        E8 -->|Cash| E11[Cash Pickup]
    end

    %% Connections
    A11 --> C1
    B9 --> C3
    C12 --> E1
```

---

## Registration Process

### Agent Registration Form Structure

#### Personal Information (Afghan Context)
```javascript
{
  // Basic Information
  fullNameDari: "احمد علی",
  fullNamePashto: "احمد علي", 
  fatherName: "محمد علی",
  nationalId: "**********123", // Tazkira number
  dateOfBirth: "1995-05-15",
  gender: "male", // male/female
  maritalStatus: "married", // single/married/divorced/widowed
  
  // Contact Information
  phonePrimary: "+93 70 123 4567",
  phoneSecondary: "+93 78 987 6543",
  email: "<EMAIL>",
  
  // Address Information
  province: "kabul",
  district: "Kabul City",
  area: "Shar-e-Naw",
  streetAddress: "Street 15, House 25",
  nearbyLandmark: "Near Police Station",
  
  // Vehicle Information
  vehicleType: "motorcycle", // motorcycle/bicycle/car/rickshaw
  vehicleModel: "Honda 125cc",
  vehicleYear: 2020,
  licensePlate: "KBL-001",
  vehicleColor: "Red",
  
  // References (Important in Afghan culture)
  reference1: {
    name: "محمد حسن",
    phone: "+93 70 111 2222",
    relation: "Uncle"
  },
  reference2: {
    name: "علی احمد", 
    phone: "+93 78 333 4444",
    relation: "Neighbor"
  },
  
  // Banking Information
  bankName: "Afghanistan International Bank",
  accountNumber: "**********",
  mobileWalletNumber: "+93 70 123 4567", // M-Paisa, etc.
  
  // Work Preferences
  workingDays: ["saturday", "sunday", "monday", "tuesday", "wednesday"],
  workingHours: { start: "08:00", end: "20:00" },
  preferredZones: ["Shar-e-Naw", "Wazir Akbar Khan", "Karte Char"]
}
```

#### Document Requirements
```javascript
{
  // Required Documents
  tazkiraFront: "base64_image_data", // National ID front
  tazkiraBack: "base64_image_data",  // National ID back
  drivingLicense: "base64_image_data", // If applicable
  vehicleRegistration: "base64_image_data", // If applicable
  profilePhoto: "base64_image_data",
  
  // Verification Status
  documentStatus: {
    tazkira: "pending", // pending/verified/rejected/expired
    license: "pending",
    vehicleReg: "pending"
  }
}
```

### Registration Workflow

#### Step 1: Application Submission
```python
# Backend API endpoint
POST /api/delivery-agent/register/
{
  "personal_info": { ... },
  "documents": { ... },
  "preferences": { ... }
}

# Response
{
  "status": "success",
  "agent_code": "**********",
  "message": "Application submitted successfully",
  "next_steps": [
    "Upload required documents",
    "Wait for admin verification", 
    "Complete training program",
    "Receive equipment and start working"
  ]
}
```

#### Step 2: Admin Verification Process
```python
# Admin workflow
1. Document Review
   - Verify Tazkira authenticity
   - Check driving license validity
   - Validate vehicle registration
   
2. Background Check
   - Contact provided references
   - Verify address and identity
   - Check criminal background
   
3. Training Assignment
   - Schedule orientation session
   - Assign training materials
   - Set practical test date
   
4. Equipment Provision
   - Delivery bag and uniform
   - Phone mount and charger
   - Company ID card
   
5. Account Activation
   - Generate login credentials
   - Activate mobile app access
   - Set initial status to "active"
```

---

## Mobile App Features

### App Interface Structure

#### Login Screen
```javascript
// Bilingual login interface
{
  title: "د ډیلیوری ایجنټ ننوتل (Delivery Agent Login)",
  fields: {
    username: {
      label: "کارن نوم (Username)",
      placeholder: "د تلیفون شمیره (Phone number)"
    },
    password: {
      label: "پټ نوم (Password)", 
      placeholder: "پټ نوم دننه کړئ (Enter password)"
    }
  },
  options: {
    biometric: "د ګوتو نښه (Fingerprint)",
    rememberMe: "زه یادولئ (Remember me)",
    forgotPassword: "پټ نوم مې هیر شو (Forgot password)"
  }
}
```

#### Dashboard Interface
```javascript
// Main dashboard components
{
  header: {
    agentName: "احمد علی (Ahmad Ali)",
    agentZone: "شهر نو کابل (Shar-e-Naw, Kabul)",
    onlineStatus: "آنلاین (Online)" // Online/Offline toggle
  },
  
  todayStats: {
    earnings: "1,850 AFN",
    deliveries: "8/15", // completed/target
    rating: "4.8 ⭐",
    cashCollected: "6,800 AFN"
  },
  
  currentOrder: {
    restaurant: "کابل رستوران (Kabul Restaurant)",
    customer: "محمد حسن (Mohammad Hassan)", 
    distance: "2.3 km",
    estimatedTime: "15 min",
    paymentMethod: "نغدي (Cash)",
    agentEarnings: "65 AFN",
    status: "assigned" // assigned/picked_up/in_transit
  },
  
  availableOrders: [
    {
      id: "ORD001",
      restaurant: "کابل رستوران",
      customer: "محمد حسن",
      amount: "850 AFN",
      distance: "2.3 km",
      estimatedTime: "15 min",
      agentEarnings: "65 AFN",
      paymentMethod: "cash"
    }
  ]
}
```

### Order Management Interface

#### Order Acceptance Flow
```javascript
// Order notification popup
{
  title: "نوی امر (New Order)",
  orderDetails: {
    restaurant: {
      name: "کابل رستوران",
      nameEn: "Kabul Restaurant",
      address: "شهر نو، کابل",
      phone: "+93 70 123 4567"
    },
    customer: {
      name: "محمد حسن", 
      nameEn: "Mohammad Hassan",
      address: "کارته چهار، کابل",
      phone: "+93 78 987 6543"
    },
    orderInfo: {
      totalAmount: "850 AFN",
      deliveryFee: "25 AFN", 
      agentEarnings: "65 AFN",
      distance: "2.3 km",
      estimatedTime: "15 min",
      paymentMethod: "cash",
      items: ["قابلی پلاو", "مانتو", "چای"]
    }
  },
  actions: {
    accept: "ومنئ (Accept)",
    decline: "رد (Decline)",
    details: "تفصیلات (Details)"
  },
  timer: 60 // seconds to respond
}
```

#### Order Status Updates
```javascript
// Status progression interface
{
  statuses: {
    assigned: {
      dari: "امر ټاکل شوی",
      english: "Order Assigned",
      action: "پیل کړئ (Start Pickup)",
      nextStatus: "picked_up"
    },
    picked_up: {
      dari: "امر واخیستل شو",
      english: "Order Picked Up", 
      action: "پیرودونکي ته روان (Head to Customer)",
      nextStatus: "in_transit"
    },
    in_transit: {
      dari: "د رسولو په حال کې",
      english: "In Transit",
      action: "ورسول شو (Delivered)",
      nextStatus: "delivered"
    },
    delivered: {
      dari: "ورسول شو",
      english: "Delivered",
      action: "تادیات راټول کړئ (Collect Payment)",
      nextStatus: "completed"
    }
  }
}
```

---

## Payment Collection

### Cash Payment Interface
```javascript
// Payment collection modal
{
  title: "د تادیاتو راټولول (Payment Collection)",
  orderSummary: {
    orderTotal: "850 AFN",
    agentEarnings: "65 AFN",
    paymentMethod: "نغدي (Cash)"
  },
  
  collectionForm: {
    amountField: {
      label: "د پیرودونکي څخه راټول شوی مقدار",
      value: "850",
      readonly: true
    },
    confirmationCheckbox: {
      label: "زه تصدیق کوم چې سمه پیسې راټولې کړې",
      labelEn: "I confirm correct amount collected"
    },
    photoProof: {
      label: "د تادیاتو عکس (Payment Photo)",
      optional: true
    },
    customerSignature: {
      label: "د پیرودونکي لاسلیک (Customer Signature)",
      canvas: true
    }
  },
  
  actions: {
    cancel: "لغو (Cancel)",
    complete: "بشپړ کړئ (Complete)"
  }
}
```

### Digital Payment Interface
```javascript
// Digital payment confirmation
{
  title: "ډیجیټل تادیه (Digital Payment)",
  paymentInfo: {
    method: "M-Paisa / Roshan Money",
    amount: "850 AFN",
    transactionId: "TXN123456789",
    status: "confirmed"
  },
  
  confirmation: {
    message: "تادیه بریالۍ وه (Payment successful)",
    timestamp: "2024-01-20 14:30:25",
    agentEarnings: "65 AFN"
  },
  
  actions: {
    confirmDelivery: "د رسولو تصدیق (Confirm Delivery)",
    reportIssue: "ستونزه راپور ورکړئ (Report Issue)"
  }
}
```

### Earnings Calculation
```python
# Agent earnings formula
def calculate_agent_earnings(order):
    base_fee = 50.00  # Base delivery fee in AFN
    distance_bonus = order.delivery_fee * 0.70  # 70% of delivery fee
    platform_fee = (base_fee + distance_bonus) * 0.15  # 15% platform fee
    performance_bonus = calculate_performance_bonus(agent)
    
    total_earnings = base_fee + distance_bonus + performance_bonus - platform_fee
    return total_earnings

# Example calculation
order_value = 850  # AFN
delivery_fee = 25  # AFN
base_fee = 50
distance_bonus = 25 * 0.70 = 17.5
subtotal = 50 + 17.5 = 67.5
platform_fee = 67.5 * 0.15 = 10.125
agent_earnings = 67.5 - 10.125 = 57.375 ≈ 57 AFN
```

---

## Admin Dashboard

### Agent Management Interface

#### Agent Overview Table
```javascript
{
  columns: [
    {
      header: "ایجنټ (Agent)",
      fields: ["name", "nameEn", "agentCode", "profilePhoto"]
    },
    {
      header: "حالت (Status)", 
      fields: ["status", "availability", "lastActive"]
    },
    {
      header: "کارکرد (Performance)",
      fields: ["rating", "totalDeliveries", "completionRate"]
    },
    {
      header: "عاید (Earnings)",
      fields: ["thisMonthEarnings", "totalEarnings"]
    },
    {
      header: "عملونه (Actions)",
      fields: ["view", "edit", "suspend", "more"]
    }
  ],
  
  filters: {
    status: ["all", "active", "inactive", "suspended", "pending"],
    availability: ["all", "online", "offline", "busy"],
    zone: ["all", "Kabul", "Herat", "Kandahar", "Mazar-i-Sharif"],
    performance: ["all", "top_performers", "needs_improvement"]
  }
}
```

#### Agent Performance Metrics
```javascript
{
  performanceKPIs: {
    completionRate: {
      formula: "(successful_deliveries / total_deliveries) * 100",
      target: "> 95%",
      current: "96.2%"
    },
    averageDeliveryTime: {
      formula: "sum(delivery_times) / total_deliveries", 
      target: "< 30 minutes",
      current: "28 minutes"
    },
    customerRating: {
      formula: "sum(ratings) / total_ratings",
      target: "> 4.5",
      current: "4.8"
    },
    onTimeDeliveries: {
      formula: "(on_time_deliveries / total_deliveries) * 100",
      target: "> 90%", 
      current: "92%"
    }
  },
  
  earningsMetrics: {
    dailyAverage: "231 AFN",
    weeklyAverage: "1,617 AFN", 
    monthlyAverage: "6,930 AFN",
    totalEarnings: "125,000 AFN"
  }
}
```

### Payment Processing System

#### Weekly Settlement Process
```python
# Automated settlement calculation
def calculate_weekly_settlement(agent_id, week_start, week_end):
    # Get all completed deliveries for the week
    deliveries = Order.objects.filter(
        delivery_agent_id=agent_id,
        status='delivered',
        delivered_at__range=[week_start, week_end]
    )
    
    # Calculate base earnings
    base_earnings = sum(calculate_agent_earnings(order) for order in deliveries)
    
    # Add performance bonuses
    performance_bonus = calculate_weekly_bonus(agent_id, deliveries)
    
    # Subtract deductions
    deductions = calculate_deductions(agent_id, week_start, week_end)
    
    # Calculate final settlement
    net_settlement = base_earnings + performance_bonus - deductions
    
    return {
        'base_earnings': base_earnings,
        'performance_bonus': performance_bonus, 
        'deductions': deductions,
        'net_settlement': net_settlement,
        'delivery_count': len(deliveries)
    }

# Weekly bonus structure
def calculate_weekly_bonus(agent_id, deliveries):
    bonus = 0
    
    # Completion rate bonus
    if len(deliveries) >= 50:  # 50+ deliveries per week
        bonus += 200  # AFN
    
    # Rating bonus
    avg_rating = calculate_average_rating(deliveries)
    if avg_rating >= 4.8:
        bonus += 300  # AFN
    
    # Perfect week bonus (100% completion, no complaints)
    if is_perfect_week(agent_id, deliveries):
        bonus += 500  # AFN
        
    return bonus
```

#### Payment Methods Integration
```javascript
{
  paymentMethods: {
    bankTransfer: {
      supportedBanks: [
        "Afghanistan International Bank (AIB)",
        "Azizi Bank", 
        "First MicroFinance Bank",
        "Maiwand Bank",
        "New Kabul Bank"
      ],
      processingTime: "1-2 business days",
      fees: "0 AFN"
    },
    
    mobileWallet: {
      supportedWallets: [
        "M-Paisa (Roshan)",
        "Roshan Money",
        "Etisalat Money", 
        "MTN Mobile Money"
      ],
      processingTime: "Instant",
      fees: "2% of amount"
    },
    
    cashPickup: {
      locations: [
        "Main Office - Kabul",
        "Branch Office - Herat", 
        "Partner Location - Kandahar"
      ],
      processingTime: "Same day",
      fees: "0 AFN",
      requirements: ["Valid ID", "Agent code"]
    }
  }
}
```

---

## Performance Tracking

### Agent Scoring Algorithm
```python
def calculate_agent_score(agent_id, time_period_days=30):
    """
    Calculate comprehensive agent performance score
    Score range: 0-100
    """
    
    # Get agent data
    agent = get_agent_data(agent_id, time_period_days)
    
    # Performance metrics (weighted)
    metrics = {
        'completion_rate': {
            'weight': 0.25,
            'value': agent.completion_rate,
            'target': 95.0
        },
        'customer_rating': {
            'weight': 0.25, 
            'value': agent.average_rating * 20,  # Convert 5-point to 100-point
            'target': 90.0  # 4.5/5 = 90/100
        },
        'delivery_time': {
            'weight': 0.20,
            'value': max(0, 100 - (agent.avg_delivery_time - 20) * 2),  # Penalty for >20 min
            'target': 100.0
        },
        'reliability': {
            'weight': 0.15,
            'value': agent.on_time_percentage,
            'target': 90.0
        },
        'activity_level': {
            'weight': 0.15,
            'value': min(100, (agent.deliveries_per_day / 15) * 100),  # Target: 15/day
            'target': 100.0
        }
    }
    
    # Calculate weighted score
    total_score = 0
    for metric, data in metrics.items():
        normalized_score = min(100, (data['value'] / data['target']) * 100)
        weighted_score = normalized_score * data['weight']
        total_score += weighted_score
    
    return {
        'overall_score': round(total_score, 1),
        'metrics_breakdown': metrics,
        'performance_level': get_performance_level(total_score)
    }

def get_performance_level(score):
    if score >= 90:
        return "Excellent (عالي)"
    elif score >= 80:
        return "Good (ښه)"
    elif score >= 70:
        return "Average (اوسط)"
    elif score >= 60:
        return "Below Average (د اوسط څخه ټیټ)"
    else:
        return "Needs Improvement (د ښه کولو ضرورت)"
```

### Performance Dashboard
```javascript
{
  agentPerformance: {
    overallScore: 87.5,
    performanceLevel: "Good (ښه)",
    ranking: "15th out of 247 agents",
    
    metricsBreakdown: {
      completionRate: {
        current: 96.2,
        target: 95.0,
        status: "exceeding",
        trend: "****% vs last month"
      },
      customerRating: {
        current: 4.8,
        target: 4.5, 
        status: "exceeding",
        trend: "+0.2 vs last month"
      },
      deliveryTime: {
        current: 28,
        target: 30,
        status: "meeting",
        trend: "-3 min vs last month"
      },
      reliability: {
        current: 92,
        target: 90,
        status: "exceeding", 
        trend: "+5% vs last month"
      }
    },
    
    achievements: [
      "🏆 Top 10% performer this month",
      "⭐ 4.8+ rating for 3 consecutive months", 
      "🎯 100% completion rate this week",
      "⚡ Fastest delivery time in zone"
    ],
    
    improvementAreas: [
      "Increase daily delivery count (+2 deliveries)",
      "Maintain rating during peak hours",
      "Reduce customer wait time at pickup"
    ]
  }
}
```

---

## Implementation Plan

### Development Roadmap

```mermaid
gantt
    title Afghan Delivery Agent System Implementation
    dateFormat  YYYY-MM-DD
    section Phase 1: Foundation
    Backend Models & APIs     :done, backend, 2024-01-01, 2024-01-15
    Database Setup           :done, db, 2024-01-10, 2024-01-20
    Authentication System    :done, auth, 2024-01-15, 2024-01-25
    
    section Phase 2: Registration
    Agent Registration Form  :active, reg-form, 2024-01-20, 2024-02-05
    Document Upload System   :active, docs, 2024-01-25, 2024-02-10
    Verification Workflow    :verify, 2024-02-01, 2024-02-15
    Background Check API     :bg-check, 2024-02-05, 2024-02-20
    
    section Phase 3: Mobile App
    Core App Structure       :mobile-core, 2024-02-10, 2024-02-25
    Offline Functionality    :offline, 2024-02-15, 2024-03-01
    GPS & Location Services  :gps, 2024-02-20, 2024-03-05
    Order Management         :orders, 2024-02-25, 2024-03-10
    
    section Phase 4: Admin Dashboard
    Agent Management UI      :admin-ui, 2024-03-01, 2024-03-15
    Performance Analytics    :analytics, 2024-03-05, 2024-03-20
    Payment Processing       :payments, 2024-03-10, 2024-03-25
    Reporting System         :reports, 2024-03-15, 2024-03-30
    
    section Phase 5: Afghan Localization
    Dari/Pashto Translation  :translation, 2024-03-20, 2024-04-05
    Cultural Adaptations     :culture, 2024-03-25, 2024-04-10
    Local Payment Methods    :local-pay, 2024-03-30, 2024-04-15
    Voice Navigation         :voice, 2024-04-01, 2024-04-20
    
    section Phase 6: Testing & Launch
    System Testing           :testing, 2024-04-10, 2024-04-25
    Pilot Program            :pilot, 2024-04-20, 2024-05-05
    Training Materials       :training, 2024-04-25, 2024-05-10
    Production Launch        :launch, 2024-05-01, 2024-05-15
```

### Deployment Strategy

#### Phase 1: Kabul Pilot (Month 1-2)
```javascript
{
  scope: {
    location: "Kabul City",
    agentTarget: 50,
    restaurantPartners: 20,
    coverageAreas: ["Shar-e-Naw", "Wazir Akbar Khan", "Karte Char"]
  },
  
  objectives: {
    primary: "Validate core functionality and user experience",
    secondary: "Gather feedback and optimize workflows",
    metrics: {
      agentRetention: "> 80%",
      deliverySuccess: "> 95%", 
      customerSatisfaction: "> 4.5",
      systemUptime: "> 99%"
    }
  },
  
  features: {
    core: ["Registration", "Order Management", "Payment Collection"],
    advanced: ["Basic Analytics", "Cash Settlement"],
    excluded: ["Advanced Routing", "Multi-language Voice"]
  }
}
```

#### Phase 2: Major Cities Expansion (Month 3-4)
```javascript
{
  scope: {
    locations: ["Herat", "Kandahar", "Mazar-i-Sharif"],
    agentTarget: 200,
    restaurantPartners: 80,
    totalCoverage: "4 major cities"
  },
  
  newFeatures: [
    "Mobile wallet integration",
    "Advanced performance analytics", 
    "Multi-zone management",
    "Automated settlements"
  ],
  
  improvements: [
    "Enhanced offline capability",
    "Better GPS accuracy",
    "Faster order assignment",
    "Improved user interface"
  ]
}
```

#### Phase 3: Provincial Rollout (Month 5-6)
```javascript
{
  scope: {
    locations: "15 provincial capitals",
    agentTarget: 500,
    restaurantPartners: 200,
    ruralCoverage: "District-level expansion"
  },
  
  adaptations: [
    "Low-bandwidth optimization",
    "SMS fallback system",
    "Local language support",
    "Community-based verification"
  ]
}
```

---

## Technical Specifications

### System Requirements

#### Backend Infrastructure
```yaml
# Server Requirements
server:
  cpu: "4+ cores"
  ram: "8GB minimum, 16GB recommended"
  storage: "100GB SSD"
  bandwidth: "100Mbps"
  
# Database
database:
  type: "PostgreSQL 13+"
  storage: "50GB initial, auto-scaling"
  backup: "Daily automated backups"
  replication: "Master-slave setup"

# APIs & Services
apis:
  authentication: "JWT with refresh tokens"
  geolocation: "Google Maps API + OpenStreetMap fallback"
  payments: "Local bank APIs + mobile wallet SDKs"
  notifications: "Firebase Cloud Messaging"
  
# Caching & Performance
caching:
  redis: "For session management and real-time data"
  cdn: "CloudFlare for static assets"
  loadBalancer: "Nginx with SSL termination"
```

#### Mobile App Specifications
```yaml
# Platform Support
platforms:
  android:
    minVersion: "Android 6.0 (API 23)"
    targetVersion: "Android 13 (API 33)"
    architecture: "ARM64, ARMv7"
  
  ios:
    minVersion: "iOS 12.0"
    targetVersion: "iOS 16.0"
    architecture: "ARM64"

# App Features
features:
  offline:
    storage: "SQLite local database"
    sync: "Background synchronization"
    capacity: "1000 orders offline"
  
  location:
    gps: "High accuracy mode"
    background: "Continuous tracking when on duty"
    battery: "Optimized for 8+ hour usage"
  
  performance:
    appSize: "< 50MB"
    startup: "< 3 seconds"
    memory: "< 200MB RAM usage"
```

#### Security & Compliance
```yaml
# Data Security
security:
  encryption:
    transit: "TLS 1.3"
    storage: "AES-256"
    database: "Encrypted at rest"
  
  authentication:
    methods: ["Biometric", "PIN", "Password"]
    session: "30-day refresh cycle"
    mfa: "SMS-based 2FA for admin"
  
  privacy:
    dataRetention: "7 years for financial records"
    userConsent: "GDPR-compliant consent management"
    anonymization: "Personal data anonymization after account deletion"

# Compliance
compliance:
  local:
    afghanistan: "Central Bank regulations compliance"
    taxation: "Automated tax calculation and reporting"
    labor: "Afghan labor law compliance"
  
  international:
    gdpr: "EU data protection compliance"
    iso27001: "Information security management"
    pci: "Payment card industry compliance"
```

---

## Conclusion

The Afghan Delivery Agent System represents a comprehensive solution designed specifically for the unique challenges and opportunities of the Afghan market. By combining technical excellence with cultural sensitivity, the system provides:

### ✅ **For Delivery Agents:**
- Sustainable income opportunities with transparent earnings
- User-friendly bilingual interface with offline capabilities
- Performance-based incentives and career growth paths
- Comprehensive support and training programs

### ✅ **For Businesses:**
- Reliable delivery network with real-time tracking
- Flexible agent assignment (manual or automatic)
- Comprehensive analytics and performance monitoring
- Cost-effective scaling across multiple cities

### ✅ **For Customers:**
- Fast and reliable delivery service
- Real-time order tracking with agent details
- Multiple payment options including cash and digital
- Quality assurance through rating and feedback systems

### ✅ **For the Afghan Economy:**
- Job creation for youth and unemployed population
- Digital payment adoption and financial inclusion
- Support for local businesses and restaurants
- Technology transfer and skill development

The system is designed to be **culturally appropriate**, **technically robust**, and **economically viable**, making it an ideal solution for establishing a modern delivery infrastructure in Afghanistan while respecting local customs and overcoming infrastructure challenges.

**Ready for implementation with proper local partnerships and regulatory compliance!** 🇦🇫🚚💰

## API Documentation

### Agent Registration API

#### Register New Agent
```http
POST /api/delivery-agent/register/
Content-Type: application/json

{
  "personal_info": {
    "full_name_dari": "احمد علی",
    "full_name_pashto": "احمد علي",
    "father_name": "محمد علی",
    "national_id": "**********123",
    "date_of_birth": "1995-05-15",
    "gender": "male",
    "marital_status": "married",
    "phone_primary": "+93 70 123 4567",
    "phone_secondary": "+93 78 987 6543",
    "email": "<EMAIL>",
    "province": "kabul",
    "district": "Kabul City",
    "area": "Shar-e-Naw",
    "street_address": "Street 15, House 25"
  },
  "vehicle_info": {
    "type": "motorcycle",
    "model": "Honda 125cc",
    "year": 2020,
    "license_plate": "KBL-001",
    "color": "Red"
  },
  "references": [
    {
      "name": "محمد حسن",
      "phone": "+93 70 111 2222",
      "relation": "Uncle"
    }
  ],
  "work_preferences": {
    "working_days": ["saturday", "sunday", "monday"],
    "working_hours": {"start": "08:00", "end": "20:00"},
    "preferred_zones": ["Shar-e-Naw", "Wazir Akbar Khan"]
  }
}
```

#### Response
```json
{
  "status": "success",
  "data": {
    "agent_code": "**********",
    "user_id": "uuid-string",
    "verification_status": "pending"
  },
  "message": "Application submitted successfully",
  "next_steps": [
    "Upload required documents",
    "Wait for admin verification",
    "Complete training program"
  ]
}
```

### Order Management API

#### Get Available Orders
```http
GET /api/delivery-agent/orders/available/
Authorization: Bearer <jwt_token>
```

#### Response
```json
{
  "status": "success",
  "data": {
    "available_orders": [
      {
        "id": "ORD001",
        "restaurant": {
          "name": "کابل رستوران",
          "address": "شهر نو، کابل",
          "phone": "+93 70 123 4567",
          "location": {"lat": 34.5553, "lng": 69.2075}
        },
        "customer": {
          "name": "محمد حسن",
          "phone": "+93 78 987 6543",
          "address": "کارته چهار، کابل",
          "location": {"lat": 34.5560, "lng": 69.2080}
        },
        "order_details": {
          "total_amount": 850,
          "delivery_fee": 25,
          "payment_method": "cash",
          "items_count": 3,
          "special_instructions": "Call before delivery"
        },
        "estimated_distance": 2.3,
        "estimated_time": 15,
        "agent_earnings": 65
      }
    ],
    "agent_status": "available"
  }
}
```

#### Accept Order
```http
POST /api/delivery-agent/orders/accept/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "order_id": "ORD001",
  "action": "accept"
}
```

#### Update Order Status
```http
POST /api/delivery-agent/orders/update-status/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "order_id": "ORD001",
  "status": "picked_up",
  "location": {
    "latitude": 34.5553,
    "longitude": 69.2075,
    "address": "Near Kabul Restaurant"
  },
  "timestamp": "2024-01-20T14:30:00Z"
}
```

### Payment Collection API

#### Record Payment Collection
```http
POST /api/delivery-agent/payments/collect/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "order_id": "ORD001",
  "payment_method": "cash",
  "amount_collected": 850,
  "customer_signature": "base64_signature_data",
  "photo_proof": "base64_image_data",
  "collection_timestamp": "2024-01-20T15:45:00Z"
}
```

#### Get Earnings Summary
```http
GET /api/delivery-agent/earnings/summary/
Authorization: Bearer <jwt_token>
```

#### Response
```json
{
  "status": "success",
  "data": {
    "today": {
      "deliveries": 8,
      "earnings": 520,
      "cash_collected": 6800
    },
    "this_week": {
      "deliveries": 45,
      "earnings": 2925,
      "average_per_delivery": 65
    },
    "total": {
      "deliveries": 247,
      "earnings": 16055,
      "rating": 4.8
    },
    "pending_settlements": [
      {
        "payment_id": "PAY123456",
        "amount": 2925,
        "period": "2024-01-15 to 2024-01-21",
        "status": "pending"
      }
    ]
  }
}
```

## Database Schema

### Afghan Delivery Agent Model
```sql
CREATE TABLE afghan_delivery_agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth_user(id),
    agent_code VARCHAR(20) UNIQUE NOT NULL,

    -- Personal Information
    full_name_dari VARCHAR(200) NOT NULL,
    full_name_pashto VARCHAR(200),
    father_name VARCHAR(200) NOT NULL,
    national_id VARCHAR(20) UNIQUE NOT NULL,
    date_of_birth DATE NOT NULL,
    gender VARCHAR(10) NOT NULL,
    marital_status VARCHAR(10) NOT NULL,

    -- Contact Information
    phone_primary VARCHAR(20) NOT NULL,
    phone_secondary VARCHAR(20),
    email VARCHAR(254),

    -- Address Information
    province VARCHAR(50) NOT NULL,
    district VARCHAR(100) NOT NULL,
    area VARCHAR(100) NOT NULL,
    street_address TEXT NOT NULL,
    nearby_landmark VARCHAR(200),

    -- Vehicle Information
    vehicle_type VARCHAR(20) NOT NULL,
    vehicle_model VARCHAR(100) NOT NULL,
    vehicle_year INTEGER NOT NULL,
    license_plate VARCHAR(20),
    vehicle_color VARCHAR(50) NOT NULL,

    -- Documents
    tazkira_front VARCHAR(255),
    tazkira_back VARCHAR(255),
    driving_license VARCHAR(255),
    vehicle_registration VARCHAR(255),
    profile_photo VARCHAR(255),

    -- Document Status
    tazkira_status VARCHAR(20) DEFAULT 'pending',
    license_status VARCHAR(20) DEFAULT 'pending',
    vehicle_reg_status VARCHAR(20) DEFAULT 'pending',

    -- References
    reference1_name VARCHAR(200) NOT NULL,
    reference1_phone VARCHAR(20) NOT NULL,
    reference1_relation VARCHAR(100) NOT NULL,
    reference2_name VARCHAR(200) NOT NULL,
    reference2_phone VARCHAR(20) NOT NULL,
    reference2_relation VARCHAR(100) NOT NULL,

    -- Banking Information
    bank_name VARCHAR(100),
    account_number VARCHAR(50),
    account_holder_name VARCHAR(200),
    mobile_wallet_number VARCHAR(20),

    -- Emergency Contact
    emergency_contact_name VARCHAR(200) NOT NULL,
    emergency_contact_phone VARCHAR(20) NOT NULL,
    emergency_contact_relation VARCHAR(100) NOT NULL,

    -- Work Information
    preferred_working_hours_start TIME DEFAULT '08:00',
    preferred_working_hours_end TIME DEFAULT '20:00',
    working_days JSONB DEFAULT '[]',
    preferred_zones JSONB DEFAULT '[]',

    -- Status and Performance
    status VARCHAR(20) DEFAULT 'pending',
    availability VARCHAR(20) DEFAULT 'offline',
    is_verified BOOLEAN DEFAULT FALSE,
    is_trained BOOLEAN DEFAULT FALSE,

    -- Performance Metrics
    total_deliveries INTEGER DEFAULT 0,
    successful_deliveries INTEGER DEFAULT 0,
    total_earnings DECIMAL(12,2) DEFAULT 0.00,
    rating DECIMAL(3,2) DEFAULT 0.00,

    -- Location Tracking
    current_latitude DECIMAL(10,8),
    current_longitude DECIMAL(11,8),
    current_address VARCHAR(500),
    last_location_update TIMESTAMP,

    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP,
    last_active TIMESTAMP,

    -- Training and Compliance
    training_completed_date TIMESTAMP,
    background_check_status VARCHAR(20) DEFAULT 'pending',
    background_check_date TIMESTAMP,

    -- Medical Information
    medical_conditions TEXT,
    has_insurance BOOLEAN DEFAULT FALSE,
    insurance_details TEXT,

    CONSTRAINT valid_gender CHECK (gender IN ('male', 'female')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'active', 'inactive', 'suspended', 'terminated')),
    CONSTRAINT valid_availability CHECK (availability IN ('available', 'busy', 'offline', 'break')),
    CONSTRAINT valid_rating CHECK (rating >= 0 AND rating <= 5)
);

-- Indexes for performance
CREATE INDEX idx_agents_status ON afghan_delivery_agents(status);
CREATE INDEX idx_agents_availability ON afghan_delivery_agents(availability);
CREATE INDEX idx_agents_location ON afghan_delivery_agents(current_latitude, current_longitude);
CREATE INDEX idx_agents_province ON afghan_delivery_agents(province);
CREATE INDEX idx_agents_rating ON afghan_delivery_agents(rating);
CREATE INDEX idx_agents_last_active ON afghan_delivery_agents(last_active);
```

### Agent Training Model
```sql
CREATE TABLE agent_training (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES afghan_delivery_agents(id),
    training_type VARCHAR(100) NOT NULL,
    training_date TIMESTAMP NOT NULL,
    trainer_name VARCHAR(200) NOT NULL,
    duration_hours INTEGER NOT NULL,
    score DECIMAL(5,2),
    passed BOOLEAN DEFAULT FALSE,
    certificate_issued BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Agent Payment Model
```sql
CREATE TABLE agent_payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID REFERENCES afghan_delivery_agents(id),
    payment_id UUID UNIQUE DEFAULT gen_random_uuid(),
    amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'AFN',
    payment_method VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',

    -- Period covered
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,

    -- Breakdown
    base_earnings DECIMAL(12,2) DEFAULT 0.00,
    bonus_amount DECIMAL(12,2) DEFAULT 0.00,
    deductions DECIMAL(12,2) DEFAULT 0.00,

    -- Payment details
    transaction_reference VARCHAR(100),
    paid_at TIMESTAMP,
    notes TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT valid_payment_method CHECK (payment_method IN ('bank_transfer', 'mobile_wallet', 'cash', 'check')),
    CONSTRAINT valid_payment_status CHECK (status IN ('pending', 'processing', 'paid', 'failed', 'cancelled'))
);
```

## Testing Strategy

### Unit Testing
```python
# Test agent registration
def test_agent_registration():
    """Test agent registration with valid data"""
    agent_data = {
        'full_name_dari': 'احمد علی',
        'national_id': '**********123',
        'phone_primary': '+93 70 123 4567',
        # ... other required fields
    }

    response = client.post('/api/delivery-agent/register/', agent_data)
    assert response.status_code == 201
    assert 'agent_code' in response.json()['data']

def test_order_assignment():
    """Test order assignment algorithm"""
    # Create test agents and orders
    agent = create_test_agent(location=(34.5553, 69.2075))
    order = create_test_order(pickup_location=(34.5555, 69.2070))

    # Test assignment
    assigned_agent = assign_order_to_agent(order.id)
    assert assigned_agent.id == agent.id

def test_earnings_calculation():
    """Test agent earnings calculation"""
    order = create_test_order(total_amount=850, delivery_fee=25)
    earnings = calculate_agent_earnings(order)

    expected_earnings = 50 + (25 * 0.7) - ((50 + 17.5) * 0.15)
    assert abs(earnings - expected_earnings) < 0.01
```

### Integration Testing
```python
# Test complete order workflow
def test_complete_order_workflow():
    """Test end-to-end order processing"""
    # Setup
    agent = create_test_agent()
    restaurant = create_test_restaurant()
    customer = create_test_customer()

    # Create order
    order = create_order(restaurant, customer, items=[...])

    # Assign to agent
    assign_order(order.id, agent.id)
    assert order.status == 'assigned'

    # Agent picks up
    update_order_status(order.id, 'picked_up', agent.id)
    assert order.status == 'picked_up'

    # Agent delivers
    update_order_status(order.id, 'delivered', agent.id)
    assert order.status == 'delivered'

    # Payment collection
    collect_payment(order.id, amount=order.total_amount)
    assert order.payment_status == 'paid'

    # Verify agent earnings
    agent.refresh_from_db()
    assert agent.total_deliveries == 1
    assert agent.total_earnings > 0
```

### Performance Testing
```python
# Load testing scenarios
def test_concurrent_order_assignments():
    """Test system under concurrent order load"""
    # Simulate 100 concurrent orders
    orders = [create_test_order() for _ in range(100)]
    agents = [create_test_agent() for _ in range(20)]

    # Assign orders concurrently
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(assign_order_to_agent, order.id)
                  for order in orders]

        results = [future.result() for future in futures]

    # Verify all orders assigned
    assert len([r for r in results if r is not None]) == 100

    # Verify no agent overloaded
    for agent in agents:
        assigned_count = Order.objects.filter(
            delivery_agent=agent,
            status='assigned'
        ).count()
        assert assigned_count <= 5  # Max 5 concurrent orders per agent
```

---

*Last Updated: January 2024*
*Version: 1.0*
*Document Status: Complete*
