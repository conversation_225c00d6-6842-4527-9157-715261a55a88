import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import { authApi } from "../../utils/authApi";
import Button from "../../components/common/Button";
import Input from "../../components/common/Input";

const LoginDebugTest = () => {
  const { user, isAuthenticated, login, logout } = useAuth();
  const [testCredentials, setTestCredentials] = useState({
    username: "ahmad123", // Default test credentials
    password: "123",
  });
  const [apiTestResult, setApiTestResult] = useState(null);
  const [contextTestResult, setContextTestResult] = useState(null);
  const [loading, setLoading] = useState(false);

  // Monitor localStorage changes
  const [storedUser, setStoredUser] = useState(null);

  useEffect(() => {
    const checkStoredUser = () => {
      try {
        const stored = localStorage.getItem("afghanSofraUser");
        setStoredUser(stored ? JSON.parse(stored) : null);
      } catch (error) {
        console.error("Error parsing stored user:", error);
        setStoredUser(null);
      }
    };

    checkStoredUser();

    // Check every second for changes
    const interval = setInterval(checkStoredUser, 1000);
    return () => clearInterval(interval);
  }, []);

  const testDirectApiLogin = async () => {
    setLoading(true);
    setApiTestResult(null);

    try {
      console.log("🧪 Testing direct API login...");
      const result = await authApi.login({
        user_name: testCredentials.username,
        password: testCredentials.password,
      });

      console.log("🧪 Direct API result:", result);
      setApiTestResult(result);
    } catch (error) {
      console.error("🧪 Direct API error:", error);
      setApiTestResult({
        success: false,
        error: error.message,
      });
    }

    setLoading(false);
  };

  const testContextLogin = async () => {
    setLoading(true);
    setContextTestResult(null);

    try {
      console.log("🧪 Testing context login...");
      const result = await login(
        testCredentials.username,
        testCredentials.password
      );

      console.log("🧪 Context login result:", result);
      setContextTestResult(result);
    } catch (error) {
      console.error("🧪 Context login error:", error);
      setContextTestResult({
        success: false,
        error: error.message,
      });
    }

    setLoading(false);
  };

  const clearStorage = () => {
    localStorage.removeItem("afghanSofraUser");
    logout();
    setStoredUser(null);
    console.log("🧹 Cleared localStorage and logged out");
  };

  const renderResult = (result, title) => {
    if (!result) return null;

    return (
      <div className='mt-4 p-4 border rounded-lg'>
        <h4 className='font-semibold text-lg mb-2'>{title}:</h4>
        <div
          className={`p-3 rounded ${
            result.success
              ? "bg-green-50 border-green-200"
              : "bg-red-50 border-red-200"
          }`}
        >
          <p
            className={`font-medium ${
              result.success ? "text-green-800" : "text-red-800"
            }`}
          >
            {result.success ? "Success" : "Error"}
          </p>
          {result.error && (
            <p className='text-sm mt-1 text-red-600'>{result.error}</p>
          )}
          {result.data && (
            <details className='mt-2'>
              <summary className='cursor-pointer text-sm font-medium'>
                View Response Data
              </summary>
              <pre className='mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40'>
                {JSON.stringify(result.data, null, 2)}
              </pre>
            </details>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-4xl mx-auto'>
        <h1 className='text-3xl font-bold mb-8'>Login Debug Test</h1>

        {/* Current Auth State */}
        <div className='mb-8 p-4 bg-blue-50 border-l-4 border-blue-500'>
          <h2 className='text-xl font-semibold mb-4'>
            Current Authentication State
          </h2>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div>
              <p>
                <strong>Is Authenticated:</strong>{" "}
                {isAuthenticated ? "✅ Yes" : "❌ No"}
              </p>
              <p>
                <strong>User Object:</strong>
              </p>
              <pre className='text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto max-h-32'>
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
            <div>
              <p>
                <strong>Stored in localStorage:</strong>
              </p>
              <pre className='text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto max-h-32'>
                {JSON.stringify(storedUser, null, 2)}
              </pre>
            </div>
          </div>
        </div>

        {/* Test Credentials */}
        <div className='mb-8 p-4 border rounded-lg'>
          <h2 className='text-xl font-semibold mb-4'>Test Credentials</h2>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <Input
              label='Username'
              value={testCredentials.username}
              onChange={(e) =>
                setTestCredentials((prev) => ({
                  ...prev,
                  username: e.target.value,
                }))
              }
            />
            <Input
              label='Password'
              type='password'
              value={testCredentials.password}
              onChange={(e) =>
                setTestCredentials((prev) => ({
                  ...prev,
                  password: e.target.value,
                }))
              }
            />
          </div>
        </div>

        {/* Test Actions */}
        <div className='mb-8'>
          <h2 className='text-xl font-semibold mb-4'>Test Actions</h2>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
            <Button
              onClick={testDirectApiLogin}
              disabled={loading}
              className='w-full'
            >
              {loading ? "Testing..." : "Test Direct API"}
            </Button>

            <Button
              onClick={testContextLogin}
              disabled={loading}
              className='w-full'
            >
              {loading ? "Testing..." : "Test Context Login"}
            </Button>

            <Button
              onClick={logout}
              className='w-full bg-yellow-500 hover:bg-yellow-600'
            >
              Logout
            </Button>

            <Button
              onClick={clearStorage}
              className='w-full bg-red-500 hover:bg-red-600'
            >
              Clear Storage
            </Button>
          </div>
        </div>

        {/* Results */}
        <div className='space-y-4'>
          {renderResult(apiTestResult, "Direct API Test Result")}
          {renderResult(contextTestResult, "Context Login Test Result")}
        </div>

        {/* Debug Information */}
        <div className='mt-8 bg-gray-50 border-l-4 border-gray-500 p-4'>
          <h3 className='font-semibold text-gray-800 mb-2'>
            Debug Information:
          </h3>
          <ul className='text-gray-700 text-sm space-y-1'>
            <li>
              <strong>API Base URL:</strong> https://afghansufra.luilala.com/api
            </li>
            <li>
              <strong>Login Endpoint:</strong> POST /auth/login/
            </li>
            <li>
              <strong>Expected Response:</strong> Should contain user data with
              access_token
            </li>
            <li>
              <strong>Storage Key:</strong> afghanSofraUser
            </li>
            <li>
              <strong>Auth Check:</strong> Looks for user.id, user.user_id, or
              user.pk
            </li>
          </ul>
        </div>

        {/* Instructions */}
        <div className='mt-8 bg-yellow-50 border-l-4 border-yellow-500 p-4'>
          <h3 className='font-semibold text-yellow-800 mb-2'>How to Debug:</h3>
          <ol className='text-yellow-700 text-sm space-y-1 list-decimal list-inside'>
            <li>Open browser console to see detailed logs</li>
            <li>Test direct API call first to verify credentials work</li>
            <li>Test context login to see if the issue is in the context</li>
            <li>Check if user data has the required ID field</li>
            <li>Verify the user role matches expected values</li>
            <li>Check if access_token is present in the response</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default LoginDebugTest;
