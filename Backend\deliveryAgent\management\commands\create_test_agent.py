from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from deliveryAgent.models import DeliveryAgentProfile

User = get_user_model()

class Command(BaseCommand):
    help = 'Create a test delivery agent for testing'

    def handle(self, *args, **options):
        # Check if user DA007 exists
        try:
            user = User.objects.get(user_name='DA007')
            self.stdout.write(f"User DA007 exists: {user.user_name}, Role: {user.role}")

            # Update user to ensure correct role and status
            user.role = 'delivery_agent'
            user.is_active = True
            user.is_verified = True
            user.save()
            self.stdout.write(f"Updated user role and status")
            
            # Check if delivery agent profile exists
            try:
                agent = DeliveryAgentProfile.objects.get(user=user)
                self.stdout.write(f"Agent profile exists: {agent.agent_id}, Status: {agent.status}")
                
                # Update agent to ensure it's properly configured
                agent.status = 'approved'
                agent.employment_status = 'active'
                agent.is_verified = True
                agent.is_online = False
                agent.availability = 'offline'
                agent.save()
                self.stdout.write(self.style.SUCCESS(f"Updated agent profile: {agent.agent_id}"))
                
            except DeliveryAgentProfile.DoesNotExist:
                self.stdout.write("No delivery agent profile found for DA007, creating one...")
                
                # Create delivery agent profile
                agent = DeliveryAgentProfile.objects.create(
                    user=user,
                    agent_id='DA007',
                    full_name='Test Delivery Agent',
                    phone_number='+93701234007',
                    email='<EMAIL>',
                    status='approved',
                    employment_status='active',
                    is_verified=True,
                    vehicle_type='motorcycle',
                    province='Kabul',
                    district='District 1',
                    area='Area 1',
                    street_address='Test Street',
                    is_online=False,
                    availability='offline'
                )
                self.stdout.write(self.style.SUCCESS(f"Created agent profile: {agent.agent_id}"))
                
        except User.DoesNotExist:
            self.stdout.write("User DA007 does not exist, creating...")

            # Create the user with unique phone and email
            user = User.objects.create_user(
                user_name='DA007',
                name='Test Delivery Agent',
                phone='+93701234007',
                email='<EMAIL>',
                password='DA8645YR',
                role='delivery_agent',
                is_active=True,
                is_verified=True
            )
            self.stdout.write(f"Created user: {user.user_name}")
            
            # Create delivery agent profile
            agent = DeliveryAgentProfile.objects.create(
                user=user,
                agent_id='DA007',
                full_name='Test Delivery Agent',
                phone_number='+93701234007',
                email='<EMAIL>',
                status='approved',
                employment_status='active',
                is_verified=True,
                vehicle_type='motorcycle',
                province='Kabul',
                district='District 1',
                area='Area 1',
                street_address='Test Street',
                is_online=False,
                availability='offline'
            )
            self.stdout.write(self.style.SUCCESS(f"Created agent profile: {agent.agent_id}"))

        # Display login credentials
        self.stdout.write(self.style.SUCCESS("\n" + "="*50))
        self.stdout.write(self.style.SUCCESS("TEST DELIVERY AGENT CREATED"))
        self.stdout.write(self.style.SUCCESS("="*50))
        self.stdout.write(f"Username: DA007")
        self.stdout.write(f"Password: DA8645YR")
        self.stdout.write(f"Role: delivery_agent")
        self.stdout.write(f"Status: approved")
        self.stdout.write(f"Employment: active")
        self.stdout.write(self.style.SUCCESS("="*50))
