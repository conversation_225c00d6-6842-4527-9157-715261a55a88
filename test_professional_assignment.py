import requests
import json

def test_professional_assignment():
    """Test the professional assignment interface"""
    
    print("🏢 Testing Professional Assignment Interface")
    print("=" * 60)
    
    # Login as admin first
    login_url = "http://127.0.0.1:8000/api/auth/login/"
    login_data = {
        "user_name": "admin_user_3602",
        "password": "admin123"
    }
    
    print("1. Logging in as admin...")
    login_response = requests.post(login_url, json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Admin login failed: {login_response.status_code}")
        return
    
    login_result = login_response.json()
    if not login_result.get('success'):
        print(f"❌ Admin login failed: {login_result}")
        return
    
    token = login_result['data']['access_token']
    print("✅ Admin login successful")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test the APIs
    print("\n2. Testing assignment APIs...")
    
    orders_url = "http://127.0.0.1:8000/api/order/orders/?status=ready"
    agents_url = "http://127.0.0.1:8000/api/delivery-agent/admin/employees/"
    
    orders_response = requests.get(orders_url, headers=headers)
    agents_response = requests.get(agents_url, headers=headers)
    
    if orders_response.status_code == 200 and agents_response.status_code == 200:
        orders_data = orders_response.json()
        agents_data = agents_response.json()
        
        ready_orders = orders_data
        available_agents = agents_data.get('data', {}).get('employees', [])
        
        print(f"✅ APIs working correctly")
        print(f"   📦 Ready Orders: {len(ready_orders)}")
        print(f"   👥 Available Agents: {len(available_agents)}")
        
        # Test assignment functionality
        if ready_orders and available_agents:
            test_order = ready_orders[0]  # Use first order for testing
            test_agent = available_agents[0]  # Use first agent for testing
            
            print(f"\n3. Testing assignment functionality...")
            print(f"   Test Order: #{test_order.get('id')} - {test_order.get('customer', {}).get('name', 'Unknown')}")
            print(f"   Test Agent: {test_agent.get('full_name')} ({test_agent.get('agent_id')})")
            
            # Test assignment API
            assignment_url = "http://127.0.0.1:8000/api/delivery-agent/admin/assignments/"
            assignment_data = {
                "order_id": test_order.get('id'),
                "agent_id": test_agent.get('agent_id'),
                "notes": "Test assignment from professional interface"
            }
            
            print(f"   Testing assignment API endpoint...")
            assignment_response = requests.post(assignment_url, json=assignment_data, headers=headers)
            
            if assignment_response.status_code == 200:
                print(f"   ✅ Assignment API working")
            else:
                print(f"   ⚠️  Assignment API returned: {assignment_response.status_code}")
                print(f"   Response: {assignment_response.text[:200]}...")
        
        print(f"\n🎯 Professional Assignment Features:")
        print(f"   ✅ Two-view interface (Cards & Assignment Table)")
        print(f"   ✅ Professional assignment table with dropdowns")
        print(f"   ✅ Quick assignment from dropdown menus")
        print(f"   ✅ Bulk selection with checkboxes")
        print(f"   ✅ Bulk assignment panel")
        print(f"   ✅ Order priority indicators")
        print(f"   ✅ Agent performance metrics")
        print(f"   ✅ Real-time filtering and search")
        print(f"   ✅ Professional UI/UX design")
        
        print(f"\n📋 Assignment Table Features:")
        print(f"   • Select individual orders with checkboxes")
        print(f"   • Select all / Clear all functionality")
        print(f"   • Quick assign via dropdown per order")
        print(f"   • Priority badges (Urgent, High, High Value)")
        print(f"   • Agent ratings and delivery counts")
        print(f"   • Sortable columns")
        
        print(f"\n🔄 Bulk Assignment Features:")
        print(f"   • Select multiple orders")
        print(f"   • View selected orders summary")
        print(f"   • Calculate total value")
        print(f"   • Choose agent for all selected orders")
        print(f"   • Assign all at once")
        
        print(f"\n🚀 How to Use:")
        print(f"   1. Navigate to: http://localhost:5173/admin/order-assignments")
        print(f"   2. Login with: admin_user_3602 / admin123")
        print(f"   3. Toggle between 'Cards' and 'Assignment' views")
        print(f"   4. In Assignment view:")
        print(f"      • Use dropdowns to quickly assign individual orders")
        print(f"      • Check multiple orders for bulk assignment")
        print(f"      • Use the bulk assignment panel for multiple orders")
        print(f"   5. Filter and search to find specific orders/agents")
        
        # Check for Order #65 specifically
        order_65 = next((order for order in ready_orders if order.get('id') == 65), None)
        if order_65:
            print(f"\n🎯 Order #65 Status:")
            print(f"   ✅ Found: {order_65.get('customer', {}).get('name')} - ${order_65.get('total_amount')}")
            print(f"   ✅ Ready for assignment in both views")
            print(f"   ✅ Can be assigned via dropdown or bulk selection")
        else:
            print(f"\n❌ Order #65 not found in ready orders")
            
    else:
        print(f"❌ API calls failed")
        print(f"   Orders API: {orders_response.status_code}")
        print(f"   Agents API: {agents_response.status_code}")

if __name__ == "__main__":
    test_professional_assignment()
