#!/usr/bin/env python3
"""
Test the address API to see if it's working correctly
"""

import requests
import json

def test_address_api():
    """Test the address API with customer credentials"""
    
    print("🔍 Testing Address API")
    print("=" * 40)
    
    # Step 1: Login as test customer
    login_url = "http://127.0.0.1:8000/api/auth/login/"
    login_data = {
        "user_name": "test_customer_3267",  # Actual username from database
        "password": "testpass123"
    }
    
    print("1. Logging in as test customer...")
    try:
        login_response = requests.post(login_url, json=login_data)
        print(f"   Login status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            print(f"   ✅ Login successful")
            print(f"   Response: {login_result}")

            # Try different possible token field names
            access_token = (login_result.get('access_token') or
                          login_result.get('access') or
                          login_result.get('token') or
                          login_result.get('data', {}).get('access_token'))

            print(f"   Token: {access_token[:20]}..." if access_token else "   ❌ No token received")

            if not access_token:
                print("   ❌ No access token received")
                return
                
        else:
            print(f"   ❌ Login failed: {login_response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return
    
    # Step 2: Test address API
    print("\n2. Testing address API...")
    address_url = "http://127.0.0.1:8000/api/restaurant/addresses/"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        address_response = requests.get(address_url, headers=headers)
        print(f"   Address API status: {address_response.status_code}")
        
        if address_response.status_code == 200:
            addresses = address_response.json()
            print(f"   ✅ API working - Found {len(addresses)} addresses")
            
            for i, addr in enumerate(addresses):
                print(f"   Address {i+1}:")
                print(f"     ID: {addr.get('id')}")
                print(f"     Street: {addr.get('street')}")
                print(f"     City: {addr.get('city')}")
                print(f"     State: {addr.get('state')}")
                print(f"     Country: {addr.get('country')}")
                print(f"     Coordinates: {addr.get('latitude')}, {addr.get('longitude')}")
                print()
                
            if len(addresses) == 0:
                print("   ⚠️ No addresses found for this customer")
                print("   This is why the checkout button is disabled!")
                
                # Create a test address
                print("\n3. Creating test address...")
                create_address_data = {
                    "street": "123 Test Checkout Street",
                    "city": "Kabul",
                    "state": "Kabul Province", 
                    "postal_code": "1001",
                    "country": "Afghanistan",
                    "latitude": "34.5600",
                    "longitude": "69.2100"
                }
                
                create_response = requests.post(address_url, json=create_address_data, headers=headers)
                print(f"   Create address status: {create_response.status_code}")
                
                if create_response.status_code == 201:
                    new_address = create_response.json()
                    print(f"   ✅ Address created successfully!")
                    print(f"   New address ID: {new_address.get('id')}")
                    print(f"   Address: {new_address.get('street')}, {new_address.get('city')}")
                else:
                    print(f"   ❌ Failed to create address: {create_response.text}")
                    
        else:
            print(f"   ❌ Address API failed: {address_response.text}")
            
    except Exception as e:
        print(f"   ❌ Address API error: {e}")
    
    print(f"\n🎯 SOLUTION:")
    print(f"   1. The customer needs to have saved addresses")
    print(f"   2. Login as: <EMAIL> / testpass123")
    print(f"   3. Go to checkout and the address should now be available")
    print(f"   4. Select the address to enable the Place Order button")

if __name__ == '__main__':
    test_address_api()
