/**
 * AddressSelector - Simple and reliable address selection component
 * Uses the new address service for all operations
 */

import React, { useState } from "react";
import {
  MapPin,
  Plus,
  Check,
  Edit,
  Trash2,
  Home,
  Building,
  Navigation,
} from "lucide-react";
import { Button } from "../common/Button";
import { Card } from "../common/Card";
import useAddresses from "../../hooks/useAddresses";
import AddressForm from "./AddressForm";

const AddressSelector = ({ onAddressSelect, className = "" }) => {
  const {
    addresses,
    selectedAddress,
    loading,
    error,
    selectAddress,
    deleteAddress,
    hasAddresses,
    isAddressSelected,
  } = useAddresses();

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);

  // Handle address selection
  const handleSelectAddress = async (address) => {
    const selected = await selectAddress(address);
    if (selected && onAddressSelect) {
      onAddressSelect(selected);
    }
  };

  // Handle address deletion
  const handleDeleteAddress = async (id) => {
    if (window.confirm("Are you sure you want to delete this address?")) {
      await deleteAddress(id);
    }
  };

  // Get icon for address type
  const getAddressIcon = (type) => {
    switch (type) {
      case "home":
        return <Home size={20} />;
      case "work":
        return <Building size={20} />;
      case "current":
        return <Navigation size={20} />;
      default:
        return <MapPin size={20} />;
    }
  };

  // Handle form success
  const handleFormSuccess = (address) => {
    setShowAddForm(false);
    setEditingAddress(null);
    if (address && onAddressSelect) {
      onAddressSelect(address);
    }
  };

  if (loading && !hasAddresses) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className='flex items-center justify-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
          <span className='ml-3 text-gray-600'>Loading addresses...</span>
        </div>
      </Card>
    );
  }

  return (
    <div className={className}>
      {/* Error Message */}
      {error && (
        <div className='mb-4 p-4 bg-red-50 border border-red-200 rounded-lg'>
          <p className='text-red-800 text-sm'>{error}</p>
        </div>
      )}

      {/* Add Address Form */}
      {showAddForm && (
        <Card className='mb-4 p-6'>
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold'>Add New Address</h3>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setShowAddForm(false)}
            >
              Cancel
            </Button>
          </div>
          <AddressForm
            onSuccess={handleFormSuccess}
            onCancel={() => setShowAddForm(false)}
          />
        </Card>
      )}

      {/* Edit Address Form */}
      {editingAddress && (
        <Card className='mb-4 p-6'>
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold'>Edit Address</h3>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setEditingAddress(null)}
            >
              Cancel
            </Button>
          </div>
          <AddressForm
            address={editingAddress}
            onSuccess={handleFormSuccess}
            onCancel={() => setEditingAddress(null)}
          />
        </Card>
      )}

      {/* Address List */}
      <Card className='p-6'>
        <div className='flex items-center justify-between mb-4'>
          <h3 className='text-lg font-semibold'>Delivery Address</h3>
          <Button
            variant='outline'
            size='sm'
            onClick={() => setShowAddForm(true)}
            className='flex items-center space-x-2'
          >
            <Plus size={16} />
            <span>Add Address</span>
          </Button>
        </div>

        {!hasAddresses ? (
          <div className='text-center py-8'>
            <MapPin size={48} className='mx-auto text-gray-400 mb-4' />
            <h4 className='text-lg font-medium text-gray-900 mb-2'>
              No addresses saved
            </h4>
            <p className='text-gray-600 mb-4'>
              Add your first delivery address to continue
            </p>
            <Button onClick={() => setShowAddForm(true)}>
              Add Your First Address
            </Button>
          </div>
        ) : (
          <div className='space-y-3'>
            {addresses.map((address) => (
              <div
                key={address.id}
                className={`relative border rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                  isAddressSelected(address.id)
                    ? "border-blue-500 bg-blue-50 shadow-md"
                    : "border-gray-200 hover:border-gray-300 hover:shadow-sm"
                }`}
                onClick={() => handleSelectAddress(address)}
              >
                {/* Selection indicator */}
                {isAddressSelected(address.id) && (
                  <div className='absolute top-0 left-0 w-full h-1 bg-blue-500 rounded-t-lg'></div>
                )}

                <div className='flex items-start justify-between'>
                  <div className='flex items-start space-x-3 flex-1'>
                    {/* Icon */}
                    <div
                      className={`flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center ${
                        isAddressSelected(address.id)
                          ? "bg-blue-500 text-white"
                          : "bg-gray-100 text-gray-600"
                      }`}
                    >
                      {getAddressIcon(address.type)}
                    </div>

                    {/* Address details */}
                    <div className='flex-1 min-w-0'>
                      <div className='flex items-center space-x-2 mb-1'>
                        <h4 className='font-medium text-gray-900 truncate'>
                          {address.label}
                        </h4>
                        {isAddressSelected(address.id) && (
                          <Check
                            size={16}
                            className='text-blue-500 flex-shrink-0'
                          />
                        )}
                      </div>
                      <p className='text-sm text-gray-600 line-clamp-2'>
                        {address.address}
                      </p>
                      {address.backendId && (
                        <p className='text-xs text-green-600 mt-1'>
                          ✓ Saved (ID: {address.backendId})
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Action buttons */}
                  <div className='flex items-center space-x-1 ml-2'>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingAddress(address);
                      }}
                      className='p-2'
                    >
                      <Edit size={14} />
                    </Button>
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteAddress(address.id);
                      }}
                      className='p-2 text-red-600 hover:text-red-700 hover:bg-red-50'
                    >
                      <Trash2 size={14} />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Selected address info */}
        {selectedAddress && (
          <div className='mt-4 p-3 bg-green-50 border border-green-200 rounded-lg'>
            <div className='flex items-center space-x-2'>
              <Check size={16} className='text-green-600' />
              <span className='text-sm font-medium text-green-800'>
                Selected for delivery
              </span>
            </div>
            <p className='text-sm text-green-700 mt-1'>
              {selectedAddress.address}
            </p>
          </div>
        )}

        {/* Loading indicator */}
        {loading && (
          <div className='mt-4 flex items-center justify-center py-2'>
            <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600'></div>
            <span className='ml-2 text-sm text-gray-600'>Updating...</span>
          </div>
        )}
      </Card>
    </div>
  );
};

export default AddressSelector;
