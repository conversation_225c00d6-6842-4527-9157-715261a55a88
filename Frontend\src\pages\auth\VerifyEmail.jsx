import React, { useState } from "react";
import {
  useNavigate,
  useSearchParams,
  Link,
  useLocation,
} from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import EmailVerification from "../../components/auth/EmailVerification";
import { Mail, ArrowLeft, AlertCircle, CheckCircle } from "lucide-react";

const VerifyEmail = () => {
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { verifyEmail, resendEmailVerification } = useAuth();
  const [email, setEmail] = useState(searchParams.get("email") || "");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(location.state?.message || null);
  const redirectPath = searchParams.get("redirect");
  const userRole = location.state?.userRole;
  const [showEmailInput, setShowEmailInput] = useState(!email);

  // Email verification handler
  const handleVerification = async (otp) => {
    if (!email) {
      setError("Email is required");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await verifyEmail(email, otp);
      if (result.success) {
        // Determine redirect path based on user role and redirect parameter
        let loginRedirect = "/login";
        let message = "Email verified successfully! You can now login.";

        if (
          userRole === "restaurant" &&
          redirectPath === "register-restaurant"
        ) {
          loginRedirect = "/login?redirect=register-restaurant";
          message =
            "Email verified successfully! You can now login and complete your restaurant application.";
        }

        navigate(loginRedirect, {
          state: {
            message: message,
          },
        });
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError("Verification failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Resend verification code
  const handleResend = async () => {
    if (!email) {
      throw new Error("No email address available for resend");
    }

    const result = await resendEmailVerification(email);
    if (!result.success) {
      throw new Error(result.error || "Failed to resend verification code");
    }
  };

  // Email input form
  const handleEmailSubmit = (e) => {
    e.preventDefault();
    if (email.trim()) {
      setShowEmailInput(false);
    }
  };

  if (showEmailInput) {
    return (
      <div className='animate-fade-in'>
        <div className='text-center mb-8'>
          <div className='w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4'>
            <Mail size={32} className='text-primary-500' />
          </div>
          <h1 className='text-2xl font-poppins font-bold text-text-primary mb-2'>
            Email Verification
          </h1>
          <p className='text-text-secondary'>
            Enter your email address to verify your account
          </p>
        </div>

        {success && (
          <div className='flex items-start p-4 mb-6 bg-green-50 border border-green-200 rounded-lg'>
            <CheckCircle
              size={18}
              className='text-green-600 mr-2 mt-0.5 flex-shrink-0'
            />
            <p className='text-green-600 text-sm'>{success}</p>
          </div>
        )}

        {error && (
          <div className='flex items-start p-4 mb-6 bg-red-50 border border-red-200 rounded-lg'>
            <AlertCircle
              size={18}
              className='text-accent-red mr-2 mt-0.5 flex-shrink-0'
            />
            <p className='text-accent-red text-sm'>{error}</p>
          </div>
        )}

        <form onSubmit={handleEmailSubmit} className='space-y-6'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Email Address
            </label>
            <input
              type='email'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder='Enter your email address'
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              required
            />
          </div>

          <button
            type='submit'
            className='w-full bg-primary-500 text-white py-2 px-4 rounded-lg hover:bg-primary-600 transition-colors'
          >
            Continue
          </button>
        </form>

        <div className='mt-6 text-center'>
          <Link
            to='/login'
            className='inline-flex items-center text-primary-500 hover:text-primary-600 transition-colors'
          >
            <ArrowLeft size={16} className='mr-1' />
            Back to Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <EmailVerification
      email={email}
      onVerify={handleVerification}
      onBack={() => setShowEmailInput(true)}
      onResend={handleResend}
      loading={loading}
      error={error}
    />
  );
};

export default VerifyEmail;
