import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { Search, Filter, X, TrendingUp, Clock, Hash } from 'lucide-react';
import Pagination from './Pagination';
import PaginationInfo from './PaginationInfo';

const SearchPagination = ({
  onSearch,
  onFilter,
  searchPlaceholder = 'Search...',
  filters = [],
  searchQuery = '',
  appliedFilters = {},
  results = [],
  pagination = {},
  loading = false,
  error = null,
  searchMetrics = {},
  showMetrics = true,
  showFilters = true,
  showAdvancedSearch = false,
  recentSearches = [],
  onSaveSearch,
  className = '',
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [searchHistory, setSearchHistory] = useState(recentSearches);
  const [searchSuggestions, setSearchSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (localSearchQuery !== searchQuery) {
        handleSearch(localSearchQuery);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [localSearchQuery, searchQuery]);

  // Handle search
  const handleSearch = useCallback((query) => {
    if (onSearch) {
      onSearch(query);
      
      // Add to search history
      if (query && !searchHistory.includes(query)) {
        const newHistory = [query, ...searchHistory.slice(0, 9)]; // Keep last 10
        setSearchHistory(newHistory);
        localStorage.setItem('searchHistory', JSON.stringify(newHistory));
      }
    }
  }, [onSearch, searchHistory]);

  // Handle filter change
  const handleFilterChange = useCallback((filterKey, value) => {
    if (onFilter) {
      const newFilters = { ...appliedFilters };
      if (value) {
        newFilters[filterKey] = value;
      } else {
        delete newFilters[filterKey];
      }
      onFilter(newFilters);
    }
  }, [onFilter, appliedFilters]);

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    if (onFilter) {
      onFilter({});
    }
  }, [onFilter]);

  // Clear search
  const clearSearch = useCallback(() => {
    setLocalSearchQuery('');
    handleSearch('');
  }, [handleSearch]);

  // Generate search suggestions
  const generateSuggestions = useCallback((query) => {
    if (!query || query.length < 2) {
      setSearchSuggestions([]);
      return;
    }

    // Filter search history for suggestions
    const historySuggestions = searchHistory
      .filter(item => item.toLowerCase().includes(query.toLowerCase()))
      .slice(0, 5);

    setSearchSuggestions(historySuggestions);
  }, [searchHistory]);

  // Handle input change
  const handleInputChange = useCallback((e) => {
    const value = e.target.value;
    setLocalSearchQuery(value);
    generateSuggestions(value);
    setShowSuggestions(true);
  }, [generateSuggestions]);

  // Handle suggestion click
  const handleSuggestionClick = useCallback((suggestion) => {
    setLocalSearchQuery(suggestion);
    setShowSuggestions(false);
    handleSearch(suggestion);
  }, [handleSearch]);

  // Count applied filters
  const appliedFilterCount = useMemo(() => {
    return Object.keys(appliedFilters).length;
  }, [appliedFilters]);

  // Load search history from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('searchHistory');
    if (saved) {
      try {
        setSearchHistory(JSON.parse(saved));
      } catch (e) {
        console.warn('Failed to load search history');
      }
    }
  }, []);

  return (
    <div className={`search-pagination ${className}`}>
      {/* Search Bar */}
      <div className="relative mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <input
            type="text"
            placeholder={searchPlaceholder}
            value={localSearchQuery}
            onChange={handleInputChange}
            onFocus={() => setShowSuggestions(true)}
            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            className="w-full pl-10 pr-20 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          
          {/* Clear search button */}
          {localSearchQuery && (
            <button
              onClick={clearSearch}
              className="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X size={16} />
            </button>
          )}

          {/* Filter toggle button */}
          {showFilters && (
            <button
              onClick={() => setShowFilterPanel(!showFilterPanel)}
              className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded ${
                appliedFilterCount > 0 
                  ? 'text-primary-600 bg-primary-50' 
                  : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <Filter size={16} />
              {appliedFilterCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary-600 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  {appliedFilterCount}
                </span>
              )}
            </button>
          )}
        </div>

        {/* Search Suggestions */}
        {showSuggestions && searchSuggestions.length > 0 && (
          <div className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-10 mt-1">
            <div className="p-2">
              <div className="text-xs text-gray-500 mb-2 flex items-center">
                <Clock size={12} className="mr-1" />
                Recent searches
              </div>
              {searchSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="w-full text-left px-3 py-2 hover:bg-gray-50 rounded text-sm"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Filter Panel */}
      {showFilters && showFilterPanel && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">Filters</h3>
            {appliedFilterCount > 0 && (
              <button
                onClick={clearAllFilters}
                className="text-sm text-primary-600 hover:text-primary-700"
              >
                Clear all
              </button>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {filters.map((filter) => (
              <div key={filter.key}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {filter.label}
                </label>
                {filter.type === 'select' ? (
                  <select
                    value={appliedFilters[filter.key] || ''}
                    onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                    className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="">All {filter.label}</option>
                    {filter.options?.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ) : filter.type === 'range' ? (
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      placeholder="Min"
                      value={appliedFilters[`${filter.key}_min`] || ''}
                      onChange={(e) => handleFilterChange(`${filter.key}_min`, e.target.value)}
                      className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                    <input
                      type="number"
                      placeholder="Max"
                      value={appliedFilters[`${filter.key}_max`] || ''}
                      onChange={(e) => handleFilterChange(`${filter.key}_max`, e.target.value)}
                      className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                ) : (
                  <input
                    type="text"
                    value={appliedFilters[filter.key] || ''}
                    onChange={(e) => handleFilterChange(filter.key, e.target.value)}
                    className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder={filter.placeholder}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Search Metrics */}
      {showMetrics && searchMetrics && Object.keys(searchMetrics).length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="flex items-center space-x-4 text-sm">
            {searchMetrics.results_count !== undefined && (
              <div className="flex items-center text-blue-700">
                <Hash size={14} className="mr-1" />
                {searchMetrics.results_count} results
              </div>
            )}
            {searchMetrics.search_time && (
              <div className="flex items-center text-blue-700">
                <Clock size={14} className="mr-1" />
                {searchMetrics.search_time}ms
              </div>
            )}
            {searchMetrics.has_exact_matches && (
              <div className="flex items-center text-green-700">
                <TrendingUp size={14} className="mr-1" />
                Exact matches found
              </div>
            )}
          </div>
        </div>
      )}

      {/* Applied Filters Display */}
      {appliedFilterCount > 0 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {Object.entries(appliedFilters).map(([key, value]) => (
            <span
              key={key}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800"
            >
              {key}: {value}
              <button
                onClick={() => handleFilterChange(key, '')}
                className="ml-2 text-primary-600 hover:text-primary-800"
              >
                <X size={12} />
              </button>
            </span>
          ))}
        </div>
      )}

      {/* Results Info */}
      <div className="mb-4">
        <PaginationInfo
          currentPage={pagination.currentPage}
          pageSize={pagination.pageSize}
          totalItems={pagination.count}
          variant="detailed"
        />
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-500"></div>
          <span className="ml-3 text-gray-600">Searching...</span>
        </div>
      )}

      {/* Results */}
      <div className="mb-6">
        {results.length > 0 ? (
          <div className="space-y-4">
            {results.map((result, index) => (
              <div key={result.id || index} className="border border-gray-200 rounded-lg p-4">
                {/* Render your result item here */}
                <div className="text-sm text-gray-600">Result {index + 1}</div>
              </div>
            ))}
          </div>
        ) : !loading && (
          <div className="text-center py-8">
            <p className="text-gray-500">
              {searchQuery ? `No results found for "${searchQuery}"` : 'Start typing to search'}
            </p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <Pagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          onPageChange={pagination.onPageChange}
          showPageSizeSelector={true}
          pageSize={pagination.pageSize}
          onPageSizeChange={pagination.onPageSizeChange}
          totalItems={pagination.count}
        />
      )}
    </div>
  );
};

export default SearchPagination;
