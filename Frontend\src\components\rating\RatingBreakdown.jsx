import React from 'react';
import { Star, TrendingUp, TrendingDown, Minus } from 'lucide-react';

const RatingBreakdown = ({ 
  ratingData,
  showTrends = false,
  className = ''
}) => {
  if (!ratingData) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Rating Breakdown</h3>
        <p className="text-gray-500">No rating data available</p>
      </div>
    );
  }

  const {
    total_ratings = 0,
    average_food_rating = 0,
    average_delivery_rating = 0,
    average_overall_rating = 0,
    five_star_count = 0,
    four_star_count = 0,
    three_star_count = 0,
    two_star_count = 0,
    one_star_count = 0
  } = ratingData;

  const ratingCategories = [
    {
      name: 'Food Quality',
      rating: average_food_rating,
      icon: '🍽️',
      color: 'text-orange-600'
    },
    {
      name: 'Delivery Service',
      rating: average_delivery_rating,
      icon: '🚚',
      color: 'text-blue-600'
    },
    {
      name: 'Overall Experience',
      rating: average_overall_rating,
      icon: '⭐',
      color: 'text-yellow-600'
    }
  ];

  const starDistribution = [
    { stars: 5, count: five_star_count },
    { stars: 4, count: four_star_count },
    { stars: 3, count: three_star_count },
    { stars: 2, count: two_star_count },
    { stars: 1, count: one_star_count }
  ];

  const renderStars = (rating, size = 16) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    return (
      <div className="flex items-center">
        {[...Array(fullStars)].map((_, i) => (
          <Star
            key={`full-${i}`}
            size={size}
            className="text-yellow-400 fill-current"
          />
        ))}
        {hasHalfStar && (
          <Star
            key="half"
            size={size}
            className="text-yellow-400 fill-current opacity-50"
          />
        )}
        {[...Array(emptyStars)].map((_, i) => (
          <Star
            key={`empty-${i}`}
            size={size}
            className="text-gray-300"
          />
        ))}
      </div>
    );
  };

  const renderProgressBar = (count, total) => {
    const percentage = total > 0 ? (count / total) * 100 : 0;
    
    return (
      <div className="flex-1 bg-gray-200 rounded-full h-2">
        <div
          className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
          style={{ width: `${percentage}%` }}
        />
      </div>
    );
  };

  const getTrendIcon = (rating) => {
    if (rating >= 4.0) return <TrendingUp className="text-green-500" size={16} />;
    if (rating >= 3.0) return <Minus className="text-yellow-500" size={16} />;
    return <TrendingDown className="text-red-500" size={16} />;
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-6">Rating Breakdown</h3>
      
      {total_ratings === 0 ? (
        <div className="text-center py-8">
          <Star className="mx-auto mb-3 text-gray-300" size={48} />
          <p className="text-gray-500">No ratings yet</p>
          <p className="text-sm text-gray-400 mt-1">
            Ratings will appear here when customers rate their orders
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Category Ratings */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Category Ratings</h4>
            <div className="space-y-4">
              {ratingCategories.map((category) => (
                <div key={category.name} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="text-xl">{category.icon}</span>
                    <span className="font-medium text-gray-700">{category.name}</span>
                    {showTrends && getTrendIcon(category.rating)}
                  </div>
                  <div className="flex items-center space-x-3">
                    {renderStars(category.rating, 14)}
                    <span className={`font-semibold ${category.color} min-w-[40px] text-right`}>
                      {category.rating.toFixed(1)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Star Distribution */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-4">Rating Distribution</h4>
            <div className="space-y-3">
              {starDistribution.map(({ stars, count }) => {
                const percentage = total_ratings > 0 ? (count / total_ratings) * 100 : 0;
                
                return (
                  <div key={stars} className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1 w-12">
                      <span className="text-sm font-medium">{stars}</span>
                      <Star size={12} className="text-yellow-400 fill-current" />
                    </div>
                    
                    {renderProgressBar(count, total_ratings)}
                    
                    <div className="flex items-center space-x-2 min-w-[80px] justify-end">
                      <span className="text-sm text-gray-600">{count}</span>
                      <span className="text-xs text-gray-500">
                        ({percentage.toFixed(1)}%)
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Summary Stats */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-gray-900">{total_ratings}</p>
                <p className="text-sm text-gray-600">Total Reviews</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-yellow-600">
                  {average_overall_rating.toFixed(1)}
                </p>
                <p className="text-sm text-gray-600">Average Rating</p>
              </div>
            </div>
          </div>

          {/* Rating Quality Indicator */}
          <div className="flex items-center justify-center space-x-2 text-sm">
            {average_overall_rating >= 4.5 && (
              <>
                <TrendingUp className="text-green-500" size={16} />
                <span className="text-green-600 font-medium">Excellent Performance</span>
              </>
            )}
            {average_overall_rating >= 4.0 && average_overall_rating < 4.5 && (
              <>
                <TrendingUp className="text-blue-500" size={16} />
                <span className="text-blue-600 font-medium">Very Good Performance</span>
              </>
            )}
            {average_overall_rating >= 3.0 && average_overall_rating < 4.0 && (
              <>
                <Minus className="text-yellow-500" size={16} />
                <span className="text-yellow-600 font-medium">Good Performance</span>
              </>
            )}
            {average_overall_rating < 3.0 && (
              <>
                <TrendingDown className="text-red-500" size={16} />
                <span className="text-red-600 font-medium">Needs Improvement</span>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default RatingBreakdown;
