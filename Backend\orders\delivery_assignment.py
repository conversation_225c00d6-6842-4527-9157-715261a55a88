"""
Automatic Delivery Assignment System

This module handles the automatic assignment of delivery agents to orders
when they are marked as "ready for pickup" by restaurant owners.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from django.db import transaction
from django.contrib.auth import get_user_model
from django.db.models import Q, Count, Avg
from django.utils import timezone
from geopy.distance import geodesic

from .models import Order, AssignmentLog
from restaurant.models import Address
from deliveryAgent.models import DeliveryAgentProfile
from deliveryAgent.zone_manager import zone_manager
from deliveryAgent.geolocation_service import geolocation_service

User = get_user_model()
logger = logging.getLogger(__name__)


class DeliveryAssignmentService:
    """Service for automatically assigning delivery agents to orders"""
    
    # Configuration constants
    MAX_ASSIGNMENT_ATTEMPTS = 3
    ASSIGNMENT_TIMEOUT_MINUTES = 5
    MAX_DELIVERY_DISTANCE_KM = 15
    MAX_CONCURRENT_ORDERS = 3
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def auto_assign_order(self, order: Order) -> Dict[str, Any]:
        """
        Automatically assign an available delivery agent to an order
        
        Args:
            order: The order to assign
            
        Returns:
            Dict containing assignment result and details
        """
        try:
            with transaction.atomic():
                # Validate order is ready for assignment
                if not self._is_order_assignable(order):
                    return {
                        'success': False,
                        'reason': 'Order is not in a state that can be assigned',
                        'order_id': order.id,
                        'status': order.status
                    }
                
                # Find the best available agent
                best_agent = self._find_best_agent(order)
                
                if not best_agent:
                    # Send failure notification
                    self._send_failure_notification(order, 'No available delivery agents found')

                    return {
                        'success': False,
                        'reason': 'No available delivery agents found',
                        'order_id': order.id,
                        'available_agents': self._get_available_agents_count()
                    }
                
                # Assign the agent to the order
                order.assign_agent(best_agent)

                # Send notifications
                self._send_assignment_notifications(order, best_agent)

                self.logger.info(
                    f"Successfully assigned order {order.id} to agent {best_agent.id} ({best_agent.email})"
                )

                return {
                    'success': True,
                    'order_id': order.id,
                    'agent_id': best_agent.id,
                    'agent_email': best_agent.email,
                    'assignment_time': timezone.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"Error auto-assigning order {order.id}: {str(e)}")
            return {
                'success': False,
                'reason': f'Assignment failed: {str(e)}',
                'order_id': order.id
            }
    
    def _is_order_assignable(self, order: Order) -> bool:
        """Check if an order can be assigned to a delivery agent"""
        return (
            order.status == 'ready' and
            order.delivery_agent is None and
            order.assignment_attempts < self.MAX_ASSIGNMENT_ATTEMPTS
        )
    
    def _find_best_agent(self, order: Order) -> Optional[User]:
        """
        Find the best available delivery agent for an order using dynamic zone-based selection
        """
        try:
            # Get restaurant and delivery locations
            restaurant_location = self._get_restaurant_location(order.restaurant)
            delivery_location = self._get_delivery_location(order.delivery_address)

            # Use zone manager to find the best agent
            best_agent_data = zone_manager.find_best_agent_for_order(
                restaurant_location, delivery_location
            )

            if best_agent_data:
                agent_id = best_agent_data['agent']['user_id']
                try:
                    return User.objects.get(id=agent_id, role='delivery_agent')
                except User.DoesNotExist:
                    self.logger.error(f"Agent user {agent_id} not found")
                    return None

            # Fallback to original method if zone-based selection fails
            return self._find_best_agent_fallback(order)

        except Exception as e:
            self.logger.error(f"Error in dynamic agent selection: {str(e)}")
            return self._find_best_agent_fallback(order)

    def _find_best_agent_fallback(self, order: Order) -> Optional[User]:
        """
        Fallback method for finding best agent (original implementation)
        """
        available_agents = self._get_available_agents()

        if not available_agents:
            return None

        # Score each agent
        agent_scores = []
        restaurant_location = self._get_restaurant_location(order.restaurant)

        for agent in available_agents:
            score = self._calculate_agent_score(agent, order, restaurant_location)
            if score > 0:  # Only consider agents with positive scores
                agent_scores.append((agent, score))

        if not agent_scores:
            return None
        
        # Sort by score (highest first) and return the best agent
        agent_scores.sort(key=lambda x: x[1], reverse=True)
        return agent_scores[0][0]
    
    def _get_available_agents(self) -> List[User]:
        """Get all available delivery agents"""
        return User.objects.filter(
            role='delivery_agent',
            is_active=True,
            # Add additional availability filters here
        ).annotate(
            current_orders=Count(
                'delivery_orders',
                filter=Q(
                    delivery_orders__status__in=['assigned', 'picked_up', 'on_the_way']
                )
            )
        ).filter(
            current_orders__lt=self.MAX_CONCURRENT_ORDERS
        )
    
    def _calculate_agent_score(self, agent: User, order: Order, restaurant_location: tuple) -> float:
        """
        Calculate a score for an agent based on various factors
        
        Higher score = better candidate
        """
        score = 100.0  # Base score
        
        # Factor 1: Distance from restaurant (closer is better)
        agent_location = self._get_agent_location(agent)
        if agent_location and restaurant_location:
            distance = self._calculate_distance(agent_location, restaurant_location)
            if distance > self.MAX_DELIVERY_DISTANCE_KM:
                return 0  # Too far, exclude this agent
            
            # Reduce score based on distance (max 30 points deduction)
            distance_penalty = min(30, (distance / self.MAX_DELIVERY_DISTANCE_KM) * 30)
            score -= distance_penalty
        
        # Factor 2: Current workload (fewer orders is better)
        current_orders = getattr(agent, 'current_orders', 0)
        workload_penalty = current_orders * 15  # 15 points per active order
        score -= workload_penalty
        
        # Factor 3: Performance metrics (if available)
        performance_bonus = self._get_performance_bonus(agent)
        score += performance_bonus
        
        # Factor 4: Recent assignment history (avoid overloading)
        recent_assignments = self._get_recent_assignments_count(agent)
        if recent_assignments > 2:  # More than 2 assignments in last hour
            score -= 20
        
        return max(0, score)  # Ensure score is not negative
    
    def _get_restaurant_location(self, restaurant) -> Dict[str, float]:
        """Get restaurant location coordinates"""
        try:
            if hasattr(restaurant, 'address') and restaurant.address:
                address_str = f"{restaurant.address.street}, {restaurant.address.city}, {restaurant.address.state}, {restaurant.address.country}"
                location = geolocation_service.geocode_address(address_str)

                if location:
                    return {'lat': location['lat'], 'lng': location['lng']}

            # Fallback to default Kabul coordinates
            return {'lat': 34.5553, 'lng': 69.2075}

        except Exception as e:
            self.logger.error(f"Error getting restaurant location: {str(e)}")
            return {'lat': 34.5553, 'lng': 69.2075}

    def _get_delivery_location(self, delivery_address) -> Dict[str, float]:
        """Get delivery location coordinates"""
        try:
            if hasattr(delivery_address, 'street'):
                address_str = f"{delivery_address.street}, {delivery_address.city}, {delivery_address.state}, {delivery_address.country}"
                location = geolocation_service.geocode_address(address_str)

                if location:
                    return {'lat': location['lat'], 'lng': location['lng']}

            # Fallback to default Kabul coordinates
            return {'lat': 34.5553, 'lng': 69.2075}

        except Exception as e:
            self.logger.error(f"Error getting delivery location: {str(e)}")
            return {'lat': 34.5553, 'lng': 69.2075}
    
    def _get_agent_location(self, agent: User) -> Optional[tuple]:
        """Get agent's current location coordinates"""
        try:
            # This would typically come from a real-time location tracking system
            # For now, we'll use a default location or the agent's home address
            # You can implement real-time location tracking later
            
            # Try to get agent's address
            agent_address = Address.objects.filter(user=agent).first()
            if agent_address:
                return (float(agent_address.latitude), float(agent_address.longitude))
            
            # Default location (Kabul center) if no address found
            return (34.5553, 69.2075)
        except (AttributeError, ValueError, TypeError):
            return None
    
    def _calculate_distance(self, location1: tuple, location2: tuple) -> float:
        """Calculate distance between two coordinates in kilometers"""
        try:
            return geodesic(location1, location2).kilometers
        except Exception:
            return float('inf')  # Return infinite distance on error
    
    def _get_performance_bonus(self, agent: User) -> float:
        """Calculate performance bonus based on agent's history"""
        try:
            # Get agent's recent performance metrics
            recent_orders = Order.objects.filter(
                delivery_agent=agent,
                status='delivered',
                created_at__gte=timezone.now() - timedelta(days=30)
            )
            
            if not recent_orders.exists():
                return 0
            
            # Calculate average delivery time performance
            # This is a simplified version - you can make it more sophisticated
            total_orders = recent_orders.count()
            
            # Bonus for high volume (up to 10 points)
            volume_bonus = min(10, total_orders / 10)
            
            return volume_bonus
        except Exception:
            return 0
    
    def _get_recent_assignments_count(self, agent: User) -> int:
        """Get number of recent assignments for an agent"""
        try:
            one_hour_ago = timezone.now() - timedelta(hours=1)
            return AssignmentLog.objects.filter(
                agent=agent,
                action='assigned',
                created_at__gte=one_hour_ago
            ).count()
        except Exception:
            return 0
    
    def _get_available_agents_count(self) -> int:
        """Get count of available agents for logging purposes"""
        return self._get_available_agents().count()
    
    def get_assignment_statistics(self) -> Dict[str, Any]:
        """Get statistics about delivery assignments"""
        try:
            total_orders_ready = Order.objects.filter(status='ready').count()
            total_orders_assigned = Order.objects.filter(status='assigned').count()
            available_agents = self._get_available_agents_count()

            return {
                'orders_ready_for_assignment': total_orders_ready,
                'orders_currently_assigned': total_orders_assigned,
                'available_agents': available_agents,
                'timestamp': timezone.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error getting assignment statistics: {str(e)}")
            return {}

    def _send_assignment_notifications(self, order: Order, agent: User) -> None:
        """Send notifications about successful assignment"""
        try:
            from .notifications import notification_service

            # Notify the delivery agent
            notification_service.notify_agent_assignment(order, agent)

            # Notify the customer
            notification_service.notify_customer_assignment(order, agent)

        except Exception as e:
            self.logger.error(f"Error sending assignment notifications for order {order.id}: {str(e)}")

    def _send_failure_notification(self, order: Order, reason: str) -> None:
        """Send notifications about assignment failure"""
        try:
            from .notifications import notification_service
            notification_service.notify_assignment_failure(order, reason)
        except Exception as e:
            self.logger.error(f"Error sending failure notification for order {order.id}: {str(e)}")


# Global instance for easy access
delivery_assignment_service = DeliveryAssignmentService()
