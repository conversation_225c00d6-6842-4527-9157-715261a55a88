import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate, Link } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import {
  Store,
  MapPin,
  Phone,
  Clock,
  Upload,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Info,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Input from "../../components/common/Input";
import FormControl from "../../components/common/FormControl";

const AddRestaurant = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm();

  const { user } = useAuth();
  const { createRestaurant, loading } = useRestaurant();
  const navigate = useNavigate();

  const [currentStep, setCurrentStep] = useState(1);
  const [error, setError] = useState(null);
  const [logoFile, setLogoFile] = useState(null);
  const [bannerFile, setBannerFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState("");
  const [bannerPreview, setBannerPreview] = useState("");

  const totalSteps = 3;

  const steps = [
    { number: 1, title: "Basic Information", icon: Store },
    { number: 2, title: "Location & Hours", icon: MapPin },
    { number: 3, title: "Images & Review", icon: Upload },
  ];

  const handleFileChange = (event, type) => {
    const file = event.target.files[0];
    if (file) {
      if (type === "logo") {
        setLogoFile(file);
        setLogoPreview(URL.createObjectURL(file));
      } else if (type === "banner") {
        setBannerFile(file);
        setBannerPreview(URL.createObjectURL(file));
      }
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const onSubmit = async (data) => {
    try {
      setError(null);

      // Prepare restaurant data
      const restaurantData = {
        name: data.name,
        description: data.description,
        contact_number: data.contact_number,
        opening_time: data.opening_time,
        closing_time: data.closing_time,
        delivery_fee: data.delivery_fee || 0,
        address: {
          street: data.street,
          city: data.city,
          state: data.state,
          postal_code: data.postal_code,
          country: data.country || "Afghanistan",
          coordinates: {
            lat: 0, // You can add map integration later
            lng: 0,
          },
        },
        logo: logoFile,
        banner: bannerFile,
      };

      const result = await createRestaurant(restaurantData);

      if (result.success) {
        navigate("/restaurant/success", {
          state: {
            restaurant: result.data,
            message:
              "Restaurant added successfully! It will be reviewed by our admin team.",
          },
        });
      } else {
        setError(result.error || "Failed to create restaurant");
      }
    } catch (err) {
      console.error("Restaurant creation error:", err);
      setError("An unexpected error occurred");
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className='space-y-6'>
            <FormControl
              label='Restaurant Name'
              htmlFor='name'
              error={errors.name?.message}
              required
            >
              <Input
                id='name'
                placeholder='Enter restaurant name'
                icon={<Store size={18} />}
                error={errors.name?.message}
                {...register("name", {
                  required: "Restaurant name is required",
                  minLength: {
                    value: 2,
                    message: "Name must be at least 2 characters",
                  },
                })}
              />
            </FormControl>

            <FormControl
              label='Description'
              htmlFor='description'
              error={errors.description?.message}
              required
            >
              <textarea
                id='description'
                rows={4}
                placeholder='Describe your restaurant, cuisine type, and specialties'
                className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none'
                {...register("description", {
                  required: "Description is required",
                  minLength: {
                    value: 10,
                    message: "Description must be at least 10 characters",
                  },
                })}
              />
            </FormControl>

            <FormControl
              label='Contact Number'
              htmlFor='contact_number'
              error={errors.contact_number?.message}
              required
            >
              <Input
                id='contact_number'
                type='tel'
                placeholder='Enter contact number'
                icon={<Phone size={18} />}
                error={errors.contact_number?.message}
                {...register("contact_number", {
                  required: "Contact number is required",
                  pattern: {
                    value: /^[+]?[\d\s\-\(\)]+$/,
                    message: "Please enter a valid phone number",
                  },
                })}
              />
            </FormControl>
          </div>
        );

      case 2:
        return (
          <div className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <FormControl
                label='Street Address'
                htmlFor='street'
                error={errors.street?.message}
                required
              >
                <Input
                  id='street'
                  placeholder='Street address'
                  icon={<MapPin size={18} />}
                  error={errors.street?.message}
                  {...register("street", {
                    required: "Street address is required",
                  })}
                />
              </FormControl>

              <FormControl
                label='City'
                htmlFor='city'
                error={errors.city?.message}
                required
              >
                <Input
                  id='city'
                  placeholder='City'
                  error={errors.city?.message}
                  {...register("city", {
                    required: "City is required",
                  })}
                />
              </FormControl>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <FormControl
                label='State'
                htmlFor='state'
                error={errors.state?.message}
                required
              >
                <Input
                  id='state'
                  placeholder='State'
                  error={errors.state?.message}
                  {...register("state", {
                    required: "State is required",
                  })}
                />
              </FormControl>

              <FormControl
                label='Postal Code'
                htmlFor='postal_code'
                error={errors.postal_code?.message}
              >
                <Input
                  id='postal_code'
                  placeholder='Postal code'
                  error={errors.postal_code?.message}
                  {...register("postal_code")}
                />
              </FormControl>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <FormControl
                label='Opening Time'
                htmlFor='opening_time'
                error={errors.opening_time?.message}
                required
              >
                <Input
                  id='opening_time'
                  type='time'
                  icon={<Clock size={18} />}
                  error={errors.opening_time?.message}
                  {...register("opening_time", {
                    required: "Opening time is required",
                  })}
                />
              </FormControl>

              <FormControl
                label='Closing Time'
                htmlFor='closing_time'
                error={errors.closing_time?.message}
                required
              >
                <Input
                  id='closing_time'
                  type='time'
                  icon={<Clock size={18} />}
                  error={errors.closing_time?.message}
                  {...register("closing_time", {
                    required: "Closing time is required",
                  })}
                />
              </FormControl>

              <FormControl
                label='Delivery Fee ($)'
                htmlFor='delivery_fee'
                error={errors.delivery_fee?.message}
              >
                <Input
                  id='delivery_fee'
                  type='number'
                  step='0.01'
                  placeholder='0.00'
                  error={errors.delivery_fee?.message}
                  {...register("delivery_fee", {
                    min: {
                      value: 0,
                      message: "Delivery fee cannot be negative",
                    },
                  })}
                />
              </FormControl>
            </div>
          </div>
        );

      case 3:
        return (
          <div className='space-y-6'>
            {/* Logo Upload */}
            <FormControl label='Restaurant Logo' htmlFor='logo'>
              <div className='flex items-center space-x-4'>
                <div className='w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center overflow-hidden'>
                  {logoPreview ? (
                    <img
                      src={logoPreview}
                      alt='Logo preview'
                      className='w-full h-full object-cover'
                    />
                  ) : (
                    <Upload className='text-gray-400' size={24} />
                  )}
                </div>
                <div className='flex-1'>
                  <input
                    id='logo'
                    type='file'
                    accept='image/*'
                    onChange={(e) => handleFileChange(e, "logo")}
                    className='hidden'
                  />
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() => document.getElementById("logo").click()}
                  >
                    Choose Logo
                  </Button>
                  <p className='text-sm text-gray-500 mt-1'>
                    Recommended: Square image, max 2MB
                  </p>
                </div>
              </div>
            </FormControl>

            {/* Banner Upload */}
            <FormControl label='Restaurant Banner' htmlFor='banner'>
              <div className='space-y-4'>
                <div className='w-full h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center overflow-hidden'>
                  {bannerPreview ? (
                    <img
                      src={bannerPreview}
                      alt='Banner preview'
                      className='w-full h-full object-cover'
                    />
                  ) : (
                    <div className='text-center'>
                      <Upload
                        className='text-gray-400 mx-auto mb-2'
                        size={32}
                      />
                      <p className='text-gray-500'>Banner preview</p>
                    </div>
                  )}
                </div>
                <div>
                  <input
                    id='banner'
                    type='file'
                    accept='image/*'
                    onChange={(e) => handleFileChange(e, "banner")}
                    className='hidden'
                  />
                  <Button
                    type='button'
                    variant='outline'
                    onClick={() => document.getElementById("banner").click()}
                  >
                    Choose Banner
                  </Button>
                  <p className='text-sm text-gray-500 mt-1'>
                    Recommended: 16:9 aspect ratio, max 5MB
                  </p>
                </div>
              </div>
            </FormControl>

            {/* Review Information */}
            <div className='bg-blue-50 p-4 rounded-lg'>
              <div className='flex items-start space-x-3'>
                <Info className='text-blue-600 mt-0.5' size={20} />
                <div>
                  <h3 className='font-medium text-blue-800 mb-1'>
                    Ready to Submit
                  </h3>
                  <p className='text-blue-700 text-sm'>
                    Your restaurant will be submitted for admin review. You'll
                    be able to manage menus and receive orders once approved.
                  </p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className='container mx-auto px-4 py-8 max-w-4xl'>
      {/* Header */}
      <div className='mb-8'>
        <Link
          to='/restaurant'
          className='inline-flex items-center text-primary-600 hover:text-primary-700 mb-4'
        >
          <ArrowLeft size={20} className='mr-2' />
          Back to Dashboard
        </Link>
        <h1 className='text-3xl font-bold text-gray-900 mb-2'>
          Add New Restaurant
        </h1>
        <p className='text-gray-600'>
          Create a new restaurant profile to start managing menus and orders.
        </p>
      </div>

      {/* Progress Steps */}
      <div className='mb-8'>
        <div className='flex items-center justify-between'>
          {steps.map((step, index) => (
            <div key={step.number} className='flex items-center'>
              <div
                className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.number
                    ? "bg-primary-500 border-primary-500 text-white"
                    : "border-gray-300 text-gray-500"
                }`}
              >
                {currentStep > step.number ? (
                  <CheckCircle size={20} />
                ) : (
                  <step.icon size={20} />
                )}
              </div>
              <div className='ml-3 hidden sm:block'>
                <p
                  className={`text-sm font-medium ${
                    currentStep >= step.number
                      ? "text-primary-600"
                      : "text-gray-500"
                  }`}
                >
                  {step.title}
                </p>
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`hidden sm:block w-16 h-0.5 ml-4 ${
                    currentStep > step.number ? "bg-primary-500" : "bg-gray-300"
                  }`}
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <Card className='p-6 mb-6'>
          <h2 className='text-xl font-semibold mb-6'>
            Step {currentStep}: {steps[currentStep - 1].title}
          </h2>

          {/* Error Display */}
          {error && (
            <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-lg'>
              <div className='flex items-center'>
                <AlertCircle className='text-red-500 mr-2' size={20} />
                <p className='text-red-700'>{error}</p>
              </div>
            </div>
          )}

          {renderStepContent()}
        </Card>

        {/* Navigation Buttons */}
        <div className='flex justify-between'>
          <Button
            type='button'
            variant='outline'
            onClick={prevStep}
            disabled={currentStep === 1}
            icon={<ArrowLeft size={18} />}
          >
            Previous
          </Button>

          {currentStep < totalSteps ? (
            <Button
              type='button'
              variant='primary'
              onClick={nextStep}
              icon={<ArrowRight size={18} />}
              iconPosition='right'
            >
              Next
            </Button>
          ) : (
            <Button
              type='submit'
              variant='primary'
              disabled={loading}
              icon={<CheckCircle size={18} />}
            >
              {loading ? "Creating..." : "Create Restaurant"}
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};

export default AddRestaurant;
