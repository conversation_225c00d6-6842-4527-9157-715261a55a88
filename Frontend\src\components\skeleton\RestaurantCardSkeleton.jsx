import React from 'react';
import { SkeletonCard, SkeletonRectangle, SkeletonText, SkeletonCircle } from './SkeletonBase';

const RestaurantCardSkeleton = ({ className }) => {
  return (
    <SkeletonCard className={className}>
      {/* Restaurant Image */}
      <SkeletonRectangle height="h-48" className="mb-4" rounded="lg" />
      
      {/* Restaurant Info */}
      <div className="space-y-3">
        {/* Restaurant Name */}
        <SkeletonText lineHeight="h-6" className="w-3/4" />
        
        {/* Rating and Cuisine */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <SkeletonCircle size="w-4 h-4" />
            <SkeletonText lineHeight="h-4" className="w-16" />
          </div>
          <SkeletonText lineHeight="h-4" className="w-20" />
        </div>
        
        {/* Delivery Info */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <SkeletonCircle size="w-4 h-4" />
              <SkeletonText lineHeight="h-4" className="w-16" />
            </div>
            <div className="flex items-center space-x-1">
              <SkeletonCircle size="w-4 h-4" />
              <SkeletonText lineHeight="h-4" className="w-12" />
            </div>
          </div>
          <SkeletonText lineHeight="h-4" className="w-16" />
        </div>
        
        {/* Cuisine Tags */}
        <div className="flex flex-wrap gap-2">
          <SkeletonRectangle width="w-16" height="h-6" rounded="full" />
          <SkeletonRectangle width="w-20" height="h-6" rounded="full" />
          <SkeletonRectangle width="w-14" height="h-6" rounded="full" />
        </div>
      </div>
    </SkeletonCard>
  );
};

// Grid of Restaurant Card Skeletons
export const RestaurantGridSkeleton = ({ count = 6, className }) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <RestaurantCardSkeleton key={index} />
      ))}
    </div>
  );
};

// List of Restaurant Card Skeletons
export const RestaurantListSkeleton = ({ count = 4, className }) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonCard key={index} className="flex">
          {/* Image */}
          <SkeletonRectangle width="w-32" height="h-24" className="flex-shrink-0 mr-4" />
          
          {/* Content */}
          <div className="flex-1 space-y-2">
            <SkeletonText lineHeight="h-5" className="w-3/4" />
            <div className="flex items-center space-x-2">
              <SkeletonCircle size="w-4 h-4" />
              <SkeletonText lineHeight="h-4" className="w-16" />
            </div>
            <div className="flex items-center justify-between">
              <SkeletonText lineHeight="h-4" className="w-24" />
              <SkeletonText lineHeight="h-4" className="w-16" />
            </div>
          </div>
        </SkeletonCard>
      ))}
    </div>
  );
};

export default RestaurantCardSkeleton;
