import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  MapPin,
  Phone,
  Mail,
  Clock,
  Package,
  DollarSign,
  Star,
  Truck,
  Activity,
  Eye,
  Edit,
  UserPlus,
  Download,
  TrendingUp,
  Navigation,
  CheckCircle,
  XCircle,
  Users,
  X,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { useAuth } from "../../context/AuthContext";

function DeliveryAgentManagement() {
  const { user: currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [agents, setAgents] = useState([]);
  const [filteredAgents, setFilteredAgents] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showAddAgent, setShowAddAgent] = useState(false);
  const [showMoreFilters, setShowMoreFilters] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [newAgent, setNewAgent] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    vehicleType: "motorcycle",
    licensePlate: "",
    vehicleColor: "",
    deliveryZones: [],
  });

  const [agentStats, setAgentStats] = useState({
    total: 0,
    active: 0,
    offline: 0,
    busy: 0,
    totalDeliveries: 0,
    totalEarnings: 0,
    avgRating: 0,
  });

  useEffect(() => {
    // Simulate API call to fetch delivery agents
    setTimeout(() => {
      const mockAgents = [
        {
          id: 1,
          name: "Mohammad Ali",
          email: "<EMAIL>",
          phone: "+93 70 234 5678",
          status: "active",
          location: {
            lat: 34.5553,
            lng: 69.2075,
            address: "Kabul, District 1",
          },
          vehicle: {
            type: "motorcycle",
            licensePlate: "KBL 1234",
            color: "Black",
          },
          rating: 4.8,
          totalDeliveries: 156,
          totalEarnings: 2340.5,
          joinedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 45),
          lastActive: new Date(Date.now() - 1000 * 60 * 15),
          deliveryZones: ["Central Kabul", "North Kabul"],
          verified: true,
          avatar:
            "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=600",
        },
        {
          id: 2,
          name: "Ahmad Khan",
          email: "<EMAIL>",
          phone: "+93 70 345 6789",
          status: "busy",
          location: {
            lat: 34.558,
            lng: 69.209,
            address: "Kabul, District 3",
          },
          vehicle: {
            type: "bicycle",
            licensePlate: "N/A",
            color: "Blue",
          },
          rating: 4.6,
          totalDeliveries: 89,
          totalEarnings: 1567.25,
          joinedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30),
          lastActive: new Date(Date.now() - 1000 * 60 * 5),
          deliveryZones: ["South Kabul", "East Kabul"],
          verified: true,
          avatar:
            "https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=600",
        },
        {
          id: 3,
          name: "Fatima Zahra",
          email: "<EMAIL>",
          phone: "+93 70 456 7890",
          status: "offline",
          location: {
            lat: 34.56,
            lng: 69.195,
            address: "Kabul, District 2",
          },
          vehicle: {
            type: "motorcycle",
            licensePlate: "KBL 5678",
            color: "Red",
          },
          rating: 4.9,
          totalDeliveries: 203,
          totalEarnings: 3245.75,
          joinedAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60),
          lastActive: new Date(Date.now() - 1000 * 60 * 60 * 8),
          deliveryZones: ["West Kabul", "Central Kabul"],
          verified: false,
          avatar:
            "https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=600",
        },
      ];

      setAgents(mockAgents);
      setFilteredAgents(mockAgents);

      // Calculate stats
      const stats = {
        total: mockAgents.length,
        active: mockAgents.filter((a) => a.status === "active").length,
        offline: mockAgents.filter((a) => a.status === "offline").length,
        busy: mockAgents.filter((a) => a.status === "busy").length,
        totalDeliveries: mockAgents.reduce(
          (sum, a) => sum + a.totalDeliveries,
          0
        ),
        totalEarnings: mockAgents.reduce((sum, a) => sum + a.totalEarnings, 0),
        avgRating:
          mockAgents.reduce((sum, a) => sum + a.rating, 0) / mockAgents.length,
      };
      setAgentStats(stats);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    let filtered = [...agents];

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((a) => a.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (a) =>
          a.name.toLowerCase().includes(query) ||
          a.email.toLowerCase().includes(query) ||
          a.phone.includes(query) ||
          a.location.address.toLowerCase().includes(query)
      );
    }

    setFilteredAgents(filtered);
  }, [agents, statusFilter, searchQuery]);

  const getStatusBadge = (status) => {
    switch (status) {
      case "active":
        return <Badge className='bg-green-100 text-green-800'>Active</Badge>;
      case "busy":
        return <Badge className='bg-yellow-100 text-yellow-800'>Busy</Badge>;
      case "offline":
        return <Badge className='bg-red-100 text-red-800'>Offline</Badge>;
      default:
        return null;
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString();
  };

  const handleAddAgent = async () => {
    try {
      const agentToAdd = {
        id: Date.now(),
        ...newAgent,
        status: "offline",
        rating: 0,
        totalDeliveries: 0,
        totalEarnings: 0,
        joinedAt: new Date(),
        lastActive: new Date(),
        verified: false,
        avatar:
          "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=600",
        location: {
          lat: 34.5553,
          lng: 69.2075,
          address: newAgent.address,
        },
        vehicle: {
          type: newAgent.vehicleType,
          licensePlate: newAgent.licensePlate,
          color: newAgent.vehicleColor,
        },
      };

      setAgents([...agents, agentToAdd]);
      setShowAddAgent(false);
      setNewAgent({
        name: "",
        email: "",
        phone: "",
        address: "",
        vehicleType: "motorcycle",
        licensePlate: "",
        vehicleColor: "",
        deliveryZones: [],
      });
    } catch (error) {
      console.error("Error adding agent:", error);
    }
  };

  const handleTrackLocation = (agent) => {
    // Simulate opening a map or tracking interface
    alert(
      `Tracking ${agent.name} at location: ${agent.location.address}\nLat: ${agent.location.lat}, Lng: ${agent.location.lng}`
    );
  };

  const handleEditAgent = () => {
    setIsEditMode(true);
  };

  const handleSaveAgent = () => {
    // Update the agent in the agents array
    const updatedAgents = agents.map((agent) =>
      agent.id === selectedAgent.id ? selectedAgent : agent
    );
    setAgents(updatedAgents);
    setIsEditMode(false);
    alert("Agent details updated successfully!");
  };

  const handleCancelEdit = () => {
    // Reset selectedAgent to original data
    const originalAgent = agents.find((agent) => agent.id === selectedAgent.id);
    setSelectedAgent(originalAgent);
    setIsEditMode(false);
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500'></div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>
            Delivery Agent Management
          </h1>
          <p className='mt-2 text-sm text-gray-600'>
            Manage and monitor delivery agents across your platform
          </p>
        </div>
        <div className='mt-4 sm:mt-0 flex items-center space-x-3'>
          <Button variant='outline' icon={<Download size={18} />}>
            Export Data
          </Button>
          <Button
            variant='primary'
            icon={<UserPlus size={18} />}
            onClick={() => setShowAddAgent(true)}
          >
            Add Agent
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
        <Card className='p-6 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200'>
          <div className='flex items-center justify-between'>
            <div>
              <div className='text-sm font-medium text-blue-700'>
                Total Agents
              </div>
              <div className='text-3xl font-bold text-blue-900 mt-1'>
                {agentStats.total}
              </div>
            </div>
            <div className='bg-blue-200 p-3 rounded-full'>
              <Users size={24} className='text-blue-700' />
            </div>
          </div>
          <div className='mt-4 flex items-center text-sm'>
            <TrendingUp size={14} className='text-green-600 mr-1' />
            <span className='text-green-600 font-medium'>+12%</span>
            <span className='text-blue-600 ml-1'>vs last month</span>
          </div>
        </Card>

        <Card className='p-6 bg-gradient-to-br from-green-50 to-green-100 border-green-200'>
          <div className='flex items-center justify-between'>
            <div>
              <div className='text-sm font-medium text-green-700'>
                Active Now
              </div>
              <div className='text-3xl font-bold text-green-900 mt-1'>
                {agentStats.active}
              </div>
            </div>
            <div className='bg-green-200 p-3 rounded-full'>
              <Activity size={24} className='text-green-700' />
            </div>
          </div>
          <div className='mt-4 text-sm text-green-600'>
            {agentStats.busy} busy, {agentStats.offline} offline
          </div>
        </Card>

        <Card className='p-6 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200'>
          <div className='flex items-center justify-between'>
            <div>
              <div className='text-sm font-medium text-purple-700'>
                Total Deliveries
              </div>
              <div className='text-3xl font-bold text-purple-900 mt-1'>
                {agentStats.totalDeliveries}
              </div>
            </div>
            <div className='bg-purple-200 p-3 rounded-full'>
              <Package size={24} className='text-purple-700' />
            </div>
          </div>
          <div className='mt-4 text-sm text-purple-600'>
            Avg: {Math.round(agentStats.totalDeliveries / agentStats.total)} per
            agent
          </div>
        </Card>

        <Card className='p-6 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200'>
          <div className='flex items-center justify-between'>
            <div>
              <div className='text-sm font-medium text-orange-700'>
                Total Earnings
              </div>
              <div className='text-3xl font-bold text-orange-900 mt-1'>
                {formatCurrency(agentStats.totalEarnings)}
              </div>
            </div>
            <div className='bg-orange-200 p-3 rounded-full'>
              <DollarSign size={24} className='text-orange-700' />
            </div>
          </div>
          <div className='mt-4 text-sm text-orange-600'>
            Avg Rating: {agentStats.avgRating.toFixed(1)} ⭐
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className='p-6'>
        <div className='flex flex-col md:flex-row md:items-center md:justify-between gap-4'>
          <div className='relative flex-1 max-w-md'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={20}
            />
            <input
              type='text'
              placeholder='Search agents by name, email, phone, or location...'
              className='w-full pl-10 pr-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className='flex items-center gap-4'>
            <select
              className='px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-white'
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value='all'>All Status</option>
              <option value='active'>Active</option>
              <option value='busy'>Busy</option>
              <option value='offline'>Offline</option>
            </select>
            <Button
              variant='outline'
              icon={<Filter size={18} />}
              onClick={() => setShowMoreFilters(!showMoreFilters)}
            >
              More Filters
            </Button>
          </div>
        </div>

        {/* More Filters Panel */}
        {showMoreFilters && (
          <div className='mt-4 p-4 bg-gray-50 rounded-lg border'>
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Vehicle Type
                </label>
                <select className='w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'>
                  <option value='all'>All Vehicles</option>
                  <option value='motorcycle'>Motorcycle</option>
                  <option value='bicycle'>Bicycle</option>
                  <option value='car'>Car</option>
                  <option value='scooter'>Scooter</option>
                </select>
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Verification Status
                </label>
                <select className='w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'>
                  <option value='all'>All Agents</option>
                  <option value='verified'>Verified Only</option>
                  <option value='unverified'>Unverified Only</option>
                </select>
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Rating Range
                </label>
                <select className='w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'>
                  <option value='all'>All Ratings</option>
                  <option value='4+'>4+ Stars</option>
                  <option value='3+'>3+ Stars</option>
                  <option value='2+'>2+ Stars</option>
                </select>
              </div>
            </div>
            <div className='mt-4 flex justify-end space-x-2'>
              <Button
                variant='outline'
                size='small'
                onClick={() => setShowMoreFilters(false)}
              >
                Clear Filters
              </Button>
              <Button variant='primary' size='small'>
                Apply Filters
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* Agent List */}
      <div className='grid gap-6'>
        {filteredAgents.map((agent) => (
          <Card
            key={agent.id}
            className='hover:shadow-md transition-shadow duration-200'
          >
            <div className='p-6'>
              <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4'>
                <div className='flex items-start space-x-4'>
                  <div className='relative'>
                    <img
                      src={agent.avatar}
                      alt={agent.name}
                      className='w-16 h-16 rounded-full object-cover'
                    />
                    <div
                      className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white ${
                        agent.status === "active"
                          ? "bg-green-500"
                          : agent.status === "busy"
                          ? "bg-yellow-500"
                          : "bg-red-500"
                      }`}
                    ></div>
                  </div>
                  <div className='flex-1'>
                    <div className='flex items-center gap-3 mb-2'>
                      <h3 className='text-lg font-semibold text-gray-900'>
                        {agent.name}
                      </h3>
                      {getStatusBadge(agent.status)}
                      {agent.verified && (
                        <Badge className='bg-blue-100 text-blue-800'>
                          <CheckCircle size={12} className='mr-1' />
                          Verified
                        </Badge>
                      )}
                    </div>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600'>
                      <div className='flex items-center'>
                        <Mail size={16} className='mr-2 text-gray-400' />
                        <span>{agent.email}</span>
                      </div>
                      <div className='flex items-center'>
                        <Phone size={16} className='mr-2 text-gray-400' />
                        <span>{agent.phone}</span>
                      </div>
                      <div className='flex items-center'>
                        <MapPin size={16} className='mr-2 text-gray-400' />
                        <span>{agent.location.address}</span>
                      </div>
                      <div className='flex items-center'>
                        <Truck size={16} className='mr-2 text-gray-400' />
                        <span>
                          {agent.vehicle.type} - {agent.vehicle.licensePlate}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className='flex flex-col lg:flex-row items-start lg:items-center gap-4'>
                  <div className='grid grid-cols-3 gap-4 text-center'>
                    <div>
                      <div className='text-lg font-semibold text-gray-900'>
                        {agent.totalDeliveries}
                      </div>
                      <div className='text-xs text-gray-500'>Deliveries</div>
                    </div>
                    <div>
                      <div className='text-lg font-semibold text-gray-900'>
                        {formatCurrency(agent.totalEarnings)}
                      </div>
                      <div className='text-xs text-gray-500'>Earnings</div>
                    </div>
                    <div>
                      <div className='flex items-center justify-center'>
                        <Star size={16} className='text-yellow-400 mr-1' />
                        <span className='text-lg font-semibold text-gray-900'>
                          {agent.rating}
                        </span>
                      </div>
                      <div className='text-xs text-gray-500'>Rating</div>
                    </div>
                  </div>

                  <div className='flex items-center gap-2'>
                    <Button
                      variant='outline'
                      size='small'
                      icon={<Eye size={16} />}
                      onClick={() => {
                        setSelectedAgent(agent);
                        setShowDetails(true);
                      }}
                    >
                      View Details
                    </Button>
                    <Button
                      variant='outline'
                      size='small'
                      icon={<Navigation size={16} />}
                      onClick={() => handleTrackLocation(agent)}
                    >
                      Track Location
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {filteredAgents.length === 0 && (
        <Card className='p-12 text-center'>
          <Truck size={48} className='mx-auto text-gray-400 mb-4' />
          <h3 className='text-lg font-medium text-gray-900 mb-2'>
            No delivery agents found
          </h3>
          <p className='text-gray-500 mb-4'>
            Try adjusting your search criteria or add a new delivery agent.
          </p>
          <Button
            variant='primary'
            icon={<UserPlus size={18} />}
            onClick={() => setShowAddAgent(true)}
          >
            Add First Agent
          </Button>
        </Card>
      )}

      {/* Add Agent Modal */}
      {showAddAgent && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg w-full max-w-2xl'>
            <div className='p-6 border-b'>
              <div className='flex justify-between items-center'>
                <h2 className='text-xl font-semibold'>
                  Add New Delivery Agent
                </h2>
                <button
                  className='text-gray-400 hover:text-gray-600'
                  onClick={() => setShowAddAgent(false)}
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className='p-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-4'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Full Name
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newAgent.name}
                      onChange={(e) =>
                        setNewAgent({ ...newAgent, name: e.target.value })
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Email
                    </label>
                    <input
                      type='email'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newAgent.email}
                      onChange={(e) =>
                        setNewAgent({ ...newAgent, email: e.target.value })
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Phone
                    </label>
                    <input
                      type='tel'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newAgent.phone}
                      onChange={(e) =>
                        setNewAgent({ ...newAgent, phone: e.target.value })
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Address
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newAgent.address}
                      onChange={(e) =>
                        setNewAgent({ ...newAgent, address: e.target.value })
                      }
                    />
                  </div>
                </div>

                <div className='space-y-4'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Vehicle Type
                    </label>
                    <select
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newAgent.vehicleType}
                      onChange={(e) =>
                        setNewAgent({
                          ...newAgent,
                          vehicleType: e.target.value,
                        })
                      }
                    >
                      <option value='motorcycle'>Motorcycle</option>
                      <option value='bicycle'>Bicycle</option>
                      <option value='car'>Car</option>
                      <option value='scooter'>Scooter</option>
                    </select>
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      License Plate
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newAgent.licensePlate}
                      onChange={(e) =>
                        setNewAgent({
                          ...newAgent,
                          licensePlate: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Vehicle Color
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newAgent.vehicleColor}
                      onChange={(e) =>
                        setNewAgent({
                          ...newAgent,
                          vehicleColor: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Delivery Zones
                    </label>
                    <input
                      type='text'
                      placeholder='e.g., Central Kabul, North Kabul'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newAgent.deliveryZones.join(", ")}
                      onChange={(e) =>
                        setNewAgent({
                          ...newAgent,
                          deliveryZones: e.target.value
                            .split(",")
                            .map((zone) => zone.trim()),
                        })
                      }
                    />
                  </div>
                </div>
              </div>

              <div className='mt-6 flex justify-end space-x-3'>
                <Button
                  variant='outline'
                  onClick={() => setShowAddAgent(false)}
                >
                  Cancel
                </Button>
                <Button variant='primary' onClick={handleAddAgent}>
                  Add Agent
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Agent Details Modal */}
      {showDetails && selectedAgent && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg w-full max-w-3xl max-h-[90vh] overflow-auto'>
            <div className='p-6 border-b'>
              <div className='flex justify-between items-start'>
                <div className='flex items-center space-x-4'>
                  <div className='relative'>
                    <img
                      src={selectedAgent.avatar}
                      alt={selectedAgent.name}
                      className='w-16 h-16 rounded-full object-cover'
                    />
                    <div
                      className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white ${
                        selectedAgent.status === "active"
                          ? "bg-green-500"
                          : selectedAgent.status === "busy"
                          ? "bg-yellow-500"
                          : "bg-gray-400"
                      }`}
                    ></div>
                  </div>
                  <div>
                    <h2 className='text-xl font-semibold'>
                      {selectedAgent.name}
                    </h2>
                    <div className='flex items-center space-x-2 mt-1'>
                      <Badge
                        className={
                          selectedAgent.status === "active"
                            ? "bg-green-100 text-green-800"
                            : selectedAgent.status === "busy"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-gray-100 text-gray-800"
                        }
                      >
                        {selectedAgent.status}
                      </Badge>
                      {selectedAgent.verified && (
                        <Badge className='bg-blue-100 text-blue-800'>
                          Verified
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <button
                  className='text-gray-400 hover:text-gray-600'
                  onClick={() => setShowDetails(false)}
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className='p-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <h3 className='text-lg font-semibold mb-4'>
                    Personal Information
                  </h3>
                  <div className='space-y-3'>
                    <div className='flex items-center'>
                      <Mail size={16} className='mr-3 text-gray-400' />
                      {isEditMode ? (
                        <input
                          type='email'
                          className='flex-1 px-3 py-1 border rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                          value={selectedAgent.email}
                          onChange={(e) =>
                            setSelectedAgent({
                              ...selectedAgent,
                              email: e.target.value,
                            })
                          }
                        />
                      ) : (
                        <span>{selectedAgent.email}</span>
                      )}
                    </div>
                    <div className='flex items-center'>
                      <Phone size={16} className='mr-3 text-gray-400' />
                      {isEditMode ? (
                        <input
                          type='tel'
                          className='flex-1 px-3 py-1 border rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                          value={selectedAgent.phone}
                          onChange={(e) =>
                            setSelectedAgent({
                              ...selectedAgent,
                              phone: e.target.value,
                            })
                          }
                        />
                      ) : (
                        <span>{selectedAgent.phone}</span>
                      )}
                    </div>
                    <div className='flex items-center'>
                      <MapPin size={16} className='mr-3 text-gray-400' />
                      {isEditMode ? (
                        <input
                          type='text'
                          className='flex-1 px-3 py-1 border rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                          value={selectedAgent.location.address}
                          onChange={(e) =>
                            setSelectedAgent({
                              ...selectedAgent,
                              location: {
                                ...selectedAgent.location,
                                address: e.target.value,
                              },
                            })
                          }
                        />
                      ) : (
                        <span>{selectedAgent.location.address}</span>
                      )}
                    </div>
                    <div className='flex items-center'>
                      <Clock size={16} className='mr-3 text-gray-400' />
                      <span>Joined {formatDate(selectedAgent.joinedAt)}</span>
                    </div>
                  </div>

                  <h3 className='text-lg font-semibold mb-4 mt-6'>
                    Vehicle Information
                  </h3>
                  <div className='space-y-3'>
                    <div className='flex items-center'>
                      <Truck size={16} className='mr-3 text-gray-400' />
                      {isEditMode ? (
                        <select
                          className='flex-1 px-3 py-1 border rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                          value={selectedAgent.vehicle.type}
                          onChange={(e) =>
                            setSelectedAgent({
                              ...selectedAgent,
                              vehicle: {
                                ...selectedAgent.vehicle,
                                type: e.target.value,
                              },
                            })
                          }
                        >
                          <option value='motorcycle'>Motorcycle</option>
                          <option value='bicycle'>Bicycle</option>
                          <option value='car'>Car</option>
                          <option value='scooter'>Scooter</option>
                        </select>
                      ) : (
                        <span className='capitalize'>
                          {selectedAgent.vehicle.type}
                        </span>
                      )}
                    </div>
                    <div className='flex items-center'>
                      <span className='w-4 h-4 mr-3 text-gray-400'>#</span>
                      {isEditMode ? (
                        <input
                          type='text'
                          className='flex-1 px-3 py-1 border rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                          value={selectedAgent.vehicle.licensePlate}
                          onChange={(e) =>
                            setSelectedAgent({
                              ...selectedAgent,
                              vehicle: {
                                ...selectedAgent.vehicle,
                                licensePlate: e.target.value,
                              },
                            })
                          }
                        />
                      ) : (
                        <span>{selectedAgent.vehicle.licensePlate}</span>
                      )}
                    </div>
                    <div className='flex items-center'>
                      <span className='w-4 h-4 mr-3 text-gray-400'>🎨</span>
                      {isEditMode ? (
                        <input
                          type='text'
                          className='flex-1 px-3 py-1 border rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                          value={selectedAgent.vehicle.color}
                          onChange={(e) =>
                            setSelectedAgent({
                              ...selectedAgent,
                              vehicle: {
                                ...selectedAgent.vehicle,
                                color: e.target.value,
                              },
                            })
                          }
                        />
                      ) : (
                        <span className='capitalize'>
                          {selectedAgent.vehicle.color}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-semibold mb-4'>
                    Performance Metrics
                  </h3>
                  <div className='grid grid-cols-1 gap-4'>
                    <div className='p-4 bg-gray-50 rounded-lg'>
                      <div className='flex items-center justify-between'>
                        <div>
                          <div className='text-sm text-gray-600'>
                            Total Deliveries
                          </div>
                          <div className='text-2xl font-semibold'>
                            {selectedAgent.totalDeliveries}
                          </div>
                        </div>
                        <Package size={24} className='text-blue-500' />
                      </div>
                    </div>
                    <div className='p-4 bg-gray-50 rounded-lg'>
                      <div className='flex items-center justify-between'>
                        <div>
                          <div className='text-sm text-gray-600'>
                            Total Earnings
                          </div>
                          <div className='text-2xl font-semibold'>
                            {formatCurrency(selectedAgent.totalEarnings)}
                          </div>
                        </div>
                        <DollarSign size={24} className='text-green-500' />
                      </div>
                    </div>
                    <div className='p-4 bg-gray-50 rounded-lg'>
                      <div className='flex items-center justify-between'>
                        <div>
                          <div className='text-sm text-gray-600'>Rating</div>
                          <div className='flex items-center'>
                            <Star size={20} className='text-yellow-400 mr-1' />
                            <span className='text-2xl font-semibold'>
                              {selectedAgent.rating}
                            </span>
                          </div>
                        </div>
                        <TrendingUp size={24} className='text-purple-500' />
                      </div>
                    </div>
                  </div>

                  <h3 className='text-lg font-semibold mb-4 mt-6'>
                    Delivery Zones
                  </h3>
                  <div className='flex flex-wrap gap-2'>
                    {selectedAgent.deliveryZones?.map((zone, index) => (
                      <Badge key={index} className='bg-blue-100 text-blue-800'>
                        {zone}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className='mt-6 pt-6 border-t flex justify-end space-x-3'>
                {isEditMode ? (
                  <>
                    <Button variant='outline' onClick={handleCancelEdit}>
                      Cancel
                    </Button>
                    <Button variant='primary' onClick={handleSaveAgent}>
                      Save Changes
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      variant='outline'
                      icon={<Navigation size={16} />}
                      onClick={() => handleTrackLocation(selectedAgent)}
                    >
                      Track Location
                    </Button>
                    <Button
                      variant='primary'
                      icon={<Edit size={16} />}
                      onClick={handleEditAgent}
                    >
                      Edit Agent
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default DeliveryAgentManagement;
