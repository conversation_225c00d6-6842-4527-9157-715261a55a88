import React, { useState } from "react";
import { orderApi, cartApi, deliveryApi } from "../../utils/orderApi";
import Button from "../../components/common/Button";

const OrderApiTest = () => {
  const [loading, setLoading] = useState({});
  const [results, setResults] = useState({});

  const testCreateOrder = async () => {
    setLoading((prev) => ({ ...prev, createOrder: true }));
    try {
      const orderData = {
        delivery_address: 5, // Replace with actual address ID
        restaurant: 2, // Replace with actual restaurant ID
        payment_method: "cash_on_delivery",
        special_instructions: "Leave at doorstep",
        items: [
          {
            menu_item_id: 5,
            quantity: 2,
          },
          {
            menu_item_id: 6,
            quantity: 2,
          },
        ],
      };

      const result = await orderApi.createOrder(orderData);
      setResults((prev) => ({ ...prev, createOrder: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        createOrder: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, createOrder: false }));
  };

  const testGetOrders = async () => {
    setLoading((prev) => ({ ...prev, getOrders: true }));
    try {
      const result = await orderApi.getOrders();
      setResults((prev) => ({ ...prev, getOrders: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getOrders: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getOrders: false }));
  };

  const testGetSingleOrder = async () => {
    setLoading((prev) => ({ ...prev, getSingleOrder: true }));
    try {
      const result = await orderApi.getOrder(15); // Replace with actual order ID
      setResults((prev) => ({ ...prev, getSingleOrder: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getSingleOrder: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getSingleOrder: false }));
  };

  const testGetOrdersByStatus = async () => {
    setLoading((prev) => ({ ...prev, getOrdersByStatus: true }));
    try {
      const result = await orderApi.getOrdersByStatus("pending");
      setResults((prev) => ({ ...prev, getOrdersByStatus: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getOrdersByStatus: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getOrdersByStatus: false }));
  };

  const testUpdateOrder = async () => {
    setLoading((prev) => ({ ...prev, updateOrder: true }));
    try {
      const updateData = {
        delivery_address: 5,
        restaurant: 2,
        payment_method: "cash_on_delivery",
        special_instructions: "Updated instructions",
        items: [
          {
            menu_item_id: 5,
            quantity: 3,
          },
        ],
      };
      const result = await orderApi.updateOrder(15, updateData); // Replace with actual order ID
      setResults((prev) => ({ ...prev, updateOrder: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        updateOrder: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, updateOrder: false }));
  };

  const testPatchOrder = async () => {
    setLoading((prev) => ({ ...prev, patchOrder: true }));
    try {
      const patchData = {
        status: "delivered",
      };
      const result = await orderApi.patchOrder(15, patchData); // Replace with actual order ID
      setResults((prev) => ({ ...prev, patchOrder: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        patchOrder: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, patchOrder: false }));
  };

  const testDeleteOrder = async () => {
    setLoading((prev) => ({ ...prev, deleteOrder: true }));
    try {
      const result = await orderApi.deleteOrder(15); // Replace with actual order ID
      setResults((prev) => ({ ...prev, deleteOrder: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        deleteOrder: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, deleteOrder: false }));
  };

  const testSaveCart = async () => {
    setLoading((prev) => ({ ...prev, saveCart: true }));
    try {
      const cartData = {
        restaurant_id: 2,
        items: [
          {
            menu_item_id: 5,
            quantity: 3,
            special_requests: "No spice",
          },
          {
            menu_item_id: 6,
            quantity: 3,
            special_requests: "Extra sauce",
          },
        ],
      };
      const result = await cartApi.saveCart(cartData);
      setResults((prev) => ({ ...prev, saveCart: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        saveCart: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, saveCart: false }));
  };

  const testGetCart = async () => {
    setLoading((prev) => ({ ...prev, getCart: true }));
    try {
      const result = await cartApi.getCart();
      setResults((prev) => ({ ...prev, getCart: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getCart: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getCart: false }));
  };

  const testDeleteCart = async () => {
    setLoading((prev) => ({ ...prev, deleteCart: true }));
    try {
      const result = await cartApi.deleteCart();
      setResults((prev) => ({ ...prev, deleteCart: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        deleteCart: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, deleteCart: false }));
  };

  const testGetOrderStatusHistory = async () => {
    setLoading((prev) => ({ ...prev, getStatusHistory: true }));
    try {
      const result = await orderApi.getOrderStatusHistory(15); // Replace with actual order ID
      setResults((prev) => ({ ...prev, getStatusHistory: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getStatusHistory: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getStatusHistory: false }));
  };

  const testAssignOrder = async () => {
    setLoading((prev) => ({ ...prev, assignOrder: true }));
    try {
      const assignmentData = {
        order_id: 14,
        agent_id: 15,
      };
      const result = await deliveryApi.assignOrder(assignmentData);
      setResults((prev) => ({ ...prev, assignOrder: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        assignOrder: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, assignOrder: false }));
  };

  const testRejectOrder = async () => {
    setLoading((prev) => ({ ...prev, rejectOrder: true }));
    try {
      const rejectionData = {
        order_id: 13,
        reason: "Too far from location",
      };
      const result = await deliveryApi.rejectOrder(rejectionData);
      setResults((prev) => ({ ...prev, rejectOrder: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        rejectOrder: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, rejectOrder: false }));
  };

  const testGetAvailableOrders = async () => {
    setLoading((prev) => ({ ...prev, getAvailableOrders: true }));
    try {
      const result = await deliveryApi.getAvailableOrders();
      setResults((prev) => ({ ...prev, getAvailableOrders: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getAvailableOrders: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getAvailableOrders: false }));
  };

  const renderResult = (key, title) => {
    const result = results[key];
    if (!result) return null;

    return (
      <div className='mt-4 p-4 border rounded-lg'>
        <h4 className='font-semibold text-lg mb-2'>{title} Result:</h4>
        <div
          className={`p-3 rounded ${
            result.success
              ? "bg-green-50 border-green-200"
              : "bg-red-50 border-red-200"
          }`}
        >
          <p
            className={`font-medium ${
              result.success ? "text-green-800" : "text-red-800"
            }`}
          >
            {result.success ? "Success" : "Error"}
          </p>
          {result.message && <p className='text-sm mt-1'>{result.message}</p>}
          {result.error && (
            <p className='text-sm mt-1 text-red-600'>{result.error}</p>
          )}
          {result.data && (
            <details className='mt-2'>
              <summary className='cursor-pointer text-sm font-medium'>
                View Data
              </summary>
              <pre className='mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40'>
                {JSON.stringify(result.data, null, 2)}
              </pre>
            </details>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-6xl mx-auto'>
        <h1 className='text-3xl font-bold mb-8'>Order & Cart API Test</h1>

        {/* Order API Tests */}
        <div className='mb-8'>
          <h2 className='text-2xl font-semibold mb-4'>Order API</h2>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
            <Button
              onClick={testCreateOrder}
              disabled={loading.createOrder}
              className='w-full'
            >
              {loading.createOrder ? "Creating..." : "Create Order"}
            </Button>
            <Button
              onClick={testGetOrders}
              disabled={loading.getOrders}
              className='w-full'
            >
              {loading.getOrders ? "Loading..." : "Get All Orders"}
            </Button>
            <Button
              onClick={testGetSingleOrder}
              disabled={loading.getSingleOrder}
              className='w-full'
            >
              {loading.getSingleOrder ? "Loading..." : "Get Single Order"}
            </Button>
            <Button
              onClick={testGetOrdersByStatus}
              disabled={loading.getOrdersByStatus}
              className='w-full'
            >
              {loading.getOrdersByStatus
                ? "Loading..."
                : "Get Orders by Status"}
            </Button>
            <Button
              onClick={testUpdateOrder}
              disabled={loading.updateOrder}
              className='w-full'
            >
              {loading.updateOrder ? "Updating..." : "Update Order (PUT)"}
            </Button>
            <Button
              onClick={testPatchOrder}
              disabled={loading.patchOrder}
              className='w-full'
            >
              {loading.patchOrder ? "Updating..." : "Update Order (PATCH)"}
            </Button>
            <Button
              onClick={testDeleteOrder}
              disabled={loading.deleteOrder}
              className='w-full bg-red-500 hover:bg-red-600'
            >
              {loading.deleteOrder ? "Deleting..." : "Delete Order"}
            </Button>
            <Button
              onClick={testGetOrderStatusHistory}
              disabled={loading.getStatusHistory}
              className='w-full'
            >
              {loading.getStatusHistory ? "Loading..." : "Get Status History"}
            </Button>
          </div>
        </div>

        {/* Cart API Tests */}
        <div className='mb-8'>
          <h2 className='text-2xl font-semibold mb-4'>Cart API</h2>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
            <Button
              onClick={testSaveCart}
              disabled={loading.saveCart}
              className='w-full'
            >
              {loading.saveCart ? "Saving..." : "Save Cart"}
            </Button>
            <Button
              onClick={testGetCart}
              disabled={loading.getCart}
              className='w-full'
            >
              {loading.getCart ? "Loading..." : "Get Cart"}
            </Button>
            <Button
              onClick={testDeleteCart}
              disabled={loading.deleteCart}
              className='w-full bg-red-500 hover:bg-red-600'
            >
              {loading.deleteCart ? "Deleting..." : "Delete Cart"}
            </Button>
          </div>
        </div>

        {/* Delivery API Tests */}
        <div className='mb-8'>
          <h2 className='text-2xl font-semibold mb-4'>
            Delivery Assignment API
          </h2>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
            <Button
              onClick={testAssignOrder}
              disabled={loading.assignOrder}
              className='w-full'
            >
              {loading.assignOrder ? "Assigning..." : "Assign Order"}
            </Button>
            <Button
              onClick={testRejectOrder}
              disabled={loading.rejectOrder}
              className='w-full'
            >
              {loading.rejectOrder ? "Rejecting..." : "Reject Order"}
            </Button>
            <Button
              onClick={testGetAvailableOrders}
              disabled={loading.getAvailableOrders}
              className='w-full'
            >
              {loading.getAvailableOrders
                ? "Loading..."
                : "Get Available Orders"}
            </Button>
          </div>
        </div>

        {/* Results */}
        <div className='space-y-4'>
          {renderResult("createOrder", "Create Order")}
          {renderResult("getOrders", "Get All Orders")}
          {renderResult("getSingleOrder", "Get Single Order")}
          {renderResult("getOrdersByStatus", "Get Orders by Status")}
          {renderResult("updateOrder", "Update Order (PUT)")}
          {renderResult("patchOrder", "Update Order (PATCH)")}
          {renderResult("deleteOrder", "Delete Order")}
          {renderResult("getStatusHistory", "Get Order Status History")}
          {renderResult("saveCart", "Save Cart")}
          {renderResult("getCart", "Get Cart")}
          {renderResult("deleteCart", "Delete Cart")}
          {renderResult("assignOrder", "Assign Order")}
          {renderResult("rejectOrder", "Reject Order")}
          {renderResult("getAvailableOrders", "Get Available Orders")}
        </div>

        <div className='mt-8 bg-blue-50 border-l-4 border-blue-500 p-4'>
          <h3 className='font-semibold text-blue-800 mb-2'>API Information:</h3>
          <ul className='text-blue-700 text-sm space-y-1'>
            <li>
              <strong>Base URL:</strong> https://afghansufra.luilala.com/api
            </li>
            <li>
              <strong>Authentication:</strong> Bearer Token (from localStorage)
            </li>
            <li>
              <strong>Order Create:</strong> POST /order/orders/
            </li>
            <li>
              <strong>Order List:</strong> GET /order/orders/
            </li>
            <li>
              <strong>Order Detail:</strong> GET /order/orders/15/
            </li>
            <li>
              <strong>Order Filter:</strong> GET /order/orders/?status=pending
            </li>
            <li>
              <strong>Order Update:</strong> PUT /order/orders/15/
            </li>
            <li>
              <strong>Order Patch:</strong> PATCH /order/orders/15/
            </li>
            <li>
              <strong>Order Delete:</strong> DELETE /order/orders/15/
            </li>
            <li>
              <strong>Cart Save:</strong> PUT /order/carts/mine/
            </li>
            <li>
              <strong>Cart Get:</strong> GET /order/carts/mine/
            </li>
            <li>
              <strong>Cart Delete:</strong> DELETE /order/carts/destroy/
            </li>
            <li>
              <strong>Order Status History:</strong> GET
              /order/orders/15/status-history/
            </li>
            <li>
              <strong>Assign Order:</strong> POST
              /order/delivery-assignment/assign/
            </li>
            <li>
              <strong>Reject Order:</strong> POST
              /order/delivery-assignment/reject/
            </li>
            <li>
              <strong>Available Orders:</strong> GET
              /order/delivery-assignment/available_orders/
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default OrderApiTest;
