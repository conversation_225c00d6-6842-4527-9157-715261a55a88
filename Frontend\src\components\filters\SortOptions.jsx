import React, { useState } from 'react';
import { ChevronDown, Check, ArrowUpDown } from 'lucide-react';
import { useFilters } from '../../context/FiltersContext';

const SortOptions = () => {
  const { filters, filterOptions, updateFilter } = useFilters();
  const [isOpen, setIsOpen] = useState(false);

  const currentSort = filterOptions.sortOptions.find(
    option => option.value === filters.sortBy
  );

  const handleSortChange = (sortValue) => {
    updateFilter('sortBy', sortValue);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
      >
        <ArrowUpDown size={16} className="text-gray-500" />
        <span className="text-sm font-medium">{currentSort?.label}</span>
        <ChevronDown 
          size={16} 
          className={`text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {isOpen && (
        <>
          {/* Overlay */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          ></div>
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-20 animate-fade-in">
            <div className="py-2">
              <div className="px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide border-b border-gray-100">
                Sort by
              </div>
              {filterOptions.sortOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleSortChange(option.value)}
                  className="w-full flex items-center justify-between px-3 py-2 text-sm text-left hover:bg-gray-50 transition-colors"
                >
                  <span className={filters.sortBy === option.value ? 'font-medium text-primary-600' : 'text-gray-700'}>
                    {option.label}
                  </span>
                  {filters.sortBy === option.value && (
                    <Check size={16} className="text-primary-600" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SortOptions;
