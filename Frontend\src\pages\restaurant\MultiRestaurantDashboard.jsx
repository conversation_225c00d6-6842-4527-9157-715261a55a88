import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import {
  Store,
  Plus,
  TrendingUp,
  DollarSign,
  ShoppingBag,
  Users,
  Star,
  Clock,
  Shield,
  ShieldCheck,
  AlertCircle,
  Settings,
  Menu,
  BarChart3,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import RestaurantSelector from "../../components/restaurant/RestaurantSelector";
import VerificationStatus from "../../components/restaurant/VerificationStatus";

const MultiRestaurantDashboard = () => {
  const { user } = useAuth();
  const { restaurants, getRestaurants, loading } = useRestaurant();
  const [selectedRestaurant, setSelectedRestaurant] = useState(null);
  const [userRestaurants, setUserRestaurants] = useState([]);
  const [dashboardLoading, setDashboardLoading] = useState(true);

  // Fetch user's restaurants
  useEffect(() => {
    const fetchUserRestaurants = async () => {
      if (user && user.role === 'restaurant') {
        try {
          setDashboardLoading(true);
          await getRestaurants();
          
          // Filter restaurants owned by current user
          const ownedRestaurants = restaurants.filter(r => r.owner === user.id);
          setUserRestaurants(ownedRestaurants);
          
          // Auto-select first restaurant if none selected
          if (!selectedRestaurant && ownedRestaurants.length > 0) {
            setSelectedRestaurant(ownedRestaurants[0]);
          }
        } catch (error) {
          console.error('Error fetching restaurants:', error);
        } finally {
          setDashboardLoading(false);
        }
      }
    };

    fetchUserRestaurants();
  }, [user, restaurants, getRestaurants, selectedRestaurant]);

  const handleRestaurantSelect = (restaurant) => {
    setSelectedRestaurant(restaurant);
  };

  if (dashboardLoading) {
    return (
      <div className='p-6 animate-fade-in'>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          <span className="ml-3 text-gray-600">Loading dashboard...</span>
        </div>
      </div>
    );
  }

  // Show welcome screen for new restaurant owners
  if (userRestaurants.length === 0) {
    return (
      <div className='p-6 animate-fade-in'>
        <div className='max-w-4xl mx-auto'>
          {/* Welcome Header */}
          <div className='text-center mb-8'>
            <div className='w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4'>
              <Store className='text-primary-600' size={40} />
            </div>
            <h1 className='text-3xl font-bold text-gray-900 mb-2'>
              Welcome to Afghan Sofra!
            </h1>
            <p className='text-gray-600 max-w-2xl mx-auto mb-6'>
              Start your journey as a restaurant partner. Create your first restaurant to begin managing menus, orders, and growing your business.
            </p>
            <Button
              variant="primary"
              size="large"
              icon={<Plus size={20} />}
              to="/add-restaurant"
            >
              Add Your First Restaurant
            </Button>
          </div>

          {/* Benefits Cards */}
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
            <Card className='p-6 text-center'>
              <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <Menu className='text-blue-600' size={24} />
              </div>
              <h3 className='font-semibold mb-2'>Menu Management</h3>
              <p className='text-sm text-gray-600'>
                Easily manage your menu categories and items with our intuitive interface.
              </p>
            </Card>

            <Card className='p-6 text-center'>
              <div className='w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <ShoppingBag className='text-green-600' size={24} />
              </div>
              <h3 className='font-semibold mb-2'>Order Management</h3>
              <p className='text-sm text-gray-600'>
                Track and manage orders in real-time with our comprehensive dashboard.
              </p>
            </Card>

            <Card className='p-6 text-center'>
              <div className='w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <BarChart3 className='text-purple-600' size={24} />
              </div>
              <h3 className='font-semibold mb-2'>Analytics & Insights</h3>
              <p className='text-sm text-gray-600'>
                Get detailed analytics to understand your business performance.
              </p>
            </Card>
          </div>

          {/* Getting Started Steps */}
          <Card className='p-6'>
            <h2 className='text-xl font-semibold mb-4'>Getting Started</h2>
            <div className='space-y-4'>
              <div className='flex items-start space-x-3'>
                <div className='w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-medium'>
                  1
                </div>
                <div>
                  <h3 className='font-medium'>Add Your Restaurant</h3>
                  <p className='text-sm text-gray-600'>
                    Provide restaurant details, location, and business information.
                  </p>
                </div>
              </div>
              <div className='flex items-start space-x-3'>
                <div className='w-6 h-6 bg-gray-300 text-white rounded-full flex items-center justify-center text-sm font-medium'>
                  2
                </div>
                <div>
                  <h3 className='font-medium text-gray-500'>Wait for Verification</h3>
                  <p className='text-sm text-gray-500'>
                    Our team will review and verify your restaurant (1-2 business days).
                  </p>
                </div>
              </div>
              <div className='flex items-start space-x-3'>
                <div className='w-6 h-6 bg-gray-300 text-white rounded-full flex items-center justify-center text-sm font-medium'>
                  3
                </div>
                <div>
                  <h3 className='font-medium text-gray-500'>Set Up Your Menu</h3>
                  <p className='text-sm text-gray-500'>
                    Add menu categories and items to start receiving orders.
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className='p-6 animate-fade-in'>
      <div className='max-w-7xl mx-auto'>
        {/* Header with Restaurant Selector */}
        <div className='flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6'>
          <div className='mb-4 lg:mb-0'>
            <h1 className='text-2xl font-bold text-gray-900 mb-2'>
              Restaurant Dashboard
            </h1>
            <p className='text-gray-600'>
              Manage your restaurants, menus, and orders from one place.
            </p>
          </div>
          
          <div className='flex items-center space-x-4'>
            <Button
              variant="outline"
              icon={<Plus size={18} />}
              to="/add-restaurant"
            >
              Add Restaurant
            </Button>
          </div>
        </div>

        {/* Restaurant Selector */}
        <div className='mb-6'>
          <RestaurantSelector
            onRestaurantSelect={handleRestaurantSelect}
            selectedRestaurant={selectedRestaurant}
          />
        </div>

        {/* Selected Restaurant Dashboard */}
        {selectedRestaurant && (
          <>
            {/* Verification Status */}
            <VerificationStatus
              restaurant={selectedRestaurant}
              user={user}
              variant="banner"
              showForVerified={false}
            />

            {/* Restaurant Overview */}
            <Card className='mb-6 p-6'>
              <div className='flex items-center space-x-4'>
                <div className='w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center'>
                  <Store className='text-primary-600' size={32} />
                </div>
                <div className='flex-1'>
                  <div className='flex items-center space-x-2 mb-1'>
                    <h2 className='text-xl font-bold text-gray-900'>
                      {selectedRestaurant.name}
                    </h2>
                    {selectedRestaurant.is_verified ? (
                      <Badge variant="success" size="small">
                        <ShieldCheck size={12} className="mr-1" />
                        Verified
                      </Badge>
                    ) : (
                      <Badge variant="warning" size="small">
                        <AlertCircle size={12} className="mr-1" />
                        Pending Verification
                      </Badge>
                    )}
                  </div>
                  <p className='text-gray-600 mb-2'>{selectedRestaurant.description}</p>
                  <div className='flex items-center space-x-4 text-sm text-gray-500'>
                    <div className='flex items-center space-x-1'>
                      <Star className='text-yellow-400 fill-current' size={14} />
                      <span>{selectedRestaurant.rating || '0.0'}</span>
                    </div>
                    <div className='flex items-center space-x-1'>
                      <Clock size={14} />
                      <span>{selectedRestaurant.opening_time} - {selectedRestaurant.closing_time}</span>
                    </div>
                  </div>
                </div>
                <div className='flex space-x-2'>
                  <Button
                    variant="outline"
                    size="small"
                    icon={<Settings size={16} />}
                    to={`/restaurant/profile?id=${selectedRestaurant.id}`}
                  >
                    Settings
                  </Button>
                </div>
              </div>
            </Card>

            {/* Quick Actions */}
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6'>
              <Button
                variant="outline"
                className="p-4 h-auto flex-col"
                to="/restaurant/menu"
                disabled={!selectedRestaurant.is_verified}
              >
                <Menu size={24} className="mb-2" />
                <span>Manage Menu</span>
              </Button>
              
              <Button
                variant="outline"
                className="p-4 h-auto flex-col"
                to="/restaurant/orders"
              >
                <ShoppingBag size={24} className="mb-2" />
                <span>View Orders</span>
              </Button>
              
              <Button
                variant="outline"
                className="p-4 h-auto flex-col"
                to="/restaurant/analytics"
              >
                <BarChart3 size={24} className="mb-2" />
                <span>Analytics</span>
              </Button>
              
              <Button
                variant="outline"
                className="p-4 h-auto flex-col"
                to={`/restaurant/profile?id=${selectedRestaurant.id}`}
              >
                <Settings size={24} className="mb-2" />
                <span>Settings</span>
              </Button>
            </div>

            {/* Stats Overview */}
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
              <Card className='p-6'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm text-gray-600'>Total Orders</p>
                    <p className='text-2xl font-bold text-gray-900'>0</p>
                  </div>
                  <div className='w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center'>
                    <ShoppingBag className='text-blue-600' size={24} />
                  </div>
                </div>
              </Card>

              <Card className='p-6'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm text-gray-600'>Revenue</p>
                    <p className='text-2xl font-bold text-gray-900'>$0</p>
                  </div>
                  <div className='w-12 h-12 bg-green-100 rounded-full flex items-center justify-center'>
                    <DollarSign className='text-green-600' size={24} />
                  </div>
                </div>
              </Card>

              <Card className='p-6'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm text-gray-600'>Menu Items</p>
                    <p className='text-2xl font-bold text-gray-900'>0</p>
                  </div>
                  <div className='w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center'>
                    <Menu className='text-purple-600' size={24} />
                  </div>
                </div>
              </Card>

              <Card className='p-6'>
                <div className='flex items-center justify-between'>
                  <div>
                    <p className='text-sm text-gray-600'>Rating</p>
                    <p className='text-2xl font-bold text-gray-900'>{selectedRestaurant.rating || '0.0'}</p>
                  </div>
                  <div className='w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center'>
                    <Star className='text-yellow-600' size={24} />
                  </div>
                </div>
              </Card>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default MultiRestaurantDashboard;
