# customer/views.py
from rest_framework import generics, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.core.exceptions import ValidationError
from django.db import transaction, IntegrityError
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.core.cache import cache
from .models import CustomerFavorite
from .serializers import CustomerFavoriteSerializer, FavoriteRestaurantSerializer
from restaurant.models import Restaurant
import logging

logger = logging.getLogger(__name__)

@method_decorator(cache_page(60 * 5), name='dispatch')  # Cache for 5 minutes
class CustomerFavoritesListView(generics.ListAPIView):
    """List all favorites for the authenticated customer"""
    serializer_class = FavoriteRestaurantSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        try:
            # Get restaurants that are favorited by the current user
            favorite_restaurant_ids = CustomerFavorite.objects.filter(
                customer=self.request.user
            ).values_list('restaurant_id', flat=True)

            return Restaurant.objects.filter(
                id__in=favorite_restaurant_ids,
                is_active=True
            ).select_related().prefetch_related('cuisine_types')
        except Exception as e:
            logger.error(f"Error fetching favorites for user {self.request.user.id}: {str(e)}")
            return Restaurant.objects.none()

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            return Response({
                'success': True,
                'data': serializer.data,
                'count': len(serializer.data),
                'message': 'Favorites retrieved successfully'
            }, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"Error in favorites list view: {str(e)}")
            return Response({
                'success': False,
                'error': 'Failed to retrieve favorites',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_to_favorites(request):
    """Add a restaurant to user's favorites"""
    try:
        restaurant_id = request.data.get('restaurant_id')

        # Validate input
        if not restaurant_id:
            return Response({
                'success': False,
                'error': 'restaurant_id is required',
                'field_errors': {'restaurant_id': ['This field is required.']}
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate restaurant_id is a valid integer
        try:
            restaurant_id = int(restaurant_id)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'restaurant_id must be a valid integer',
                'field_errors': {'restaurant_id': ['Must be a valid integer.']}
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if restaurant exists and is active
        try:
            restaurant = Restaurant.objects.get(id=restaurant_id, is_active=True)
        except Restaurant.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Restaurant not found or inactive',
            }, status=status.HTTP_404_NOT_FOUND)

        # Use transaction to ensure data consistency
        with transaction.atomic():
            favorite, created = CustomerFavorite.objects.get_or_create(
                customer=request.user,
                restaurant=restaurant
            )

        # Clear cache for user's favorites
        cache_key = f"user_favorites_{request.user.id}"
        cache.delete(cache_key)

        if created:
            logger.info(f"User {request.user.id} added restaurant {restaurant_id} to favorites")
            return Response({
                'success': True,
                'message': 'Restaurant added to favorites successfully',
                'data': {
                    'restaurant_id': restaurant_id,
                    'restaurant_name': restaurant.name,
                    'is_favorite': True
                }
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                'success': True,
                'message': 'Restaurant is already in favorites',
                'data': {
                    'restaurant_id': restaurant_id,
                    'restaurant_name': restaurant.name,
                    'is_favorite': True
                }
            }, status=status.HTTP_200_OK)

    except IntegrityError as e:
        logger.error(f"Integrity error adding favorite: {str(e)}")
        return Response({
            'success': False,
            'error': 'Database integrity error occurred'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logger.error(f"Unexpected error adding favorite: {str(e)}")
        return Response({
            'success': False,
            'error': 'An unexpected error occurred'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def remove_from_favorites(request, restaurant_id):
    """Remove a restaurant from user's favorites"""
    try:
        # Validate restaurant_id is a valid integer
        try:
            restaurant_id = int(restaurant_id)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'restaurant_id must be a valid integer',
            }, status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():
            try:
                favorite = CustomerFavorite.objects.get(
                    customer=request.user,
                    restaurant_id=restaurant_id
                )
                restaurant_name = favorite.restaurant.name
                favorite.delete()

                # Clear cache for user's favorites
                cache_key = f"user_favorites_{request.user.id}"
                cache.delete(cache_key)

                logger.info(f"User {request.user.id} removed restaurant {restaurant_id} from favorites")

                return Response({
                    'success': True,
                    'message': 'Restaurant removed from favorites successfully',
                    'data': {
                        'restaurant_id': restaurant_id,
                        'restaurant_name': restaurant_name,
                        'is_favorite': False
                    }
                }, status=status.HTTP_200_OK)

            except CustomerFavorite.DoesNotExist:
                return Response({
                    'success': False,
                    'error': 'Restaurant not found in favorites',
                }, status=status.HTTP_404_NOT_FOUND)

    except Exception as e:
        logger.error(f"Error removing favorite: {str(e)}")
        return Response({
            'success': False,
            'error': 'An unexpected error occurred'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_favorite_status(request, restaurant_id):
    """Check if a restaurant is in user's favorites"""
    try:
        # Validate restaurant_id is a valid integer
        try:
            restaurant_id = int(restaurant_id)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'restaurant_id must be a valid integer',
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if restaurant exists
        if not Restaurant.objects.filter(id=restaurant_id, is_active=True).exists():
            return Response({
                'success': False,
                'error': 'Restaurant not found or inactive',
            }, status=status.HTTP_404_NOT_FOUND)

        is_favorite = CustomerFavorite.objects.filter(
            customer=request.user,
            restaurant_id=restaurant_id
        ).exists()

        return Response({
            'success': True,
            'data': {
                'restaurant_id': restaurant_id,
                'is_favorite': is_favorite
            }
        }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error checking favorite status: {str(e)}")
        return Response({
            'success': False,
            'error': 'An unexpected error occurred'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def clear_all_favorites(request):
    """Clear all favorites for the user"""
    try:
        with transaction.atomic():
            deleted_count, _ = CustomerFavorite.objects.filter(customer=request.user).delete()

            # Clear cache for user's favorites
            cache_key = f"user_favorites_{request.user.id}"
            cache.delete(cache_key)

            logger.info(f"User {request.user.id} cleared {deleted_count} favorites")

            return Response({
                'success': True,
                'message': f'Successfully cleared {deleted_count} favorite{"s" if deleted_count != 1 else ""}',
                'data': {
                    'cleared_count': deleted_count
                }
            }, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error clearing favorites: {str(e)}")
        return Response({
            'success': False,
            'error': 'An unexpected error occurred while clearing favorites'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def toggle_favorite(request):
    """Toggle favorite status for a restaurant"""
    try:
        restaurant_id = request.data.get('restaurant_id')

        # Validate input
        if not restaurant_id:
            return Response({
                'success': False,
                'error': 'restaurant_id is required',
                'field_errors': {'restaurant_id': ['This field is required.']}
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate restaurant_id is a valid integer
        try:
            restaurant_id = int(restaurant_id)
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'restaurant_id must be a valid integer',
                'field_errors': {'restaurant_id': ['Must be a valid integer.']}
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if restaurant exists and is active
        try:
            restaurant = Restaurant.objects.get(id=restaurant_id, is_active=True)
        except Restaurant.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Restaurant not found or inactive',
            }, status=status.HTTP_404_NOT_FOUND)

        with transaction.atomic():
            favorite = CustomerFavorite.objects.filter(
                customer=request.user,
                restaurant=restaurant
            ).first()

            if favorite:
                # Remove from favorites
                favorite.delete()
                is_favorite = False
                action = 'removed from'
                status_code = status.HTTP_200_OK
            else:
                # Add to favorites
                CustomerFavorite.objects.create(
                    customer=request.user,
                    restaurant=restaurant
                )
                is_favorite = True
                action = 'added to'
                status_code = status.HTTP_201_CREATED

            # Clear cache for user's favorites
            cache_key = f"user_favorites_{request.user.id}"
            cache.delete(cache_key)

            logger.info(f"User {request.user.id} {action} favorites: restaurant {restaurant_id}")

            return Response({
                'success': True,
                'message': f'Restaurant {action} favorites successfully',
                'data': {
                    'restaurant_id': restaurant_id,
                    'restaurant_name': restaurant.name,
                    'is_favorite': is_favorite,
                    'action': action.split()[0]  # 'added' or 'removed'
                }
            }, status=status_code)

    except IntegrityError as e:
        logger.error(f"Integrity error toggling favorite: {str(e)}")
        return Response({
            'success': False,
            'error': 'Database integrity error occurred'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    except Exception as e:
        logger.error(f"Unexpected error toggling favorite: {str(e)}")
        return Response({
            'success': False,
            'error': 'An unexpected error occurred'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
