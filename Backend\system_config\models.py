# system_config/models.py
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import json

class SystemSetting(models.Model):
    """Dynamic system settings"""
    SETTING_TYPES = [
        ('string', 'String'),
        ('integer', 'Integer'),
        ('decimal', 'Decimal'),
        ('boolean', 'Boolean'),
        ('json', 'JSON'),
        ('text', 'Text'),
        ('email', 'Email'),
        ('url', 'URL'),
    ]

    CATEGORIES = [
        ('general', 'General'),
        ('delivery', 'Delivery'),
        ('payment', 'Payment'),
        ('notification', 'Notification'),
        ('security', 'Security'),
        ('appearance', 'Appearance'),
        ('business', 'Business'),
    ]

    key = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    value = models.TextField()
    default_value = models.TextField()
    setting_type = models.CharField(max_length=20, choices=SETTING_TYPES, default='string')
    category = models.CharField(max_length=20, choices=CATEGORIES, default='general')
    is_public = models.BooleanField(default=False, help_text="Can be accessed by frontend")
    is_editable = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['category', 'name']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['category']),
            models.Index(fields=['is_public']),
        ]

    def __str__(self):
        return f"{self.name} ({self.key})"

    def get_value(self):
        """Get typed value"""
        if self.setting_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.setting_type == 'integer':
            try:
                return int(self.value)
            except ValueError:
                return int(self.default_value) if self.default_value else 0
        elif self.setting_type == 'decimal':
            try:
                return float(self.value)
            except ValueError:
                return float(self.default_value) if self.default_value else 0.0
        elif self.setting_type == 'json':
            try:
                return json.loads(self.value)
            except json.JSONDecodeError:
                try:
                    return json.loads(self.default_value) if self.default_value else {}
                except json.JSONDecodeError:
                    return {}
        else:
            return self.value

class ChoiceOption(models.Model):
    """Dynamic choice options for dropdowns, filters, etc."""
    OPTION_TYPES = [
        ('user_role', 'User Role'),
        ('order_status', 'Order Status'),
        ('payment_method', 'Payment Method'),
        ('payment_status', 'Payment Status'),
        ('dietary_restriction', 'Dietary Restriction'),
        ('delivery_time_slot', 'Delivery Time Slot'),
        ('vehicle_type', 'Vehicle Type'),
        ('bank_name', 'Bank Name'),
        ('language', 'Language'),
        ('currency', 'Currency'),
        ('timezone', 'Timezone'),
        ('country', 'Country'),
        ('city', 'City'),
        ('feature_tag', 'Feature Tag'),
        ('sort_option', 'Sort Option'),
    ]

    option_type = models.CharField(max_length=30, choices=OPTION_TYPES)
    value = models.CharField(max_length=100)
    label = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="Icon class or emoji")
    color = models.CharField(max_length=7, blank=True, help_text="Hex color code")
    display_order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('option_type', 'value')
        ordering = ['option_type', 'display_order', 'label']
        indexes = [
            models.Index(fields=['option_type', 'is_active']),
            models.Index(fields=['is_default']),
        ]

    def __str__(self):
        return f"{self.get_option_type_display()}: {self.label}"

class FilterConfiguration(models.Model):
    """Dynamic filter configurations"""
    FILTER_TYPES = [
        ('range', 'Range'),
        ('select', 'Select'),
        ('multiselect', 'Multi-Select'),
        ('boolean', 'Boolean'),
        ('search', 'Search'),
    ]

    name = models.CharField(max_length=100)
    key = models.CharField(max_length=50, unique=True)
    filter_type = models.CharField(max_length=20, choices=FILTER_TYPES)
    label = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True)

    # Range filter settings
    min_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    max_value = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    step = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('1'))

    # Select filter settings
    options = models.JSONField(default=list, blank=True, help_text="List of options for select filters")

    # Display settings
    display_order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['display_order', 'name']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_featured']),
        ]

    def __str__(self):
        return f"{self.name} ({self.filter_type})"
