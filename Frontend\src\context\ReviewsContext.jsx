import React, { createContext, useContext, useState, useEffect } from 'react';

const ReviewsContext = createContext();

export const useReviews = () => useContext(ReviewsContext);

export const ReviewsProvider = ({ children }) => {
  const [reviews, setReviews] = useState({}); // { [restaurantId]: [review, ...] }
  const [agentReviews, setAgentReviews] = useState({}); // { [agentId]: [review, ...] }

  // Load from localStorage on mount
  useEffect(() => {
    const stored = localStorage.getItem('afghanSofraReviews');
    if (stored) setReviews(JSON.parse(stored));
    const storedAgents = localStorage.getItem('afghanSofraAgentReviews');
    if (storedAgents) setAgentReviews(JSON.parse(storedAgents));
  }, []);

  // Save to localStorage on change
  useEffect(() => {
    localStorage.setItem('afghanSofraReviews', JSON.stringify(reviews));
  }, [reviews]);
  useEffect(() => {
    localStorage.setItem('afghanSofraAgentReviews', JSON.stringify(agentReviews));
  }, [agentReviews]);

  const addReview = (restaurantId, review) => {
    setReviews(prev => ({
      ...prev,
      [restaurantId]: [review, ...(prev[restaurantId] || [])],
    }));
  };

  const getReviews = (restaurantId) => reviews[restaurantId] || [];

  // Delivery agent reviews
  const addAgentReview = (agentId, review) => {
    setAgentReviews(prev => ({
      ...prev,
      [agentId]: [review, ...(prev[agentId] || [])],
    }));
  };

  const getAgentReviews = (agentId) => agentReviews[agentId] || [];

  return (
    <ReviewsContext.Provider value={{ addReview, getReviews, addAgentReview, getAgentReviews }}>
      {children}
    </ReviewsContext.Provider>
  );
}; 