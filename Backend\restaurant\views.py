# restaurant/views.py
from django.forms import ValidationError
from django.shortcuts import get_object_or_404
from django.db import models
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.decorators import action
from .models import (
    Restaurant, MenuCategory, MenuItem, RestaurantReview, ReviewResponse,
    MenuItemVariant, MenuItemAddon, MenuItemCustomizationGroup, MenuItemCustomizationOption,
    CuisineType, RestaurantCategory, ServiceArea, Promotion, PromotionCode, Address
)
from .serializers import (
    RestaurantSerializer, MenuCategorySerializer, MenuItemSerializer,
    RestaurantReviewSerializer, ReviewResponseSerializer, RestaurantWithReviewsSerializer,
    MenuItemVariantSerializer, MenuItemAddonSerializer, MenuItemCustomizationGroupSerializer,
    MenuItemCustomizationOptionSerializer, MenuItemDetailSerializer,
    CuisineTypeSerializer, RestaurantCategorySerializer, ServiceAreaSerializer, RestaurantDetailSerializer,
    RestaurantAdminSerializer, PromotionSerializer, PromotionCreateSerializer, PromotionCodeSerializer,
    AddressSerializer
)
from users.models import User
from rest_framework.exceptions import PermissionDenied

class RestaurantViewSet(viewsets.ModelViewSet):
    queryset = Restaurant.objects.filter(is_active=True).order_by('id')
    serializer_class = RestaurantDetailSerializer  # Use detailed serializer for cuisine type support

    def get_permissions(self):
        """
        Allow public access for list and retrieve actions,
        require authentication for create, update, delete
        """
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        # Use detailed serializer for retrieve action to include reviews and business details
        if self.action == 'retrieve':
            return RestaurantDetailSerializer
        elif self.action in ['update', 'partial_update']:
            return RestaurantDetailSerializer
        return RestaurantSerializer

    def get_queryset(self):
        user = self.request.user
        # For public access (list/retrieve), return all active restaurants
        if self.action in ['list', 'retrieve']:
            return Restaurant.objects.filter(is_active=True).order_by('id')

        # For authenticated restaurant owners, show only their restaurants
        if user.is_authenticated and hasattr(user, 'role') and user.role == 'restaurant':
            return Restaurant.objects.filter(is_active=True, owner=user).order_by('id')

        # Default: return all active restaurants
        return Restaurant.objects.filter(is_active=True).order_by('id')

    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

    
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        
        # Verify ownership for non-superusers
        if request.user != instance.owner and not request.user.is_superuser:
            return Response(
                {'error': 'You do not have permission to edit this restaurant'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        return Response(serializer.data)
    
    @action(detail=True, methods=['patch'])
    def toggle_activation(self, request, pk=None):
        restaurant = self.get_object()
        if request.user != restaurant.owner and not request.user.is_superuser:
            return Response({'error': 'You do not have permission to perform this action.'},
                          status=status.HTTP_403_FORBIDDEN)

        restaurant.is_active = not restaurant.is_active
        restaurant.save()
        return Response({'status': 'success', 'is_active': restaurant.is_active})

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def my_restaurants(self, request):
        """
        Get all restaurants owned by the current user
        """
        if not hasattr(request.user, 'role') or request.user.role != 'restaurant':
            return Response(
                {'error': 'Only restaurant owners can access this endpoint'},
                status=status.HTTP_403_FORBIDDEN
            )

        restaurants = Restaurant.objects.filter(
            owner=request.user
        ).order_by('-created_at')

        serializer = self.get_serializer(restaurants, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], permission_classes=[IsAuthenticated])
    def admin_restaurants(self, request):
        """
        Get all restaurants for admin management (including unverified ones)
        """
        if not hasattr(request.user, 'role') or request.user.role != 'admin':
            return Response(
                {'error': 'Only admins can access this endpoint'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get query parameters for filtering
        status_filter = request.query_params.get('status', 'all')
        search = request.query_params.get('search', '')

        # Base queryset - include all restaurants (active and inactive)
        queryset = Restaurant.objects.all().select_related('owner', 'address').order_by('-created_at')

        # Apply status filter
        if status_filter == 'pending':
            queryset = queryset.filter(is_verified=False)
        elif status_filter == 'approved':
            queryset = queryset.filter(is_verified=True)
        elif status_filter == 'active':
            queryset = queryset.filter(is_active=True)
        elif status_filter == 'inactive':
            queryset = queryset.filter(is_active=False)

        # Apply search filter
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(owner__name__icontains=search) |
                models.Q(owner__email__icontains=search) |
                models.Q(contact_number__icontains=search)
            )

        serializer = RestaurantAdminSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['patch'], permission_classes=[IsAuthenticated])
    def admin_verify(self, request, pk=None):
        """
        Admin endpoint to verify/unverify restaurants
        """
        if not hasattr(request.user, 'role') or request.user.role != 'admin':
            return Response(
                {'error': 'Only admins can verify restaurants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get restaurant without filtering by is_active (admin should access all restaurants)
        try:
            restaurant = Restaurant.objects.get(pk=pk)
        except Restaurant.DoesNotExist:
            return Response(
                {'error': 'Restaurant not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        action = request.data.get('action')  # 'approve' or 'reject'
        notes = request.data.get('notes', '')

        if action == 'approve':
            restaurant.is_verified = True
            restaurant.is_active = True
            message = f"Restaurant '{restaurant.name}' has been approved and verified."
        elif action == 'reject':
            restaurant.is_verified = False
            restaurant.is_active = False
            message = f"Restaurant '{restaurant.name}' has been rejected."
        else:
            return Response(
                {'error': 'Invalid action. Use "approve" or "reject"'},
                status=status.HTTP_400_BAD_REQUEST
            )

        restaurant.save()

        # TODO: Send email notification to restaurant owner
        # send_restaurant_status_notification(restaurant, action, notes)

        return Response({
            'status': 'success',
            'message': message,
            'restaurant': RestaurantDetailSerializer(restaurant).data
        })

class MenuCategoryViewSet(viewsets.ModelViewSet):
    queryset = MenuCategory.objects.all()
    serializer_class = MenuCategorySerializer

    def get_permissions(self):
        """
        Allow public access for list and retrieve actions,
        require authentication for create, update, delete
        """
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_queryset(self):
        user = self.request.user
        restaurant_id = self.request.query_params.get('restaurant_id')

        # Base queryset with ownership check
        if user.is_authenticated and hasattr(user, 'role') and user.role == 'restaurant':
            queryset = self.queryset.filter(restaurant__owner=user)
        else:
            queryset = self.queryset.all()

        # Additional filtering
        if restaurant_id:
            queryset = queryset.filter(restaurant_id=restaurant_id)

        return queryset

    def perform_create(self, serializer):
        restaurant_id = self.request.data.get('restaurant')
        if not restaurant_id:
            raise ValidationError("restaurant field is required")

        restaurant = get_object_or_404(Restaurant, id=restaurant_id)
        if restaurant.owner != self.request.user:
            raise PermissionDenied("You don't own this restaurant")

        # Check if restaurant is verified
        if not restaurant.is_verified:
            raise PermissionDenied("Your restaurant must be verified by admin before you can add menu categories. Please wait for admin approval.")

        serializer.save(restaurant=restaurant)

class MenuItemViewSet(viewsets.ModelViewSet):
    queryset = MenuItem.objects.all()  # Remove is_available filter from base queryset
    serializer_class = MenuItemSerializer

    def get_permissions(self):
        """
        Allow public access for list and retrieve actions,
        require authentication for create, update, delete
        """
        if self.action in ['list', 'retrieve']:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def get_serializer_class(self):
        # Use detailed serializer for retrieve action to include variants and customizations
        if self.action == 'retrieve':
            return MenuItemDetailSerializer
        return MenuItemSerializer

    def get_queryset(self):
        user = self.request.user
        category_id = self.request.query_params.get('category_id')
        restaurant_id = self.request.query_params.get('restaurant_id')

        queryset = self.queryset

        # Apply ownership filter for restaurant owners
        if user.is_authenticated and hasattr(user, 'role') and user.role == 'restaurant':
            queryset = queryset.filter(category__restaurant__owner=user)

        # Additional filtering
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        elif restaurant_id:
            queryset = queryset.filter(category__restaurant_id=restaurant_id)
        
        # Only filter by is_available for non-owners
        if not user.is_authenticated or user.role != 'restaurant':
            queryset = queryset.filter(is_available=True)
            
        return queryset

    @action(detail=True, methods=['patch'])
    def toggle_availability(self, request, pk=None):
        menu_item = self.get_object()
        if menu_item.category.restaurant.owner != request.user:
            raise PermissionDenied("You don't own this menu item")
        menu_item.is_available = not menu_item.is_available
        menu_item.save()
        return Response({'status': 'success', 'is_available': menu_item.is_available})

    def perform_create(self, serializer):
        category_id = self.request.data.get('category_id')
        if not category_id:
            raise ValidationError("category_id is required")

        category = get_object_or_404(MenuCategory, id=category_id)
        restaurant = category.restaurant

        # Check ownership
        if restaurant.owner != self.request.user:
            raise PermissionDenied("You don't own this restaurant")

        # Check if restaurant is verified
        if not restaurant.is_verified:
            raise PermissionDenied("Your restaurant must be verified by admin before you can add menu items. Please wait for admin approval.")

        serializer.save(category=category)


# REVIEWS & RATINGS VIEWS
class RestaurantReviewViewSet(viewsets.ModelViewSet):
    queryset = RestaurantReview.objects.filter(is_approved=True)
    serializer_class = RestaurantReviewSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        restaurant_id = self.request.query_params.get('restaurant_id')
        if restaurant_id:
            return self.queryset.filter(restaurant_id=restaurant_id)

        # For restaurant owners, show all reviews for their restaurants
        user = self.request.user
        if user.role == 'restaurant':
            return RestaurantReview.objects.filter(
                restaurant__owner=user
            ).order_by('-created_at')

        return self.queryset.order_by('-created_at')

    def perform_create(self, serializer):
        restaurant_id = self.request.data.get('restaurant_id')
        if not restaurant_id:
            raise ValidationError("restaurant_id is required")
        restaurant = get_object_or_404(Restaurant, id=restaurant_id)

        # Check if customer has ordered from this restaurant
        from orders.models import Order
        has_ordered = Order.objects.filter(
            customer=self.request.user,
            restaurant=restaurant,
            status='delivered'
        ).exists()

        # Save review
        review = serializer.save(
            customer=self.request.user,
            restaurant=restaurant,
            is_verified=has_ordered
        )

        # Update restaurant rating
        restaurant.update_rating()

    def destroy(self, request, *args, **kwargs):
        review = self.get_object()

        # Only customer who wrote the review or restaurant owner can delete
        if request.user != review.customer and request.user != review.restaurant.owner:
            raise PermissionDenied("You don't have permission to delete this review")

        restaurant = review.restaurant
        response = super().destroy(request, *args, **kwargs)

        # Update restaurant rating after deletion
        restaurant.update_rating()
        return response

class ReviewResponseViewSet(viewsets.ModelViewSet):
    queryset = ReviewResponse.objects.all()
    serializer_class = ReviewResponseSerializer
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'put', 'patch', 'delete']

    def get_queryset(self):
        review_id = self.request.query_params.get('review_id')
        if review_id:
            return self.queryset.filter(review_id=review_id)
        return self.queryset

    def perform_create(self, serializer):
        review_id = self.request.data.get('review_id')
        if not review_id:
            raise ValidationError("review_id is required")
        review = get_object_or_404(RestaurantReview, id=review_id)

        # Only restaurant owner can respond to reviews
        if self.request.user != review.restaurant.owner:
            raise PermissionDenied("Only restaurant owner can respond to reviews")

        serializer.save(
            review=review,
            restaurant_owner=self.request.user
        )

    def perform_update(self, serializer):
        # Only the restaurant owner who created the response can update it
        if self.request.user != self.get_object().restaurant_owner:
            raise PermissionDenied("You can only update your own responses")

        serializer.save()

    def destroy(self, request, *args, **kwargs):
        response = self.get_object()

        # Only the restaurant owner who created the response can delete it
        if request.user != response.restaurant_owner:
            raise PermissionDenied("You can only delete your own responses")

        return super().destroy(request, *args, **kwargs)


# MENU ITEM VARIANTS & CUSTOMIZATIONS VIEWS
class MenuItemVariantViewSet(viewsets.ModelViewSet):
    queryset = MenuItemVariant.objects.all()
    serializer_class = MenuItemVariantSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        menu_item_id = self.request.query_params.get('menu_item_id')
        if menu_item_id:
            return self.queryset.filter(menu_item_id=menu_item_id)

        # For restaurant owners, show variants for their menu items only
        user = self.request.user
        if user.role == 'restaurant':
            return self.queryset.filter(menu_item__category__restaurant__owner=user)

        return self.queryset.filter(is_available=True)

    def perform_create(self, serializer):
        menu_item_id = self.request.data.get('menu_item_id')
        if not menu_item_id:
            raise ValidationError("menu_item_id is required")

        menu_item = get_object_or_404(MenuItem, id=menu_item_id)

        # Check ownership
        if self.request.user != menu_item.category.restaurant.owner:
            raise PermissionDenied("You don't own this menu item")

        serializer.save(menu_item=menu_item)

class MenuItemAddonViewSet(viewsets.ModelViewSet):
    queryset = MenuItemAddon.objects.all()
    serializer_class = MenuItemAddonSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        menu_item_id = self.request.query_params.get('menu_item_id')
        if menu_item_id:
            return self.queryset.filter(menu_item_id=menu_item_id)

        # For restaurant owners, show addons for their menu items only
        user = self.request.user
        if user.role == 'restaurant':
            return self.queryset.filter(menu_item__category__restaurant__owner=user)

        return self.queryset.filter(is_available=True)

    def perform_create(self, serializer):
        menu_item_id = self.request.data.get('menu_item_id')
        if not menu_item_id:
            raise ValidationError("menu_item_id is required")

        menu_item = get_object_or_404(MenuItem, id=menu_item_id)

        # Check ownership
        if self.request.user != menu_item.category.restaurant.owner:
            raise PermissionDenied("You don't own this menu item")

        serializer.save(menu_item=menu_item)

class MenuItemCustomizationGroupViewSet(viewsets.ModelViewSet):
    queryset = MenuItemCustomizationGroup.objects.all()
    serializer_class = MenuItemCustomizationGroupSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        menu_item_id = self.request.query_params.get('menu_item_id')
        if menu_item_id:
            return self.queryset.filter(menu_item_id=menu_item_id)

        # For restaurant owners, show groups for their menu items only
        user = self.request.user
        if user.role == 'restaurant':
            return self.queryset.filter(menu_item__category__restaurant__owner=user)

        return self.queryset

    def perform_create(self, serializer):
        menu_item_id = self.request.data.get('menu_item_id')
        if not menu_item_id:
            raise ValidationError("menu_item_id is required")

        menu_item = get_object_or_404(MenuItem, id=menu_item_id)

        # Check ownership
        if self.request.user != menu_item.category.restaurant.owner:
            raise PermissionDenied("You don't own this menu item")

        serializer.save(menu_item=menu_item)

class MenuItemCustomizationOptionViewSet(viewsets.ModelViewSet):
    queryset = MenuItemCustomizationOption.objects.all()
    serializer_class = MenuItemCustomizationOptionSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        group_id = self.request.query_params.get('group_id')
        if group_id:
            return self.queryset.filter(group_id=group_id)

        # For restaurant owners, show options for their menu items only
        user = self.request.user
        if user.role == 'restaurant':
            return self.queryset.filter(group__menu_item__category__restaurant__owner=user)

        return self.queryset.filter(is_available=True)

    def perform_create(self, serializer):
        group_id = self.request.data.get('group_id')
        if not group_id:
            raise ValidationError("group_id is required")

        group = get_object_or_404(MenuItemCustomizationGroup, id=group_id)

        # Check ownership
        if self.request.user != group.menu_item.category.restaurant.owner:
            raise PermissionDenied("You don't own this customization group")

        serializer.save(group=group)


# RESTAURANT BUSINESS DETAILS VIEWS
class CuisineTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only viewset for cuisine types"""
    queryset = CuisineType.objects.filter(is_active=True)
    serializer_class = CuisineTypeSerializer
    permission_classes = []  # Make public since it's reference data

class RestaurantCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """Read-only viewset for restaurant categories"""
    queryset = RestaurantCategory.objects.filter(is_active=True)
    serializer_class = RestaurantCategorySerializer
    permission_classes = [IsAuthenticated]

class ServiceAreaViewSet(viewsets.ModelViewSet):
    queryset = ServiceArea.objects.all()
    serializer_class = ServiceAreaSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        restaurant_id = self.request.query_params.get('restaurant_id')
        if restaurant_id:
            return self.queryset.filter(restaurant_id=restaurant_id)

        # For restaurant owners, show service areas for their restaurants only
        user = self.request.user
        if user.role == 'restaurant':
            return self.queryset.filter(restaurant__owner=user)

        return self.queryset.filter(is_active=True)

    def perform_create(self, serializer):
        restaurant_id = self.request.data.get('restaurant_id')
        if not restaurant_id:
            raise ValidationError("restaurant_id is required")

        restaurant = get_object_or_404(Restaurant, id=restaurant_id)

        # Check ownership
        if self.request.user != restaurant.owner:
            raise PermissionDenied("You don't own this restaurant")

        serializer.save(restaurant=restaurant)


# PROMOTIONS & DISCOUNTS VIEWS
class PromotionViewSet(viewsets.ModelViewSet):
    queryset = Promotion.objects.all()
    serializer_class = PromotionSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        if self.action == 'create':
            return PromotionCreateSerializer
        return PromotionSerializer

    def get_queryset(self):
        restaurant_id = self.request.query_params.get('restaurant_id')
        if restaurant_id:
            return self.queryset.filter(restaurant_id=restaurant_id)

        # For restaurant owners, show promotions for their restaurants only
        user = self.request.user
        if user.role == 'restaurant':
            return self.queryset.filter(restaurant__owner=user)

        # For customers, show only active and valid promotions
        if user.role == 'customer':
            from django.utils import timezone
            now = timezone.now()
            return self.queryset.filter(
                is_active=True,
                start_date__lte=now,
                end_date__gte=now
            )

        return self.queryset

    def perform_create(self, serializer):
        restaurant_id = self.request.data.get('restaurant_id')
        if not restaurant_id:
            raise ValidationError("restaurant_id is required")

        restaurant = get_object_or_404(Restaurant, id=restaurant_id)

        # Check ownership
        if self.request.user != restaurant.owner:
            raise PermissionDenied("You don't own this restaurant")

        serializer.save(restaurant=restaurant)

    @action(detail=True, methods=['post'])
    def toggle_active(self, request, pk=None):
        """Toggle promotion active status"""
        promotion = self.get_object()

        # Check ownership
        if request.user != promotion.restaurant.owner:
            raise PermissionDenied("You don't own this promotion")

        promotion.is_active = not promotion.is_active
        promotion.save()

        return Response({
            'status': 'success',
            'is_active': promotion.is_active,
            'message': f"Promotion {'activated' if promotion.is_active else 'deactivated'}"
        })

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured promotions"""
        featured_promotions = self.get_queryset().filter(is_featured=True, is_active=True)
        serializer = self.get_serializer(featured_promotions, many=True)
        return Response(serializer.data)

class PromotionCodeViewSet(viewsets.ModelViewSet):
    queryset = PromotionCode.objects.all()
    serializer_class = PromotionCodeSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        promotion_id = self.request.query_params.get('promotion_id')
        if promotion_id:
            return self.queryset.filter(promotion_id=promotion_id)

        # For restaurant owners, show codes for their promotions only
        user = self.request.user
        if user.role == 'restaurant':
            return self.queryset.filter(promotion__restaurant__owner=user)

        return self.queryset.filter(is_active=True)

    def perform_create(self, serializer):
        promotion_id = self.request.data.get('promotion_id')
        if not promotion_id:
            raise ValidationError("promotion_id is required")

        promotion = get_object_or_404(Promotion, id=promotion_id)

        # Check ownership
        if self.request.user != promotion.restaurant.owner:
            raise PermissionDenied("You don't own this promotion")

        serializer.save(promotion=promotion)

    @action(detail=False, methods=['post'])
    def validate_code(self, request):
        """Validate a promotion code"""
        code = request.data.get('code', '').upper().strip()
        if not code:
            return Response({'error': 'Code is required'}, status=400)

        try:
            promotion_code = PromotionCode.objects.get(code=code, is_active=True)

            if promotion_code.can_be_used(request.user):
                serializer = PromotionSerializer(promotion_code.promotion)
                return Response({
                    'valid': True,
                    'promotion': serializer.data,
                    'code': promotion_code.code
                })
            else:
                return Response({
                    'valid': False,
                    'error': 'Code cannot be used (expired, usage limit reached, or other restrictions)'
                })

        except PromotionCode.DoesNotExist:
            return Response({
                'valid': False,
                'error': 'Invalid promotion code'
            })


class AddressViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing user addresses
    """
    serializer_class = AddressSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Return addresses for the current user only"""
        return Address.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        """Set the user to the current user when creating an address"""
        serializer.save(user=self.request.user)