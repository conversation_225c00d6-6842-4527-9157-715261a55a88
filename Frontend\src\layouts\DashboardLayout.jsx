import React, { useState } from "react";
import { Outlet, NavLink, useNavigate } from "react-router-dom";
import RatingNotifications from "../components/rating/RatingNotifications";
import {
  Home,
  Menu,
  ShoppingBag,
  Settings,
  LogOut,
  Users,
  CircleDollarSign,
  Package,
  LayoutDashboard,
  ChevronRight,
  ChevronLeft,
  CheckCircle,
  BarChart2,
  Store,
  ClipboardCheck,
  BarChart3,
  Bell,
  Truck,
  Zap,
  Monitor,
  Activity,
  Shield,
  FileText,
  Database,
  Star,
} from "lucide-react";
import { useAuth } from "../context/AuthContext";
import { useAdmin } from "../context/AdminContext";
import Button from "../components/common/Button";

import { useSiteName } from "../hooks/useConfig";

const DashboardLayout = ({ userType = "restaurant" }) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  // Use admin context only for admin users
  const isAdmin = user?.role === "admin";
  const adminContext = isAdmin ? useAdmin() : null;
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showRatingNotifications, setShowRatingNotifications] = useState(false);
  const { value: siteName } = useSiteName();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  const handleNotificationClick = () => {
    setShowNotifications(!showNotifications);
  };

  // Mock notifications based on user type
  const getNotifications = () => {
    switch (userType) {
      case "admin":
        return [
          {
            id: 1,
            title: "New Restaurant Registration",
            message:
              "Kabul Kitchen has submitted their registration for approval",
            time: "5 minutes ago",
            type: "approval",
            unread: true,
          },
          {
            id: 2,
            title: "High Order Volume",
            message: "Order volume increased by 25% today",
            time: "1 hour ago",
            type: "info",
            unread: true,
          },
          {
            id: 3,
            title: "System Update",
            message: "Platform maintenance completed successfully",
            time: "2 hours ago",
            type: "success",
            unread: false,
          },
        ];
      case "restaurant":
        return [
          {
            id: 1,
            title: "New Order Received",
            message: "Order #1234 from Ahmad Khan - $24.50",
            time: "2 minutes ago",
            type: "order",
            unread: true,
          },
          {
            id: 2,
            title: "Menu Item Low Stock",
            message: "Kabuli Pulao is running low in stock",
            time: "30 minutes ago",
            type: "warning",
            unread: true,
          },
        ];
      case "delivery":
        return [
          {
            id: 1,
            title: "New Delivery Assignment",
            message: "Order #1234 assigned for delivery",
            time: "1 minute ago",
            type: "delivery",
            unread: true,
          },
          {
            id: 2,
            title: "Payment Received",
            message: "Cash payment of $24.50 confirmed",
            time: "15 minutes ago",
            type: "payment",
            unread: false,
          },
        ];
      default:
        return [];
    }
  };

  const notifications = getNotifications();
  const unreadCount = notifications.filter((n) => n.unread).length;

  const getNavItems = () => {
    switch (userType) {
      case "restaurant":
        return [
          {
            path: "/restaurant",
            icon: <LayoutDashboard size={20} />,
            label: "Dashboard",
          },
          {
            path: "/restaurant/my-restaurants",
            icon: <Store size={20} />,
            label: "My Restaurants",
          },
          {
            path: "/restaurant/menu",
            icon: <Menu size={20} />,
            label: "Menu Management",
          },
          {
            path: "/restaurant/menu-categories",
            icon: <Store size={20} />,
            label: "Menu Categories",
          },
          {
            path: "/restaurant/orders",
            icon: <ShoppingBag size={20} />,
            label: "Orders",
          },
          {
            path: "/restaurant/order-management",
            icon: <Monitor size={20} />,
            label: "Order Management",
          },
          {
            path: "/restaurant/ratings",
            icon: <Star size={20} />,
            label: "Ratings & Reviews",
          },
          {
            path: "/restaurant/profile",
            icon: <Settings size={20} />,
            label: "Restaurant Profile",
          },
        ];
      case "admin":
        return [
          {
            path: "/admin/dashboard",
            icon: <LayoutDashboard size={20} />,
            label: "Dashboard",
          },
          {
            path: "/admin/users",
            icon: <Users size={20} />,
            label: "User Management",
          },
          {
            path: "/admin/restaurants",
            icon: <Store size={20} />,
            label: "Restaurant Management",
          },
          {
            path: "/admin/restaurant-approvals",
            icon: <ClipboardCheck size={20} />,
            label: "Restaurant Approvals",
          },
          {
            path: "/admin/orders",
            icon: <ShoppingBag size={20} />,
            label: "Order Management",
          },
          {
            path: "/admin/employees",
            icon: <Users size={20} />,
            label: "Employee Management",
          },
          {
            path: "/admin/order-assignments",
            icon: <Truck size={20} />,
            label: "Order Assignments",
          },
          {
            path: "/admin/delivery-agents",
            icon: <Truck size={20} />,
            label: "Legacy: Delivery Agents",
          },
          {
            path: "/admin/delivery-agent-approvals",
            icon: <Shield size={20} />,
            label: "Legacy: Agent Approvals",
            badge: true,
            badgeCount: adminContext?.pendingDeliveryApprovals || 0,
          },
          {
            path: "/admin/analytics",
            icon: <BarChart3 size={20} />,
            label: "Analytics",
          },
          {
            path: "/admin/realtime-demo",
            icon: <Zap size={20} />,
            label: "Real-time Demo",
          },
          {
            path: "/admin/api-test",
            icon: <Monitor size={20} />,
            label: "API Test",
          },
          {
            path: "/admin/restaurant-test",
            icon: <Store size={20} />,
            label: "Restaurant Test",
          },
          {
            path: "/admin/auth-api-test",
            icon: <Shield size={20} />,
            label: "Auth API Test",
          },
          {
            path: "/admin/reports",
            icon: <FileText size={20} />,
            label: "Reports",
          },
          {
            path: "/admin/settings",
            icon: <Settings size={20} />,
            label: "System Settings",
          },
          {
            path: "/admin/notifications",
            icon: <Bell size={20} />,
            label: "Notifications",
          },
          {
            path: "/admin/backup",
            icon: <Database size={20} />,
            label: "Backup & Restore",
          },
          {
            path: "/admin/logs",
            icon: <FileText size={20} />,
            label: "System Logs",
          },
        ];
      case "delivery":
        return [
          {
            path: "/delivery",
            icon: <LayoutDashboard size={20} />,
            label: "Dashboard",
          },
          {
            path: "/delivery/orders",
            icon: <Package size={20} />,
            label: "Deliveries",
          },
          {
            path: "/delivery/cash-collection",
            icon: <CircleDollarSign size={20} />,
            label: "Cash Collection",
          },
          {
            path: "/delivery/earnings",
            icon: <BarChart2 size={20} />,
            label: "Earnings",
          },
          {
            path: "/delivery/profile",
            icon: <Settings size={20} />,
            label: "Profile",
          },
        ];
      default:
        return [];
    }
  };

  const navItems = getNavItems();

  const getTitle = () => {
    switch (userType) {
      case "restaurant":
        return "Restaurant Dashboard";
      case "admin":
        return "Admin Dashboard";
      case "delivery":
        return "Delivery Dashboard";
      default:
        return "Dashboard";
    }
  };

  return (
    <div
      className={`min-h-screen flex ${
        userType === "admin" || userType === "delivery"
          ? "bg-gradient-to-br from-primary-50 via-orange-50 to-primary-100"
          : "bg-gray-50"
      }`}
    >
      {/* Sidebar */}
      <aside
        className={`bg-white shadow-lg transition-all duration-300 flex flex-col ${
          sidebarCollapsed ? "w-20" : "w-72"
        } fixed top-0 left-0 h-screen z-30`}
      >
        {/* Logo */}
        <div
          className={`p-6 border-b border-gray-100 flex ${
            sidebarCollapsed ? "justify-center" : "justify-between"
          } items-center`}
        >
          {!sidebarCollapsed && (
            <span className='text-primary-600 font-poppins font-bold text-2xl'>
              {siteName || "Afghan Sufra"}
            </span>
          )}
          <button
            className='p-1.5 rounded-lg text-gray-500 hover:bg-gray-100 hover:text-primary-600 transition-colors duration-200'
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          >
            {sidebarCollapsed ? (
              <ChevronRight size={20} />
            ) : (
              <ChevronLeft size={20} />
            )}
          </button>
        </div>

        {/* User info */}
        <div
          className={`p-6 border-b border-gray-100 ${
            sidebarCollapsed ? "items-center" : ""
          } flex`}
        >
          <div className='w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center overflow-hidden mr-4 ring-2 ring-primary-100'>
            {user?.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className='w-full h-full object-cover'
              />
            ) : (
              <Users size={24} className='text-primary-600' />
            )}
          </div>
          {!sidebarCollapsed && (
            <div className='flex flex-col'>
              <span className='font-semibold text-gray-900 text-lg'>
                {user?.name}
              </span>
              <span className='text-sm text-gray-500 capitalize'>
                {user?.role}
              </span>
            </div>
          )}
        </div>

        {/* Navigation */}
        <nav className='flex-grow py-6 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400'>
          <ul className='space-y-2 px-3 pb-4'>
            {navItems.map((item) => (
              <li key={item.path}>
                <NavLink
                  to={item.path}
                  end={item.path.split("/").length <= 2}
                  className={({ isActive }) => `
                    flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200
                    ${
                      isActive
                        ? "text-primary-600 bg-primary-50"
                        : "text-gray-600 hover:bg-gray-50 hover:text-primary-600"
                    }
                    ${sidebarCollapsed ? "justify-center" : ""}
                  `}
                >
                  <span className='flex items-center justify-between w-full'>
                    <span className='flex items-center'>
                      {item.icon}
                      {!sidebarCollapsed && (
                        <span className='ml-3'>{item.label}</span>
                      )}
                    </span>
                    {item.badge && item.badgeCount > 0 && !sidebarCollapsed && (
                      <span className='bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full min-w-[20px] text-center'>
                        {item.badgeCount}
                      </span>
                    )}
                  </span>
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>

        {/* Logout */}
        <div className='p-6 mt-auto border-t border-gray-100'>
          <Button
            variant='outline'
            size='medium'
            fullWidth
            icon={<LogOut size={18} />}
            iconPosition={sidebarCollapsed ? "center" : "left"}
            onClick={handleLogout}
            className={`${
              sidebarCollapsed ? "justify-center px-0" : ""
            } hover:bg-red-50 hover:text-red-600 hover:border-red-200 transition-colors duration-200`}
          >
            {!sidebarCollapsed && "Logout"}
          </Button>
        </div>
      </aside>

      {/* Main content */}
      <div
        className={`flex-grow transition-all duration-300 ${
          sidebarCollapsed ? "ml-20" : "ml-72"
        }
        `}
      >
        {/* Top bar */}
        <header className='bg-white shadow-sm sticky top-0 z-20'>
          <div className='px-8 py-4 flex items-center justify-between'>
            <h1 className='text-2xl font-bold text-gray-900'>{getTitle()}</h1>
            <div className='flex items-center space-x-4 relative'>
              {/* Go Home Button */}
              <Button
                variant='outline'
                size='small'
                onClick={() => navigate("/")}
                className='flex items-center space-x-2 text-gray-600 border-gray-300 hover:bg-gray-50 hover:text-primary-600 hover:border-primary-300 transition-colors duration-200'
                title='Go to Home Page'
              >
                <Home size={16} />
                <span className='hidden sm:inline'>Go Home</span>
              </Button>
              {/* Rating Notifications for Restaurant Users */}
              {user?.role === "restaurant" && (
                <button
                  className='p-2 rounded-lg text-gray-500 hover:bg-gray-100 hover:text-yellow-600 transition-colors duration-200 relative'
                  onClick={() => setShowRatingNotifications(true)}
                  title='Rating Notifications'
                >
                  <Star size={20} />
                  <span className='absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full'></span>
                </button>
              )}

              <button
                className='p-2 rounded-lg text-gray-500 hover:bg-gray-100 hover:text-primary-600 transition-colors duration-200 relative'
                onClick={handleNotificationClick}
              >
                <Bell size={20} />
                {unreadCount > 0 && (
                  <span className='absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center'>
                    {unreadCount}
                  </span>
                )}
              </button>

              {/* Notifications Dropdown */}
              {showNotifications && (
                <div className='absolute top-12 right-0 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50'>
                  <div className='p-4 border-b border-gray-100'>
                    <div className='flex items-center justify-between'>
                      <h3 className='text-lg font-semibold text-gray-900'>
                        Notifications
                      </h3>
                      <span className='text-sm text-gray-500'>
                        {unreadCount} unread
                      </span>
                    </div>
                  </div>
                  <div className='max-h-96 overflow-y-auto'>
                    {notifications.length > 0 ? (
                      notifications.map((notification) => (
                        <div
                          key={notification.id}
                          className={`p-4 border-b border-gray-50 hover:bg-gray-50 cursor-pointer ${
                            notification.unread ? "bg-blue-50" : ""
                          }`}
                        >
                          <div className='flex items-start space-x-3'>
                            <div
                              className={`w-2 h-2 rounded-full mt-2 ${
                                notification.unread
                                  ? "bg-blue-500"
                                  : "bg-gray-300"
                              }`}
                            ></div>
                            <div className='flex-1'>
                              <h4 className='text-sm font-medium text-gray-900'>
                                {notification.title}
                              </h4>
                              <p className='text-sm text-gray-600 mt-1'>
                                {notification.message}
                              </p>
                              <p className='text-xs text-gray-400 mt-2'>
                                {notification.time}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className='p-8 text-center text-gray-500'>
                        <Bell
                          size={48}
                          className='mx-auto text-gray-300 mb-4'
                        />
                        <p>No notifications</p>
                      </div>
                    )}
                  </div>
                  {notifications.length > 0 && (
                    <div className='p-4 border-t border-gray-100'>
                      <button className='w-full text-center text-sm text-primary-600 hover:text-primary-700 font-medium'>
                        Mark all as read
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className='p-8 w-full'>
          <Outlet />
        </main>
      </div>

      {/* Rating Notifications Modal */}
      {showRatingNotifications && (
        <RatingNotifications
          onClose={() => setShowRatingNotifications(false)}
        />
      )}
    </div>
  );
};

export default DashboardLayout;
