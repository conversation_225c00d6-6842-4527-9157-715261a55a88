import React from "react";
import { Link, useLocation } from "react-router-dom";
import { CheckCircle, Home, Store, Mail, Plus } from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";

const RegistrationSuccess = () => {
  const location = useLocation();
  const { restaurant, message } = location.state || {};

  return (
    <div className='container mx-auto px-4 py-12 max-w-2xl animate-fade-in'>
      <Card className='text-center p-8'>
        <div className='w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6'>
          <CheckCircle size={40} className='text-green-600' />
        </div>

        <h1 className='text-2xl font-bold text-gray-900 mb-4'>
          {restaurant
            ? "Restaurant Added Successfully!"
            : "Restaurant Application Submitted!"}
        </h1>

        <p className='text-gray-600 mb-6'>
          {message ||
            "Thank you for applying to join Afghan Sofra as a restaurant partner. Our team will review your application and get back to you within 1-3 business days."}
        </p>

        <div className='bg-blue-50 p-4 rounded-lg mb-6'>
          <h2 className='font-medium text-blue-800 mb-2'>What happens next?</h2>
          <ol className='text-left text-blue-700 space-y-2'>
            <li className='flex items-start'>
              <span className='inline-flex items-center justify-center w-6 h-6 bg-blue-200 rounded-full text-blue-800 font-medium mr-2 flex-shrink-0'>
                1
              </span>
              <span>Our team will review your restaurant details</span>
            </li>
            <li className='flex items-start'>
              <span className='inline-flex items-center justify-center w-6 h-6 bg-blue-200 rounded-full text-blue-800 font-medium mr-2 flex-shrink-0'>
                2
              </span>
              <span>
                You'll receive an email notification about your application
                status
              </span>
            </li>
            <li className='flex items-start'>
              <span className='inline-flex items-center justify-center w-6 h-6 bg-blue-200 rounded-full text-blue-800 font-medium mr-2 flex-shrink-0'>
                3
              </span>
              <span>
                Once approved, you can set up your menu and start receiving
                orders
              </span>
            </li>
          </ol>
        </div>

        <div className='flex flex-col sm:flex-row gap-4 mb-6'>
          <Button
            variant='primary'
            fullWidth
            icon={<Store size={18} />}
            to='/restaurant'
          >
            Go to Dashboard
          </Button>

          <Button
            variant='outline'
            fullWidth
            onClick={() => (window.location.href = "http://localhost:5173")}
          >
            Return to Home Page
          </Button>

          {restaurant && (
            <Button
              variant='outline'
              fullWidth
              icon={<Plus size={18} />}
              to='/add-restaurant'
            >
              Add Another Restaurant
            </Button>
          )}

          <Button
            variant='outline'
            fullWidth
            icon={<Mail size={18} />}
            to='/contact'
          >
            Contact Support
          </Button>
        </div>

        <p className='text-sm text-text-secondary'>
          Have questions? Visit our{" "}
          <Link to='/faq' className='text-primary-500 hover:text-primary-600'>
            FAQ
          </Link>{" "}
          or{" "}
          <Link
            to='/contact'
            className='text-primary-500 hover:text-primary-600'
          >
            contact our support team
          </Link>
          .
        </p>
      </Card>
    </div>
  );
};

export default RegistrationSuccess;
