// Frontend/src/config/api.js
export const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000/api";

// API endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: "/auth/login/",
    REGISTER: "/auth/register/",
    VERIFY_EMAIL: "/auth/verify-email/",
    CHANGE_PASSWORD: "/auth/change-password/",
  },

  // Configuration
  CONFIG: {
    PUBLIC: "/config/settings/public_config/",
    SETTINGS: "/config/settings/",
    CHOICE_OPTIONS: "/config/choice-options/",
    CHOICE_OPTIONS_BY_TYPE: "/config/choice-options/by_type/",
    FILTER_CONFIGS: "/config/filter-configs/",
  },

  // Restaurant
  RESTAURANT: {
    LIST: "/restaurant/restaurants/",
    DETAIL: (id) => `/restaurant/restaurants/${id}/`,
    MENU_CATEGORIES: "/restaurant/menu-categories/",
    MENU_ITEMS: "/restaurant/menu-items/",
  },

  // Orders
  ORDER: {
    LIST: "/order/orders/",
    DETAIL: (id) => `/order/orders/${id}/`,
    CART: "/order/carts/mine/",
    CART_DESTROY: "/order/carts/destroy/",
  },
};

// Default headers
export const DEFAULT_HEADERS = {
  "Content-Type": "application/json",
};

// Get auth headers
export const getAuthHeaders = () => {
  const token = localStorage.getItem("token");
  return token ? { Authorization: `Bearer ${token}` } : {};
};

// API helper function
export const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  const headers = {
    ...DEFAULT_HEADERS,
    ...getAuthHeaders(),
    ...options.headers,
  };

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
};
