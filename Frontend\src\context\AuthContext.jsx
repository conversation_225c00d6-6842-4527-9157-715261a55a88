import { createContext, useContext, useState, useEffect } from "react";
import { authApi } from "../utils/authApi";

const AuthContext = createContext(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Initialize authentication state from localStorage
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const storedUser = localStorage.getItem("afghanSofraUser");

        if (storedUser && storedUser !== "undefined" && storedUser !== "null") {
          const parsedUser = JSON.parse(storedUser);

          // Validate user data - must have either ID or access_token
          const isValidUser =
            parsedUser &&
            (parsedUser.id || parsedUser.user_id || parsedUser.access_token);

          if (isValidUser) {
            setUser(parsedUser);
          } else {
            localStorage.removeItem("afghanSofraUser");
          }
        }
      } catch (error) {
        console.error("❌ Error parsing stored user:", error);
        localStorage.removeItem("afghanSofraUser");
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (userNameOrEmail, password) => {
    try {
      setLoading(true);
      console.log("🔐 Attempting login for:", userNameOrEmail);

      const result = await authApi.login({
        user_name: userNameOrEmail,
        password: password,
      });

      if (result.success) {
        let userData = result.data;

        // Handle different API response structures
        if (userData.data && userData.data.user) {
          // Backend returns: { success: true, data: { user: {...}, access_token: "..." } }
          userData = {
            ...userData.data.user,
            access_token: userData.data.access_token,
            refresh_token: userData.data.refresh_token,
          };
        } else if (userData.user && typeof userData.user === "object") {
          // If API returns nested user object
          userData = {
            ...userData.user,
            access_token: userData.access_token,
            refresh_token: userData.refresh_token,
          };
        }

        // Store user data
        setUser(userData);
        localStorage.setItem("afghanSofraUser", JSON.stringify(userData));

        return { success: true, user: userData };
      } else {
        console.log("❌ Login failed:", result.error);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error("❌ Login error:", error);
      return { success: false, error: "Network error. Please try again." };
    } finally {
      setLoading(false);
    }
  };

  // Registration function
  const register = async (userData) => {
    try {
      setLoading(true);
      console.log("🚀 Starting registration for:", userData.email);

      // Prepare registration data
      const registrationData = {
        name: userData.name,
        user_name: userData.user_name || userData.username,
        phone: userData.phone,
        email: userData.email,
        password: userData.password,
        confirm_password: userData.confirm_password, // Include confirm_password!
        role: userData.role || "customer",
      };

      const result = await authApi.register(registrationData);

      if (result.success) {
        console.log("✅ Registration successful, OTP sent to email");
        return {
          success: true,
          data: result.data,
          message: result.message,
          requiresVerification: true,
          userEmail: registrationData.email,
        };
      } else {
        console.log("❌ Registration failed:", result.error);
        return {
          success: false,
          error: result.error,
          details: result.details,
          errors: result.errors, // Pass through specific field errors
        };
      }
    } catch (error) {
      console.error("❌ Registration error:", error);
      return {
        success: false,
        error: "Network error. Please check your connection and try again.",
      };
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    console.log("🚪 Logging out user");
    setUser(null);
    localStorage.removeItem("afghanSofraUser");
  };

  // Update user profile
  const updateProfile = (updatedData) => {
    const updatedUser = { ...user, ...updatedData };
    setUser(updatedUser);
    localStorage.setItem("afghanSofraUser", JSON.stringify(updatedUser));
    return { success: true, user: updatedUser };
  };

  // Email verification with OTP
  const verifyEmail = async (email, otp) => {
    try {
      setLoading(true);
      console.log("🔐 Verifying email:", email);

      const result = await authApi.verifyEmail({ email, otp });

      if (result.success) {
        console.log("✅ Email verification successful");
        return {
          success: true,
          message: "Email verified successfully. You can now login.",
        };
      } else {
        console.log("❌ Email verification failed:", result.error);
        return {
          success: false,
          error: result.error,
        };
      }
    } catch (error) {
      console.error("❌ Email verification error:", error);
      return {
        success: false,
        error: "Network error. Please try again.",
      };
    } finally {
      setLoading(false);
    }
  };

  // Resend email verification OTP
  const resendEmailVerification = async (email) => {
    try {
      setLoading(true);
      console.log("📧 Resending verification code to:", email);

      const result = await authApi.resendEmailVerification({ email });

      if (result.success) {
        console.log("✅ Verification code resent successfully");
        return {
          success: true,
          message: "Verification code sent to your email.",
        };
      } else {
        console.log("❌ Failed to resend verification code:", result.error);
        return {
          success: false,
          error: result.error,
        };
      }
    } catch (error) {
      console.error("❌ Resend verification error:", error);
      return {
        success: false,
        error: "Network error. Please try again.",
      };
    } finally {
      setLoading(false);
    }
  };

  // Request password reset
  const requestPasswordReset = async (email) => {
    try {
      setLoading(true);
      console.log("🔑 Requesting password reset for:", email);

      const result = await authApi.requestPasswordReset({ email });

      if (result.success) {
        console.log("✅ Password reset OTP sent");
        return {
          success: true,
          message: "Password reset OTP sent to your email.",
        };
      } else {
        console.log("❌ Failed to send password reset OTP:", result.error);
        return {
          success: false,
          error: result.error,
        };
      }
    } catch (error) {
      console.error("❌ Password reset request error:", error);
      return {
        success: false,
        error: "Network error. Please try again.",
      };
    } finally {
      setLoading(false);
    }
  };

  // Reset password with OTP
  const resetPassword = async (email, oldPassword, newPassword, otp) => {
    try {
      setLoading(true);
      console.log("🔑 Resetting password for:", email);

      const result = await authApi.resetPassword({
        email,
        old_password: oldPassword,
        new_password: newPassword,
        otp,
      });

      if (result.success) {
        console.log("✅ Password reset successful");
        return {
          success: true,
          message: "Password reset successfully.",
        };
      } else {
        console.log("❌ Password reset failed:", result.error);
        return {
          success: false,
          error: result.error,
        };
      }
    } catch (error) {
      console.error("❌ Password reset error:", error);
      return {
        success: false,
        error: "Network error. Please try again.",
      };
    } finally {
      setLoading(false);
    }
  };

  // Change password from profile (without OTP)
  const changePassword = async (currentPassword, newPassword) => {
    try {
      setLoading(true);

      if (!user || !user.email) {
        return {
          success: false,
          error: "User not found. Please login again.",
        };
      }

      console.log("🔑 Changing password for:", user.email);

      const result = await authApi.changePassword({
        email: user.email,
        current_password: currentPassword,
        new_password: newPassword,
      });

      if (result.success) {
        console.log("✅ Password changed successfully");
        return {
          success: true,
          message: "Password changed successfully.",
        };
      } else {
        console.log("❌ Password change failed:", result.error);
        return {
          success: false,
          error: result.error,
        };
      }
    } catch (error) {
      console.error("❌ Password change error:", error);
      return {
        success: false,
        error: "Network error. Please try again.",
      };
    } finally {
      setLoading(false);
    }
  };

  // Check if user is authenticated
  const isAuthenticated = !!(
    user &&
    (user.id || user.user_id || user.access_token)
  );

  // Context value
  const value = {
    // State
    user,
    loading,
    isAuthenticated,

    // Authentication methods
    login,
    logout,
    register,

    // Email verification
    verifyEmail,
    resendEmailVerification,

    // Password management
    requestPasswordReset,
    resetPassword,
    changePassword,

    // Profile management
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
