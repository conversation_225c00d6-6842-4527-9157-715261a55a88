import axios from "axios";

// Get API base URL from environment variables
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000/api";

// Create axios instance for menu API
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");

    if (user.access_token) {
      config.headers.Authorization = `Bearer ${user.access_token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle authentication errors
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Clear invalid token and redirect to login
      localStorage.removeItem("afghanSofraUser");
      if (window.location.pathname !== "/login") {
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

/**
 * Menu Category API Functions
 */
export const menuCategoryApi = {
  /**
   * Create a new menu category
   * @param {Object} categoryData - Category data
   * @param {string} categoryData.name - Category name
   * @param {string} categoryData.description - Category description
   * @param {number} categoryData.restaurant - Restaurant ID
   * @returns {Promise} API response
   */
  async createCategory(categoryData) {
    try {
      // Ensure restaurant ID is a number
      const processedData = {
        ...categoryData,
        restaurant: parseInt(categoryData.restaurant, 10),
      };

      const response = await apiClient.post(
        "/restaurant/menu-categories/",
        processedData
      );

      return {
        success: true,
        data: response.data,
        message: "Menu category created successfully",
      };
    } catch (error) {
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.response?.data?.detail ||
          error.message ||
          "Failed to create menu category",
        details: error.response?.data,
        status: error.response?.status,
      };
    }
  },

  /**
   * Get all categories for a restaurant
   * @param {number} restaurantId - Restaurant ID
   * @returns {Promise} API response
   */
  async getCategories(restaurantId) {
    try {
      const response = await apiClient.get(
        `/restaurant/menu-categories/?restaurant_id=${restaurantId}`
      );

      return {
        success: true,
        data: response.data,
        message: "Categories retrieved successfully",
      };
    } catch (error) {
      console.error("Get categories error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve categories",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get a single category by ID
   * @param {number} categoryId - Category ID
   * @returns {Promise} API response
   */
  async getCategory(categoryId) {
    try {
      const response = await apiClient.get(
        `/restaurant/menu-categories/${categoryId}/`
      );

      return {
        success: true,
        data: response.data,
        message: "Category retrieved successfully",
      };
    } catch (error) {
      console.error("Get category error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve category",
        details: error.response?.data,
      };
    }
  },

  /**
   * Update a category
   * @param {number} categoryId - Category ID
   * @param {Object} updateData - Data to update
   * @returns {Promise} API response
   */
  async updateCategory(categoryId, updateData) {
    try {
      const response = await apiClient.patch(
        `/restaurant/menu-categories/${categoryId}/`,
        updateData
      );

      return {
        success: true,
        data: response.data,
        message: "Category updated successfully",
      };
    } catch (error) {
      console.error("Update category error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to update category",
        details: error.response?.data,
      };
    }
  },

  /**
   * Delete a category
   * @param {number} categoryId - Category ID
   * @returns {Promise} API response
   */
  async deleteCategory(categoryId) {
    try {
      await apiClient.delete(`/restaurant/menu-categories/${categoryId}/`);

      return {
        success: true,
        message: "Category deleted successfully",
      };
    } catch (error) {
      console.error("Delete category error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to delete category",
        details: error.response?.data,
      };
    }
  },
};

/**
 * Menu Item API Functions
 */
export const menuItemApi = {
  /**
   * Create a new menu item
   * @param {Object} itemData - Item data
   * @param {number} itemData.category - Category ID
   * @param {string} itemData.name - Item name
   * @param {number} itemData.price - Item price
   * @param {File} itemData.image - Item image file
   * @param {boolean} itemData.is_vegetarian - Is vegetarian
   * @param {boolean} itemData.is_available - Is available
   * @param {string} itemData.description - Item description
   * @param {number} itemData.preparation_time - Preparation time in minutes
   * @returns {Promise} API response
   */
  async createItem(itemData) {
    try {
      console.log("Creating menu item with data:", itemData);

      // Create FormData for multipart/form-data request
      const formData = new FormData();

      // Append all fields to FormData with proper validation
      formData.append("category_id", parseInt(itemData.category_id));
      formData.append("name", itemData.name);
      formData.append("price", parseFloat(itemData.price));
      formData.append(
        "is_vegetarian",
        itemData.is_vegetarian === true ? "true" : "false"
      );
      formData.append(
        "is_available",
        itemData.is_available === true ? "true" : "false"
      );
      formData.append("description", itemData.description);
      formData.append("preparation_time", parseInt(itemData.preparation_time));

      // Append image file if provided
      if (itemData.image && itemData.image instanceof File) {
        formData.append("image", itemData.image);
      }

      // Log FormData contents for debugging
      console.log("FormData contents:");
      for (let [key, value] of formData.entries()) {
        console.log(`${key}:`, value);
      }

      // Use the existing apiClient but modify headers for this request
      const response = await apiClient.post(
        "/restaurant/menu-items/",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      return {
        success: true,
        data: response.data,
        message: "Menu item created successfully",
      };
    } catch (error) {
      console.error("Create menu item error:", error);
      console.error("Error response:", error.response?.data);
      console.error("Error status:", error.response?.status);

      return {
        success: false,
        error:
          error.response?.data?.detail ||
          error.response?.data?.message ||
          error.response?.data?.error ||
          (error.response?.data && typeof error.response.data === "object"
            ? JSON.stringify(error.response.data)
            : error.response?.data) ||
          error.message ||
          "Failed to create menu item",
        details: error.response?.data,
        status: error.response?.status,
      };
    }
  },

  /**
   * Get all menu items by category
   * @param {number} categoryId - Category ID
   * @returns {Promise} API response
   */
  async getItemsByCategory(categoryId) {
    try {
      const response = await apiClient.get(
        `/restaurant/menu-items/?category_id=${categoryId}`
      );

      return {
        success: true,
        data: response.data,
        message: "Menu items retrieved successfully",
      };
    } catch (error) {
      console.error("Get menu items by category error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve menu items",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get all menu items by restaurant
   * @param {number} restaurantId - Restaurant ID
   * @returns {Promise} API response
   */
  async getItemsByRestaurant(restaurantId) {
    try {
      const response = await apiClient.get(
        `/restaurant/menu-items/?restaurant_id=${restaurantId}`
      );

      return {
        success: true,
        data: response.data,
        message: "Menu items retrieved successfully",
      };
    } catch (error) {
      console.error("Get menu items by restaurant error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve menu items",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get a single menu item by ID
   * @param {number} itemId - Item ID
   * @returns {Promise} API response
   */
  async getItem(itemId) {
    try {
      const response = await apiClient.get(`/restaurant/menu-items/${itemId}/`);

      return {
        success: true,
        data: response.data,
        message: "Menu item retrieved successfully",
      };
    } catch (error) {
      console.error("Get menu item error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve menu item",
        details: error.response?.data,
      };
    }
  },

  /**
   * Update a menu item (partial update)
   * @param {number} itemId - Item ID
   * @param {Object} updateData - Data to update
   * @returns {Promise} API response
   */
  async updateItem(itemId, updateData) {
    try {
      console.log("Updating menu item with data:", updateData);

      // Check if updateData is FormData (for image uploads) or regular object
      let requestData;
      let headers = {};

      if (updateData instanceof FormData) {
        requestData = updateData;
        headers["Content-Type"] = "multipart/form-data";

        // Log FormData contents for debugging
        console.log("FormData contents:");
        for (let [key, value] of updateData.entries()) {
          console.log(`${key}:`, value);
        }
      } else {
        requestData = updateData;
        headers["Content-Type"] = "application/json";
      }

      const response = await apiClient.patch(
        `/restaurant/menu-items/${itemId}/`,
        requestData,
        { headers }
      );

      return {
        success: true,
        data: response.data,
        message: "Menu item updated successfully",
      };
    } catch (error) {
      console.error("Update menu item error:", error);
      console.error("Error response:", error.response?.data);
      console.error("Error status:", error.response?.status);

      return {
        success: false,
        error:
          error.response?.data?.detail ||
          error.response?.data?.message ||
          error.response?.data?.error ||
          (error.response?.data && typeof error.response.data === "object"
            ? JSON.stringify(error.response.data)
            : error.response?.data) ||
          error.message ||
          "Failed to update menu item",
        details: error.response?.data,
        status: error.response?.status,
      };
    }
  },

  /**
   * Delete a menu item
   * @param {number} itemId - Item ID
   * @returns {Promise} API response
   */
  async deleteItem(itemId) {
    try {
      await apiClient.delete(`/restaurant/menu-items/${itemId}/`);

      return {
        success: true,
        message: "Menu item deleted successfully",
      };
    } catch (error) {
      console.error("Delete menu item error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to delete menu item",
        details: error.response?.data,
      };
    }
  },
};

export default menuCategoryApi;
