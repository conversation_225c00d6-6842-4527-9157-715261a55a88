import React, { useState } from 'react';
import { WifiOff, RefreshCw, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import Button from '../common/Button';
import Card from '../common/Card';

const NetworkError = ({ 
  error, 
  onRetry, 
  retryCount = 0, 
  maxRetries = 3,
  showRetryCount = true,
  className = "",
  variant = "card" // "card", "inline", "banner"
}) => {
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryAttempts, setRetryAttempts] = useState(retryCount);

  const handleRetry = async () => {
    if (retryAttempts >= maxRetries) return;
    
    setIsRetrying(true);
    setRetryAttempts(prev => prev + 1);
    
    try {
      await onRetry?.();
    } catch (err) {
      console.error('Retry failed:', err);
    } finally {
      setIsRetrying(false);
    }
  };

  const getErrorMessage = () => {
    if (!navigator.onLine) {
      return {
        title: "No Internet Connection",
        message: "Please check your internet connection and try again.",
        icon: WifiOff,
        color: "red"
      };
    }

    if (error?.status >= 500) {
      return {
        title: "Server Error",
        message: "Our servers are experiencing issues. Please try again in a moment.",
        icon: AlertCircle,
        color: "red"
      };
    }

    if (error?.status === 404) {
      return {
        title: "Not Found",
        message: "The requested resource could not be found.",
        icon: AlertCircle,
        color: "orange"
      };
    }

    if (error?.status === 403) {
      return {
        title: "Access Denied",
        message: "You don't have permission to access this resource.",
        icon: AlertCircle,
        color: "red"
      };
    }

    if (error?.status === 429) {
      return {
        title: "Too Many Requests",
        message: "Please wait a moment before trying again.",
        icon: Clock,
        color: "orange"
      };
    }

    return {
      title: "Connection Error",
      message: "Unable to connect to our servers. Please check your connection and try again.",
      icon: WifiOff,
      color: "red"
    };
  };

  const errorInfo = getErrorMessage();
  const IconComponent = errorInfo.icon;
  const canRetry = retryAttempts < maxRetries && onRetry;

  // Banner variant (top of page)
  if (variant === "banner") {
    return (
      <div className={`bg-red-50 border-l-4 border-red-400 p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <IconComponent size={20} className="text-red-400 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-red-800">{errorInfo.title}</h3>
              <p className="text-sm text-red-700">{errorInfo.message}</p>
            </div>
          </div>
          {canRetry && (
            <Button
              variant="outline"
              size="small"
              onClick={handleRetry}
              loading={isRetrying}
              icon={<RefreshCw size={14} />}
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              Retry
            </Button>
          )}
        </div>
      </div>
    );
  }

  // Inline variant (within content)
  if (variant === "inline") {
    return (
      <div className={`flex items-center justify-between p-3 bg-gray-50 rounded-lg border ${className}`}>
        <div className="flex items-center">
          <IconComponent size={18} className={`text-${errorInfo.color}-500 mr-2`} />
          <span className="text-sm text-gray-700">{errorInfo.message}</span>
        </div>
        {canRetry && (
          <Button
            variant="ghost"
            size="small"
            onClick={handleRetry}
            loading={isRetrying}
            icon={<RefreshCw size={14} />}
          >
            Retry
          </Button>
        )}
      </div>
    );
  }

  // Card variant (default)
  return (
    <Card className={`p-6 text-center border-red-200 bg-red-50 ${className}`}>
      <div className={`w-16 h-16 bg-${errorInfo.color}-100 rounded-full flex items-center justify-center mx-auto mb-4`}>
        <IconComponent size={32} className={`text-${errorInfo.color}-600`} />
      </div>
      
      <h3 className={`font-semibold text-${errorInfo.color}-800 mb-2`}>
        {errorInfo.title}
      </h3>
      
      <p className={`text-${errorInfo.color}-600 mb-4`}>
        {errorInfo.message}
      </p>

      {/* Error details */}
      {error && (
        <div className="bg-white rounded-lg p-3 mb-4 text-left">
          <div className="text-xs text-gray-500 space-y-1">
            {error.status && (
              <div>Status: <span className="font-mono">{error.status}</span></div>
            )}
            {error.statusText && (
              <div>Error: <span className="font-mono">{error.statusText}</span></div>
            )}
            <div>Time: <span className="font-mono">{new Date().toLocaleTimeString()}</span></div>
          </div>
        </div>
      )}

      {/* Retry section */}
      <div className="space-y-3">
        {canRetry ? (
          <Button
            variant="primary"
            onClick={handleRetry}
            loading={isRetrying}
            icon={<RefreshCw size={16} />}
            fullWidth
          >
            {isRetrying ? 'Retrying...' : 'Try Again'}
          </Button>
        ) : retryAttempts >= maxRetries ? (
          <div className="text-sm text-gray-600">
            <AlertCircle size={16} className="inline mr-1" />
            Maximum retry attempts reached. Please refresh the page.
          </div>
        ) : null}

        {showRetryCount && retryAttempts > 0 && (
          <div className="text-xs text-gray-500">
            Attempt {retryAttempts} of {maxRetries}
          </div>
        )}
      </div>

      {/* Connection status indicator */}
      <div className="mt-4 pt-4 border-t border-red-200">
        <div className="flex items-center justify-center text-xs">
          {navigator.onLine ? (
            <>
              <CheckCircle size={14} className="text-green-500 mr-1" />
              <span className="text-green-600">Internet Connected</span>
            </>
          ) : (
            <>
              <WifiOff size={14} className="text-red-500 mr-1" />
              <span className="text-red-600">No Internet Connection</span>
            </>
          )}
        </div>
      </div>
    </Card>
  );
};

// Hook for network error handling
export const useNetworkError = () => {
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);

  const handleError = (err) => {
    setError(err);
  };

  const retry = async (retryFn) => {
    setRetryCount(prev => prev + 1);
    try {
      await retryFn();
      setError(null);
      setRetryCount(0);
    } catch (err) {
      setError(err);
    }
  };

  const clearError = () => {
    setError(null);
    setRetryCount(0);
  };

  return {
    error,
    retryCount,
    handleError,
    retry,
    clearError
  };
};

export default NetworkError;
