"""
Pagination Analytics and Monitoring System

This module provides comprehensive analytics and monitoring for pagination performance:
- Usage analytics
- Performance monitoring
- User behavior tracking
- System optimization recommendations
"""

from django.core.cache import cache
from django.db import models, connection
from django.utils import timezone
from django.contrib.auth import get_user_model
import json
import time
import statistics
from datetime import datetime, timedelta
from collections import defaultdict, Counter

User = get_user_model()


class PaginationAnalytics:
    """Main analytics class for pagination monitoring"""
    
    def __init__(self):
        self.cache_prefix = "pagination_analytics"
        self.cache_timeout = 86400  # 24 hours
    
    def track_page_view(self, user_id, view_name, page, page_size, response_time, filters=None):
        """Track a pagination page view"""
        timestamp = timezone.now()
        
        # Create analytics entry
        analytics_data = {
            'user_id': user_id,
            'view_name': view_name,
            'page': page,
            'page_size': page_size,
            'response_time': response_time,
            'filters': filters or {},
            'timestamp': timestamp.isoformat(),
        }
        
        # Store in cache for real-time analytics
        cache_key = f"{self.cache_prefix}:views:{timestamp.strftime('%Y%m%d')}"
        views = cache.get(cache_key, [])
        views.append(analytics_data)
        cache.set(cache_key, views, self.cache_timeout)
        
        # Update aggregated metrics
        self._update_aggregated_metrics(analytics_data)
    
    def _update_aggregated_metrics(self, analytics_data):
        """Update aggregated metrics for faster reporting"""
        date_key = datetime.fromisoformat(analytics_data['timestamp']).strftime('%Y%m%d')
        
        # Daily metrics
        daily_key = f"{self.cache_prefix}:daily:{date_key}"
        daily_metrics = cache.get(daily_key, {
            'total_views': 0,
            'unique_users': set(),
            'avg_response_time': 0,
            'total_response_time': 0,
            'page_sizes': Counter(),
            'views_by_hour': defaultdict(int),
            'popular_pages': Counter(),
            'view_names': Counter(),
        })
        
        # Update metrics
        daily_metrics['total_views'] += 1
        daily_metrics['unique_users'].add(analytics_data['user_id'])
        daily_metrics['total_response_time'] += analytics_data['response_time']
        daily_metrics['avg_response_time'] = daily_metrics['total_response_time'] / daily_metrics['total_views']
        daily_metrics['page_sizes'][analytics_data['page_size']] += 1
        
        hour = datetime.fromisoformat(analytics_data['timestamp']).hour
        daily_metrics['views_by_hour'][hour] += 1
        daily_metrics['popular_pages'][analytics_data['page']] += 1
        daily_metrics['view_names'][analytics_data['view_name']] += 1
        
        cache.set(daily_key, daily_metrics, self.cache_timeout)
    
    def get_daily_report(self, date=None):
        """Get daily analytics report"""
        if date is None:
            date = timezone.now().date()
        
        date_key = date.strftime('%Y%m%d')
        daily_key = f"{self.cache_prefix}:daily:{date_key}"
        metrics = cache.get(daily_key, {})
        
        if not metrics:
            return {
                'date': date.isoformat(),
                'total_views': 0,
                'unique_users': 0,
                'avg_response_time': 0,
                'message': 'No data available for this date'
            }
        
        # Convert sets to counts for JSON serialization
        if 'unique_users' in metrics and isinstance(metrics['unique_users'], set):
            metrics['unique_users'] = len(metrics['unique_users'])
        
        return {
            'date': date.isoformat(),
            'total_views': metrics.get('total_views', 0),
            'unique_users': metrics.get('unique_users', 0),
            'avg_response_time': round(metrics.get('avg_response_time', 0), 2),
            'page_sizes': dict(metrics.get('page_sizes', {})),
            'views_by_hour': dict(metrics.get('views_by_hour', {})),
            'popular_pages': dict(metrics.get('popular_pages', {})),
            'view_names': dict(metrics.get('view_names', {})),
        }
    
    def get_weekly_report(self, end_date=None):
        """Get weekly analytics report"""
        if end_date is None:
            end_date = timezone.now().date()
        
        start_date = end_date - timedelta(days=6)
        weekly_data = []
        
        current_date = start_date
        while current_date <= end_date:
            daily_report = self.get_daily_report(current_date)
            weekly_data.append(daily_report)
            current_date += timedelta(days=1)
        
        # Calculate weekly aggregates
        total_views = sum(day['total_views'] for day in weekly_data)
        unique_users = len(set(
            user for day in weekly_data 
            for user in day.get('unique_users', [])
        )) if weekly_data else 0
        
        response_times = [
            day['avg_response_time'] for day in weekly_data 
            if day['avg_response_time'] > 0
        ]
        avg_response_time = statistics.mean(response_times) if response_times else 0
        
        return {
            'period': f"{start_date.isoformat()} to {end_date.isoformat()}",
            'total_views': total_views,
            'unique_users': unique_users,
            'avg_response_time': round(avg_response_time, 2),
            'daily_data': weekly_data,
        }
    
    def get_performance_insights(self):
        """Get performance insights and recommendations"""
        # Get recent data for analysis
        today = timezone.now().date()
        weekly_report = self.get_weekly_report(today)
        
        insights = {
            'performance_score': 0,
            'recommendations': [],
            'alerts': [],
            'trends': {},
        }
        
        # Analyze response times
        avg_response_time = weekly_report['avg_response_time']
        if avg_response_time > 1000:  # > 1 second
            insights['alerts'].append({
                'type': 'performance',
                'severity': 'high',
                'message': f'Average response time is {avg_response_time}ms, which is above recommended 1000ms'
            })
            insights['recommendations'].append(
                'Consider implementing caching or optimizing database queries'
            )
        elif avg_response_time > 500:  # > 500ms
            insights['alerts'].append({
                'type': 'performance',
                'severity': 'medium',
                'message': f'Average response time is {avg_response_time}ms, consider optimization'
            })
        
        # Analyze page size usage
        daily_data = weekly_report['daily_data']
        if daily_data:
            # Get most common page sizes
            all_page_sizes = Counter()
            for day in daily_data:
                for size, count in day.get('page_sizes', {}).items():
                    all_page_sizes[int(size)] += count
            
            if all_page_sizes:
                most_common_size = all_page_sizes.most_common(1)[0][0]
                if most_common_size > 50:
                    insights['recommendations'].append(
                        f'Most users prefer page size {most_common_size}. Consider making this the default.'
                    )
        
        # Calculate performance score
        score = 100
        if avg_response_time > 1000:
            score -= 30
        elif avg_response_time > 500:
            score -= 15
        
        if weekly_report['total_views'] == 0:
            score = 0
        
        insights['performance_score'] = max(0, score)
        
        return insights
    
    def get_user_behavior_analysis(self, user_id, days=7):
        """Analyze individual user pagination behavior"""
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days-1)
        
        user_data = []
        current_date = start_date
        
        while current_date <= end_date:
            date_key = current_date.strftime('%Y%m%d')
            cache_key = f"{self.cache_prefix}:views:{date_key}"
            views = cache.get(cache_key, [])
            
            user_views = [v for v in views if v['user_id'] == user_id]
            user_data.extend(user_views)
            
            current_date += timedelta(days=1)
        
        if not user_data:
            return {
                'user_id': user_id,
                'total_views': 0,
                'message': 'No pagination data found for this user'
            }
        
        # Analyze user behavior
        page_preferences = Counter(v['page_size'] for v in user_data)
        view_preferences = Counter(v['view_name'] for v in user_data)
        page_patterns = Counter(v['page'] for v in user_data)
        
        avg_response_time = statistics.mean(v['response_time'] for v in user_data)
        
        return {
            'user_id': user_id,
            'total_views': len(user_data),
            'avg_response_time': round(avg_response_time, 2),
            'preferred_page_size': page_preferences.most_common(1)[0][0] if page_preferences else None,
            'most_visited_view': view_preferences.most_common(1)[0][0] if view_preferences else None,
            'page_preferences': dict(page_preferences),
            'view_preferences': dict(view_preferences),
            'page_patterns': dict(page_patterns),
        }


class PaginationMonitoringMiddleware:
    """Middleware to automatically track pagination usage"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.analytics = PaginationAnalytics()
    
    def __call__(self, request):
        start_time = time.time()
        response = self.get_response(request)
        response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        # Track pagination requests
        if self._is_pagination_request(request):
            self._track_pagination_request(request, response_time)
        
        return response
    
    def _is_pagination_request(self, request):
        """Check if request is a pagination request"""
        return (
            request.method == 'GET' and
            ('page' in request.GET or 'cursor' in request.GET or 'offset' in request.GET)
        )
    
    def _track_pagination_request(self, request, response_time):
        """Track pagination request analytics"""
        try:
            user_id = request.user.id if request.user.is_authenticated else None
            view_name = getattr(request.resolver_match, 'view_name', 'unknown')
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            
            # Extract filters
            filters = {
                k: v for k, v in request.GET.items()
                if k not in ['page', 'page_size', 'cursor', 'offset', 'limit']
            }
            
            self.analytics.track_page_view(
                user_id=user_id,
                view_name=view_name,
                page=page,
                page_size=page_size,
                response_time=response_time,
                filters=filters
            )
        except Exception as e:
            # Fail silently to not break the request
            pass


# API Views for analytics
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAdminUser


class PaginationAnalyticsView(APIView):
    """API view for pagination analytics"""
    permission_classes = [IsAdminUser]
    
    def __init__(self):
        super().__init__()
        self.analytics = PaginationAnalytics()
    
    def get(self, request):
        """Get pagination analytics"""
        report_type = request.GET.get('type', 'daily')
        date_str = request.GET.get('date')
        
        if date_str:
            try:
                date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response({'error': 'Invalid date format. Use YYYY-MM-DD'}, status=400)
        else:
            date = None
        
        if report_type == 'daily':
            data = self.analytics.get_daily_report(date)
        elif report_type == 'weekly':
            data = self.analytics.get_weekly_report(date)
        elif report_type == 'insights':
            data = self.analytics.get_performance_insights()
        else:
            return Response({'error': 'Invalid report type'}, status=400)
        
        return Response(data)


class UserBehaviorAnalyticsView(APIView):
    """API view for user behavior analytics"""
    permission_classes = [IsAdminUser]
    
    def __init__(self):
        super().__init__()
        self.analytics = PaginationAnalytics()
    
    def get(self, request, user_id):
        """Get user behavior analytics"""
        days = int(request.GET.get('days', 7))
        data = self.analytics.get_user_behavior_analysis(user_id, days)
        return Response(data)


# Utility functions
def get_pagination_health_check():
    """Get overall pagination system health"""
    analytics = PaginationAnalytics()
    insights = analytics.get_performance_insights()
    
    health_status = 'healthy'
    if insights['performance_score'] < 50:
        health_status = 'critical'
    elif insights['performance_score'] < 75:
        health_status = 'warning'
    
    return {
        'status': health_status,
        'score': insights['performance_score'],
        'alerts': len(insights['alerts']),
        'recommendations': len(insights['recommendations']),
    }


def generate_pagination_report():
    """Generate comprehensive pagination report"""
    analytics = PaginationAnalytics()
    
    return {
        'daily_report': analytics.get_daily_report(),
        'weekly_report': analytics.get_weekly_report(),
        'insights': analytics.get_performance_insights(),
        'health_check': get_pagination_health_check(),
        'generated_at': timezone.now().isoformat(),
    }
