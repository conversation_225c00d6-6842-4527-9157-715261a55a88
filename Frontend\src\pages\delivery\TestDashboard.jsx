import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { mockDeliveryAgents, mockDeliveryOrders, mockDeliveryEarnings } from '../../data/deliveryAgents';

function TestDashboard() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  useEffect(() => {
    const loadData = () => {
      try {
        console.log('=== TEST DASHBOARD ===');
        console.log('User:', user);
        console.log('Agents:', mockDeliveryAgents);
        console.log('Orders:', mockDeliveryOrders);
        console.log('Earnings:', mockDeliveryEarnings);

        setData({
          user,
          agentsCount: mockDeliveryAgents?.length || 0,
          ordersCount: mockDeliveryOrders?.length || 0,
          earningsCount: mockDeliveryEarnings?.length || 0,
        });

        setLoading(false);
      } catch (err) {
        console.error('Error:', err);
        setError(err.message);
        setLoading(false);
      }
    };

    setTimeout(loadData, 500);
  }, [user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading test dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">Error</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Test Delivery Dashboard</h1>
        
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Debug Information</h2>
          <div className="space-y-2">
            <p><strong>User ID:</strong> {data?.user?.id || 'Not available'}</p>
            <p><strong>User Role:</strong> {data?.user?.role || 'Not available'}</p>
            <p><strong>User Email:</strong> {data?.user?.email || 'Not available'}</p>
            <p><strong>Agents Count:</strong> {data?.agentsCount}</p>
            <p><strong>Orders Count:</strong> {data?.ordersCount}</p>
            <p><strong>Earnings Count:</strong> {data?.earningsCount}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Delivery Agents</h3>
            <p className="text-3xl font-bold text-blue-600">{data?.agentsCount}</p>
            <p className="text-sm text-gray-600">Available agents</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Orders</h3>
            <p className="text-3xl font-bold text-green-600">{data?.ordersCount}</p>
            <p className="text-sm text-gray-600">Total orders</p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Earnings</h3>
            <p className="text-3xl font-bold text-purple-600">{data?.earningsCount}</p>
            <p className="text-sm text-gray-600">Earnings records</p>
          </div>
        </div>

        {mockDeliveryAgents && mockDeliveryAgents.length > 0 && (
          <div className="bg-white rounded-lg shadow p-6 mt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Sample Agent Data</h3>
            <div className="bg-gray-50 p-4 rounded">
              <pre className="text-sm overflow-x-auto">
                {JSON.stringify(mockDeliveryAgents[0], null, 2)}
              </pre>
            </div>
          </div>
        )}

        <div className="mt-6 text-center">
          <button
            onClick={() => window.location.href = '/delivery'}
            className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mr-4"
          >
            Try Full Dashboard
          </button>
          <button
            onClick={() => window.location.href = '/'}
            className="px-6 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Go Home
          </button>
        </div>
      </div>
    </div>
  );
}

export default TestDashboard;
