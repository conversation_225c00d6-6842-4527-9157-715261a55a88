import React, { useState } from "react";
import axios from "axios";

const ApiDiagnostic = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const addResult = (test, result) => {
    setResults((prev) => [
      ...prev,
      {
        test,
        result,
        timestamp: new Date().toLocaleTimeString(),
      },
    ]);
  };

  const testApiEndpoint = async () => {
    setLoading(true);
    try {
      console.log("🔍 Testing API endpoint...");

      const baseUrl = "http://127.0.0.1:8000/api";

      // Test 1: Basic connectivity
      try {
        const response = await fetch(baseUrl + "/auth/register/", {
          method: "OPTIONS",
          headers: {
            "Content-Type": "application/json",
          },
        });

        addResult("API Connectivity", {
          success: response.ok,
          status: response.status,
          message: response.ok
            ? "✅ API endpoint is reachable"
            : "❌ API endpoint not reachable",
          url: baseUrl + "/auth/register/",
        });
      } catch (error) {
        addResult("API Connectivity", {
          success: false,
          message: "❌ Cannot reach API endpoint",
          error: error.message,
          url: baseUrl + "/auth/register/",
        });
      }

      // Test 2: Try a simple POST request
      try {
        const testData = {
          name: "API Test User",
          user_name: "apitest" + Date.now(),
          email: "<EMAIL>",
          password: "123456",
          phone: "+1234567890",
          role: "customer",
        };

        const response = await axios.post(
          baseUrl + "/auth/register/",
          testData,
          {
            headers: {
              "Content-Type": "application/json",
            },
            timeout: 10000,
          }
        );

        addResult("Registration API Test", {
          success: true,
          status: response.status,
          message: "✅ Registration API working",
          data: response.data,
        });
      } catch (error) {
        const errorInfo = {
          success: false,
          status: error.response?.status,
          message: `❌ Registration API failed (${
            error.response?.status || "Network Error"
          })`,
          error: error.message,
          details: {
            url: error.config?.url,
            method: error.config?.method,
            requestData: error.config?.data,
            responseData: error.response?.data,
            headers: error.response?.headers,
          },
        };

        if (error.response?.status === 500) {
          errorInfo.suggestion =
            "Backend server error. Check your Django/backend logs for detailed error information.";
        } else if (error.response?.status === 404) {
          errorInfo.suggestion =
            "API endpoint not found. Check if the URL path is correct.";
        } else if (error.response?.status === 400) {
          errorInfo.suggestion =
            "Bad request. Check if the data format matches what the backend expects.";
        } else if (!error.response) {
          errorInfo.suggestion =
            "Network error. Check if the backend server is running.";
        }

        addResult("Registration API Test", errorInfo);
      }
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => setResults([]);

  return (
    <div className='p-6 max-w-4xl mx-auto'>
      <h1 className='text-2xl font-bold mb-6'>🔍 API Diagnostic Tool</h1>

      <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6'>
        <h2 className='font-semibold text-yellow-800 mb-2'>
          🚨 Current Issue:
        </h2>
        <p className='text-sm text-yellow-700'>
          <strong>Error:</strong> HTTP 500 Internal Server Error
          <br />
          <strong>Meaning:</strong> Your backend API is reachable but has an
          internal error
          <br />
          <strong>Solution:</strong> Check your backend server logs for detailed
          error information
        </p>
      </div>

      <div className='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6'>
        <h2 className='font-semibold text-blue-800 mb-2'>📋 Checklist:</h2>
        <ul className='text-sm text-blue-700 space-y-1'>
          <li>✅ Frontend is working correctly</li>
          <li>✅ API endpoint is reachable</li>
          <li>❌ Backend server has an internal error (500)</li>
          <li>🔍 Need to check backend logs</li>
        </ul>
      </div>

      <div className='grid grid-cols-1 gap-4 mb-6'>
        <button
          onClick={testApiEndpoint}
          disabled={loading}
          className='bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50'
        >
          🔍 Run API Diagnostic
        </button>

        <button
          onClick={clearResults}
          className='bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600'
        >
          Clear Results
        </button>
      </div>

      {loading && (
        <div className='text-center py-4'>
          <div className='inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500'></div>
          <p className='mt-2'>Running diagnostics...</p>
        </div>
      )}

      <div className='space-y-4'>
        <h2 className='text-xl font-semibold'>Diagnostic Results:</h2>
        {results.length === 0 ? (
          <p className='text-gray-500'>
            No diagnostics run yet. Click the button above.
          </p>
        ) : (
          results.map((item, index) => (
            <div
              key={index}
              className={`border rounded p-4 ${
                item.result.success
                  ? "bg-green-50 border-green-200"
                  : "bg-red-50 border-red-200"
              }`}
            >
              <div className='flex justify-between items-center mb-2'>
                <h3 className='font-semibold'>{item.test}</h3>
                <span className='text-sm text-gray-500'>{item.timestamp}</span>
              </div>
              <div className='mb-2'>
                <span
                  className={`font-semibold ${
                    item.result.success ? "text-green-700" : "text-red-700"
                  }`}
                >
                  {item.result.message}
                </span>
                {item.result.status && (
                  <span className='ml-2 text-sm text-gray-600'>
                    (Status: {item.result.status})
                  </span>
                )}
              </div>
              {item.result.suggestion && (
                <div className='mb-2 p-2 bg-yellow-100 border border-yellow-300 rounded text-sm'>
                  <strong>💡 Suggestion:</strong> {item.result.suggestion}
                </div>
              )}
              <details className='text-sm'>
                <summary className='cursor-pointer'>
                  View Technical Details
                </summary>
                <pre className='mt-2 p-2 bg-gray-100 rounded overflow-auto text-xs'>
                  {JSON.stringify(item.result, null, 2)}
                </pre>
              </details>
            </div>
          ))
        )}
      </div>

      <div className='mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4'>
        <h3 className='font-semibold mb-2'>🛠️ Next Steps:</h3>
        <ol className='text-sm space-y-1 list-decimal list-inside'>
          <li>Check your backend server logs for the 500 error details</li>
          <li>Verify your Django/backend API is running properly</li>
          <li>Check database connections in your backend</li>
          <li>Verify the /auth/register/ endpoint implementation</li>
          <li>Test the API directly with tools like Postman or curl</li>
        </ol>
      </div>
    </div>
  );
};

export default ApiDiagnostic;
