# financial_management/serializers.py
from rest_framework import serializers
from .models import (
    CommissionStructure, RestaurantEarnings, RestaurantPayout,
    RestaurantBankAccount, FinancialReport
)
from .delivery_agent_payments import (
    DeliveryAgentCommission,
    DeliveryAgentEarnings,
    DeliveryAgentPayout,
    DeliveryAgentFinancialSummary
)


class CommissionStructureSerializer(serializers.ModelSerializer):
    class Meta:
        model = CommissionStructure
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']


class RestaurantEarningsSerializer(serializers.ModelSerializer):
    order_details = serializers.SerializerMethodField()
    
    class Meta:
        model = RestaurantEarnings
        fields = '__all__'
        read_only_fields = [
            'commission_amount', 'payment_processing_fee', 
            'delivery_fee_platform_share', 'gross_earnings', 
            'net_earnings', 'created_at', 'updated_at'
        ]
    
    def get_order_details(self, obj):
        return {
            'id': obj.order.id,
            'customer': obj.order.customer.name,
            'status': obj.order.status,
            'created_at': obj.order.created_at,
        }


class RestaurantPayoutSerializer(serializers.ModelSerializer):
    restaurant_name = serializers.CharField(source='restaurant.name', read_only=True)
    
    class Meta:
        model = RestaurantPayout
        fields = '__all__'
        read_only_fields = [
            'orders_count', 'total_gross_earnings', 'total_commission',
            'total_fees', 'created_at', 'processed_at', 'completed_at'
        ]


class RestaurantBankAccountSerializer(serializers.ModelSerializer):
    class Meta:
        model = RestaurantBankAccount
        fields = '__all__'
        read_only_fields = ['is_verified', 'verified_at', 'verified_by', 'created_at', 'updated_at']
        extra_kwargs = {
            'account_number': {'write_only': True},
            'routing_number': {'write_only': True},
        }


class FinancialReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = FinancialReport
        fields = '__all__'
        read_only_fields = ['created_at']


class EarningsSummarySerializer(serializers.Serializer):
    """Serializer for earnings summary data"""
    total_earnings = serializers.DecimalField(max_digits=12, decimal_places=2)
    pending_earnings = serializers.DecimalField(max_digits=12, decimal_places=2)
    paid_earnings = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_orders = serializers.IntegerField()
    average_order_value = serializers.DecimalField(max_digits=8, decimal_places=2)
    commission_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    next_payout_date = serializers.DateTimeField(allow_null=True)
    next_payout_amount = serializers.DecimalField(max_digits=12, decimal_places=2)


class PayoutRequestSerializer(serializers.Serializer):
    """Serializer for payout requests"""
    amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    payment_method = serializers.ChoiceField(choices=[
        ('bank_transfer', 'Bank Transfer'),
        ('paypal', 'PayPal'),
        ('stripe', 'Stripe'),
    ])
    notes = serializers.CharField(max_length=500, required=False)


class FinancialDashboardSerializer(serializers.Serializer):
    """Serializer for financial dashboard data"""
    # Current period metrics
    current_period_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    current_period_orders = serializers.IntegerField()
    current_period_commission = serializers.DecimalField(max_digits=12, decimal_places=2)
    current_period_net_earnings = serializers.DecimalField(max_digits=12, decimal_places=2)

    # Previous period comparison
    revenue_growth = serializers.DecimalField(max_digits=5, decimal_places=2)
    orders_growth = serializers.DecimalField(max_digits=5, decimal_places=2)

    # Payout information
    pending_payout_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    next_payout_date = serializers.DateTimeField(allow_null=True)
    last_payout_amount = serializers.DecimalField(max_digits=12, decimal_places=2, allow_null=True)
    last_payout_date = serializers.DateTimeField(allow_null=True)

    # Charts data
    revenue_chart_data = serializers.ListField()
    orders_chart_data = serializers.ListField()
    commission_breakdown = serializers.DictField()


# Delivery Agent Serializers

class DeliveryAgentCommissionSerializer(serializers.ModelSerializer):
    """Serializer for delivery agent commission structure"""
    class Meta:
        model = DeliveryAgentCommission
        fields = '__all__'
        read_only_fields = ['created_at', 'updated_at']


class DeliveryAgentEarningsSerializer(serializers.ModelSerializer):
    """Serializer for delivery agent earnings"""
    order_details = serializers.SerializerMethodField()
    delivery_agent_name = serializers.CharField(source='delivery_agent.get_full_name', read_only=True)

    class Meta:
        model = DeliveryAgentEarnings
        fields = '__all__'
        read_only_fields = [
            'base_fee', 'distance_bonus', 'time_bonus', 'gross_earnings',
            'platform_commission', 'net_earnings', 'created_at', 'updated_at'
        ]

    def get_order_details(self, obj):
        return {
            'id': obj.order.id,
            'customer': obj.order.customer.get_full_name() if obj.order.customer else 'Unknown',
            'restaurant': obj.order.restaurant.name,
            'status': obj.order.status,
            'total_amount': obj.order.total_amount,
            'delivery_address': str(obj.order.delivery_address),
            'created_at': obj.order.created_at,
        }


class DeliveryAgentPayoutSerializer(serializers.ModelSerializer):
    """Serializer for delivery agent payouts"""
    delivery_agent_name = serializers.CharField(source='delivery_agent.get_full_name', read_only=True)
    delivery_agent_email = serializers.CharField(source='delivery_agent.email', read_only=True)

    class Meta:
        model = DeliveryAgentPayout
        fields = '__all__'
        read_only_fields = [
            'deliveries_count', 'total_gross_earnings', 'total_commission',
            'total_tips', 'performance_bonus', 'created_at', 'processed_at', 'completed_at'
        ]


class DeliveryAgentFinancialSummarySerializer(serializers.ModelSerializer):
    """Serializer for delivery agent financial summaries"""
    delivery_agent_name = serializers.CharField(source='delivery_agent.get_full_name', read_only=True)

    class Meta:
        model = DeliveryAgentFinancialSummary
        fields = '__all__'
        read_only_fields = ['created_at']


class DeliveryAgentDashboardSerializer(serializers.Serializer):
    """Serializer for delivery agent dashboard data"""
    # Current period metrics
    total_earnings = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_deliveries = serializers.IntegerField()
    total_tips = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_distance_km = serializers.DecimalField(max_digits=8, decimal_places=2)

    # Performance metrics
    avg_earnings_per_delivery = serializers.DecimalField(max_digits=8, decimal_places=2)
    avg_delivery_time = serializers.DecimalField(max_digits=5, decimal_places=2)
    completion_rate = serializers.DecimalField(max_digits=5, decimal_places=2)

    # Payout information
    pending_earnings = serializers.DecimalField(max_digits=12, decimal_places=2)
    last_payout_amount = serializers.DecimalField(max_digits=12, decimal_places=2, allow_null=True)
    last_payout_date = serializers.DateTimeField(allow_null=True)
    next_payout_eligible = serializers.BooleanField()

    # Charts data
    earnings_chart_data = serializers.ListField()
    deliveries_chart_data = serializers.ListField()
    performance_metrics = serializers.DictField()
