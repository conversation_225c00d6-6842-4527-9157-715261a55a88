#!/usr/bin/env python3
"""
Test script to verify OTP functionality in Afghan Sufra system
"""

import requests
import json
import time

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_user_registration():
    """Test user registration and OTP generation"""
    print("🧪 Testing User Registration and OTP Generation...")
    
    # Test user data
    test_user = {
        "name": "Test User OTP",
        "user_name": f"testuser_otp_{int(time.time())}",
        "email": f"testuser{int(time.time())}@example.com",
        "phone": f"+123456{int(time.time()) % 10000}",
        "password": "TestPassword123",
        "confirm_password": "TestPassword123",
        "role": "customer"
    }
    
    print(f"📝 Registering user: {test_user['email']}")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/register/",
            headers=HEADERS,
            data=json.dumps(test_user)
        )
        
        print(f"📡 Response Status: {response.status_code}")
        print(f"📄 Response Content: {response.text}")
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                print("✅ Registration successful!")
                print(f"👤 User ID: {result['data']['user_id']}")
                print(f"📧 Email: {result['data']['email']}")
                print("📬 Check Django console for OTP email...")
                return result['data']
            else:
                print("❌ Registration failed:", result.get('message'))
                return None
        else:
            print(f"❌ Registration failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {error_data}")
            except:
                print(f"Raw response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Exception during registration: {e}")
        return None

def test_email_verification(email, otp):
    """Test email verification with OTP"""
    print(f"\n🔐 Testing Email Verification for {email}...")
    
    verification_data = {
        "email": email,
        "otp": otp
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/verify-email/",
            headers=HEADERS,
            data=json.dumps(verification_data)
        )
        
        print(f"📡 Response Status: {response.status_code}")
        print(f"📄 Response Content: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Email verification successful!")
                print(f"🎫 Access Token: {result['data']['access_token'][:50]}...")
                return True
            else:
                print("❌ Email verification failed:", result.get('message'))
                return False
        else:
            print(f"❌ Verification failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during verification: {e}")
        return False

def test_resend_otp(email):
    """Test OTP resend functionality"""
    print(f"\n🔄 Testing OTP Resend for {email}...")
    
    resend_data = {
        "email": email
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/resend-otp/",
            headers=HEADERS,
            data=json.dumps(resend_data)
        )
        
        print(f"📡 Response Status: {response.status_code}")
        print(f"📄 Response Content: {response.text}")
        
        if response.status_code == 200:
            print("✅ OTP resend successful!")
            print("📬 Check Django console for new OTP email...")
            return True
        else:
            print(f"❌ OTP resend failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during OTP resend: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting OTP Functionality Test")
    print("=" * 50)
    
    # Test 1: User Registration
    user_data = test_user_registration()
    if not user_data:
        print("❌ Registration test failed. Stopping tests.")
        return
    
    print("\n" + "=" * 50)
    print("📋 MANUAL STEPS REQUIRED:")
    print("1. Check the Django console (Backend terminal) for the OTP email")
    print("2. Copy the 6-digit OTP code from the console output")
    print("3. Enter the OTP when prompted below")
    print("=" * 50)
    
    # Wait for user to get OTP from console
    otp = input(f"\n🔑 Enter the OTP sent to {user_data['email']}: ").strip()
    
    if len(otp) == 6 and otp.isdigit():
        # Test 2: Email Verification
        verification_success = test_email_verification(user_data['email'], otp)
        
        if verification_success:
            print("\n🎉 OTP functionality is working correctly!")
        else:
            print("\n❌ OTP verification failed")
            
            # Test 3: Resend OTP
            print("\nTesting OTP resend...")
            resend_success = test_resend_otp(user_data['email'])
            
            if resend_success:
                new_otp = input(f"\n🔑 Enter the NEW OTP sent to {user_data['email']}: ").strip()
                if len(new_otp) == 6 and new_otp.isdigit():
                    verification_success = test_email_verification(user_data['email'], new_otp)
                    if verification_success:
                        print("\n🎉 OTP resend and verification working correctly!")
    else:
        print("❌ Invalid OTP format. OTP should be 6 digits.")
    
    print("\n" + "=" * 50)
    print("🏁 OTP Test Complete")

if __name__ == "__main__":
    main()
