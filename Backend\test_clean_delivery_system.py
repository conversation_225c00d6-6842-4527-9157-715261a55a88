#!/usr/bin/env python
"""
🚚 CLEAN DELIVERY AGENT SYSTEM TEST
Test the hybrid registration system (self-registration + admin approval)
"""

import os
import sys
import django
import json
import requests
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from deliveryAgent.models import DeliveryAgentProfile

User = get_user_model()

class CleanDeliverySystemTest:
    def __init__(self):
        self.client = Client()
        self.base_url = 'http://localhost:8001'
        self.test_data = {}
        
    def print_header(self, title):
        print(f"\n{'='*60}")
        print(f"{title}")
        print(f"{'='*60}")
    
    def print_step(self, step_num, title):
        print(f"\n{'='*60}")
        print(f"STEP {step_num}: {title}")
        print(f"{'='*60}")
    
    def print_success(self, message):
        print(f"✅ SUCCESS: {message}")
    
    def print_error(self, message):
        print(f"❌ FAILED: {message}")
    
    def test_agent_self_registration(self):
        """Test agent self-registration"""
        self.print_step(1, "AGENT SELF-REGISTRATION")
        
        try:
            # Test data for Afghan agent
            registration_data = {
                'full_name': 'احمد علی تست',
                'father_name': 'محمد علی',
                'national_id': '**********123',
                'date_of_birth': '1995-05-15',
                'gender': 'male',
                'marital_status': 'married',
                'phone_primary': '+***********',
                'phone_secondary': '+93789876543',
                'email': '<EMAIL>',
                'province': 'kabul',
                'district': 'Kabul City',
                'area': 'Shar-e-Naw',
                'street_address': 'Street 15, House 25',
                'nearby_landmark': 'Near Police Station',
                'vehicle_type': 'motorcycle',
                'vehicle_model': 'Honda 125cc',
                'vehicle_year': 2020,
                'license_plate': 'KBL-001',
                'vehicle_color': 'Red',
                'driving_license': 'DL123456',
                'reference1_name': 'محمد حسن',
                'reference1_phone': '+***********',
                'reference1_relation': 'Uncle',
                'reference2_name': 'علی احمد',
                'reference2_phone': '+***********',
                'reference2_relation': 'Neighbor',
                'emergency_contact': '+***********',
                'emergency_relation': 'Brother',
                'bank_name': 'Afghanistan Bank',
                'account_number': '**********',
                'account_holder_name': 'احمد علی',
                'mobile_wallet': '+***********'
            }
            
            # Make registration request
            response = self.client.post(
                '/api/delivery-agent/self-register/',
                data=json.dumps(registration_data),
                content_type='application/json'
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    self.test_data['agent_id'] = result.get('agent_id')
                    self.test_data['application_number'] = result.get('application_number')
                    
                    self.print_success(f"Agent registered successfully!")
                    print(f"   • Agent ID: {self.test_data['agent_id']}")
                    print(f"   • Application Number: {self.test_data['application_number']}")
                    print(f"   • Status: Pending admin approval")
                    print(f"   • Phone: {registration_data['phone_primary']}")
                    print(f"   • Province: {registration_data['province']}")
                    print(f"   • Vehicle: {registration_data['vehicle_type']}")
                    
                    return True
                else:
                    self.print_error(f"Registration failed: {result.get('message')}")
                    return False
            else:
                self.print_error(f"HTTP {response.status_code}: {response.content.decode()}")
                return False
                
        except Exception as e:
            self.print_error(f"Registration error: {str(e)}")
            return False
    
    def test_application_status_check(self):
        """Test application status checking"""
        self.print_step(2, "APPLICATION STATUS CHECK")
        
        try:
            if not self.test_data.get('agent_id'):
                self.print_error("No agent ID available from registration")
                return False
            
            # Check status by agent ID
            response = self.client.get(
                f'/api/delivery-agent/application-status/?agent_id={self.test_data["agent_id"]}'
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    self.print_success("Application status retrieved successfully!")
                    print(f"   • Agent ID: {result.get('agent_id')}")
                    print(f"   • Status: {result.get('application_status')}")
                    print(f"   • Application Date: {result.get('application_date')}")
                    print(f"   • Verification Status: {result.get('verification_status')}")
                    print(f"   • Next Steps: {result.get('next_steps')}")
                    
                    return True
                else:
                    self.print_error(f"Status check failed: {result.get('message')}")
                    return False
            else:
                self.print_error(f"HTTP {response.status_code}: {response.content.decode()}")
                return False
                
        except Exception as e:
            self.print_error(f"Status check error: {str(e)}")
            return False
    
    def test_admin_approval_workflow(self):
        """Test admin approval workflow"""
        self.print_step(3, "ADMIN APPROVAL WORKFLOW")
        
        try:
            # Create admin user
            admin_user, created = User.objects.get_or_create(
                user_name='admin_test',
                defaults={
                    'name': 'Test Admin',
                    'email': '<EMAIL>',
                    'role': 'admin',
                    'is_staff': True,
                    'is_superuser': True
                }
            )
            admin_user.set_password('admin123')
            admin_user.save()
            
            # Login as admin
            login_success = self.client.login(username='admin_test', password='admin123')
            if not login_success:
                self.print_error("Admin login failed")
                return False
            
            # Get pending applications
            response = self.client.get('/api/delivery-agent/admin/applications/')
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    agents = result.get('agents', [])
                    self.print_success(f"Found {len(agents)} pending applications")
                    
                    if agents:
                        # Show first application details
                        agent = agents[0]
                        print(f"   • Agent ID: {agent.get('agent_id')}")
                        print(f"   • Name: {agent.get('full_name')}")
                        print(f"   • Phone: {agent.get('phone_number')}")
                        print(f"   • Province: {agent.get('province')}")
                        print(f"   • Vehicle: {agent.get('vehicle_type')}")
                        print(f"   • Application Date: {agent.get('application_date')}")
                        
                        # Test approval
                        return self.test_agent_approval(agent.get('agent_id'))
                    else:
                        self.print_error("No pending applications found")
                        return False
                else:
                    self.print_error(f"Failed to get applications: {result.get('message')}")
                    return False
            else:
                self.print_error(f"HTTP {response.status_code}: {response.content.decode()}")
                return False
                
        except Exception as e:
            self.print_error(f"Admin workflow error: {str(e)}")
            return False
    
    def test_agent_approval(self, agent_id):
        """Test agent approval"""
        try:
            # Approve the agent
            approval_data = {
                'action': 'approve',
                'notes': 'All documents verified. Agent approved for delivery service.'
            }
            
            response = self.client.post(
                f'/api/delivery-agent/admin/applications/{agent_id}/action/',
                data=json.dumps(approval_data),
                content_type='application/json'
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    self.print_success(f"Agent {agent_id} approved successfully!")
                    print(f"   • Agent ID: {result.get('agent_id')}")
                    print(f"   • Login Username: {result.get('login_credentials', {}).get('username')}")
                    print(f"   • Password: {result.get('login_credentials', {}).get('password')}")
                    print(f"   • Next Steps: {result.get('next_steps')}")
                    
                    # Store credentials for testing
                    self.test_data['agent_username'] = result.get('login_credentials', {}).get('username')
                    self.test_data['agent_password'] = result.get('login_credentials', {}).get('password')
                    
                    return True
                else:
                    self.print_error(f"Approval failed: {result.get('message')}")
                    return False
            else:
                self.print_error(f"HTTP {response.status_code}: {response.content.decode()}")
                return False
                
        except Exception as e:
            self.print_error(f"Approval error: {str(e)}")
            return False
    
    def test_agent_login_and_dashboard(self):
        """Test agent login and dashboard access"""
        self.print_step(4, "AGENT LOGIN & DASHBOARD ACCESS")
        
        try:
            if not self.test_data.get('agent_username') or not self.test_data.get('agent_password'):
                self.print_error("No agent credentials available")
                return False
            
            # Login as agent
            login_success = self.client.login(
                username=self.test_data['agent_username'],
                password=self.test_data['agent_password']
            )
            
            if not login_success:
                self.print_error("Agent login failed")
                return False
            
            # Access dashboard
            response = self.client.get('/api/delivery-agent/dashboard/')
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    data = result.get('data', {})
                    agent_info = data.get('agent_info', {})
                    
                    self.print_success("Agent dashboard accessed successfully!")
                    print(f"   • Agent ID: {agent_info.get('agent_id')}")
                    print(f"   • Name: {agent_info.get('full_name')}")
                    print(f"   • Status: {agent_info.get('status')}")
                    print(f"   • Availability: {agent_info.get('availability')}")
                    print(f"   • Verified: {agent_info.get('is_verified')}")
                    print(f"   • Rating: {agent_info.get('rating')}")
                    print(f"   • Next Action: {data.get('status_info', {}).get('next_action')}")
                    
                    return True
                else:
                    self.print_error(f"Dashboard access failed: {result.get('message')}")
                    return False
            else:
                self.print_error(f"HTTP {response.status_code}: {response.content.decode()}")
                return False
                
        except Exception as e:
            self.print_error(f"Dashboard access error: {str(e)}")
            return False
    
    def test_performance_metrics(self):
        """Test performance metrics"""
        self.print_step(5, "PERFORMANCE METRICS")
        
        try:
            response = self.client.get('/api/delivery-agent/performance-metrics/')
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    metrics = result.get('metrics', {})
                    
                    self.print_success("Performance metrics retrieved successfully!")
                    print(f"   • Overall Rating: {metrics.get('overall_rating')}")
                    print(f"   • Completion Rate: {metrics.get('completion_rate')}%")
                    print(f"   • Total Deliveries: {metrics.get('total_deliveries')}")
                    print(f"   • Successful Deliveries: {metrics.get('successful_deliveries')}")
                    print(f"   • Total Earnings: ${metrics.get('total_earnings')}")
                    print(f"   • Verification Status: {metrics.get('verification_status')}")
                    print(f"   • Achievements: {metrics.get('achievements')}")
                    print(f"   • Next Goals: {metrics.get('next_goals')}")
                    
                    return True
                else:
                    self.print_error(f"Metrics access failed: {result.get('message')}")
                    return False
            else:
                self.print_error(f"HTTP {response.status_code}: {response.content.decode()}")
                return False
                
        except Exception as e:
            self.print_error(f"Metrics error: {str(e)}")
            return False
    
    def cleanup_test_data(self):
        """Clean up test data"""
        self.print_step("CLEANUP", "CLEANING UP TEST DATA")
        
        try:
            # Delete test agent
            if self.test_data.get('agent_id'):
                DeliveryAgentProfile.objects.filter(agent_id=self.test_data['agent_id']).delete()
            
            # Delete test admin
            User.objects.filter(user_name='admin_test').delete()
            
            self.print_success("Test data cleaned up successfully")
            return True
            
        except Exception as e:
            self.print_error(f"Cleanup error: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        self.print_header("🚚 CLEAN DELIVERY AGENT SYSTEM TEST")
        print("Testing hybrid registration system (self-registration + admin approval)")
        
        tests = [
            self.test_agent_self_registration,
            self.test_application_status_check,
            self.test_admin_approval_workflow,
            self.test_agent_login_and_dashboard,
            self.test_performance_metrics
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        # Cleanup
        self.cleanup_test_data()
        
        # Summary
        self.print_header("TEST SUMMARY")
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("✅ All tests passed! Clean Delivery Agent System is working perfectly.")
        else:
            print("⚠️  Some tests failed. Please check the errors above.")
        
        return passed == total

if __name__ == '__main__':
    tester = CleanDeliverySystemTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 Clean Delivery Agent System is ready for production!")
    else:
        print("\n❌ Clean Delivery Agent System has issues that need to be fixed.")
