import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';
import { cn } from '../../utils/cn';

// Enhanced Input with validation
export const ValidatedInput = ({
  label,
  type = 'text',
  value,
  onChange,
  onBlur,
  placeholder,
  required = false,
  validation = {},
  error,
  success,
  disabled = false,
  className = "",
  showPasswordToggle = false,
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [touched, setTouched] = useState(false);
  const [localError, setLocalError] = useState('');

  const inputType = type === 'password' && showPassword ? 'text' : type;

  // Real-time validation
  useEffect(() => {
    if (touched && value !== undefined) {
      const validationError = validateField(value, validation, required);
      setLocalError(validationError);
    }
  }, [value, validation, required, touched]);

  const handleBlur = (e) => {
    setTouched(true);
    onBlur?.(e);
  };

  const displayError = error || localError;
  const hasError = touched && displayError;
  const hasSuccess = touched && !displayError && value && required;

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        <input
          type={inputType}
          value={value || ''}
          onChange={onChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            'w-full px-3 py-2 border rounded-md shadow-sm transition-colors',
            'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
            hasError && 'border-red-500 focus:ring-red-500 focus:border-red-500',
            hasSuccess && 'border-green-500 focus:ring-green-500 focus:border-green-500',
            !hasError && !hasSuccess && 'border-gray-300',
            disabled && 'bg-gray-100 cursor-not-allowed'
          )}
          {...props}
        />

        {/* Password toggle */}
        {showPasswordToggle && type === 'password' && (
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
          </button>
        )}

        {/* Status icons */}
        {!showPasswordToggle && (hasError || hasSuccess) && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            {hasError ? (
              <AlertCircle size={16} className="text-red-500" />
            ) : (
              <CheckCircle size={16} className="text-green-500" />
            )}
          </div>
        )}
      </div>

      {/* Error/Success message */}
      {hasError && (
        <p className="text-sm text-red-600 flex items-center">
          <AlertCircle size={14} className="mr-1 flex-shrink-0" />
          {displayError}
        </p>
      )}
      
      {hasSuccess && (
        <p className="text-sm text-green-600 flex items-center">
          <CheckCircle size={14} className="mr-1 flex-shrink-0" />
          Looks good!
        </p>
      )}
    </div>
  );
};

// Validation rules
export const validationRules = {
  required: (value) => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      return 'This field is required';
    }
    return '';
  },

  email: (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (value && !emailRegex.test(value)) {
      return 'Please enter a valid email address';
    }
    return '';
  },

  phone: (value) => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (value && !phoneRegex.test(value.replace(/\s/g, ''))) {
      return 'Please enter a valid phone number';
    }
    return '';
  },

  password: (value) => {
    if (!value) return '';
    
    const errors = [];
    if (value.length < 8) errors.push('at least 8 characters');
    if (!/[A-Z]/.test(value)) errors.push('one uppercase letter');
    if (!/[a-z]/.test(value)) errors.push('one lowercase letter');
    if (!/\d/.test(value)) errors.push('one number');
    
    if (errors.length > 0) {
      return `Password must contain ${errors.join(', ')}`;
    }
    return '';
  },

  confirmPassword: (value, originalPassword) => {
    if (value && value !== originalPassword) {
      return 'Passwords do not match';
    }
    return '';
  },

  minLength: (min) => (value) => {
    if (value && value.length < min) {
      return `Must be at least ${min} characters long`;
    }
    return '';
  },

  maxLength: (max) => (value) => {
    if (value && value.length > max) {
      return `Must be no more than ${max} characters long`;
    }
    return '';
  },

  pattern: (regex, message) => (value) => {
    if (value && !regex.test(value)) {
      return message || 'Invalid format';
    }
    return '';
  }
};

// Validate a single field
export const validateField = (value, rules, required = false) => {
  // Check required first
  if (required) {
    const requiredError = validationRules.required(value);
    if (requiredError) return requiredError;
  }

  // Skip other validations if field is empty and not required
  if (!value && !required) return '';

  // Run validation rules
  for (const rule of Object.keys(rules)) {
    const ruleConfig = rules[rule];
    let validator;

    if (typeof ruleConfig === 'function') {
      validator = ruleConfig;
    } else if (typeof ruleConfig === 'boolean' && ruleConfig) {
      validator = validationRules[rule];
    } else if (typeof ruleConfig === 'object') {
      validator = validationRules[rule](...Object.values(ruleConfig));
    }

    if (validator) {
      const error = validator(value);
      if (error) return error;
    }
  }

  return '';
};

// Form validation hook
export const useFormValidation = (initialValues = {}, validationSchema = {}) => {
  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateForm = () => {
    const newErrors = {};
    
    Object.keys(validationSchema).forEach(field => {
      const fieldConfig = validationSchema[field];
      const value = values[field];
      const error = validateField(value, fieldConfig.rules || {}, fieldConfig.required);
      
      if (error) {
        newErrors[field] = error;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (field) => (e) => {
    const value = e.target.value;
    setValues(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleBlur = (field) => () => {
    setTouched(prev => ({ ...prev, [field]: true }));
    
    // Validate field on blur
    const fieldConfig = validationSchema[field];
    if (fieldConfig) {
      const error = validateField(values[field], fieldConfig.rules || {}, fieldConfig.required);
      setErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const handleSubmit = async (onSubmit) => {
    setIsSubmitting(true);
    
    // Mark all fields as touched
    const allTouched = {};
    Object.keys(validationSchema).forEach(field => {
      allTouched[field] = true;
    });
    setTouched(allTouched);

    // Validate form
    const isValid = validateForm();
    
    if (isValid) {
      try {
        await onSubmit(values);
      } catch (error) {
        console.error('Form submission error:', error);
      }
    }
    
    setIsSubmitting(false);
    return isValid;
  };

  const reset = () => {
    setValues(initialValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  };

  return {
    values,
    errors,
    touched,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    validateForm,
    reset,
    isValid: Object.keys(errors).length === 0
  };
};

export default ValidatedInput;
