/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,jsx}'],
  theme: {
    extend: {
      fontFamily: {
        'poppins': ['Poppins', 'sans-serif'],
        'inter': ['Inter', 'sans-serif'],
      },
      colors: {
        primary: {
          DEFAULT: '#FF6B00',
          50: '#FFF2E5',
          100: '#FFE5CC',
          200: '#FFD1A3',
          300: '#FFBD7A',
          400: '#FFA952',
          500: '#FF6B00',
          600: '#CC5600',
          700: '#994000',
          800: '#662B00',
          900: '#331500',
        },
        accent: {
          red: {
            DEFAULT: '#D32F2F',
            light: '#EF5350',
            dark: '#B71C1C',
          },
          green: {
            DEFAULT: '#388E3C',
            light: '#66BB6A',
            dark: '#1B5E20',
          },
        },
        background: {
          DEFAULT: '#FFFFFF',
          light: '#F5F5F5',
          dark: '#EEEEEE',
        },
        text: {
          primary: '#212121',
          secondary: '#757575',
          light: '#FAFAFA',
        }
      },
      boxShadow: {
        'card': '0 2px 8px rgba(0, 0, 0, 0.1)',
        'nav': '0 2px 10px rgba(0, 0, 0, 0.05)',
        'button': '0 4px 6px rgba(255, 107, 0, 0.25)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'slide-down': 'slideDown 0.5s ease-out',
        'pulse-light': 'pulseLight 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        pulseLight: {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.7' },
        },
      },
    },
  },
  plugins: [],
};