import React, { useState } from "react";
import { authApi } from "../../utils/authApi";
import Button from "../../components/common/Button";
import { AlertCircle, CheckCircle, Mail, RefreshCw } from "lucide-react";

const OtpTroubleshoot = () => {
  const [email, setEmail] = useState("<EMAIL>");
  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState({});
  const [results, setResults] = useState({});

  const testRegistration = async () => {
    setLoading((prev) => ({ ...prev, register: true }));
    try {
      const result = await authApi.register({
        name: "OTP Test User",
        user_name: "otptest" + Date.now(),
        phone: "+1234567890",
        email: email,
        password: "123456",
        role: "customer",
      });
      setResults((prev) => ({ ...prev, register: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        register: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, register: false }));
  };

  const testResend = async () => {
    setLoading((prev) => ({ ...prev, resend: true }));
    try {
      const result = await authApi.resendEmailVerification({ email });
      setResults((prev) => ({ ...prev, resend: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        resend: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, resend: false }));
  };

  const testVerification = async () => {
    if (!otp.trim()) {
      alert("Please enter the OTP code");
      return;
    }

    setLoading((prev) => ({ ...prev, verify: true }));
    try {
      const result = await authApi.verifyEmail({
        email: email,
        otp: otp.trim(),
      });
      setResults((prev) => ({ ...prev, verify: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        verify: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, verify: false }));
  };

  const renderResult = (key, title) => {
    const result = results[key];
    if (!result) return null;

    return (
      <div className='mt-4 p-4 bg-gray-50 rounded-lg'>
        <h4 className='font-semibold mb-2 flex items-center gap-2'>
          {result.success ? (
            <CheckCircle size={16} className='text-green-600' />
          ) : (
            <AlertCircle size={16} className='text-red-600' />
          )}
          {title}:
        </h4>
        <div
          className={`p-3 rounded text-sm ${
            result.success
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          {result.success ? (
            <div>
              <p className='font-medium'>✅ Success!</p>
              <p>{result.message}</p>
              {result.data && (
                <pre className='mt-2 text-xs overflow-auto'>
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              )}
            </div>
          ) : (
            <div>
              <p className='font-medium'>❌ Error:</p>
              <p>{result.error}</p>
              {result.details && (
                <pre className='mt-2 text-xs overflow-auto'>
                  {JSON.stringify(result.details, null, 2)}
                </pre>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className='p-6'>
      <div className='max-w-4xl mx-auto'>
        <h1 className='text-3xl font-bold text-gray-900 mb-8'>
          OTP Troubleshooting Tool
        </h1>

        <div className='bg-white rounded-lg shadow-md p-6 mb-6'>
          <h2 className='text-xl font-semibold mb-4'>Test Configuration</h2>

          <div className='mb-4'>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Test Email Address:
            </label>
            <input
              type='email'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
              placeholder='Enter email address'
            />
            <p className='text-xs text-blue-600 mt-1'>
              ✅ All OTP codes are automatically <NAME_EMAIL>
            </p>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <Button
              onClick={testRegistration}
              loading={loading.register}
              variant='primary'
              className='flex items-center gap-2'
            >
              <Mail size={16} />
              1. Register & Send OTP
            </Button>

            <Button
              onClick={testResend}
              loading={loading.resend}
              variant='secondary'
              className='flex items-center gap-2'
            >
              <RefreshCw size={16} />
              2. Resend OTP
            </Button>

            <div className='flex gap-2'>
              <input
                type='text'
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                placeholder='Enter OTP'
                className='flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                maxLength={6}
              />
              <Button
                onClick={testVerification}
                loading={loading.verify}
                variant='success'
                className='whitespace-nowrap'
              >
                3. Verify
              </Button>
            </div>
          </div>
        </div>

        <div className='space-y-4'>
          {renderResult("register", "Registration & OTP Send")}
          {renderResult("resend", "Resend OTP")}
          {renderResult("verify", "OTP Verification")}
        </div>

        <div className='mt-8 bg-blue-50 border-l-4 border-blue-500 p-4'>
          <h3 className='font-semibold text-blue-800 mb-2'>
            API Endpoints Used:
          </h3>
          <ul className='text-blue-700 text-sm space-y-1'>
            <li>
              <strong>Register:</strong> POST /auth/register/
            </li>
            <li>
              <strong>Resend:</strong> POST /auth/resend-verification/
            </li>
            <li>
              <strong>Verify:</strong> POST /auth/verify-email/
            </li>
          </ul>
        </div>

        <div className='mt-6 bg-yellow-50 border-l-4 border-yellow-500 p-4'>
          <h3 className='font-semibold text-yellow-800 mb-2'>
            Troubleshooting Steps:
          </h3>
          <ol className='text-yellow-700 text-sm space-y-1 list-decimal list-inside'>
            <li>First, test registration to trigger OTP sending</li>
            <li>Check the email inbox (including spam folder)</li>
            <li>If no OTP received, try the resend function</li>
            <li>Enter the 6-digit OTP code and verify</li>
            <li>Check the API responses for error details</li>
          </ol>
        </div>

        <div className='mt-6 bg-red-50 border-l-4 border-red-500 p-4'>
          <h3 className='font-semibold text-red-800 mb-2'>Common Issues:</h3>
          <ul className='text-red-700 text-sm space-y-1 list-disc list-inside'>
            <li>Email not in inbox - check spam/junk folder</li>
            <li>OTP expired - request a new one using resend</li>
            <li>Wrong email format - ensure valid email address</li>
            <li>Network issues - check internet connection</li>
            <li>Backend issues - check API response for error details</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default OtpTroubleshoot;
