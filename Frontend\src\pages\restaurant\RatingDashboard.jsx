import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import { ratingApi } from "../../utils/ratingApi";
import RatingDisplay from "../../components/rating/RatingDisplay";
import RatingBreakdown from "../../components/rating/RatingBreakdown";
import {
  Star,
  TrendingUp,
  MessageSquare,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Eye,
  BarChart3,
  Users,
  AlertCircle,
  CheckCircle,
  Clock,
  Target,
} from "lucide-react";

const RatingDashboard = () => {
  const { user } = useAuth();
  const [ratings, setRatings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [filterRating, setFilterRating] = useState("all");
  const [sortBy, setSortBy] = useState("date");
  const [selectedRating, setSelectedRating] = useState(null);
  const [showModal, setShowModal] = useState(false);

  // Statistics
  const [stats, setStats] = useState({
    totalRatings: 0,
    averageRating: 0,
    averageFoodRating: 0,
    averageDeliveryRating: 0,
    ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
  });

  useEffect(() => {
    // Only load ratings if user is a restaurant owner
    if (user?.role === "restaurant") {
      loadRatings();
    } else {
      setLoading(false);
      setError("Only restaurant owners can view ratings dashboard.");
    }
  }, [user]);

  const loadRatings = async () => {
    // Double-check user role before making API call
    if (!user || user.role !== "restaurant") {
      setError("Only restaurant owners can view ratings dashboard.");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log(
        "🔄 RatingDashboard: Loading restaurant ratings for user:",
        user?.user_name || user?.name
      );
      const ratingsData = await ratingApi.getMyRestaurantRatings();
      setRatings(ratingsData);
      calculateStats(ratingsData);
    } catch (err) {
      console.error("Error loading ratings:", err);
      setError("Failed to load ratings. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadRatings();
    setRefreshing(false);
  };

  const calculateStats = (ratingsData) => {
    if (!ratingsData || ratingsData.length === 0) {
      setStats({
        totalRatings: 0,
        averageRating: 0,
        averageFoodRating: 0,
        averageDeliveryRating: 0,
        ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
      });
      return;
    }

    const totalRatings = ratingsData.length;
    const avgOverall =
      ratingsData.reduce((sum, r) => sum + r.overall_rating, 0) / totalRatings;
    const avgFood =
      ratingsData.reduce((sum, r) => sum + r.food_rating, 0) / totalRatings;
    const avgDelivery =
      ratingsData.reduce((sum, r) => sum + r.delivery_rating, 0) / totalRatings;

    const distribution = { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 };
    ratingsData.forEach((rating) => {
      distribution[rating.overall_rating]++;
    });

    setStats({
      totalRatings,
      averageRating: avgOverall,
      averageFoodRating: avgFood,
      averageDeliveryRating: avgDelivery,
      ratingDistribution: distribution,
    });
  };

  const filteredRatings = ratings
    .filter((rating) => {
      if (filterRating === "all") return true;
      return rating.overall_rating === parseInt(filterRating);
    })
    .sort((a, b) => {
      if (sortBy === "date") {
        return new Date(b.created_at) - new Date(a.created_at);
      } else if (sortBy === "rating") {
        return b.overall_rating - a.overall_rating;
      }
      return 0;
    });

  const renderStars = (rating) => {
    return (
      <div className='flex items-center'>
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={16}
            className={`${
              star <= rating ? "text-yellow-400 fill-current" : "text-gray-300"
            }`}
          />
        ))}
        <span className='ml-1 text-sm text-gray-600'>{rating.toFixed(1)}</span>
      </div>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className='p-6'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-6'></div>
          <div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className='bg-white p-6 rounded-lg shadow'>
                <div className='h-4 bg-gray-200 rounded w-3/4 mb-2'></div>
                <div className='h-8 bg-gray-200 rounded w-1/2'></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='p-6 max-w-7xl mx-auto'>
      {/* Header */}
      <div className='flex justify-between items-center mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>Rating Dashboard</h1>
          <p className='text-gray-600 mt-1'>
            Monitor and analyze customer feedback
          </p>
        </div>
        <div className='flex items-center space-x-3'>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className='flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50'
          >
            <RefreshCw size={16} className={refreshing ? "animate-spin" : ""} />
            <span>Refresh</span>
          </button>
          <button className='flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700'>
            <Download size={16} />
            <span>Export</span>
          </button>
        </div>
      </div>

      {error && (
        <div className='bg-red-50 border border-red-200 rounded-lg p-4 mb-6'>
          <p className='text-red-800'>{error}</p>
        </div>
      )}

      {/* Statistics Cards */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
        <div className='bg-white p-6 rounded-lg shadow'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Total Ratings</p>
              <p className='text-2xl font-bold text-gray-900'>
                {stats.totalRatings}
              </p>
            </div>
            <MessageSquare className='text-blue-600' size={24} />
          </div>
        </div>

        <div className='bg-white p-6 rounded-lg shadow'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>
                Average Rating
              </p>
              <div className='flex items-center space-x-2'>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.averageRating.toFixed(1)}
                </p>
                {renderStars(stats.averageRating)}
              </div>
            </div>
            <Star className='text-yellow-500' size={24} />
          </div>
        </div>

        <div className='bg-white p-6 rounded-lg shadow'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Food Quality</p>
              <div className='flex items-center space-x-2'>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.averageFoodRating.toFixed(1)}
                </p>
                {renderStars(stats.averageFoodRating)}
              </div>
            </div>
            <TrendingUp className='text-green-600' size={24} />
          </div>
        </div>

        <div className='bg-white p-6 rounded-lg shadow'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>
                Delivery Service
              </p>
              <div className='flex items-center space-x-2'>
                <p className='text-2xl font-bold text-gray-900'>
                  {stats.averageDeliveryRating.toFixed(1)}
                </p>
                {renderStars(stats.averageDeliveryRating)}
              </div>
            </div>
            <Users className='text-purple-600' size={24} />
          </div>
        </div>
      </div>

      {/* Rating Distribution Chart */}
      <div className='bg-white p-6 rounded-lg shadow mb-8'>
        <h3 className='text-lg font-semibold text-gray-900 mb-4'>
          Rating Distribution
        </h3>
        <div className='space-y-3'>
          {[5, 4, 3, 2, 1].map((stars) => {
            const count = stats.ratingDistribution[stars];
            const percentage =
              stats.totalRatings > 0 ? (count / stats.totalRatings) * 100 : 0;

            return (
              <div key={stars} className='flex items-center space-x-3'>
                <div className='flex items-center space-x-1 w-16'>
                  <span className='text-sm font-medium'>{stars}</span>
                  <Star size={14} className='text-yellow-400 fill-current' />
                </div>
                <div className='flex-1 bg-gray-200 rounded-full h-3'>
                  <div
                    className='bg-yellow-400 h-3 rounded-full transition-all duration-300'
                    style={{ width: `${percentage}%` }}
                  ></div>
                </div>
                <span className='text-sm text-gray-600 w-12'>{count}</span>
                <span className='text-sm text-gray-500 w-12'>
                  {percentage.toFixed(1)}%
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Filters and Controls */}
      <div className='bg-white p-4 rounded-lg shadow mb-6'>
        <div className='flex flex-wrap items-center justify-between gap-4'>
          <div className='flex items-center space-x-4'>
            <div className='flex items-center space-x-2'>
              <Filter size={16} className='text-gray-500' />
              <select
                value={filterRating}
                onChange={(e) => setFilterRating(e.target.value)}
                className='border border-gray-300 rounded px-3 py-1 text-sm'
              >
                <option value='all'>All Ratings</option>
                <option value='5'>5 Stars</option>
                <option value='4'>4 Stars</option>
                <option value='3'>3 Stars</option>
                <option value='2'>2 Stars</option>
                <option value='1'>1 Star</option>
              </select>
            </div>

            <div className='flex items-center space-x-2'>
              <BarChart3 size={16} className='text-gray-500' />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className='border border-gray-300 rounded px-3 py-1 text-sm'
              >
                <option value='date'>Sort by Date</option>
                <option value='rating'>Sort by Rating</option>
              </select>
            </div>
          </div>

          <div className='text-sm text-gray-600'>
            Showing {filteredRatings.length} of {ratings.length} ratings
          </div>
        </div>
      </div>

      {/* Ratings List */}
      <div className='bg-white rounded-lg shadow overflow-hidden'>
        {filteredRatings.length === 0 ? (
          <div className='p-8 text-center'>
            <MessageSquare className='mx-auto mb-4 text-gray-400' size={48} />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              No ratings found
            </h3>
            <p className='text-gray-600'>
              {filterRating !== "all"
                ? "Try adjusting your filter to see more ratings."
                : "Ratings from customers will appear here when they rate their orders."}
            </p>
          </div>
        ) : (
          <div className='divide-y divide-gray-200'>
            {filteredRatings.map((rating) => (
              <div
                key={rating.id}
                className='p-6 hover:bg-gray-50 transition-colors'
              >
                <div className='flex justify-between items-start'>
                  <div className='flex-1'>
                    <div className='flex items-center justify-between mb-2'>
                      <div className='flex items-center space-x-3'>
                        <h4 className='font-medium text-gray-900'>
                          Order #{rating.order}
                        </h4>
                        <span className='text-sm text-gray-500'>
                          by {rating.customer_name}
                        </span>
                        <span className='text-sm text-gray-500'>
                          ${rating.order_total}
                        </span>
                      </div>
                      <div className='flex items-center space-x-2'>
                        <Calendar size={14} className='text-gray-400' />
                        <span className='text-sm text-gray-500'>
                          {formatDate(rating.created_at)}
                        </span>
                      </div>
                    </div>

                    <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-3'>
                      <div className='flex items-center space-x-2'>
                        <span className='text-sm text-gray-600'>Food:</span>
                        {renderStars(rating.food_rating)}
                      </div>
                      <div className='flex items-center space-x-2'>
                        <span className='text-sm text-gray-600'>Delivery:</span>
                        {renderStars(rating.delivery_rating)}
                      </div>
                      <div className='flex items-center space-x-2'>
                        <span className='text-sm text-gray-600'>Overall:</span>
                        {renderStars(rating.overall_rating)}
                      </div>
                    </div>

                    {rating.review_text && (
                      <div className='bg-gray-50 p-3 rounded-lg'>
                        <p className='text-gray-700 text-sm leading-relaxed'>
                          "{rating.review_text}"
                        </p>
                      </div>
                    )}
                  </div>

                  <button
                    onClick={() => {
                      setSelectedRating(rating);
                      setShowModal(true);
                    }}
                    className='ml-4 flex items-center space-x-1 px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded'
                  >
                    <Eye size={14} />
                    <span>View</span>
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RatingDashboard;
