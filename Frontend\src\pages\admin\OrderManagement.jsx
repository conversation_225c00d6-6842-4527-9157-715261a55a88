import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  MapPin,
  Phone,
  Mail,
  Clock,
  Package,
  AlertCircle,
  CheckCircle,
  XCircle,
  DollarSign,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { useAuth } from "../../context/AuthContext";

function OrderManagement() {
  const { user: currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      // In a real app, this would be an API call
      const mockOrders = [
        {
          id: "ORD001",
          customer: {
            name: "Ahmad Khan",
            email: "<EMAIL>",
            phone: "+93 70 123 4567",
            address: "Kabul, District 1",
          },
          restaurant: {
            name: "Kabul Kitchen",
            address: "Kabul, District 1, Street 5",
          },
          items: [
            { name: "Kabuli Pulao", quantity: 2, price: 15.99 },
            { name: "Mantoo", quantity: 1, price: 12.99 },
          ],
          total: 44.97,
          status: "delivered",
          paymentMethod: "cash",
          paymentStatus: "paid",
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
          deliveryAgent: {
            name: "Mohammad Ali",
            phone: "+93 70 234 5678",
          },
        },
        {
          id: "ORD002",
          customer: {
            name: "Fatima Zahra",
            email: "<EMAIL>",
            phone: "+93 70 345 6789",
            address: "Herat, District 3",
          },
          restaurant: {
            name: "Herat Delights",
            address: "Herat, District 3, Street 12",
          },
          items: [
            { name: "Qabeli Palaw", quantity: 1, price: 16.99 },
            { name: "Kebab", quantity: 2, price: 14.99 },
          ],
          total: 46.97,
          status: "preparing",
          paymentMethod: "card",
          paymentStatus: "paid",
          createdAt: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
          deliveryAgent: null,
        },
        {
          id: "ORD003",
          customer: {
            name: "Karim Ahmad",
            email: "<EMAIL>",
            phone: "+93 70 456 7890",
            address: "Mazar-e-Sharif, District 2",
          },
          restaurant: {
            name: "Mazar Restaurant",
            address: "Mazar-e-Sharif, District 2, Street 8",
          },
          items: [
            { name: "Ashak", quantity: 1, price: 13.99 },
            { name: "Boulani", quantity: 2, price: 8.99 },
          ],
          total: 31.97,
          status: "cancelled",
          paymentMethod: "card",
          paymentStatus: "refunded",
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
          deliveryAgent: null,
        },
      ];

      setOrders(mockOrders);
      setFilteredOrders(mockOrders);
    } catch (error) {
      console.error("Error fetching orders:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let filtered = [...orders];

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((o) => o.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (o) =>
          o.id.toLowerCase().includes(query) ||
          o.customer.name.toLowerCase().includes(query) ||
          o.restaurant.name.toLowerCase().includes(query)
      );
    }

    setFilteredOrders(filtered);
  }, [orders, statusFilter, searchQuery]);

  const handleUpdateStatus = async (orderId, newStatus) => {
    try {
      // In a real app, this would be an API call
      setOrders(
        orders.map((o) => (o.id === orderId ? { ...o, status: newStatus } : o))
      );
      setShowDetails(false);
    } catch (error) {
      console.error("Error updating order status:", error);
    }
  };

  const handleUpdatePaymentStatus = async (orderId, newStatus) => {
    try {
      // In a real app, this would be an API call
      setOrders(
        orders.map((o) =>
          o.id === orderId ? { ...o, paymentStatus: newStatus } : o
        )
      );
      setShowDetails(false);
    } catch (error) {
      console.error("Error updating payment status:", error);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "pending":
        return <Badge className='bg-yellow-100 text-yellow-800'>Pending</Badge>;
      case "preparing":
        return <Badge className='bg-blue-100 text-blue-800'>Preparing</Badge>;
      case "ready":
        return <Badge className='bg-purple-100 text-purple-800'>Ready</Badge>;
      case "delivered":
        return <Badge className='bg-green-100 text-green-800'>Delivered</Badge>;
      case "cancelled":
        return <Badge className='bg-red-100 text-red-800'>Cancelled</Badge>;
      default:
        return null;
    }
  };

  const getPaymentStatusBadge = (status) => {
    switch (status) {
      case "paid":
        return <Badge className='bg-green-100 text-green-800'>Paid</Badge>;
      case "pending":
        return <Badge className='bg-yellow-100 text-yellow-800'>Pending</Badge>;
      case "refunded":
        return <Badge className='bg-blue-100 text-blue-800'>Refunded</Badge>;
      case "failed":
        return <Badge className='bg-red-100 text-red-800'>Failed</Badge>;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500'></div>
      </div>
    );
  }

  return (
    <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8'>
      <div className='mb-8'>
        <h1 className='text-3xl font-bold text-gray-900'>Order Management</h1>
        <p className='mt-2 text-sm text-gray-600'>
          Manage and track all orders across your platform
        </p>
      </div>

      {/* Filters Section */}
      <div className='bg-white rounded-xl shadow-sm p-6 mb-8'>
        <div className='flex flex-col md:flex-row md:items-center md:justify-between gap-4'>
          <div className='relative flex-1 max-w-md'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={20}
            />
            <input
              type='text'
              placeholder='Search by order ID, customer name, or restaurant...'
              className='w-full pl-10 pr-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className='flex items-center gap-4'>
            <select
              className='px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-white'
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value='all'>All Status</option>
              <option value='pending'>Pending</option>
              <option value='preparing'>Preparing</option>
              <option value='ready'>Ready</option>
              <option value='delivered'>Delivered</option>
              <option value='cancelled'>Cancelled</option>
            </select>
          </div>
        </div>
      </div>

      {/* Order List */}
      <div className='grid gap-6'>
        {filteredOrders.map((order) => (
          <Card
            key={order.id}
            className='hover:shadow-md transition-shadow duration-200'
          >
            <div className='p-6'>
              <div className='flex flex-col md:flex-row md:items-start md:justify-between gap-4'>
                <div className='flex-1'>
                  <div className='flex items-center gap-3 mb-4'>
                    <h3 className='text-lg font-semibold text-gray-900'>
                      Order #{order.id}
                    </h3>
                    <div className='flex items-center gap-2'>
                      {getStatusBadge(order.status)}
                      {getPaymentStatusBadge(order.paymentStatus)}
                    </div>
                  </div>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div className='space-y-3'>
                      <div className='flex items-center text-sm text-gray-600'>
                        <Package size={16} className='mr-2 text-gray-400' />
                        <span className='font-medium'>
                          {order.restaurant.name}
                        </span>
                      </div>
                      <div className='flex items-center text-sm text-gray-600'>
                        <MapPin size={16} className='mr-2 text-gray-400' />
                        <span>{order.customer.address}</span>
                      </div>
                    </div>
                    <div className='space-y-3'>
                      <div className='flex items-center text-sm text-gray-600'>
                        <DollarSign size={16} className='mr-2 text-gray-400' />
                        <span className='font-medium'>
                          {formatCurrency(order.total)}
                        </span>
                        <span className='ml-2 text-gray-400'>
                          ({order.paymentMethod})
                        </span>
                      </div>
                      <div className='flex items-center text-sm text-gray-600'>
                        <Clock size={16} className='mr-2 text-gray-400' />
                        <span>
                          {new Date(order.createdAt).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className='flex items-center gap-3'>
                  <Button
                    variant='outline'
                    size='small'
                    onClick={() => {
                      setSelectedOrder(order);
                      setShowDetails(true);
                    }}
                    className='whitespace-nowrap'
                  >
                    View Details
                  </Button>
                  {order.status !== "delivered" &&
                    order.status !== "cancelled" && (
                      <select
                        className='px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-white'
                        value={order.status}
                        onChange={(e) =>
                          handleUpdateStatus(order.id, e.target.value)
                        }
                      >
                        <option value='pending'>Pending</option>
                        <option value='preparing'>Preparing</option>
                        <option value='ready'>Ready</option>
                        <option value='delivered'>Delivered</option>
                        <option value='cancelled'>Cancelled</option>
                      </select>
                    )}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Order Details Modal */}
      {showDetails && selectedOrder && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-auto'>
            <div className='sticky top-0 bg-white border-b z-10'>
              <div className='p-6'>
                <div className='flex justify-between items-start'>
                  <div>
                    <h2 className='text-2xl font-semibold text-gray-900'>
                      Order #{selectedOrder.id}
                    </h2>
                    <div className='flex items-center mt-2'>
                      <Clock size={16} className='text-gray-400 mr-2' />
                      <span className='text-sm text-gray-500'>
                        Placed{" "}
                        {new Date(selectedOrder.createdAt).toLocaleString()}
                      </span>
                    </div>
                  </div>
                  <button
                    className='text-gray-400 hover:text-gray-600 transition-colors duration-200'
                    onClick={() => setShowDetails(false)}
                  >
                    <XCircle size={24} />
                  </button>
                </div>
              </div>
            </div>

            <div className='p-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
                <div className='space-y-6'>
                  <div className='bg-gray-50 rounded-lg p-6'>
                    <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                      Order Information
                    </h3>
                    <div className='space-y-4'>
                      <div>
                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                          Status
                        </label>
                        <select
                          className='w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-white'
                          value={selectedOrder.status}
                          onChange={(e) =>
                            setSelectedOrder({
                              ...selectedOrder,
                              status: e.target.value,
                            })
                          }
                          disabled={
                            selectedOrder.status === "delivered" ||
                            selectedOrder.status === "cancelled"
                          }
                        >
                          <option value='pending'>Pending</option>
                          <option value='preparing'>Preparing</option>
                          <option value='ready'>Ready</option>
                          <option value='delivered'>Delivered</option>
                          <option value='cancelled'>Cancelled</option>
                        </select>
                      </div>
                      <div>
                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                          Payment Status
                        </label>
                        <select
                          className='w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-white'
                          value={selectedOrder.paymentStatus}
                          onChange={(e) =>
                            setSelectedOrder({
                              ...selectedOrder,
                              paymentStatus: e.target.value,
                            })
                          }
                        >
                          <option value='paid'>Paid</option>
                          <option value='pending'>Pending</option>
                          <option value='refunded'>Refunded</option>
                          <option value='failed'>Failed</option>
                        </select>
                      </div>
                      <div>
                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                          Payment Method
                        </label>
                        <div className='px-3 py-2 border border-gray-200 rounded-lg bg-white'>
                          {selectedOrder.paymentMethod}
                        </div>
                      </div>
                      <div>
                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                          Total Amount
                        </label>
                        <div className='px-3 py-2 border border-gray-200 rounded-lg bg-white font-medium'>
                          {formatCurrency(selectedOrder.total)}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className='bg-gray-50 rounded-lg p-6'>
                    <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                      Customer Information
                    </h3>
                    <div className='space-y-4'>
                      <div>
                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                          Name
                        </label>
                        <div className='px-3 py-2 border border-gray-200 rounded-lg bg-white'>
                          {selectedOrder.customer.name}
                        </div>
                      </div>
                      <div>
                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                          Contact
                        </label>
                        <div className='space-y-2'>
                          <div className='px-3 py-2 border border-gray-200 rounded-lg bg-white'>
                            {selectedOrder.customer.email}
                          </div>
                          <div className='px-3 py-2 border border-gray-200 rounded-lg bg-white'>
                            {selectedOrder.customer.phone}
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className='block text-sm font-medium text-gray-700 mb-1'>
                          Delivery Address
                        </label>
                        <div className='px-3 py-2 border border-gray-200 rounded-lg bg-white'>
                          {selectedOrder.customer.address}
                        </div>
                      </div>
                    </div>
                  </div>

                  {selectedOrder.deliveryAgent && (
                    <div className='bg-gray-50 rounded-lg p-6'>
                      <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                        Delivery Agent
                      </h3>
                      <div className='space-y-4'>
                        <div>
                          <label className='block text-sm font-medium text-gray-700 mb-1'>
                            Name
                          </label>
                          <div className='px-3 py-2 border border-gray-200 rounded-lg bg-white'>
                            {selectedOrder.deliveryAgent.name}
                          </div>
                        </div>
                        <div>
                          <label className='block text-sm font-medium text-gray-700 mb-1'>
                            Phone
                          </label>
                          <div className='px-3 py-2 border border-gray-200 rounded-lg bg-white'>
                            {selectedOrder.deliveryAgent.phone}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className='space-y-6'>
                  <div className='bg-gray-50 rounded-lg p-6'>
                    <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                      Order Items
                    </h3>
                    <div className='overflow-hidden rounded-lg border border-gray-200'>
                      <table className='min-w-full divide-y divide-gray-200'>
                        <thead className='bg-white'>
                          <tr>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Item
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Quantity
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Price
                            </th>
                            <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                              Total
                            </th>
                          </tr>
                        </thead>
                        <tbody className='bg-white divide-y divide-gray-200'>
                          {selectedOrder.items.map((item, index) => (
                            <tr key={index} className='hover:bg-gray-50'>
                              <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                                {item.name}
                              </td>
                              <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                                {item.quantity}
                              </td>
                              <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900'>
                                {formatCurrency(item.price)}
                              </td>
                              <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium'>
                                {formatCurrency(item.price * item.quantity)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>

              <div className='mt-8 pt-6 border-t flex justify-end'>
                <Button
                  variant='primary'
                  onClick={() => {
                    handleUpdateStatus(selectedOrder.id, selectedOrder.status);
                    handleUpdatePaymentStatus(
                      selectedOrder.id,
                      selectedOrder.paymentStatus
                    );
                  }}
                  className='px-6'
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default OrderManagement;
