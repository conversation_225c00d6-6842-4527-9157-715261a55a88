# customer/urls.py
from django.urls import path
from . import views

urlpatterns = [
    # Favorites endpoints
    path('favorites/', views.CustomerFavoritesListView.as_view(), name='customer-favorites-list'),
    path('favorites/add/', views.add_to_favorites, name='add-to-favorites'),
    path('favorites/remove/<int:restaurant_id>/', views.remove_from_favorites, name='remove-from-favorites'),
    path('favorites/check/<int:restaurant_id>/', views.check_favorite_status, name='check-favorite-status'),
    path('favorites/toggle/', views.toggle_favorite, name='toggle-favorite'),
    path('favorites/clear/', views.clear_all_favorites, name='clear-all-favorites'),
]
