import React from 'react';
import { Calendar, Clock, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useScheduling } from '../../context/SchedulingContext';
import Card from '../common/Card';
import Button from '../common/Button';
import Badge from '../common/Badge';

const UpcomingScheduled = ({ limit = 3 }) => {
  const { getUpcomingOrders, getTodaysScheduledOrders } = useScheduling();
  
  const upcomingOrders = getUpcomingOrders(limit);
  const todaysOrders = getTodaysScheduledOrders();

  const formatTime = (dateTime) => {
    const date = new Date(dateTime);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    let dateStr;
    if (diffDays === 0) {
      dateStr = 'Today';
    } else if (diffDays === 1) {
      dateStr = 'Tomorrow';
    } else if (diffDays < 7) {
      dateStr = date.toLocaleDateString('en-US', { weekday: 'short' });
    } else {
      dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }

    const timeStr = date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });

    return `${dateStr} at ${timeStr}`;
  };

  if (upcomingOrders.length === 0 && todaysOrders.length === 0) {
    return (
      <Card className="text-center py-8">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Calendar size={24} className="text-gray-400" />
        </div>
        <h3 className="font-semibold mb-2">No Scheduled Orders</h3>
        <p className="text-gray-600 text-sm mb-4">
          Schedule your next order for convenience
        </p>
        <Button variant="primary" size="small" to="/restaurants">
          Browse Restaurants
        </Button>
      </Card>
    );
  }

  return (
    <Card>
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold flex items-center">
          <Calendar size={18} className="mr-2 text-primary-500" />
          Scheduled Orders
        </h3>
        <Link 
          to="/scheduled-orders"
          className="text-primary-500 hover:text-primary-600 text-sm flex items-center"
        >
          View All <ArrowRight size={14} className="ml-1" />
        </Link>
      </div>

      {/* Today's Orders */}
      {todaysOrders.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Today</h4>
          <div className="space-y-2">
            {todaysOrders.slice(0, 2).map((order) => (
              <div key={order.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex-1">
                  <p className="font-medium text-sm">{order.restaurantName}</p>
                  <div className="flex items-center text-xs text-gray-600 mt-1">
                    <Clock size={12} className="mr-1" />
                    <span>{formatTime(order.scheduledFor)}</span>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant="info" size="small">Today</Badge>
                  <p className="text-xs text-gray-600 mt-1">${order.total?.toFixed(2)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upcoming Orders */}
      {upcomingOrders.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Upcoming</h4>
          <div className="space-y-2">
            {upcomingOrders.slice(0, limit - todaysOrders.length).map((order) => (
              <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <p className="font-medium text-sm">{order.restaurantName}</p>
                  <div className="flex items-center text-xs text-gray-600 mt-1">
                    <Clock size={12} className="mr-1" />
                    <span>{formatTime(order.scheduledFor)}</span>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant="secondary" size="small">Scheduled</Badge>
                  <p className="text-xs text-gray-600 mt-1">${order.total?.toFixed(2)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {(upcomingOrders.length > limit || todaysOrders.length > 2) && (
        <div className="mt-4 pt-3 border-t border-gray-100">
          <Link 
            to="/scheduled-orders"
            className="text-primary-500 hover:text-primary-600 text-sm font-medium flex items-center justify-center"
          >
            View All Scheduled Orders <ArrowRight size={14} className="ml-1" />
          </Link>
        </div>
      )}
    </Card>
  );
};

export default UpcomingScheduled;
