import { useState, useCallback, useRef } from 'react';

/**
 * Hook for handling retry logic with exponential backoff
 */
export const useRetry = ({
  maxRetries = 3,
  initialDelay = 1000,
  maxDelay = 10000,
  backoffFactor = 2,
  onRetry,
  onMaxRetriesReached,
  onSuccess,
  onError
} = {}) => {
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [lastError, setLastError] = useState(null);
  const timeoutRef = useRef(null);

  const calculateDelay = useCallback((attempt) => {
    const delay = Math.min(initialDelay * Math.pow(backoffFactor, attempt), maxDelay);
    // Add jitter to prevent thundering herd
    return delay + Math.random() * 1000;
  }, [initialDelay, backoffFactor, maxDelay]);

  const retry = useCallback(async (asyncFunction, ...args) => {
    if (retryCount >= maxRetries) {
      onMaxRetriesReached?.(lastError);
      return { success: false, error: lastError, retryCount };
    }

    setIsRetrying(true);
    
    try {
      const result = await asyncFunction(...args);
      
      // Success - reset everything
      setRetryCount(0);
      setLastError(null);
      setIsRetrying(false);
      onSuccess?.(result);
      
      return { success: true, data: result, retryCount: 0 };
    } catch (error) {
      setLastError(error);
      const newRetryCount = retryCount + 1;
      setRetryCount(newRetryCount);
      
      onError?.(error, newRetryCount);
      
      if (newRetryCount >= maxRetries) {
        setIsRetrying(false);
        onMaxRetriesReached?.(error);
        return { success: false, error, retryCount: newRetryCount };
      }

      // Schedule next retry
      const delay = calculateDelay(newRetryCount - 1);
      onRetry?.(newRetryCount, delay);
      
      return new Promise((resolve) => {
        timeoutRef.current = setTimeout(async () => {
          const result = await retry(asyncFunction, ...args);
          resolve(result);
        }, delay);
      });
    }
  }, [retryCount, maxRetries, lastError, calculateDelay, onRetry, onMaxRetriesReached, onSuccess, onError]);

  const reset = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsRetrying(false);
    setRetryCount(0);
    setLastError(null);
  }, []);

  const canRetry = retryCount < maxRetries;

  return {
    retry,
    reset,
    isRetrying,
    retryCount,
    lastError,
    canRetry,
    maxRetries
  };
};

/**
 * Hook for API calls with automatic retry
 */
export const useApiWithRetry = (apiFunction, options = {}) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const {
    retry,
    reset,
    isRetrying,
    retryCount,
    canRetry
  } = useRetry({
    maxRetries: 3,
    initialDelay: 1000,
    onRetry: (count, delay) => {
      console.log(`Retrying API call (attempt ${count}) in ${delay}ms`);
    },
    onMaxRetriesReached: (err) => {
      console.error('Max retries reached for API call:', err);
      setError(err);
      setLoading(false);
    },
    onSuccess: (result) => {
      setData(result);
      setError(null);
      setLoading(false);
    },
    onError: (err) => {
      setError(err);
    },
    ...options
  });

  const execute = useCallback(async (...args) => {
    setLoading(true);
    setError(null);
    
    const result = await retry(apiFunction, ...args);
    
    if (!result.success && !canRetry) {
      setLoading(false);
    }
    
    return result;
  }, [retry, apiFunction, canRetry]);

  const refetch = useCallback(() => {
    reset();
    return execute();
  }, [reset, execute]);

  return {
    data,
    loading: loading || isRetrying,
    error,
    execute,
    refetch,
    retryCount,
    canRetry,
    isRetrying
  };
};

/**
 * Hook for network status monitoring
 */
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [wasOffline, setWasOffline] = useState(false);

  useState(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline) {
        setWasOffline(false);
        // Trigger reconnection logic
        window.dispatchEvent(new CustomEvent('network-reconnected'));
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [wasOffline]);

  return {
    isOnline,
    wasOffline,
    isReconnecting: wasOffline && isOnline
  };
};

/**
 * Hook for handling async operations with loading states
 */
export const useAsyncOperation = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  const execute = useCallback(async (asyncFunction, ...args) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await asyncFunction(...args);
      setData(result);
      return { success: true, data: result };
    } catch (err) {
      setError(err);
      return { success: false, error: err };
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
    setData(null);
  }, []);

  return {
    loading,
    error,
    data,
    execute,
    reset
  };
};

export default useRetry;
