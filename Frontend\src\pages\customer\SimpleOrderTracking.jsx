import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  Clock,
  MapPin,
  Phone,
  Star,
  ArrowLeft,
  Package,
  Truck,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  User,
  CreditCard,
  Calendar,
  DollarSign,
  ChefHat,
  Navigation,
} from "lucide-react";
import { orderApi } from "../../utils/orderApi";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";

// Helper functions for order status
const getStatusSteps = () => [
  {
    key: "pending",
    label: "Order Placed",
    description: "Your order has been received",
    icon: <Package size={20} />,
  },
  {
    key: "confirmed",
    label: "Order Confirmed",
    description: "Restaurant has confirmed your order",
    icon: <CheckCircle size={20} />,
  },
  {
    key: "preparing",
    label: "Preparing",
    description: "Your food is being prepared",
    icon: <ChefHat size={20} />,
  },
  {
    key: "ready",
    label: "Ready for Pickup",
    description: "Order is ready for delivery",
    icon: <Package size={20} />,
  },
  {
    key: "assigned",
    label: "Assigned to Delivery",
    description: "Delivery agent has been assigned",
    icon: <User size={20} />,
  },
  {
    key: "picked_up",
    label: "Picked Up",
    description: "Order has been picked up",
    icon: <Truck size={20} />,
  },
  {
    key: "on_the_way",
    label: "On the Way",
    description: "Your order is on the way",
    icon: <Navigation size={20} />,
  },
  {
    key: "delivered",
    label: "Delivered",
    description: "Order has been delivered",
    icon: <CheckCircle size={20} />,
  },
];

const getStatusColor = (status) => {
  const colors = {
    pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    confirmed: "bg-blue-100 text-blue-800 border-blue-200",
    preparing: "bg-orange-100 text-orange-800 border-orange-200",
    ready: "bg-purple-100 text-purple-800 border-purple-200",
    assigned: "bg-indigo-100 text-indigo-800 border-indigo-200",
    picked_up: "bg-cyan-100 text-cyan-800 border-cyan-200",
    on_the_way: "bg-green-100 text-green-800 border-green-200",
    delivered: "bg-green-100 text-green-800 border-green-200",
    cancelled: "bg-red-100 text-red-800 border-red-200",
    refunded: "bg-gray-100 text-gray-800 border-gray-200",
  };
  return colors[status] || "bg-gray-100 text-gray-800 border-gray-200";
};

const getStatusIcon = (status) => {
  const icons = {
    pending: <Clock size={16} />,
    confirmed: <CheckCircle size={16} />,
    preparing: <ChefHat size={16} />,
    ready: <Package size={16} />,
    assigned: <User size={16} />,
    picked_up: <Truck size={16} />,
    on_the_way: <Navigation size={16} />,
    delivered: <CheckCircle size={16} />,
    cancelled: <AlertCircle size={16} />,
    refunded: <AlertCircle size={16} />,
  };
  return icons[status] || <Clock size={16} />;
};

const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

const formatTime = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
  });
};

const formatDateTime = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const SimpleOrderTracking = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  // Load order data
  const loadOrderData = async (isRefresh = false) => {
    if (!id) return;

    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }

    try {
      console.log("📦 Loading order:", id);
      const response = await orderApi.getOrder(id);

      if (response.success && response.data) {
        setOrder(response.data);
        setError(null);
        console.log("✅ Order loaded successfully:", response.data);
      } else {
        setError("Order not found");
        console.error("❌ Order not found");
      }
    } catch (err) {
      setError("Failed to load order details");
      console.error("❌ Error loading order:", err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Load order on component mount and when ID changes
  useEffect(() => {
    loadOrderData();
  }, [id]);

  // Refresh order data
  const handleRefresh = () => {
    loadOrderData(true);
  };

  // Get current status step index
  const getCurrentStatusIndex = (currentStatus) => {
    const steps = getStatusSteps();
    return steps.findIndex((step) => step.key === currentStatus);
  };

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto'></div>
          <p className='mt-4 text-gray-600'>Loading order details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <AlertCircle className='w-16 h-16 text-red-500 mx-auto mb-4' />
          <h2 className='text-xl font-semibold text-gray-900 mb-2'>
            Unable to Load Order
          </h2>
          <p className='text-gray-600 mb-6'>{error}</p>
          <div className='space-x-4'>
            <Button
              variant='outline'
              onClick={() => navigate("/orders")}
              icon={<ArrowLeft size={16} />}
            >
              Back to Orders
            </Button>
            <Button
              variant='primary'
              onClick={handleRefresh}
              icon={<RefreshCw size={16} />}
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <Package className='w-16 h-16 text-gray-400 mx-auto mb-4' />
          <h2 className='text-xl font-semibold text-gray-900 mb-2'>
            Order Not Found
          </h2>
          <p className='text-gray-600 mb-6'>
            The order you're looking for doesn't exist or has been removed.
          </p>
          <Button
            variant='primary'
            onClick={() => navigate("/orders")}
            icon={<ArrowLeft size={16} />}
          >
            Back to Orders
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 py-8'>
      <div className='max-w-6xl mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Header */}
        <div className='mb-8'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center'>
              <Button
                variant='ghost'
                onClick={() => navigate("/orders")}
                icon={<ArrowLeft size={20} />}
                className='mr-4'
              >
                Back to Orders
              </Button>
              <div>
                <h1 className='text-3xl font-bold text-gray-900'>
                  Order #{order.id}
                </h1>
                <p className='text-gray-600 mt-1'>
                  Placed on {formatDate(order.created_at)} at{" "}
                  {formatTime(order.created_at)}
                </p>
              </div>
            </div>
            <Button
              variant='outline'
              onClick={handleRefresh}
              loading={refreshing}
              icon={<RefreshCw size={16} />}
            >
              {refreshing ? "Refreshing..." : "Refresh"}
            </Button>
          </div>
        </div>

        {/* Order Status Timeline */}
        <Card className='mb-8'>
          <div className='p-6'>
            <div className='flex items-center justify-between mb-6'>
              <h2 className='text-xl font-semibold text-gray-900'>
                Order Status
              </h2>
              <Badge variant='primary' className={getStatusColor(order.status)}>
                <div className='flex items-center'>
                  {getStatusIcon(order.status)}
                  <span className='ml-2 capitalize'>
                    {order.status.replace("_", " ")}
                  </span>
                </div>
              </Badge>
            </div>

            {/* Status Timeline */}
            <div className='relative'>
              <div className='absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200'></div>
              {getStatusSteps().map((step, index) => {
                const currentIndex = getCurrentStatusIndex(order.status);
                const isCompleted = index <= currentIndex;
                const isCurrent = index === currentIndex;

                return (
                  <div
                    key={step.key}
                    className='relative flex items-center mb-6 last:mb-0'
                  >
                    <div
                      className={`relative z-10 flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                        isCompleted
                          ? "bg-green-500 border-green-500 text-white"
                          : isCurrent
                          ? "bg-blue-500 border-blue-500 text-white"
                          : "bg-white border-gray-300 text-gray-400"
                      }`}
                    >
                      {isCompleted ? (
                        <CheckCircle size={16} />
                      ) : (
                        React.cloneElement(step.icon, { size: 16 })
                      )}
                    </div>
                    <div className='ml-4 flex-1'>
                      <h3
                        className={`font-medium ${
                          isCompleted || isCurrent
                            ? "text-gray-900"
                            : "text-gray-500"
                        }`}
                      >
                        {step.label}
                      </h3>
                      <p
                        className={`text-sm ${
                          isCompleted || isCurrent
                            ? "text-gray-600"
                            : "text-gray-400"
                        }`}
                      >
                        {step.description}
                      </p>
                      {isCurrent && (
                        <p className='text-xs text-blue-600 mt-1 font-medium'>
                          Current Status
                        </p>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card>

        <div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
          {/* Main Content */}
          <div className='lg:col-span-2 space-y-6'>
            {/* Restaurant Information */}
            {order.restaurant && (
              <Card>
                <div className='p-6'>
                  <h2 className='text-xl font-semibold text-gray-900 mb-4'>
                    Restaurant Details
                  </h2>
                  <div className='flex items-start space-x-4'>
                    <div className='w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center'>
                      <ChefHat size={24} className='text-white' />
                    </div>
                    <div className='flex-1'>
                      <h3 className='text-lg font-semibold text-gray-900'>
                        {order.restaurant.name}
                      </h3>
                      <p className='text-gray-600 mb-2'>
                        {order.restaurant.description}
                      </p>
                      <div className='flex items-center space-x-4 text-sm text-gray-500'>
                        <div className='flex items-center'>
                          <Star size={16} className='text-yellow-400 mr-1' />
                          <span>{order.restaurant.rating || "N/A"}</span>
                        </div>
                        <div className='flex items-center'>
                          <Phone size={16} className='mr-1' />
                          <span>{order.restaurant.phone || "N/A"}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            )}

            {/* Order Items */}
            <Card>
              <div className='p-6'>
                <h2 className='text-xl font-semibold text-gray-900 mb-4'>
                  Order Items
                </h2>
                <div className='space-y-4'>
                  {order.items?.map((item, index) => (
                    <div
                      key={index}
                      className='flex items-center justify-between p-4 bg-gray-50 rounded-lg'
                    >
                      <div className='flex-1'>
                        <h3 className='font-semibold text-gray-900'>
                          {item.menu_item?.name}
                        </h3>
                        <p className='text-sm text-gray-600 mb-1'>
                          ${item.price_at_order} × {item.quantity}
                        </p>
                        {item.special_requests && (
                          <p className='text-sm text-gray-500 italic'>
                            Note: {item.special_requests}
                          </p>
                        )}
                      </div>
                      <div className='text-right'>
                        <p className='font-semibold text-gray-900'>
                          $
                          {(
                            parseFloat(item.price_at_order) * item.quantity
                          ).toFixed(2)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* Delivery Information */}
            <Card>
              <div className='p-6'>
                <h2 className='text-xl font-semibold text-gray-900 mb-4'>
                  Delivery Information
                </h2>
                <div className='space-y-4'>
                  <div className='flex items-start space-x-3'>
                    <MapPin className='w-5 h-5 text-gray-400 mt-1' />
                    <div>
                      <p className='font-medium text-gray-900'>
                        Delivery Address
                      </p>
                      {order.delivery_address &&
                      typeof order.delivery_address === "object" ? (
                        <p className='text-gray-600'>
                          {order.delivery_address.street},{" "}
                          {order.delivery_address.city},{" "}
                          {order.delivery_address.state},{" "}
                          {order.delivery_address.country}
                        </p>
                      ) : (
                        <p className='text-gray-600'>
                          Address ID: {order.delivery_address}
                        </p>
                      )}
                    </div>
                  </div>
                  {order.estimated_delivery_time && (
                    <div className='flex items-start space-x-3'>
                      <Clock className='w-5 h-5 text-gray-400 mt-1' />
                      <div>
                        <p className='font-medium text-gray-900'>
                          Estimated Delivery
                        </p>
                        <p className='text-gray-600'>
                          {formatDateTime(order.estimated_delivery_time)}
                        </p>
                      </div>
                    </div>
                  )}
                  {order.special_instructions && (
                    <div className='flex items-start space-x-3'>
                      <Package className='w-5 h-5 text-gray-400 mt-1' />
                      <div>
                        <p className='font-medium text-gray-900'>
                          Special Instructions
                        </p>
                        <p className='text-gray-600'>
                          {order.special_instructions}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className='space-y-6'>
            {/* Order Summary */}
            <Card>
              <div className='p-6'>
                <h2 className='text-xl font-semibold text-gray-900 mb-4'>
                  Order Summary
                </h2>
                <div className='space-y-3'>
                  <div className='flex justify-between text-sm'>
                    <span className='text-gray-600'>Subtotal</span>
                    <span className='font-medium'>
                      $
                      {(
                        parseFloat(order.total_amount) -
                        parseFloat(order.delivery_fee || 0) -
                        parseFloat(order.tax_amount || 0)
                      ).toFixed(2)}
                    </span>
                  </div>
                  {order.delivery_fee && (
                    <div className='flex justify-between text-sm'>
                      <span className='text-gray-600'>Delivery Fee</span>
                      <span className='font-medium'>${order.delivery_fee}</span>
                    </div>
                  )}
                  {order.tax_amount && (
                    <div className='flex justify-between text-sm'>
                      <span className='text-gray-600'>Tax</span>
                      <span className='font-medium'>${order.tax_amount}</span>
                    </div>
                  )}
                  <div className='border-t pt-3'>
                    <div className='flex justify-between'>
                      <span className='font-semibold text-gray-900'>Total</span>
                      <span className='font-semibold text-gray-900'>
                        ${order.total_amount}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Payment Information */}
            <Card>
              <div className='p-6'>
                <h2 className='text-xl font-semibold text-gray-900 mb-4'>
                  Payment Details
                </h2>
                <div className='space-y-3'>
                  <div className='flex items-center space-x-3'>
                    <CreditCard className='w-5 h-5 text-gray-400' />
                    <div>
                      <p className='font-medium text-gray-900'>
                        Payment Method
                      </p>
                      <p className='text-sm text-gray-600 capitalize'>
                        {order.payment_method?.replace("_", " ")}
                      </p>
                    </div>
                  </div>
                  <div className='flex items-center space-x-3'>
                    <DollarSign className='w-5 h-5 text-gray-400' />
                    <div>
                      <p className='font-medium text-gray-900'>
                        Payment Status
                      </p>
                      <Badge
                        variant={
                          order.payment_status === "completed"
                            ? "success"
                            : "warning"
                        }
                        className='mt-1'
                      >
                        {order.payment_status?.charAt(0).toUpperCase() +
                          order.payment_status?.slice(1)}
                      </Badge>
                    </div>
                  </div>
                  {order.transaction_id && (
                    <div className='flex items-center space-x-3'>
                      <Package className='w-5 h-5 text-gray-400' />
                      <div>
                        <p className='font-medium text-gray-900'>
                          Transaction ID
                        </p>
                        <p className='text-sm text-gray-600 font-mono'>
                          {order.transaction_id}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>

            {/* Order Timeline */}
            <Card>
              <div className='p-6'>
                <h2 className='text-xl font-semibold text-gray-900 mb-4'>
                  Order Timeline
                </h2>
                <div className='space-y-3 text-sm'>
                  <div className='flex items-center space-x-3'>
                    <Calendar className='w-4 h-4 text-gray-400' />
                    <div>
                      <p className='font-medium'>Order Placed</p>
                      <p className='text-gray-600'>
                        {formatDateTime(order.created_at)}
                      </p>
                    </div>
                  </div>
                  {order.updated_at !== order.created_at && (
                    <div className='flex items-center space-x-3'>
                      <Clock className='w-4 h-4 text-gray-400' />
                      <div>
                        <p className='font-medium'>Last Updated</p>
                        <p className='text-gray-600'>
                          {formatDateTime(order.updated_at)}
                        </p>
                      </div>
                    </div>
                  )}
                  {order.actual_delivery_time && (
                    <div className='flex items-center space-x-3'>
                      <CheckCircle className='w-4 h-4 text-green-500' />
                      <div>
                        <p className='font-medium'>Delivered</p>
                        <p className='text-gray-600'>
                          {formatDateTime(order.actual_delivery_time)}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleOrderTracking;
