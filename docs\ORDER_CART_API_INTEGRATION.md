# Order & Cart CRUD API Integration Guide

This document explains how to use the Afghan Sofra Order and Cart API integration in your React frontend.

## API Base URL
```
https://afghansufra.luilala.com/api/
```

## Authentication
All endpoints require Bearer token authentication. Tokens are automatically added to requests via the axios interceptor.

## Order API Endpoints

### 1. Create Order
**Endpoint:** `POST /order/orders/`
**Content-Type:** `application/json`
**Authentication:** <PERSON><PERSON>ken Required

**Request Body:**
```javascript
{
  "delivery_address": 5,        // Address ID (number)
  "restaurant": 2,              // Restaurant ID (number)
  "payment_method": "cash_on_delivery", // Payment method (string)
  "special_instructions": "Leave at doorstep", // Optional instructions
  "items": [
    {
      "menu_item_id": 5,         // Menu item ID (number)
      "quantity": 2              // Quantity (number)
    },
    {
      "menu_item_id": 6,
      "quantity": 2
    }
  ]
}
```

**Response:**
```javascript
{
  "id": 15,
  "delivery_address": 5,
  "restaurant": 2,
  "delivery_agent": null,
  "status": "pending",
  "total_amount": "1310.00",
  "delivery_fee": "100.00",
  "tax_amount": "110.00",
  "special_instructions": "",
  "created_at": "2025-05-19T10:52:34.000156Z",
  "updated_at": "2025-05-19T10:52:34.000156Z",
  "estimated_delivery_time": null,
  "actual_delivery_time": null,
  "payment_method": "cash_on_delivery",
  "payment_status": "pending",
  "transaction_id": "",
  "items": [
    {
      "id": 28,
      "menu_item": {
        "id": 5,
        "name": "chicken biryani",
        "price": "300.00",
        "image": "http://127.0.0.1:8000/media/menu_items/db_erd_4tNibWB.png",
        "description": "Good Pizza",
        "is_vegetarian": false,
        "is_available": true,
        "preparation_time": 30
      },
      "quantity": 2,
      "price_at_order": "300.00",
      "special_requests": ""
    }
  ]
}
```

### 2. Get All Orders
**Endpoint:** `GET /order/orders/`
**Authentication:** Bearer Token Required

### 3. Get Single Order
**Endpoint:** `GET /order/orders/{orderId}/`
**Authentication:** Bearer Token Required

### 4. Filter Orders by Status
**Endpoint:** `GET /order/orders/?status=pending`
**Authentication:** Bearer Token Required

### 5. Update Order (Full Update)
**Endpoint:** `PUT /order/orders/{orderId}/`
**Content-Type:** `application/json`
**Authentication:** Bearer Token Required

### 6. Update Order (Partial Update)
**Endpoint:** `PATCH /order/orders/{orderId}/`
**Content-Type:** `application/json`
**Authentication:** Bearer Token Required

**Example:**
```javascript
{
  "status": "delivered"
}
```

### 7. Delete Order
**Endpoint:** `DELETE /order/orders/{orderId}/`
**Authentication:** Bearer Token Required

### 8. Get Order Status History
**Endpoint:** `GET /order/orders/{orderId}/status-history/`
**Authentication:** Bearer Token Required

**Response:**
```javascript
[
  {
    "from_status": "pending",
    "to_status": "cancelled",
    "changed_by": null,
    "created_at": "2025-05-25T05:36:08.957127Z",
    "notes": ""
  }
]
```

## Cart API Endpoints

### 1. Save/Update Cart
**Endpoint:** `PUT /order/carts/mine/`
**Content-Type:** `application/json`
**Authentication:** Bearer Token Required

**Request Body:**
```javascript
{
  "restaurant_id": 2,
  "items": [
    {
      "menu_item_id": 5,
      "quantity": 3,
      "special_requests": "No spice"
    },
    {
      "menu_item_id": 6,
      "quantity": 3,
      "special_requests": "Extra sauce"
    }
  ]
}
```

### 2. Get Cart
**Endpoint:** `GET /order/carts/mine/`
**Authentication:** Bearer Token Required

### 3. Delete Cart
**Endpoint:** `DELETE /order/carts/destroy/`
**Authentication:** Bearer Token Required

## Delivery Assignment API Endpoints

### 1. Assign Order to Agent
**Endpoint:** `POST /order/delivery-assignment/assign/`
**Content-Type:** `application/json`
**Authentication:** Bearer Token Required

**Request Body:**
```javascript
{
  "order_id": 14,
  "agent_id": 15
}
```

### 2. Reject Order by Agent
**Endpoint:** `POST /order/delivery-assignment/reject/`
**Content-Type:** `application/json`
**Authentication:** Bearer Token Required

**Request Body:**
```javascript
{
  "order_id": 13,
  "reason": "Too far from location"
}
```

### 3. Get Available Orders
**Endpoint:** `GET /order/delivery-assignment/available_orders/`
**Authentication:** Bearer Token Required

## Integration Components

### 1. OrderContext Provider
Located at `src/context/OrderContext.jsx`
- Manages order state and operations
- Provides CRUD operations for orders
- Handles loading states and error management
- Automatically loads orders when user context changes

**Usage:**
```javascript
import { useOrder } from '../context/OrderContext';

const MyComponent = () => {
  const {
    orders,
    currentOrder,
    loading,
    error,
    createOrder,
    updateOrder,
    patchOrder,
    deleteOrder,
    getOrdersByStatus,
    getOrderStatusHistory,
    clearError
  } = useOrder();
  
  // Component logic here
};
```

### 2. Enhanced CartContext Provider
Located at `src/context/CartContext.jsx`
- Enhanced with API integration
- Auto-saves cart to server
- Loads cart from server on login
- Syncs local and server cart state

**New Functions:**
```javascript
const {
  cart,
  loading,
  error,
  loadCartFromAPI,
  saveCartToAPI,
  deleteCartFromAPI,
  clearError
} = useCart();
```

### 3. Updated Components

#### Checkout Component (`src/pages/customer/Checkout.jsx`)
- Integrated with real Order API
- Creates orders via API instead of mock data
- Handles API errors and loading states

#### Orders Component (`src/pages/customer/Orders.jsx`)
- Loads orders from API instead of mock data
- Real-time order management
- Error handling and retry functionality

### 4. API Utility Functions
Located at `src/utils/orderApi.js`

**Order API:**
- `orderApi.createOrder(orderData)`
- `orderApi.getOrders()`
- `orderApi.getOrder(orderId)`
- `orderApi.getOrdersByStatus(status)`
- `orderApi.updateOrder(orderId, orderData)`
- `orderApi.patchOrder(orderId, updateData)`
- `orderApi.deleteOrder(orderId)`
- `orderApi.getOrderStatusHistory(orderId)`

**Cart API:**
- `cartApi.saveCart(cartData)`
- `cartApi.getCart()`
- `cartApi.deleteCart()`

**Delivery API:**
- `deliveryApi.assignOrder(assignmentData)`
- `deliveryApi.rejectOrder(rejectionData)`
- `deliveryApi.getAvailableOrders()`

## Testing

### API Test Component
Access the test interface at: `/admin/order-api-test`

The test component allows you to:
- Test all Order CRUD operations
- Test Cart save/load/delete operations
- Test Delivery assignment operations
- View detailed API responses
- Debug authentication and network issues

### Manual Testing Steps

1. **Login as Customer**
   - Ensure you have customer credentials
   - Login and add items to cart

2. **Test Cart Operations**
   - Add items to cart (auto-saves to API)
   - Refresh page (cart loads from API)
   - Clear cart (deletes from API)

3. **Test Order Creation**
   - Go to checkout
   - Fill in delivery information
   - Place order (creates via API)

4. **Test Order Management**
   - View orders list (loads from API)
   - View order details
   - Test order status updates (if admin/restaurant)

## Error Handling

The API integration includes comprehensive error handling:

1. **Network Errors**: Handled by axios interceptors
2. **Authentication Errors**: Automatic token refresh and redirect
3. **Validation Errors**: Displayed to users with retry options
4. **Server Errors**: User-friendly error messages with retry functionality

## Best Practices

1. **Always check result.success** before proceeding
2. **Handle loading states** for better UX
3. **Clear errors** after user actions
4. **Use optimistic updates** where appropriate
5. **Implement proper error boundaries** for component-level errors
6. **Auto-save cart changes** with debouncing to avoid excessive API calls

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check token validity and refresh
2. **403 Forbidden**: Verify user permissions
3. **400 Bad Request**: Check required fields and data types
4. **404 Not Found**: Verify order/cart exists and user has access
5. **500 Server Error**: Check server logs and API status

### Debug Steps

1. Check browser console for errors
2. Verify localStorage contains valid token
3. Test API endpoints directly with tools like Postman
4. Check network tab for request/response details
5. Use the API test component for isolated testing
6. Verify order/cart data structure matches API expectations
