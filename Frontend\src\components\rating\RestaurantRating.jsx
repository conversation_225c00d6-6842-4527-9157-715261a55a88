import React, { useState, useEffect } from 'react';
import { ratingApi } from '../../utils/ratingApi';
import './RestaurantRating.css';

const RestaurantRating = ({ restaurantId, showDetails = false }) => {
  const [ratingData, setRatingData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (restaurantId) {
      fetchRatingData();
    }
  }, [restaurantId]);

  const fetchRatingData = async () => {
    try {
      setLoading(true);
      const data = await ratingApi.getRestaurantRatings(restaurantId);
      setRatingData(data);
    } catch (err) {
      console.error('Error fetching rating data:', err);
      setError('Failed to load ratings');
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    return (
      <div className="stars-display">
        {/* Full stars */}
        {[...Array(fullStars)].map((_, i) => (
          <span key={`full-${i}`} className="star filled">⭐</span>
        ))}
        {/* Half star */}
        {hasHalfStar && <span className="star half">⭐</span>}
        {/* Empty stars */}
        {[...Array(emptyStars)].map((_, i) => (
          <span key={`empty-${i}`} className="star empty">☆</span>
        ))}
      </div>
    );
  };

  const renderRatingBar = (count, total) => {
    const percentage = total > 0 ? (count / total) * 100 : 0;
    return (
      <div className="rating-bar">
        <div className="rating-bar-fill" style={{ width: `${percentage}%` }}></div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="restaurant-rating loading">
        <div className="rating-skeleton">
          <div className="skeleton-stars"></div>
          <div className="skeleton-text"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="restaurant-rating error">
        <span className="error-text">⚠️ {error}</span>
      </div>
    );
  }

  if (!ratingData || ratingData.total_ratings === 0) {
    return (
      <div className="restaurant-rating no-ratings">
        <span className="no-ratings-text">⭐ No ratings yet</span>
      </div>
    );
  }

  return (
    <div className={`restaurant-rating ${showDetails ? 'detailed' : 'compact'}`}>
      <div className="rating-summary">
        <div className="overall-rating">
          <span className="rating-number">{ratingData.average_rating}</span>
          {renderStars(ratingData.average_rating)}
          <span className="rating-count">({ratingData.total_ratings} reviews)</span>
        </div>
      </div>

      {showDetails && (
        <div className="rating-details">
          <div className="rating-breakdown">
            <h4>Rating Breakdown</h4>
            
            <div className="rating-category">
              <span className="category-label">🍽️ Food Quality</span>
              <div className="category-rating">
                {renderStars(ratingData.average_food_rating)}
                <span className="category-score">{ratingData.average_food_rating}</span>
              </div>
            </div>

            <div className="rating-category">
              <span className="category-label">🚚 Delivery</span>
              <div className="category-rating">
                {renderStars(ratingData.average_delivery_rating)}
                <span className="category-score">{ratingData.average_delivery_rating}</span>
              </div>
            </div>

            <div className="rating-category">
              <span className="category-label">⭐ Overall</span>
              <div className="category-rating">
                {renderStars(ratingData.average_overall_rating)}
                <span className="category-score">{ratingData.average_overall_rating}</span>
              </div>
            </div>
          </div>

          <div className="rating-distribution">
            <h4>Rating Distribution</h4>
            
            {[5, 4, 3, 2, 1].map((stars) => {
              const count = ratingData[`${stars === 1 ? 'one' : stars === 2 ? 'two' : stars === 3 ? 'three' : stars === 4 ? 'four' : 'five'}_star_count`];
              return (
                <div key={stars} className="distribution-row">
                  <span className="star-label">{stars} ⭐</span>
                  {renderRatingBar(count, ratingData.total_ratings)}
                  <span className="count-label">{count}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default RestaurantRating;
