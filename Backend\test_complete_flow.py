#!/usr/bin/env python
"""
Test the complete restaurant ordering flow
"""

import requests
import json

print("🧪 Testing complete restaurant ordering flow...")

# API base URL
API_BASE_URL = "http://127.0.0.1:8000/api"

def test_restaurants_api():
    """Test restaurants listing"""
    print("\n1. 🏪 Testing restaurants API...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/restaurant/restaurants/")
        
        if response.status_code == 200:
            restaurants = response.json()
            print(f"✅ Found {len(restaurants)} restaurants")
            
            if restaurants:
                sample_restaurant = restaurants[0]
                print(f"   Sample: {sample_restaurant['name']}")
                return sample_restaurant
            else:
                print("❌ No restaurants found")
                return None
        else:
            print(f"❌ API Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return None

def test_customer_login():
    """Test customer login"""
    print("\n2. 👤 Testing customer login...")
    
    login_data = {
        "user_name": "customer1",
        "password": "customer123"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/auth/login/", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            access_token = data['data']['access_token']
            user_info = data['data']['user']
            print(f"✅ Login successful for {user_info['name']}")
            return access_token
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login request failed: {e}")
        return None

def test_menu_categories(restaurant_id, token):
    """Test menu categories for a restaurant"""
    print(f"\n3. 📂 Testing menu categories for restaurant {restaurant_id}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/restaurant/menu-categories/?restaurant_id={restaurant_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            categories = response.json()
            print(f"✅ Found {len(categories)} categories")
            
            for category in categories[:3]:  # Show first 3
                print(f"   - {category['name']}")
                
            return categories[0] if categories else None
        else:
            print(f"❌ Categories API Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Categories request failed: {e}")
        return None

def test_menu_items(category_id, token):
    """Test menu items for a category"""
    print(f"\n4. 🍽️ Testing menu items for category {category_id}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/restaurant/menu-items/?category_id={category_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            items = response.json()
            print(f"✅ Found {len(items)} menu items")
            
            for item in items[:3]:  # Show first 3
                print(f"   - {item['name']}: ${item['price']}")
                
            return items[0] if items else None
        else:
            print(f"❌ Menu items API Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Menu items request failed: {e}")
        return None

def test_cart_operations(token):
    """Test cart operations"""
    print("\n5. 🛒 Testing cart operations...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Get current cart
        response = requests.get(f"{API_BASE_URL}/order/carts/mine/", headers=headers)
        
        if response.status_code == 200:
            cart = response.json()
            print(f"✅ Cart retrieved successfully")
            print(f"   Items in cart: {len(cart.get('items', []))}")
            return True
        else:
            print(f"❌ Cart API Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Cart request failed: {e}")
        return False

def test_orders_api(token):
    """Test orders API"""
    print("\n6. 📋 Testing orders API...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{API_BASE_URL}/order/orders/", headers=headers)
        
        if response.status_code == 200:
            orders = response.json()
            print(f"✅ Orders retrieved successfully")
            print(f"   Total orders: {len(orders)}")
            return True
        else:
            print(f"❌ Orders API Error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Orders request failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting complete system test...")
    
    # Test 1: Restaurants API
    restaurant = test_restaurants_api()
    if not restaurant:
        print("❌ Cannot continue without restaurants")
        return
    
    # Test 2: Customer login
    token = test_customer_login()
    if not token:
        print("❌ Cannot continue without authentication")
        return
    
    # Test 3: Menu categories
    category = test_menu_categories(restaurant['id'], token)
    if category:
        # Test 4: Menu items
        test_menu_items(category['id'], token)
    
    # Test 5: Cart operations
    test_cart_operations(token)
    
    # Test 6: Orders API
    test_orders_api(token)
    
    print("\n" + "="*60)
    print("🎉 COMPLETE SYSTEM TEST FINISHED!")
    print("✅ All major components are working")
    print("🎯 The restaurant ordering system is ready for use!")
    print("="*60)

if __name__ == "__main__":
    main()
