.restaurant-rating {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.restaurant-rating.compact {
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.restaurant-rating.loading,
.restaurant-rating.error,
.restaurant-rating.no-ratings {
  padding: 8px 0;
}

/* Rating Summary */
.rating-summary {
  display: flex;
  align-items: center;
  gap: 12px;
}

.overall-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rating-number {
  font-size: 24px;
  font-weight: 700;
  color: #059669;
}

.compact .rating-number {
  font-size: 18px;
}

.stars-display {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 20px;
  color: #fbbf24;
}

.compact .star {
  font-size: 16px;
}

.star.filled {
  color: #f59e0b;
}

.star.half {
  color: #f59e0b;
  opacity: 0.7;
}

.star.empty {
  color: #d1d5db;
}

.rating-count {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

/* Rating Details */
.rating-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.rating-breakdown h4,
.rating-distribution h4 {
  margin: 0 0 16px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

/* Rating Categories */
.rating-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.rating-category:last-child {
  border-bottom: none;
}

.category-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

.category-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-rating .stars-display {
  gap: 1px;
}

.category-rating .star {
  font-size: 16px;
}

.category-score {
  font-size: 14px;
  font-weight: 600;
  color: #059669;
  min-width: 30px;
  text-align: right;
}

/* Rating Distribution */
.distribution-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.star-label {
  font-size: 14px;
  color: #374151;
  min-width: 40px;
}

.rating-bar {
  flex: 1;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.rating-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.count-label {
  font-size: 12px;
  color: #6b7280;
  min-width: 20px;
  text-align: right;
}

/* Loading State */
.rating-skeleton {
  display: flex;
  align-items: center;
  gap: 8px;
}

.skeleton-stars {
  width: 120px;
  height: 20px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.skeleton-text {
  width: 80px;
  height: 16px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Error and No Ratings States */
.error-text,
.no-ratings-text {
  color: #6b7280;
  font-size: 14px;
  font-style: italic;
}

.error-text {
  color: #dc2626;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .rating-details {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 16px;
  }
  
  .rating-number {
    font-size: 20px;
  }
  
  .star {
    font-size: 18px;
  }
  
  .compact .star {
    font-size: 14px;
  }
  
  .distribution-row {
    gap: 8px;
  }
  
  .star-label {
    min-width: 35px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .restaurant-rating.compact {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .overall-rating {
    gap: 6px;
  }
  
  .rating-number {
    font-size: 18px;
  }
  
  .star {
    font-size: 16px;
  }
  
  .rating-count {
    font-size: 12px;
  }
}
