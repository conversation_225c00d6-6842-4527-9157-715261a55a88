import requests
import json

# Get admin token first
login_data = {'user_name': 'admin', 'password': 'admin123'}
login_response = requests.post('http://127.0.0.1:8000/api/auth/login/', json=login_data)

if login_response.status_code == 200:
    login_data = login_response.json()
    token = login_data['data']['access_token']
    headers = {'Authorization': f'Bearer {token}'}

    # Get all orders
    orders_response = requests.get('http://127.0.0.1:8000/api/order/orders/', headers=headers)
    if orders_response.status_code == 200:
        orders = orders_response.json()
        print(f'Total orders: {len(orders)}')
        for order in orders[:5]:  # Show first 5 orders
            print(f'Order {order["id"]}: Customer {order.get("customer", "N/A")}, Status: {order["status"]}, Amount: ${order["total_amount"]}')
    else:
        print(f'Failed to get orders: {orders_response.status_code}')

    # Try to get customer users via admin endpoint
    print(f'\nTrying to get customer data...')

    # Check if there's a customer with username 'customer1'
    customer_login = {'user_name': 'customer1', 'password': 'customer123'}
    customer_response = requests.post('http://127.0.0.1:8000/api/auth/login/', json=customer_login)
    if customer_response.status_code == 200:
        customer_data = customer_response.json()
        print(f'Found customer: {customer_data["data"]["user"]["name"]} (ID: {customer_data["data"]["user"]["id"]})')

        # Test customer order access
        customer_token = customer_data['data']['access_token']
        customer_headers = {'Authorization': f'Bearer {customer_token}'}
        customer_orders = requests.get('http://127.0.0.1:8000/api/order/orders/', headers=customer_headers)
        if customer_orders.status_code == 200:
            orders = customer_orders.json()
            print(f'Customer can see {len(orders)} orders')
        else:
            print(f'Customer order access failed: {customer_orders.status_code}')
    else:
        print('No customer1 account found')
else:
    print(f'Login failed: {login_response.status_code}')
