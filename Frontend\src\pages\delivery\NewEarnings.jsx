import React, { useState, useEffect } from "react";
import {
  DollarSign,
  TrendingUp,
  Calendar,
  Clock,
  Package,
  Star,
  Target,
  Award,
  RefreshCw,
  Download,
  Filter,
  BarChart3,
  PieChart,
  Activity,
  Wallet,
  CreditCard,
  Receipt,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import Badge from "../../components/common/Badge";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";

function NewDeliveryEarnings() {
  const { user } = useAuth();

  // State management
  const [earningsData, setEarningsData] = useState(null);
  const [performanceData, setPerformanceData] = useState(null);
  const [payoutHistory, setPayoutHistory] = useState([]);
  const [commissionStructure, setCommissionStructure] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState("today");
  const [refreshing, setRefreshing] = useState(false);
  const [showPayoutModal, setShowPayoutModal] = useState(false);
  const [payoutAmount, setPayoutAmount] = useState("");
  const [payoutMethod, setPayoutMethod] = useState("bank_transfer");
  const [requestingPayout, setRequestingPayout] = useState(false);

  // Load earnings data
  const loadEarningsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load earnings summary
      const earningsResult = await deliveryAgentApi.getEarningsSummary();
      if (earningsResult.success) {
        setEarningsData(earningsResult.data.earnings);
        setCommissionStructure(earningsResult.data.commission_structure);
      }

      // Load performance metrics
      const performanceResult = await deliveryAgentApi.getPerformanceMetrics();
      if (performanceResult.success) {
        setPerformanceData(performanceResult.data);
      }

      // Load payout history
      const payoutResult = await deliveryAgentApi.getPayoutHistory();
      if (payoutResult.success) {
        setPayoutHistory(payoutResult.data.payouts || []);
      }
    } catch (err) {
      console.error("Earnings load error:", err);
      setError("Failed to load earnings data");
    } finally {
      setLoading(false);
    }
  };

  // Refresh earnings
  const refreshEarnings = async () => {
    setRefreshing(true);
    await loadEarningsData();
    setRefreshing(false);
  };

  // Request payout
  const requestPayout = async () => {
    try {
      setRequestingPayout(true);
      setError(null);

      const amount = parseFloat(payoutAmount);
      if (!amount || amount <= 0) {
        setError("Please enter a valid payout amount");
        return;
      }

      const result = await deliveryAgentApi.requestPayout(amount, payoutMethod);

      if (result.success) {
        setShowPayoutModal(false);
        setPayoutAmount("");
        alert(
          `Payout request submitted successfully! Amount: $${amount.toFixed(2)}`
        );
        await refreshEarnings();
      } else {
        setError(result.error?.message || "Failed to request payout");
      }
    } catch (err) {
      console.error("Payout request error:", err);
      setError("Failed to request payout");
    } finally {
      setRequestingPayout(false);
    }
  };

  useEffect(() => {
    loadEarningsData();
  }, [user]);

  // Get current period data
  const getCurrentPeriodData = () => {
    if (!earningsData) return null;

    switch (selectedPeriod) {
      case "today":
        return earningsData.today;
      case "week":
        return earningsData.this_week;
      case "month":
        return earningsData.this_month;
      default:
        return earningsData.today;
    }
  };

  const currentData = getCurrentPeriodData();

  // Loading state
  if (loading) {
    return (
      <div className='min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-600 text-lg'>Loading earnings data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50'>
      {/* Header */}
      <div className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center py-4'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900'>Earnings</h1>
              <p className='text-sm text-gray-500'>
                Track your delivery earnings and performance
              </p>
            </div>

            <div className='flex items-center space-x-3'>
              <Button
                variant='outline'
                onClick={refreshEarnings}
                disabled={refreshing}
                className='flex items-center space-x-2'
              >
                <RefreshCw
                  className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
                />
                <span>Refresh</span>
              </Button>

              <Button variant='outline' className='flex items-center space-x-2'>
                <Download className='h-4 w-4' />
                <span>Export</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
        {/* Period Selector */}
        <Card className='p-4 mb-6'>
          <div className='flex flex-wrap gap-2'>
            {[
              { key: "today", label: "Today", icon: Calendar },
              { key: "week", label: "This Week", icon: Calendar },
              { key: "month", label: "This Month", icon: Calendar },
            ].map(({ key, label, icon: Icon }) => (
              <Button
                key={key}
                variant={selectedPeriod === key ? "primary" : "outline"}
                onClick={() => setSelectedPeriod(key)}
                className='flex items-center space-x-2'
              >
                <Icon className='h-4 w-4' />
                <span>{label}</span>
              </Button>
            ))}
          </div>
        </Card>

        {/* Earnings Overview */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
          {/* Total Earnings */}
          <Card className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Total Earnings
                </p>
                <p className='text-3xl font-bold text-green-600'>
                  ${currentData?.total_earnings?.toFixed(2) || "0.00"}
                </p>
              </div>
              <div className='p-3 rounded-full bg-green-100'>
                <DollarSign className='h-8 w-8 text-green-600' />
              </div>
            </div>
          </Card>

          {/* Total Tips */}
          <Card className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>Tips</p>
                <p className='text-3xl font-bold text-blue-600'>
                  ${currentData?.total_tips?.toFixed(2) || "0.00"}
                </p>
              </div>
              <div className='p-3 rounded-full bg-blue-100'>
                <Wallet className='h-8 w-8 text-blue-600' />
              </div>
            </div>
          </Card>

          {/* Total Deliveries */}
          <Card className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>Deliveries</p>
                <p className='text-3xl font-bold text-purple-600'>
                  {currentData?.total_deliveries || 0}
                </p>
              </div>
              <div className='p-3 rounded-full bg-purple-100'>
                <Package className='h-8 w-8 text-purple-600' />
              </div>
            </div>
          </Card>

          {/* Average per Delivery */}
          <Card className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Avg per Delivery
                </p>
                <p className='text-3xl font-bold text-orange-600'>
                  $
                  {currentData?.total_deliveries > 0
                    ? (
                        currentData.total_earnings /
                        currentData.total_deliveries
                      ).toFixed(2)
                    : "0.00"}
                </p>
              </div>
              <div className='p-3 rounded-full bg-orange-100'>
                <Target className='h-8 w-8 text-orange-600' />
              </div>
            </div>
          </Card>
        </div>

        {/* Earnings Breakdown */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8'>
          {/* Earnings Composition */}
          <Card className='p-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-4'>
              Earnings Breakdown ({selectedPeriod})
            </h3>

            <div className='space-y-4'>
              <div className='flex justify-between items-center p-3 bg-green-50 rounded-lg'>
                <div className='flex items-center space-x-3'>
                  <div className='w-3 h-3 bg-green-500 rounded-full'></div>
                  <span className='text-sm font-medium text-gray-700'>
                    Base Fees
                  </span>
                </div>
                <span className='font-semibold text-green-600'>
                  ${currentData?.base_fees?.toFixed(2) || "0.00"}
                </span>
              </div>

              <div className='flex justify-between items-center p-3 bg-blue-50 rounded-lg'>
                <div className='flex items-center space-x-3'>
                  <div className='w-3 h-3 bg-blue-500 rounded-full'></div>
                  <span className='text-sm font-medium text-gray-700'>
                    Distance Bonus
                  </span>
                </div>
                <span className='font-semibold text-blue-600'>
                  ${currentData?.distance_bonus?.toFixed(2) || "0.00"}
                </span>
              </div>

              <div className='flex justify-between items-center p-3 bg-purple-50 rounded-lg'>
                <div className='flex items-center space-x-3'>
                  <div className='w-3 h-3 bg-purple-500 rounded-full'></div>
                  <span className='text-sm font-medium text-gray-700'>
                    Time Bonus
                  </span>
                </div>
                <span className='font-semibold text-purple-600'>
                  ${currentData?.time_bonus?.toFixed(2) || "0.00"}
                </span>
              </div>

              <div className='flex justify-between items-center p-3 bg-yellow-50 rounded-lg'>
                <div className='flex items-center space-x-3'>
                  <div className='w-3 h-3 bg-yellow-500 rounded-full'></div>
                  <span className='text-sm font-medium text-gray-700'>
                    Tips
                  </span>
                </div>
                <span className='font-semibold text-yellow-600'>
                  ${currentData?.total_tips?.toFixed(2) || "0.00"}
                </span>
              </div>

              <div className='flex justify-between items-center p-3 bg-red-50 rounded-lg'>
                <div className='flex items-center space-x-3'>
                  <div className='w-3 h-3 bg-red-500 rounded-full'></div>
                  <span className='text-sm font-medium text-gray-700'>
                    Platform Commission
                  </span>
                </div>
                <span className='font-semibold text-red-600'>
                  -${currentData?.platform_commission?.toFixed(2) || "0.00"}
                </span>
              </div>

              <div className='border-t pt-3'>
                <div className='flex justify-between items-center mb-2'>
                  <span className='text-sm text-gray-600'>Gross Earnings</span>
                  <span className='font-medium text-gray-700'>
                    ${currentData?.gross_earnings?.toFixed(2) || "0.00"}
                  </span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-lg font-semibold text-gray-900'>
                    Net Earnings
                  </span>
                  <span className='text-lg font-bold text-green-600'>
                    ${currentData?.total_earnings?.toFixed(2) || "0.00"}
                  </span>
                </div>
              </div>
            </div>
          </Card>

          {/* Performance Metrics */}
          <Card className='p-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-4'>
              Performance Metrics
            </h3>

            <div className='space-y-4'>
              <div className='flex justify-between items-center'>
                <div className='flex items-center space-x-2'>
                  <Star className='h-4 w-4 text-yellow-500' />
                  <span className='text-sm text-gray-600'>Average Rating</span>
                </div>
                <span className='font-medium'>
                  {performanceData?.weekly?.[0]?.average_rating?.toFixed(1) ||
                    "0.0"}
                </span>
              </div>

              <div className='flex justify-between items-center'>
                <div className='flex items-center space-x-2'>
                  <Target className='h-4 w-4 text-green-500' />
                  <span className='text-sm text-gray-600'>Completion Rate</span>
                </div>
                <span className='font-medium'>
                  {performanceData?.weekly?.[0]?.completion_rate?.toFixed(1) ||
                    "0.0"}
                  %
                </span>
              </div>

              <div className='flex justify-between items-center'>
                <div className='flex items-center space-x-2'>
                  <Clock className='h-4 w-4 text-blue-500' />
                  <span className='text-sm text-gray-600'>
                    Avg Delivery Time
                  </span>
                </div>
                <span className='font-medium'>
                  {performanceData?.weekly?.[0]?.average_delivery_time?.toFixed(
                    0
                  ) || "0"}{" "}
                  min
                </span>
              </div>

              <div className='flex justify-between items-center'>
                <div className='flex items-center space-x-2'>
                  <Activity className='h-4 w-4 text-purple-500' />
                  <span className='text-sm text-gray-600'>Orders per Hour</span>
                </div>
                <span className='font-medium'>
                  {performanceData?.weekly?.[0]?.orders_per_hour?.toFixed(1) ||
                    "0.0"}
                </span>
              </div>
            </div>
          </Card>
        </div>

        {/* Recent Performance Trends */}
        <Card className='p-6 mb-8'>
          <h3 className='text-lg font-semibold text-gray-900 mb-4'>
            Recent Performance Trends
          </h3>

          <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
            {/* Daily Performance */}
            <div>
              <h4 className='text-sm font-medium text-gray-700 mb-3'>
                Daily Performance
              </h4>
              <div className='space-y-2'>
                {performanceData?.daily?.slice(0, 7).map((day, index) => (
                  <div
                    key={index}
                    className='flex justify-between items-center text-sm'
                  >
                    <span className='text-gray-600'>
                      {new Date(day.period_start).toLocaleDateString("en-US", {
                        weekday: "short",
                      })}
                    </span>
                    <div className='flex items-center space-x-2'>
                      <span className='font-medium'>{day.total_orders}</span>
                      <span className='text-green-600'>
                        ${day.total_earnings?.toFixed(0)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Weekly Performance */}
            <div>
              <h4 className='text-sm font-medium text-gray-700 mb-3'>
                Weekly Performance
              </h4>
              <div className='space-y-2'>
                {performanceData?.weekly?.slice(0, 4).map((week, index) => (
                  <div
                    key={index}
                    className='flex justify-between items-center text-sm'
                  >
                    <span className='text-gray-600'>Week {index + 1}</span>
                    <div className='flex items-center space-x-2'>
                      <span className='font-medium'>{week.total_orders}</span>
                      <span className='text-green-600'>
                        ${week.total_earnings?.toFixed(0)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Monthly Performance */}
            <div>
              <h4 className='text-sm font-medium text-gray-700 mb-3'>
                Monthly Performance
              </h4>
              <div className='space-y-2'>
                {performanceData?.monthly?.slice(0, 3).map((month, index) => (
                  <div
                    key={index}
                    className='flex justify-between items-center text-sm'
                  >
                    <span className='text-gray-600'>
                      {new Date(month.period_start).toLocaleDateString(
                        "en-US",
                        { month: "short" }
                      )}
                    </span>
                    <div className='flex items-center space-x-2'>
                      <span className='font-medium'>{month.total_orders}</span>
                      <span className='text-green-600'>
                        ${month.total_earnings?.toFixed(0)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Card>

        {/* Payout Management */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8'>
          {/* Payout Request */}
          <Card className='p-6'>
            <div className='flex justify-between items-center mb-4'>
              <h3 className='text-lg font-semibold text-gray-900'>
                Request Payout
              </h3>
              {commissionStructure && (
                <span className='text-sm text-gray-500'>
                  Min: ${commissionStructure.minimum_payout_amount}
                </span>
              )}
            </div>

            <div className='space-y-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Payout Amount
                </label>
                <div className='relative'>
                  <span className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500'>
                    $
                  </span>
                  <input
                    type='number'
                    value={payoutAmount}
                    onChange={(e) => setPayoutAmount(e.target.value)}
                    placeholder='0.00'
                    className='w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                    min='0'
                    step='0.01'
                  />
                </div>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Payment Method
                </label>
                <select
                  value={payoutMethod}
                  onChange={(e) => setPayoutMethod(e.target.value)}
                  className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                >
                  <option value='bank_transfer'>Bank Transfer</option>
                  <option value='paypal'>PayPal</option>
                  <option value='mobile_money'>Mobile Money</option>
                </select>
              </div>

              <Button
                onClick={requestPayout}
                disabled={requestingPayout || !payoutAmount}
                className='w-full flex items-center justify-center space-x-2'
              >
                <CreditCard className='h-4 w-4' />
                <span>
                  {requestingPayout ? "Processing..." : "Request Payout"}
                </span>
              </Button>

              {commissionStructure && (
                <div className='text-xs text-gray-500 space-y-1'>
                  <p>
                    • Processing time: {commissionStructure.payout_frequency}
                  </p>
                  <p>
                    • Commission rate:{" "}
                    {(
                      commissionStructure.platform_commission_rate * 100
                    ).toFixed(1)}
                    %
                  </p>
                </div>
              )}
            </div>
          </Card>

          {/* Recent Payouts */}
          <Card className='p-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-4'>
              Recent Payouts
            </h3>

            {payoutHistory.length > 0 ? (
              <div className='space-y-3 max-h-64 overflow-y-auto'>
                {payoutHistory.slice(0, 5).map((payout) => (
                  <div
                    key={payout.id}
                    className='flex justify-between items-center p-3 bg-gray-50 rounded-lg'
                  >
                    <div>
                      <p className='font-medium text-gray-900'>
                        ${payout.amount?.toFixed(2)}
                      </p>
                      <p className='text-sm text-gray-500'>
                        {new Date(payout.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className='text-right'>
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          payout.status === "completed"
                            ? "bg-green-100 text-green-800"
                            : payout.status === "pending"
                            ? "bg-yellow-100 text-yellow-800"
                            : payout.status === "processing"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {payout.status}
                      </span>
                      {payout.performance_bonus > 0 && (
                        <p className='text-xs text-green-600 mt-1'>
                          +${payout.performance_bonus?.toFixed(2)} bonus
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className='text-center py-8'>
                <Receipt className='h-12 w-12 text-gray-400 mx-auto mb-2' />
                <p className='text-gray-500'>No payouts yet</p>
              </div>
            )}
          </Card>
        </div>

        {/* Achievements and Goals */}
        <Card className='p-6'>
          <h3 className='text-lg font-semibold text-gray-900 mb-4'>
            Achievements & Goals
          </h3>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
            <div className='text-center p-4 bg-yellow-50 rounded-lg'>
              <Award className='h-8 w-8 text-yellow-600 mx-auto mb-2' />
              <p className='text-sm font-medium text-gray-700'>Top Performer</p>
              <p className='text-xs text-gray-500'>This Week</p>
            </div>

            <div className='text-center p-4 bg-green-50 rounded-lg'>
              <Star className='h-8 w-8 text-green-600 mx-auto mb-2' />
              <p className='text-sm font-medium text-gray-700'>5-Star Rating</p>
              <p className='text-xs text-gray-500'>Maintained</p>
            </div>

            <div className='text-center p-4 bg-blue-50 rounded-lg'>
              <Target className='h-8 w-8 text-blue-600 mx-auto mb-2' />
              <p className='text-sm font-medium text-gray-700'>100% On-Time</p>
              <p className='text-xs text-gray-500'>This Month</p>
            </div>

            <div className='text-center p-4 bg-purple-50 rounded-lg'>
              <TrendingUp className='h-8 w-8 text-purple-600 mx-auto mb-2' />
              <p className='text-sm font-medium text-gray-700'>Earnings Goal</p>
              <p className='text-xs text-gray-500'>85% Complete</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}

export default NewDeliveryEarnings;
