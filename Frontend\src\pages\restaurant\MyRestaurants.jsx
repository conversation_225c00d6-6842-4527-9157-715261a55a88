import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import {
  Store,
  Plus,
  MapPin,
  Star,
} from "lucide-react";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import Badge from "../../components/common/Badge";
import { DashboardSkeleton } from "../../components/skeleton/DashboardSkeleton";

const MyRestaurants = () => {
  const { user } = useAuth();
  const {
    restaurants,
    getUserRestaurants,
    loading,
    error,
    setCurrentRestaurant,
  } = useRestaurant();
  const navigate = useNavigate();


  useEffect(() => {
    const fetchRestaurants = async () => {
      // Fetch all restaurants owned by the current user
      await getUserRestaurants();
    };

    if (user && user.role === "restaurant") {
      fetchRestaurants();
    }
  }, [user, getUserRestaurants]);

  const handleRestaurantSelect = (restaurant) => {
    setCurrentRestaurant(restaurant);
    navigate(`/restaurant/dashboard/${restaurant.id}`);
  };

  const handleAddNewRestaurant = () => {
    navigate("/register-restaurant");
  };



  if (loading) {
    return (
      <div className='p-6 animate-fade-in'>
        <DashboardSkeleton userRole='restaurant' />
      </div>
    );
  }

  return (
    <div className='p-6 animate-fade-in'>
      {/* Header */}
      <div className='mb-8'>
        <h1 className='mb-2 font-bold text-gray-900 text-3xl'>
          My Restaurants
        </h1>
        <p className='text-text-secondary'>
          Manage all your restaurants from one place
        </p>
      </div>

      {/* Add New Restaurant Button */}
      <div className='mb-8'>
        <Button
          variant='primary'
          onClick={handleAddNewRestaurant}
          className='flex items-center'
        >
          <Plus className='mr-2' size={18} />
          Add New Restaurant
        </Button>
      </div>

      {/* Restaurant List */}
      {restaurants.length === 0 ? (
        <div className='bg-white shadow-sm p-8 border border-gray-200 rounded-lg text-center'>
          <div className='flex justify-center items-center bg-primary-100 mx-auto mb-4 rounded-full w-16 h-16'>
            <Store className='text-primary-600' size={28} />
          </div>
          <h2 className='mb-2 font-semibold text-xl'>No Restaurants Yet</h2>
          <p className='mx-auto mb-6 max-w-md text-text-secondary'>
            You haven't added any restaurants yet. Create your first restaurant
            to start receiving orders.
          </p>
          <Button
            variant='primary'
            onClick={handleAddNewRestaurant}
            className='flex items-center mx-auto'
          >
            <Plus className='mr-2' size={18} />
            Create Your First Restaurant
          </Button>
        </div>
      ) : (
        <div className='gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3'>
          {restaurants.map((restaurant) => (
            <Card
              key={restaurant.id}
              className='hover:shadow-md overflow-hidden transition-shadow'
            >
              {/* Restaurant Banner */}
              <div className='relative bg-gray-200 h-32'>
                {restaurant.banner ? (
                  <img
                    src={restaurant.banner}
                    alt={`${restaurant.name} banner`}
                    className='w-full h-full object-cover'
                  />
                ) : (
                  <div className='flex justify-center items-center bg-gradient-to-r from-primary-100 to-primary-200 w-full h-full'>
                    <Store className='text-primary-600' size={32} />
                  </div>
                )}
                <div className='top-2 right-2 absolute'>
                  <Badge
                    variant={restaurant.is_verified ? "success" : "warning"}
                  >
                    {restaurant.is_verified ? "Verified" : "Pending"}
                  </Badge>
                </div>
              </div>

              {/* Restaurant Info */}
              <div className='p-4'>
                <div className='flex items-start'>
                  <div className='bg-white shadow-md -mt-8 mr-3 border-4 border-white rounded-full w-16 h-16 overflow-hidden'>
                    {restaurant.logo ? (
                      <img
                        src={restaurant.logo}
                        alt={`${restaurant.name} logo`}
                        className='w-full h-full object-cover'
                      />
                    ) : (
                      <div className='flex justify-center items-center bg-primary-100 w-full h-full'>
                        <Store className='text-primary-600' size={24} />
                      </div>
                    )}
                  </div>
                  <div className='flex-1 -mt-2'>
                    <h3 className='font-semibold text-gray-900 text-lg truncate'>
                      {restaurant.name}
                    </h3>
                    <div className='flex items-center mt-1 text-text-secondary text-sm'>
                      <MapPin size={14} className='mr-1' />
                      <span className='truncate'>
                        {restaurant.address?.street || "No address"}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Restaurant Stats */}
                <div className='gap-4 grid grid-cols-2 mt-4'>
                  <div className='bg-gray-50 p-3 rounded-lg'>
                    <div className='text-text-secondary text-sm'>Orders</div>
                    <div className='font-semibold text-lg'>
                      {restaurant.total_orders || 0}
                    </div>
                  </div>
                  <div className='bg-gray-50 p-3 rounded-lg'>
                    <div className='text-text-secondary text-sm'>Rating</div>
                    <div className='flex items-center font-semibold text-lg'>
                      {restaurant.rating || "N/A"}
                      {restaurant.rating && (
                        <Star
                          size={16}
                          className='fill-current ml-1 text-yellow-400'
                        />
                      )}
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className='mt-4'>
                  <Button
                    variant='primary'
                    onClick={() => handleRestaurantSelect(restaurant)}
                    className='w-full'
                  >
                    Manage Restaurant
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}


    </div>
  );
};

export default MyRestaurants;
