import React, { createContext, useContext, useState, useEffect } from "react";
import { API_BASE_URL } from "../config/api";

const SearchContext = createContext();

export const useSearch = () => {
  const context = useContext(SearchContext);
  if (!context) {
    throw new Error("useSearch must be used within a SearchProvider");
  }
  return context;
};

export const SearchProvider = ({ children }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchHistory, setSearchHistory] = useState([]);
  const [popularSearches, setPopularSearches] = useState([]);
  const [searchResults, setSearchResults] = useState({
    restaurants: [],
    dishes: [],
    cuisines: [],
  });
  const [isSearching, setIsSearching] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Load search history from localStorage
  useEffect(() => {
    const savedHistory = localStorage.getItem("searchHistory");
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory));
    }

    // Set popular searches (in real app, this would come from analytics)
    setPopularSearches([
      "Kabuli Pulao",
      "Kebab",
      "Biryani",
      "Mantu",
      "Afghan Bread",
      "Lamb Karahi",
      "Chicken Tikka",
      "Qorma",
      "Ashak",
      "Bolani",
    ]);
  }, []);

  // Save search history to localStorage
  useEffect(() => {
    if (searchHistory.length > 0) {
      localStorage.setItem("searchHistory", JSON.stringify(searchHistory));
    }
  }, [searchHistory]);

  // Perform search when query changes
  useEffect(() => {
    if (searchQuery.trim().length > 0) {
      performSearch(searchQuery);
    } else {
      setSearchResults({ restaurants: [], dishes: [], cuisines: [] });
    }
  }, [searchQuery]);

  const performSearch = async (query) => {
    setIsSearching(true);
    const lowerQuery = query.toLowerCase();

    // Search restaurants via API
    try {
      const restaurantsResponse = await fetch(
        `${API_BASE_URL}/restaurant/restaurants/`
      );
      const allRestaurants = restaurantsResponse.ok
        ? await restaurantsResponse.json()
        : [];

      const restaurants = allRestaurants.filter(
        (restaurant) =>
          restaurant.name.toLowerCase().includes(lowerQuery) ||
          restaurant.description?.toLowerCase().includes(lowerQuery)
      );

      // Search dishes/menu items via API
      const menuItemsResponse = await fetch(
        `${API_BASE_URL}/restaurant/menu-items/`
      );
      const allMenuItems = menuItemsResponse.ok
        ? await menuItemsResponse.json()
        : [];

      const dishes = allMenuItems.filter(
        (item) =>
          item.name.toLowerCase().includes(lowerQuery) ||
          item.description?.toLowerCase().includes(lowerQuery)
      );

      // Extract unique cuisines from search results
      const cuisines = [
        ...new Set(restaurants.map((restaurant) => restaurant.name)),
      ].filter((name) => name.toLowerCase().includes(lowerQuery));

      setSearchResults({
        restaurants: restaurants.slice(0, 10), // Limit results
        dishes: dishes.slice(0, 15),
        cuisines: cuisines.slice(0, 5),
      });
    } catch (error) {
      console.error("Search error:", error);
      setSearchResults({ restaurants: [], dishes: [], cuisines: [] });
    }

    setIsSearching(false);
  };

  const addToSearchHistory = (query) => {
    if (!query.trim()) return;

    const newHistory = [
      query,
      ...searchHistory.filter((item) => item !== query),
    ].slice(0, 10); // Keep only last 10 searches

    setSearchHistory(newHistory);
  };

  const clearSearchHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem("searchHistory");
  };

  const removeFromHistory = (query) => {
    const newHistory = searchHistory.filter((item) => item !== query);
    setSearchHistory(newHistory);
  };

  const getSuggestions = (query) => {
    if (!query.trim()) return [];

    const lowerQuery = query.toLowerCase();
    const suggestions = [];

    // Add matching restaurants
    mockRestaurants.forEach((restaurant) => {
      if (restaurant.name.toLowerCase().includes(lowerQuery)) {
        suggestions.push({
          type: "restaurant",
          text: restaurant.name,
          subtitle: restaurant.cuisine.join(", "),
          id: restaurant.id,
        });
      }
    });

    // Add matching dishes
    mockMenuItems.forEach((item) => {
      if (item.name.toLowerCase().includes(lowerQuery)) {
        const restaurant = mockRestaurants.find(
          (r) => r.id === item.restaurantId
        );
        suggestions.push({
          type: "dish",
          text: item.name,
          subtitle: restaurant?.name || "Unknown Restaurant",
          id: item.id,
          restaurantId: item.restaurantId,
        });
      }
    });

    // Add matching cuisines
    const allCuisines = [...new Set(mockRestaurants.flatMap((r) => r.cuisine))];
    allCuisines.forEach((cuisine) => {
      if (cuisine.toLowerCase().includes(lowerQuery)) {
        suggestions.push({
          type: "cuisine",
          text: cuisine,
          subtitle: "Cuisine type",
          id: cuisine,
        });
      }
    });

    return suggestions.slice(0, 8); // Limit suggestions
  };

  const value = {
    searchQuery,
    setSearchQuery,
    searchHistory,
    popularSearches,
    searchResults,
    isSearching,
    showSuggestions,
    setShowSuggestions,
    addToSearchHistory,
    clearSearchHistory,
    removeFromHistory,
    getSuggestions,
    performSearch,
  };

  return (
    <SearchContext.Provider value={value}>{children}</SearchContext.Provider>
  );
};

export default SearchContext;
