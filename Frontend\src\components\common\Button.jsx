import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '../../utils/cn';
import Loader from './Loader';

const Button = ({
  children,
  variant = 'primary',
  size = 'medium',
  type = 'button',
  disabled = false,
  loading = false,
  className,
  icon,
  iconPosition = 'left',
  fullWidth = false,
  href,
  to,
  onClick,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-md transition-all duration-200 font-medium focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500',
    secondary: 'bg-background-dark hover:bg-gray-300 text-text-primary focus:ring-gray-400',
    outline: 'border border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500',
    danger: 'bg-accent-red hover:bg-accent-red-dark text-white focus:ring-accent-red',
    success: 'bg-accent-green hover:bg-accent-green-dark text-white focus:ring-accent-green',
    ghost: 'bg-transparent hover:bg-gray-100 text-text-primary',
  };
  
  const sizeClasses = {
    small: 'text-xs px-3 py-1.5',
    medium: 'text-sm px-4 py-2',
    large: 'text-base px-6 py-3',
  };
  
  const disabledClasses = 'opacity-60 cursor-not-allowed pointer-events-none';
  
  const buttonClasses = cn(
    baseClasses,
    variantClasses[variant],
    sizeClasses[size],
    fullWidth ? 'w-full' : '',
    (disabled || loading) ? disabledClasses : '',
    className
  );
  
  const content = (
    <>
      {loading && <Loader size="small" color={variant === 'outline' || variant === 'ghost' ? 'primary' : 'white'} />}
      {icon && iconPosition === 'left' && !loading && <span className="mr-2">{icon}</span>}
      <span className={loading ? 'ml-2' : ''}>{children}</span>
      {icon && iconPosition === 'right' && !loading && <span className="ml-2">{icon}</span>}
    </>
  );
  
  if (to) {
    return (
      <Link to={to} className={buttonClasses} {...props}>
        {content}
      </Link>
    );
  }
  
  if (href) {
    return (
      <a href={href} className={buttonClasses} {...props}>
        {content}
      </a>
    );
  }
  
  return (
    <button
      type={type}
      className={buttonClasses}
      disabled={disabled || loading}
      onClick={onClick}
      {...props}
    >
      {content}
    </button>
  );
};

export default Button;