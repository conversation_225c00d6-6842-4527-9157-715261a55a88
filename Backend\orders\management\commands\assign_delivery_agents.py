"""
Management command for manually assigning delivery agents to orders
and testing the automatic assignment system.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from orders.models import Order
from orders.delivery_assignment import delivery_assignment_service


class Command(BaseCommand):
    help = 'Assign delivery agents to ready orders'

    def add_arguments(self, parser):
        parser.add_argument(
            '--order-id',
            type=int,
            help='Assign specific order by ID',
        )
        parser.add_argument(
            '--all',
            action='store_true',
            help='Assign all ready orders',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be assigned without making changes',
        )
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show assignment statistics',
        )

    def handle(self, *args, **options):
        if options['stats']:
            self.show_statistics()
            return

        if options['order_id']:
            self.assign_single_order(options['order_id'], options['dry_run'])
        elif options['all']:
            self.assign_all_ready_orders(options['dry_run'])
        else:
            self.stdout.write(
                self.style.ERROR('Please specify --order-id, --all, or --stats')
            )

    def assign_single_order(self, order_id, dry_run=False):
        """Assign a specific order"""
        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            raise CommandError(f'Order with ID {order_id} does not exist')

        if order.status != 'ready':
            self.stdout.write(
                self.style.WARNING(
                    f'Order {order_id} is not ready for assignment (status: {order.status})'
                )
            )
            return

        if order.delivery_agent:
            self.stdout.write(
                self.style.WARNING(
                    f'Order {order_id} is already assigned to agent {order.delivery_agent.email}'
                )
            )
            return

        if dry_run:
            self.stdout.write(f'Would assign order {order_id}')
            return

        result = delivery_assignment_service.auto_assign_order(order)
        
        if result['success']:
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully assigned order {order_id} to agent {result["agent_email"]}'
                )
            )
        else:
            self.stdout.write(
                self.style.ERROR(
                    f'Failed to assign order {order_id}: {result["reason"]}'
                )
            )

    def assign_all_ready_orders(self, dry_run=False):
        """Assign all ready orders"""
        ready_orders = Order.objects.filter(
            status='ready',
            delivery_agent__isnull=True
        )

        if not ready_orders.exists():
            self.stdout.write(
                self.style.WARNING('No orders ready for assignment')
            )
            return

        self.stdout.write(f'Found {ready_orders.count()} orders ready for assignment')

        success_count = 0
        failure_count = 0

        for order in ready_orders:
            if dry_run:
                self.stdout.write(f'Would assign order {order.id}')
                continue

            result = delivery_assignment_service.auto_assign_order(order)
            
            if result['success']:
                success_count += 1
                self.stdout.write(
                    self.style.SUCCESS(
                        f'✓ Order {order.id} assigned to {result["agent_email"]}'
                    )
                )
            else:
                failure_count += 1
                self.stdout.write(
                    self.style.ERROR(
                        f'✗ Order {order.id} failed: {result["reason"]}'
                    )
                )

        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'\nAssignment complete: {success_count} successful, {failure_count} failed'
                )
            )

    def show_statistics(self):
        """Show assignment statistics"""
        stats = delivery_assignment_service.get_assignment_statistics()
        
        self.stdout.write(self.style.SUCCESS('=== Delivery Assignment Statistics ==='))
        self.stdout.write(f'Orders ready for assignment: {stats.get("orders_ready_for_assignment", "N/A")}')
        self.stdout.write(f'Orders currently assigned: {stats.get("orders_currently_assigned", "N/A")}')
        self.stdout.write(f'Available delivery agents: {stats.get("available_agents", "N/A")}')
        self.stdout.write(f'Last updated: {stats.get("timestamp", "N/A")}')
        
        # Additional statistics
        total_orders = Order.objects.count()
        pending_orders = Order.objects.filter(status__in=['pending', 'confirmed', 'preparing']).count()
        delivered_orders = Order.objects.filter(status='delivered').count()
        
        self.stdout.write('\n=== General Order Statistics ===')
        self.stdout.write(f'Total orders: {total_orders}')
        self.stdout.write(f'Pending orders: {pending_orders}')
        self.stdout.write(f'Delivered orders: {delivered_orders}')
