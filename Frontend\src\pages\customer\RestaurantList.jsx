import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
  Search,
  MapPin,
  Star,
  Clock,
  Filter,
  X,
  ArrowUpDown,
  Heart,
} from "lucide-react";
import { API_BASE_URL } from "../../config/api";
import { useFilters } from "../../context/FiltersContext";
import { useFavorites } from "../../context/FavoritesContext";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import Loader from "../../components/common/Loader";
import FilterPanel from "../../components/filters/FilterPanel";
import QuickFilters from "../../components/filters/QuickFilters";
import SortOptions from "../../components/filters/SortOptions";
import ActiveFilters from "../../components/filters/ActiveFilters";
import { RestaurantGridSkeleton } from "../../components/skeleton/RestaurantCardSkeleton";
import NetworkError from "../../components/error/NetworkError";
import {
  NoRestaurantsFound,
  NoSearchResults,
} from "../../components/error/EmptyState";
import { useRetry } from "../../hooks/useRetry";
import PullToRefresh from "../../components/mobile/PullToRefresh";
import { SwipeableRestaurantCard } from "../../components/mobile/SwipeableCard";
import { useDeliveryFee } from "../../hooks/useDeliveryFee";
import {
  DeliveryFeeBadge,
  DeliveryTimeBadge,
} from "../../components/delivery/DeliveryFeeDisplay";
import RestaurantCard, {
  RestaurantGrid,
} from "../../components/restaurant/RestaurantCard";

const RestaurantList = () => {
  const {
    isFilterPanelOpen,
    setIsFilterPanelOpen,
    appliedFiltersCount,
    filters,
  } = useFilters();

  const { toggleFavorite, isFavorite } = useFavorites();

  const [activeCategory, setActiveCategory] = useState("All");
  const [restaurants, setRestaurants] = useState([]);
  const [filteredRestaurants, setFilteredRestaurants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [displayRestaurants, setDisplayRestaurants] = useState([]);
  const [cuisineCategories, setCuisineCategories] = useState(["All"]);

  // Fetch restaurants from API
  useEffect(() => {
    const fetchRestaurants = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`${API_BASE_URL}/restaurant/restaurants/`);

        if (response.ok) {
          const restaurantData = await response.json();

          setRestaurants(restaurantData);
          setFilteredRestaurants(restaurantData);
          setDisplayRestaurants(restaurantData);

          // Extract unique cuisine categories from restaurant names
          const categories = [...new Set(restaurantData.map((r) => r.name))];
          setCuisineCategories(["All", ...categories.slice(0, 8)]); // Limit to 8 categories
        } else {
          setError("Failed to fetch restaurants");
        }
      } catch (error) {
        console.error("Error fetching restaurants:", error);
        setError("Network error occurred");
      } finally {
        setLoading(false);
      }
    };

    fetchRestaurants();
  }, []);

  // Retry logic for failed requests
  const { retry, isRetrying, retryCount } = useRetry({
    maxRetries: 3,
    onRetry: (count) => {
      console.log(`Retrying restaurant fetch (attempt ${count})`);
    },
  });

  // Filter restaurants based on search and filters
  useEffect(() => {
    if (restaurants.length === 0) return;

    let filtered = [...restaurants];

    // Filter by category (using restaurant name as category for now)
    if (activeCategory !== "All") {
      filtered = filtered.filter((restaurant) =>
        restaurant.name.toLowerCase().includes(activeCategory.toLowerCase())
      );
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(
        (restaurant) =>
          restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          restaurant.description
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase())
      );
    }

    // Filter by rating - ONLY if rating filter is set in FiltersContext
    if (filters.rating !== null && filters.rating !== undefined) {
      const beforeRatingFilter = filtered.length;
      filtered = filtered.filter((restaurant) => {
        const rating = parseFloat(restaurant.rating) || 0;
        const minRating = filters.rating || 0;

        const passesRating = rating >= minRating;

        return passesRating;
      });
    }

    // TODO: Add delivery fee filter when it's added to FiltersContext

    // Sort restaurants
    if (filters.sortBy === "rating") {
      filtered.sort(
        (a, b) => parseFloat(b.rating || 0) - parseFloat(a.rating || 0)
      );
    } else if (filters.sortBy === "deliveryFee") {
      filtered.sort(
        (a, b) =>
          parseFloat(a.delivery_fee || 0) - parseFloat(b.delivery_fee || 0)
      );
    } else if (filters.sortBy === "deliveryTime") {
      filtered.sort((a, b) => {
        const aTime = a.average_preparation_time || 30;
        const bTime = b.average_preparation_time || 30;
        return aTime - bTime;
      });
    }

    setFilteredRestaurants(filtered);
    setDisplayRestaurants(filtered);
  }, [restaurants, activeCategory, searchQuery, filters]);

  const clearFilters = () => {
    setActiveCategory("All");
    setSearchQuery("");
  };

  const handleRefresh = async () => {
    setLoading(true);
    setError(null);

    // Simulate API refresh
    return new Promise((resolve) => {
      setTimeout(() => {
        setLoading(false);
        resolve();
      }, 1500);
    });
  };

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      <div className='container mx-auto px-4 py-8 animate-fade-in'>
        {/* Header */}
        <div className='mb-8'>
          <h1 className='text-3xl font-bold mb-2'>Restaurants</h1>
          <p className='text-gray-600'>Discover amazing restaurants near you</p>
        </div>

        {/* Search & Filter Bar */}
        <div className='mb-8'>
          <div className='flex flex-col lg:flex-row gap-4 items-center mb-6'>
            <div className='relative flex-1'>
              <Search
                className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
                size={20}
              />
              <input
                type='text'
                placeholder='Search for restaurants or cuisines'
                className='w-full pl-10 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            <div className='flex items-center space-x-3'>
              <SortOptions />
              <Button
                variant={appliedFiltersCount > 0 ? "primary" : "outline"}
                icon={<Filter size={18} />}
                onClick={() => setIsFilterPanelOpen(true)}
                className='relative'
              >
                Filters
                {appliedFiltersCount > 0 && (
                  <span className='absolute -top-2 -right-2 bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center'>
                    {appliedFiltersCount}
                  </span>
                )}
              </Button>
            </div>
          </div>

          {/* Quick Filters */}
          <div className='mb-6'>
            <QuickFilters />
          </div>

          {/* Active Filters */}
          <ActiveFilters />
        </div>

        {/* Cuisine Categories */}
        <div className='mb-8'>
          <h3 className='text-lg font-semibold mb-4'>Browse by Cuisine</h3>
          <div className='flex overflow-x-auto py-2 scrollbar-hide'>
            <div className='flex space-x-3'>
              {cuisineCategories.map((category) => (
                <button
                  key={category}
                  className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                    activeCategory === category
                      ? "bg-primary-500 text-white shadow-md"
                      : "bg-white border border-gray-200 text-text-primary hover:bg-gray-50 hover:border-primary-300"
                  }`}
                  onClick={() => setActiveCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Restaurant List */}
        <div>
          <div className='flex items-center justify-between mb-6'>
            <h2 className='text-2xl font-poppins font-semibold'>
              {loading
                ? "Loading restaurants..."
                : displayRestaurants.length === 0
                ? "No restaurants found"
                : `${displayRestaurants.length} Restaurant${
                    displayRestaurants.length !== 1 ? "s" : ""
                  } Found`}
            </h2>

            {!loading && displayRestaurants.length > 0 && (
              <div className='text-sm text-gray-600'>
                Showing results for{" "}
                {activeCategory !== "All" ? activeCategory : "all cuisines"}
                {searchQuery && ` matching "${searchQuery}"`}
              </div>
            )}
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {loading || isRetrying ? (
              <RestaurantGridSkeleton count={6} />
            ) : error ? (
              <div className='col-span-full'>
                <NetworkError
                  error={error}
                  onRetry={() =>
                    retry(() => {
                      // Simulate API call that might fail
                      return new Promise((resolve, reject) => {
                        setTimeout(() => {
                          if (Math.random() > 0.7) {
                            setError(null);
                            resolve();
                          } else {
                            reject(new Error("Failed to fetch restaurants"));
                          }
                        }, 1000);
                      });
                    })
                  }
                  retryCount={retryCount}
                  variant='card'
                />
              </div>
            ) : displayRestaurants.length === 0 ? (
              <div className='col-span-full'>
                {searchQuery ? (
                  <NoSearchResults
                    searchTerm={searchQuery}
                    onClearSearch={() => setSearchQuery("")}
                  />
                ) : (
                  <NoRestaurantsFound onClearFilters={clearFilters} />
                )}
              </div>
            ) : (
              displayRestaurants.map((restaurant) => (
                <RestaurantCard
                  key={restaurant.id}
                  restaurant={restaurant}
                  variant='default'
                />
              ))
            )}
          </div>
        </div>

        {/* Filter Panel */}
        <FilterPanel
          isOpen={isFilterPanelOpen}
          onClose={() => setIsFilterPanelOpen(false)}
        />
      </div>
    </PullToRefresh>
  );
};

export default RestaurantList;
