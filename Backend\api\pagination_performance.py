"""
Performance optimization utilities for pagination

This module provides tools for optimizing pagination performance:
- Database query optimization
- Caching strategies
- Performance monitoring
- Query analysis
"""

from django.core.cache import cache
from django.db import connection
from django.db.models import Prefetch, Q
from django.conf import settings
import time
import logging
import hashlib
import json
from functools import wraps

logger = logging.getLogger(__name__)


class PaginationPerformanceMonitor:
    """Monitor and track pagination performance metrics"""
    
    def __init__(self):
        self.metrics = {}
    
    def track_query_performance(self, view_name, query_time, query_count, cache_hit=False):
        """Track query performance metrics"""
        key = f"pagination_perf:{view_name}"
        
        # Get existing metrics
        metrics = cache.get(key, {
            'total_requests': 0,
            'total_query_time': 0,
            'total_queries': 0,
            'cache_hits': 0,
            'avg_query_time': 0,
            'avg_queries_per_request': 0,
            'cache_hit_rate': 0,
        })
        
        # Update metrics
        metrics['total_requests'] += 1
        metrics['total_query_time'] += query_time
        metrics['total_queries'] += query_count
        if cache_hit:
            metrics['cache_hits'] += 1
        
        # Calculate averages
        metrics['avg_query_time'] = metrics['total_query_time'] / metrics['total_requests']
        metrics['avg_queries_per_request'] = metrics['total_queries'] / metrics['total_requests']
        metrics['cache_hit_rate'] = (metrics['cache_hits'] / metrics['total_requests']) * 100
        
        # Store metrics for 24 hours
        cache.set(key, metrics, 86400)
        
        return metrics
    
    def get_performance_report(self, view_name=None):
        """Get performance report for a view or all views"""
        if view_name:
            return cache.get(f"pagination_perf:{view_name}", {})
        
        # Get all performance metrics
        all_metrics = {}
        cache_keys = cache.keys("pagination_perf:*") if hasattr(cache, 'keys') else []
        
        for key in cache_keys:
            view = key.replace("pagination_perf:", "")
            all_metrics[view] = cache.get(key, {})
        
        return all_metrics


class QueryOptimizer:
    """Optimize database queries for pagination"""
    
    @staticmethod
    def optimize_queryset(queryset, view_class=None):
        """Apply common optimizations to querysets"""
        model = queryset.model
        
        # Auto-detect and apply select_related for foreign keys
        foreign_keys = [
            field.name for field in model._meta.fields 
            if field.many_to_one and not field.null
        ]
        
        if foreign_keys:
            queryset = queryset.select_related(*foreign_keys)
        
        # Auto-detect and apply prefetch_related for reverse foreign keys
        reverse_fks = [
            field.get_accessor_name() for field in model._meta.get_fields()
            if field.one_to_many or field.many_to_many
        ]
        
        # Only prefetch commonly used relations to avoid over-fetching
        common_relations = ['menu_items', 'orders', 'reviews', 'categories']
        prefetch_fields = [
            field for field in reverse_fks 
            if any(common in field for common in common_relations)
        ]
        
        if prefetch_fields:
            queryset = queryset.prefetch_related(*prefetch_fields[:3])  # Limit to 3
        
        return queryset
    
    @staticmethod
    def add_pagination_indexes(model_class):
        """Suggest database indexes for pagination performance"""
        suggestions = []
        
        # Common pagination fields
        pagination_fields = ['created_at', 'updated_at', 'id']
        
        for field in pagination_fields:
            if hasattr(model_class, field):
                suggestions.append(f"CREATE INDEX idx_{model_class._meta.db_table}_{field} ON {model_class._meta.db_table} ({field} DESC);")
        
        # Composite indexes for filtering + pagination
        filter_fields = ['status', 'is_active', 'category_id', 'restaurant_id']
        
        for filter_field in filter_fields:
            if hasattr(model_class, filter_field):
                for pagination_field in pagination_fields:
                    if hasattr(model_class, pagination_field):
                        suggestions.append(
                            f"CREATE INDEX idx_{model_class._meta.db_table}_{filter_field}_{pagination_field} "
                            f"ON {model_class._meta.db_table} ({filter_field}, {pagination_field} DESC);"
                        )
        
        return suggestions


class PaginationCache:
    """Advanced caching for pagination results"""
    
    def __init__(self, timeout=300):  # 5 minutes default
        self.timeout = timeout
    
    def generate_cache_key(self, request, view_name, queryset=None):
        """Generate a unique cache key for pagination"""
        # Include relevant request parameters
        params = {
            'page': request.GET.get('page', 1),
            'page_size': request.GET.get('page_size', 20),
            'search': request.GET.get('search', ''),
            'ordering': request.GET.get('ordering', ''),
        }
        
        # Include filter parameters
        filter_params = {
            k: v for k, v in request.GET.items() 
            if k not in ['page', 'page_size'] and v
        }
        params.update(filter_params)
        
        # Include user context if relevant
        if request.user.is_authenticated:
            params['user_id'] = request.user.id
            params['user_role'] = getattr(request.user, 'role', 'customer')
        
        # Create hash of parameters
        params_str = json.dumps(params, sort_keys=True)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()
        
        return f"pagination:{view_name}:{params_hash}"
    
    def get_cached_page(self, cache_key):
        """Get cached pagination page"""
        return cache.get(cache_key)
    
    def cache_page(self, cache_key, page_data, count_data=None):
        """Cache pagination page with metadata"""
        cache_data = {
            'page_data': page_data,
            'count_data': count_data,
            'cached_at': time.time(),
        }
        
        cache.set(cache_key, cache_data, self.timeout)
        return cache_data
    
    def invalidate_cache_pattern(self, pattern):
        """Invalidate cache entries matching a pattern"""
        if hasattr(cache, 'delete_pattern'):
            cache.delete_pattern(pattern)
        else:
            # Fallback for cache backends that don't support pattern deletion
            logger.warning("Cache backend doesn't support pattern deletion")


def performance_monitor(func):
    """Decorator to monitor pagination performance"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        start_time = time.time()
        initial_queries = len(connection.queries)
        
        # Check for cached result
        cache_key = None
        cached_result = None
        
        if hasattr(self, 'request'):
            cache_manager = PaginationCache()
            cache_key = cache_manager.generate_cache_key(
                self.request, 
                self.__class__.__name__
            )
            cached_result = cache_manager.get_cached_page(cache_key)
        
        if cached_result:
            # Return cached result
            end_time = time.time()
            query_time = end_time - start_time
            
            # Track performance
            monitor = PaginationPerformanceMonitor()
            monitor.track_query_performance(
                self.__class__.__name__,
                query_time,
                0,  # No new queries for cached result
                cache_hit=True
            )
            
            return cached_result['page_data']
        
        # Execute original function
        result = func(self, *args, **kwargs)
        
        # Calculate performance metrics
        end_time = time.time()
        query_time = end_time - start_time
        query_count = len(connection.queries) - initial_queries
        
        # Cache the result
        if cache_key and hasattr(self, 'request'):
            cache_manager = PaginationCache()
            cache_manager.cache_page(cache_key, result)
        
        # Track performance
        monitor = PaginationPerformanceMonitor()
        monitor.track_query_performance(
            self.__class__.__name__,
            query_time,
            query_count,
            cache_hit=False
        )
        
        # Log slow queries
        if query_time > 1.0:  # Log queries taking more than 1 second
            logger.warning(
                f"Slow pagination query in {self.__class__.__name__}: "
                f"{query_time:.2f}s, {query_count} queries"
            )
        
        return result
    
    return wrapper


class SmartPaginationMixin:
    """Mixin to add smart pagination features to ViewSets"""
    
    def get_queryset(self):
        """Override to apply query optimizations"""
        queryset = super().get_queryset()
        
        # Apply query optimizations
        optimizer = QueryOptimizer()
        queryset = optimizer.optimize_queryset(queryset, self.__class__)
        
        return queryset
    
    @performance_monitor
    def paginate_queryset(self, queryset):
        """Override to add performance monitoring"""
        return super().paginate_queryset(queryset)
    
    def get_performance_metrics(self):
        """Get performance metrics for this view"""
        monitor = PaginationPerformanceMonitor()
        return monitor.get_performance_report(self.__class__.__name__)


# Utility functions for performance analysis
def analyze_pagination_performance():
    """Analyze overall pagination performance"""
    monitor = PaginationPerformanceMonitor()
    all_metrics = monitor.get_performance_report()
    
    analysis = {
        'total_views': len(all_metrics),
        'slow_views': [],
        'high_query_views': [],
        'low_cache_hit_views': [],
        'recommendations': [],
    }
    
    for view_name, metrics in all_metrics.items():
        # Identify slow views (>500ms average)
        if metrics.get('avg_query_time', 0) > 0.5:
            analysis['slow_views'].append({
                'view': view_name,
                'avg_time': metrics['avg_query_time']
            })
        
        # Identify views with too many queries (>5 per request)
        if metrics.get('avg_queries_per_request', 0) > 5:
            analysis['high_query_views'].append({
                'view': view_name,
                'avg_queries': metrics['avg_queries_per_request']
            })
        
        # Identify views with low cache hit rate (<50%)
        if metrics.get('cache_hit_rate', 0) < 50:
            analysis['low_cache_hit_views'].append({
                'view': view_name,
                'cache_hit_rate': metrics['cache_hit_rate']
            })
    
    # Generate recommendations
    if analysis['slow_views']:
        analysis['recommendations'].append(
            "Consider adding database indexes or optimizing queries for slow views"
        )
    
    if analysis['high_query_views']:
        analysis['recommendations'].append(
            "Use select_related/prefetch_related to reduce query count"
        )
    
    if analysis['low_cache_hit_views']:
        analysis['recommendations'].append(
            "Increase cache timeout or review cache invalidation strategy"
        )
    
    return analysis


def generate_index_suggestions():
    """Generate database index suggestions for all models"""
    from django.apps import apps
    
    suggestions = []
    optimizer = QueryOptimizer()
    
    for model in apps.get_models():
        model_suggestions = optimizer.add_pagination_indexes(model)
        suggestions.extend(model_suggestions)
    
    return suggestions
