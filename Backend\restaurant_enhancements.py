#!/usr/bin/env python3
"""
Restaurant Backend Enhancements
Critical missing features for a complete restaurant management system
"""

# Additional Models to add to restaurant/models.py

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from users.models import User
from restaurant.models import Restaurant, MenuItem

# 1. RESTAURANT BUSINESS FEATURES
class RestaurantCuisine(models.Model):
    """Restaurant cuisine types"""
    name = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True)
    
    def __str__(self):
        return self.name

class RestaurantTag(models.Model):
    """Restaurant categories/tags"""
    name = models.CharField(max_length=50, unique=True)
    icon = models.CharField(max_length=50, blank=True)  # Icon class name
    
    def __str__(self):
        return self.name

# Enhanced Restaurant Model (additions)
"""
Add these fields to existing Restaurant model:

cuisines = models.ManyToManyField(RestaurantCuisine, blank=True)
tags = models.ManyToManyField(RestaurantTag, blank=True)
minimum_order = models.DecimalField(max_digits=8, decimal_places=2, default=0)
average_prep_time = models.PositiveIntegerField(default=30)  # minutes
service_radius = models.PositiveIntegerField(default=5)  # km
accepts_cash = models.BooleanField(default=True)
accepts_card = models.BooleanField(default=True)
website = models.URLField(blank=True)
facebook = models.URLField(blank=True)
instagram = models.URLField(blank=True)
"""

# 2. REVIEWS & RATINGS SYSTEM
class RestaurantReview(models.Model):
    """Customer reviews for restaurants"""
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name='reviews')
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='restaurant_reviews')
    order = models.ForeignKey('orders.Order', on_delete=models.SET_NULL, null=True, blank=True)
    
    rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    title = models.CharField(max_length=200, blank=True)
    comment = models.TextField()
    
    # Rating breakdown
    food_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True
    )
    service_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True
    )
    delivery_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)], null=True, blank=True
    )
    
    is_verified = models.BooleanField(default=False)  # Verified purchase
    is_approved = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('restaurant', 'customer', 'order')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.customer.name} - {self.restaurant.name} ({self.rating}★)"

class ReviewResponse(models.Model):
    """Restaurant responses to reviews"""
    review = models.OneToOneField(RestaurantReview, on_delete=models.CASCADE, related_name='response')
    restaurant_owner = models.ForeignKey(User, on_delete=models.CASCADE)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Response to {self.review}"

# 3. PROMOTIONS & OFFERS
class RestaurantPromotion(models.Model):
    """Restaurant promotions and offers"""
    DISCOUNT_TYPES = [
        ('percentage', 'Percentage'),
        ('fixed', 'Fixed Amount'),
        ('free_delivery', 'Free Delivery'),
        ('buy_one_get_one', 'Buy One Get One'),
    ]
    
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name='promotions')
    title = models.CharField(max_length=200)
    description = models.TextField()
    discount_type = models.CharField(max_length=20, choices=DISCOUNT_TYPES)
    discount_value = models.DecimalField(max_digits=8, decimal_places=2)
    
    minimum_order = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    max_discount = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    
    # Usage limits
    max_uses = models.PositiveIntegerField(null=True, blank=True)
    max_uses_per_customer = models.PositiveIntegerField(default=1)
    current_uses = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"{self.restaurant.name} - {self.title}"

# 4. MENU ITEM VARIANTS & ADD-ONS
class MenuItemVariant(models.Model):
    """Menu item variants (sizes, options)"""
    menu_item = models.ForeignKey(MenuItem, on_delete=models.CASCADE, related_name='variants')
    name = models.CharField(max_length=100)  # Small, Medium, Large
    price_adjustment = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    is_default = models.BooleanField(default=False)
    is_available = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ('menu_item', 'name')
    
    def __str__(self):
        return f"{self.menu_item.name} - {self.name}"

class MenuItemAddon(models.Model):
    """Menu item add-ons and customizations"""
    ADDON_TYPES = [
        ('required', 'Required'),
        ('optional', 'Optional'),
        ('extra', 'Extra'),
    ]
    
    menu_item = models.ForeignKey(MenuItem, on_delete=models.CASCADE, related_name='addons')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    price = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    addon_type = models.CharField(max_length=20, choices=ADDON_TYPES, default='optional')
    is_available = models.BooleanField(default=True)
    
    def __str__(self):
        return f"{self.menu_item.name} - {self.name}"

# 5. RESTAURANT ANALYTICS
class RestaurantAnalytics(models.Model):
    """Daily analytics for restaurants"""
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name='analytics')
    date = models.DateField()
    
    # Order metrics
    total_orders = models.PositiveIntegerField(default=0)
    completed_orders = models.PositiveIntegerField(default=0)
    cancelled_orders = models.PositiveIntegerField(default=0)
    
    # Revenue metrics
    gross_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    net_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    commission_paid = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Performance metrics
    average_order_value = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    average_prep_time = models.PositiveIntegerField(default=0)  # minutes
    customer_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    
    class Meta:
        unique_together = ('restaurant', 'date')
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.restaurant.name} - {self.date}"

# 6. INVENTORY MANAGEMENT
class Ingredient(models.Model):
    """Restaurant ingredients"""
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name='ingredients')
    name = models.CharField(max_length=100)
    unit = models.CharField(max_length=20)  # kg, pieces, liters, etc.
    cost_per_unit = models.DecimalField(max_digits=8, decimal_places=2)
    current_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    minimum_stock = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    def __str__(self):
        return f"{self.restaurant.name} - {self.name}"

class MenuItemIngredient(models.Model):
    """Ingredients used in menu items"""
    menu_item = models.ForeignKey(MenuItem, on_delete=models.CASCADE, related_name='ingredients')
    ingredient = models.ForeignKey(Ingredient, on_delete=models.CASCADE)
    quantity_needed = models.DecimalField(max_digits=8, decimal_places=2)
    
    class Meta:
        unique_together = ('menu_item', 'ingredient')
    
    def __str__(self):
        return f"{self.menu_item.name} - {self.ingredient.name}"

# 7. RESTAURANT STAFF MANAGEMENT
class RestaurantStaff(models.Model):
    """Restaurant staff members"""
    STAFF_ROLES = [
        ('manager', 'Manager'),
        ('chef', 'Chef'),
        ('cashier', 'Cashier'),
        ('waiter', 'Waiter'),
        ('delivery', 'Delivery'),
    ]
    
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name='staff')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='restaurant_staff')
    role = models.CharField(max_length=20, choices=STAFF_ROLES)
    hire_date = models.DateField()
    is_active = models.BooleanField(default=True)
    
    # Permissions
    can_manage_menu = models.BooleanField(default=False)
    can_manage_orders = models.BooleanField(default=False)
    can_view_analytics = models.BooleanField(default=False)
    
    class Meta:
        unique_together = ('restaurant', 'user')
    
    def __str__(self):
        return f"{self.restaurant.name} - {self.user.name} ({self.role})"

"""
API ENDPOINTS TO ADD:

1. Reviews:
   - GET/POST /restaurant/restaurants/{id}/reviews/
   - POST /restaurant/reviews/{id}/response/

2. Promotions:
   - GET/POST /restaurant/restaurants/{id}/promotions/
   - PATCH /restaurant/promotions/{id}/

3. Analytics:
   - GET /restaurant/restaurants/{id}/analytics/
   - GET /restaurant/restaurants/{id}/analytics/summary/

4. Inventory:
   - GET/POST /restaurant/restaurants/{id}/ingredients/
   - GET/POST /restaurant/menu-items/{id}/ingredients/

5. Staff:
   - GET/POST /restaurant/restaurants/{id}/staff/
   - PATCH /restaurant/staff/{id}/permissions/

6. Menu Enhancements:
   - GET/POST /restaurant/menu-items/{id}/variants/
   - GET/POST /restaurant/menu-items/{id}/addons/
"""
