import React, { useState, useEffect } from "react";
import {
  Package,
  MapPin,
  Clock,
  Phone,
  Navigation,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Filter,
  Search,
  Eye,
  DollarSign,
  User,
  Store,
  Route,
  Timer,
  Star,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import Badge from "../../components/common/Badge";
import Input from "../../components/common/Input";
import MobileOrderCard from "../../components/delivery/MobileOrderCard";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";
import { useResponsive } from "../../hooks/useResponsive";

function NewDeliveryOrders() {
  const { user } = useAuth();
  const { isMobile } = useResponsive();

  // State management
  const [availableOrders, setAvailableOrders] = useState([]);
  const [myOrders, setMyOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("available");
  const [searchQuery, setSearchQuery] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderDetails, setShowOrderDetails] = useState(false);

  // Load orders data
  const loadOrders = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load available orders
      const availableResult = await deliveryAgentApi.getAvailableOrders();
      if (availableResult.success) {
        setAvailableOrders(availableResult.data.orders || []);
      }

      // Load dashboard to get my orders
      const dashboardResult = await deliveryAgentApi.getDashboard();
      if (dashboardResult.success) {
        setMyOrders(dashboardResult.data.active_orders || []);
      }
    } catch (err) {
      console.error("Orders load error:", err);
      setError("Failed to load orders");
    } finally {
      setLoading(false);
    }
  };

  // Refresh orders
  const refreshOrders = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  // Accept order
  const acceptOrder = async (orderId) => {
    try {
      const result = await deliveryAgentApi.acceptOrder(orderId);

      if (result.success) {
        await refreshOrders();
        // Show success message
        alert("Order accepted successfully!");
      } else {
        setError(result.error?.message || "Failed to accept order");
      }
    } catch (err) {
      console.error("Accept order error:", err);
      setError("Failed to accept order");
    }
  };

  // Update order status
  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      const result = await deliveryAgentApi.updateOrderStatus(
        orderId,
        newStatus
      );

      if (result.success) {
        await refreshOrders();
        // Show success message
        alert(`Order status updated to ${newStatus.replace("_", " ")}`);
      } else {
        setError(result.error?.message || "Failed to update order status");
      }
    } catch (err) {
      console.error("Update order status error:", err);
      setError("Failed to update order status");
    }
  };

  // Filter orders based on search
  const filteredAvailableOrders = availableOrders.filter(
    (order) =>
      order.restaurant?.name
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      order.customer?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.delivery_address?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredMyOrders = myOrders.filter(
    (order) =>
      order.restaurant_name
        ?.toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      order.customer_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      order.delivery_address?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  useEffect(() => {
    loadOrders();
  }, [user]);

  // Loading state
  if (loading) {
    return (
      <div className='min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-600 text-lg'>Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50'>
      {/* Header */}
      <div className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center py-4'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900'>Orders</h1>
              <p className='text-sm text-gray-500'>
                Manage your delivery orders
              </p>
            </div>

            <Button
              variant='outline'
              onClick={refreshOrders}
              disabled={refreshing}
              className='flex items-center space-x-2'
            >
              <RefreshCw
                className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
              />
              <span>Refresh</span>
            </Button>
          </div>
        </div>
      </div>

      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
        {/* Error Message */}
        {error && (
          <Card className='mb-6 p-4 bg-red-50 border-red-200'>
            <div className='flex items-center space-x-2'>
              <AlertCircle className='h-5 w-5 text-red-500' />
              <p className='text-red-700'>{error}</p>
              <Button
                variant='outline'
                size='sm'
                onClick={() => setError(null)}
                className='ml-auto'
              >
                Dismiss
              </Button>
            </div>
          </Card>
        )}

        {/* Search and Filters */}
        <Card className='p-4 mb-6'>
          <div className='flex flex-col sm:flex-row gap-4'>
            <div className='flex-1'>
              <Input
                type='text'
                placeholder='Search orders by restaurant, customer, or address...'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className='w-full'
                icon={<Search className='h-4 w-4' />}
              />
            </div>
          </div>
        </Card>

        {/* Tabs */}
        <div className='flex space-x-1 mb-6'>
          <Button
            variant={activeTab === "available" ? "primary" : "outline"}
            onClick={() => setActiveTab("available")}
            className='flex items-center space-x-2'
          >
            <Package className='h-4 w-4' />
            <span>Available Orders ({filteredAvailableOrders.length})</span>
          </Button>
          <Button
            variant={activeTab === "my-orders" ? "primary" : "outline"}
            onClick={() => setActiveTab("my-orders")}
            className='flex items-center space-x-2'
          >
            <CheckCircle className='h-4 w-4' />
            <span>My Orders ({filteredMyOrders.length})</span>
          </Button>
        </div>

        {/* Orders List */}
        <div className={isMobile ? "space-y-2" : "space-y-4"}>
          {activeTab === "available" ? (
            // Available Orders
            filteredAvailableOrders.length > 0 ? (
              isMobile ? (
                // Mobile view with MobileOrderCard
                filteredAvailableOrders.map((order) => (
                  <MobileOrderCard
                    key={order.id}
                    order={order}
                    type='available'
                    onAccept={acceptOrder}
                    onViewDetails={setSelectedOrder}
                  />
                ))
              ) : (
                // Desktop view with regular cards
                filteredAvailableOrders.map((order) => (
                  <Card
                    key={order.id}
                    className='p-6 hover:shadow-lg transition-shadow'
                  >
                    <div className='flex justify-between items-start'>
                      <div className='flex-1'>
                        <div className='flex items-center space-x-3 mb-3'>
                          <div className='p-2 bg-blue-100 rounded-full'>
                            <Store className='h-5 w-5 text-blue-600' />
                          </div>
                          <div>
                            <h3 className='text-lg font-semibold text-gray-900'>
                              {order.restaurant?.name}
                            </h3>
                            <p className='text-sm text-gray-500'>
                              {order.restaurant?.address}
                            </p>
                          </div>
                        </div>

                        <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
                          <div className='flex items-center space-x-2'>
                            <User className='h-4 w-4 text-gray-400' />
                            <span className='text-sm text-gray-600'>
                              Customer: {order.customer?.name}
                            </span>
                          </div>
                          <div className='flex items-center space-x-2'>
                            <Phone className='h-4 w-4 text-gray-400' />
                            <span className='text-sm text-gray-600'>
                              {order.customer?.phone}
                            </span>
                          </div>
                          <div className='flex items-center space-x-2'>
                            <MapPin className='h-4 w-4 text-gray-400' />
                            <span className='text-sm text-gray-600'>
                              {order.delivery_address}
                            </span>
                          </div>
                          <div className='flex items-center space-x-2'>
                            <Route className='h-4 w-4 text-gray-400' />
                            <span className='text-sm text-gray-600'>
                              Distance: {order.estimated_distance}
                            </span>
                          </div>
                        </div>

                        <div className='flex items-center space-x-4 text-sm text-gray-500'>
                          <div className='flex items-center space-x-1'>
                            <Clock className='h-4 w-4' />
                            <span>Est. {order.estimated_time}</span>
                          </div>
                          <div className='flex items-center space-x-1'>
                            <Timer className='h-4 w-4' />
                            <span>
                              {new Date(order.created_at).toLocaleTimeString()}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className='text-right ml-6'>
                        <div className='mb-4'>
                          <p className='text-2xl font-bold text-green-600'>
                            ${order.total_amount?.toFixed(2)}
                          </p>
                          <p className='text-sm text-gray-500'>
                            Delivery Fee: $
                            {order.estimated_delivery_fee?.toFixed(2)}
                          </p>
                        </div>

                        <div className='space-y-2'>
                          <Button
                            onClick={() => acceptOrder(order.id)}
                            className='w-full flex items-center justify-center space-x-2'
                          >
                            <CheckCircle className='h-4 w-4' />
                            <span>Accept Order</span>
                          </Button>
                          <Button
                            variant='outline'
                            size='sm'
                            onClick={() => {
                              setSelectedOrder(order);
                              setShowOrderDetails(true);
                            }}
                            className='w-full flex items-center justify-center space-x-2'
                          >
                            <Eye className='h-4 w-4' />
                            <span>View Details</span>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))
              )
            ) : (
              <Card className='p-8 text-center'>
                <Package className='h-16 w-16 text-gray-400 mx-auto mb-4' />
                <h3 className='text-lg font-medium text-gray-900 mb-2'>
                  No Available Orders
                </h3>
                <p className='text-gray-500'>
                  {searchQuery
                    ? "No orders match your search criteria."
                    : "There are no available orders at the moment."}
                </p>
              </Card>
            )
          ) : // My Orders
          filteredMyOrders.length > 0 ? (
            isMobile ? (
              // Mobile view with MobileOrderCard
              filteredMyOrders.map((order) => (
                <MobileOrderCard
                  key={order.id}
                  order={order}
                  type='active'
                  onUpdateStatus={updateOrderStatus}
                  onViewDetails={setSelectedOrder}
                />
              ))
            ) : (
              // Desktop view with regular cards
              filteredMyOrders.map((order) => (
                <Card
                  key={order.id}
                  className='p-6 hover:shadow-lg transition-shadow'
                >
                  <div className='flex justify-between items-start'>
                    <div className='flex-1'>
                      <div className='flex items-center space-x-3 mb-3'>
                        <div className='p-2 bg-green-100 rounded-full'>
                          <Store className='h-5 w-5 text-green-600' />
                        </div>
                        <div>
                          <h3 className='text-lg font-semibold text-gray-900'>
                            {order.restaurant_name}
                          </h3>
                          <Badge
                            variant={
                              order.status === "assigned"
                                ? "warning"
                                : order.status === "picked_up"
                                ? "info"
                                : order.status === "on_the_way"
                                ? "success"
                                : "secondary"
                            }
                          >
                            {order.status?.replace("_", " ")}
                          </Badge>
                        </div>
                      </div>

                      <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
                        <div className='flex items-center space-x-2'>
                          <User className='h-4 w-4 text-gray-400' />
                          <span className='text-sm text-gray-600'>
                            Customer: {order.customer_name}
                          </span>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <MapPin className='h-4 w-4 text-gray-400' />
                          <span className='text-sm text-gray-600'>
                            {order.delivery_address}
                          </span>
                        </div>
                      </div>

                      <div className='flex items-center space-x-4 text-sm text-gray-500'>
                        <div className='flex items-center space-x-1'>
                          <Clock className='h-4 w-4' />
                          <span>
                            {new Date(order.created_at).toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className='text-right ml-6'>
                      <div className='mb-4'>
                        <p className='text-2xl font-bold text-green-600'>
                          ${order.total_amount?.toFixed(2)}
                        </p>
                      </div>

                      <div className='space-y-2'>
                        {order.status === "assigned" && (
                          <Button
                            onClick={() =>
                              updateOrderStatus(order.id, "accepted")
                            }
                            className='w-full flex items-center justify-center space-x-2'
                          >
                            <CheckCircle className='h-4 w-4' />
                            <span>Accept Order</span>
                          </Button>
                        )}
                        {order.status === "accepted" && (
                          <Button
                            onClick={() =>
                              updateOrderStatus(
                                order.id,
                                "en_route_to_restaurant"
                              )
                            }
                            className='w-full flex items-center justify-center space-x-2'
                          >
                            <Navigation className='h-4 w-4' />
                            <span>Head to Restaurant</span>
                          </Button>
                        )}
                        {order.status === "en_route_to_restaurant" && (
                          <Button
                            onClick={() =>
                              updateOrderStatus(
                                order.id,
                                "arrived_at_restaurant"
                              )
                            }
                            className='w-full flex items-center justify-center space-x-2'
                          >
                            <MapPin className='h-4 w-4' />
                            <span>Arrived at Restaurant</span>
                          </Button>
                        )}
                        {order.status === "arrived_at_restaurant" && (
                          <Button
                            onClick={() =>
                              updateOrderStatus(order.id, "picked_up")
                            }
                            className='w-full flex items-center justify-center space-x-2'
                          >
                            <Package className='h-4 w-4' />
                            <span>Mark Picked Up</span>
                          </Button>
                        )}
                        {order.status === "picked_up" && (
                          <Button
                            onClick={() =>
                              updateOrderStatus(
                                order.id,
                                "en_route_to_customer"
                              )
                            }
                            className='w-full flex items-center justify-center space-x-2'
                          >
                            <Navigation className='h-4 w-4' />
                            <span>Head to Customer</span>
                          </Button>
                        )}
                        {(order.status === "en_route_to_customer" ||
                          order.status === "arrived_at_customer") && (
                          <Button
                            onClick={() =>
                              updateOrderStatus(order.id, "delivered")
                            }
                            className='w-full flex items-center justify-center space-x-2'
                          >
                            <CheckCircle className='h-4 w-4' />
                            <span>Mark Delivered</span>
                          </Button>
                        )}
                        <Button
                          variant='outline'
                          size='sm'
                          onClick={() => {
                            setSelectedOrder(order);
                            setShowOrderDetails(true);
                          }}
                          className='w-full flex items-center justify-center space-x-2'
                        >
                          <Eye className='h-4 w-4' />
                          <span>View Details</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))
            )
          ) : (
            <Card className='p-8 text-center'>
              <CheckCircle className='h-16 w-16 text-gray-400 mx-auto mb-4' />
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                No Active Orders
              </h3>
              <p className='text-gray-500'>
                {searchQuery
                  ? "No orders match your search criteria."
                  : "You have no active orders at the moment."}
              </p>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

export default NewDeliveryOrders;
