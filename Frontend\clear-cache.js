// Run this in browser console to completely clear all cache and fix issues

console.log('🧹 Starting complete cache clear...');

// 1. Clear localStorage
localStorage.clear();
console.log('✅ localStorage cleared');

// 2. Clear sessionStorage  
sessionStorage.clear();
console.log('✅ sessionStorage cleared');

// 3. Unregister service workers
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(let registration of registrations) {
      registration.unregister();
      console.log('✅ Service worker unregistered');
    }
  });
}

// 4. Clear all caches
if ('caches' in window) {
  caches.keys().then(function(names) {
    for (let name of names) {
      caches.delete(name);
      console.log('✅ Cache deleted:', name);
    }
  });
}

// 5. Set fresh admin user
const adminUser = {
  id: 'user-4',
  name: 'Admin User',
  email: '<EMAIL>',
  role: 'admin',
  avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=600',
  phone: '************',
  address: '101 Admin Rd, City, Country'
};

localStorage.setItem('afghanSofraUser', JSON.stringify(adminUser));
console.log('✅ Fresh admin user set');

console.log('🎉 Cache clear complete! Reloading page...');

// 6. Force reload
setTimeout(() => {
  window.location.reload(true);
}, 1000);
