import React, { useState } from 'react';
import { User, Phone, MapPin, Upload, Camera, FileText, Shield, CheckCircle } from 'lucide-react';

const AgentRegistrationForm = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Personal Information
    fullName: '',
    fatherName: '', // Important in Afghan culture
    nationalId: '', // Tazkira number
    phoneNumber: '',
    emergencyContact: '',
    dateOfBirth: '',
    gender: '',
    maritalStatus: '',
    
    // Address Information
    province: '',
    district: '',
    area: '',
    streetAddress: '',
    nearbyLandmark: '',
    
    // Vehicle Information
    vehicleType: 'motorcycle', // Most common in Afghanistan
    vehicleModel: '',
    vehicleYear: '',
    licensePlate: '',
    drivingLicense: '',
    vehicleRegistration: '',
    
    // Documents
    tazkiraFront: null,
    tazkiraBack: null,
    drivingLicenseDoc: null,
    vehicleRegistrationDoc: null,
    profilePhoto: null,
    
    // References (Important in Afghan culture)
    reference1Name: '',
    reference1Phone: '',
    reference1Relation: '',
    reference2Name: '',
    reference2Phone: '',
    reference2Relation: '',
    
    // Banking Information
    bankName: '',
    accountNumber: '',
    accountHolderName: '',
    mobileWalletNumber: '', // For M-Paisa, etc.
    
    // Availability
    workingDays: [],
    workingHours: { start: '08:00', end: '20:00' },
    preferredZones: [],
    
    // Emergency Information
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelation: '',
    medicalConditions: '',
    
    // Agreement
    termsAccepted: false,
    backgroundCheckConsent: false,
    dataProcessingConsent: false
  });

  const afghanProvinces = [
    'Kabul', 'Herat', 'Kandahar', 'Balkh', 'Nangarhar', 'Kunduz',
    'Takhar', 'Baghlan', 'Ghazni', 'Paktia', 'Khost', 'Laghman',
    'Kapisa', 'Parwan', 'Wardak', 'Logar', 'Bamyan', 'Panjshir',
    'Badakhshan', 'Faryab', 'Jawzjan', 'Sar-e Pol', 'Samangan',
    'Badghis', 'Ghor', 'Daykundi', 'Uruzgan', 'Zabul', 'Paktika',
    'Kunar', 'Nuristan', 'Farah', 'Nimroz', 'Helmand'
  ];

  const vehicleTypes = [
    { value: 'motorcycle', label: 'موټرسایکل (Motorcycle)', icon: '🏍️' },
    { value: 'bicycle', label: 'بایسکل (Bicycle)', icon: '🚲' },
    { value: 'car', label: 'موټر (Car)', icon: '🚗' },
    { value: 'rickshaw', label: 'رکشا (Rickshaw)', icon: '🛺' }
  ];

  const workingDaysOptions = [
    { value: 'saturday', label: 'شنبه (Saturday)' },
    { value: 'sunday', label: 'یکشنبه (Sunday)' },
    { value: 'monday', label: 'دوشنبه (Monday)' },
    { value: 'tuesday', label: 'سه‌شنبه (Tuesday)' },
    { value: 'wednesday', label: 'چهارشنبه (Wednesday)' },
    { value: 'thursday', label: 'پنج‌شنبه (Thursday)' },
    { value: 'friday', label: 'جمعه (Friday)' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (field, file) => {
    setFormData(prev => ({ ...prev, [field]: file }));
  };

  const handleWorkingDaysChange = (day) => {
    setFormData(prev => ({
      ...prev,
      workingDays: prev.workingDays.includes(day)
        ? prev.workingDays.filter(d => d !== day)
        : [...prev.workingDays, day]
    }));
  };

  const nextStep = () => {
    if (currentStep < 6) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {[1, 2, 3, 4, 5, 6].map((step) => (
        <div key={step} className="flex items-center">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            step <= currentStep ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
          }`}>
            {step < currentStep ? <CheckCircle size={16} /> : step}
          </div>
          {step < 6 && (
            <div className={`w-12 h-1 ${step < currentStep ? 'bg-blue-600' : 'bg-gray-200'}`} />
          )}
        </div>
      ))}
    </div>
  );

  const renderPersonalInfo = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        معلومات شخصی (Personal Information)
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            نوم بشپړ (Full Name) *
          </label>
          <input
            type="text"
            value={formData.fullName}
            onChange={(e) => handleInputChange('fullName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            placeholder="احمد علی (Ahmad Ali)"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            د پلار نوم (Father's Name) *
          </label>
          <input
            type="text"
            value={formData.fatherName}
            onChange={(e) => handleInputChange('fatherName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            placeholder="محمد علی (Mohammad Ali)"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            د تذکرې شمیره (National ID/Tazkira) *
          </label>
          <input
            type="text"
            value={formData.nationalId}
            onChange={(e) => handleInputChange('nationalId', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            placeholder="1234567890123"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            د تلیفون شمیره (Phone Number) *
          </label>
          <input
            type="tel"
            value={formData.phoneNumber}
            onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            placeholder="+93 70 123 4567"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            جنسیت (Gender) *
          </label>
          <select
            value={formData.gender}
            onChange={(e) => handleInputChange('gender', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            required
          >
            <option value="">انتخاب وکړئ (Select)</option>
            <option value="male">نارینه (Male)</option>
            <option value="female">ښځینه (Female)</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            د زیږیدو نیټه (Date of Birth) *
          </label>
          <input
            type="date"
            value={formData.dateOfBirth}
            onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>
      </div>
    </div>
  );

  const renderAddressInfo = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        د استوګنې معلومات (Address Information)
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ولایت (Province) *
          </label>
          <select
            value={formData.province}
            onChange={(e) => handleInputChange('province', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            required
          >
            <option value="">انتخاب وکړئ (Select Province)</option>
            {afghanProvinces.map(province => (
              <option key={province} value={province}>{province}</option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ولسوالۍ (District) *
          </label>
          <input
            type="text"
            value={formData.district}
            onChange={(e) => handleInputChange('district', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            placeholder="د کابل ښار (Kabul City)"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            سیمه (Area/Neighborhood) *
          </label>
          <input
            type="text"
            value={formData.area}
            onChange={(e) => handleInputChange('area', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            placeholder="شهر نو (Shar-e-Naw)"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            د کوڅې پته (Street Address)
          </label>
          <input
            type="text"
            value={formData.streetAddress}
            onChange={(e) => handleInputChange('streetAddress', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            placeholder="د ۱۵مې کوڅه (15th Street)"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            نږدې ځای (Nearby Landmark)
          </label>
          <input
            type="text"
            value={formData.nearbyLandmark}
            onChange={(e) => handleInputChange('nearbyLandmark', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            placeholder="د پولیسو مرکز ته نږدې (Near Police Station)"
          />
        </div>
      </div>
    </div>
  );

  // Continue with other step renderers...
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1: return renderPersonalInfo();
      case 2: return renderAddressInfo();
      case 3: return <div>Vehicle Information Step</div>;
      case 4: return <div>Document Upload Step</div>;
      case 5: return <div>References & Banking Step</div>;
      case 6: return <div>Final Review & Agreement Step</div>;
      default: return renderPersonalInfo();
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          د ډیلیوری ایجنټ ثبت نوم (Delivery Agent Registration)
        </h2>
        <p className="text-gray-600">
          د افغان سفرې سره د کار لپاره ثبت نوم وکړئ (Register to work with Afghan Sofra)
        </p>
      </div>

      {renderStepIndicator()}
      
      <form className="space-y-6">
        {renderCurrentStep()}
        
        <div className="flex justify-between pt-6 border-t">
          <button
            type="button"
            onClick={prevStep}
            disabled={currentStep === 1}
            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50"
          >
            شاته (Previous)
          </button>
          
          <button
            type="button"
            onClick={nextStep}
            disabled={currentStep === 6}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {currentStep === 6 ? 'وسپارئ (Submit)' : 'بل ګام (Next)'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AgentRegistrationForm;
