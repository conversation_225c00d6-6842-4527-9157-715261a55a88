# 💰 Afghan Sofra - Cash on Delivery (COD) Flow System

## 📋 Table of Contents

- [COD System Overview](#cod-system-overview)
- [Cash Flow Diagrams](#cash-flow-diagrams)
- [Money Collection Process](#money-collection-process)
- [Revenue Distribution](#revenue-distribution)
- [Settlement Process](#settlement-process)
- [Implementation Guide](#implementation-guide)

## 🎯 COD System Overview

Afghan Sofra operates on a **Cash on Delivery (COD)** model where:
- **👥 Customers** pay cash to delivery agents upon delivery
- **🚚 Delivery Agents** collect cash and remit to platform
- **🏪 Restaurants** receive their share after commission deduction
- **👨‍💼 Platform** manages cash collection and distribution

### **Key Stakeholders in COD Flow**
- **👥 Customers** - Pay cash on delivery
- **🚚 Delivery Agents** - Collect cash, act as payment collectors
- **🏪 Restaurants** - Receive net earnings after commission
- **👨‍💼 Platform** - Manages cash flow and settlements

## 💸 Cash Flow Diagrams

### **1. COD Money Flow Overview**

```mermaid
graph TD
    A[👥 Customer<br/>Pays $100 Cash] -->|Cash Payment| B[🚚 Delivery Agent<br/>Collects Cash]
    B -->|$100 - $4.90 = $95.10| C[🏦 Platform Account<br/>Cash Remittance]
    
    C -->|$75.02| D[🏪 Restaurant<br/>Net Earnings]
    C -->|$20.08| E[👨‍💼 Platform<br/>Commission & Fees]
    
    B -->|$4.90| F[🚚 Delivery Agent<br/>Keeps Earnings]
    
    subgraph "💰 Order Breakdown"
        G[📦 Food: $85.00]
        H[🚚 Delivery: $7.00]
        I[💸 Tax: $8.00]
        J[💡 Total: $100.00]
    end
    
    subgraph "🚚 Agent Responsibilities"
        K[💵 Collect $100 from customer]
        L[💰 Keep $4.90 delivery earnings]
        M[🏦 Remit $95.10 to platform]
        N[📱 Report collection via app]
    end
    
    subgraph "🏦 Platform Settlement"
        O[💰 Receive $95.10 from agent]
        P[🏪 Pay $75.02 to restaurant]
        Q[👨‍💼 Keep $20.08 commission]
        R[📊 Track all transactions]
    end
```

### **2. COD Collection Process**

```mermaid
sequenceDiagram
    participant C as 👥 Customer
    participant DA as 🚚 Delivery Agent
    participant PA as 🏦 Platform Account
    participant R as 🏪 Restaurant
    participant A as 👨‍💼 Admin
    
    Note over C,A: Order Placement (No Payment)
    C->>R: Place order (COD selected)
    R->>R: Prepare order
    R->>DA: Order ready for pickup
    
    Note over C,A: Cash Collection Process
    DA->>DA: Pick up order from restaurant
    DA->>C: Deliver order
    C->>DA: Pay $100 cash
    DA->>PA: Report cash collection via app
    
    Note over C,A: Cash Settlement Process
    DA->>DA: Keep delivery earnings ($4.90)
    DA->>PA: Remit remaining cash ($95.10)
    
    Note over C,A: Revenue Distribution
    PA->>PA: Calculate commissions
    PA->>R: Transfer $75.02 (Net earnings)
    PA->>A: Retain $20.08 (Platform revenue)
    
    Note over C,A: Settlement Verification
    PA->>DA: Confirm cash received
    PA->>R: Process restaurant payout
    PA->>A: Update financial records
```

### **3. Daily Cash Settlement Flow**

```mermaid
graph LR
    A[🌅 Start of Day<br/>Agent Balance: $0] -->|Deliveries| B[📦 Collect Orders<br/>Throughout Day]
    B --> C[💰 End of Day<br/>Total Collected: $500]
    
    C --> D{💵 Settlement Options}
    
    D -->|Option 1| E[🏦 Bank Deposit<br/>Remit to Platform]
    D -->|Option 2| F[📱 Digital Transfer<br/>Via App]
    D -->|Option 3| G[🏢 Office Drop-off<br/>Physical Cash]
    
    E --> H[✅ Settlement Complete<br/>Agent Keeps Earnings]
    F --> H
    G --> H
    
    H --> I[📊 Platform Distributes<br/>to Restaurants]
```

## 💰 Revenue Distribution (COD Model)

### **Example Order: $100 Cash Payment**

| Component | Amount | Percentage | Collection Method |
|-----------|--------|------------|-------------------|
| **Order Subtotal** | $85.00 | 85% | Cash from customer |
| **Delivery Fee** | $7.00 | 7% | Cash from customer |
| **Taxes** | $8.00 | 8% | Cash from customer |
| **TOTAL COLLECTED** | **$100.00** | **100%** | **Cash to Agent** |

### **Cash Distribution After Collection**

| Recipient | Calculation | Amount | Percentage | Payment Method |
|-----------|-------------|--------|------------|----------------|
| **🚚 Delivery Agent** | $5 base + $1.50 distance + $0.50 bonus - $2.10 platform cut | $4.90 | 4.9% | **Keeps from collection** |
| **🏪 Restaurant** | $85 - 15% - 2.5% + $7 - 30% - agent earnings | $75.02 | 75.02% | Bank transfer |
| **👨‍💼 Platform** | Commission + processing + delivery share | $20.08 | 20.08% | Retained |
| **TOTAL** | | **$100.00** | **100%** | |

## 🔄 COD Collection & Settlement Process

### **1. Order Placement (No Payment Required)**
```
1. Customer places order → Selects "Cash on Delivery"
2. No payment gateway → No upfront payment
3. Order confirmed → Restaurant starts preparation
4. Delivery assigned → Agent receives pickup notification
```

### **2. Cash Collection Process**
```
1. Agent picks up order → From restaurant
2. Agent delivers order → To customer address
3. Customer pays cash → $100 total amount
4. Agent reports collection → Via mobile app
5. Transaction recorded → In system database
```

### **3. Daily Settlement Process**
```
1. Agent accumulates cash → Throughout the day
2. End-of-day settlement → Multiple options available:
   - Bank deposit to platform account
   - Digital transfer via app
   - Physical cash drop-off at office
3. Platform receives funds → Minus agent earnings
4. Revenue distributed → To restaurants and platform
```

### **4. Restaurant Payout Process**
```
1. Platform receives cash → From delivery agents
2. Commissions calculated → Automatically by system
3. Restaurant earnings → Transferred weekly/monthly
4. Payment methods → Bank transfer, mobile money
```

## 📊 COD Financial Management

### **Delivery Agent Cash Management**

#### **Daily Cash Flow Example**
```
Agent completes 10 deliveries @ $100 each:

Total Cash Collected: $1,000
Agent Earnings (10 × $4.90): $49
Cash to Remit: $950

Settlement Options:
- Keep $49 as daily earnings
- Remit $950 to platform account
- Report all transactions via app
```

#### **Agent Responsibilities**
- ✅ Collect exact cash amount from customers
- ✅ Provide change if needed (platform provides float)
- ✅ Report all collections via mobile app
- ✅ Remit collected cash minus earnings
- ✅ Maintain transaction records

### **Platform Cash Management**

#### **Daily Operations**
```
Total Orders: 100 orders × $100 = $10,000
Agent Collections: $10,000 cash
Agent Earnings: 100 × $4.90 = $490
Platform Receives: $10,000 - $490 = $9,510

Distribution:
- Restaurant Payments: $7,502
- Platform Revenue: $2,008
- Total Distributed: $9,510 ✓
```

#### **Cash Flow Challenges & Solutions**

| Challenge | Solution |
|-----------|----------|
| **Agent Cash Float** | Platform provides daily float for change |
| **Cash Security** | Insurance coverage, secure collection points |
| **Settlement Delays** | Multiple daily settlement windows |
| **Record Keeping** | Digital transaction logging via app |
| **Fraud Prevention** | GPS tracking, photo verification |

## 🏦 Settlement & Payout Methods

### **For Delivery Agents**
- **Immediate**: Keep earnings from each delivery
- **Daily Float**: Platform provides change money
- **Settlement**: End-of-day cash remittance
- **Verification**: App-based transaction reporting

### **For Restaurants**
- **Weekly Payouts**: Bank transfers every Friday
- **Minimum Amount**: $50 accumulated earnings
- **Payment Methods**: Bank transfer, mobile money
- **Documentation**: Detailed earning statements

### **For Platform**
- **Cash Management**: Daily cash collection from agents
- **Banking**: Deposit to business accounts
- **Distribution**: Automated restaurant payouts
- **Reporting**: Real-time financial dashboards

## 🔧 Technical Implementation

### **COD-Specific Models**

```python
class CODTransaction(models.Model):
    """Track cash on delivery transactions"""
    order = models.OneToOneField(Order, on_delete=models.CASCADE)
    delivery_agent = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Cash collection details
    cash_collected = models.DecimalField(max_digits=10, decimal_places=2)
    collection_time = models.DateTimeField()
    customer_paid_amount = models.DecimalField(max_digits=10, decimal_places=2)
    change_given = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Agent settlement
    agent_earnings = models.DecimalField(max_digits=8, decimal_places=2)
    amount_to_remit = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Settlement status
    is_settled = models.BooleanField(default=False)
    settled_at = models.DateTimeField(null=True, blank=True)
    settlement_method = models.CharField(max_length=50)
    
    # Verification
    gps_location = models.CharField(max_length=100, blank=True)
    photo_verification = models.ImageField(upload_to='cod_verification/', blank=True)

class DailyAgentSettlement(models.Model):
    """Daily settlement records for delivery agents"""
    delivery_agent = models.ForeignKey(User, on_delete=models.CASCADE)
    settlement_date = models.DateField()
    
    # Daily totals
    total_orders = models.PositiveIntegerField()
    total_cash_collected = models.DecimalField(max_digits=12, decimal_places=2)
    total_agent_earnings = models.DecimalField(max_digits=10, decimal_places=2)
    total_remittance = models.DecimalField(max_digits=12, decimal_places=2)
    
    # Settlement details
    settlement_method = models.CharField(max_length=50)
    settlement_reference = models.CharField(max_length=100)
    is_verified = models.BooleanField(default=False)
```

### **COD API Endpoints**

```
# Cash Collection
POST /api/cod/collect-cash/           # Report cash collection
GET  /api/cod/daily-collections/      # Agent's daily collections
POST /api/cod/settle-cash/            # End-of-day settlement

# Agent Management
GET  /api/cod/agent-balance/          # Current cash balance
GET  /api/cod/settlement-history/     # Settlement history
POST /api/cod/request-float/          # Request change money

# Platform Management
GET  /api/cod/daily-settlements/      # All agent settlements
GET  /api/cod/cash-flow-report/       # Cash flow analytics
POST /api/cod/verify-settlement/      # Verify agent settlement
```

## 📱 Mobile App Features

### **For Delivery Agents**
- **Cash Collection**: Record cash received from customers
- **Change Calculator**: Calculate change to give customers
- **Daily Summary**: Track total collections and earnings
- **Settlement**: Report end-of-day cash remittance
- **Float Request**: Request change money from platform

### **For Restaurants**
- **Earnings Tracker**: Real-time earnings from COD orders
- **Payout Schedule**: View upcoming payment dates
- **Transaction History**: Detailed order and payment records

### **For Admin**
- **Cash Flow Dashboard**: Real-time cash collection monitoring
- **Agent Settlements**: Track all agent cash remittances
- **Financial Reports**: Daily, weekly, monthly cash flow reports
- **Fraud Detection**: Monitor unusual collection patterns

## 🎯 COD Success Metrics

### **Operational Metrics**
- **Collection Rate**: 99%+ cash collection success
- **Settlement Rate**: 95%+ daily agent settlements
- **Accuracy Rate**: 98%+ correct cash amounts
- **Fraud Rate**: <1% fraudulent transactions

### **Financial Health**
- **Cash Flow Velocity**: Same-day cash collection
- **Settlement Time**: 24-48 hours to restaurants
- **Float Management**: Optimal change money distribution
- **Cost Efficiency**: Low cash handling costs

---

**Afghan Sofra COD System** - Efficient cash collection and distribution for a cash-based economy! 💰

**Status**: ✅ **COD-OPTIMIZED** - Perfect for cash-based markets!
