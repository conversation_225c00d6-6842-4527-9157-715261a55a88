#!/usr/bin/env python3
"""
Seed Dynamic Configuration Data
Populates the system with dynamic settings, choice options, and filter configurations
"""

import os
import sys
import django
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from system_config.models import SystemSetting, ChoiceOption, FilterConfiguration

def create_system_settings():
    """Create system settings"""
    print("🔧 Creating system settings...")
    
    settings = [
        # General Settings
        {
            'key': 'site_name',
            'name': 'Site Name',
            'description': 'Name of the food delivery platform',
            'value': 'Afghan Sufra',
            'default_value': 'Afghan Sufra',
            'setting_type': 'string',
            'category': 'general',
            'is_public': True
        },
        {
            'key': 'site_description',
            'name': 'Site Description',
            'description': 'Description of the platform',
            'value': 'Premium Afghan Food Delivery Service',
            'default_value': 'Premium Afghan Food Delivery Service',
            'setting_type': 'text',
            'category': 'general',
            'is_public': True
        },
        {
            'key': 'contact_email',
            'name': 'Contact Email',
            'description': 'Main contact email address',
            'value': '<EMAIL>',
            'default_value': '<EMAIL>',
            'setting_type': 'email',
            'category': 'general',
            'is_public': True
        },
        {
            'key': 'contact_phone',
            'name': 'Contact Phone',
            'description': 'Main contact phone number',
            'value': '+93 70 123 4567',
            'default_value': '+93 70 123 4567',
            'setting_type': 'string',
            'category': 'general',
            'is_public': True
        },
        {
            'key': 'default_currency',
            'name': 'Default Currency',
            'description': 'Default currency for the platform',
            'value': 'USD',
            'default_value': 'USD',
            'setting_type': 'string',
            'category': 'general',
            'is_public': True
        },
        {
            'key': 'default_timezone',
            'name': 'Default Timezone',
            'description': 'Default timezone for the platform',
            'value': 'Asia/Kabul',
            'default_value': 'Asia/Kabul',
            'setting_type': 'string',
            'category': 'general',
            'is_public': True
        },
        
        # Delivery Settings
        {
            'key': 'default_delivery_fee',
            'name': 'Default Delivery Fee',
            'description': 'Default delivery fee amount',
            'value': '4.99',
            'default_value': '4.99',
            'setting_type': 'decimal',
            'category': 'delivery',
            'is_public': True
        },
        {
            'key': 'free_delivery_threshold',
            'name': 'Free Delivery Threshold',
            'description': 'Minimum order amount for free delivery',
            'value': '25.00',
            'default_value': '25.00',
            'setting_type': 'decimal',
            'category': 'delivery',
            'is_public': True
        },
        {
            'key': 'max_delivery_distance',
            'name': 'Maximum Delivery Distance',
            'description': 'Maximum delivery distance in kilometers',
            'value': '15',
            'default_value': '15',
            'setting_type': 'integer',
            'category': 'delivery',
            'is_public': True
        },
        {
            'key': 'estimated_delivery_time',
            'name': 'Estimated Delivery Time',
            'description': 'Default estimated delivery time in minutes',
            'value': '45',
            'default_value': '45',
            'setting_type': 'integer',
            'category': 'delivery',
            'is_public': True
        },
        
        # Business Settings
        {
            'key': 'platform_commission_rate',
            'name': 'Platform Commission Rate',
            'description': 'Commission rate percentage for restaurants',
            'value': '15.0',
            'default_value': '15.0',
            'setting_type': 'decimal',
            'category': 'business',
            'is_public': False
        },
        {
            'key': 'tax_rate',
            'name': 'Tax Rate',
            'description': 'Default tax rate percentage',
            'value': '10.0',
            'default_value': '10.0',
            'setting_type': 'decimal',
            'category': 'business',
            'is_public': True
        },
        
        # Notification Settings
        {
            'key': 'enable_email_notifications',
            'name': 'Enable Email Notifications',
            'description': 'Enable email notifications for users',
            'value': 'true',
            'default_value': 'true',
            'setting_type': 'boolean',
            'category': 'notification',
            'is_public': False
        },
        {
            'key': 'enable_sms_notifications',
            'name': 'Enable SMS Notifications',
            'description': 'Enable SMS notifications for users',
            'value': 'false',
            'default_value': 'false',
            'setting_type': 'boolean',
            'category': 'notification',
            'is_public': False
        }
    ]
    
    created_count = 0
    for setting_data in settings:
        setting, created = SystemSetting.objects.get_or_create(
            key=setting_data['key'],
            defaults=setting_data
        )
        if created:
            created_count += 1
            print(f"   ✅ Created setting: {setting.name}")
        else:
            print(f"   ⚠️ Setting exists: {setting.name}")
    
    print(f"✅ Created {created_count} new system settings")

def create_choice_options():
    """Create choice options"""
    print("\n📋 Creating choice options...")
    
    choice_options = [
        # User Roles (Public Registration - delivery_agent removed)
        {'option_type': 'user_role', 'value': 'customer', 'label': 'Customer', 'icon': '👤', 'display_order': 1},
        {'option_type': 'user_role', 'value': 'restaurant', 'label': 'Restaurant Owner', 'icon': '🏪', 'display_order': 2},
        # delivery_agent role removed from public registration - admin creates these accounts
        {'option_type': 'user_role', 'value': 'admin', 'label': 'Administrator', 'icon': '👨‍💼', 'display_order': 4},
        
        # Order Status
        {'option_type': 'order_status', 'value': 'pending', 'label': 'Pending', 'icon': '⏳', 'color': '#FFA500', 'display_order': 1},
        {'option_type': 'order_status', 'value': 'confirmed', 'label': 'Confirmed', 'icon': '✅', 'color': '#28A745', 'display_order': 2},
        {'option_type': 'order_status', 'value': 'preparing', 'label': 'Preparing', 'icon': '👨‍🍳', 'color': '#17A2B8', 'display_order': 3},
        {'option_type': 'order_status', 'value': 'ready', 'label': 'Ready for Pickup', 'icon': '📦', 'color': '#6F42C1', 'display_order': 4},
        {'option_type': 'order_status', 'value': 'assigned', 'label': 'Assigned to Delivery', 'icon': '🚚', 'color': '#FD7E14', 'display_order': 5},
        {'option_type': 'order_status', 'value': 'picked_up', 'label': 'Picked Up', 'icon': '📋', 'color': '#20C997', 'display_order': 6},
        {'option_type': 'order_status', 'value': 'on_the_way', 'label': 'On the Way', 'icon': '🛵', 'color': '#007BFF', 'display_order': 7},
        {'option_type': 'order_status', 'value': 'delivered', 'label': 'Delivered', 'icon': '🎉', 'color': '#28A745', 'display_order': 8},
        {'option_type': 'order_status', 'value': 'cancelled', 'label': 'Cancelled', 'icon': '❌', 'color': '#DC3545', 'display_order': 9},
        
        # Payment Methods
        {'option_type': 'payment_method', 'value': 'cash_on_delivery', 'label': 'Cash on Delivery', 'icon': '💵', 'display_order': 1, 'is_default': True},
        {'option_type': 'payment_method', 'value': 'credit_card', 'label': 'Credit Card', 'icon': '💳', 'display_order': 2},
        {'option_type': 'payment_method', 'value': 'debit_card', 'label': 'Debit Card', 'icon': '💳', 'display_order': 3},
        {'option_type': 'payment_method', 'value': 'paypal', 'label': 'PayPal', 'icon': '🅿️', 'display_order': 4},
        
        # Dietary Restrictions
        {'option_type': 'dietary_restriction', 'value': 'vegetarian', 'label': 'Vegetarian', 'icon': '🥬', 'display_order': 1},
        {'option_type': 'dietary_restriction', 'value': 'vegan', 'label': 'Vegan', 'icon': '🌱', 'display_order': 2},
        {'option_type': 'dietary_restriction', 'value': 'halal', 'label': 'Halal', 'icon': '☪️', 'display_order': 3},
        {'option_type': 'dietary_restriction', 'value': 'gluten_free', 'label': 'Gluten Free', 'icon': '🌾', 'display_order': 4},
        {'option_type': 'dietary_restriction', 'value': 'dairy_free', 'label': 'Dairy Free', 'icon': '🥛', 'display_order': 5},
        {'option_type': 'dietary_restriction', 'value': 'nut_free', 'label': 'Nut Free', 'icon': '🥜', 'display_order': 6},
        {'option_type': 'dietary_restriction', 'value': 'keto', 'label': 'Keto Friendly', 'icon': '🥑', 'display_order': 7},
        {'option_type': 'dietary_restriction', 'value': 'low_carb', 'label': 'Low Carb', 'icon': '🥗', 'display_order': 8},
        
        # Vehicle Types
        {'option_type': 'vehicle_type', 'value': 'motorcycle', 'label': 'Motorcycle', 'icon': '🏍️', 'display_order': 1},
        {'option_type': 'vehicle_type', 'value': 'bicycle', 'label': 'Bicycle', 'icon': '🚲', 'display_order': 2},
        {'option_type': 'vehicle_type', 'value': 'car', 'label': 'Car', 'icon': '🚗', 'display_order': 3},
        {'option_type': 'vehicle_type', 'value': 'scooter', 'label': 'Scooter', 'icon': '🛵', 'display_order': 4},
        
        # Currencies
        {'option_type': 'currency', 'value': 'USD', 'label': 'US Dollar', 'icon': '$', 'display_order': 1, 'is_default': True},
        {'option_type': 'currency', 'value': 'AFN', 'label': 'Afghan Afghani', 'icon': '؋', 'display_order': 2},
        {'option_type': 'currency', 'value': 'EUR', 'label': 'Euro', 'icon': '€', 'display_order': 3},
        {'option_type': 'currency', 'value': 'GBP', 'label': 'British Pound', 'icon': '£', 'display_order': 4},
    ]
    
    created_count = 0
    for option_data in choice_options:
        option, created = ChoiceOption.objects.get_or_create(
            option_type=option_data['option_type'],
            value=option_data['value'],
            defaults=option_data
        )
        if created:
            created_count += 1
            print(f"   ✅ Created option: {option.option_type} - {option.label}")
        else:
            print(f"   ⚠️ Option exists: {option.option_type} - {option.label}")
    
    print(f"✅ Created {created_count} new choice options")

def main():
    print("🚀 Seeding Dynamic Configuration Data")
    print("=" * 50)
    
    # Create system settings
    create_system_settings()
    
    # Create choice options
    create_choice_options()
    
    print("\n" + "=" * 50)
    print("🎉 Dynamic Configuration Seeding Complete!")
    print("\n📊 Summary:")
    print(f"   System Settings: {SystemSetting.objects.count()}")
    print(f"   Choice Options: {ChoiceOption.objects.count()}")
    print(f"   Filter Configurations: {FilterConfiguration.objects.count()}")
    
    print("\n🔗 API Endpoints Available:")
    print("✅ GET /api/config/settings/public_config/ - Get public configuration")
    print("✅ GET /api/config/choice-options/by_type/ - Get choice options by type")
    print("✅ GET /api/config/filter-configs/ - Get filter configurations")

if __name__ == "__main__":
    main()
