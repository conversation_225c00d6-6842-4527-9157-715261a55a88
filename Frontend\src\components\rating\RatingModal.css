.rating-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.rating-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.rating-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.rating-modal-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 24px;
  font-weight: 700;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.rating-modal-content {
  padding: 24px;
}

.order-info {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.order-info h3 {
  margin: 0 0 8px 0;
  color: #1e40af;
  font-size: 18px;
  font-weight: 600;
}

.restaurant-name {
  margin: 4px 0;
  color: #374151;
  font-weight: 500;
}

.order-total {
  margin: 4px 0 0 0;
  color: #059669;
  font-weight: 600;
  font-size: 16px;
}

.rating-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.rating-section {
  padding: 20px;
  border: 2px solid #f3f4f6;
  border-radius: 12px;
  transition: all 0.3s;
}

.rating-section:hover {
  border-color: #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.rating-section h4 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.rating-section p {
  margin: 0 0 16px 0;
  color: #6b7280;
  font-size: 14px;
}

.star-rating {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.star {
  background: none;
  border: none;
  font-size: 32px;
  cursor: pointer;
  transition: all 0.2s;
  filter: grayscale(100%);
  opacity: 0.3;
  transform: scale(1);
}

.star:hover {
  transform: scale(1.1);
  opacity: 0.7;
}

.star.filled {
  filter: grayscale(0%);
  opacity: 1;
  transform: scale(1.1);
}

.rating-text {
  color: #059669;
  font-weight: 600;
  font-size: 14px;
}

.review-section {
  padding: 20px;
  border: 2px solid #f3f4f6;
  border-radius: 12px;
}

.review-section h4 {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-size: 16px;
  font-weight: 600;
}

.review-textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s;
}

.review-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.character-count {
  text-align: right;
  color: #6b7280;
  font-size: 12px;
  margin-top: 4px;
}

.rating-modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.btn-secondary {
  padding: 12px 24px;
  border: 2px solid #e5e7eb;
  background: white;
  color: #374151;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-secondary:hover:not(:disabled) {
  border-color: #d1d5db;
  background-color: #f9fafb;
}

.btn-primary {
  padding: 12px 24px;
  border: none;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-message {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 16px;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .rating-modal-overlay {
    padding: 10px;
  }
  
  .rating-modal {
    max-height: 95vh;
  }
  
  .rating-modal-header,
  .rating-modal-content {
    padding: 16px;
  }
  
  .star {
    font-size: 28px;
  }
  
  .rating-modal-actions {
    flex-direction: column;
  }
  
  .btn-secondary,
  .btn-primary {
    width: 100%;
    justify-content: center;
  }
}
