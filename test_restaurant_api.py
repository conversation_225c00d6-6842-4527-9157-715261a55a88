#!/usr/bin/env python3
"""
Test restaurant API to check restaurant verification status
"""

import requests
import json

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_restaurant_list():
    """Test restaurant list API"""
    print("🏪 Testing Restaurant List API...")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=HEADERS,
            timeout=10
        )
        
        print(f"📡 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 Total Restaurants: {len(data)}")
            
            if data:
                print("\n🏪 Restaurant Verification Status:")
                for i, restaurant in enumerate(data[:5]):  # Show first 5
                    print(f"   {i+1}. {restaurant.get('name', 'Unknown')}")
                    print(f"      - ID: {restaurant.get('id')}")
                    print(f"      - Verified: {restaurant.get('is_verified', False)}")
                    print(f"      - Owner: {restaurant.get('owner', 'Unknown')}")
                    print()
            else:
                print("📭 No restaurants found")
                
        else:
            print(f"❌ Failed to get restaurants: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_restaurant_creation_flow():
    """Test what happens when a restaurant user has no restaurant"""
    print("\n🔧 Testing Restaurant Creation Flow...")
    
    # This would simulate what happens when a restaurant user logs in
    # but has no restaurant profile yet
    
    print("✅ Expected Flow:")
    print("   1. User with role 'restaurant' logs in")
    print("   2. System checks if user has any restaurants")
    print("   3. If no restaurants: Show 'Create Restaurant' page")
    print("   4. If restaurant exists but not verified: Show 'Pending Approval' page")
    print("   5. If restaurant exists and verified: Show full dashboard")

if __name__ == "__main__":
    print("🧪 Restaurant Status API Test")
    print("=" * 50)
    
    test_restaurant_list()
    test_restaurant_creation_flow()
    
    print("\n" + "=" * 50)
    print("🏁 Test Complete")
