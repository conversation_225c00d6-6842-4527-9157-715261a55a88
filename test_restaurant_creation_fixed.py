#!/usr/bin/env python3
"""
Test restaurant creation after fixing the delivery_fee validator issue
"""

import requests
import json
import time

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def create_test_user():
    """Create a test restaurant user"""
    timestamp = int(time.time())
    
    test_user = {
        "name": "Test Restaurant Owner",
        "user_name": f"testowner{timestamp}",
        "email": f"testowner{timestamp}@example.com",
        "phone": f"+93701{timestamp % 100000:05d}",
        "password": "TestPassword123",
        "confirm_password": "TestPassword123",
        "role": "restaurant"
    }
    
    print(f"📝 Creating test user: {test_user['email']}")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/register/",
            headers=HEADERS,
            data=json.dumps(test_user)
        )
        
        print(f"📡 Registration Status: {response.status_code}")
        print(f"📄 Registration Response: {response.text}")
        
        if response.status_code == 201:
            print("✅ User created successfully")
            return test_user
        else:
            print("❌ User creation failed")
            return None
            
    except Exception as e:
        print(f"❌ Exception during user creation: {e}")
        return None

def login_user(user_data):
    """Login and get authentication token"""
    login_data = {
        "user_name": user_data['user_name'],
        "password": user_data['password']
    }
    
    print(f"🔐 Logging in user: {login_data['user_name']}")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=HEADERS,
            data=json.dumps(login_data)
        )
        
        print(f"📡 Login Status: {response.status_code}")
        print(f"📄 Login Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            token = result['data']['access_token']
            print("✅ Login successful")
            return token
        else:
            print("❌ Login failed")
            return None
            
    except Exception as e:
        print(f"❌ Exception during login: {e}")
        return None

def test_restaurant_creation(token):
    """Test restaurant creation with authentication"""
    auth_headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    # Test with proper address structure
    restaurant_data = {
        "name": "Test Afghan Restaurant",
        "description": "A beautiful test restaurant serving authentic Afghan cuisine",
        "address": {
            "street": "123 Main Street",
            "city": "Kabul",
            "state": "Kabul",
            "postal_code": "1001",
            "country": "Afghanistan",
            "latitude": 34.5553,
            "longitude": 69.2075
        },
        "contact_number": "+93701234567",
        "opening_time": "09:00:00",
        "closing_time": "22:00:00",
        "delivery_fee": "50.00",
        "min_order_amount": "200.00",
        "average_preparation_time": 30,
        "accepts_cash": True,
        "accepts_card": True,
        "accepts_online_payment": True
    }
    
    print(f"🏗️ Creating restaurant: {restaurant_data['name']}")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            data=json.dumps(restaurant_data)
        )
        
        print(f"📡 Restaurant Creation Status: {response.status_code}")
        print(f"📄 Restaurant Creation Response: {response.text}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ Restaurant created successfully!")
            print(f"   Restaurant ID: {result.get('id')}")
            print(f"   Name: {result.get('name')}")
            print(f"   Delivery Fee: {result.get('delivery_fee')}")
            return True
        else:
            print("❌ Restaurant creation failed")
            try:
                error_data = response.json()
                print(f"🔍 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"🔍 Raw Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during restaurant creation: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Restaurant Creation After Fix")
    print("=" * 60)
    
    # Step 1: Create test user
    user_data = create_test_user()
    if not user_data:
        print("❌ Failed to create test user")
        return
    
    # Step 2: Login user
    token = login_user(user_data)
    if not token:
        print("❌ Failed to login user")
        return
    
    # Step 3: Test restaurant creation
    success = test_restaurant_creation(token)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: Restaurant creation is working!")
        print("✅ The delivery_fee validator fix resolved the issue")
    else:
        print("❌ FAILED: Restaurant creation still has issues")
        print("🔍 Additional debugging may be needed")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
