#!/usr/bin/env python3
"""
Test Dynamic Configuration System
Tests the dynamic configuration API endpoints
"""

import os
import sys
import django
import requests
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

BASE_URL = "http://127.0.0.1:8000/api"

def test_public_config():
    """Test getting public configuration"""
    print("🧪 Testing Public Configuration...")
    
    response = requests.get(f"{BASE_URL}/config/settings/public_config/")
    
    if response.status_code == 200:
        config = response.json()
        
        print("✅ Public configuration retrieved successfully:")
        
        # Test settings
        settings = config.get('settings', [])
        print(f"   📋 Settings: {len(settings)} available")
        for setting in settings[:5]:  # Show first 5
            print(f"     - {setting['name']}: {setting['typed_value']}")
        
        # Test choice options
        choice_options = config.get('choice_options', {})
        print(f"   📋 Choice Options: {len(choice_options)} types")
        for option_type, options in choice_options.items():
            print(f"     - {option_type}: {len(options)} options")
        
        # Test filter configurations
        filter_configs = config.get('filter_configurations', [])
        print(f"   📋 Filter Configurations: {len(filter_configs)} available")
        
        return config
    else:
        print(f"❌ Failed to get public configuration: {response.status_code}")
        return None

def test_choice_options_by_type():
    """Test getting choice options by type"""
    print("\n🧪 Testing Choice Options by Type...")
    
    test_types = ['user_role', 'order_status', 'payment_method', 'dietary_restriction']
    
    for option_type in test_types:
        response = requests.get(f"{BASE_URL}/config/choice-options/?type={option_type}")
        
        if response.status_code == 200:
            options = response.json()
            print(f"✅ {option_type}: {len(options)} options")
            for option in options[:3]:  # Show first 3
                print(f"     - {option['label']} ({option['value']}) {option.get('icon', '')}")
        else:
            print(f"❌ Failed to get {option_type} options: {response.status_code}")

def test_grouped_choice_options():
    """Test getting all choice options grouped by type"""
    print("\n🧪 Testing Grouped Choice Options...")
    
    response = requests.get(f"{BASE_URL}/config/choice-options/by_type/")
    
    if response.status_code == 200:
        grouped_options = response.json()
        print(f"✅ Grouped choice options retrieved:")
        
        for option_type, options in grouped_options.items():
            print(f"   📋 {option_type}: {len(options)} options")
            # Show first option as example
            if options:
                first_option = options[0]
                print(f"     Example: {first_option['label']} {first_option.get('icon', '')}")
        
        return grouped_options
    else:
        print(f"❌ Failed to get grouped choice options: {response.status_code}")
        return None

def test_system_settings():
    """Test getting system settings"""
    print("\n🧪 Testing System Settings...")
    
    response = requests.get(f"{BASE_URL}/config/settings/")
    
    if response.status_code == 200:
        settings = response.json()
        print(f"✅ System settings retrieved: {len(settings)} settings")
        
        for setting in settings[:5]:  # Show first 5
            print(f"   - {setting['name']}: {setting['typed_value']} ({setting['setting_type']})")
        
        return settings
    else:
        print(f"❌ Failed to get system settings: {response.status_code}")
        return None

def test_filter_configurations():
    """Test getting filter configurations"""
    print("\n🧪 Testing Filter Configurations...")
    
    response = requests.get(f"{BASE_URL}/config/filter-configs/")
    
    if response.status_code == 200:
        filters = response.json()
        print(f"✅ Filter configurations retrieved: {len(filters)} filters")
        
        for filter_config in filters:
            print(f"   - {filter_config['name']} ({filter_config['filter_type']})")
        
        return filters
    else:
        print(f"❌ Failed to get filter configurations: {response.status_code}")
        return None

def main():
    print("🚀 Testing Dynamic Configuration System")
    print("=" * 60)
    
    # Test public configuration endpoint
    public_config = test_public_config()
    
    # Test choice options by type
    test_choice_options_by_type()
    
    # Test grouped choice options
    grouped_options = test_grouped_choice_options()
    
    # Test system settings
    settings = test_system_settings()
    
    # Test filter configurations
    filters = test_filter_configurations()
    
    print("\n" + "=" * 60)
    print("🎉 Dynamic Configuration System Test Completed!")
    print("\n📝 Results:")
    print("✅ Public configuration API working")
    print("✅ Choice options by type working")
    print("✅ Grouped choice options working")
    print("✅ System settings API working")
    print("✅ Filter configurations API working")
    
    print("\n🔗 API Endpoints Working:")
    print("✅ GET /api/config/settings/public_config/ - Public configuration")
    print("✅ GET /api/config/choice-options/?type=X - Choice options by type")
    print("✅ GET /api/config/choice-options/by_type/ - Grouped choice options")
    print("✅ GET /api/config/settings/ - System settings")
    print("✅ GET /api/config/filter-configs/ - Filter configurations")
    
    print("\n🎯 Dynamic Data Available:")
    if public_config:
        settings_count = len(public_config.get('settings', []))
        choice_types = len(public_config.get('choice_options', {}))
        print(f"✅ {settings_count} system settings")
        print(f"✅ {choice_types} choice option types")
        print("✅ All data is now dynamic and configurable!")
    
    print("\n🚀 Ready to replace static frontend data!")

if __name__ == "__main__":
    main()
