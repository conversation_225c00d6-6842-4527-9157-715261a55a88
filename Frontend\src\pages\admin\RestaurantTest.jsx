import React, { useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useRestaurant } from '../../context/RestaurantContext';

const RestaurantTest = () => {
  console.log('🧪 RestaurantTest component loaded');
  
  const { user } = useAuth();
  const { restaurants, loading, error, getRestaurants } = useRestaurant();

  useEffect(() => {
    console.log('🧪 Testing restaurant system...');
    getRestaurants();
  }, [getRestaurants]);

  console.log('🧪 Test state:', {
    user: user?.email,
    userRole: user?.role,
    restaurantsCount: restaurants?.length,
    loading,
    error
  });

  return (
    <div style={{ padding: '20px' }}>
      <h1>🧪 Restaurant System Test</h1>
      
      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h3>Authentication Status</h3>
        <p><strong>User:</strong> {user?.email || 'Not logged in'}</p>
        <p><strong>Role:</strong> {user?.role || 'No role'}</p>
        <p><strong>User ID:</strong> {user?.id || 'No ID'}</p>
      </div>

      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h3>Restaurant Context Status</h3>
        <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
        <p><strong>Error:</strong> {error || 'None'}</p>
        <p><strong>Restaurants Count:</strong> {restaurants?.length || 0}</p>
      </div>

      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h3>Restaurants Data</h3>
        {loading && <p>Loading restaurants...</p>}
        {error && <p style={{ color: 'red' }}>Error: {error}</p>}
        {restaurants && restaurants.length > 0 ? (
          <ul>
            {restaurants.slice(0, 3).map((restaurant, index) => (
              <li key={restaurant.id || index}>
                <strong>{restaurant.name}</strong> - {restaurant.contact_number}
              </li>
            ))}
            {restaurants.length > 3 && <li>... and {restaurants.length - 3} more</li>}
          </ul>
        ) : (
          !loading && <p>No restaurants found</p>
        )}
      </div>

      <div style={{ padding: '15px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h3>Test Actions</h3>
        <button 
          onClick={() => getRestaurants()}
          style={{ padding: '10px 15px', marginRight: '10px', cursor: 'pointer' }}
        >
          Reload Restaurants
        </button>
        <button 
          onClick={() => window.location.href = '/admin/restaurants'}
          style={{ padding: '10px 15px', cursor: 'pointer' }}
        >
          Go to Restaurant Management
        </button>
      </div>

      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        <p>Check browser console for detailed logs</p>
      </div>
    </div>
  );
};

export default RestaurantTest;
