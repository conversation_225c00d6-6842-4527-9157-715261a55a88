#!/usr/bin/env python3
"""
Test script for favorites API endpoints
"""
import os
import sys
import django
import requests
import json

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from restaurant.models import Restaurant
from customer.models import CustomerFavorite

User = get_user_model()

def test_favorites_api():
    """Test the favorites API endpoints"""
    
    # API base URL
    base_url = "http://127.0.0.1:8000/api"
    
    print("🧪 Testing Favorites API...")
    
    # Get a test user (customer)
    try:
        customer = User.objects.filter(role='customer').first()
        if not customer:
            print("❌ No customer found in database")
            return

        print(f"✅ Found customer: {customer.email} (username: {customer.user_name})")
        
        # Get a test restaurant
        restaurant = Restaurant.objects.filter(is_active=True).first()
        if not restaurant:
            print("❌ No active restaurant found in database")
            return
            
        print(f"✅ Found restaurant: {restaurant.name} (ID: {restaurant.id})")
        
        # Create token manually for testing
        from rest_framework_simplejwt.tokens import RefreshToken
        refresh = RefreshToken.for_user(customer)
        token = str(refresh.access_token)
        print("✅ Token created manually for testing")
        
        # Headers for authenticated requests
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # Test 1: Check current favorites
        print("\n📋 Testing: Get favorites list")
        favorites_response = requests.get(f"{base_url}/customer/favorites/", headers=headers)
        print(f"Status: {favorites_response.status_code}")
        print(f"Response: {favorites_response.text}")
        
        # Test 2: Add to favorites
        print("\n➕ Testing: Add to favorites")
        add_data = {"restaurant_id": restaurant.id}
        add_response = requests.post(f"{base_url}/customer/favorites/add/", json=add_data, headers=headers)
        print(f"Status: {add_response.status_code}")
        print(f"Response: {add_response.text}")
        
        # Test 3: Toggle favorite (should remove since we just added)
        print("\n🔄 Testing: Toggle favorite")
        toggle_data = {"restaurant_id": restaurant.id}
        toggle_response = requests.post(f"{base_url}/customer/favorites/toggle/", json=toggle_data, headers=headers)
        print(f"Status: {toggle_response.status_code}")
        print(f"Response: {toggle_response.text}")
        
        # Test 4: Toggle again (should add back)
        print("\n🔄 Testing: Toggle favorite again")
        toggle_response2 = requests.post(f"{base_url}/customer/favorites/toggle/", json=toggle_data, headers=headers)
        print(f"Status: {toggle_response2.status_code}")
        print(f"Response: {toggle_response2.text}")
        
        # Test 5: Check favorite status
        print("\n✅ Testing: Check favorite status")
        status_response = requests.get(f"{base_url}/customer/favorites/check/{restaurant.id}/", headers=headers)
        print(f"Status: {status_response.status_code}")
        print(f"Response: {status_response.text}")
        
        print("\n🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_favorites_api()
