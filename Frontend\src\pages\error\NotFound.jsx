import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Home, ArrowLeft, Search, MapPin } from 'lucide-react';
import Button from '../../components/common/Button';
import Card from '../../components/common/Card';

const NotFound = () => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  const quickActions = [
    {
      label: 'Go Home',
      icon: <Home size={16} />,
      onClick: () => navigate('/'),
      variant: 'primary'
    },
    {
      label: 'Browse Restaurants',
      icon: <Search size={16} />,
      onClick: () => navigate('/restaurants'),
      variant: 'outline'
    },
    {
      label: 'Go Back',
      icon: <ArrowLeft size={16} />,
      onClick: handleGoBack,
      variant: 'ghost'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-lg w-full text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-8xl font-bold text-primary-500 mb-4">404</div>
          <div className="text-6xl mb-4">🍽️</div>
        </div>

        <Card className="p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Page Not Found
          </h1>
          
          <p className="text-gray-600 mb-8 leading-relaxed">
            Oops! The page you're looking for seems to have wandered off like a hungry customer. 
            Don't worry, we'll help you find your way back to delicious food!
          </p>

          {/* Quick Actions */}
          <div className="space-y-3 mb-8">
            {quickActions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant}
                onClick={action.onClick}
                icon={action.icon}
                fullWidth
                className="justify-center"
              >
                {action.label}
              </Button>
            ))}
          </div>

          {/* Popular Suggestions */}
          <div className="border-t pt-6">
            <h3 className="font-semibold text-gray-900 mb-4">Popular Pages</h3>
            <div className="grid grid-cols-2 gap-3 text-sm">
              <button
                onClick={() => navigate('/restaurants')}
                className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left"
              >
                <div className="font-medium text-gray-900">Restaurants</div>
                <div className="text-gray-600">Browse all restaurants</div>
              </button>
              
              <button
                onClick={() => navigate('/orders')}
                className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left"
              >
                <div className="font-medium text-gray-900">My Orders</div>
                <div className="text-gray-600">Track your orders</div>
              </button>
              
              <button
                onClick={() => navigate('/favorites')}
                className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left"
              >
                <div className="font-medium text-gray-900">Favorites</div>
                <div className="text-gray-600">Your saved restaurants</div>
              </button>
              
              <button
                onClick={() => navigate('/support')}
                className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-left"
              >
                <div className="font-medium text-gray-900">Help</div>
                <div className="text-gray-600">Get support</div>
              </button>
            </div>
          </div>

          {/* Contact Support */}
          <div className="mt-6 pt-6 border-t">
            <p className="text-sm text-gray-500 mb-3">
              Still can't find what you're looking for?
            </p>
            <Button
              variant="outline"
              size="small"
              onClick={() => navigate('/support')}
              icon={<MapPin size={14} />}
            >
              Contact Support
            </Button>
          </div>
        </Card>

        {/* Fun Facts */}
        <div className="mt-6 text-sm text-gray-500">
          <p>Fun fact: 404 errors are named after room 404 at CERN where the first web server was located! 🤓</p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
