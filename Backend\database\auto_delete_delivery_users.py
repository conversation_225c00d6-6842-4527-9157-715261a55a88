#!/usr/bin/env python3
"""
Auto Delete Delivery Users Script
Automatically deletes all delivery users without prompts
"""

import os
import sys
import django
from pathlib import Path

# Add the Backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import transaction

User = get_user_model()

def delete_all_delivery_users():
    """Delete all delivery users automatically"""
    print("🗑️  Auto-deleting delivery users...")
    
    try:
        with transaction.atomic():
            # Get all delivery users
            delivery_users = User.objects.filter(role='delivery_agent')
            
            if not delivery_users.exists():
                print("✅ No delivery users found to delete.")
                return True
            
            print(f"📊 Found {delivery_users.count()} delivery users to delete:")
            for user in delivery_users:
                print(f"   • ID: {user.id}, Username: {user.user_name}, Name: {user.name}")
            
            # Update orders to remove delivery agent assignments
            orders_updated = 0
            try:
                from orders.models import Order
                orders_updated = Order.objects.filter(delivery_agent__in=delivery_users).update(delivery_agent=None)
                print(f"✅ Updated {orders_updated} orders (set delivery_agent to NULL)")
            except Exception as e:
                print(f"⚠️  Could not update orders: {e}")
            
            # Delete delivery agent profiles if they exist
            try:
                from deliveryAgent.models import DeliveryAgentProfile
                profiles = DeliveryAgentProfile.objects.filter(user__in=delivery_users)
                profile_count = profiles.count()
                profiles.delete()
                print(f"✅ Deleted {profile_count} delivery agent profiles")
            except Exception as e:
                print(f"⚠️  Could not delete profiles: {e}")
            
            # Delete the users
            user_count = delivery_users.count()
            user_names = [user.user_name for user in delivery_users]
            delivery_users.delete()
            
            print(f"✅ Successfully deleted {user_count} delivery users!")
            print("📋 Deleted users:")
            for name in user_names:
                print(f"   • {name}")
            
            print(f"\n📊 Summary:")
            print(f"   • Users deleted: {user_count}")
            print(f"   • Orders updated: {orders_updated}")
            print(f"   • Database cleaned successfully!")
            
            return True
            
    except Exception as e:
        print(f"❌ Error during deletion: {e}")
        return False

def verify_deletion():
    """Verify that delivery users were deleted"""
    print("\n🔍 Verifying deletion...")
    
    delivery_users = User.objects.filter(role='delivery_agent')
    
    if delivery_users.exists():
        print(f"⚠️  Warning: {delivery_users.count()} delivery users still exist:")
        for user in delivery_users:
            print(f"   • {user.user_name} ({user.name})")
        return False
    else:
        print("✅ Verification successful: No delivery users found in database")
        return True

def main():
    """Main function"""
    print("🚀 Auto Delivery Users Deletion")
    print("=" * 40)
    
    # Delete all delivery users
    success = delete_all_delivery_users()
    
    if success:
        # Verify deletion
        verify_deletion()
        print("\n🎉 Delivery users deletion completed successfully!")
    else:
        print("\n❌ Deletion failed. Please check the errors above.")

if __name__ == '__main__':
    main()
