#!/usr/bin/env python3
"""
Test frontend integration by simulating form submission
"""

import requests
import json

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"

def test_frontend_restaurant_creation():
    """Test restaurant creation exactly as the frontend would send it"""
    
    # Login with our test user
    login_data = {
        "user_name": "testrestaurant",
        "password": "password123"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print("🔐 Logging in...")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=headers,
            data=json.dumps(login_data)
        )
        
        if response.status_code != 200:
            print("❌ Login failed")
            return False
        
        result = response.json()
        token = result['data']['access_token']
        print("✅ Login successful!")
        
        # Test with FormData-like structure (multipart/form-data simulation)
        # This simulates what the frontend sends
        
        # First, test with JSON (what our API expects)
        auth_headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        # Data structure that matches our enhanced frontend form
        restaurant_data = {
            "name": "Enhanced Test Restaurant",
            "description": "A comprehensive test restaurant with all enhanced features",
            "address": {
                "street": "Enhanced Street 456",
                "city": "Kabul",
                "state": "Kabul",
                "postal_code": "1002",
                "country": "Afghanistan",
                "latitude": 34.5553,
                "longitude": 69.2075
            },
            "contact_number": "+93701234568",
            "opening_time": "08:00:00",
            "closing_time": "23:00:00",
            "delivery_fee": "75.00",
            "min_order_amount": "250.00",
            "average_preparation_time": 35,
            "accepts_cash": True,
            "accepts_card": True,
            "accepts_online_payment": True,
            "website": "https://testrestaurant.com",
            "facebook_url": "https://facebook.com/testrestaurant",
            "instagram_url": "https://instagram.com/testrestaurant",
            "twitter_url": "https://twitter.com/testrestaurant"
        }
        
        print(f"\n🏗️ Creating enhanced restaurant...")
        
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            data=json.dumps(restaurant_data)
        )
        
        print(f"📡 Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ Enhanced restaurant created successfully!")
            print(f"   Restaurant ID: {result.get('id')}")
            print(f"   Name: {result.get('name')}")
            print(f"   Website: {result.get('website')}")
            print(f"   Facebook: {result.get('facebook_url')}")
            print(f"   Payment Methods:")
            print(f"     - Cash: {result.get('accepts_cash')}")
            print(f"     - Card: {result.get('accepts_card')}")
            print(f"     - Online: {result.get('accepts_online_payment')}")
            return True
        else:
            print("❌ Enhanced restaurant creation failed")
            try:
                error_data = response.json()
                print(f"🔍 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"🔍 Raw Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_get_restaurants():
    """Test getting the list of restaurants to verify our creations"""
    print("\n🔍 Getting restaurant list...")
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=headers
        )
        
        print(f"📡 Status: {response.status_code}")
        
        if response.status_code == 200:
            restaurants = response.json()
            print(f"📊 Found {len(restaurants)} restaurants:")
            
            for restaurant in restaurants:
                print(f"   - {restaurant.get('name')} (ID: {restaurant.get('id')})")
                print(f"     Delivery Fee: {restaurant.get('delivery_fee')}")
                print(f"     Website: {restaurant.get('website', 'None')}")
                print(f"     Verified: {restaurant.get('is_verified', False)}")
                print()
            
            return True
        else:
            print(f"❌ Failed to get restaurants: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Frontend Integration Test")
    print("=" * 60)
    
    # Test enhanced restaurant creation
    creation_success = test_frontend_restaurant_creation()
    
    # Test getting restaurants
    list_success = test_get_restaurants()
    
    print("=" * 60)
    if creation_success and list_success:
        print("🎉 SUCCESS: Frontend integration is working!")
        print("✅ Enhanced restaurant form should work perfectly")
        print("✅ All new fields are properly handled")
        print("✅ API endpoints are functioning correctly")
    else:
        print("❌ FAILED: Some issues remain")
        print(f"   Creation: {'✅' if creation_success else '❌'}")
        print(f"   List: {'✅' if list_success else '❌'}")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
