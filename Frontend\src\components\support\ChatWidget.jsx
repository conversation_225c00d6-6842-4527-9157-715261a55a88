import React, { useState, useRef, useEffect } from 'react';
import { MessageCircle, X, Send, Minimize2, User } from 'lucide-react';
import { useSupport } from '../../context/SupportContext';
import { useAuth } from '../../context/AuthContext';
import Button from '../common/Button';

const ChatWidget = () => {
  const { user } = useAuth();
  const {
    chatMessages,
    isChatOpen,
    setIsChatOpen,
    isOnline,
    unreadCount,
    sendChatMessage,
    markChatAsRead
  } = useSupport();

  const [message, setMessage] = useState('');
  const [isMinimized, setIsMinimized] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    if (isChatOpen) {
      scrollToBottom();
      markChatAsRead();
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [chatMessages, isChatOpen, markChatAsRead]);

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (!message.trim() || !user) return;

    sendChatMessage(message.trim());
    setMessage('');
  };

  const handleOpenChat = () => {
    setIsChatOpen(true);
    setIsMinimized(false);
    markChatAsRead();
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
    setIsMinimized(false);
  };

  const handleMinimize = () => {
    setIsMinimized(true);
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  // Don't show widget if user is not logged in
  if (!user) return null;

  return (
    <>
      {/* Chat Button */}
      {!isChatOpen && (
        <button
          onClick={handleOpenChat}
          className="fixed bottom-6 right-6 z-50 bg-primary-500 hover:bg-primary-600 text-white rounded-full p-4 shadow-lg transition-all duration-300 hover:scale-110"
        >
          <MessageCircle size={24} />
          {unreadCount > 0 && (
            <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-6 h-6 flex items-center justify-center font-bold">
              {unreadCount > 9 ? '9+' : unreadCount}
            </div>
          )}
        </button>
      )}

      {/* Chat Window */}
      {isChatOpen && (
        <div className={`fixed bottom-6 right-6 z-50 bg-white rounded-lg shadow-2xl border border-gray-200 transition-all duration-300 ${
          isMinimized ? 'w-80 h-16' : 'w-80 h-96'
        }`}>
          {/* Chat Header */}
          <div className="bg-primary-500 text-white p-4 rounded-t-lg flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center mr-3">
                <MessageCircle size={16} />
              </div>
              <div>
                <h3 className="font-semibold text-sm">Afghan Sofra Support</h3>
                <div className="flex items-center text-xs opacity-90">
                  <div className={`w-2 h-2 rounded-full mr-2 ${isOnline ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                  {isOnline ? 'Online' : 'Offline'}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={handleMinimize}
                className="p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
              >
                <Minimize2 size={16} />
              </button>
              <button
                onClick={handleCloseChat}
                className="p-1 hover:bg-white hover:bg-opacity-20 rounded transition-colors"
              >
                <X size={16} />
              </button>
            </div>
          </div>

          {/* Chat Content */}
          {!isMinimized && (
            <>
              {/* Messages */}
              <div className="h-64 overflow-y-auto p-4 space-y-3">
                {chatMessages.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    <MessageCircle size={32} className="mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Start a conversation with our support team!</p>
                  </div>
                ) : (
                  chatMessages.map((msg) => (
                    <div
                      key={msg.id}
                      className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-xs ${msg.sender === 'user' ? 'order-2' : 'order-1'}`}>
                        <div
                          className={`rounded-lg p-3 text-sm ${
                            msg.sender === 'user'
                              ? 'bg-primary-500 text-white'
                              : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {msg.message}
                        </div>
                        <div className={`text-xs text-gray-500 mt-1 ${
                          msg.sender === 'user' ? 'text-right' : 'text-left'
                        }`}>
                          {msg.senderName} • {formatTime(msg.timestamp)}
                        </div>
                      </div>
                      
                      {msg.sender !== 'user' && (
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center mr-2 order-0 flex-shrink-0">
                          <User size={14} className="text-gray-600" />
                        </div>
                      )}
                    </div>
                  ))
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <div className="border-t border-gray-200 p-4">
                <form onSubmit={handleSendMessage} className="flex space-x-2">
                  <input
                    ref={inputRef}
                    type="text"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Type your message..."
                    className="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    disabled={!isOnline}
                  />
                  <button
                    type="submit"
                    disabled={!message.trim() || !isOnline}
                    className="bg-primary-500 hover:bg-primary-600 disabled:bg-gray-300 text-white rounded-lg p-2 transition-colors"
                  >
                    <Send size={16} />
                  </button>
                </form>
                
                {!isOnline && (
                  <p className="text-xs text-gray-500 mt-2">
                    We're currently offline. Leave a message and we'll get back to you!
                  </p>
                )}
              </div>
            </>
          )}
        </div>
      )}
    </>
  );
};

export default ChatWidget;
