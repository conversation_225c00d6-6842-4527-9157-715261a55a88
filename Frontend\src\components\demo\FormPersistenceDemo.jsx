import React from 'react';
import { useForm } from 'react-hook-form';
import useFormPersistence from '../../hooks/useFormPersistence';
import Input from '../common/Input';
import Button from '../common/Button';
import { RefreshCw, Save, Trash2 } from 'lucide-react';

const FormPersistenceDemo = () => {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm({
    mode: 'onChange',
  });

  const watchedValues = watch();
  const { clearFormData } = useFormPersistence(
    watchedValues,
    setValue,
    'demo_form_data',
    ['password'] // Exclude password from persistence
  );

  const onSubmit = (data) => {
    alert(`Form submitted with data: ${JSON.stringify(data, null, 2)}`);
    clearFormData();
    reset();
  };

  const handleClearStorage = () => {
    clearFormData();
    reset();
    alert('Form data cleared from storage and form reset!');
  };

  const handleReload = () => {
    window.location.reload();
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Form Persistence Demo
        </h2>
        <p className="text-gray-600 text-sm">
          Fill out the form below, then reload the page to see persistence in action.
          Note: Password field is not persisted for security.
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <Input
          label="Name"
          placeholder="Enter your name"
          error={errors.name?.message}
          {...register('name', {
            required: 'Name is required',
            minLength: {
              value: 2,
              message: 'Name must be at least 2 characters',
            },
          })}
        />

        <Input
          label="Email"
          type="email"
          placeholder="Enter your email"
          error={errors.email?.message}
          {...register('email', {
            required: 'Email is required',
            pattern: {
              value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
              message: 'Please enter a valid email',
            },
          })}
        />

        <Input
          label="Phone"
          placeholder="Enter your phone number"
          error={errors.phone?.message}
          {...register('phone', {
            required: 'Phone is required',
            pattern: {
              value: /^[+]?[\d\s-()]+$/,
              message: 'Please enter a valid phone number',
            },
          })}
        />

        <Input
          label="Password (Not Persisted)"
          type="password"
          placeholder="Enter your password"
          error={errors.password?.message}
          {...register('password', {
            required: 'Password is required',
            minLength: {
              value: 6,
              message: 'Password must be at least 6 characters',
            },
          })}
        />

        <div className="flex gap-2 pt-4">
          <Button type="submit" variant="primary" className="flex-1">
            <Save size={16} className="mr-2" />
            Submit
          </Button>
          
          <Button
            type="button"
            variant="secondary"
            onClick={handleReload}
            className="flex-1"
          >
            <RefreshCw size={16} className="mr-2" />
            Reload Page
          </Button>
          
          <Button
            type="button"
            variant="outline"
            onClick={handleClearStorage}
            className="flex-1"
          >
            <Trash2 size={16} className="mr-2" />
            Clear
          </Button>
        </div>
      </form>

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-800 mb-2">Current Form Data:</h3>
        <pre className="text-xs text-gray-600 overflow-auto">
          {JSON.stringify(
            {
              ...watchedValues,
              password: watchedValues.password ? '[HIDDEN]' : undefined,
            },
            null,
            2
          )}
        </pre>
      </div>

      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">Instructions:</h3>
        <ol className="text-sm text-blue-700 space-y-1">
          <li>1. Fill out the form fields above</li>
          <li>2. Click "Reload Page" to test persistence</li>
          <li>3. Notice that all fields except password are restored</li>
          <li>4. Use "Clear" to manually clear stored data</li>
          <li>5. Submit the form to see automatic cleanup</li>
        </ol>
      </div>
    </div>
  );
};

export default FormPersistenceDemo;
