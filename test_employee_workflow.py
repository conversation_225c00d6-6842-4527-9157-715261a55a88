#!/usr/bin/env python3
"""
Test the employee delivery workflow
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_employee_workflow():
    """Test the complete employee workflow"""
    print("🚀 Testing Employee Delivery Workflow")
    print("=" * 50)
    
    # Step 1: Employee Login
    print("\n1. Employee Login")
    print("-" * 30)
    
    emp_login = {
        "user_name": "EMP001",
        "password": "employee123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/", json=emp_login)
        if response.status_code == 200:
            emp_token = response.json()['data']['access_token']
            user_info = response.json()['data']['user']
            print(f"✅ Employee login successful: {user_info['name']}")
        else:
            print("❌ Employee login failed")
            return False
    except Exception as e:
        print(f"❌ Employee login error: {e}")
        return False
    
    emp_headers = {"Authorization": f"Bearer {emp_token}"}
    
    # Step 2: Get Assigned Orders
    print("\n2. Getting Assigned Orders")
    print("-" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/delivery-agent/my-orders/", headers=emp_headers)
        if response.status_code == 200:
            orders_data = response.json()['data']
            orders = orders_data['orders']
            print(f"✅ Found {len(orders)} assigned orders")
            
            if orders:
                order = orders[0]
                print(f"📦 Order #{order['id']}")
                print(f"   Status: {order['status']}")
                print(f"   Customer: {order['customer']['name']}")
                print(f"   Restaurant: {order['restaurant']['name']}")
                print(f"   Total: ${order['total_amount']}")
                print(f"   Can Accept: {order['can_accept']}")
                print(f"   Can Reject: {order['can_reject']}")
                
                if order['can_accept']:
                    return test_order_workflow(order['id'], emp_headers)
                else:
                    print("⚠️  Order cannot be accepted")
                    return True
            else:
                print("⚠️  No assigned orders found")
                return True
                
        else:
            print(f"❌ Failed to get orders: {response.json()}")
            return False
            
    except Exception as e:
        print(f"❌ Error getting orders: {e}")
        return False

def test_order_workflow(order_id, headers):
    """Test the complete order workflow"""
    print(f"\n3. Testing Order Workflow for Order #{order_id}")
    print("-" * 50)
    
    # Step 1: Accept Order
    print("\n3.1 Accepting Order")
    try:
        response = requests.post(f"{BASE_URL}/delivery-agent/accept-order/", 
                               json={"order_id": order_id}, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Order accepted: {result['message']}")
            print(f"   New Status: {result['new_status']}")
            if 'next_steps' in result:
                print(f"   Next Steps: {', '.join(result['next_steps'])}")
        else:
            print(f"❌ Accept failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Accept error: {e}")
        return False
    
    # Step 2: Update Status - En Route to Restaurant
    print("\n3.2 En Route to Restaurant")
    try:
        response = requests.post(f"{BASE_URL}/delivery-agent/update-order-status/", 
                               json={"order_id": order_id, "status": "en_route_to_restaurant"}, 
                               headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status updated: {result['message']}")
            if 'next_steps' in result:
                print(f"   Next Steps: {', '.join(result['next_steps'])}")
        else:
            print(f"❌ Status update failed: {response.json()}")
    except Exception as e:
        print(f"❌ Status update error: {e}")
    
    # Step 3: Arrived at Restaurant
    print("\n3.3 Arrived at Restaurant")
    try:
        response = requests.post(f"{BASE_URL}/delivery-agent/update-order-status/", 
                               json={"order_id": order_id, "status": "arrived_at_restaurant"}, 
                               headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status updated: {result['message']}")
        else:
            print(f"❌ Status update failed: {response.json()}")
    except Exception as e:
        print(f"❌ Status update error: {e}")
    
    # Step 4: Picked Up
    print("\n3.4 Picked Up Order")
    try:
        response = requests.post(f"{BASE_URL}/delivery-agent/update-order-status/", 
                               json={"order_id": order_id, "status": "picked_up"}, 
                               headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status updated: {result['message']}")
        else:
            print(f"❌ Status update failed: {response.json()}")
    except Exception as e:
        print(f"❌ Status update error: {e}")
    
    # Step 5: En Route to Customer
    print("\n3.5 En Route to Customer")
    try:
        response = requests.post(f"{BASE_URL}/delivery-agent/update-order-status/", 
                               json={"order_id": order_id, "status": "en_route_to_customer"}, 
                               headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status updated: {result['message']}")
        else:
            print(f"❌ Status update failed: {response.json()}")
    except Exception as e:
        print(f"❌ Status update error: {e}")
    
    # Step 6: Arrived at Customer
    print("\n3.6 Arrived at Customer")
    try:
        response = requests.post(f"{BASE_URL}/delivery-agent/update-order-status/", 
                               json={"order_id": order_id, "status": "arrived_at_customer"}, 
                               headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status updated: {result['message']}")
        else:
            print(f"❌ Status update failed: {response.json()}")
    except Exception as e:
        print(f"❌ Status update error: {e}")
    
    # Step 7: Delivered
    print("\n3.7 Delivered Order")
    try:
        response = requests.post(f"{BASE_URL}/delivery-agent/update-order-status/", 
                               json={"order_id": order_id, "status": "delivered"}, 
                               headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Status updated: {result['message']}")
            
            # Check if it's cash on delivery
            order_details = result.get('order_details', {})
            if order_details.get('payment_method') == 'cash_on_delivery':
                print(f"💰 Cash on Delivery - Amount: ${order_details.get('total_amount')}")
                return test_cash_collection(order_id, order_details.get('total_amount'), headers)
            else:
                return test_complete_order(order_id, headers)
        else:
            print(f"❌ Status update failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Status update error: {e}")
        return False

def test_cash_collection(order_id, amount, headers):
    """Test cash collection"""
    print(f"\n3.8 Collecting Cash (${amount})")
    try:
        response = requests.post(f"{BASE_URL}/delivery-agent/collect-cash/", 
                               json={"order_id": order_id, "amount_collected": amount}, 
                               headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Cash collected: {result['message']}")
            print(f"   Amount: ${result['amount_collected']}")
            print(f"   Delivery Fee Earned: ${result['delivery_fee_earned']}")
            
            return test_complete_order(order_id, headers)
        else:
            print(f"❌ Cash collection failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Cash collection error: {e}")
        return False

def test_complete_order(order_id, headers):
    """Test order completion"""
    print(f"\n3.9 Completing Order")
    try:
        response = requests.post(f"{BASE_URL}/delivery-agent/update-order-status/", 
                               json={"order_id": order_id, "status": "completed"}, 
                               headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Order completed: {result['message']}")
            return True
        else:
            print(f"❌ Order completion failed: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Order completion error: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Employee Delivery Workflow Test")
    print("Testing complete order lifecycle from assignment to completion")
    
    success = test_employee_workflow()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Employee Delivery Workflow Test PASSED!")
        print("\n📋 Workflow Tested:")
        print("   1. Employee login with credentials")
        print("   2. Fetch assigned orders")
        print("   3. Accept order")
        print("   4. Update status through all stages")
        print("   5. Collect cash (if COD)")
        print("   6. Complete order")
        
        print("\n🎯 Frontend Ready:")
        print("   - Login with EMP001 / employee123")
        print("   - Navigate to Orders page")
        print("   - See assigned orders and workflow buttons")
    else:
        print("❌ Some workflow steps failed")

if __name__ == "__main__":
    main()
