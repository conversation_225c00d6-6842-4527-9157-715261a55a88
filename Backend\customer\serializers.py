# customer/serializers.py
from rest_framework import serializers
from .models import CustomerFavorite
from restaurant.serializers import RestaurantSerializer
from users.models import User

class CustomerFavoriteSerializer(serializers.ModelSerializer):
    """Serializer for customer favorites"""
    restaurant = RestaurantSerializer(read_only=True)
    restaurant_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = CustomerFavorite
        fields = ['id', 'restaurant', 'restaurant_id', 'created_at']
        read_only_fields = ['id', 'created_at']

    def create(self, validated_data):
        # Get the customer from the request context
        customer = self.context['request'].user
        validated_data['customer'] = customer
        return super().create(validated_data)

    def validate_restaurant_id(self, value):
        """Validate that the restaurant exists and is active"""
        from restaurant.models import Restaurant
        try:
            restaurant = Restaurant.objects.get(id=value, is_active=True)
            return value
        except Restaurant.DoesNotExist:
            raise serializers.ValidationError("Restaurant not found or inactive")

class FavoriteRestaurantSerializer(serializers.ModelSerializer):
    """Simplified serializer for just the restaurant data in favorites"""
    cuisine_types = serializers.SerializerMethodField()
    is_favorite = serializers.SerializerMethodField()
    favorite_added_at = serializers.SerializerMethodField()

    class Meta:
        from restaurant.models import Restaurant
        model = Restaurant
        fields = [
            'id', 'name', 'description', 'logo', 'banner', 'rating',
            'delivery_fee', 'min_order_amount', 'average_preparation_time',
            'opening_time', 'closing_time', 'is_active', 'is_verified',
            'cuisine_types', 'is_favorite', 'favorite_added_at'
        ]

    def get_cuisine_types(self, obj):
        """Get cuisine types as list of names"""
        return [ct.name for ct in obj.cuisine_types.all()]

    def get_is_favorite(self, obj):
        """Always return True since this is in favorites list"""
        return True

    def get_favorite_added_at(self, obj):
        """Get when this restaurant was added to favorites"""
        request = self.context.get('request')
        if request and request.user:
            try:
                favorite = CustomerFavorite.objects.get(
                    customer=request.user,
                    restaurant=obj
                )
                return favorite.created_at.isoformat()
            except CustomerFavorite.DoesNotExist:
                return None
        return None
