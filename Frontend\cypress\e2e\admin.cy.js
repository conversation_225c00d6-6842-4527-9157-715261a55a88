describe("Admin Panel Smoke Test", () => {
  before(() => {
    // Log in as admin before all tests
    cy.visit("/login");
    cy.get('input[type="email"]').type("<EMAIL>");
    cy.get('input[type="password"]').type("password123");
    cy.get('button[type="submit"]').click();
    cy.url().should("include", "/admin");
  });

  it("loads the Admin Dashboard", () => {
    cy.visit("/admin");
    cy.contains("Dashboard").should("exist");
  });

  it("loads User Management", () => {
    cy.visit("/admin/user-management");
    cy.contains("User Management").should("exist");
  });

  it("loads Restaurant Management", () => {
    cy.visit("/admin/restaurant-management");
    cy.contains("Restaurant Management").should("exist");
  });

  it("loads Restaurant Approval", () => {
    cy.visit("/admin/restaurant-approval");
    cy.contains("Restaurant Approval").should("exist");
  });

  it("loads Order Management", () => {
    cy.visit("/admin/order-management");
    cy.contains("Order Management").should("exist");
  });

  it("loads Delivery Agent Management", () => {
    cy.visit("/admin/delivery-agent-management");
    cy.contains("Delivery Agent Management").should("exist");
  });

  it("loads Analytics", () => {
    cy.visit("/admin/analytics");
    cy.contains("Analytics").should("exist");
  });

  it("loads Real-Time Demo", () => {
    cy.visit("/admin/real-time-demo");
    cy.contains("Real-Time Demo").should("exist");
  });
});
