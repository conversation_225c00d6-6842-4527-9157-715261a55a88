from django.contrib import admin
from .models import DeliveryAgentProfile

@admin.register(DeliveryAgentProfile)
class DeliveryAgentProfileAdmin(admin.ModelAdmin):
    """
    Admin interface for delivery agent employees
    Employee-based system management
    """
    list_display = [
        'agent_id', 'employee_number', 'full_name', 'phone_number',
        'employment_status', 'availability', 'is_clocked_in', 'hire_date'
    ]

    list_filter = [
        'employment_status', 'availability', 'is_verified', 'province',
        'vehicle_type', 'hire_date', 'salary_type', 'work_schedule'
    ]
    
    search_fields = [
        'agent_id', 'full_name', 'father_name', 'national_id', 
        'phone_number', 'email'
    ]
    
    readonly_fields = [
        'agent_id', 'employee_number', 'created_at', 'updated_at',
        'last_clock_in', 'last_clock_out', 'current_shift_hours',
        'total_deliveries', 'successful_deliveries', 'total_earnings', 'rating'
    ]
    
    fieldsets = (
        ('Employee Information', {
            'fields': (
                'agent_id', 'employee_number', 'user', 'employment_status',
                'hire_date', 'termination_date', 'supervisor'
            )
        }),
        ('Personal Information', {
            'fields': (
                'full_name', 'father_name', 'national_id', 'date_of_birth',
                'gender', 'marital_status'
            )
        }),
        ('Compensation', {
            'fields': (
                'salary_type', 'base_salary', 'commission_per_delivery', 'hourly_rate'
            )
        }),
        ('Work Schedule', {
            'fields': (
                'work_schedule', 'shift_start_time', 'shift_end_time', 'working_days'
            )
        }),
        ('Current Status', {
            'fields': (
                'availability', 'is_clocked_in', 'last_clock_in', 'last_clock_out', 'current_shift_hours'
            )
        }),
        ('Contact Information', {
            'fields': (
                'phone_number', 'secondary_phone', 'email'
            )
        }),
        ('Address Information', {
            'fields': (
                'province', 'district', 'area', 'street_address', 'nearby_landmark'
            )
        }),
        ('Vehicle Information', {
            'fields': (
                'vehicle_type', 'vehicle_model', 'vehicle_year',
                'license_plate', 'vehicle_color', 'driving_license'
            )
        }),
        ('Documents', {
            'fields': (
                'tazkira_front_image', 'tazkira_back_image',
                'driving_license_image', 'vehicle_registration_image',
                'profile_photo'
            )
        }),
        ('References', {
            'fields': (
                'reference1_name', 'reference1_phone', 'reference1_relation',
                'reference2_name', 'reference2_phone', 'reference2_relation'
            )
        }),
        ('Emergency Contact', {
            'fields': (
                'emergency_contact', 'emergency_relation'
            )
        }),
        ('Banking Information', {
            'fields': (
                'bank_name', 'account_number', 'account_holder_name', 'mobile_wallet'
            )
        }),
        ('Employee Status', {
            'fields': (
                'is_verified', 'training_completed', 'training_completion_date',
                'documents_complete', 'background_check_completed', 'medical_clearance',
                'admin_notes', 'performance_notes'
            )
        }),
        ('Performance Metrics', {
            'fields': (
                'total_deliveries', 'successful_deliveries', 'total_earnings', 'rating'
            )
        }),
        ('Location Tracking', {
            'fields': (
                'current_latitude', 'current_longitude', 'current_address', 'last_location_update'
            )
        }),
        ('Timestamps', {
            'fields': (
                'created_at', 'updated_at', 'last_active'
            )
        })
    )
    
    actions = ['activate_employees', 'deactivate_employees', 'complete_training', 'clock_out_all']

    def activate_employees(self, request, queryset):
        """Activate employees"""
        updated = queryset.filter(employment_status='inactive').update(employment_status='active')
        self.message_user(request, f'{updated} employees activated.')
    activate_employees.short_description = "Activate selected employees"

    def deactivate_employees(self, request, queryset):
        """Deactivate employees"""
        updated = queryset.filter(employment_status='active').update(employment_status='inactive')
        self.message_user(request, f'{updated} employees deactivated.')
    deactivate_employees.short_description = "Deactivate selected employees"

    def complete_training(self, request, queryset):
        """Mark training as completed"""
        from django.utils import timezone
        updated = queryset.filter(training_completed=False).update(
            training_completed=True,
            training_completion_date=timezone.now().date()
        )
        self.message_user(request, f'{updated} employees marked as training completed.')
    complete_training.short_description = "Mark training completed"

    def clock_out_all(self, request, queryset):
        """Clock out all selected employees"""
        updated = queryset.filter(is_clocked_in=True).update(
            is_clocked_in=False,
            availability='offline'
        )
        self.message_user(request, f'{updated} employees clocked out.')
    clock_out_all.short_description = "Clock out selected employees"
    
    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related('user', 'supervisor')
    
    def has_delete_permission(self, request, obj=None):
        """Restrict deletion to superusers only"""
        return request.user.is_superuser
