# financial_management/models.py
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
from django.conf import settings


class CommissionStructure(models.Model):
    """Platform commission structure for restaurants"""
    restaurant = models.OneToOneField(
        'restaurant.Restaurant',
        on_delete=models.CASCADE,
        related_name='commission_structure'
    )
    commission_rate = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=15.00,
        validators=[MinV<PERSON>ueValidator(0), MaxValueValidator(50)],
        help_text="Commission percentage (0-50%)"
    )
    payment_processing_fee = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=2.50,
        help_text="Payment processing fee percentage"
    )
    delivery_fee_share = models.DecimalField(
        max_digits=5, 
        decimal_places=2, 
        default=30.00,
        help_text="Platform's share of delivery fee percentage"
    )
    minimum_payout_amount = models.DecimalField(
        max_digits=8, 
        decimal_places=2, 
        default=50.00,
        help_text="Minimum amount for payout"
    )
    payout_frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('bi_weekly', 'Bi-Weekly'),
            ('monthly', 'Monthly'),
        ],
        default='weekly'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.restaurant.name} - {self.commission_rate}%"


class RestaurantEarnings(models.Model):
    """Track restaurant earnings from orders"""
    restaurant = models.ForeignKey(
        'restaurant.Restaurant',
        on_delete=models.CASCADE,
        related_name='earnings'
    )
    order = models.OneToOneField(
        'orders.Order',
        on_delete=models.CASCADE,
        related_name='earnings'
    )
    
    # Order amounts
    order_total = models.DecimalField(max_digits=10, decimal_places=2)
    delivery_fee = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    
    # Platform fees
    commission_amount = models.DecimalField(max_digits=8, decimal_places=2)
    payment_processing_fee = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    delivery_fee_platform_share = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    
    # Restaurant earnings
    gross_earnings = models.DecimalField(max_digits=10, decimal_places=2)
    net_earnings = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Status
    is_paid = models.BooleanField(default=False)
    payout_date = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.restaurant.name} - Order #{self.order.id} - ${self.net_earnings}"

    def calculate_earnings(self):
        """Calculate restaurant earnings based on commission structure"""
        commission_structure = self.restaurant.commission_structure
        
        # Calculate commission
        self.commission_amount = (self.order_total * commission_structure.commission_rate) / 100
        
        # Calculate payment processing fee
        self.payment_processing_fee = (self.order_total * commission_structure.payment_processing_fee) / 100
        
        # Calculate delivery fee platform share
        self.delivery_fee_platform_share = (self.delivery_fee * commission_structure.delivery_fee_share) / 100
        
        # Calculate gross earnings (order total + delivery fee)
        self.gross_earnings = self.order_total + self.delivery_fee
        
        # Calculate net earnings (gross - all fees)
        self.net_earnings = (
            self.gross_earnings 
            - self.commission_amount 
            - self.payment_processing_fee 
            - self.delivery_fee_platform_share
        )
        
        self.save()


class RestaurantPayout(models.Model):
    """Restaurant payout records"""
    restaurant = models.ForeignKey(
        'restaurant.Restaurant',
        on_delete=models.CASCADE,
        related_name='payouts'
    )
    
    # Payout details
    payout_amount = models.DecimalField(max_digits=12, decimal_places=2)
    payout_period_start = models.DateTimeField()
    payout_period_end = models.DateTimeField()
    
    # Orders included in this payout
    orders_count = models.PositiveIntegerField()
    total_gross_earnings = models.DecimalField(max_digits=12, decimal_places=2)
    total_commission = models.DecimalField(max_digits=12, decimal_places=2)
    total_fees = models.DecimalField(max_digits=12, decimal_places=2)
    
    # Payout status
    PAYOUT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    status = models.CharField(
        max_length=20, 
        choices=PAYOUT_STATUS_CHOICES, 
        default='pending'
    )
    
    # Payment details
    payment_method = models.CharField(
        max_length=50,
        choices=[
            ('bank_transfer', 'Bank Transfer'),
            ('paypal', 'PayPal'),
            ('stripe', 'Stripe'),
            ('check', 'Check'),
        ],
        default='bank_transfer'
    )
    transaction_id = models.CharField(max_length=100, blank=True)
    payment_reference = models.CharField(max_length=100, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.restaurant.name} - ${self.payout_amount} - {self.status}"


class RestaurantBankAccount(models.Model):
    """Restaurant bank account details for payouts"""
    restaurant = models.OneToOneField(
        'restaurant.Restaurant',
        on_delete=models.CASCADE,
        related_name='bank_account'
    )
    
    # Bank details
    bank_name = models.CharField(max_length=100)
    account_holder_name = models.CharField(max_length=100)
    account_number = models.CharField(max_length=50)
    routing_number = models.CharField(max_length=20, blank=True)
    swift_code = models.CharField(max_length=20, blank=True)
    
    # Address
    bank_address = models.TextField()
    
    # Verification
    is_verified = models.BooleanField(default=False)
    verified_at = models.DateTimeField(null=True, blank=True)
    verified_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_bank_accounts'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.restaurant.name} - {self.bank_name}"


class FinancialReport(models.Model):
    """Monthly/weekly financial reports for restaurants"""
    restaurant = models.ForeignKey(
        'restaurant.Restaurant',
        on_delete=models.CASCADE,
        related_name='financial_reports'
    )
    
    # Report period
    report_type = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly'),
            ('yearly', 'Yearly'),
        ]
    )
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    
    # Financial metrics
    total_orders = models.PositiveIntegerField(default=0)
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_commission = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_fees = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    net_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Performance metrics
    average_order_value = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    commission_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-period_start']
        unique_together = ('restaurant', 'report_type', 'period_start')

    def __str__(self):
        return f"{self.restaurant.name} - {self.report_type} - {self.period_start.date()}"
