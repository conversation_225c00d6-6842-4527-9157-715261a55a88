import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  CreditCard,
  Download,
  Eye,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Banknote,
  Pie<PERSON>hart,
  BarChart3,
} from "lucide-react";
import { Line, Doughnut, Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
} from "chart.js";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement
);

function FinancialDashboard() {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [earnings, setEarnings] = useState([]);
  const [payouts, setPayouts] = useState([]);
  const [bankAccount, setBankAccount] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [showPayoutModal, setShowPayoutModal] = useState(false);
  const [payoutAmount, setPayoutAmount] = useState("");

  useEffect(() => {
    if (user && user.role === "restaurant") {
      loadFinancialData();
    }
  }, [user]);

  const loadFinancialData = async () => {
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const headers = {
          Authorization: `Bearer ${userData.access_token}`,
          "Content-Type": "application/json",
        };

        // Load dashboard data
        const dashboardResponse = await fetch(
          "http://127.0.0.1:8000/api/financial-management/restaurant-financial/dashboard/",
          { headers }
        );

        if (dashboardResponse.ok) {
          const data = await dashboardResponse.json();
          setDashboardData(data);
        }

        // Load earnings
        const earningsResponse = await fetch(
          "http://127.0.0.1:8000/api/financial-management/restaurant-financial/earnings/",
          { headers }
        );

        if (earningsResponse.ok) {
          const earningsData = await earningsResponse.json();
          setEarnings(earningsData);
        }

        // Load payouts
        const payoutsResponse = await fetch(
          "http://127.0.0.1:8000/api/financial-management/restaurant-financial/payouts/",
          { headers }
        );

        if (payoutsResponse.ok) {
          const payoutsData = await payoutsResponse.json();
          setPayouts(payoutsData);
        }

        // Load bank account
        const bankResponse = await fetch(
          "http://127.0.0.1:8000/api/financial-management/restaurant-financial/bank_account/",
          { headers }
        );

        if (bankResponse.ok) {
          const bankData = await bankResponse.json();
          setBankAccount(bankData);
        }
      }
    } catch (error) {
      console.error("Error loading financial data:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePayoutRequest = async () => {
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const headers = {
          Authorization: `Bearer ${userData.access_token}`,
          "Content-Type": "application/json",
        };

        const response = await fetch(
          "http://127.0.0.1:8000/api/financial-management/restaurant-financial/request_payout/",
          {
            method: "POST",
            headers,
            body: JSON.stringify({
              amount: parseFloat(payoutAmount),
              payment_method: "bank_transfer",
            }),
          }
        );

        if (response.ok) {
          alert("Payout request submitted successfully!");
          setShowPayoutModal(false);
          setPayoutAmount("");
          loadFinancialData();
        } else {
          const error = await response.json();
          alert(`Error: ${error.error || "Failed to submit payout request"}`);
        }
      }
    } catch (error) {
      console.error("Error requesting payout:", error);
      alert("Failed to submit payout request");
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Financial Dashboard</h1>
        <div className="flex justify-center items-center h-64">
          <RefreshCw className="animate-spin h-8 w-8 text-blue-600" />
          <span className="ml-2">Loading financial data...</span>
        </div>
      </div>
    );
  }

  // Chart data for revenue
  const revenueChartData = {
    labels: dashboardData?.revenue_chart_data?.map((item) =>
      new Date(item.date).toLocaleDateString()
    ) || [],
    datasets: [
      {
        label: "Revenue",
        data: dashboardData?.revenue_chart_data?.map((item) => item.revenue) || [],
        borderColor: "rgb(59, 130, 246)",
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        tension: 0.4,
        fill: true,
      },
    ],
  };

  // Chart data for commission breakdown
  const commissionChartData = {
    labels: ["Platform Commission", "Payment Processing", "Delivery Fee Share"],
    datasets: [
      {
        data: [
          dashboardData?.commission_breakdown?.platform_commission || 0,
          dashboardData?.commission_breakdown?.payment_processing || 0,
          dashboardData?.commission_breakdown?.delivery_fee_share || 0,
        ],
        backgroundColor: [
          "rgba(239, 68, 68, 0.8)",
          "rgba(245, 158, 11, 0.8)",
          "rgba(34, 197, 94, 0.8)",
        ],
        borderColor: [
          "rgba(239, 68, 68, 1)",
          "rgba(245, 158, 11, 1)",
          "rgba(34, 197, 94, 1)",
        ],
        borderWidth: 1,
      },
    ],
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Financial Dashboard
              </h1>
              <p className="mt-1 text-sm text-gray-600">
                Manage your earnings, payouts, and financial reports
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={loadFinancialData}
                className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <RefreshCw size={16} />
                <span>Refresh</span>
              </button>
              <button
                onClick={() => setShowPayoutModal(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <DollarSign size={16} />
                <span>Request Payout</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: "overview", name: "Overview", icon: BarChart3 },
              { id: "earnings", name: "Earnings", icon: DollarSign },
              { id: "payouts", name: "Payouts", icon: CreditCard },
              { id: "settings", name: "Settings", icon: Banknote },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? "border-blue-500 text-blue-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                <tab.icon size={16} />
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === "overview" && dashboardData && (
          <div className="space-y-8">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <DollarSign className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Current Period Revenue
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(dashboardData.current_period_revenue)}
                    </p>
                    <div className="flex items-center mt-1">
                      {dashboardData.revenue_growth >= 0 ? (
                        <TrendingUp className="h-4 w-4 text-green-500" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-red-500" />
                      )}
                      <span
                        className={`text-sm ml-1 ${
                          dashboardData.revenue_growth >= 0
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {Math.abs(dashboardData.revenue_growth).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Banknote className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Net Earnings
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(dashboardData.current_period_net_earnings)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Clock className="h-8 w-8 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Pending Payout
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(dashboardData.pending_payout_amount)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BarChart3 className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">
                      Total Orders
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {dashboardData.current_period_orders}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Revenue Chart */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Revenue Trend</h3>
                <div className="h-64">
                  <Line
                    data={revenueChartData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true,
                        },
                      },
                    }}
                  />
                </div>
              </div>

              {/* Commission Breakdown */}
              <div className="bg-white rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold mb-4">Commission Breakdown</h3>
                <div className="h-64">
                  <Doughnut
                    data={commissionChartData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Payout Request Modal */}
      {showPayoutModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h2 className="text-xl font-bold mb-4">Request Payout</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payout Amount
                </label>
                <input
                  type="number"
                  value={payoutAmount}
                  onChange={(e) => setPayoutAmount(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter amount"
                />
              </div>
              <div className="text-sm text-gray-600">
                Available for payout: {formatCurrency(dashboardData?.pending_payout_amount || 0)}
              </div>
            </div>
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowPayoutModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handlePayoutRequest}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Submit Request
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default FinancialDashboard;
