/**
 * Address System Test Page
 * Test the new address management system
 */

import React, { useState, useEffect } from "react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import AddressSelector from "../../components/address/AddressSelector";
import useAddresses from "../../hooks/useAddresses";
import addressService from "../../services/addressService";

const AddressSystemTest = () => {
  const {
    addresses,
    selectedAddress,
    loading,
    error,
    isInitialized,
    hasAddresses,
    refreshAddresses,
    clearAll,
  } = useAddresses();

  const [testResults, setTestResults] = useState([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  // Add test result
  const addTestResult = (test, passed, message) => {
    setTestResults((prev) => [
      ...prev,
      {
        test,
        passed,
        message,
        timestamp: new Date().toLocaleTimeString(),
      },
    ]);
  };

  // Run comprehensive tests
  const runTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    try {
      // Test 1: Service initialization
      addTestResult(
        "Service Initialization",
        addressService.isInitialized,
        addressService.isInitialized
          ? "Service initialized successfully"
          : "Service not initialized"
      );

      // Test 2: Address loading
      const loadedAddresses = await addressService.getAddresses();
      addTestResult(
        "Address Loading",
        Array.isArray(loadedAddresses),
        `Loaded ${loadedAddresses.length} addresses`
      );

      // Test 3: Backend sync
      try {
        await addressService.syncWithBackend();
        addTestResult("Backend Sync", true, "Successfully synced with backend");
      } catch (err) {
        addTestResult("Backend Sync", false, `Sync failed: ${err.message}`);
      }

      // Test 4: Address validation
      if (selectedAddress) {
        const validation =
          addressService.validateAddressForOrder(selectedAddress);
        addTestResult(
          "Address Validation",
          validation.valid,
          validation.valid
            ? "Selected address is valid for orders"
            : validation.error
        );
      } else {
        addTestResult("Address Validation", false, "No address selected");
      }

      // Test 5: Order address ID
      try {
        const orderAddressId = await addressService.getValidOrderAddressId();
        addTestResult(
          "Order Address ID",
          !!orderAddressId,
          orderAddressId
            ? `Valid order address ID: ${orderAddressId}`
            : "No valid order address ID"
        );
      } catch (err) {
        addTestResult("Order Address ID", false, `Failed: ${err.message}`);
      }
    } catch (error) {
      addTestResult("Test Suite", false, `Test suite failed: ${error.message}`);
    } finally {
      setIsRunningTests(false);
    }
  };

  // Clear all data
  const handleClearAll = () => {
    if (
      window.confirm(
        "Are you sure you want to clear all address data? This will reset everything."
      )
    ) {
      clearAll();
      setTestResults([]);
    }
  };

  return (
    <div className='min-h-screen bg-gray-50 p-4'>
      <div className='max-w-6xl mx-auto'>
        <div className='mb-6'>
          <h1 className='text-3xl font-bold text-gray-900 mb-2'>
            Address System Test Page
          </h1>
          <p className='text-gray-600'>
            Test and debug the new address management system
          </p>
        </div>

        {/* System Status */}
        <Card className='mb-6 p-6'>
          <h2 className='text-xl font-semibold mb-4'>System Status</h2>
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
            <div className='text-center'>
              <div
                className={`w-4 h-4 rounded-full mx-auto mb-2 ${
                  isInitialized ? "bg-green-500" : "bg-red-500"
                }`}
              ></div>
              <p className='text-sm font-medium'>Initialized</p>
              <p className='text-xs text-gray-600'>
                {isInitialized ? "Yes" : "No"}
              </p>
            </div>
            <div className='text-center'>
              <div
                className={`w-4 h-4 rounded-full mx-auto mb-2 ${
                  hasAddresses ? "bg-green-500" : "bg-yellow-500"
                }`}
              ></div>
              <p className='text-sm font-medium'>Has Addresses</p>
              <p className='text-xs text-gray-600'>{addresses.length}</p>
            </div>
            <div className='text-center'>
              <div
                className={`w-4 h-4 rounded-full mx-auto mb-2 ${
                  selectedAddress ? "bg-green-500" : "bg-yellow-500"
                }`}
              ></div>
              <p className='text-sm font-medium'>Selected</p>
              <p className='text-xs text-gray-600'>
                {selectedAddress ? "Yes" : "No"}
              </p>
            </div>
            <div className='text-center'>
              <div
                className={`w-4 h-4 rounded-full mx-auto mb-2 ${
                  error ? "bg-red-500" : "bg-green-500"
                }`}
              ></div>
              <p className='text-sm font-medium'>Status</p>
              <p className='text-xs text-gray-600'>{error ? "Error" : "OK"}</p>
            </div>
          </div>

          {error && (
            <div className='mt-4 p-3 bg-red-50 border border-red-200 rounded'>
              <p className='text-red-800 text-sm'>{error}</p>
            </div>
          )}
        </Card>

        {/* Test Controls */}
        <Card className='mb-6 p-6'>
          <h2 className='text-xl font-semibold mb-4'>Test Controls</h2>
          <div className='flex flex-wrap gap-3'>
            <Button
              onClick={runTests}
              disabled={isRunningTests}
              className='bg-blue-600 hover:bg-blue-700 text-white'
            >
              {isRunningTests ? "Running Tests..." : "Run All Tests"}
            </Button>
            <Button
              onClick={refreshAddresses}
              disabled={loading}
              variant='outline'
            >
              Refresh Addresses
            </Button>
            <Button
              onClick={handleClearAll}
              variant='outline'
              className='text-red-600 border-red-300 hover:bg-red-50'
            >
              Clear All Data
            </Button>
          </div>
        </Card>

        {/* Test Results */}
        {testResults.length > 0 && (
          <Card className='mb-6 p-6'>
            <h2 className='text-xl font-semibold mb-4'>Test Results</h2>
            <div className='space-y-2'>
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-3 rounded ${
                    result.passed
                      ? "bg-green-50 border border-green-200"
                      : "bg-red-50 border border-red-200"
                  }`}
                >
                  <div className='flex items-center space-x-3'>
                    <div
                      className={`w-3 h-3 rounded-full ${
                        result.passed ? "bg-green-500" : "bg-red-500"
                      }`}
                    ></div>
                    <span className='font-medium'>{result.test}</span>
                  </div>
                  <div className='text-right'>
                    <p
                      className={`text-sm ${
                        result.passed ? "text-green-700" : "text-red-700"
                      }`}
                    >
                      {result.message}
                    </p>
                    <p className='text-xs text-gray-500'>{result.timestamp}</p>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Address Selector */}
        <Card className='mb-6 p-6'>
          <h2 className='text-xl font-semibold mb-4'>Address Selector Test</h2>
          <AddressSelector
            onAddressSelect={(address) => {
              console.log("Address selected:", address);
              addTestResult(
                "Address Selection",
                true,
                `Selected: ${address.label || address.address}`
              );
            }}
          />
        </Card>

        {/* Debug Information */}
        <Card className='p-6'>
          <h2 className='text-xl font-semibold mb-4'>Debug Information</h2>
          <div className='space-y-4'>
            <div>
              <h3 className='font-medium mb-2'>
                Addresses ({addresses.length})
              </h3>
              <pre className='bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40'>
                {JSON.stringify(addresses, null, 2)}
              </pre>
            </div>

            {selectedAddress && (
              <div>
                <h3 className='font-medium mb-2'>Selected Address</h3>
                <pre className='bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40'>
                  {JSON.stringify(selectedAddress, null, 2)}
                </pre>
              </div>
            )}

            <div>
              <h3 className='font-medium mb-2'>localStorage Data</h3>
              <pre className='bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40'>
                {JSON.stringify(
                  {
                    addresses: localStorage.getItem("afghanSofra_addresses"),
                    selectedAddress: localStorage.getItem(
                      "afghanSofra_selectedAddress"
                    ),
                    cart: localStorage.getItem("afghanSofraCart"),
                  },
                  null,
                  2
                )}
              </pre>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AddressSystemTest;
