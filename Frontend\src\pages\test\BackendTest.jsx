import React, { useState } from "react";
import { authApi } from "../../utils/authApi";

const BackendTest = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const addResult = (test, result) => {
    setResults(prev => [...prev, { 
      test, 
      result, 
      timestamp: new Date().toLocaleTimeString(),
      success: result.success 
    }]);
  };

  const testBackendConnection = async () => {
    setLoading(true);
    try {
      console.log("🌐 Testing backend connection...");
      
      // Test with a simple registration call to see if backend responds
      const testData = {
        name: "Backend Test User",
        user_name: "backendtest" + Date.now(),
        email: "<EMAIL>",
        password: "123456",
        phone: "+1234567890",
        role: "customer"
      };

      const result = await authApi.register(testData);
      console.log("🌐 Backend response:", result);
      
      addResult("Backend Connection Test", {
        success: result.success,
        message: result.success ? "✅ Backend is responding!" : "❌ Backend connection failed",
        details: result,
        apiUrl: "https://afghansufra.luilala.com/api/auth/register/"
      });
      
    } catch (error) {
      console.error("🌐 Backend test error:", error);
      addResult("Backend Connection Test", {
        success: false,
        message: "❌ Network error or backend unavailable",
        error: error.message,
        apiUrl: "https://afghansufra.luilala.com/api/auth/register/"
      });
    } finally {
      setLoading(false);
    }
  };

  const testRealRegistration = async () => {
    setLoading(true);
    try {
      const email = prompt("Enter your real email address to receive OTP:");
      if (!email) return;

      const testData = {
        name: "Real Test User",
        user_name: "realtest" + Date.now(),
        email: email,
        password: "123456",
        phone: "+1234567890",
        role: "customer"
      };

      console.log("📧 Registering with real email:", email);
      const result = await authApi.register(testData);
      
      addResult("Real Registration Test", {
        success: result.success,
        message: result.success ? 
          `✅ Registration successful! Check ${email} for OTP` : 
          "❌ Registration failed",
        details: result,
        email: email
      });
      
    } catch (error) {
      addResult("Real Registration Test", {
        success: false,
        message: "❌ Registration error",
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => setResults([]);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">🌐 Backend API Test</h1>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h2 className="font-semibold text-blue-800 mb-2">API Configuration:</h2>
        <p className="text-sm text-blue-700">
          <strong>Base URL:</strong> https://afghansufra.luilala.com/api<br/>
          <strong>Registration:</strong> POST /auth/register/<br/>
          <strong>Login:</strong> POST /auth/login/<br/>
          <strong>Email Verification:</strong> POST /auth/verify-email/
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <button
          onClick={testBackendConnection}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          🌐 Test Backend Connection
        </button>
        
        <button
          onClick={testRealRegistration}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          📧 Test Real Registration (with your email)
        </button>
      </div>

      <div className="flex gap-4 mb-6">
        <button
          onClick={clearResults}
          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
        >
          Clear Results
        </button>
      </div>

      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="mt-2">Testing backend...</p>
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test Results:</h2>
        {results.length === 0 ? (
          <p className="text-gray-500">No tests run yet. Click a test button above.</p>
        ) : (
          results.map((item, index) => (
            <div key={index} className={`border rounded p-4 ${item.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-semibold">{item.test}</h3>
                <span className="text-sm text-gray-500">{item.timestamp}</span>
              </div>
              <div className="mb-2">
                <span className={`font-semibold ${item.success ? 'text-green-700' : 'text-red-700'}`}>
                  {item.result.message}
                </span>
              </div>
              <details className="text-sm">
                <summary className="cursor-pointer">View Details</summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto text-xs">
                  {JSON.stringify(item.result, null, 2)}
                </pre>
              </details>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default BackendTest;
