#!/usr/bin/env python3
"""
Create a saved address for the current user
"""

import os
import sys
import django

# Add the Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from restaurant.models import Address

User = get_user_model()

def create_saved_address():
    """Create a saved address for all customer users"""
    
    print("🏠 Creating Saved Addresses for All Customers")
    print("=" * 50)
    
    # Get all customer users
    customers = User.objects.filter(role='customer')
    print(f"Found {customers.count()} customers")
    
    for customer in customers:
        print(f"\nProcessing customer: {customer.name} ({customer.email})")
        
        # Check if customer already has addresses
        existing_addresses = Address.objects.filter(user=customer)
        print(f"  Existing addresses: {existing_addresses.count()}")
        
        if existing_addresses.exists():
            print(f"  ✅ Customer already has addresses:")
            for addr in existing_addresses:
                print(f"    - {addr.id}: {addr.street}, {addr.city}")
        else:
            # Create a saved address for this customer
            try:
                address = Address.objects.create(
                    user=customer,
                    street="123 Saved Address Street",
                    city="Kabul",
                    state="Kabul Province",
                    postal_code="1001",
                    country="Afghanistan",
                    latitude=34.5553,
                    longitude=69.2075
                )
                print(f"  ✅ Created new saved address: {address.id}")
            except Exception as e:
                print(f"  ❌ Error creating address: {e}")
    
    print("\n🎯 CHECKOUT INSTRUCTIONS:")
    print("1. Login as any customer")
    print("2. Go to checkout: http://localhost:5174/checkout")
    print("3. Select one of the saved addresses")
    print("4. The Place Order button should be enabled")
    
    # Create JavaScript to help select saved address
    print("\n📋 BROWSER CONSOLE FIX:")
    print("Copy and paste this in browser console (F12):")
    print("""
// Select saved address and enable button
function fixCheckout() {
  // Get all address cards
  const addressCards = document.querySelectorAll('[role="radio"]');
  console.log(`Found ${addressCards.length} address cards`);
  
  // Click the first address card to select it
  if (addressCards.length > 0) {
    addressCards[0].click();
    console.log('✅ Selected first address');
  }
  
  // Wait a bit and enable the button
  setTimeout(() => {
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
      if (button.textContent.includes('Place Order')) {
        button.disabled = false;
        console.log('✅ Enabled Place Order button');
      }
    });
  }, 500);
}

// Run the fix
fixCheckout();
    """)

if __name__ == '__main__':
    create_saved_address()
