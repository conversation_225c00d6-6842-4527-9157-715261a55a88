import React from 'react';
import { SkeletonCard, SkeletonRectangle, SkeletonText, SkeletonCircle, SkeletonButton } from './SkeletonBase';

const OrderCardSkeleton = ({ className, variant = 'default' }) => {
  if (variant === 'compact') {
    return (
      <SkeletonCard className={className}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <SkeletonRectangle width="w-12" height="h-12" rounded="lg" />
            <div className="space-y-1">
              <SkeletonText lineHeight="h-4" className="w-32" />
              <SkeletonText lineHeight="h-3" className="w-24" />
            </div>
          </div>
          <div className="text-right space-y-1">
            <SkeletonRectangle width="w-16" height="h-5" rounded="full" />
            <SkeletonText lineHeight="h-3" className="w-12" />
          </div>
        </div>
      </SkeletonCard>
    );
  }

  return (
    <SkeletonCard className={className}>
      {/* Order Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="space-y-2">
          <SkeletonText lineHeight="h-5" className="w-48" />
          <SkeletonText lineHeight="h-4" className="w-32" />
        </div>
        <SkeletonRectangle width="w-20" height="h-6" rounded="full" />
      </div>

      {/* Order Items */}
      <div className="space-y-3 mb-4">
        {Array.from({ length: 2 }).map((_, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <SkeletonRectangle width="w-10" height="h-10" rounded="lg" />
              <div className="space-y-1">
                <SkeletonText lineHeight="h-4" className="w-28" />
                <SkeletonText lineHeight="h-3" className="w-16" />
              </div>
            </div>
            <SkeletonText lineHeight="h-4" className="w-12" />
          </div>
        ))}
      </div>

      {/* Order Total */}
      <div className="border-t pt-3 mb-4">
        <div className="flex items-center justify-between">
          <SkeletonText lineHeight="h-5" className="w-16" />
          <SkeletonText lineHeight="h-5" className="w-20" />
        </div>
      </div>

      {/* Order Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <SkeletonCircle size="w-4 h-4" />
          <SkeletonText lineHeight="h-4" className="w-24" />
        </div>
        <div className="flex space-x-2">
          <SkeletonButton size="small" />
          <SkeletonButton size="small" />
        </div>
      </div>
    </SkeletonCard>
  );
};

// Order List Skeleton
export const OrderListSkeleton = ({ count = 3, variant = 'default', className }) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <OrderCardSkeleton key={index} variant={variant} />
      ))}
    </div>
  );
};

// Order Tracking Skeleton
export const OrderTrackingSkeleton = ({ className }) => {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Order Status */}
      <SkeletonCard>
        <div className="text-center space-y-4">
          <SkeletonCircle size="w-16 h-16" className="mx-auto" />
          <SkeletonText lineHeight="h-6" className="w-48 mx-auto" />
          <SkeletonText lineHeight="h-4" className="w-32 mx-auto" />
        </div>
      </SkeletonCard>

      {/* Progress Steps */}
      <SkeletonCard>
        <div className="space-y-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-4">
              <SkeletonCircle size="w-8 h-8" />
              <div className="flex-1 space-y-1">
                <SkeletonText lineHeight="h-4" className="w-32" />
                <SkeletonText lineHeight="h-3" className="w-24" />
              </div>
              <SkeletonText lineHeight="h-3" className="w-16" />
            </div>
          ))}
        </div>
      </SkeletonCard>

      {/* Delivery Info */}
      <SkeletonCard>
        <div className="space-y-4">
          <SkeletonText lineHeight="h-5" className="w-32" />
          <div className="flex items-center space-x-3">
            <SkeletonCircle size="w-12 h-12" />
            <div className="space-y-1">
              <SkeletonText lineHeight="h-4" className="w-28" />
              <SkeletonText lineHeight="h-3" className="w-20" />
            </div>
          </div>
          <div className="flex space-x-2">
            <SkeletonButton size="small" />
            <SkeletonButton size="small" />
          </div>
        </div>
      </SkeletonCard>
    </div>
  );
};

export default OrderCardSkeleton;
