#!/usr/bin/env python3
"""
Test OTP resend functionality
"""

import requests
import json
import time

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_otp_resend():
    """Test OTP resend functionality"""
    print("🔄 Testing OTP Resend Functionality...")
    
    # First, register a new user
    test_user = {
        "name": "Test Resend User",
        "user_name": f"testresend_{int(time.time())}",
        "email": f"testresend{int(time.time())}@example.com",
        "phone": f"+987654{int(time.time()) % 10000}",
        "password": "TestPassword123",
        "confirm_password": "TestPassword123",
        "role": "customer"
    }
    
    print(f"📝 Registering user: {test_user['email']}")
    
    # Register user
    response = requests.post(
        f"{API_BASE_URL}/auth/register/",
        headers=HEADERS,
        data=json.dumps(test_user)
    )
    
    if response.status_code != 201:
        print(f"❌ Registration failed: {response.text}")
        return
    
    result = response.json()
    email = result['data']['email']
    print(f"✅ User registered: {email}")
    
    # Wait a moment
    time.sleep(2)
    
    # Test resend OTP
    print(f"\n🔄 Testing OTP resend for {email}...")
    
    resend_data = {"email": email}
    
    response = requests.post(
        f"{API_BASE_URL}/auth/resend-otp/",
        headers=HEADERS,
        data=json.dumps(resend_data)
    )
    
    print(f"📡 Resend Response Status: {response.status_code}")
    print(f"📄 Resend Response: {response.text}")
    
    if response.status_code == 200:
        print("✅ OTP resend successful!")
        print("📬 Check Django console for the new OTP...")
        return True
    else:
        print("❌ OTP resend failed")
        return False

if __name__ == "__main__":
    test_otp_resend()
