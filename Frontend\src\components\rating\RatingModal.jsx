import React, { useState, useEffect } from "react";
import { ratingApi } from "../../utils/ratingApi";
import { Star, X, CheckCircle, AlertCircle } from "lucide-react";
import "./RatingModal.css";

const RatingModal = ({ isOpen, onClose, order, onRatingSubmitted }) => {
  const [ratings, setRatings] = useState({
    food_rating: 0,
    delivery_rating: 0,
    overall_rating: 0,
  });
  const [reviewText, setReviewText] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [canRate, setCanRate] = useState(null);
  const [hoveredStar, setHoveredStar] = useState({ category: null, rating: 0 });

  // Check if user can rate this order when modal opens
  useEffect(() => {
    if (isOpen && order) {
      checkCanRate();
    }
  }, [isOpen, order]);

  const checkCanRate = async () => {
    try {
      const result = await ratingApi.canRateOrder(order.id);
      setCanRate(result);
      if (!result.can_rate) {
        setError(result.reason);
      }
    } catch (err) {
      console.error("Error checking rating eligibility:", err);
      setError("Unable to verify rating eligibility");
    }
  };

  const handleStarClick = (category, rating) => {
    setRatings((prev) => ({
      ...prev,
      [category]: rating,
    }));
    setError(null); // Clear any previous errors
  };

  const handleStarHover = (category, rating) => {
    setHoveredStar({ category, rating });
  };

  const handleStarLeave = () => {
    setHoveredStar({ category: null, rating: 0 });
  };

  const renderStars = (category, currentRating) => {
    const displayRating =
      hoveredStar.category === category ? hoveredStar.rating : currentRating;

    return (
      <div className='flex items-center space-x-1'>
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type='button'
            className={`transition-all duration-200 hover:scale-110 ${
              star <= displayRating ? "text-yellow-400" : "text-gray-300"
            }`}
            onClick={() => handleStarClick(category, star)}
            onMouseEnter={() => handleStarHover(category, star)}
            onMouseLeave={handleStarLeave}
          >
            <Star
              size={24}
              className={star <= displayRating ? "fill-current" : ""}
            />
          </button>
        ))}
        <span className='ml-2 text-sm text-gray-600 min-w-[60px]'>
          {displayRating > 0 && `${displayRating}/5`}
        </span>
      </div>
    );
  };

  const getRatingLabel = (rating) => {
    const labels = {
      1: "Poor",
      2: "Fair",
      3: "Good",
      4: "Very Good",
      5: "Excellent",
    };
    return labels[rating] || "";
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (
      ratings.food_rating === 0 ||
      ratings.delivery_rating === 0 ||
      ratings.overall_rating === 0
    ) {
      setError("Please provide ratings for all categories");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const ratingData = {
        order_id: order.id,
        food_rating: ratings.food_rating,
        delivery_rating: ratings.delivery_rating,
        overall_rating: ratings.overall_rating,
        review_text: reviewText.trim() || null,
      };

      await ratingApi.createRating(ratingData);

      // Show success message
      setSuccess(true);

      // Wait a moment then close
      setTimeout(() => {
        // Call the callback to notify parent component
        if (onRatingSubmitted) {
          onRatingSubmitted();
        }

        // Close modal and reset
        handleClose();
      }, 1500);
    } catch (error) {
      console.error("Error submitting rating:", error);
      setError(
        error.response?.data?.error ||
          "Failed to submit rating. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    // Reset all state
    setRatings({
      food_rating: 0,
      delivery_rating: 0,
      overall_rating: 0,
    });
    setReviewText("");
    setError(null);
    setSuccess(false);
    setCanRate(null);
    setHoveredStar({ category: null, rating: 0 });
    onClose();
  };

  if (!isOpen || !order) return null;

  // Show success state
  if (success) {
    return (
      <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
        <div className='bg-white rounded-lg p-8 max-w-md w-full mx-4 text-center'>
          <CheckCircle className='mx-auto mb-4 text-green-500' size={64} />
          <h3 className='text-xl font-semibold text-gray-900 mb-2'>
            Thank You!
          </h3>
          <p className='text-gray-600'>
            Your rating has been submitted successfully.
          </p>
        </div>
      </div>
    );
  }

  // Show error state if can't rate
  if (canRate && !canRate.can_rate) {
    return (
      <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
        <div className='bg-white rounded-lg p-8 max-w-md w-full mx-4'>
          <div className='flex items-center justify-between mb-4'>
            <h3 className='text-lg font-semibold text-gray-900'>
              Unable to Rate
            </h3>
            <button
              onClick={handleClose}
              className='text-gray-400 hover:text-gray-600'
            >
              <X size={24} />
            </button>
          </div>
          <div className='flex items-center space-x-3 mb-4'>
            <AlertCircle className='text-yellow-500' size={24} />
            <p className='text-gray-700'>{canRate.reason}</p>
          </div>
          <button
            onClick={handleClose}
            className='w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700'
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto'>
        <div className='flex items-center justify-between p-6 border-b border-gray-200'>
          <h2 className='text-2xl font-bold text-gray-900'>Rate Your Order</h2>
          <button
            onClick={handleClose}
            className='text-gray-400 hover:text-gray-600'
          >
            <X size={24} />
          </button>
        </div>

        <div className='p-6'>
          {/* Order Information */}
          <div className='bg-gray-50 rounded-lg p-4 mb-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-2'>
              Order #{order.id}
            </h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600'>
              <div className='flex items-center space-x-2'>
                <span>🏪</span>
                <span>{order.restaurant?.name || "Restaurant"}</span>
              </div>
              <div className='flex items-center space-x-2'>
                <span>💰</span>
                <span>Total: ${order.total_amount}</span>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className='space-y-6'>
            {error && (
              <div className='bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-3'>
                <AlertCircle className='text-red-500' size={20} />
                <span className='text-red-800'>{error}</span>
              </div>
            )}

            {/* Food Quality Rating */}
            <div className='bg-white border border-gray-200 rounded-lg p-4'>
              <div className='flex items-center space-x-2 mb-2'>
                <span className='text-2xl'>🍽️</span>
                <h4 className='text-lg font-semibold text-gray-900'>
                  Food Quality
                </h4>
              </div>
              <p className='text-gray-600 mb-4'>
                How was the taste and quality of your food?
              </p>
              {renderStars("food_rating", ratings.food_rating)}
              {ratings.food_rating > 0 && (
                <p className='text-sm text-gray-500 mt-2'>
                  {getRatingLabel(ratings.food_rating)} ({ratings.food_rating}
                  /5)
                </p>
              )}
            </div>

            {/* Delivery Service Rating */}
            <div className='bg-white border border-gray-200 rounded-lg p-4'>
              <div className='flex items-center space-x-2 mb-2'>
                <span className='text-2xl'>🚚</span>
                <h4 className='text-lg font-semibold text-gray-900'>
                  Delivery Service
                </h4>
              </div>
              <p className='text-gray-600 mb-4'>
                How was the delivery speed and service?
              </p>
              {renderStars("delivery_rating", ratings.delivery_rating)}
              {ratings.delivery_rating > 0 && (
                <p className='text-sm text-gray-500 mt-2'>
                  {getRatingLabel(ratings.delivery_rating)} (
                  {ratings.delivery_rating}/5)
                </p>
              )}
            </div>

            {/* Overall Experience Rating */}
            <div className='bg-white border border-gray-200 rounded-lg p-4'>
              <div className='flex items-center space-x-2 mb-2'>
                <span className='text-2xl'>⭐</span>
                <h4 className='text-lg font-semibold text-gray-900'>
                  Overall Experience
                </h4>
              </div>
              <p className='text-gray-600 mb-4'>
                How would you rate your overall experience?
              </p>
              {renderStars("overall_rating", ratings.overall_rating)}
              {ratings.overall_rating > 0 && (
                <p className='text-sm text-gray-500 mt-2'>
                  {getRatingLabel(ratings.overall_rating)} (
                  {ratings.overall_rating}/5)
                </p>
              )}
            </div>

            {/* Review Section */}
            <div className='bg-white border border-gray-200 rounded-lg p-4'>
              <div className='flex items-center space-x-2 mb-2'>
                <span className='text-2xl'>💬</span>
                <h4 className='text-lg font-semibold text-gray-900'>
                  Write a Review
                </h4>
                <span className='text-sm text-gray-500'>(Optional)</span>
              </div>
              <textarea
                value={reviewText}
                onChange={(e) => setReviewText(e.target.value)}
                placeholder='Share your experience with other customers...'
                maxLength={1000}
                rows={4}
                className='w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none'
              />
              <div className='flex justify-between items-center mt-2'>
                <span className='text-sm text-gray-500'>
                  {reviewText.length}/1000 characters
                </span>
                {reviewText.length > 900 && (
                  <span className='text-sm text-orange-500'>
                    {1000 - reviewText.length} characters remaining
                  </span>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className='flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200'>
              <button
                type='button'
                onClick={handleClose}
                className='flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors'
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type='submit'
                className='flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2'
                disabled={
                  isSubmitting ||
                  ratings.food_rating === 0 ||
                  ratings.delivery_rating === 0 ||
                  ratings.overall_rating === 0
                }
              >
                {isSubmitting ? (
                  <>
                    <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-white'></div>
                    <span>Submitting...</span>
                  </>
                ) : (
                  <>
                    <Star size={16} className='fill-current' />
                    <span>Submit Rating</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default RatingModal;
