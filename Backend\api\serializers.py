# api/serializers.py
from rest_framework import serializers
from django.contrib.auth import authenticate
from django.core.validators import RegexValidator
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError as DjangoValidationError
from django.contrib.auth.password_validation import validate_password

from rest_framework import serializers
from users.models import OTPVerification, User

import pyotp
import re


class UserLoginSerializer(serializers.Serializer):
    user_name = serializers.CharField(
        write_only=True,
        error_messages={
            'required': 'Username is required.',
            'blank': 'Username cannot be empty.'
        }
    )
    password = serializers.CharField(
        write_only=True,
        error_messages={
            'required': 'Password is required.',
            'blank': 'Password cannot be empty.'
        }
    )

    def validate_user_name(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError('Username is required.')

        # Check if username contains only valid characters (allow + for phone numbers)
        if not re.match(r'^[a-zA-Z0-9_.+-]+$', value):
            raise serializers.ValidationError('Username can only contain letters, numbers, dots, hyphens, underscores, and plus signs.')

        return value.strip()

    def validate_password(self, value):
        if not value:
            raise serializers.ValidationError('Password is required.')

        if len(value) < 6:
            raise serializers.ValidationError('Password must be at least 6 characters long.')

        return value

    def validate(self, data):
        user_name = data.get('user_name')
        password = data.get('password')

        # Check if user exists
        try:
            user_obj = User.objects.get(user_name=user_name)
        except User.DoesNotExist:
            raise serializers.ValidationError({
                'user_name': 'No account found with this username.'
            })

        # Check if user is verified
        if not user_obj.is_verified:
            raise serializers.ValidationError({
                'non_field_errors': 'Please verify your email address before logging in.'
            })

        # Authenticate user
        user = authenticate(user_name=user_name, password=password)
        if not user:
            raise serializers.ValidationError({
                'password': 'Incorrect password.'
            })

        # Check if user account is active
        if not user.is_active:
            raise serializers.ValidationError({
                'non_field_errors': 'Your account has been deactivated. Please contact support.'
            })

        data['user'] = user
        return data
    




class EmailVerificationSerializer(serializers.Serializer):
    email = serializers.EmailField(
        error_messages={
            'required': 'Email address is required.',
            'invalid': 'Please enter a valid email address.',
            'blank': 'Email address cannot be empty.'
        }
    )
    otp = serializers.CharField(
        max_length=6,
        min_length=6,
        error_messages={
            'required': 'OTP code is required.',
            'max_length': 'OTP must be exactly 6 digits.',
            'min_length': 'OTP must be exactly 6 digits.',
            'blank': 'OTP code cannot be empty.'
        }
    )

    def validate_email(self, value):
        if not value:
            raise serializers.ValidationError('Email address is required.')

        return value.lower()

    def validate_otp(self, value):
        if not value:
            raise serializers.ValidationError('OTP code is required.')

        # Check if OTP contains only digits
        if not value.isdigit():
            raise serializers.ValidationError('OTP must contain only numbers.')

        if len(value) != 6:
            raise serializers.ValidationError('OTP must be exactly 6 digits.')

        return value

    def validate(self, data):
        email = data.get('email')
        otp = data.get('otp')

        # Check if user exists
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise serializers.ValidationError({
                'email': 'No account found with this email address.'
            })

        # Check if user is already verified
        if user.is_verified:
            raise serializers.ValidationError({
                'non_field_errors': 'This account is already verified.'
            })

        # Check for valid OTP
        otp_record = OTPVerification.objects.filter(
            user=user,
            otp=otp,
            purpose='registration',
            is_used=False
        ).first()

        if not otp_record:
            raise serializers.ValidationError({
                'otp': 'Invalid OTP code. Please check and try again.'
            })

        if not otp_record.is_valid():
            raise serializers.ValidationError({
                'otp': 'OTP code has expired. Please request a new one.'
            })

        data['user'] = user
        data['otp_record'] = otp_record
        return data

class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(
        write_only=True,
        min_length=8,
        error_messages={
            'required': 'Password is required.',
            'min_length': 'Password must be at least 8 characters long.',
            'blank': 'Password cannot be empty.'
        }
    )
    confirm_password = serializers.CharField(write_only=True)
    phone = serializers.CharField(
        max_length=15,
        validators=[RegexValidator(
            regex=r'^\+?[1-9]\d{1,14}$',
            message="Phone number must be in international format (e.g., +1234567890)."
        )],
        error_messages={
            'required': 'Phone number is required.',
            'blank': 'Phone number cannot be empty.'
        }
    )
    email = serializers.EmailField(
        required=True,
        error_messages={
            'required': 'Email address is required.',
            'invalid': 'Please enter a valid email address.',
            'blank': 'Email address cannot be empty.'
        }
    )
    name = serializers.CharField(
        max_length=100,
        error_messages={
            'required': 'Full name is required.',
            'max_length': 'Name cannot exceed 100 characters.',
            'blank': 'Name cannot be empty.'
        }
    )
    user_name = serializers.CharField(
        max_length=100,
        error_messages={
            'required': 'Username is required.',
            'max_length': 'Username cannot exceed 100 characters.',
            'blank': 'Username cannot be empty.'
        }
    )
    role = serializers.ChoiceField(
        choices=[('customer', 'Customer'), ('restaurant', 'Restaurant')],
        default='customer',
        error_messages={
            'invalid_choice': 'Please select a valid role.'
        }
    )

    class Meta:
        model = User
        fields = ['name', 'user_name', 'phone', 'password', 'confirm_password', 'role', 'email']

    def validate_name(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError('Full name is required.')

        # Check if name contains only letters, numbers, and spaces (more flexible for test names)
        if not re.match(r'^[a-zA-Z0-9\s]+$', value.strip()):
            raise serializers.ValidationError('Name can only contain letters, numbers, and spaces.')

        if len(value.strip()) < 2:
            raise serializers.ValidationError('Name must be at least 2 characters long.')

        return value.strip()

    def validate_user_name(self, value):
        if not value or not value.strip():
            raise serializers.ValidationError('Username is required.')

        # Check if username contains only valid characters (allow + for phone numbers)
        if not re.match(r'^[a-zA-Z0-9_.+-]+$', value):
            raise serializers.ValidationError('Username can only contain letters, numbers, dots, hyphens, underscores, and plus signs.')

        if len(value) < 3:
            raise serializers.ValidationError('Username must be at least 3 characters long.')

        # Check if username already exists
        if User.objects.filter(user_name=value).exists():
            raise serializers.ValidationError('This username is already taken.')

        return value.strip()

    def validate_email(self, value):
        if not value:
            raise serializers.ValidationError('Email address is required.')

        # Check if email already exists
        if User.objects.filter(email=value.lower()).exists():
            raise serializers.ValidationError('An account with this email already exists.')

        return value.lower()

    def validate_phone(self, value):
        if not value:
            raise serializers.ValidationError('Phone number is required.')

        # Check if phone already exists
        if User.objects.filter(phone=value).exists():
            raise serializers.ValidationError('An account with this phone number already exists.')

        return value

    def validate_password(self, value):
        if not value:
            raise serializers.ValidationError('Password is required.')

        # Use Django's built-in password validation
        try:
            validate_password(value)
        except DjangoValidationError as e:
            raise serializers.ValidationError(list(e.messages))

        # Additional custom validations
        if len(value) < 8:
            raise serializers.ValidationError('Password must be at least 8 characters long.')

        if not re.search(r'[A-Za-z]', value):
            raise serializers.ValidationError('Password must contain at least one letter.')

        if not re.search(r'\d', value):
            raise serializers.ValidationError('Password must contain at least one number.')

        return value

    def validate_role(self, value):
        # Prevent admin and delivery_agent role registration
        if value == 'admin':
            raise serializers.ValidationError('Admin role registration is not allowed.')

        if value == 'delivery_agent':
            raise serializers.ValidationError('Delivery agent accounts are created by admin only. Please contact support.')

        return value

    def validate(self, data):
        # Check if passwords match
        password = data.get('password')
        confirm_password = data.get('confirm_password')

        if password != confirm_password:
            raise serializers.ValidationError({
                'confirm_password': 'Passwords do not match.'
            })

        return data

    def create(self, validated_data):
        # Remove confirm_password from validated_data
        validated_data.pop('confirm_password', None)

        try:
            user = User.objects.create_user(
                phone=validated_data['phone'],
                name=validated_data['name'],
                user_name=validated_data['user_name'],
                password=validated_data['password'],
                role=validated_data.get('role', 'customer'),
                email=validated_data['email'],
                is_verified=False,  # User is not verified until OTP is confirmed
                is_staff=False,  # Explicitly set to False
                is_superuser=False  # Explicitly set to False
            )

            # Generate and send OTP
            otp_record = OTPVerification.generate_otp(user, 'registration')
            otp_record.send_otp_email()

            return user
        except Exception as e:
            raise serializers.ValidationError({
                'non_field_errors': 'Failed to create account. Please try again.'
            })


class PasswordChangeSerializer(serializers.Serializer):
    email = serializers.EmailField(
        error_messages={
            'required': 'Email address is required.',
            'invalid': 'Please enter a valid email address.',
            'blank': 'Email address cannot be empty.'
        }
    )
    otp = serializers.CharField(
        max_length=6,
        min_length=6,
        error_messages={
            'required': 'OTP code is required.',
            'max_length': 'OTP must be exactly 6 digits.',
            'min_length': 'OTP must be exactly 6 digits.',
            'blank': 'OTP code cannot be empty.'
        }
    )
    new_password = serializers.CharField(
        write_only=True,
        min_length=8,
        error_messages={
            'required': 'New password is required.',
            'min_length': 'Password must be at least 8 characters long.',
            'blank': 'Password cannot be empty.'
        }
    )
    confirm_password = serializers.CharField(write_only=True)

    def validate_email(self, value):
        if not value:
            raise serializers.ValidationError('Email address is required.')

        return value.lower()

    def validate_otp(self, value):
        if not value:
            raise serializers.ValidationError('OTP code is required.')

        # Check if OTP contains only digits
        if not value.isdigit():
            raise serializers.ValidationError('OTP must contain only numbers.')

        if len(value) != 6:
            raise serializers.ValidationError('OTP must be exactly 6 digits.')

        return value

    def validate_new_password(self, value):
        if not value:
            raise serializers.ValidationError('New password is required.')

        # Use Django's built-in password validation
        try:
            validate_password(value)
        except DjangoValidationError as e:
            raise serializers.ValidationError(list(e.messages))

        # Additional custom validations
        if len(value) < 8:
            raise serializers.ValidationError('Password must be at least 8 characters long.')

        if not re.search(r'[A-Za-z]', value):
            raise serializers.ValidationError('Password must contain at least one letter.')

        if not re.search(r'\d', value):
            raise serializers.ValidationError('Password must contain at least one number.')

        return value

    def validate(self, data):
        email = data.get('email')
        otp = data.get('otp')
        new_password = data.get('new_password')
        confirm_password = data.get('confirm_password')

        # Check if passwords match
        if new_password != confirm_password:
            raise serializers.ValidationError({
                'confirm_password': 'Passwords do not match.'
            })

        # Check if user exists
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise serializers.ValidationError({
                'email': 'No account found with this email address.'
            })

        # Check for valid OTP
        otp_record = OTPVerification.objects.filter(
            user=user,
            otp=otp,
            purpose='password_reset',
            is_used=False
        ).first()

        if not otp_record:
            raise serializers.ValidationError({
                'otp': 'Invalid OTP code. Please check and try again.'
            })

        if not otp_record.is_valid():
            raise serializers.ValidationError({
                'otp': 'OTP code has expired. Please request a new one.'
            })

        data['user'] = user
        data['otp_record'] = otp_record
        return data

class PasswordChangeSerializer(serializers.Serializer):
    email = serializers.EmailField()
    old_password = serializers.CharField(write_only=True)
    new_password = serializers.CharField(write_only=True)
    otp = serializers.CharField(max_length=6)

    def validate(self, data):
        email = data.get('email')
        old_password = data.get('old_password')
        new_password = data.get('new_password')
        otp = data.get('otp')

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise serializers.ValidationError('User with this email does not exist')

        if not user.check_password(old_password):
            raise serializers.ValidationError('Incorrect old password')

        # Check for valid OTP
        otp_record = OTPVerification.objects.filter(
            user=user,
            otp=otp,
            purpose='password_reset',
            is_used=False
        ).first()

        if not otp_record or not otp_record.is_valid():
            raise serializers.ValidationError('Invalid or expired OTP')

        data['user'] = user
        data['otp_record'] = otp_record
        return data
    

