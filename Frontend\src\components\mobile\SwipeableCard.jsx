import React, { useState } from "react";
import { Heart, Trash2, <PERSON>hare2, <PERSON>, MoreHorizontal } from "lucide-react";
import { useSwipeableCard } from "../../hooks/useTouch";
import { cn } from "../../utils/cn";
import Card from "../common/Card";

const SwipeableCard = ({
  children,
  onSwipeLeft,
  onSwipeRight,
  leftAction,
  rightAction,
  threshold = 100,
  className = "",
  disabled = false,
  ...props
}) => {
  const [showActions, setShowActions] = useState(false);

  const {
    touchHandlers,
    cardStyle,
    offset,
    isDragging,
    isSwipedLeft,
    isSwipedRight,
  } = useSwipeableCard({
    onSwipeLeft: () => {
      onSwipeLeft?.();
      setShowActions(false);
    },
    onSwipeRight: () => {
      onSwipeRight?.();
      setShowActions(false);
    },
    threshold,
  });

  if (disabled) {
    return (
      <Card className={className} {...props}>
        {children}
      </Card>
    );
  }

  return (
    <div className='relative overflow-hidden'>
      {/* Left action (revealed when swiping right) */}
      {rightAction && (
        <div
          className={cn(
            "absolute left-0 top-0 bottom-0 flex items-center justify-center transition-all duration-200",
            rightAction.className || "bg-green-500 text-white"
          )}
          style={{
            width: `${Math.max(0, offset)}px`,
            opacity: offset > 20 ? 1 : 0,
          }}
        >
          <div className='flex items-center space-x-2 px-4'>
            {rightAction.icon}
            {offset > 60 && (
              <span className='text-sm font-medium whitespace-nowrap'>
                {rightAction.label}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Right action (revealed when swiping left) */}
      {leftAction && (
        <div
          className={cn(
            "absolute right-0 top-0 bottom-0 flex items-center justify-center transition-all duration-200",
            leftAction.className || "bg-red-500 text-white"
          )}
          style={{
            width: `${Math.max(0, -offset)}px`,
            opacity: offset < -20 ? 1 : 0,
          }}
        >
          <div className='flex items-center space-x-2 px-4'>
            {leftAction.icon}
            {offset < -60 && (
              <span className='text-sm font-medium whitespace-nowrap'>
                {leftAction.label}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Main card */}
      <Card
        className={cn(
          "relative z-10 transition-shadow duration-200",
          isDragging && "shadow-lg",
          isSwipedLeft && leftAction && "shadow-red-200",
          isSwipedRight && rightAction && "shadow-green-200",
          className
        )}
        style={cardStyle}
        {...touchHandlers}
        {...props}
      >
        {children}
      </Card>
    </div>
  );
};

// Predefined swipeable order card
export const SwipeableOrderCard = ({
  order,
  onFavorite,
  onDelete,
  onShare,
  className = "",
  ...props
}) => {
  return (
    <SwipeableCard
      leftAction={{
        icon: <Trash2 size={20} />,
        label: "Delete",
        className: "bg-red-500 text-white",
      }}
      rightAction={{
        icon: <Heart size={20} />,
        label: "Favorite",
        className: "bg-green-500 text-white",
      }}
      onSwipeLeft={() => onDelete?.(order)}
      onSwipeRight={() => onFavorite?.(order)}
      className={className}
      {...props}
    >
      <div className='flex items-center justify-between p-4'>
        <div className='flex items-center space-x-3'>
          <img
            src={order.restaurant?.logo || "/placeholder-restaurant.jpg"}
            alt={order.restaurant?.name || order.restaurantName}
            className='w-12 h-12 rounded-lg object-cover'
          />
          <div>
            <h3 className='font-medium'>
              {order.restaurant?.name || order.restaurantName || "Restaurant"}
            </h3>
            <p className='text-sm text-gray-600'>
              {(order.items || order.orderItems || []).length} items • $
              {Number(
                order.total_amount || order.totalAmount || order.total || 0
              ).toFixed(2)}
            </p>
            <p className='text-xs text-gray-500'>
              {new Date(
                order.created_at || order.createdAt
              ).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className='text-right'>
          <div
            className={cn(
              "px-2 py-1 rounded-full text-xs font-medium",
              order.status === "delivered" && "bg-green-100 text-green-800",
              order.status === "pending" && "bg-yellow-100 text-yellow-800",
              order.status === "cancelled" && "bg-red-100 text-red-800"
            )}
          >
            {order.status}
          </div>
          <button
            onClick={() => onShare?.(order)}
            className='mt-2 p-1 text-gray-400 hover:text-gray-600'
          >
            <Share2 size={16} />
          </button>
        </div>
      </div>
    </SwipeableCard>
  );
};

// Predefined swipeable restaurant card
export const SwipeableRestaurantCard = ({
  restaurant,
  onFavorite,
  onShare,
  className = "",
  ...props
}) => {
  return (
    <SwipeableCard
      rightAction={{
        icon: <Heart size={20} />,
        label: "Favorite",
        className: "bg-red-500 text-white",
      }}
      leftAction={{
        icon: <Share2 size={20} />,
        label: "Share",
        className: "bg-blue-500 text-white",
      }}
      onSwipeRight={() => onFavorite?.(restaurant)}
      onSwipeLeft={() => onShare?.(restaurant)}
      className={className}
      {...props}
    >
      <div className='flex items-center space-x-4 p-4'>
        <img
          src={restaurant.logo}
          alt={restaurant.name}
          className='w-16 h-16 rounded-lg object-cover'
        />

        <div className='flex-1'>
          <h3 className='font-semibold text-lg'>{restaurant.name}</h3>
          <div className='flex items-center mt-1'>
            <Star size={16} className='text-yellow-500 mr-1' />
            <span className='text-sm'>{restaurant.rating}</span>
            <span className='text-sm text-gray-500 ml-2'>
              {restaurant.cuisine?.join(", ")}
            </span>
          </div>
          <div className='flex items-center justify-between mt-2 text-sm text-gray-600'>
            <span>{restaurant.deliveryTime}</span>
            <span>${restaurant.deliveryFee} delivery</span>
          </div>
        </div>
      </div>
    </SwipeableCard>
  );
};

export default SwipeableCard;
