import React, { useState, useEffect } from "react";
import {
  TruckIcon,
  MapPin,
  Clock,
  DollarSign,
  Star,
  Package,
  Activity,
  Users,
  Navigation,
  Play,
  Square,
  RefreshCw,
  Bell,
  Settings,
  Eye,
  CheckCircle,
  AlertCircle,
  Zap,
  Target,
  TrendingUp,
  Calendar,
  Timer,
  Route,
  Award,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import { useRealTime } from "../../context/RealTimeContext";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import Badge from "../../components/common/Badge";
import InAppNotifications from "../../components/delivery/InAppNotifications";
import MobileDashboard from "../../components/delivery/MobileDashboard";
import PendingApprovalBanner from "../../components/delivery/PendingApprovalBanner";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";
import { useResponsive } from "../../hooks/useResponsive";

function NewDeliveryDashboard() {
  const { user } = useAuth();
  const { isMobile, isTablet } = useResponsive();
  const {
    isConnected,
    connectionStatus,
    isLocationTracking,
    currentLocation,
    startLocationTracking,
    stopLocationTracking,
    updateStatus,
    showNotification,
    inAppNotifications,
    removeInAppNotification,
  } = useRealTime();

  // State management
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isOnline, setIsOnline] = useState(false);
  const [currentShift, setCurrentShift] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [locationTrackingInterval, setLocationTrackingInterval] =
    useState(null);

  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await deliveryAgentApi.getDashboard();

      if (result.success) {
        setDashboardData(result.data);
        setIsOnline(result.data.agent_info?.is_online || false);
        setCurrentShift(result.data.current_shift);
      } else {
        setError(result.error?.message || "Failed to load dashboard data");
      }
    } catch (err) {
      console.error("Dashboard load error:", err);
      setError("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  // Refresh dashboard data
  const refreshDashboard = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  // Toggle online status
  const toggleOnlineStatus = async () => {
    try {
      const newStatus = !isOnline;
      const availability = newStatus ? "available" : "offline";

      const result = await deliveryAgentApi.updateAvailability({
        is_online: newStatus,
        availability: availability,
      });

      if (result.success) {
        setIsOnline(newStatus);

        // Update status via real-time
        updateStatus(availability, newStatus);

        // Show notification
        showNotification(
          newStatus ? "🟢 You are now online" : "🔴 You are now offline",
          {
            body: newStatus
              ? "You can now receive delivery orders"
              : "You will not receive new orders",
            tag: "status-update",
          }
        );

        await refreshDashboard();
      } else {
        setError(result.error?.message || "Failed to update status");
      }
    } catch (err) {
      console.error("Status update error:", err);
      setError("Failed to update status");
    }
  };

  // Start shift
  const startShift = async () => {
    try {
      const location = await deliveryAgentApi.getCurrentLocation();
      const result = await deliveryAgentApi.startShift({
        latitude: location.latitude,
        longitude: location.longitude,
        planned_duration_hours: 8.0,
      });

      if (result.success) {
        await refreshDashboard();

        // Start real-time location tracking using API
        try {
          const interval = deliveryAgentApi.startLocationTracking(
            (result) => {
              console.log("Location updated:", result);
            },
            30000 // Update every 30 seconds
          );
          setLocationTrackingInterval(interval);

          showNotification("🚀 Shift Started", {
            body: "Location tracking enabled. You're ready to receive orders!",
            tag: "shift-start",
          });
        } catch (error) {
          console.warn("Location tracking failed:", error);
          showNotification("⚠️ Shift Started", {
            body: "Shift started but location tracking is disabled. Enable location for better service.",
            tag: "shift-start",
          });
        }
      } else {
        setError(result.error?.message || "Failed to start shift");
      }
    } catch (err) {
      console.error("Start shift error:", err);
      setError("Failed to start shift");
    }
  };

  // End shift
  const endShift = async () => {
    try {
      const location = await deliveryAgentApi.getCurrentLocation();
      const result = await deliveryAgentApi.endShift({
        latitude: location.latitude,
        longitude: location.longitude,
        notes: "Shift ended from dashboard",
      });

      if (result.success) {
        await refreshDashboard();

        // Stop real-time location tracking
        if (locationTrackingInterval) {
          deliveryAgentApi.stopLocationTracking(locationTrackingInterval);
          setLocationTrackingInterval(null);
        }

        showNotification("🏁 Shift Ended", {
          body: `Great work! You earned $${
            result.data?.total_earnings?.toFixed(2) || "0.00"
          } this shift.`,
          tag: "shift-end",
        });
      } else {
        setError(result.error?.message || "Failed to end shift");
      }
    } catch (err) {
      console.error("End shift error:", err);
      setError("Failed to end shift");
    }
  };

  useEffect(() => {
    loadDashboardData();

    // Cleanup location tracking on unmount
    return () => {
      if (locationTrackingInterval) {
        deliveryAgentApi.stopLocationTracking(locationTrackingInterval);
      }
    };
  }, [user]);

  // Loading state
  if (loading) {
    return (
      <div className='min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-600 text-lg'>Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className='min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center'>
        <Card className='max-w-md w-full mx-4'>
          <div className='text-center p-6'>
            <AlertCircle className='h-16 w-16 text-red-500 mx-auto mb-4' />
            <h2 className='text-xl font-semibold text-red-600 mb-2'>
              Dashboard Error
            </h2>
            <p className='text-gray-600 mb-6'>{error}</p>
            <div className='space-y-3'>
              <Button onClick={refreshDashboard} className='w-full'>
                <RefreshCw className='h-4 w-4 mr-2' />
                Retry
              </Button>
              <Button
                variant='outline'
                onClick={() => window.location.reload()}
                className='w-full'
              >
                Reload Page
              </Button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // Authentication check
  if (!user) {
    return (
      <div className='min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center'>
        <Card className='max-w-md w-full mx-4'>
          <div className='text-center p-6'>
            <Users className='h-16 w-16 text-gray-400 mx-auto mb-4' />
            <h2 className='text-xl font-semibold text-gray-900 mb-2'>
              Authentication Required
            </h2>
            <p className='text-gray-600 mb-6'>
              Please log in to access the delivery dashboard.
            </p>
            <Button
              onClick={() => (window.location.href = "/auth/login")}
              className='w-full'
            >
              Go to Login
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // Render mobile dashboard for mobile devices
  if (isMobile) {
    return (
      <>
        {/* Approval Status Banner */}
        <div className='p-4'>
          <PendingApprovalBanner status={dashboardData?.agent_info?.status} />
        </div>

        <MobileDashboard
          dashboardData={dashboardData}
          isOnline={isOnline}
          currentShift={currentShift}
          isConnected={isConnected}
          isLocationTracking={locationTrackingInterval !== null}
          onToggleOnlineStatus={toggleOnlineStatus}
          onStartShift={startShift}
          onEndShift={endShift}
          onRefresh={refreshDashboard}
          refreshing={refreshing}
        />

        {/* In-App Notifications for Mobile */}
        <InAppNotifications
          notifications={inAppNotifications}
          onRemove={removeInAppNotification}
        />
      </>
    );
  }

  return (
    <div className='min-h-screen  '>
      {/* Approval Status Banner */}
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6'>
        <PendingApprovalBanner status={dashboardData?.agent_info?.status} />
      </div>

      {/* Header Section */}
      <div className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center py-4'>
            <div className='flex items-center space-x-4'>
              <div className='flex items-center space-x-3'>
                <div
                  className={`w-12 h-12 rounded-full flex items-center justify-center ${
                    isOnline ? "bg-green-100" : "bg-gray-100"
                  }`}
                >
                  <TruckIcon
                    className={`h-6 w-6 ${
                      isOnline ? "text-green-600" : "text-gray-400"
                    }`}
                  />
                </div>
                <div>
                  <h1 className='text-xl font-bold text-gray-900'>
                    {dashboardData?.agent_info?.name ||
                      user?.first_name ||
                      "Delivery Agent"}
                  </h1>
                  <div className='flex items-center space-x-2'>
                    <p className='text-sm text-gray-500'>
                      ID: {dashboardData?.agent_info?.agent_id || "AGENT-12345"}
                    </p>
                    {/* Real-time connection status */}
                    <div className='flex items-center space-x-1'>
                      <div
                        className={`w-2 h-2 rounded-full ${
                          isConnected ? "bg-green-500" : "bg-red-500"
                        }`}
                      ></div>
                      <span
                        className={`text-xs ${
                          isConnected ? "text-green-600" : "text-red-600"
                        }`}
                      >
                        {isConnected ? "Connected" : "Disconnected"}
                      </span>
                    </div>
                    {/* Location tracking status */}
                    {locationTrackingInterval && (
                      <div className='flex items-center space-x-1'>
                        <Navigation className='w-3 h-3 text-blue-500' />
                        <span className='text-xs text-blue-600'>
                          GPS Active
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className='flex items-center space-x-3'>
              <Button
                variant='outline'
                size='sm'
                onClick={refreshDashboard}
                disabled={refreshing}
                className='flex items-center space-x-2'
              >
                <RefreshCw
                  className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
                />
                <span>Refresh</span>
              </Button>

              <Button
                onClick={toggleOnlineStatus}
                className={`flex items-center space-x-2 ${
                  isOnline
                    ? "bg-red-600 hover:bg-red-700"
                    : "bg-green-600 hover:bg-green-700"
                }`}
              >
                {isOnline ? (
                  <Square className='h-4 w-4' />
                ) : (
                  <Play className='h-4 w-4' />
                )}
                <span>{isOnline ? "Go Offline" : "Go Online"}</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
        {/* Status Cards */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
          {/* Online Status */}
          <Card className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>Status</p>
                <p
                  className={`text-2xl font-bold ${
                    isOnline ? "text-green-600" : "text-gray-400"
                  }`}
                >
                  {isOnline ? "Online" : "Offline"}
                </p>
              </div>
              <div
                className={`p-3 rounded-full ${
                  isOnline ? "bg-green-100" : "bg-gray-100"
                }`}
              >
                <Activity
                  className={`h-6 w-6 ${
                    isOnline ? "text-green-600" : "text-gray-400"
                  }`}
                />
              </div>
            </div>
          </Card>

          {/* Today's Orders */}
          <Card className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Today's Orders
                </p>
                <p className='text-2xl font-bold text-blue-600'>
                  {dashboardData?.today_stats?.completed_orders || 0}
                </p>
              </div>
              <div className='p-3 rounded-full bg-blue-100'>
                <Package className='h-6 w-6 text-blue-600' />
              </div>
            </div>
          </Card>

          {/* Today's Earnings */}
          <Card className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>
                  Today's Earnings
                </p>
                <p className='text-2xl font-bold text-green-600'>
                  ${dashboardData?.today_stats?.earnings?.toFixed(2) || "0.00"}
                </p>
              </div>
              <div className='p-3 rounded-full bg-green-100'>
                <DollarSign className='h-6 w-6 text-green-600' />
              </div>
            </div>
          </Card>

          {/* Rating */}
          <Card className='p-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>Rating</p>
                <p className='text-2xl font-bold text-yellow-600'>
                  {dashboardData?.agent_info?.rating?.toFixed(1) || "0.0"}
                </p>
              </div>
              <div className='p-3 rounded-full bg-yellow-100'>
                <Star className='h-6 w-6 text-yellow-600' />
              </div>
            </div>
          </Card>
        </div>

        {/* Shift Management and Quick Stats */}
        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8'>
          <Card className='lg:col-span-2 p-6'>
            <div className='flex items-center justify-between mb-4'>
              <h3 className='text-lg font-semibold text-gray-900'>
                Current Shift
              </h3>
              {currentShift?.is_active ? (
                <Badge
                  variant='success'
                  className='flex items-center space-x-1'
                >
                  <Clock className='h-3 w-3' />
                  <span>Active</span>
                </Badge>
              ) : (
                <Badge variant='secondary'>Inactive</Badge>
              )}
            </div>

            {currentShift?.is_active ? (
              <div className='space-y-4'>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <p className='text-sm text-gray-600'>Start Time</p>
                    <p className='font-medium'>
                      {new Date(currentShift.start_time).toLocaleTimeString()}
                    </p>
                  </div>
                  <div>
                    <p className='text-sm text-gray-600'>Duration</p>
                    <p className='font-medium'>
                      {currentShift.duration_hours?.toFixed(1) || "0.0"}h
                    </p>
                  </div>
                  <div>
                    <p className='text-sm text-gray-600'>Orders Completed</p>
                    <p className='font-medium'>
                      {currentShift.orders_completed || 0}
                    </p>
                  </div>
                  <div>
                    <p className='text-sm text-gray-600'>Shift Earnings</p>
                    <p className='font-medium text-green-600'>
                      ${currentShift.earnings?.toFixed(2) || "0.00"}
                    </p>
                  </div>
                </div>
                <Button
                  onClick={endShift}
                  variant='outline'
                  className='w-full flex items-center justify-center space-x-2'
                >
                  <Square className='h-4 w-4' />
                  <span>End Shift</span>
                </Button>
              </div>
            ) : (
              <div className='text-center py-8'>
                <Timer className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                <p className='text-gray-600 mb-4'>No active shift</p>
                <Button
                  onClick={startShift}
                  className='flex items-center space-x-2'
                >
                  <Play className='h-4 w-4' />
                  <span>Start Shift</span>
                </Button>
              </div>
            )}
          </Card>

          {/* Quick Stats */}
          <Card className='p-6'>
            <h3 className='text-lg font-semibold text-gray-900 mb-4'>
              Quick Stats
            </h3>
            <div className='space-y-4'>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-gray-600'>Total Deliveries</span>
                <span className='font-medium'>
                  {dashboardData?.agent_info?.total_deliveries || 0}
                </span>
              </div>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-gray-600'>Completion Rate</span>
                <span className='font-medium text-green-600'>
                  {dashboardData?.agent_info?.completion_rate?.toFixed(1) ||
                    "0.0"}
                  %
                </span>
              </div>
              <div className='flex justify-between items-center'>
                <span className='text-sm text-gray-600'>Active Orders</span>
                <span className='font-medium text-blue-600'>
                  {dashboardData?.today_stats?.active_orders || 0}
                </span>
              </div>
            </div>
          </Card>
        </div>

        {/* Active Orders */}
        {dashboardData?.active_orders?.length > 0 && (
          <Card className='p-6 mb-8'>
            <h3 className='text-lg font-semibold text-gray-900 mb-4'>
              Active Orders
            </h3>
            <div className='space-y-4'>
              {dashboardData.active_orders.map((order) => (
                <div
                  key={order.id}
                  className='border rounded-lg p-4 bg-gray-50'
                >
                  <div className='flex justify-between items-start'>
                    <div className='flex-1'>
                      <div className='flex items-center space-x-2 mb-2'>
                        <h4 className='font-medium text-gray-900'>
                          {order.restaurant_name}
                        </h4>
                        <Badge
                          variant={
                            order.status === "assigned"
                              ? "warning"
                              : order.status === "picked_up"
                              ? "info"
                              : order.status === "on_the_way"
                              ? "success"
                              : "secondary"
                          }
                        >
                          {order.status.replace("_", " ")}
                        </Badge>
                      </div>
                      <p className='text-sm text-gray-600 mb-1'>
                        Customer: {order.customer_name}
                      </p>
                      <p className='text-sm text-gray-500'>
                        <MapPin className='h-3 w-3 inline mr-1' />
                        {order.delivery_address}
                      </p>
                    </div>
                    <div className='text-right'>
                      <p className='text-lg font-bold text-green-600'>
                        ${order.total_amount?.toFixed(2)}
                      </p>
                      <p className='text-xs text-gray-500'>
                        {new Date(order.created_at).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Performance Overview */}
        <Card className='p-6'>
          <h3 className='text-lg font-semibold text-gray-900 mb-4'>
            Weekly Performance
          </h3>
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
            <div className='text-center'>
              <div className='flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto mb-2'>
                <Target className='h-6 w-6 text-blue-600' />
              </div>
              <p className='text-2xl font-bold text-blue-600'>
                {dashboardData?.weekly_performance?.total_orders || 0}
              </p>
              <p className='text-sm text-gray-600'>Total Orders</p>
            </div>
            <div className='text-center'>
              <div className='flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto mb-2'>
                <CheckCircle className='h-6 w-6 text-green-600' />
              </div>
              <p className='text-2xl font-bold text-green-600'>
                {dashboardData?.weekly_performance?.completion_rate?.toFixed(
                  1
                ) || "0.0"}
                %
              </p>
              <p className='text-sm text-gray-600'>Completion Rate</p>
            </div>
            <div className='text-center'>
              <div className='flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-full mx-auto mb-2'>
                <Star className='h-6 w-6 text-yellow-600' />
              </div>
              <p className='text-2xl font-bold text-yellow-600'>
                {dashboardData?.weekly_performance?.average_rating?.toFixed(
                  1
                ) || "0.0"}
              </p>
              <p className='text-sm text-gray-600'>Avg Rating</p>
            </div>
            <div className='text-center'>
              <div className='flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mx-auto mb-2'>
                <TrendingUp className='h-6 w-6 text-purple-600' />
              </div>
              <p className='text-2xl font-bold text-purple-600'>
                {dashboardData?.weekly_performance?.orders_per_hour?.toFixed(
                  1
                ) || "0.0"}
              </p>
              <p className='text-sm text-gray-600'>Orders/Hour</p>
            </div>
          </div>
        </Card>
      </div>

      {/* In-App Notifications */}
      <InAppNotifications
        notifications={inAppNotifications}
        onRemove={removeInAppNotification}
      />
    </div>
  );
}

export default NewDeliveryDashboard;
