#!/usr/bin/env python3
"""
Setup a test delivery agent to be available for testing
"""

import os
import sys
import django

# Add the Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from deliveryAgent.models import DeliveryAgentProfile

User = get_user_model()

def setup_test_agent():
    """Setup a test delivery agent for testing"""
    
    print("🔧 Setting up test delivery agent...")
    
    # Find an existing delivery agent or create one
    try:
        # Try to find our test agent first
        agent_user = User.objects.get(email='<EMAIL>')
        print(f"✅ Found existing test agent: {agent_user.email}")
    except User.DoesNotExist:
        # Find any delivery agent user
        agent_users = User.objects.filter(role='delivery_agent')
        if agent_users.exists():
            agent_user = agent_users.first()
            print(f"✅ Using existing delivery agent: {agent_user.email}")
        else:
            print("❌ No delivery agent users found. Creating one...")
            import random
            phone_suffix = random.randint(1000, 9999)
            agent_user = User.objects.create(
                email='<EMAIL>',
                name='Test Delivery Agent',
                user_name=f'test_agent_new_{phone_suffix}',
                role='delivery_agent',
                is_verified=True,
                phone=f'+93 79 999 {phone_suffix}'
            )
            agent_user.set_password('testpass123')
            agent_user.save()
            print(f"✅ Created new delivery agent: {agent_user.email}")
    
    # Get or create delivery agent profile
    try:
        agent_profile = DeliveryAgentProfile.objects.get(user=agent_user)
        print(f"✅ Found agent profile: {agent_profile.agent_id}")
    except DeliveryAgentProfile.DoesNotExist:
        agent_profile = DeliveryAgentProfile.objects.create(
            user=agent_user,
            agent_id=f'DA{agent_user.id:03d}',
            status='approved',
            availability='available',
            employment_status='active',
            is_clocked_in=True,
            current_latitude=34.5553,
            current_longitude=69.2075
        )
        print(f"✅ Created agent profile: {agent_profile.agent_id}")
    
    # Make sure the agent is available for testing
    agent_profile.status = 'approved'
    agent_profile.availability = 'available'
    agent_profile.employment_status = 'active'
    agent_profile.is_clocked_in = True
    agent_profile.current_latitude = 34.5553
    agent_profile.current_longitude = 69.2075
    agent_profile.save()
    
    print(f"✅ Agent setup complete!")
    print(f"   Email: {agent_user.email}")
    print(f"   Password: testpass123")
    print(f"   Agent ID: {agent_profile.agent_id}")
    print(f"   Status: {agent_profile.status}")
    print(f"   Availability: {agent_profile.availability}")
    
    return agent_user, agent_profile

if __name__ == '__main__':
    setup_test_agent()
