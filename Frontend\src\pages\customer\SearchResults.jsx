import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { Search, Filter, Star, Clock, MapPin, ChefHat, Utensils } from 'lucide-react';
import { useSearch } from '../../context/SearchContext';
import SearchBox from '../../components/search/SearchBox';
import Card from '../../components/common/Card';
import Badge from '../../components/common/Badge';
import Button from '../../components/common/Button';

const SearchResults = () => {
  const [searchParams] = useSearchParams();
  const query = searchParams.get('q') || '';
  const { searchResults, isSearching, setSearchQuery } = useSearch();
  const [activeTab, setActiveTab] = useState('all');
  const [sortBy, setSortBy] = useState('relevance');

  useEffect(() => {
    if (query) {
      setSearchQuery(query);
    }
  }, [query, setSearchQuery]);

  const { restaurants, dishes, cuisines } = searchResults;
  const totalResults = restaurants.length + dishes.length + cuisines.length;

  const sortResults = (results, type) => {
    if (sortBy === 'relevance') return results;
    
    if (type === 'restaurants') {
      switch (sortBy) {
        case 'rating':
          return [...results].sort((a, b) => b.rating - a.rating);
        case 'delivery_time':
          return [...results].sort((a, b) => 
            parseInt(a.deliveryTime) - parseInt(b.deliveryTime)
          );
        case 'delivery_fee':
          return [...results].sort((a, b) => a.deliveryFee - b.deliveryFee);
        default:
          return results;
      }
    }
    
    if (type === 'dishes') {
      switch (sortBy) {
        case 'price':
          return [...results].sort((a, b) => a.price - b.price);
        default:
          return results;
      }
    }
    
    return results;
  };

  const getFilteredResults = () => {
    switch (activeTab) {
      case 'restaurants':
        return { restaurants: sortResults(restaurants, 'restaurants'), dishes: [], cuisines: [] };
      case 'dishes':
        return { restaurants: [], dishes: sortResults(dishes, 'dishes'), cuisines: [] };
      case 'cuisines':
        return { restaurants: [], dishes: [], cuisines };
      default:
        return {
          restaurants: sortResults(restaurants, 'restaurants'),
          dishes: sortResults(dishes, 'dishes'),
          cuisines
        };
    }
  };

  const filteredResults = getFilteredResults();

  if (isSearching) {
    return (
      <div className="container mx-auto px-4 py-8">
        <SearchBox className="mb-8" />
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 animate-fade-in">
      {/* Search Box */}
      <SearchBox className="mb-8" />

      {/* Search Results Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">
          {query ? `Search results for "${query}"` : 'Search Results'}
        </h1>
        <p className="text-gray-600">
          {totalResults} result{totalResults !== 1 ? 's' : ''} found
        </p>
      </div>

      {totalResults === 0 ? (
        <div className="text-center py-16">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Search size={40} className="text-gray-400" />
          </div>
          <h3 className="text-xl font-medium mb-2">No results found</h3>
          <p className="text-gray-600 mb-6">
            Try adjusting your search terms or browse our restaurants.
          </p>
          <Button variant="primary" to="/restaurants">
            Browse Restaurants
          </Button>
        </div>
      ) : (
        <>
          {/* Filters and Tabs */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            {/* Result Type Tabs */}
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('all')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'all'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                All ({totalResults})
              </button>
              <button
                onClick={() => setActiveTab('restaurants')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'restaurants'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Restaurants ({restaurants.length})
              </button>
              <button
                onClick={() => setActiveTab('dishes')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'dishes'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Dishes ({dishes.length})
              </button>
              <button
                onClick={() => setActiveTab('cuisines')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'cuisines'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Cuisines ({cuisines.length})
              </button>
            </div>

            {/* Sort Options */}
            <div className="flex items-center space-x-2">
              <Filter size={16} className="text-gray-400" />
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="relevance">Sort by Relevance</option>
                <option value="rating">Highest Rated</option>
                <option value="delivery_time">Fastest Delivery</option>
                <option value="delivery_fee">Lowest Delivery Fee</option>
                <option value="price">Price: Low to High</option>
              </select>
            </div>
          </div>

          {/* Search Results */}
          <div className="space-y-8">
            {/* Restaurants */}
            {filteredResults.restaurants.length > 0 && (
              <div>
                {activeTab === 'all' && (
                  <h2 className="text-xl font-semibold mb-4 flex items-center">
                    <Utensils size={20} className="mr-2 text-primary-500" />
                    Restaurants
                  </h2>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredResults.restaurants.map((restaurant) => (
                    <Link to={`/restaurants/${restaurant.id}`} key={restaurant.id}>
                      <Card className="h-full transition-transform duration-200 hover:-translate-y-1" hoverable>
                        <div className="relative h-48 rounded-t-lg overflow-hidden -mx-5 -mt-5 mb-4">
                          <img 
                            src={restaurant.coverImage}
                            alt={restaurant.name}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-4 left-4">
                            {restaurant.isOpen ? (
                              <Badge variant="success" size="small">Open Now</Badge>
                            ) : (
                              <Badge variant="danger" size="small">Closed</Badge>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-start">
                          <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100 mr-4 flex-shrink-0">
                            <img 
                              src={restaurant.logo}
                              alt={restaurant.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg">{restaurant.name}</h3>
                            <div className="flex items-center mt-1 text-gray-600 text-sm">
                              <Star size={16} className="text-yellow-500 mr-1" /> 
                              <span>{restaurant.rating}</span>
                              <span className="mx-2">•</span>
                              <span>{restaurant.cuisine.join(', ')}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="mt-4 flex items-center justify-between text-sm text-gray-600">
                          <div className="flex items-center">
                            <Clock size={16} className="mr-1" />
                            <span>{restaurant.deliveryTime}</span>
                          </div>
                          <div className="flex items-center">
                            <MapPin size={16} className="mr-1" />
                            <span>2.5 km</span>
                          </div>
                          <div>
                            ${restaurant.deliveryFee.toFixed(2)} delivery
                          </div>
                        </div>
                      </Card>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* Dishes */}
            {filteredResults.dishes.length > 0 && (
              <div>
                {activeTab === 'all' && (
                  <h2 className="text-xl font-semibold mb-4 flex items-center">
                    <ChefHat size={20} className="mr-2 text-primary-500" />
                    Dishes
                  </h2>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredResults.dishes.map((dish) => {
                    const restaurant = restaurants.find(r => r.id === dish.restaurantId);
                    return (
                      <Link to={`/restaurants/${dish.restaurantId}`} key={dish.id}>
                        <Card className="transition-transform duration-200 hover:-translate-y-1" hoverable>
                          <div className="flex">
                            <div className="flex-1 pr-4">
                              <h3 className="font-semibold text-lg">{dish.name}</h3>
                              <p className="text-gray-600 text-sm mt-1 line-clamp-2">
                                {dish.description}
                              </p>
                              <div className="mt-2">
                                <span className="font-bold text-primary-600">
                                  ${dish.price.toFixed(2)}
                                </span>
                              </div>
                              {restaurant && (
                                <div className="mt-2 text-sm text-gray-500">
                                  at {restaurant.name}
                                </div>
                              )}
                            </div>
                            <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                              <img 
                                src={dish.image}
                                alt={dish.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                          </div>
                        </Card>
                      </Link>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Cuisines */}
            {filteredResults.cuisines.length > 0 && (
              <div>
                {activeTab === 'all' && (
                  <h2 className="text-xl font-semibold mb-4">Cuisines</h2>
                )}
                <div className="flex flex-wrap gap-3">
                  {filteredResults.cuisines.map((cuisine, index) => (
                    <Link 
                      to={`/restaurants?cuisine=${encodeURIComponent(cuisine)}`}
                      key={index}
                    >
                      <Badge 
                        variant="secondary" 
                        className="px-4 py-2 text-base hover:bg-primary-100 transition-colors cursor-pointer"
                      >
                        {cuisine}
                      </Badge>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default SearchResults;
