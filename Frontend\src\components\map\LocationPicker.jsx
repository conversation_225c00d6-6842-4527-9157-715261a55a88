import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from "react-leaflet";
import { MapPin, Navigation, Search, X, Check } from "lucide-react";
import L from "leaflet";
import Button from "../common/Button";
import Card from "../common/Card";
import { cn } from "../../utils/cn";

// Fix Leaflet icon issue
import icon from "leaflet/dist/images/marker-icon.png";
import iconShadow from "leaflet/dist/images/marker-shadow.png";

const DefaultIcon = L.icon({
  iconUrl: icon,
  shadowUrl: iconShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
});

L.Marker.prototype.options.icon = DefaultIcon;

// Custom marker for selected location
const LocationMarker = ({ position, onLocationSelect }) => {
  const map = useMapEvents({
    click(e) {
      onLocationSelect([e.latlng.lat, e.latlng.lng]);
    },
  });

  return position ? <Marker position={position} /> : null;
};

const LocationPicker = ({
  onLocationSelect,
  onClose,
  initialLocation = [34.526, 69.1776], // Kabul, Afghanistan
  className = "",
  title = "Select Your Location",
}) => {
  const [selectedLocation, setSelectedLocation] = useState(initialLocation);
  const [address, setAddress] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoadingAddress, setIsLoadingAddress] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const mapRef = useRef(null);

  // Get address from coordinates (reverse geocoding) with better error handling
  const getAddressFromCoords = async (lat, lng) => {
    setIsLoadingAddress(true);
    try {
      console.log(
        `🗺️ LocationPicker: Getting address for coordinates: ${lat}, ${lng}`
      );

      // Using Nominatim API for reverse geocoding with better parameters
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1&accept-language=en`,
        {
          headers: {
            "User-Agent": "Afghan-Sofra-App/1.0",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log("🗺️ LocationPicker: Reverse geocoding result:", data);

      if (data && data.display_name) {
        // Format address more professionally
        const formattedAddress = formatAddress(data);
        setAddress(formattedAddress);
        console.log("🗺️ LocationPicker: Formatted address:", formattedAddress);
      } else {
        const fallbackAddress = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        setAddress(fallbackAddress);
        console.log(
          "🗺️ LocationPicker: Using fallback address:",
          fallbackAddress
        );
      }
    } catch (error) {
      console.error("🚫 LocationPicker: Error getting address:", error);
      const fallbackAddress = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
      setAddress(fallbackAddress);

      // Show user-friendly error message
      if (error.message.includes("Failed to fetch")) {
        console.log(
          "🌐 LocationPicker: Network error - using coordinates as address"
        );
      }
    } finally {
      setIsLoadingAddress(false);
    }
  };

  // Format address from Nominatim data
  const formatAddress = (data) => {
    const address = data.address || {};
    const parts = [];

    // Add house number and road
    if (address.house_number && address.road) {
      parts.push(`${address.house_number} ${address.road}`);
    } else if (address.road) {
      parts.push(address.road);
    }

    // Add neighborhood or suburb
    if (address.neighbourhood) {
      parts.push(address.neighbourhood);
    } else if (address.suburb) {
      parts.push(address.suburb);
    }

    // Add city
    if (address.city) {
      parts.push(address.city);
    } else if (address.town) {
      parts.push(address.town);
    } else if (address.village) {
      parts.push(address.village);
    }

    // Add country
    if (address.country) {
      parts.push(address.country);
    }

    return parts.length > 0 ? parts.join(", ") : data.display_name;
  };

  // Search for locations
  const searchLocation = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
          query
        )}&limit=5&countrycodes=af`
      );
      const data = await response.json();
      setSearchResults(data || []);
    } catch (error) {
      console.error("Error searching location:", error);
      setSearchResults([]);
    }
  };

  // Get user's current location
  const getCurrentLocation = () => {
    setIsGettingLocation(true);

    if (!navigator.geolocation) {
      alert("Geolocation is not supported by this browser.");
      setIsGettingLocation(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        const newLocation = [latitude, longitude];
        setSelectedLocation(newLocation);
        getAddressFromCoords(latitude, longitude);
        setIsGettingLocation(false);
      },
      (error) => {
        console.error("Error getting location:", error);
        alert(
          "Unable to get your location. Please select manually on the map."
        );
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      }
    );
  };

  // Handle location selection
  const handleLocationSelect = (location) => {
    setSelectedLocation(location);
    getAddressFromCoords(location[0], location[1]);
  };

  // Handle search result selection
  const handleSearchResultSelect = (result) => {
    const location = [parseFloat(result.lat), parseFloat(result.lon)];
    setSelectedLocation(location);
    setAddress(result.display_name);
    setSearchQuery("");
    setSearchResults([]);
  };

  // Confirm location selection
  const handleConfirm = () => {
    onLocationSelect({
      coordinates: selectedLocation,
      address: address,
      lat: selectedLocation[0],
      lng: selectedLocation[1],
    });
    onClose?.();
  };

  // Initialize address for initial location
  useEffect(() => {
    if (selectedLocation) {
      getAddressFromCoords(selectedLocation[0], selectedLocation[1]);
    }
  }, []);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery) {
        searchLocation(searchQuery);
      } else {
        setSearchResults([]);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  return (
    <div className='fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4'>
      <Card
        className={cn(
          "w-full max-w-4xl max-h-[90vh] overflow-hidden",
          className
        )}
      >
        {/* Header */}
        <div className='flex items-center justify-between p-4 border-b'>
          <h2 className='text-xl font-semibold'>{title}</h2>
          <button
            onClick={onClose}
            className='p-2 hover:bg-gray-100 rounded-full transition-colors'
          >
            <X size={20} />
          </button>
        </div>

        {/* Search Bar */}
        <div className='p-4 border-b'>
          <div className='relative'>
            <Search
              size={20}
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
            />
            <input
              type='text'
              placeholder='Search for a location...'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className='w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500'
            />
          </div>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div className='mt-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-40 overflow-y-auto'>
              {searchResults.map((result, index) => (
                <button
                  key={index}
                  onClick={() => handleSearchResultSelect(result)}
                  className='w-full text-left p-3 hover:bg-gray-50 border-b last:border-b-0 transition-colors'
                >
                  <div className='flex items-start'>
                    <MapPin
                      size={16}
                      className='text-gray-400 mt-1 mr-2 flex-shrink-0'
                    />
                    <div>
                      <div className='font-medium text-sm'>
                        {result.display_name}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Map */}
        <div className='h-96 relative'>
          <MapContainer
            center={selectedLocation}
            zoom={15}
            style={{ width: "100%", height: "100%" }}
            ref={mapRef}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
            />
            <LocationMarker
              position={selectedLocation}
              onLocationSelect={handleLocationSelect}
            />
          </MapContainer>

          {/* Current Location Button */}
          <button
            onClick={getCurrentLocation}
            disabled={isGettingLocation}
            className='absolute bottom-4 right-4 bg-white p-3 rounded-full shadow-lg hover:shadow-xl transition-shadow disabled:opacity-50'
          >
            <Navigation
              size={20}
              className={cn(
                "text-primary-500",
                isGettingLocation && "animate-spin"
              )}
            />
          </button>
        </div>

        {/* Selected Address */}
        <div className='p-4 border-t'>
          <div className='mb-4'>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Selected Address:
            </label>
            <div className='flex items-start space-x-2'>
              <MapPin size={16} className='text-gray-400 mt-1 flex-shrink-0' />
              <div className='flex-1'>
                {isLoadingAddress ? (
                  <div className='animate-pulse bg-gray-200 h-4 rounded'></div>
                ) : (
                  <p className='text-sm text-gray-600'>{address}</p>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className='flex space-x-3'>
            <Button variant='outline' onClick={onClose} className='flex-1'>
              Cancel
            </Button>
            <Button
              variant='primary'
              onClick={handleConfirm}
              disabled={!selectedLocation || isLoadingAddress}
              icon={<Check size={16} />}
              className='flex-1'
            >
              Confirm Location
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default LocationPicker;
