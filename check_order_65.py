#!/usr/bin/env python3
"""
Check if Order #65 exists and its current status
"""

import os
import sys
import django

# Setup Django
backend_path = os.path.join(os.path.dirname(__file__), 'Backend')
sys.path.insert(0, backend_path)
os.chdir(backend_path)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Food_Delivery_project.settings')
django.setup()

from orders.models import Order
from django.contrib.auth import get_user_model

User = get_user_model()

def check_order_65():
    print("🔍 Checking Order #65")
    print("=" * 50)
    
    try:
        # Try to find Order #65
        order = Order.objects.get(id=65)
        print(f"✅ Order #65 EXISTS!")
        print(f"   Customer: {order.customer.name}")
        print(f"   Restaurant: {order.restaurant.name}")
        print(f"   Status: {order.status}")
        print(f"   Total Amount: ${order.total_amount}")
        print(f"   Delivery Agent: {order.delivery_agent}")
        print(f"   Created: {order.created_at}")
        print(f"   Updated: {order.updated_at}")
        
        # Check why it might not show in admin assignment
        print(f"\n🔍 Assignment Eligibility Check:")
        print(f"   Status is 'ready': {order.status == 'ready'}")
        print(f"   No delivery agent assigned: {order.delivery_agent is None}")
        print(f"   Should show in admin: {order.status == 'ready' and order.delivery_agent is None}")
        
    except Order.DoesNotExist:
        print("❌ Order #65 does NOT exist in the database")
        
        # Show the highest order ID
        latest_order = Order.objects.order_by('-id').first()
        if latest_order:
            print(f"   Highest order ID: #{latest_order.id}")
        
        # Show orders from khan sab
        print(f"\n📦 Orders from 'khan sab':")
        khan_orders = Order.objects.filter(customer__name__icontains='khan').order_by('-created_at')[:5]
        for order in khan_orders:
            print(f"   Order #{order.id}: ${order.total_amount} - {order.status} - {order.created_at.strftime('%Y-%m-%d %H:%M')}")
    
    # Show all ready orders for comparison
    print(f"\n📋 All Ready Orders (unassigned):")
    ready_orders = Order.objects.filter(
        status='ready',
        delivery_agent__isnull=True
    ).order_by('-created_at')[:10]
    
    for order in ready_orders:
        print(f"   Order #{order.id}: {order.customer.name} - ${order.total_amount} - {order.created_at.strftime('%Y-%m-%d %H:%M')}")

if __name__ == "__main__":
    check_order_65()
