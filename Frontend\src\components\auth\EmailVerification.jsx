import React, { useState, useEffect } from "react";
import {
  Mail,
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  RefreshCw,
} from "lucide-react";
import Button from "../common/Button";
import Input from "../common/Input";
import toast from "../../utils/toast";

const EmailVerification = ({
  email,
  onVerify,
  onBack,
  loading,
  error,
  onResend,
}) => {
  const [otp, setOtp] = useState("");
  const [resendLoading, setResendLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [resendSuccess, setResendSuccess] = useState(false);

  // Cooldown timer effect
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (otp.trim()) {
      onVerify(otp.trim());
    }
  };

  const handleResend = async () => {
    if (resendCooldown > 0) return;

    setResendLoading(true);
    setResendSuccess(false);

    try {
      if (onResend) {
        await onResend();
        setResendSuccess(true);
        setResendCooldown(60); // 60 second cooldown

        // Clear success message after 3 seconds
        setTimeout(() => setResendSuccess(false), 3000);
      }
    } catch (error) {
      console.error("Resend failed:", error);
      // Error will be handled by parent component
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <div className='animate-fade-in'>
      <div className='text-center mb-8'>
        <div className='w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4'>
          <Mail size={32} className='text-primary-500' />
        </div>
        <h1 className='text-2xl font-poppins font-bold text-text-primary mb-2'>
          Verify Your Email
        </h1>
        <p className='text-text-secondary'>We've sent a verification code to</p>
        <p className='text-primary-500 font-medium'>{email}</p>
      </div>

      {error && (
        <div className='flex items-start p-4 mb-6 bg-red-50 border border-red-200 rounded-lg'>
          <AlertCircle
            size={18}
            className='text-accent-red mr-2 mt-0.5 flex-shrink-0'
          />
          <p className='text-accent-red text-sm'>{error}</p>
        </div>
      )}

      {resendSuccess && (
        <div className='flex items-start p-4 mb-6 bg-green-50 border border-green-200 rounded-lg'>
          <CheckCircle
            size={18}
            className='text-green-600 mr-2 mt-0.5 flex-shrink-0'
          />
          <p className='text-green-600 text-sm'>
            Verification code sent successfully! Please check your email.
          </p>
        </div>
      )}

      <form onSubmit={handleSubmit} className='space-y-6'>
        <div>
          <Input
            label='Verification Code'
            type='text'
            placeholder='Enter 6-digit code'
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            maxLength={6}
            required
            className='text-center text-lg tracking-widest'
          />
          <p className='text-sm text-text-secondary mt-2'>
            Please enter the 6-digit code sent to <strong>{email}</strong>
          </p>
        </div>

        <Button
          type='submit'
          variant='primary'
          fullWidth
          loading={loading}
          disabled={otp.length !== 6}
        >
          Verify Email
        </Button>
      </form>

      <div className='mt-6 text-center'>
        <button
          onClick={onBack}
          className='inline-flex items-center text-primary-500 hover:text-primary-600 transition-colors'
        >
          <ArrowLeft size={16} className='mr-1' />
          Back to Registration
        </button>
      </div>

      <div className='mt-4 text-center'>
        <p className='text-sm text-text-secondary'>
          Didn't receive the code?{" "}
          <button
            className={`font-medium transition-colors ${
              resendCooldown > 0
                ? "text-gray-400 cursor-not-allowed"
                : "text-primary-500 hover:text-primary-600"
            }`}
            onClick={handleResend}
            disabled={resendCooldown > 0 || resendLoading}
          >
            {resendLoading ? (
              <span className='inline-flex items-center'>
                <RefreshCw size={14} className='animate-spin mr-1' />
                Sending...
              </span>
            ) : resendCooldown > 0 ? (
              `Resend in ${resendCooldown}s`
            ) : (
              "Resend"
            )}
          </button>
        </p>
      </div>
    </div>
  );
};

export default EmailVerification;
