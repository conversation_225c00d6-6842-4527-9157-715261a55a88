import React, { useState, useEffect } from 'react';
import { Trophy, Star, TrendingUp, Award, Clock, Target } from 'lucide-react';
import deliveryAgentApi from '../../services/deliveryAgentApi';

const PerformanceLeaderboard = () => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [periodDays, setPeriodDays] = useState(30);
  const [userRank, setUserRank] = useState(null);

  useEffect(() => {
    loadLeaderboard();
  }, [periodDays]);

  const loadLeaderboard = async () => {
    try {
      setLoading(true);
      const result = await deliveryAgentApi.getPerformanceLeaderboard(periodDays, 20);
      
      if (result.success) {
        setLeaderboard(result.data.leaderboard);
        
        // Find current user's rank
        const currentUser = JSON.parse(localStorage.getItem('user'));
        if (currentUser) {
          const userIndex = result.data.leaderboard.findIndex(
            agent => agent.agent_name === currentUser.first_name + ' ' + currentUser.last_name
          );
          setUserRank(userIndex >= 0 ? userIndex + 1 : null);
        }
        
        setError(null);
      } else {
        setError(result.error?.message || 'Failed to load leaderboard');
      }
    } catch (err) {
      setError('Failed to load leaderboard');
      console.error('Leaderboard loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (rank) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-6 w-6 text-yellow-500" />;
      case 2:
        return <Award className="h-6 w-6 text-gray-400" />;
      case 3:
        return <Award className="h-6 w-6 text-amber-600" />;
      default:
        return <span className="text-lg font-bold text-gray-600">#{rank}</span>;
    }
  };

  const getRankBadgeColor = (rank) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white';
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getScoreColor = (score) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <div className="text-red-600 text-lg font-semibold mb-2">Error</div>
          <div className="text-gray-600 mb-4">{error}</div>
          <button
            onClick={loadLeaderboard}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Trophy className="h-6 w-6 text-yellow-500" />
            <h2 className="text-xl font-bold text-gray-900">Performance Leaderboard</h2>
          </div>
          
          <select
            value={periodDays}
            onChange={(e) => setPeriodDays(parseInt(e.target.value))}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
          >
            <option value={7}>Last 7 days</option>
            <option value={30}>Last 30 days</option>
            <option value={90}>Last 90 days</option>
          </select>
        </div>
        
        {userRank && (
          <div className="mt-3 p-3 bg-blue-50 rounded-lg">
            <div className="text-sm text-blue-800">
              Your current rank: <span className="font-semibold">#{userRank}</span>
            </div>
          </div>
        )}
      </div>

      {/* Leaderboard */}
      <div className="p-6">
        {leaderboard.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <Trophy className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <div>No performance data available</div>
            <div className="text-sm">Complete some deliveries to appear on the leaderboard</div>
          </div>
        ) : (
          <div className="space-y-3">
            {leaderboard.map((agent, index) => {
              const rank = index + 1;
              const isCurrentUser = userRank === rank;
              
              return (
                <div
                  key={agent.agent_id}
                  className={`flex items-center p-4 rounded-lg border-2 transition-all ${
                    isCurrentUser 
                      ? 'border-blue-300 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {/* Rank */}
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full mr-4 ${getRankBadgeColor(rank)}`}>
                    {getRankIcon(rank)}
                  </div>

                  {/* Agent Info */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-gray-900">{agent.agent_name}</h3>
                      {isCurrentUser && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                          You
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <Target className="h-4 w-4" />
                        <span>{agent.completion_rate.toFixed(1)}% completion</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-yellow-400" />
                        <span>{agent.average_rating.toFixed(1)} rating</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{agent.total_orders} orders</span>
                      </div>
                    </div>
                  </div>

                  {/* Score */}
                  <div className="text-right">
                    <div className={`text-2xl font-bold ${getScoreColor(agent.overall_score)}`}>
                      {agent.overall_score.toFixed(1)}
                    </div>
                    <div className="text-xs text-gray-500">Score</div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-6 bg-gray-50 border-t border-gray-200 rounded-b-lg">
        <div className="text-center text-sm text-gray-600">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-1">
              <TrendingUp className="h-4 w-4" />
              <span>Rankings update every hour</span>
            </div>
          </div>
          <div className="mt-2">
            Score based on completion rate, delivery time, customer ratings, and efficiency
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceLeaderboard;
