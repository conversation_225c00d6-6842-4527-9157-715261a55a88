from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from deliveryAgent.afghan_agent_models import AfghanDeliveryAgent, AgentTraining, AgentPayment
from deliveryAgent.models import DeliveryAgentProfile

User = get_user_model()

class Command(BaseCommand):
    help = 'Delete delivery agent users and related data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--all',
            action='store_true',
            help='Delete all delivery users and related data',
        )
        parser.add_argument(
            '--user-id',
            type=int,
            help='Delete specific user by ID',
        )
        parser.add_argument(
            '--username',
            type=str,
            help='Delete specific user by username',
        )
        parser.add_argument(
            '--role-only',
            action='store_true',
            help='Delete only users with delivery_agent role (keep profiles)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be deleted without actually deleting',
        )

    def handle(self, *args, **options):
        if options['all']:
            self.delete_all_delivery_users(options['dry_run'])
        elif options['user_id']:
            self.delete_user_by_id(options['user_id'], options['dry_run'])
        elif options['username']:
            self.delete_user_by_username(options['username'], options['dry_run'])
        elif options['role_only']:
            self.delete_by_role_only(options['dry_run'])
        else:
            self.list_delivery_users()

    def list_delivery_users(self):
        """List all delivery users"""
        self.stdout.write(self.style.HTTP_INFO('🔍 Delivery Agent Users:'))
        self.stdout.write('-' * 60)
        
        # Get all delivery users
        delivery_users = User.objects.filter(role='delivery_agent')
        afghan_agents = AfghanDeliveryAgent.objects.all()
        regular_agents = DeliveryAgentProfile.objects.all()
        
        # Users with delivery_agent role
        self.stdout.write(f'Users with delivery_agent role: {delivery_users.count()}')
        for user in delivery_users:
            self.stdout.write(f'  • ID: {user.id}, Username: {user.user_name}, Name: {user.name}')
        
        # Afghan delivery agents
        self.stdout.write(f'\nAfghan delivery agents: {afghan_agents.count()}')
        for agent in afghan_agents:
            user_info = f"User: {agent.user.user_name}" if agent.user else "No user"
            self.stdout.write(f'  • {agent.agent_code}: {agent.full_name_dari} ({user_info})')
        
        # Regular delivery agents
        self.stdout.write(f'\nRegular delivery agents: {regular_agents.count()}')
        for agent in regular_agents:
            user_info = f"User: {agent.user.user_name}" if agent.user else "No user"
            self.stdout.write(f'  • {agent.full_name} ({user_info})')

    def delete_all_delivery_users(self, dry_run=False):
        """Delete all delivery users and related data"""
        self.stdout.write(self.style.WARNING('⚠️  Deleting ALL delivery users and related data'))
        
        # Get all delivery users
        delivery_users = User.objects.filter(role='delivery_agent')
        afghan_agents = AfghanDeliveryAgent.objects.all()
        regular_agents = DeliveryAgentProfile.objects.all()
        
        # Count related data
        training_count = AgentTraining.objects.filter(agent__in=afghan_agents).count()
        payment_count = AgentPayment.objects.filter(agent__in=afghan_agents).count()
        
        self.stdout.write(f'Will delete:')
        self.stdout.write(f'  • {delivery_users.count()} users with delivery_agent role')
        self.stdout.write(f'  • {afghan_agents.count()} Afghan delivery agents')
        self.stdout.write(f'  • {regular_agents.count()} regular delivery agents')
        self.stdout.write(f'  • {training_count} training records')
        self.stdout.write(f'  • {payment_count} payment records')
        
        if dry_run:
            self.stdout.write(self.style.SUCCESS('🔍 DRY RUN - No data was actually deleted'))
            return
        
        # Confirm deletion
        confirm = input('Type "DELETE" to confirm: ')
        if confirm != 'DELETE':
            self.stdout.write(self.style.ERROR('❌ Deletion cancelled'))
            return
        
        try:
            with transaction.atomic():
                # Delete training records
                deleted_training = AgentTraining.objects.filter(agent__in=afghan_agents).delete()
                
                # Delete payment records
                deleted_payments = AgentPayment.objects.filter(agent__in=afghan_agents).delete()
                
                # Update orders
                from orders.models import Order
                orders_updated = Order.objects.filter(
                    delivery_agent__in=delivery_users
                ).update(delivery_agent=None)
                
                # Delete agent profiles
                deleted_afghan = afghan_agents.delete()
                deleted_regular = regular_agents.delete()
                
                # Delete users
                deleted_users = delivery_users.delete()
                
                self.stdout.write(self.style.SUCCESS('✅ Deletion completed:'))
                self.stdout.write(f'  • Users: {deleted_users[0]}')
                self.stdout.write(f'  • Afghan agents: {deleted_afghan[0]}')
                self.stdout.write(f'  • Regular agents: {deleted_regular[0]}')
                self.stdout.write(f'  • Training records: {deleted_training[0]}')
                self.stdout.write(f'  • Payment records: {deleted_payments[0]}')
                self.stdout.write(f'  • Orders updated: {orders_updated}')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error: {e}'))

    def delete_user_by_id(self, user_id, dry_run=False):
        """Delete specific user by ID"""
        try:
            user = User.objects.get(id=user_id)
            self.stdout.write(f'Found user: {user.user_name} ({user.name})')
            
            if dry_run:
                self.stdout.write(self.style.SUCCESS('🔍 DRY RUN - No data was actually deleted'))
                return
            
            self._delete_single_user(user)
            
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'❌ User with ID {user_id} not found'))

    def delete_user_by_username(self, username, dry_run=False):
        """Delete specific user by username"""
        try:
            user = User.objects.get(user_name=username)
            self.stdout.write(f'Found user: {user.user_name} ({user.name})')
            
            if dry_run:
                self.stdout.write(self.style.SUCCESS('🔍 DRY RUN - No data was actually deleted'))
                return
            
            self._delete_single_user(user)
            
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'❌ User "{username}" not found'))

    def delete_by_role_only(self, dry_run=False):
        """Delete users with delivery_agent role only"""
        delivery_users = User.objects.filter(role='delivery_agent')
        
        self.stdout.write(f'Will delete {delivery_users.count()} users with delivery_agent role')
        
        if dry_run:
            self.stdout.write(self.style.SUCCESS('🔍 DRY RUN - No data was actually deleted'))
            return
        
        try:
            with transaction.atomic():
                # Update orders first
                from orders.models import Order
                orders_updated = Order.objects.filter(
                    delivery_agent__in=delivery_users
                ).update(delivery_agent=None)
                
                # Delete users
                deleted_count = delivery_users.count()
                delivery_users.delete()
                
                self.stdout.write(self.style.SUCCESS(f'✅ Deleted {deleted_count} users'))
                self.stdout.write(f'✅ Updated {orders_updated} orders')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error: {e}'))

    def _delete_single_user(self, user):
        """Delete a single user and related data"""
        try:
            with transaction.atomic():
                deleted_counts = {}
                
                # Delete Afghan agent data
                try:
                    agent = AfghanDeliveryAgent.objects.get(user=user)
                    training_deleted = AgentTraining.objects.filter(agent=agent).delete()
                    payment_deleted = AgentPayment.objects.filter(agent=agent).delete()
                    agent.delete()
                    deleted_counts['afghan_agent'] = True
                    deleted_counts['training'] = training_deleted[0]
                    deleted_counts['payments'] = payment_deleted[0]
                except AfghanDeliveryAgent.DoesNotExist:
                    deleted_counts['afghan_agent'] = False
                
                # Delete regular agent data
                try:
                    agent = DeliveryAgentProfile.objects.get(user=user)
                    agent.delete()
                    deleted_counts['regular_agent'] = True
                except DeliveryAgentProfile.DoesNotExist:
                    deleted_counts['regular_agent'] = False
                
                # Update orders
                from orders.models import Order
                orders_updated = Order.objects.filter(delivery_agent=user).update(delivery_agent=None)
                deleted_counts['orders_updated'] = orders_updated
                
                # Delete user
                user.delete()
                
                self.stdout.write(self.style.SUCCESS(f'✅ User "{user.user_name}" deleted successfully'))
                if deleted_counts['afghan_agent']:
                    self.stdout.write(f'  • Afghan agent profile deleted')
                    self.stdout.write(f'  • {deleted_counts["training"]} training records deleted')
                    self.stdout.write(f'  • {deleted_counts["payments"]} payment records deleted')
                if deleted_counts['regular_agent']:
                    self.stdout.write(f'  • Regular agent profile deleted')
                if orders_updated > 0:
                    self.stdout.write(f'  • {orders_updated} orders updated')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error deleting user: {e}'))
