import React, { useState } from "react";
import { menuCategoryApi } from "../../utils/menuApi";
import Button from "../../components/common/Button";

const MenuApiTest = () => {
  const [loading, setLoading] = useState({});
  const [results, setResults] = useState({});

  const testCreateCategory = async () => {
    setLoading((prev) => ({ ...prev, create: true }));
    try {
      const result = await menuCategoryApi.createCategory({
        name: "Test Category",
        description: "This is a test category created via API",
        restaurant: 1, // Replace with actual restaurant ID
      });
      setResults((prev) => ({ ...prev, create: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        create: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, create: false }));
  };

  const testGetCategories = async () => {
    setLoading((prev) => ({ ...prev, getAll: true }));
    try {
      const result = await menuCategoryApi.getCategories(1); // Replace with actual restaurant ID
      setResults((prev) => ({ ...prev, getAll: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getAll: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getAll: false }));
  };

  const testGetCategory = async () => {
    setLoading((prev) => ({ ...prev, getSingle: true }));
    try {
      const result = await menuCategoryApi.getCategory(1); // Replace with actual category ID
      setResults((prev) => ({ ...prev, getSingle: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getSingle: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getSingle: false }));
  };

  const testUpdateCategory = async () => {
    setLoading((prev) => ({ ...prev, update: true }));
    try {
      const result = await menuCategoryApi.updateCategory(1, {
        name: "Updated Test Category",
        description: "This category has been updated via API",
      });
      setResults((prev) => ({ ...prev, update: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        update: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, update: false }));
  };

  const testDeleteCategory = async () => {
    setLoading((prev) => ({ ...prev, delete: true }));
    try {
      const result = await menuCategoryApi.deleteCategory(1); // Replace with actual category ID
      setResults((prev) => ({ ...prev, delete: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        delete: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, delete: false }));
  };

  const renderResult = (key) => {
    const result = results[key];
    if (!result) return null;

    return (
      <div className='mt-4 p-4 bg-gray-50 rounded-lg'>
        <h4 className='font-semibold mb-2'>
          {key.charAt(0).toUpperCase() + key.slice(1)} Result:
        </h4>
        <div
          className={`p-3 rounded ${
            result.success
              ? "bg-green-100 text-green-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          <pre className='text-sm overflow-auto'>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      </div>
    );
  };

  return (
    <div className='p-6'>
      <div className='max-w-4xl mx-auto'>
        <h1 className='text-3xl font-bold text-gray-900 mb-8'>
          Menu Category API Test
        </h1>

        <div className='bg-white rounded-lg shadow-md p-6'>
          <h2 className='text-xl font-semibold mb-6'>API Endpoints Testing</h2>

          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8'>
            <Button
              onClick={testCreateCategory}
              loading={loading.create}
              variant='primary'
            >
              Test Create Category
            </Button>

            <Button
              onClick={testGetCategories}
              loading={loading.getAll}
              variant='secondary'
            >
              Test Get All Categories
            </Button>

            <Button
              onClick={testGetCategory}
              loading={loading.getSingle}
              variant='info'
            >
              Test Get Single Category
            </Button>

            <Button
              onClick={testUpdateCategory}
              loading={loading.update}
              variant='warning'
            >
              Test Update Category
            </Button>

            <Button
              onClick={testDeleteCategory}
              loading={loading.delete}
              variant='danger'
            >
              Test Delete Category
            </Button>
          </div>

          <div className='space-y-4'>
            {renderResult("create")}
            {renderResult("getAll")}
            {renderResult("getSingle")}
            {renderResult("update")}
            {renderResult("delete")}
          </div>
        </div>

        <div className='mt-8 bg-blue-50 border-l-4 border-blue-500 p-4'>
          <h3 className='font-semibold text-blue-800 mb-2'>API Information:</h3>
          <ul className='text-blue-700 text-sm space-y-1'>
            <li>
              <strong>Base URL:</strong> https://afghansufra.luilala.com/api
            </li>
            <li>
              <strong>Authentication:</strong> Bearer Token (from localStorage)
            </li>
            <li>
              <strong>Create:</strong> POST /restaurant/menu-categories/
            </li>
            <li>
              <strong>List:</strong> GET /menu-categories/?restaurant_id=1
            </li>
            <li>
              <strong>Get Single:</strong> GET /menu-categories/1/
            </li>
            <li>
              <strong>Update:</strong> PATCH /menu-categories/1/
            </li>
            <li>
              <strong>Delete:</strong> DELETE /menu-categories/1/
            </li>
          </ul>
        </div>

        <div className='mt-6 bg-yellow-50 border-l-4 border-yellow-500 p-4'>
          <h3 className='font-semibold text-yellow-800 mb-2'>Note:</h3>
          <p className='text-yellow-700 text-sm'>
            Make sure you're logged in as a restaurant user and update the
            restaurant/category IDs in the test functions to match your actual
            data.
          </p>
        </div>
      </div>
    </div>
  );
};

export default MenuApiTest;
