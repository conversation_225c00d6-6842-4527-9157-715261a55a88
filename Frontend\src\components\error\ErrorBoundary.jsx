import React from 'react';
import { Al<PERSON><PERSON>riangle, RefreshCw, Home, Bug } from 'lucide-react';
import Button from '../common/Button';
import Card from '../common/Card';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    this.setState({
      error,
      errorInfo
    });

    // Log to error reporting service (in production)
    this.logErrorToService(error, errorInfo);
  }

  logErrorToService = (error, errorInfo) => {
    // In production, send to error tracking service like Sentry
    console.error('Error Boundary caught an error:', {
      error: error.toString(),
      errorInfo,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      const { fallback: Fallback, level = 'page' } = this.props;
      
      // Use custom fallback if provided
      if (Fallback) {
        return <Fallback 
          error={this.state.error} 
          retry={this.handleRetry}
          errorId={this.state.errorId}
        />;
      }

      // Component-level error (smaller error UI)
      if (level === 'component') {
        return (
          <Card className="p-6 text-center border-red-200 bg-red-50">
            <AlertTriangle size={32} className="text-red-500 mx-auto mb-3" />
            <h3 className="font-semibold text-red-800 mb-2">Something went wrong</h3>
            <p className="text-red-600 text-sm mb-4">
              This component encountered an error and couldn't load properly.
            </p>
            <Button 
              variant="outline" 
              size="small" 
              onClick={this.handleRetry}
              icon={<RefreshCw size={14} />}
            >
              Try Again
            </Button>
          </Card>
        );
      }

      // Page-level error (full error page)
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <div className="max-w-md w-full">
            <Card className="p-8 text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <AlertTriangle size={32} className="text-red-600" />
              </div>
              
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Oops! Something went wrong
              </h1>
              
              <p className="text-gray-600 mb-6">
                We're sorry, but something unexpected happened. Our team has been notified and is working on a fix.
              </p>

              {/* Error ID for support */}
              <div className="bg-gray-100 rounded-lg p-3 mb-6">
                <p className="text-xs text-gray-500 mb-1">Error ID:</p>
                <code className="text-sm font-mono text-gray-700">
                  {this.state.errorId}
                </code>
              </div>

              <div className="space-y-3">
                <Button 
                  variant="primary" 
                  fullWidth 
                  onClick={this.handleRetry}
                  icon={<RefreshCw size={16} />}
                >
                  Try Again
                </Button>
                
                <Button 
                  variant="outline" 
                  fullWidth 
                  onClick={this.handleReload}
                >
                  Reload Page
                </Button>
                
                <Button 
                  variant="ghost" 
                  fullWidth 
                  onClick={this.handleGoHome}
                  icon={<Home size={16} />}
                >
                  Go to Homepage
                </Button>
              </div>

              {/* Development error details */}
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-6 text-left">
                  <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700 flex items-center">
                    <Bug size={14} className="mr-1" />
                    Show Error Details (Development)
                  </summary>
                  <div className="mt-3 p-3 bg-gray-900 rounded text-white text-xs overflow-auto">
                    <div className="mb-2">
                      <strong>Error:</strong> {this.state.error.toString()}
                    </div>
                    <div>
                      <strong>Stack Trace:</strong>
                      <pre className="mt-1 whitespace-pre-wrap">
                        {this.state.errorInfo?.componentStack}
                      </pre>
                    </div>
                  </div>
                </details>
              )}
            </Card>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  return function WithErrorBoundaryComponent(props) {
    return (
      <ErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </ErrorBoundary>
    );
  };
};

// Hook for error boundary in functional components
export const useErrorHandler = () => {
  return (error, errorInfo) => {
    // This would trigger the nearest error boundary
    throw error;
  };
};

export default ErrorBoundary;
