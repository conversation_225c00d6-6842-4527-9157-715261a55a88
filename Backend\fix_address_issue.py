#!/usr/bin/env python
"""
Fix address validation issues for order placement
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from users.models import User
from restaurant.models import Address

def check_all_addresses():
    """Check all addresses in the system"""
    print("🔍 Checking all addresses in the system...")
    
    all_addresses = Address.objects.all()
    print(f"Total addresses in database: {all_addresses.count()}")
    
    # Group by user
    users_with_addresses = {}
    for addr in all_addresses:
        user_key = f"{addr.user.user_name} ({addr.user.role})"
        if user_key not in users_with_addresses:
            users_with_addresses[user_key] = []
        users_with_addresses[user_key].append(addr)
    
    for user_key, addresses in users_with_addresses.items():
        print(f"\n👤 {user_key}:")
        for addr in addresses:
            print(f"   📍 ID: {addr.id} - {addr.street}, {addr.city}")
    
    return all_addresses

def check_specific_address(address_id):
    """Check if a specific address exists"""
    try:
        address = Address.objects.get(id=address_id)
        print(f"✅ Address ID {address_id} exists:")
        print(f"   User: {address.user.user_name} ({address.user.role})")
        print(f"   Address: {address.street}, {address.city}")
        return address
    except Address.DoesNotExist:
        print(f"❌ Address ID {address_id} does not exist")
        return None

def create_test_addresses():
    """Create test addresses for customers if needed"""
    print("\n🏗️ Creating test addresses for customers...")
    
    customers = User.objects.filter(role='customer')
    created_count = 0
    
    for customer in customers:
        # Check if customer has any addresses
        existing_addresses = Address.objects.filter(user=customer)
        
        if existing_addresses.count() == 0:
            # Create a default address
            address = Address.objects.create(
                user=customer,
                street=f"Test Street for {customer.user_name}",
                city="Kabul",
                state="Kabul",
                postal_code="1001",
                country="Afghanistan",
                latitude=34.5553,
                longitude=69.2075
            )
            print(f"   ✅ Created address ID {address.id} for {customer.user_name}")
            created_count += 1
        else:
            print(f"   ℹ️ {customer.user_name} already has {existing_addresses.count()} address(es)")
    
    print(f"\n📊 Created {created_count} new addresses")

def validate_order_addresses():
    """Validate addresses that might be used in orders"""
    print("\n🔍 Validating common address IDs...")
    
    # Check common IDs that might be causing issues
    test_ids = [1, 2, 3, 4, 5]
    
    for addr_id in test_ids:
        check_specific_address(addr_id)

def get_customer_addresses(customer_username=None):
    """Get addresses for a specific customer or all customers"""
    if customer_username:
        try:
            customer = User.objects.get(user_name=customer_username, role='customer')
            addresses = Address.objects.filter(user=customer)
            print(f"\n📍 Addresses for {customer_username}:")
            for addr in addresses:
                print(f"   ID: {addr.id} - {addr.street}, {addr.city}")
            return addresses
        except User.DoesNotExist:
            print(f"❌ Customer {customer_username} not found")
            return []
    else:
        customers = User.objects.filter(role='customer')
        all_customer_addresses = []
        
        print(f"\n📍 All customer addresses:")
        for customer in customers:
            addresses = Address.objects.filter(user=customer)
            if addresses.exists():
                print(f"\n👤 {customer.user_name}:")
                for addr in addresses:
                    print(f"   ID: {addr.id} - {addr.street}, {addr.city}")
                    all_customer_addresses.append(addr)
            else:
                print(f"\n👤 {customer.user_name}: No addresses")
        
        return all_customer_addresses

def main():
    """Main function"""
    print("🚀 Starting address validation and fix...")
    
    # Check all addresses
    all_addresses = check_all_addresses()
    
    # Check specific problematic address
    print(f"\n🔍 Checking problematic address ID 2...")
    check_specific_address(2)
    
    # Validate common address IDs
    validate_order_addresses()
    
    # Get all customer addresses
    customer_addresses = get_customer_addresses()
    
    # Create test addresses if needed
    create_test_addresses()
    
    # Final summary
    print(f"\n📊 Summary:")
    print(f"   Total addresses: {all_addresses.count()}")
    print(f"   Customer addresses: {len(customer_addresses)}")
    
    # Suggest valid address IDs for testing
    if customer_addresses:
        valid_ids = [addr.id for addr in customer_addresses[:3]]
        print(f"   Valid address IDs for testing: {valid_ids}")
    
    print(f"\n✅ Address validation complete!")

if __name__ == '__main__':
    main()
