#!/usr/bin/env python3
"""
Debug restaurant creation issue
"""

import requests
import json

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_restaurant_creation_debug():
    """Debug restaurant creation with detailed error reporting"""
    print("🔍 Debugging Restaurant Creation Issue...")
    print("=" * 60)
    
    # First, let's try to create a restaurant user and get a token
    # Use the existing user from the logs
    login_data = {
        "user_name": "bismullahwafadar5",  # From the logs
        "password": "TestPassword123"  # Assuming this password
    }
    
    print(f"🔐 Attempting login with user: {login_data['user_name']}")
    
    try:
        login_response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=HEADERS,
            data=json.dumps(login_data)
        )
        
        print(f"📡 Login Response Status: {login_response.status_code}")
        print(f"📄 Login Response: {login_response.text}")
        
        if login_response.status_code != 200:
            print("❌ Login failed. Let's try with a different approach...")
            return test_without_auth()
        
        login_result = login_response.json()
        token = login_result['data']['access_token']
        
        # Update headers with token
        auth_headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        print("✅ Login successful, testing restaurant creation...")
        
        # Test restaurant creation with minimal data first
        minimal_restaurant = {
            "name": "Debug Test Restaurant",
            "description": "A test restaurant for debugging",
            "address": "Test Address, Kabul",
            "contact_number": "+93701234567",
            "opening_time": "09:00",
            "closing_time": "22:00",
            "delivery_fee": "50",
        }
        
        print(f"\n🏗️ Testing minimal restaurant creation...")
        
        restaurant_response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            data=json.dumps(minimal_restaurant)
        )
        
        print(f"📡 Restaurant Creation Status: {restaurant_response.status_code}")
        print(f"📄 Restaurant Creation Response: {restaurant_response.text}")
        
        if restaurant_response.status_code != 201:
            print("\n❌ Minimal restaurant creation failed")
            try:
                error_data = restaurant_response.json()
                print(f"🔍 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"🔍 Raw Error: {restaurant_response.text}")
        else:
            print("✅ Minimal restaurant creation successful!")
            
    except Exception as e:
        print(f"❌ Exception during debug: {e}")

def test_without_auth():
    """Test restaurant creation without authentication to see the error"""
    print("\n🔍 Testing without authentication to see error format...")
    
    test_restaurant = {
        "name": "Test Restaurant",
        "description": "Test description",
        "address": "Test address",
        "contact_number": "+93701234567",
        "opening_time": "09:00",
        "closing_time": "22:00",
        "delivery_fee": "50",
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=HEADERS,
            data=json.dumps(test_restaurant)
        )
        
        print(f"📡 No-Auth Response Status: {response.status_code}")
        print(f"📄 No-Auth Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Exception: {e}")

def check_restaurant_model_fields():
    """Check what fields are expected by getting existing restaurants"""
    print("\n🔍 Checking Restaurant Model Fields...")
    
    try:
        response = requests.get(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=HEADERS
        )
        
        print(f"📡 Get Restaurants Status: {response.status_code}")
        
        if response.status_code == 200:
            restaurants = response.json()
            print(f"📊 Found {len(restaurants)} restaurants")
            
            if restaurants:
                print("\n📋 Sample Restaurant Structure:")
                sample = restaurants[0]
                for key, value in sample.items():
                    print(f"   {key}: {type(value).__name__} = {value}")
            else:
                print("📭 No restaurants found - can't see field structure")
        else:
            print(f"❌ Failed to get restaurants: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def main():
    """Main debug function"""
    print("🧪 Restaurant Creation Debug Tool")
    print("=" * 60)
    
    # Check existing restaurants first
    check_restaurant_model_fields()
    
    # Test restaurant creation
    test_restaurant_creation_debug()
    
    print("\n" + "=" * 60)
    print("🏁 Debug Complete")

if __name__ == "__main__":
    main()
