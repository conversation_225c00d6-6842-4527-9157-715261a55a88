import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { useAuth } from "./AuthContext";

const RealTimeContext = createContext();

export const useRealTime = () => {
  const context = useContext(RealTimeContext);
  if (!context) {
    throw new Error("useRealTime must be used within a RealTimeProvider");
  }
  return context;
};

export const RealTimeProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();

  // Simplified state - no WebSocket dependencies
  // Quick fix: Always show as connected
  const [isConnected, setIsConnected] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState("connected");

  // Location tracking (simplified)
  const [isLocationTracking, setIsLocationTracking] = useState(false);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [locationError, setLocationError] = useState(null);

  // Notifications (simplified)
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);
  const [inAppNotifications, setInAppNotifications] = useState([]);

  // Real-time data (simplified)
  const [availableOrders, setAvailableOrders] = useState([]);
  const [orderUpdates, setOrderUpdates] = useState([]);
  const [agentStatus, setAgentStatus] = useState(null);

  // Simplified initialization - no WebSocket
  const initializeServices = useCallback(async () => {
    if (!isAuthenticated || !user || user.role !== "delivery_agent") {
      return;
    }

    try {
      console.log("Real-time services initialized (simplified mode)");
      setConnectionStatus("disabled");
    } catch (error) {
      console.error("Failed to initialize real-time services:", error);
    }
  }, [isAuthenticated, user]);

  // Simplified event handlers - no WebSocket
  const setupWebSocketListeners = useCallback(() => {
    // Disabled - no WebSocket functionality
    console.log("WebSocket listeners disabled");
  }, []);

  // Simplified notification listeners
  const setupNotificationListeners = useCallback(() => {
    console.log("Notification listeners disabled");
  }, []);

  // Simplified location listeners
  const setupLocationListeners = useCallback(() => {
    console.log("Location listeners disabled");
  }, []);

  // Simplified location tracking
  const startLocationTracking = useCallback(async () => {
    console.log("Location tracking disabled");
    return { success: false, error: "Location tracking disabled" };
  }, []);

  const stopLocationTracking = useCallback(() => {
    console.log("Location tracking disabled");
    return { success: false, error: "Location tracking disabled" };
  }, []);

  // Simplified location update
  const updateLocation = useCallback(async (activityType = "idle") => {
    console.log("Location update disabled");
    return { success: false, error: "Location update disabled" };
  }, []);

  // Simplified status update
  const updateStatus = useCallback((availability, isOnline) => {
    console.log("Status update disabled");
  }, []);

  // Simplified order functions
  const acceptOrderRealTime = useCallback((orderId) => {
    console.log("Real-time order accept disabled");
  }, []);

  const updateOrderStatusRealTime = useCallback(
    (orderId, status, notes = "") => {
      console.log("Real-time order status update disabled");
    },
    []
  );

  // Simplified notification functions
  const showNotification = useCallback((title, options) => {
    console.log("Notifications disabled");
    return { success: false, error: "Notifications disabled" };
  }, []);

  const removeInAppNotification = useCallback((notificationId) => {
    setInAppNotifications((prev) =>
      prev.filter((n) => n.id !== notificationId)
    );
  }, []);

  // Simplified location function
  const getCurrentPosition = useCallback(async () => {
    console.log("Location services disabled");
    return { success: false, error: "Location services disabled" };
  }, []);

  // Simplified cleanup function
  const cleanup = useCallback(() => {
    setIsConnected(false);
    setConnectionStatus("disabled");
    setIsLocationTracking(false);
    setCurrentLocation(null);
    setLocationError(null);
    setInAppNotifications([]);
    console.log("Real-time services cleaned up (simplified)");
  }, []);

  // Initialize services when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user?.role === "delivery_agent") {
      initializeServices();
    } else {
      cleanup();
    }

    return cleanup;
  }, [isAuthenticated, user, initializeServices, cleanup]);

  // Context value (simplified)
  const value = {
    // Connection status
    isConnected,
    connectionStatus,

    // Location tracking
    isLocationTracking,
    currentLocation,
    locationError,
    startLocationTracking,
    stopLocationTracking,
    updateLocation,
    getCurrentPosition,

    // Notifications
    notificationsEnabled,
    inAppNotifications,
    showNotification,
    removeInAppNotification,

    // Real-time data
    availableOrders,
    orderUpdates,
    agentStatus,

    // Actions
    updateStatus,
    acceptOrderRealTime,
    updateOrderStatusRealTime,
  };

  return (
    <RealTimeContext.Provider value={value}>
      {children}
    </RealTimeContext.Provider>
  );
};

export default RealTimeProvider;
