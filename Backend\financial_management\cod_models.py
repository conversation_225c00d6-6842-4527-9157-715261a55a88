"""
Cash on Delivery (COD) Financial Models

This module implements the COD-specific financial models for Afghan Sofra
where delivery agents collect cash from customers and remit to the platform.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from django.utils import timezone
from decimal import Decimal
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class CODTransaction(models.Model):
    """Track individual cash on delivery transactions"""
    order = models.OneToOneField(
        'orders.Order',
        on_delete=models.CASCADE,
        related_name='cod_transaction'
    )
    delivery_agent = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='cod_transactions',
        limit_choices_to={'role': 'delivery_agent'}
    )
    
    # Cash collection details
    order_total = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Total order amount to collect"
    )
    cash_collected = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Actual cash collected from customer"
    )
    customer_paid_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Amount customer paid (may be more than order total)"
    )
    change_given = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Change given to customer"
    )
    
    # Collection timing
    collection_time = models.DateTimeField(
        help_text="When cash was collected from customer"
    )
    reported_time = models.DateTimeField(
        auto_now_add=True,
        help_text="When agent reported the collection"
    )
    
    # Agent earnings from this transaction
    agent_delivery_earnings = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        help_text="Agent's earnings from this delivery"
    )
    amount_to_remit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Amount agent needs to remit to platform"
    )
    
    # Settlement status
    SETTLEMENT_STATUS_CHOICES = [
        ('pending', 'Pending Settlement'),
        ('settled', 'Settled'),
        ('disputed', 'Disputed'),
        ('lost', 'Lost/Stolen'),
    ]
    settlement_status = models.CharField(
        max_length=20,
        choices=SETTLEMENT_STATUS_CHOICES,
        default='pending'
    )
    settled_at = models.DateTimeField(null=True, blank=True)
    settlement_reference = models.CharField(max_length=100, blank=True)
    
    # Verification and security
    gps_latitude = models.DecimalField(
        max_digits=10,
        decimal_places=7,
        null=True,
        blank=True,
        help_text="GPS latitude of collection location"
    )
    gps_longitude = models.DecimalField(
        max_digits=10,
        decimal_places=7,
        null=True,
        blank=True,
        help_text="GPS longitude of collection location"
    )
    photo_verification = models.ImageField(
        upload_to='cod_verification/',
        blank=True,
        help_text="Photo proof of delivery/collection"
    )
    customer_signature = models.ImageField(
        upload_to='cod_signatures/',
        blank=True,
        help_text="Customer signature confirmation"
    )
    
    # Notes and issues
    collection_notes = models.TextField(
        blank=True,
        help_text="Any notes about the collection process"
    )
    issues_reported = models.TextField(
        blank=True,
        help_text="Any issues during collection"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-collection_time']
        indexes = [
            models.Index(fields=['delivery_agent', 'collection_time']),
            models.Index(fields=['settlement_status', 'collection_time']),
        ]

    def __str__(self):
        return f"COD #{self.order.id} - {self.delivery_agent.email} - ${self.cash_collected}"

    def calculate_amounts(self):
        """Calculate agent earnings and remittance amount"""
        # Get agent's delivery earnings (calculated separately)
        try:
            delivery_earnings = self.order.delivery_earnings
            self.agent_delivery_earnings = delivery_earnings.net_earnings
        except:
            # Fallback calculation
            self.agent_delivery_earnings = Decimal('5.00')  # Default delivery fee
        
        # Amount to remit = Total collected - Agent earnings
        self.amount_to_remit = self.cash_collected - self.agent_delivery_earnings
        self.save()

    def mark_as_settled(self, settlement_method, reference=None):
        """Mark transaction as settled"""
        self.settlement_status = 'settled'
        self.settled_at = timezone.now()
        self.settlement_reference = reference or f"COD-{self.id}-{timezone.now().strftime('%Y%m%d')}"
        self.save()
        
        logger.info(f"COD transaction {self.id} marked as settled via {settlement_method}")


class DailyAgentSettlement(models.Model):
    """Daily cash settlement records for delivery agents"""
    delivery_agent = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='daily_settlements',
        limit_choices_to={'role': 'delivery_agent'}
    )
    settlement_date = models.DateField()
    
    # Daily collection totals
    total_orders = models.PositiveIntegerField(
        default=0,
        help_text="Number of COD orders completed"
    )
    total_cash_collected = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Total cash collected from customers"
    )
    total_agent_earnings = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text="Total agent earnings for the day"
    )
    total_remittance_due = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Total amount agent needs to remit"
    )
    
    # Settlement details
    SETTLEMENT_METHOD_CHOICES = [
        ('bank_deposit', 'Bank Deposit'),
        ('digital_transfer', 'Digital Transfer'),
        ('office_dropoff', 'Office Drop-off'),
        ('mobile_money', 'Mobile Money'),
        ('cash_pickup', 'Cash Pickup'),
    ]
    settlement_method = models.CharField(
        max_length=20,
        choices=SETTLEMENT_METHOD_CHOICES,
        blank=True
    )
    settlement_amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        help_text="Actual amount settled by agent"
    )
    settlement_reference = models.CharField(
        max_length=100,
        blank=True,
        help_text="Bank reference, transaction ID, etc."
    )
    
    # Status tracking
    SETTLEMENT_STATUS_CHOICES = [
        ('pending', 'Pending Settlement'),
        ('partial', 'Partially Settled'),
        ('completed', 'Completed'),
        ('overdue', 'Overdue'),
        ('disputed', 'Disputed'),
    ]
    status = models.CharField(
        max_length=20,
        choices=SETTLEMENT_STATUS_CHOICES,
        default='pending'
    )
    
    # Verification
    is_verified = models.BooleanField(default=False)
    verified_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='verified_settlements',
        limit_choices_to={'role': 'admin'}
    )
    verified_at = models.DateTimeField(null=True, blank=True)
    
    # Timing
    settlement_deadline = models.DateTimeField(
        help_text="Deadline for agent to settle cash"
    )
    settled_at = models.DateTimeField(null=True, blank=True)
    
    # Notes
    notes = models.TextField(blank=True)
    admin_notes = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-settlement_date']
        unique_together = ['delivery_agent', 'settlement_date']
        indexes = [
            models.Index(fields=['settlement_date', 'status']),
            models.Index(fields=['delivery_agent', 'settlement_date']),
        ]

    def __str__(self):
        return f"{self.delivery_agent.email} - {self.settlement_date} - ${self.total_remittance_due}"

    def calculate_daily_totals(self):
        """Calculate daily totals from COD transactions"""
        transactions = CODTransaction.objects.filter(
            delivery_agent=self.delivery_agent,
            collection_time__date=self.settlement_date
        )
        
        self.total_orders = transactions.count()
        self.total_cash_collected = sum(t.cash_collected for t in transactions)
        self.total_agent_earnings = sum(t.agent_delivery_earnings for t in transactions)
        self.total_remittance_due = self.total_cash_collected - self.total_agent_earnings
        
        self.save()
        return self

    def mark_as_settled(self, amount, method, reference=None):
        """Mark settlement as completed"""
        self.settlement_amount = amount
        self.settlement_method = method
        self.settlement_reference = reference or f"SETTLE-{self.id}-{timezone.now().strftime('%Y%m%d')}"
        self.settled_at = timezone.now()
        
        # Update status based on amount
        if amount >= self.total_remittance_due:
            self.status = 'completed'
        elif amount > 0:
            self.status = 'partial'
        
        self.save()
        
        # Mark individual transactions as settled
        CODTransaction.objects.filter(
            delivery_agent=self.delivery_agent,
            collection_time__date=self.settlement_date,
            settlement_status='pending'
        ).update(
            settlement_status='settled',
            settled_at=timezone.now(),
            settlement_reference=self.settlement_reference
        )
        
        logger.info(f"Daily settlement {self.id} marked as settled: ${amount} via {method}")


class AgentCashFloat(models.Model):
    """Track cash float provided to delivery agents for making change"""
    delivery_agent = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='cash_floats',
        limit_choices_to={'role': 'delivery_agent'}
    )
    
    # Float details
    float_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        help_text="Amount of cash float provided"
    )
    float_date = models.DateField()
    
    # Denominations breakdown
    notes_breakdown = models.JSONField(
        default=dict,
        help_text="Breakdown of notes provided (e.g., {'1000': 5, '500': 10})"
    )
    coins_breakdown = models.JSONField(
        default=dict,
        help_text="Breakdown of coins provided"
    )
    
    # Status
    FLOAT_STATUS_CHOICES = [
        ('issued', 'Issued'),
        ('active', 'Active'),
        ('returned', 'Returned'),
        ('lost', 'Lost'),
    ]
    status = models.CharField(
        max_length=20,
        choices=FLOAT_STATUS_CHOICES,
        default='issued'
    )
    
    # Return details
    returned_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True
    )
    returned_at = models.DateTimeField(null=True, blank=True)
    
    # Tracking
    issued_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='issued_floats',
        limit_choices_to={'role': 'admin'}
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-float_date']
        indexes = [
            models.Index(fields=['delivery_agent', 'float_date']),
            models.Index(fields=['status', 'float_date']),
        ]

    def __str__(self):
        return f"{self.delivery_agent.email} - Float ${self.float_amount} - {self.status}"


class CODFinancialSummary(models.Model):
    """Daily/weekly/monthly COD financial summaries"""
    
    PERIOD_TYPE_CHOICES = [
        ('daily', 'Daily'),
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
    ]
    period_type = models.CharField(max_length=20, choices=PERIOD_TYPE_CHOICES)
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    
    # Overall metrics
    total_cod_orders = models.PositiveIntegerField(default=0)
    total_cash_collected = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_agent_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_restaurant_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_platform_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Settlement metrics
    total_settlements = models.PositiveIntegerField(default=0)
    pending_settlements = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    overdue_settlements = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Performance metrics
    collection_success_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    settlement_success_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    average_order_value = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-period_start']
        unique_together = ['period_type', 'period_start']

    def __str__(self):
        return f"COD Summary - {self.period_type} - {self.period_start.date()}"
