#!/usr/bin/env python3
"""
Test all delivery agent profiles
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_user_profile(username, password, description):
    """Test profile for a specific user"""
    print(f"\n🧪 Testing {description}")
    print("-" * 40)
    
    # Login
    login_data = {"user_name": username, "password": password}
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
        if response.status_code == 200:
            login_result = response.json()
            access_token = login_result['data']['access_token']
            user_info = login_result['data']['user']
            print(f"✅ Login: {user_info['name']} (Role: {user_info['role']})")
        else:
            print(f"❌ Login failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Test profile
    headers = {"Authorization": f"Bearer {access_token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/delivery-agent/profile/", headers=headers)
        if response.status_code == 200:
            profile_data = response.json()['data']
            print(f"✅ Profile: {profile_data['agent_id']} - {profile_data['full_name']}")
            print(f"   Status: {profile_data['employment_status']}")
            print(f"   Vehicle: {profile_data['vehicle_type']} - {profile_data['license_plate']}")
            return True
        else:
            print(f"❌ Profile failed: {response.status_code}")
            print(f"   Error: {response.json()}")
            return False
    except Exception as e:
        print(f"❌ Profile error: {e}")
        return False

def main():
    """Test all delivery agent profiles"""
    print("🚀 Testing All Delivery Agent Profiles")
    print("=" * 50)
    
    test_cases = [
        ("EMP001", "employee123", "Test Employee"),
        ("delivery", "delivery123", "Delivery Agent Jan"),
        ("admin", "admin123", "Admin User (should fail)")
    ]
    
    results = []
    for username, password, description in test_cases:
        success = test_user_profile(username, password, description)
        results.append((description, success))
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    for description, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {description}")
    
    delivery_agents_working = sum(1 for desc, success in results if success and "Admin" not in desc)
    print(f"\n🎯 {delivery_agents_working}/2 delivery agent profiles working")
    
    if delivery_agents_working == 2:
        print("✅ All delivery agent profiles are working!")
        print("The 'Failed to load profile' error should be resolved.")
    else:
        print("⚠️  Some profiles still have issues.")

if __name__ == "__main__":
    main()
