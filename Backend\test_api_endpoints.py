#!/usr/bin/env python
"""
Test API endpoints for dynamic delivery features
Run this while the Django server is running
"""

import requests
import json

def test_dynamic_delivery_fee():
    """Test dynamic delivery fee calculation API"""
    print("🧪 Testing Dynamic Delivery Fee API...")
    
    url = 'http://localhost:8000/api/delivery-agent/dynamic-delivery-fee/'
    data = {
        'restaurant_location': {'lat': 34.5553, 'lng': 69.2075},
        'delivery_location': {'lat': 34.5560, 'lng': 69.2080},
        'order_value': 25.00,
        'priority': 'normal'
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Response: {json.dumps(result, indent=2)}")
            
            if result.get('status') == 'success':
                fee_data = result.get('data', {})
                print(f"✅ Dynamic Fee: ${fee_data.get('total_fee', 'N/A')}")
                print(f"✅ Distance: {fee_data.get('distance_info', {}).get('distance_km', 'N/A')} km")
                print(f"✅ Estimated Time: {fee_data.get('estimated_delivery_time', 'N/A')}")
            else:
                print(f"❌ API Error: {result.get('message', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure Django server is running on port 8000")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_express_priority():
    """Test express priority fee calculation"""
    print("\n🧪 Testing Express Priority Fee...")
    
    url = 'http://localhost:8000/api/delivery-agent/dynamic-delivery-fee/'
    data = {
        'restaurant_location': {'lat': 34.5553, 'lng': 69.2075},
        'delivery_location': {'lat': 34.5560, 'lng': 69.2080},
        'order_value': 25.00,
        'priority': 'express'
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                fee_data = result.get('data', {})
                print(f"✅ Express Fee: ${fee_data.get('total_fee', 'N/A')}")
                
                # Check multipliers
                breakdown = fee_data.get('breakdown', {})
                multipliers = breakdown.get('multipliers', {})
                print(f"✅ Priority Multiplier: {multipliers.get('priority_multiplier', 'N/A')}")
            else:
                print(f"❌ API Error: {result.get('message', 'Unknown error')}")
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_server_health():
    """Test if server is running and responsive"""
    print("\n🧪 Testing Server Health...")
    
    try:
        response = requests.get('http://localhost:8000/api/', timeout=5)
        print(f"✅ Server is running (Status: {response.status_code})")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Dynamic Delivery API Endpoints...")
    print("=" * 50)
    
    if test_server_health():
        test_dynamic_delivery_fee()
        test_express_priority()
    else:
        print("\n💡 To start the server, run: python manage.py runserver 8000")
    
    print("\n" + "=" * 50)
    print("✅ API Testing Complete!")
