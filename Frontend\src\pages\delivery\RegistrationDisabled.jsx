import React from 'react';
import { Link } from 'react-router-dom';
import { 
  UserX, 
  AlertCircle, 
  Home, 
  Phone, 
  Mail,
  Building2
} from 'lucide-react';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

const RegistrationDisabled = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <div className="p-8 text-center">
            {/* Icon */}
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
              <UserX className="h-8 w-8 text-red-600" />
            </div>
            
            {/* Title */}
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Registration Not Available
            </h2>
            
            {/* Message */}
            <div className="space-y-4 text-gray-600">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                <p className="text-left">
                  Delivery agent self-registration is no longer available. 
                  We now operate with a professional employee-based delivery system.
                </p>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
                <h3 className="font-semibold text-blue-900 mb-2">
                  Interested in joining our team?
                </h3>
                <p className="text-blue-800 text-sm">
                  Delivery agent positions are now full-time employee roles with:
                </p>
                <ul className="text-blue-800 text-sm mt-2 space-y-1">
                  <li>• Fixed monthly salary</li>
                  <li>• Performance bonuses</li>
                  <li>• Training and support</li>
                  <li>• Equipment provided</li>
                </ul>
              </div>
            </div>
            
            {/* Contact Information */}
            <div className="mt-6 space-y-3">
              <h3 className="font-semibold text-gray-900">Contact Us for Employment</h3>
              
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-center justify-center space-x-2">
                  <Phone className="h-4 w-4" />
                  <span>+93 70 123 4567</span>
                </div>
                
                <div className="flex items-center justify-center space-x-2">
                  <Mail className="h-4 w-4" />
                  <span><EMAIL></span>
                </div>
                
                <div className="flex items-center justify-center space-x-2">
                  <Building2 className="h-4 w-4" />
                  <span>Visit our office in Kabul</span>
                </div>
              </div>
            </div>
            
            {/* Actions */}
            <div className="mt-8 space-y-3">
              <Button
                as={Link}
                to="/"
                variant="primary"
                icon={<Home size={16} />}
                className="w-full"
              >
                Return to Home
              </Button>
              
              <Button
                as={Link}
                to="/contact"
                variant="outline"
                className="w-full"
              >
                Contact Support
              </Button>
            </div>
            
            {/* Additional Info */}
            <div className="mt-6 text-xs text-gray-500">
              <p>
                This change ensures better service quality and employee benefits. 
                Thank you for your understanding.
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default RegistrationDisabled;
