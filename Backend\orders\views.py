# orders/views.py
import logging
from django.http import Http404
from django.shortcuts import get_object_or_404

from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from django.db.models import Q, Avg, Count, Case, When, IntegerField
from rest_framework.decorators import action
from rest_framework.response import Response
from django.core.exceptions import PermissionDenied
from api.permissions import IsAssignedDeliveryAgent
from users.models import User
from .models import Order, OrderItem, OrderStatusHistory, RejectionLog, SavedCart, Rating
from .serializers import AssignDeliveryAgentSerializer, OrderSerializer, OrderDetailSerializer, OrderItemSerializer, OrderStatusHistorySerializer, RejectDeliverySerializer, SavedCartSerializer, RatingSerializer, RatingDetailSerializer, RestaurantRatingsSummarySerializer

logger = logging.getLogger(__name__)

class OrderViewSet(viewsets.ModelViewSet):
    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'put', 'patch', 'delete']  # Explicitly allow POST
    filterset_fields = ['status', 'customer', 'restaurant', 'payment_status']
    search_fields = ['transaction_id', 'special_instructions']
    ordering_fields = ['created_at', 'total_amount']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Use detailed serializer for read operations"""
        if self.action in ['list', 'retrieve']:
            return OrderDetailSerializer
        return OrderSerializer

    def create(self, request, *args, **kwargs):
        """Create order with detailed error handling"""
        try:
            print(f"🔍 Order creation request data: {request.data}")
            print(f"🔍 User: {request.user}")
            print(f"🔍 User role: {request.user.role}")

            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                print(f"❌ Serializer validation errors: {serializer.errors}")
                return Response(
                    {
                        'error': 'Validation failed',
                        'details': serializer.errors
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )

            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)
            print(f"✅ Order created successfully: {serializer.data}")
            return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

        except Exception as e:
            print(f"❌ Order creation error: {str(e)}")
            print(f"❌ Error type: {type(e)}")
            import traceback
            print(f"❌ Traceback: {traceback.format_exc()}")
            return Response(
                {
                    'error': 'Order creation failed',
                    'details': str(e)
                },
                status=status.HTTP_400_BAD_REQUEST
            )

    def get_queryset(self):
        user = self.request.user
        if user.role == 'customer':
            return Order.objects.filter(customer=user)
        elif user.role == 'restaurant':
            return Order.objects.filter(restaurant__owner=user)
        elif user.role == 'delivery_agent':
            return Order.objects.filter(delivery_agent=user)
        return super().get_queryset()

    def perform_create(self, serializer):
        if self.request.user.role == 'customer':
            serializer.save(customer=self.request.user)
        else:
            serializer.save()

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        
        # Verify ownership for non-superusers
        user = request.user
        has_permission = (
            user == instance.customer or  # Customer can edit their own orders
            (user.role == 'restaurant' and instance.restaurant.owner == user) or  # Restaurant owner can edit their orders
            user.is_superuser  # Superuser can edit any order
        )

        if not has_permission:
            return Response(
                {'error': 'You do not have permission to edit this order'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Handle status changes separately
        new_status = request.data.get('status')
        if new_status:
            return self._handle_status_change(instance, new_status)
        
        # Normal partial update
        serializer = self.get_serializer(
            instance, 
            data=request.data, 
            partial=True
        )
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        
        return Response(serializer.data)

    def _handle_status_change(self, order, new_status):
        """Handle special status transition logic"""
        valid_transitions = {
            'pending': ['confirmed', 'cancelled'],
            'confirmed': ['preparing', 'cancelled', 'delivered'],
            'preparing': ['ready', 'cancelled'],
            'ready': ['assigned', 'cancelled'],
            'assigned': ['picked_up', 'cancelled'],
            'picked_up': ['on_the_way', 'cancelled'],
            'on_the_way': ['delivered', 'cancelled'],
            'delivered': [],  # Final state
            'cancelled': [],  # Final state
        }

        # Validate transition
        if new_status not in valid_transitions.get(order.status, []):
            return Response(
                {'error': f'Cannot change status from {order.status} to {new_status}'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Special case: cancellation
        if new_status == 'cancelled':
            return self._cancel_order(order)

        # Store who made the change for tracking
        order._changed_by = self.request.user
        order._status_change_notes = f"Status changed from {order.status} to {new_status}"

        # Normal status update
        order.status = new_status
        order.save()

        # Log the status change
        logger.info(
            f"Order {order.id} status changed to {new_status} by {self.request.user.email}"
        )

        return Response(self.get_serializer(order).data)

    def _cancel_order(self, order):
        """Handle order cancellation"""
        if order.status == 'delivered':
            return Response(
                {'error': 'Cannot cancel already delivered order'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        order.status = 'cancelled'
        
        # Add refund logic if needed
        if order.payment_status == 'completed':
            order.payment_status = 'refunded'
        
        order.save()
        return Response(self.get_serializer(order).data)

class OrderItemViewSet(viewsets.ModelViewSet):
    queryset = OrderItem.objects.all()
    serializer_class = OrderItemSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        order_id = self.kwargs.get('order_id')
        if order_id:
            return OrderItem.objects.filter(order_id=order_id)
        return super().get_queryset()
    

from rest_framework.decorators import action



class CartViewSet(viewsets.GenericViewSet):
    serializer_class = SavedCartSerializer
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'put','patch', 'delete']

    def get_object(self):
        cart, created = SavedCart.objects.get_or_create(user=self.request.user)

        # Auto-fix cart restaurant if missing but has items
        if not cart.restaurant and cart.items.exists():
            first_item = cart.items.first()
            cart.restaurant = first_item.menu_item.restaurant
            cart.save()
            print(f"🔧 Auto-fixed cart restaurant to: {cart.restaurant.name} (ID: {cart.restaurant.id})")

        return cart

    @action(detail=False, methods=['get', 'put'])
    def mine(self, request):
        cart = self.get_object()
        if request.method == 'PUT':
            serializer = self.get_serializer(cart, data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        else:
            serializer = self.get_serializer(cart)
            return Response(serializer.data)
        
    def destroy(self, request, *args, **kwargs):
        cart = self.get_object()
        cart.items.all().delete()  # Clear all items
        cart.restaurant = None     # Optional: Clear restaurant association
        # cart.save()                # Preserves cart record but empty
        cart.delete()        # Completely removes cart record
        return Response(status=status.HTTP_204_NO_CONTENT)
    

class OrderStatusHistoryViewSet(viewsets.ReadOnlyModelViewSet):
    serializer_class = OrderStatusHistorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        order_id = self.kwargs['order_id']
        return OrderStatusHistory.objects.filter(
            order_id=order_id
        ).select_related('changed_by')
    
from rest_framework.permissions import BasePermission

class IsAssignmentAuthorized(BasePermission):
    def has_permission(self, request, view):
        return request.user.role in ('admin', 'delivery_agent', 'delivery_coordinator') or request.user.is_superuser
    

class DeliveryAssignmentViewSet(viewsets.GenericViewSet):
    permission_classes = [IsAuthenticated, IsAssignmentAuthorized]
    
    @action(detail=False, methods=['post'])
    def assign(self, request):
        serializer = AssignDeliveryAgentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            with transaction.atomic():
                # Get order with status check
                order = get_object_or_404(
                    Order.objects.select_for_update(),
                    id=serializer.validated_data['order_id'],
                    status__in=['ready', 'reassigned']
                )

                # Check if user is restaurant owner and owns this order
                if request.user.role == 'restaurant_owner':
                    if not order.restaurant.owner == request.user:
                        return Response({
                            'status': 'error',
                            'message': 'You can only assign agents to your own orders'
                        }, status=status.HTTP_403_FORBIDDEN)

                # Get active delivery agent
                agent = get_object_or_404(
                    User.objects.filter(role='delivery_agent', is_active=True),
                    id=serializer.validated_data['agent_id']
                )

                # Check if agent is available
                try:
                    agent_profile = DeliveryAgentProfile.objects.get(user=agent)
                    if agent_profile.availability != 'available':
                        return Response({
                            'status': 'error',
                            'message': f'Agent is currently {agent_profile.availability}'
                        }, status=status.HTTP_400_BAD_REQUEST)
                except DeliveryAgentProfile.DoesNotExist:
                    return Response({
                        'status': 'error',
                        'message': 'Agent profile not found'
                    }, status=status.HTTP_404_NOT_FOUND)

                # Perform assignment
                order.assign_agent(agent, request)

                return Response({
                    'status': 'success',
                    'order_id': order.id,
                    'agent_id': agent.id,
                    'agent_name': agent.name,
                    'new_status': order.status,
                    'message': 'Order assigned successfully'
                }, status=status.HTTP_200_OK)

        except Http404 as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['get'])
    def available_agents(self, request):
        """Get available delivery agents for manual assignment"""
        try:
            # Get order ID to calculate distances
            order_id = request.query_params.get('order_id')

            if not order_id:
                return Response({
                    'status': 'error',
                    'message': 'order_id parameter is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the order
            try:
                order = Order.objects.get(id=order_id)

                # Check if user is restaurant owner and owns this order
                if request.user.role == 'restaurant_owner':
                    if not order.restaurant.owner == request.user:
                        return Response({
                            'status': 'error',
                            'message': 'You can only view agents for your own orders'
                        }, status=status.HTTP_403_FORBIDDEN)

            except Order.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': 'Order not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Get available delivery agents
            from deliveryAgent.models import DeliveryAgentProfile
            from deliveryAgent.geolocation_service import geolocation_service

            available_agents = DeliveryAgentProfile.objects.filter(
                status='active',
                availability='available',
                is_verified=True,
                user__is_active=True
            ).select_related('user')

            agents_data = []
            restaurant_location = {
                'lat': float(order.restaurant.address.latitude),
                'lng': float(order.restaurant.address.longitude)
            }

            for agent in available_agents:
                # Calculate distance if agent has location
                distance_info = None
                if agent.current_latitude and agent.current_longitude:
                    agent_location = {
                        'lat': float(agent.current_latitude),
                        'lng': float(agent.current_longitude)
                    }
                    distance_info = geolocation_service.calculate_distance(
                        restaurant_location, agent_location
                    )

                agent_data = {
                    'id': agent.user.id,
                    'name': agent.user.name,
                    'phone': agent.phone_number,
                    'vehicle_type': agent.vehicle_type,
                    'vehicle_model': agent.vehicle_model,
                    'license_plate': agent.license_plate,
                    'rating': float(agent.rating),
                    'total_deliveries': agent.total_deliveries,
                    'completion_rate': agent.completion_rate,
                    'current_location': {
                        'latitude': float(agent.current_latitude) if agent.current_latitude else None,
                        'longitude': float(agent.current_longitude) if agent.current_longitude else None,
                        'address': agent.current_address
                    },
                    'distance_info': distance_info,
                    'is_online': agent.is_online,
                    'last_seen': agent.last_seen
                }
                agents_data.append(agent_data)

            # Sort by distance (closest first)
            agents_data.sort(key=lambda x: x['distance_info']['distance_km'] if x['distance_info'] else float('inf'))

            return Response({
                'status': 'success',
                'data': {
                    'agents': agents_data,
                    'total_count': len(agents_data),
                    'order_info': {
                        'id': order.id,
                        'restaurant_name': order.restaurant.name,
                        'restaurant_location': restaurant_location,
                        'delivery_address': order.delivery_address.street + ', ' + order.delivery_address.city
                    }
                }
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['post'])
    def reject(self, request):
        serializer = RejectDeliverySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
   
        try:
            with transaction.atomic():
                # Get the order with lock
                order = Order.objects.select_for_update().get(
                    id=serializer.validated_data['order_id']
                )
                
                # Check permissions again (in case of race condition)
                if not IsAssignedDeliveryAgent().has_object_permission(request, self, order):
                    return Response(
                        {"detail": "You can only reject orders assigned to you"},
                        status=status.HTTP_403_FORBIDDEN
                    )
                
                # Process rejection
                rejection_reason = serializer.validated_data.get('reason', 'No reason provided')
                order._status_change_notes = f"Rejected: {serializer.validated_data.get('reason', 'No reason')}"
                order.unassign_agent(request)
                RejectionLog.objects.create(
                    order=order,
                    agent=request.user,
                    reason=rejection_reason
                )
                
                return Response({
                    "status": "rejected",
                    "order_id": order.id,
                    "new_status": order.status,
                    "attempts_remaining": 3 - order.assignment_attempts
                })
                
        except Order.DoesNotExist:
            return Response(
                {"detail": "Order not found"},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'])
    def available_orders(self, request):
        if request.user.role != 'delivery_agent':
            return Response(status=403)

        orders = Order.objects.filter(
            status='ready',
            assignment_attempts__lt=3  # Max 3 assignment attempts
        ).exclude(
            delivery_agent=request.user
        ).order_by('created_at')

        return Response(OrderSerializer(orders, many=True).data)

    @action(detail=False, methods=['post'])
    def auto_assign(self, request):
        """Manually trigger auto-assignment for ready orders"""
        if not request.user.is_staff:
            return Response(
                {"detail": "Only staff can trigger auto-assignment"},
                status=status.HTTP_403_FORBIDDEN
            )

        from .delivery_assignment import delivery_assignment_service

        order_id = request.data.get('order_id')

        if order_id:
            # Assign specific order
            try:
                order = Order.objects.get(id=order_id)
                result = delivery_assignment_service.auto_assign_order(order)
                return Response(result)
            except Order.DoesNotExist:
                return Response(
                    {"detail": "Order not found"},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            # Assign all ready orders
            ready_orders = Order.objects.filter(
                status='ready',
                delivery_agent__isnull=True
            )

            results = []
            for order in ready_orders:
                result = delivery_assignment_service.auto_assign_order(order)
                results.append(result)

            return Response({
                "total_orders": len(results),
                "successful_assignments": len([r for r in results if r['success']]),
                "failed_assignments": len([r for r in results if not r['success']]),
                "results": results
            })

    @action(detail=False, methods=['get'])
    def assignment_stats(self, request):
        """Get delivery assignment statistics"""
        if not request.user.is_staff:
            return Response(
                {"detail": "Only staff can view assignment statistics"},
                status=status.HTTP_403_FORBIDDEN
            )

        from .delivery_assignment import delivery_assignment_service
        stats = delivery_assignment_service.get_assignment_statistics()
        return Response(stats)


class RatingViewSet(viewsets.ModelViewSet):
    """ViewSet for managing order ratings"""
    serializer_class = RatingSerializer
    permission_classes = [IsAuthenticated]

    def get_serializer_class(self):
        """Use detailed serializer for read operations"""
        if self.action in ['list', 'retrieve', 'restaurant_ratings', 'my_ratings']:
            return RatingDetailSerializer
        return RatingSerializer

    def get_queryset(self):
        """Filter ratings based on user role"""
        user = self.request.user
        if user.role == 'customer':
            return Rating.objects.filter(customer=user)
        elif user.role == 'restaurant_owner':
            # Restaurant owners can see ratings for their restaurants
            return Rating.objects.filter(restaurant__owner=user)
        elif user.role == 'admin':
            return Rating.objects.all()
        return Rating.objects.none()

    def create(self, request, *args, **kwargs):
        """Create a rating for a delivered order"""
        order_id = request.data.get('order_id')

        if not order_id:
            return Response(
                {'error': 'order_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            order = Order.objects.get(id=order_id, customer=request.user)
        except Order.DoesNotExist:
            return Response(
                {'error': 'Order not found or you do not have permission to rate this order'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if order is delivered
        if order.status != 'delivered':
            return Response(
                {'error': 'You can only rate delivered orders'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if rating already exists
        if hasattr(order, 'rating'):
            return Response(
                {'error': 'You have already rated this order'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create rating
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            serializer.save(
                order=order,
                restaurant=order.restaurant,
                customer=request.user
            )
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def restaurant_summary(self, request):
        """Get rating summary for a restaurant"""
        restaurant_id = request.query_params.get('restaurant_id')

        if not restaurant_id:
            return Response(
                {'error': 'restaurant_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from restaurant.models import Restaurant
            restaurant = Restaurant.objects.get(id=restaurant_id)
        except Restaurant.DoesNotExist:
            return Response(
                {'error': 'Restaurant not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Calculate rating statistics
        ratings = Rating.objects.filter(restaurant=restaurant)

        if not ratings.exists():
            return Response({
                'restaurant_id': restaurant.id,
                'restaurant_name': restaurant.name,
                'total_ratings': 0,
                'average_food_rating': 0,
                'average_delivery_rating': 0,
                'average_overall_rating': 0,
                'average_rating': 0,
                'five_star_count': 0,
                'four_star_count': 0,
                'three_star_count': 0,
                'two_star_count': 0,
                'one_star_count': 0,
            })

        # Aggregate ratings
        aggregates = ratings.aggregate(
            total_ratings=Count('id'),
            avg_food=Avg('food_rating'),
            avg_delivery=Avg('delivery_rating'),
            avg_overall=Avg('overall_rating'),
            five_stars=Count(Case(When(overall_rating=5, then=1), output_field=IntegerField())),
            four_stars=Count(Case(When(overall_rating=4, then=1), output_field=IntegerField())),
            three_stars=Count(Case(When(overall_rating=3, then=1), output_field=IntegerField())),
            two_stars=Count(Case(When(overall_rating=2, then=1), output_field=IntegerField())),
            one_star=Count(Case(When(overall_rating=1, then=1), output_field=IntegerField())),
        )

        # Calculate overall average
        avg_rating = (
            (aggregates['avg_food'] or 0) +
            (aggregates['avg_delivery'] or 0) +
            (aggregates['avg_overall'] or 0)
        ) / 3

        summary_data = {
            'restaurant_id': restaurant.id,
            'restaurant_name': restaurant.name,
            'total_ratings': aggregates['total_ratings'],
            'average_food_rating': round(aggregates['avg_food'] or 0, 2),
            'average_delivery_rating': round(aggregates['avg_delivery'] or 0, 2),
            'average_overall_rating': round(aggregates['avg_overall'] or 0, 2),
            'average_rating': round(avg_rating, 2),
            'five_star_count': aggregates['five_stars'],
            'four_star_count': aggregates['four_stars'],
            'three_star_count': aggregates['three_stars'],
            'two_star_count': aggregates['two_stars'],
            'one_star_count': aggregates['one_star'],
        }

        return Response(summary_data)

    @action(detail=False, methods=['get'])
    def my_ratings(self, request):
        """Get current user's ratings"""
        if request.user.role != 'customer':
            return Response(
                {'error': 'Only customers can view their ratings'},
                status=status.HTTP_403_FORBIDDEN
            )

        ratings = Rating.objects.filter(customer=request.user).order_by('-created_at')
        serializer = self.get_serializer(ratings, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def restaurant_ratings(self, request):
        """Get all ratings for restaurants owned by the current user"""
        print(f"🔍 User: {request.user.user_name if hasattr(request.user, 'user_name') else 'Anonymous'}")
        print(f"🔍 User role: {getattr(request.user, 'role', 'No role')}")
        print(f"🔍 User authenticated: {request.user.is_authenticated}")

        if not request.user.is_authenticated:
            return Response(
                {'error': 'Authentication required'},
                status=status.HTTP_401_UNAUTHORIZED
            )

        if not hasattr(request.user, 'role') or request.user.role != 'restaurant':
            return Response(
                {'error': f'Only restaurant owners can view their restaurant ratings. Current role: {getattr(request.user, "role", "unknown")}'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get all restaurants owned by the user
        from restaurant.models import Restaurant
        restaurants = Restaurant.objects.filter(owner=request.user, is_active=True)

        # Get all ratings for these restaurants
        ratings = Rating.objects.filter(restaurant__in=restaurants).order_by('-created_at')

        # Add restaurant name to each rating for easier display
        ratings_data = []
        for rating in ratings:
            rating_data = self.get_serializer(rating).data
            rating_data['restaurant_name'] = rating.restaurant.name
            rating_data['customer_name'] = rating.customer.name
            rating_data['order_total'] = str(rating.order.total_amount)
            ratings_data.append(rating_data)

        return Response(ratings_data)

    @action(detail=False, methods=['get'])
    def can_rate_order(self, request):
        """Check if user can rate a specific order"""
        order_id = request.query_params.get('order_id')

        if not order_id:
            return Response(
                {'error': 'order_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            order = Order.objects.get(id=order_id, customer=request.user)
        except Order.DoesNotExist:
            return Response(
                {'can_rate': False, 'reason': 'Order not found or access denied'},
                status=status.HTTP_200_OK
            )

        # Check if order is delivered
        if order.status != 'delivered':
            return Response({
                'can_rate': False,
                'reason': 'Order must be delivered to rate',
                'order_status': order.status
            })

        # Check if already rated
        if hasattr(order, 'rating'):
            return Response({
                'can_rate': False,
                'reason': 'Order already rated',
                'existing_rating': self.get_serializer(order.rating).data
            })

        return Response({
            'can_rate': True,
            'order': {
                'id': order.id,
                'restaurant_name': order.restaurant.name,
                'total_amount': str(order.total_amount),
                'delivered_at': order.updated_at
            }
        })