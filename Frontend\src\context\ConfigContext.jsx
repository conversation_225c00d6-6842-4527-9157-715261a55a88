import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import configService from "../services/configService";

const ConfigContext = createContext();

export const useConfig = () => {
  const context = useContext(ConfigContext);
  if (!context) {
    throw new Error("useConfig must be used within a ConfigProvider");
  }
  return context;
};

// Global state to prevent multiple simultaneous loads
let globalLoadingPromise = null;
let globalConfig = null;

export const ConfigProvider = ({ children }) => {
  const [config, setConfig] = useState({
    settings: [],
    choiceOptions: {},
    filterConfigurations: [],
    loading: true,
    error: null,
  });

  const loadConfig = useCallback(async () => {
    // If already loading globally, wait for that promise
    if (globalLoadingPromise) {
      console.log("⏳ Config already loading globally, waiting...");
      try {
        const result = await globalLoadingPromise;
        setConfig(result);
        return result;
      } catch (error) {
        setConfig((prev) => ({
          ...prev,
          loading: false,
          error: error.message,
        }));
        throw error;
      }
    }

    // If we already have global config, use it
    if (globalConfig && !globalConfig.error) {
      console.log("📋 Using cached global config");
      setConfig(globalConfig);
      return globalConfig;
    }

    // Start new loading process
    console.log("🔄 Starting new config load...");
    setConfig((prev) => ({ ...prev, loading: true, error: null }));

    globalLoadingPromise = (async () => {
      try {
        console.log("🌐 Fetching config from API...");
        const allConfig = await configService.getAllConfig();

        const result = {
          ...allConfig,
          loading: false,
          error: null,
        };

        globalConfig = result;
        console.log("✅ Config loaded and cached globally");
        return result;
      } catch (error) {
        console.error("❌ Error loading configuration:", error);
        const errorResult = {
          settings: [],
          choiceOptions: {},
          filterConfigurations: [],
          loading: false,
          error: error.message,
        };
        globalConfig = errorResult;
        throw error;
      } finally {
        globalLoadingPromise = null;
      }
    })();

    try {
      const result = await globalLoadingPromise;
      setConfig(result);
      return result;
    } catch (error) {
      setConfig((prev) => ({
        ...prev,
        loading: false,
        error: error.message,
      }));
      throw error;
    }
  }, []);

  useEffect(() => {
    loadConfig();
  }, []); // Only load once on mount

  const getSetting = useCallback(
    (key) => {
      const setting = config.settings.find((s) => s.key === key);
      return setting ? setting.typed_value : null;
    },
    [config.settings]
  );

  const getChoiceOptions = useCallback(
    (type) => {
      return config.choiceOptions[type] || [];
    },
    [config.choiceOptions]
  );

  const refreshConfig = useCallback(() => {
    // Clear global cache
    globalConfig = null;
    globalLoadingPromise = null;
    configService.clearCache();
    loadConfig();
  }, [loadConfig]);

  const value = {
    ...config,
    getSetting,
    getChoiceOptions,
    refreshConfig,
    reload: loadConfig,
  };

  return (
    <ConfigContext.Provider value={value}>{children}</ConfigContext.Provider>
  );
};

export default ConfigProvider;
