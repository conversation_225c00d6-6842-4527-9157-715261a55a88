/**
 * Address Service - Robust address management with proper backend synchronization
 * This service handles all address operations and ensures data consistency
 */

import apiClient from './apiClient';

class AddressService {
  constructor() {
    this.STORAGE_KEY = 'afghanSofra_addresses';
    this.SELECTED_KEY = 'afghanSofra_selectedAddress';
    this.cache = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the service and sync with backend
   */
  async initialize() {
    if (this.isInitialized) return;
    
    try {
      console.log('🏠 AddressService: Initializing...');
      await this.syncWithBackend();
      this.isInitialized = true;
      console.log('✅ AddressService: Initialized successfully');
    } catch (error) {
      console.error('❌ AddressService: Initialization failed:', error);
      // Continue with cached data if backend fails
      this.loadFromStorage();
      this.isInitialized = true;
    }
  }

  /**
   * Sync addresses with backend and update local storage
   */
  async syncWithBackend() {
    try {
      console.log('🔄 AddressService: Syncing with backend...');
      const response = await apiClient.get('/restaurant/addresses/');
      
      if (response.data && Array.isArray(response.data)) {
        const backendAddresses = response.data.map(addr => this.normalizeAddress(addr, 'backend'));
        
        // Store in cache and localStorage
        this.cache.set('addresses', backendAddresses);
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(backendAddresses));
        
        console.log(`✅ AddressService: Synced ${backendAddresses.length} addresses from backend`);
        return backendAddresses;
      } else {
        console.warn('⚠️ AddressService: Invalid response format from backend');
        return [];
      }
    } catch (error) {
      console.error('❌ AddressService: Backend sync failed:', error);
      // Fallback to localStorage
      return this.loadFromStorage();
    }
  }

  /**
   * Load addresses from localStorage
   */
  loadFromStorage() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      const addresses = stored ? JSON.parse(stored) : [];
      this.cache.set('addresses', addresses);
      console.log(`📦 AddressService: Loaded ${addresses.length} addresses from storage`);
      return addresses;
    } catch (error) {
      console.error('❌ AddressService: Failed to load from storage:', error);
      return [];
    }
  }

  /**
   * Normalize address object to ensure consistent structure
   */
  normalizeAddress(address, source = 'unknown') {
    return {
      id: address.id,
      backendId: address.id, // Always use the same ID for backend addresses
      type: 'saved',
      label: address.label || `${address.street}, ${address.city}`,
      address: `${address.street}, ${address.city}, ${address.country}`,
      street: address.street,
      city: address.city,
      state: address.state,
      postal_code: address.postal_code,
      country: address.country,
      latitude: parseFloat(address.latitude),
      longitude: parseFloat(address.longitude),
      coordinates: [parseFloat(address.latitude), parseFloat(address.longitude)],
      isDefault: address.isDefault || false,
      source: source,
      createdAt: address.created_at || Date.now(),
      updatedAt: address.updated_at || Date.now()
    };
  }

  /**
   * Get all addresses
   */
  async getAddresses() {
    if (!this.isInitialized) {
      await this.initialize();
    }
    
    return this.cache.get('addresses') || [];
  }

  /**
   * Get address by ID
   */
  async getAddressById(id) {
    const addresses = await this.getAddresses();
    return addresses.find(addr => addr.id == id || addr.backendId == id);
  }

  /**
   * Create new address
   */
  async createAddress(addressData) {
    try {
      console.log('🏗️ AddressService: Creating new address:', addressData);
      
      // Validate required fields
      const required = ['street', 'city', 'state', 'postal_code', 'country', 'latitude', 'longitude'];
      for (const field of required) {
        if (!addressData[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Send to backend
      const response = await apiClient.post('/restaurant/addresses/', {
        street: addressData.street.substring(0, 255),
        city: addressData.city.substring(0, 100),
        state: addressData.state.substring(0, 100),
        postal_code: addressData.postal_code.substring(0, 20),
        country: addressData.country.substring(0, 100),
        latitude: addressData.latitude.toString(),
        longitude: addressData.longitude.toString()
      });

      if (response.data && response.data.id) {
        const newAddress = this.normalizeAddress(response.data, 'backend');
        
        // Update cache
        const addresses = await this.getAddresses();
        const updatedAddresses = [...addresses, newAddress];
        this.cache.set('addresses', updatedAddresses);
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedAddresses));
        
        console.log('✅ AddressService: Address created successfully:', newAddress);
        return { success: true, data: newAddress };
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('❌ AddressService: Failed to create address:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to create address'
      };
    }
  }

  /**
   * Update existing address
   */
  async updateAddress(id, updates) {
    try {
      console.log('📝 AddressService: Updating address:', id, updates);
      
      const response = await apiClient.put(`/restaurant/addresses/${id}/`, updates);
      
      if (response.data) {
        const updatedAddress = this.normalizeAddress(response.data, 'backend');
        
        // Update cache
        const addresses = await this.getAddresses();
        const updatedAddresses = addresses.map(addr => 
          (addr.id == id || addr.backendId == id) ? updatedAddress : addr
        );
        this.cache.set('addresses', updatedAddresses);
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedAddresses));
        
        console.log('✅ AddressService: Address updated successfully');
        return { success: true, data: updatedAddress };
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('❌ AddressService: Failed to update address:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to update address'
      };
    }
  }

  /**
   * Delete address
   */
  async deleteAddress(id) {
    try {
      console.log('🗑️ AddressService: Deleting address:', id);
      
      await apiClient.delete(`/restaurant/addresses/${id}/`);
      
      // Update cache
      const addresses = await this.getAddresses();
      const updatedAddresses = addresses.filter(addr => addr.id != id && addr.backendId != id);
      this.cache.set('addresses', updatedAddresses);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedAddresses));
      
      // Clear selected address if it was deleted
      const selectedAddress = this.getSelectedAddress();
      if (selectedAddress && (selectedAddress.id == id || selectedAddress.backendId == id)) {
        this.clearSelectedAddress();
      }
      
      console.log('✅ AddressService: Address deleted successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ AddressService: Failed to delete address:', error);
      return { 
        success: false, 
        error: error.response?.data?.message || error.message || 'Failed to delete address'
      };
    }
  }

  /**
   * Get selected address
   */
  getSelectedAddress() {
    try {
      const stored = localStorage.getItem(this.SELECTED_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('❌ AddressService: Failed to get selected address:', error);
      return null;
    }
  }

  /**
   * Set selected address
   */
  async setSelectedAddress(address) {
    try {
      if (!address) {
        this.clearSelectedAddress();
        return;
      }

      // Ensure we have a valid backend address
      let validAddress = address;
      if (!address.backendId && address.id) {
        validAddress = await this.getAddressById(address.id);
      }

      if (!validAddress || !validAddress.backendId) {
        throw new Error('Cannot select address without valid backend ID');
      }

      localStorage.setItem(this.SELECTED_KEY, JSON.stringify(validAddress));
      console.log('✅ AddressService: Selected address set:', validAddress.id);
      return validAddress;
    } catch (error) {
      console.error('❌ AddressService: Failed to set selected address:', error);
      return null;
    }
  }

  /**
   * Clear selected address
   */
  clearSelectedAddress() {
    localStorage.removeItem(this.SELECTED_KEY);
    console.log('🧹 AddressService: Selected address cleared');
  }

  /**
   * Validate address for order placement
   */
  validateAddressForOrder(address) {
    if (!address) {
      return { valid: false, error: 'No address provided' };
    }

    if (!address.backendId) {
      return { valid: false, error: 'Address must be saved to place an order' };
    }

    if (address.type !== 'saved') {
      return { valid: false, error: 'Only saved addresses can be used for orders' };
    }

    return { valid: true };
  }

  /**
   * Get valid address ID for order placement
   */
  async getValidOrderAddressId() {
    const selectedAddress = this.getSelectedAddress();
    
    if (selectedAddress) {
      const validation = this.validateAddressForOrder(selectedAddress);
      if (validation.valid) {
        return selectedAddress.backendId;
      }
    }

    // Fallback: get first available saved address
    const addresses = await this.getAddresses();
    const validAddress = addresses.find(addr => addr.type === 'saved' && addr.backendId);
    
    if (validAddress) {
      await this.setSelectedAddress(validAddress);
      return validAddress.backendId;
    }

    return null;
  }

  /**
   * Clear all data (for debugging)
   */
  clearAll() {
    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.SELECTED_KEY);
    this.cache.clear();
    this.isInitialized = false;
    console.log('🧹 AddressService: All data cleared');
  }
}

// Create singleton instance
const addressService = new AddressService();

export default addressService;
