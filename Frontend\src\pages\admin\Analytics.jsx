import React, { useState } from 'react';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  ShoppingBag, 
  DollarSign, 
  Calendar, 
  Download,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';

// Sample data for charts
const revenueData = [
  { name: 'Jan', revenue: 4000 },
  { name: 'Feb', revenue: 5000 },
  { name: 'Mar', revenue: 6000 },
  { name: 'Apr', revenue: 7000 },
  { name: 'May', revenue: 5500 },
  { name: 'Jun', revenue: 8000 },
  { name: 'Jul', revenue: 9000 },
  { name: 'Aug', revenue: 10000 },
  { name: 'Sep', revenue: 11000 },
  { name: 'Oct', revenue: 9500 },
  { name: 'Nov', revenue: 12000 },
  { name: 'Dec', revenue: 15000 },
];

const orderData = [
  { name: 'Jan', orders: 240 },
  { name: 'Feb', orders: 300 },
  { name: 'Mar', orders: 350 },
  { name: 'Apr', orders: 400 },
  { name: 'May', orders: 380 },
  { name: 'Jun', orders: 450 },
  { name: 'Jul', orders: 500 },
  { name: 'Aug', orders: 550 },
  { name: 'Sep', orders: 600 },
  { name: 'Oct', orders: 580 },
  { name: 'Nov', orders: 650 },
  { name: 'Dec', orders: 700 },
];

const userGrowthData = [
  { name: 'Jan', users: 1000 },
  { name: 'Feb', users: 1200 },
  { name: 'Mar', users: 1400 },
  { name: 'Apr', users: 1600 },
  { name: 'May', users: 1800 },
  { name: 'Jun', users: 2000 },
  { name: 'Jul', users: 2200 },
  { name: 'Aug', users: 2400 },
  { name: 'Sep', users: 2600 },
  { name: 'Oct', users: 2800 },
  { name: 'Nov', users: 3000 },
  { name: 'Dec', users: 3200 },
];

const cuisineData = [
  { name: 'Afghan', value: 35 },
  { name: 'Indian', value: 25 },
  { name: 'Italian', value: 15 },
  { name: 'Chinese', value: 10 },
  { name: 'American', value: 15 },
];

const COLORS = ['#FF6B00', '#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

function Analytics() {
  const [timeRange, setTimeRange] = useState('year');
  
  return (
    <div className="p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <h1 className="text-2xl font-bold mb-4 sm:mb-0">Analytics & Reports</h1>
        <div className="flex gap-4">
          <select 
            className="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <option value="week">Last 7 Days</option>
            <option value="month">Last 30 Days</option>
            <option value="quarter">Last 3 Months</option>
            <option value="year">Last 12 Months</option>
          </select>
          <Button
            variant="outline"
            icon={<Download size={18} />}
            onClick={() => alert('Downloading report...')}
          >
            Export Report
          </Button>
        </div>
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <div className="p-6">
            <div className="flex items-start">
              <div className="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mr-4">
                <DollarSign size={24} className="text-primary-600" />
              </div>
              <div>
                <h2 className="text-sm font-medium text-gray-600 mb-1">Total Revenue</h2>
                <div className="text-2xl font-bold">$92,500</div>
                <div className="flex items-center text-xs text-green-600 mt-1">
                  <ArrowUpRight size={14} className="mr-1" />
                  <span>+12.5% from last {timeRange}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-start">
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                <ShoppingBag size={24} className="text-blue-600" />
              </div>
              <div>
                <h2 className="text-sm font-medium text-gray-600 mb-1">Total Orders</h2>
                <div className="text-2xl font-bold">5,280</div>
                <div className="flex items-center text-xs text-green-600 mt-1">
                  <ArrowUpRight size={14} className="mr-1" />
                  <span>+8.3% from last {timeRange}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-start">
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-4">
                <Users size={24} className="text-green-600" />
              </div>
              <div>
                <h2 className="text-sm font-medium text-gray-600 mb-1">Total Users</h2>
                <div className="text-2xl font-bold">3,200</div>
                <div className="flex items-center text-xs text-green-600 mt-1">
                  <ArrowUpRight size={14} className="mr-1" />
                  <span>+15.2% from last {timeRange}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6">
            <div className="flex items-start">
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center mr-4">
                <TrendingUp size={24} className="text-yellow-600" />
              </div>
              <div>
                <h2 className="text-sm font-medium text-gray-600 mb-1">Avg. Order Value</h2>
                <div className="text-2xl font-bold">$17.50</div>
                <div className="flex items-center text-xs text-red-600 mt-1">
                  <ArrowDownRight size={14} className="mr-1" />
                  <span>-2.1% from last {timeRange}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
      
      {/* Revenue Chart */}
      <Card className="mb-8">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold">Revenue Overview</h2>
        </div>
        <div className="p-6">
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${value}`, 'Revenue']} />
                <Legend />
                <Line type="monotone" dataKey="revenue" stroke="#FF6B00" strokeWidth={2} activeDot={{ r: 8 }} />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </Card>
      
      {/* Order and User Growth Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <Card>
          <div className="p-6 border-b">
            <h2 className="text-lg font-semibold">Order Trends</h2>
          </div>
          <div className="p-6">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={orderData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="orders" fill="#0088FE" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="p-6 border-b">
            <h2 className="text-lg font-semibold">User Growth</h2>
          </div>
          <div className="p-6">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={userGrowthData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="users" stroke="#00C49F" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        </Card>
      </div>
      
      {/* Cuisine Distribution */}
      <Card>
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold">Cuisine Distribution</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="h-80 flex items-center justify-center">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={cuisineData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {cuisineData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value, name) => [`${value}%`, name]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
            
            <div>
              <h3 className="text-md font-medium mb-4">Top Performing Cuisines</h3>
              <div className="space-y-4">
                {cuisineData.map((cuisine, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: COLORS[index % COLORS.length] }}></div>
                      <span>{cuisine.name}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium">{cuisine.value}%</span>
                      <div className="w-24 h-2 bg-gray-200 rounded-full ml-3">
                        <div 
                          className="h-2 rounded-full" 
                          style={{ 
                            width: `${cuisine.value}%`, 
                            backgroundColor: COLORS[index % COLORS.length] 
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-8">
                <h3 className="text-md font-medium mb-4">Key Insights</h3>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-start">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary-500 mt-1.5 mr-2"></div>
                    <span>Afghan cuisine remains the most popular with 35% of total orders</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary-500 mt-1.5 mr-2"></div>
                    <span>Indian cuisine has shown 5% growth compared to last quarter</span>
                  </li>
                  <li className="flex items-start">
                    <div className="w-1.5 h-1.5 rounded-full bg-primary-500 mt-1.5 mr-2"></div>
                    <span>Italian cuisine has the highest average order value at $22.50</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default Analytics;
