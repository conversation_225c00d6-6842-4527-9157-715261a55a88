import React, { useState } from "react";
import {
  Star,
  Gift,
  Trophy,
  TrendingUp,
  Users,
  Calendar,
  Crown,
  Zap,
  Award,
  ChevronRight,
  Copy,
  Check,
} from "lucide-react";
import { useLoyalty } from "../../context/LoyaltyContext";
import Card from "../common/Card";
import Button from "../common/Button";
import Badge from "../common/Badge";

const LoyaltyDashboard = ({ className = "" }) => {
  const {
    loyaltyData,
    pointsHistory,
    redeemedRewards,
    referralCode,
    LOYALTY_TIERS,
    REWARDS,
    getNextTier,
    getPointsToNextTier,
  } = useLoyalty();

  const [activeTab, setActiveTab] = useState("overview");
  const [copiedReferral, setCopiedReferral] = useState(false);

  if (!loyaltyData) {
    return (
      <Card className={className}>
        <div className="p-8 text-center">
          <Trophy size={48} className="mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">
            Join Our Loyalty Program
          </h3>
          <p className="text-gray-500">
            Start earning points with every order and unlock amazing rewards!
          </p>
        </div>
      </Card>
    );
  }

  const currentTier = LOYALTY_TIERS[loyaltyData.tier];
  const nextTier = getNextTier(loyaltyData.tier);
  const pointsToNext = getPointsToNextTier(loyaltyData.totalPoints, loyaltyData.tier);
  const progressPercentage = nextTier 
    ? ((loyaltyData.totalPoints - currentTier.minPoints) / 
       (LOYALTY_TIERS[nextTier].minPoints - currentTier.minPoints)) * 100
    : 100;

  const copyReferralCode = () => {
    navigator.clipboard.writeText(referralCode);
    setCopiedReferral(true);
    setTimeout(() => setCopiedReferral(false), 2000);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const TierIcon = ({ tier }) => {
    const icons = {
      BRONZE: <Award className="text-amber-600" size={24} />,
      SILVER: <Star className="text-gray-500" size={24} />,
      GOLD: <Crown className="text-yellow-500" size={24} />,
      PLATINUM: <Trophy className="text-purple-600" size={24} />,
    };
    return icons[tier] || icons.BRONZE;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Loyalty Program</h2>
          <p className="text-gray-600">Earn points, unlock rewards, and enjoy exclusive benefits</p>
        </div>
        <div className="flex items-center space-x-2">
          <TierIcon tier={loyaltyData.tier} />
          <Badge
            variant="primary"
            className="text-lg px-4 py-2"
            style={{ backgroundColor: currentTier.color, color: "white" }}
          >
            {currentTier.name}
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mr-4">
                <Star size={24} className="text-orange-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Available Points</h3>
                <p className="text-2xl font-bold text-gray-900">{loyaltyData.availablePoints}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <TrendingUp size={24} className="text-blue-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Total Points Earned</h3>
                <p className="text-2xl font-bold text-gray-900">{loyaltyData.totalPoints}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                <Gift size={24} className="text-green-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Total Orders</h3>
                <p className="text-2xl font-bold text-gray-900">{loyaltyData.totalOrders}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                <Users size={24} className="text-purple-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Referrals</h3>
                <p className="text-2xl font-bold text-gray-900">{loyaltyData.referralsCount}</p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Tier Progress */}
      {nextTier && (
        <Card>
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Progress to {LOYALTY_TIERS[nextTier].name}</h3>
              <span className="text-sm text-gray-500">
                {pointsToNext} points to go
              </span>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
              <div
                className="bg-gradient-to-r from-orange-400 to-orange-600 h-3 rounded-full transition-all duration-500"
                style={{ width: `${Math.min(progressPercentage, 100)}%` }}
              />
            </div>
            
            <div className="flex justify-between text-sm text-gray-600">
              <span>{currentTier.name}</span>
              <span>{LOYALTY_TIERS[nextTier].name}</span>
            </div>
          </div>
        </Card>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: "overview", label: "Overview", icon: <Star size={16} /> },
            { id: "rewards", label: "Rewards", icon: <Gift size={16} /> },
            { id: "history", label: "History", icon: <Calendar size={16} /> },
            { id: "referral", label: "Refer Friends", icon: <Users size={16} /> },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? "border-orange-500 text-orange-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === "overview" && (
          <div className="space-y-6">
            {/* Current Tier Benefits */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <TierIcon tier={loyaltyData.tier} />
                  <span className="ml-2">{currentTier.name} Benefits</span>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentTier.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span className="text-gray-700">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
            </Card>

            {/* Recent Activity */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
                <div className="space-y-3">
                  {pointsHistory.slice(0, 5).map((entry) => (
                    <div key={entry.id} className="flex items-center justify-between py-2">
                      <div className="flex items-center space-x-3">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            entry.type === "earned" ? "bg-green-100" : "bg-red-100"
                          }`}
                        >
                          {entry.type === "earned" ? (
                            <TrendingUp size={16} className="text-green-600" />
                          ) : (
                            <Gift size={16} className="text-red-600" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{entry.reason}</p>
                          <p className="text-sm text-gray-500">
                            {formatDate(entry.timestamp)}
                          </p>
                        </div>
                      </div>
                      <span
                        className={`font-semibold ${
                          entry.type === "earned" ? "text-green-600" : "text-red-600"
                        }`}
                      >
                        {entry.type === "earned" ? "+" : ""}{entry.points} pts
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeTab === "rewards" && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.values(REWARDS).map((reward) => (
              <Card key={reward.id}>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Gift size={24} className="text-orange-500" />
                    <Badge variant="outline">{reward.pointsCost} pts</Badge>
                  </div>
                  <h3 className="font-semibold mb-2">{reward.name}</h3>
                  <p className="text-sm text-gray-600 mb-4">{reward.description}</p>
                  <Button
                    variant={loyaltyData.availablePoints >= reward.pointsCost ? "primary" : "outline"}
                    size="small"
                    fullWidth
                    disabled={loyaltyData.availablePoints < reward.pointsCost}
                  >
                    {loyaltyData.availablePoints >= reward.pointsCost ? "Redeem" : "Not Enough Points"}
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        )}

        {activeTab === "history" && (
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Points History</h3>
              <div className="space-y-3">
                {pointsHistory.map((entry) => (
                  <div key={entry.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                    <div className="flex items-center space-x-3">
                      <div
                        className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          entry.type === "earned" ? "bg-green-100" : "bg-red-100"
                        }`}
                      >
                        {entry.type === "earned" ? (
                          <TrendingUp size={20} className="text-green-600" />
                        ) : (
                          <Gift size={20} className="text-red-600" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium">{entry.reason}</p>
                        <p className="text-sm text-gray-500">
                          {formatDate(entry.timestamp)}
                        </p>
                      </div>
                    </div>
                    <span
                      className={`font-semibold text-lg ${
                        entry.type === "earned" ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {entry.type === "earned" ? "+" : ""}{entry.points}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        )}

        {activeTab === "referral" && (
          <div className="space-y-6">
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Refer Friends & Earn Points</h3>
                <p className="text-gray-600 mb-6">
                  Share your referral code with friends and earn 100 points when they place their first order!
                </p>
                
                <div className="bg-gray-50 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-700">Your Referral Code</p>
                      <p className="text-2xl font-bold text-orange-600">{referralCode}</p>
                    </div>
                    <Button
                      variant="outline"
                      size="small"
                      icon={copiedReferral ? <Check size={16} /> : <Copy size={16} />}
                      onClick={copyReferralCode}
                    >
                      {copiedReferral ? "Copied!" : "Copy"}
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Users size={24} className="text-blue-600" />
                    </div>
                    <h4 className="font-medium">Share Code</h4>
                    <p className="text-sm text-gray-600">Send your code to friends</p>
                  </div>
                  <div>
                    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Gift size={24} className="text-green-600" />
                    </div>
                    <h4 className="font-medium">Friend Orders</h4>
                    <p className="text-sm text-gray-600">They place their first order</p>
                  </div>
                  <div>
                    <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Star size={24} className="text-orange-600" />
                    </div>
                    <h4 className="font-medium">Earn Points</h4>
                    <p className="text-sm text-gray-600">You both get 100 points</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoyaltyDashboard;
