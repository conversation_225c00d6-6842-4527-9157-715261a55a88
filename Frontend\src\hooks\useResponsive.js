import { useState, useEffect } from 'react';

// Breakpoints (matching Tailwind CSS defaults)
const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536
};

export const useResponsive = () => {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768
  });

  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [isDesktop, setIsDesktop] = useState(true);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({ width, height });
      
      // Update device type based on screen width
      setIsMobile(width < breakpoints.md);
      setIsTablet(width >= breakpoints.md && width < breakpoints.lg);
      setIsDesktop(width >= breakpoints.lg);
    };

    // Set initial values
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Helper functions for specific breakpoints
  const isSmallScreen = screenSize.width < breakpoints.sm;
  const isMediumScreen = screenSize.width >= breakpoints.sm && screenSize.width < breakpoints.md;
  const isLargeScreen = screenSize.width >= breakpoints.md && screenSize.width < breakpoints.lg;
  const isExtraLargeScreen = screenSize.width >= breakpoints.lg && screenSize.width < breakpoints.xl;
  const is2XLScreen = screenSize.width >= breakpoints.xl;

  // Check if screen is smaller than a specific breakpoint
  const isBelow = (breakpoint) => {
    return screenSize.width < breakpoints[breakpoint];
  };

  // Check if screen is larger than a specific breakpoint
  const isAbove = (breakpoint) => {
    return screenSize.width >= breakpoints[breakpoint];
  };

  // Check if screen is between two breakpoints
  const isBetween = (minBreakpoint, maxBreakpoint) => {
    return screenSize.width >= breakpoints[minBreakpoint] && 
           screenSize.width < breakpoints[maxBreakpoint];
  };

  return {
    screenSize,
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    isExtraLargeScreen,
    is2XLScreen,
    isBelow,
    isAbove,
    isBetween,
    breakpoints
  };
};

// Hook for detecting touch devices
export const useTouchDevice = () => {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    const checkTouchDevice = () => {
      return (
        'ontouchstart' in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0
      );
    };

    setIsTouchDevice(checkTouchDevice());
  }, []);

  return isTouchDevice;
};

// Hook for detecting device orientation
export const useOrientation = () => {
  const [orientation, setOrientation] = useState({
    isPortrait: true,
    isLandscape: false,
    angle: 0
  });

  useEffect(() => {
    const handleOrientationChange = () => {
      const angle = window.screen?.orientation?.angle || 0;
      const isPortrait = window.innerHeight > window.innerWidth;
      
      setOrientation({
        isPortrait,
        isLandscape: !isPortrait,
        angle
      });
    };

    // Set initial orientation
    handleOrientationChange();

    // Listen for orientation changes
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);

    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
    };
  }, []);

  return orientation;
};

// Hook for detecting if user prefers reduced motion
export const usePrefersReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  return prefersReducedMotion;
};

// Hook for detecting dark mode preference
export const usePrefersDarkMode = () => {
  const [prefersDarkMode, setPrefersDarkMode] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setPrefersDarkMode(mediaQuery.matches);

    const handleChange = (event) => {
      setPrefersDarkMode(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);

  return prefersDarkMode;
};

// Combined hook for all responsive features
export const useDeviceInfo = () => {
  const responsive = useResponsive();
  const isTouchDevice = useTouchDevice();
  const orientation = useOrientation();
  const prefersReducedMotion = usePrefersReducedMotion();
  const prefersDarkMode = usePrefersDarkMode();

  // Detect mobile operating system
  const getMobileOS = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;

    if (/android/i.test(userAgent)) {
      return 'Android';
    }

    if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
      return 'iOS';
    }

    return 'Unknown';
  };

  // Check if running as PWA
  const isPWA = () => {
    return window.matchMedia('(display-mode: standalone)').matches ||
           window.navigator.standalone === true;
  };

  // Check if device supports vibration
  const supportsVibration = () => {
    return 'vibrate' in navigator;
  };

  // Check if device supports geolocation
  const supportsGeolocation = () => {
    return 'geolocation' in navigator;
  };

  return {
    ...responsive,
    isTouchDevice,
    ...orientation,
    prefersReducedMotion,
    prefersDarkMode,
    mobileOS: getMobileOS(),
    isPWA: isPWA(),
    supportsVibration: supportsVibration(),
    supportsGeolocation: supportsGeolocation()
  };
};

export default useResponsive;
