import React, { forwardRef } from 'react';
import { cn } from '../../utils/cn';

const Input = forwardRef(({
  label,
  name,
  type = 'text',
  placeholder,
  error,
  helperText,
  required,
  icon,
  iconPosition = 'left',
  fullWidth = true,
  className,
  ...props
}, ref) => {
  const inputClasses = cn(
    'w-full px-4 py-2 bg-white border rounded-md outline-none transition-colors duration-200',
    'focus:ring-2 focus:ring-primary-500 focus:border-transparent',
    error
      ? 'border-accent-red text-accent-red focus:ring-accent-red'
      : 'border-gray-300 text-text-primary',
    icon && iconPosition === 'left' && 'pl-10',
    icon && iconPosition === 'right' && 'pr-10',
    className
  );

  const containerClasses = cn(
    'relative',
    fullWidth ? 'w-full' : 'inline-block'
  );

  const iconClasses = cn(
    'absolute top-1/2 transform -translate-y-1/2 text-gray-400',
    iconPosition === 'left' ? 'left-3' : 'right-3'
  );

  return (
    <div className={fullWidth ? 'w-full' : 'inline-block'}>
      {label && (
        <label 
          htmlFor={name} 
          className="block mb-2 text-sm font-medium text-text-primary"
        >
          {label}
          {required && <span className="ml-1 text-accent-red">*</span>}
        </label>
      )}
      
      <div className={containerClasses}>
        {icon && <span className={iconClasses}>{icon}</span>}
        
        <input
          ref={ref}
          type={type}
          id={name}
          name={name}
          placeholder={placeholder}
          className={inputClasses}
          aria-invalid={error ? 'true' : 'false'}
          required={required}
          {...props}
        />
      </div>
      
      {(error || helperText) && (
        <p className={`mt-1 text-sm ${error ? 'text-accent-red' : 'text-text-secondary'}`}>
          {error || helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;