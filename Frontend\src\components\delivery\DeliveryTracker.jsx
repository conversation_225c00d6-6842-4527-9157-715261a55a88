import React, { useEffect, useState } from "react";
import {
  TruckIcon,
  Clock,
  MapPin,
  Phone,
  MessageCircle,
  UserCheck,
  Navigation,
  AlertCircle,
} from "lucide-react";
import Card from "../common/Card";
import Button from "../common/Button";
import Badge from "../common/Badge";
import LiveMap from "../map/LiveMap";
import { useMap } from "../../context/MapContext";
import { useNotifications } from "../../context/NotificationContext";
import { mockDeliveryAgents } from "../../data/deliveryAgents";

// Import CSS for Leaflet
import "leaflet/dist/leaflet.css";

const DeliveryTracker = ({ order, className = "" }) => {
  const [deliveryAgent, setDeliveryAgent] = useState(null);
  const [isTracking, setIsTracking] = useState(false);
  const [trackingStarted, setTrackingStarted] = useState(false);
  const [estimatedTime, setEstimatedTime] = useState(null);
  const [distance, setDistance] = useState(null);

  const {
    initializeTracking,
    startSimulation,
    routeDistance,
    routeDuration,
    estimatedArrival,
    agentLocation,
    isSimulating,
  } = useMap();

  const { startOrderTracking, getOrderTracking, trackedOrders } =
    useNotifications();

  // Initialize tracking when component mounts
  useEffect(() => {
    if (!order || !order.id) return;

    // Start order tracking in notification context
    startOrderTracking(order.id);

    // Get a random delivery agent for demo purposes
    const randomAgent = mockDeliveryAgents[0];
    setDeliveryAgent(randomAgent);

    // Set tracking flag
    setTrackingStarted(true);
  }, [order, startOrderTracking]);

  // Initialize map tracking when agent is assigned
  useEffect(() => {
    if (!order || !deliveryAgent || !trackingStarted) return;

    const setupTracking = async () => {
      // Create locations for the map
      const restaurantLoc = {
        lat: 34.5553,
        lng: 69.2075,
        address: order.restaurantName + ", Kabul, Afghanistan",
      };

      const customerLoc = {
        lat: 34.565,
        lng: 69.1952,
        address: order.deliveryAddress,
      };

      // Initialize tracking on the map
      const trackingInfo = await initializeTracking({
        restaurantLocation: restaurantLoc,
        customerLocation: customerLoc,
      });

      if (trackingInfo) {
        setDistance(trackingInfo.distance);
        setEstimatedTime(trackingInfo.duration);

        // Only start simulation if order is out for delivery
        if (order.status === "outForDelivery") {
          startSimulation(
            trackingInfo.restaurant,
            trackingInfo.customer,
            trackingInfo.duration * 60 // Convert minutes to seconds
          );
          setIsTracking(true);
        }
      }
    };

    setupTracking();
  }, [
    order,
    deliveryAgent,
    trackingStarted,
    initializeTracking,
    startSimulation,
  ]);

  // Update estimated time based on route calculation
  useEffect(() => {
    if (routeDuration) {
      setEstimatedTime(routeDuration);
    }

    if (routeDistance) {
      setDistance(routeDistance);
    }
  }, [routeDuration, routeDistance]);

  // Format time remaining
  const formatTimeRemaining = () => {
    if (!estimatedTime) return "Calculating...";

    if (estimatedArrival) {
      const now = new Date();
      const diffMs = estimatedArrival - now;

      if (diffMs <= 0) return "Arriving now";

      const diffMins = Math.round(diffMs / 60000);
      return `${diffMins} min`;
    }

    return `${estimatedTime} min`;
  };

  // Check if order is being tracked
  const isOrderTracked = () => {
    return !!getOrderTracking(order.id);
  };

  // Get current order status from tracking
  const getCurrentStatus = () => {
    const tracking = getOrderTracking(order.id);
    return tracking ? tracking.status : order.status;
  };

  // Get status badge
  const getStatusBadge = (status) => {
    switch (status) {
      case "outForDelivery":
        return (
          <Badge className='bg-blue-100 text-blue-800'>Out for Delivery</Badge>
        );
      case "delivered":
        return <Badge className='bg-green-100 text-green-800'>Delivered</Badge>;
      case "preparing":
        return (
          <Badge className='bg-yellow-100 text-yellow-800'>Preparing</Badge>
        );
      default:
        return <Badge className='bg-gray-100 text-gray-800'>{status}</Badge>;
    }
  };

  if (!order) {
    return (
      <Card className={className}>
        <div className='p-6 text-center'>
          <AlertCircle size={48} className='mx-auto text-red-500 mb-4' />
          <h3 className='text-lg font-medium mb-2'>No Order Found</h3>
          <p className='text-gray-600'>
            We couldn't find the order you're looking for.
          </p>
        </div>
      </Card>
    );
  }

  const currentStatus = getCurrentStatus();
  const showAgentInfo =
    currentStatus === "outForDelivery" || currentStatus === "delivered";

  return (
    <Card className={className}>
      {/* Map Section */}
      <div className='h-64 md:h-80 w-full relative'>
        <LiveMap orderId={order.id} width='100%' height='100%' />

        {/* Status Overlay */}
        <div className='absolute top-4 left-4 bg-white px-3 py-2 rounded-md shadow-md'>
          {getStatusBadge(currentStatus)}
        </div>

        {/* Time Remaining */}
        {currentStatus === "outForDelivery" && (
          <div className='absolute bottom-4 right-4 bg-white px-3 py-2 rounded-md shadow-md'>
            <div className='flex items-center'>
              <Clock size={16} className='text-primary-500 mr-2' />
              <span className='font-medium'>{formatTimeRemaining()}</span>
            </div>
          </div>
        )}
      </div>

      {/* Delivery Info */}
      <div className='p-6'>
        <div className='flex justify-between items-center mb-4'>
          <h3 className='text-lg font-semibold'>Delivery Status</h3>
          {distance && (
            <div className='text-sm text-gray-600'>
              {distance.toFixed(1)} km
            </div>
          )}
        </div>

        {/* Delivery Agent Info */}
        {showAgentInfo && deliveryAgent && (
          <div className='mb-6'>
            <h4 className='text-sm font-medium text-gray-500 mb-3'>
              Delivery Agent
            </h4>
            <div className='flex items-center'>
              <div className='w-12 h-12 rounded-full bg-gray-200 overflow-hidden mr-4'>
                {deliveryAgent.avatar ? (
                  <img
                    src={deliveryAgent.avatar}
                    alt={deliveryAgent.name}
                    className='w-full h-full object-cover'
                  />
                ) : (
                  <UserCheck
                    size={24}
                    className='w-full h-full p-3 text-gray-500'
                  />
                )}
              </div>
              <div className='flex-grow'>
                <h4 className='font-medium'>{deliveryAgent.name}</h4>
                <p className='text-sm text-gray-600'>
                  {deliveryAgent.vehicle?.type} •{" "}
                  {deliveryAgent.vehicle?.licensePlate}
                </p>
              </div>
              <div className='flex space-x-2'>
                <button className='w-10 h-10 bg-green-100 rounded-full flex items-center justify-center text-green-600'>
                  <Phone size={20} />
                </button>
                <button className='w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600'>
                  <MessageCircle size={20} />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Delivery Address */}
        <div className='mb-6'>
          <h4 className='text-sm font-medium text-gray-500 mb-3'>
            Delivery Address
          </h4>
          <div className='flex items-start'>
            <MapPin size={20} className='text-gray-500 mr-2 mt-0.5' />
            <p>{order.deliveryAddress}</p>
          </div>
        </div>

        {/* Delivery Instructions */}
        {order.deliveryInstructions && (
          <div className='mb-6'>
            <h4 className='text-sm font-medium text-gray-500 mb-3'>
              Delivery Instructions
            </h4>
            <p className='text-gray-700 bg-gray-50 p-3 rounded-md'>
              {order.deliveryInstructions}
            </p>
          </div>
        )}

        {/* Action Buttons */}
        {currentStatus === "outForDelivery" && (
          <div className='flex gap-3'>
            <Button
              variant='outline'
              fullWidth
              icon={<Navigation size={16} />}
              onClick={() => {
                // In a real app, this would open maps with directions
                alert("Opening navigation to delivery location...");
              }}
            >
              Navigate
            </Button>
            <Button
              variant='primary'
              fullWidth
              icon={<Phone size={16} />}
              onClick={() => {
                // In a real app, this would call the delivery agent
                alert("Calling delivery agent...");
              }}
            >
              Call Agent
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
};

export default DeliveryTracker;
