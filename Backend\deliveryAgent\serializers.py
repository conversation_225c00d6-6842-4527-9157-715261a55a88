from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import DeliveryAgentProfile

User = get_user_model()

class DeliveryAgentProfileSerializer(serializers.ModelSerializer):
    """
    Clean serializer for delivery agent profiles
    Used in hybrid registration system
    """
    user_name = serializers.CharField(source='user.user_name', read_only=True)
    age = serializers.ReadOnlyField()
    completion_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = DeliveryAgentProfile
        fields = [
            # Basic Information
            'id', 'agent_id', 'user', 'user_name',
            
            # Personal Information
            'full_name', 'father_name', 'national_id', 'date_of_birth',
            'gender', 'marital_status', 'age',
            
            # Contact Information
            'phone_number', 'secondary_phone', 'email',
            
            # Address Information
            'province', 'district', 'area', 'street_address', 'nearby_landmark',
            
            # Vehicle Information
            'vehicle_type', 'vehicle_model', 'vehicle_year',
            'license_plate', 'vehicle_color', 'driving_license',
            
            # Documents
            'tazkira_front_image', 'tazkira_back_image',
            'driving_license_image', 'vehicle_registration_image', 'profile_photo',
            
            # References
            'reference1_name', 'reference1_phone', 'reference1_relation',
            'reference2_name', 'reference2_phone', 'reference2_relation',
            
            # Emergency Contact
            'emergency_contact', 'emergency_relation',
            
            # Banking Information
            'bank_name', 'account_number', 'account_holder_name', 'mobile_wallet',
            
            # Application Status
            'status', 'availability', 'is_online', 'is_verified',
            'application_date', 'approval_date', 'rejection_date',
            'admin_notes',
            
            # Verification Status
            'document_verification_status', 'background_check_status', 'training_status',
            
            # Performance Metrics
            'total_deliveries', 'successful_deliveries', 'total_earnings',
            'rating', 'completion_rate',
            
            # Location
            'current_latitude', 'current_longitude', 'current_address',
            'last_location_update',
            
            # Timestamps
            'created_at', 'updated_at', 'last_active'
        ]
        read_only_fields = [
            'id', 'agent_id', 'user_name', 'age', 'completion_rate',
            'application_date', 'approval_date', 'rejection_date',
            'total_deliveries', 'successful_deliveries', 'total_earnings',
            'rating', 'created_at', 'updated_at'
        ]

class AgentRegistrationSerializer(serializers.ModelSerializer):
    """
    Serializer for agent self-registration
    """
    password = serializers.CharField(write_only=True, min_length=8)
    confirm_password = serializers.CharField(write_only=True)
    
    class Meta:
        model = DeliveryAgentProfile
        fields = [
            # Required for registration
            'full_name', 'father_name', 'national_id', 'date_of_birth',
            'phone_number', 'email', 'password', 'confirm_password',
            'province', 'district', 'area', 'street_address',
            'vehicle_type', 'vehicle_model', 'license_plate',
            
            # Optional fields
            'secondary_phone', 'nearby_landmark', 'vehicle_year',
            'vehicle_color', 'driving_license',
            'reference1_name', 'reference1_phone', 'reference1_relation',
            'reference2_name', 'reference2_phone', 'reference2_relation',
            'emergency_contact', 'emergency_relation',
            'bank_name', 'account_number', 'account_holder_name', 'mobile_wallet'
        ]
    
    def validate(self, data):
        """Validate registration data"""
        if data['password'] != data['confirm_password']:
            raise serializers.ValidationError("Passwords don't match")
        
        # Check if national ID already exists
        if DeliveryAgentProfile.objects.filter(national_id=data['national_id']).exists():
            raise serializers.ValidationError("National ID already registered")
        
        # Check if phone already exists
        if User.objects.filter(phone=data['phone_number']).exists():
            raise serializers.ValidationError("Phone number already registered")
        
        return data
    
    def create(self, validated_data):
        """Create user and agent profile"""
        password = validated_data.pop('password')
        validated_data.pop('confirm_password')

        # Generate agent ID
        agent_id = self.generate_agent_id()

        # Create user
        user = User.objects.create_user(
            user_name=validated_data['phone_number'],
            name=validated_data['full_name'],
            phone=validated_data['phone_number'],
            email=validated_data.get('email', ''),
            password=password,
            role='delivery_agent',
            is_active=True,  # Active immediately - no approval needed
            is_verified=True  # Verified immediately - no email verification needed
        )

        # Create agent profile with approved status (no approval needed)
        agent_profile = DeliveryAgentProfile.objects.create(
            user=user,
            agent_id=agent_id,
            status='approved',  # Automatically approved
            **validated_data
        )

        return agent_profile

    def generate_agent_id(self):
        """Generate unique agent ID"""
        import random
        import string

        while True:
            # Generate random agent ID
            random_part = ''.join(random.choices(string.digits, k=6))
            agent_id = f"DA{random_part}"

            # Check if it already exists
            if not DeliveryAgentProfile.objects.filter(agent_id=agent_id).exists():
                return agent_id

class AgentDashboardSerializer(serializers.ModelSerializer):
    """
    Serializer for agent dashboard data
    """
    user_name = serializers.CharField(source='user.user_name', read_only=True)
    completion_rate = serializers.ReadOnlyField()
    
    class Meta:
        model = DeliveryAgentProfile
        fields = [
            'agent_id', 'user_name', 'full_name', 'status', 'availability', 'is_online',
            'is_verified', 'rating', 'total_deliveries', 'successful_deliveries',
            'total_earnings', 'completion_rate', 'profile_photo',
            'current_latitude', 'current_longitude', 'current_address'
        ]

class AgentLocationUpdateSerializer(serializers.Serializer):
    """
    Serializer for location updates
    """
    latitude = serializers.DecimalField(max_digits=10, decimal_places=8)
    longitude = serializers.DecimalField(max_digits=11, decimal_places=8)
    address = serializers.CharField(max_length=500, required=False, allow_blank=True)

class AgentStatusUpdateSerializer(serializers.Serializer):
    """
    Serializer for status updates
    """
    availability = serializers.ChoiceField(choices=[
        ('available', 'Available'),
        ('busy', 'Busy'),
        ('offline', 'Offline'),
        ('break', 'Break')
    ])

class AdminApprovalSerializer(serializers.Serializer):
    """
    Serializer for admin approval actions
    """
    action = serializers.ChoiceField(choices=['approve', 'reject'])
    notes = serializers.CharField(max_length=1000, required=False, allow_blank=True)
