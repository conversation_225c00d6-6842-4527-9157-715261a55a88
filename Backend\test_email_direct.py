#!/usr/bin/env python3
"""
Direct email test using Django settings
"""

import os
import sys
import django

# Add the Backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.core.mail import send_mail
from django.conf import settings

def test_email_configuration():
    """Test email configuration directly"""
    print("🔧 Testing Email Configuration...")
    print("=" * 50)
    
    print("📧 Current Email Settings:")
    print(f"   Backend: {settings.EMAIL_BACKEND}")
    print(f"   Host: {settings.EMAIL_HOST}")
    print(f"   Port: {settings.EMAIL_PORT}")
    print(f"   Use TLS: {settings.EMAIL_USE_TLS}")
    print(f"   Host User: {settings.EMAIL_HOST_USER}")
    print(f"   From Email: {settings.DEFAULT_FROM_EMAIL}")
    
    # Get test email
    test_email = input("\n📧 Enter your email to test: ").strip()
    
    if not test_email or "@" not in test_email:
        print("❌ Invalid email address")
        return
    
    print(f"\n📤 Sending test email to: {test_email}")
    
    try:
        send_mail(
            subject='Afghan Sufra - Email Test',
            message='This is a test email from Afghan Sufra system. If you receive this, email configuration is working!',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[test_email],
            fail_silently=False,
        )
        print("✅ Test email sent successfully!")
        print("📬 Please check your email inbox (and spam folder)")
        
    except Exception as e:
        print(f"❌ Failed to send email: {e}")
        print("\n🔧 Possible issues:")
        print("   1. Gmail App Password might be incorrect")
        print("   2. Gmail account might have 2FA disabled")
        print("   3. Network connectivity issues")
        print("   4. Gmail SMTP settings might be blocked")

def test_otp_generation():
    """Test OTP generation and email sending"""
    print("\n🔐 Testing OTP Generation...")
    print("=" * 50)
    
    from users.models import User, OTPVerification
    
    # Get test email
    test_email = input("📧 Enter email for OTP test: ").strip()
    
    if not test_email or "@" not in test_email:
        print("❌ Invalid email address")
        return
    
    try:
        # Create a temporary user for testing
        user, created = User.objects.get_or_create(
            email=test_email,
            defaults={
                'name': 'Test User',
                'user_name': f'test_{int(time.time())}',
                'phone': '+**********',
                'role': 'customer'
            }
        )
        
        if created:
            user.set_password('testpass123')
            user.save()
            print(f"✅ Created test user: {user.email}")
        else:
            print(f"✅ Using existing user: {user.email}")
        
        # Generate OTP
        otp_record = OTPVerification.generate_otp(user, 'registration')
        print(f"🔑 Generated OTP: {otp_record.otp}")
        
        # Send OTP email
        otp_record.send_otp_email()
        print("📤 OTP email sent!")
        print("📬 Please check your email for the OTP")
        
    except Exception as e:
        print(f"❌ Error in OTP test: {e}")

if __name__ == "__main__":
    import time
    
    print("🚀 Afghan Sufra Email System Test")
    print("=" * 50)
    
    # Test 1: Basic email configuration
    test_email_configuration()
    
    # Test 2: OTP generation and sending
    test_otp_generation()
