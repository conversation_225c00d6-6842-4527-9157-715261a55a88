"""
Advanced Pagination Classes for Afghan Sofra API

This module provides sophisticated pagination classes optimized for different use cases:
- Cursor-based pagination for real-time data
- Virtual scrolling for large datasets
- Search-optimized pagination
- Analytics-enabled pagination
- Performance-optimized pagination
"""

from rest_framework.pagination import PageNumberPagination, LimitOffsetPagination
from rest_framework.response import Response
from collections import OrderedDict
from django.core.cache import cache
from django.db.models import Q
import time
import logging

logger = logging.getLogger(__name__)


class CursorPagination(PageNumberPagination):
    """
    Cursor-based pagination for real-time data and better performance
    Uses timestamp-based cursors for consistent pagination
    """
    page_size = 20
    page_size_query_param = 'page_size'
    max_page_size = 100
    cursor_query_param = 'cursor'
    ordering = '-created_at'  # Must be a unique, ordered field
    
    def paginate_queryset(self, queryset, request, view=None):
        """Override to implement cursor-based pagination"""
        cursor = request.GET.get(self.cursor_query_param)
        page_size = self.get_page_size(request)
        
        if cursor:
            # Apply cursor filter
            if self.ordering.startswith('-'):
                field_name = self.ordering[1:]
                queryset = queryset.filter(**{f"{field_name}__lt": cursor})
            else:
                queryset = queryset.filter(**{f"{self.ordering}__gt": cursor})
        
        # Get one extra item to check if there's a next page
        items = list(queryset.order_by(self.ordering)[:page_size + 1])
        
        self.has_next = len(items) > page_size
        if self.has_next:
            items = items[:-1]
        
        self.items = items
        return items
    
    def get_paginated_response(self, data):
        next_cursor = None
        previous_cursor = None
        
        if self.items:
            # Generate cursors based on the ordering field
            field_name = self.ordering[1:] if self.ordering.startswith('-') else self.ordering
            if hasattr(self.items[0], field_name):
                if self.has_next:
                    next_cursor = getattr(self.items[-1], field_name).isoformat()
                if self.request.GET.get(self.cursor_query_param):
                    previous_cursor = getattr(self.items[0], field_name).isoformat()
        
        return Response(OrderedDict([
            ('count', None),  # Count not available in cursor pagination
            ('next_cursor', next_cursor),
            ('previous_cursor', previous_cursor),
            ('page_size', self.get_page_size(self.request)),
            ('has_next', self.has_next),
            ('has_previous', bool(self.request.GET.get(self.cursor_query_param))),
            ('cursor_based', True),
            ('results', data)
        ]))


class VirtualScrollPagination(LimitOffsetPagination):
    """
    Virtual scrolling pagination for large datasets
    Optimized for frontend virtual scrolling components
    """
    default_limit = 50
    limit_query_param = 'limit'
    offset_query_param = 'offset'
    max_limit = 500
    
    def get_paginated_response(self, data):
        return Response(OrderedDict([
            ('count', self.count),
            ('limit', self.get_limit(self.request)),
            ('offset', self.get_offset(self.request)),
            ('has_more', self.get_next_link() is not None),
            ('total_pages', (self.count + self.get_limit(self.request) - 1) // self.get_limit(self.request)),
            ('current_page', (self.get_offset(self.request) // self.get_limit(self.request)) + 1),
            ('virtual_scroll_optimized', True),
            ('buffer_size', min(self.get_limit(self.request) * 2, 200)),  # Recommended buffer
            ('results', data)
        ]))


class SearchOptimizedPagination(PageNumberPagination):
    """
    Advanced search pagination with relevance scoring
    Optimized for search results with scoring and filtering
    """
    page_size = 15
    page_size_query_param = 'page_size'
    max_page_size = 100
    
    def get_paginated_response(self, data):
        search_query = self.request.GET.get('search', '')
        filters_applied = len([k for k in self.request.GET.keys() 
                             if k not in ['page', 'page_size', 'search', 'ordering']]) > 0
        
        # Calculate search relevance metrics
        search_metrics = self._calculate_search_metrics(search_query, data)
        
        return Response(OrderedDict([
            ('count', self.page.paginator.count),
            ('total_pages', self.page.paginator.num_pages),
            ('current_page', self.page.number),
            ('page_size', self.get_page_size(self.request)),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('has_next', self.page.has_next()),
            ('has_previous', self.page.has_previous()),
            ('search_query', search_query),
            ('filters_applied', filters_applied),
            ('search_metrics', search_metrics),
            ('search_optimized', True),
            ('results', data)
        ]))
    
    def _calculate_search_metrics(self, query, data):
        """Calculate search relevance metrics"""
        if not query or not data:
            return {}
        
        return {
            'query_length': len(query),
            'results_count': len(data),
            'has_exact_matches': any(query.lower() in str(item).lower() for item in data),
            'search_time': getattr(self, '_search_time', None),
        }


class AnalyticsPagination(PageNumberPagination):
    """
    Pagination with built-in analytics tracking
    Tracks pagination usage patterns and performance
    """
    page_size = 25
    page_size_query_param = 'page_size'
    max_page_size = 200
    
    def paginate_queryset(self, queryset, request, view=None):
        # Track pagination usage
        start_time = time.time()
        result = super().paginate_queryset(queryset, request, view)
        end_time = time.time()
        
        self._track_pagination_usage(request, view, end_time - start_time)
        return result
    
    def _track_pagination_usage(self, request, view, query_time):
        """Track pagination usage for analytics"""
        try:
            # Create analytics key
            view_name = view.__class__.__name__ if view else 'unknown'
            user_id = request.user.id if request.user.is_authenticated else 'anonymous'
            cache_key = f"pagination_analytics:{view_name}:{user_id}"
            
            # Get existing analytics data
            analytics_data = cache.get(cache_key, {
                'total_requests': 0,
                'pages_accessed': [],
                'last_access': None,
                'avg_page_size': 0,
                'total_query_time': 0,
                'avg_query_time': 0,
            })
            
            # Update analytics
            current_page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', self.page_size))
            
            analytics_data['total_requests'] += 1
            analytics_data['pages_accessed'].append(current_page)
            analytics_data['last_access'] = time.time()
            analytics_data['avg_page_size'] = (
                (analytics_data['avg_page_size'] * (analytics_data['total_requests'] - 1) + page_size) 
                / analytics_data['total_requests']
            )
            analytics_data['total_query_time'] += query_time
            analytics_data['avg_query_time'] = analytics_data['total_query_time'] / analytics_data['total_requests']
            
            # Keep only last 100 page accesses
            if len(analytics_data['pages_accessed']) > 100:
                analytics_data['pages_accessed'] = analytics_data['pages_accessed'][-100:]
            
            # Store for 24 hours
            cache.set(cache_key, analytics_data, 86400)
            
        except Exception as e:
            # Fail silently to not break pagination
            logger.warning(f"Failed to track pagination analytics: {e}")
    
    def get_paginated_response(self, data):
        return Response(OrderedDict([
            ('count', self.page.paginator.count),
            ('total_pages', self.page.paginator.num_pages),
            ('current_page', self.page.number),
            ('page_size', self.get_page_size(self.request)),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('has_next', self.page.has_next()),
            ('has_previous', self.page.has_previous()),
            ('analytics_enabled', True),
            ('results', data)
        ]))


class PerformanceOptimizedPagination(PageNumberPagination):
    """
    High-performance pagination with caching and optimization
    """
    page_size = 30
    page_size_query_param = 'page_size'
    max_page_size = 100
    cache_timeout = 300  # 5 minutes
    
    def paginate_queryset(self, queryset, request, view=None):
        # Generate cache key
        cache_key = self._generate_cache_key(request, view)
        
        # Try to get cached result
        cached_result = cache.get(cache_key)
        if cached_result:
            self.page = cached_result['page']
            return cached_result['data']
        
        # If not cached, paginate normally
        result = super().paginate_queryset(queryset, request, view)
        
        # Cache the result
        if result:
            cache_data = {
                'page': self.page,
                'data': result
            }
            cache.set(cache_key, cache_data, self.cache_timeout)
        
        return result
    
    def _generate_cache_key(self, request, view):
        """Generate cache key for pagination"""
        view_name = view.__class__.__name__ if view else 'unknown'
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', self.page_size)
        
        # Include relevant query parameters
        query_params = sorted([
            (k, v) for k, v in request.GET.items() 
            if k not in ['page', 'page_size']
        ])
        
        params_str = '&'.join([f"{k}={v}" for k, v in query_params])
        return f"pagination:{view_name}:p{page}:s{page_size}:{hash(params_str)}"
    
    def get_paginated_response(self, data):
        return Response(OrderedDict([
            ('count', self.page.paginator.count),
            ('total_pages', self.page.paginator.num_pages),
            ('current_page', self.page.number),
            ('page_size', self.get_page_size(self.request)),
            ('next', self.get_next_link()),
            ('previous', self.get_previous_link()),
            ('has_next', self.page.has_next()),
            ('has_previous', self.page.has_previous()),
            ('cached', True),
            ('cache_timeout', self.cache_timeout),
            ('performance_optimized', True),
            ('results', data)
        ]))


# Advanced pagination class mapping
ADVANCED_PAGINATION_CLASSES = {
    'cursor': CursorPagination,
    'virtual': VirtualScrollPagination,
    'search_advanced': SearchOptimizedPagination,
    'analytics': AnalyticsPagination,
    'performance': PerformanceOptimizedPagination,
}


def get_advanced_pagination_class(pagination_type='cursor'):
    """
    Get advanced pagination class by type
    
    Args:
        pagination_type (str): Type of advanced pagination needed
        
    Returns:
        Pagination class
    """
    return ADVANCED_PAGINATION_CLASSES.get(pagination_type, CursorPagination)


def get_optimal_pagination_class(queryset_size, is_search=False, is_mobile=False, 
                                has_analytics=False, needs_performance=False):
    """
    Automatically select optimal pagination class based on context
    
    Args:
        queryset_size (int): Estimated size of the queryset
        is_search (bool): Whether this is for search results
        is_mobile (bool): Whether this is for mobile interface
        has_analytics (bool): Whether analytics tracking is needed
        needs_performance (bool): Whether performance optimization is critical
        
    Returns:
        Pagination class
    """
    if needs_performance:
        return PerformanceOptimizedPagination
    
    if has_analytics:
        return AnalyticsPagination
    
    if is_search:
        return SearchOptimizedPagination
    
    if queryset_size > 10000:
        return CursorPagination
    elif queryset_size > 1000:
        return VirtualScrollPagination
    else:
        return CursorPagination  # Default to cursor for better performance
