import React, { useState } from 'react';
import { MapPin, Plus, Navigation, ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import Button from '../../components/common/Button';
import Card from '../../components/common/Card';
import AddressManager from '../../components/map/AddressManager';
import LocationPicker from '../../components/map/LocationPicker';
import { useLocation } from '../../hooks/useLocation';

const MyAddresses = () => {
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const {
    addresses,
    selectedAddress,
    selectAddress,
    addAddress,
    getCurrentLocation,
    isGettingLocation,
    locationError,
    hasAddresses
  } = useLocation();

  const handleAddAddress = (locationData) => {
    addAddress({
      type: 'other',
      label: 'New Address',
      address: locationData.address,
      coordinates: locationData.coordinates
    });
    setShowLocationPicker(false);
  };

  const handleGetCurrentLocation = async () => {
    try {
      const location = await getCurrentLocation();
      addAddress({
        type: 'current',
        label: 'Current Location',
        address: location.address,
        coordinates: location.coordinates
      });
    } catch (error) {
      console.error('Error getting current location:', error);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl animate-fade-in">
      {/* Header */}
      <div className="flex items-center mb-8">
        <Link
          to="/profile"
          className="text-text-primary hover:text-primary-500 mr-3"
        >
          <ArrowLeft size={20} />
        </Link>
        <h1 className="text-2xl font-poppins font-semibold">My Addresses</h1>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        <Card className="p-6 text-center hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => setShowLocationPicker(true)}>
          <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Plus size={24} className="text-primary-600" />
          </div>
          <h3 className="font-semibold mb-2">Add New Address</h3>
          <p className="text-sm text-gray-600">Select location on map</p>
        </Card>

        <Card className="p-6 text-center hover:shadow-md transition-shadow cursor-pointer"
              onClick={handleGetCurrentLocation}>
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Navigation size={24} className={`text-blue-600 ${isGettingLocation ? 'animate-spin' : ''}`} />
          </div>
          <h3 className="font-semibold mb-2">
            {isGettingLocation ? 'Getting Location...' : 'Use Current Location'}
          </h3>
          <p className="text-sm text-gray-600">
            {isGettingLocation ? 'Please wait...' : 'Add your current location'}
          </p>
        </Card>
      </div>

      {/* Error Message */}
      {locationError && (
        <Card className="p-4 mb-6 bg-red-50 border-red-200">
          <div className="flex items-center">
            <MapPin size={20} className="text-red-600 mr-2" />
            <p className="text-red-800">{locationError}</p>
          </div>
        </Card>
      )}

      {/* Address Manager */}
      <Card className="p-6">
        <AddressManager
          onAddressSelect={selectAddress}
          selectedAddressId={selectedAddress?.id}
          showAddButton={false}
          title="Saved Addresses"
        />

        {!hasAddresses && (
          <div className="text-center py-12">
            <MapPin size={64} className="text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">No addresses saved</h3>
            <p className="text-gray-600 mb-6">
              Add your first address to make ordering faster and easier
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                variant="primary"
                onClick={() => setShowLocationPicker(true)}
                icon={<Plus size={16} />}
              >
                Add Address
              </Button>
              <Button
                variant="outline"
                onClick={handleGetCurrentLocation}
                disabled={isGettingLocation}
                icon={<Navigation size={16} />}
              >
                {isGettingLocation ? 'Getting...' : 'Use Current Location'}
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* Address Statistics */}
      {hasAddresses && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-primary-600 mb-1">
              {addresses.length}
            </div>
            <div className="text-sm text-gray-600">Total Addresses</div>
          </Card>
          
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {addresses.filter(addr => addr.isDefault).length}
            </div>
            <div className="text-sm text-gray-600">Default Address</div>
          </Card>
          
          <Card className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {addresses.filter(addr => addr.type === 'home').length + 
               addresses.filter(addr => addr.type === 'work').length}
            </div>
            <div className="text-sm text-gray-600">Home & Work</div>
          </Card>
        </div>
      )}

      {/* Tips */}
      <Card className="p-6 mt-8 bg-blue-50 border-blue-200">
        <h3 className="font-semibold text-blue-900 mb-3">💡 Tips for better delivery</h3>
        <ul className="space-y-2 text-sm text-blue-800">
          <li>• Add detailed landmarks in your address</li>
          <li>• Include apartment/floor numbers</li>
          <li>• Set your most used address as default</li>
          <li>• Keep your phone number updated for delivery contact</li>
        </ul>
      </Card>

      {/* Location Picker Modal */}
      {showLocationPicker && (
        <LocationPicker
          onLocationSelect={handleAddAddress}
          onClose={() => setShowLocationPicker(false)}
          title="Add New Address"
        />
      )}
    </div>
  );
};

export default MyAddresses;
