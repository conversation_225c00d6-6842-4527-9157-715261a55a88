import { createContext, useContext, useState, useCallback } from "react";
import { restaurantApi } from "../utils/restaurantApi";

const RestaurantContext = createContext(null);

export const useRestaurant = () => {
  const context = useContext(RestaurantContext);
  if (!context) {
    throw new Error("useRestaurant must be used within a RestaurantProvider");
  }
  return context;
};

export const RestaurantProvider = ({ children }) => {
  const [restaurants, setRestaurants] = useState([]);
  const [currentRestaurant, setCurrentRestaurant] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Clear error helper
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Create a new restaurant
   */
  const createRestaurant = useCallback(async (restaurantData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await restaurantApi.createRestaurant(restaurantData);

      if (result.success) {
        // Add new restaurant to the list
        setRestaurants((prev) => [...prev, result.data]);
        setCurrentRestaurant(result.data);
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (error) {
      const errorMessage = "Failed to create restaurant";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get restaurant by ID
   */
  const getRestaurant = useCallback(async (restaurantId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await restaurantApi.getRestaurant(restaurantId);

      if (result.success) {
        setCurrentRestaurant(result.data);
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (error) {
      const errorMessage = "Failed to fetch restaurant";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Update restaurant (full update)
   */
  const updateRestaurant = useCallback(
    async (restaurantId, restaurantData) => {
      setLoading(true);
      setError(null);

      try {
        const result = await restaurantApi.updateRestaurant(
          restaurantId,
          restaurantData
        );

        if (result.success) {
          // Update restaurant in the list
          setRestaurants((prev) =>
            prev.map((restaurant) =>
              restaurant.id === restaurantId ? result.data : restaurant
            )
          );

          // Update current restaurant if it's the same one
          if (currentRestaurant?.id === restaurantId) {
            setCurrentRestaurant(result.data);
          }

          return result;
        } else {
          setError(result.error);
          return result;
        }
      } catch (error) {
        const errorMessage = "Failed to update restaurant";
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [currentRestaurant]
  );

  /**
   * Partially update restaurant
   */
  const patchRestaurant = useCallback(
    async (restaurantId, partialData) => {
      setLoading(true);
      setError(null);

      try {
        const result = await restaurantApi.patchRestaurant(
          restaurantId,
          partialData
        );

        if (result.success) {
          // Update restaurant in the list
          setRestaurants((prev) =>
            prev.map((restaurant) =>
              restaurant.id === restaurantId ? result.data : restaurant
            )
          );

          // Update current restaurant if it's the same one
          if (currentRestaurant?.id === restaurantId) {
            setCurrentRestaurant(result.data);
          }

          return result;
        } else {
          setError(result.error);
          return result;
        }
      } catch (error) {
        const errorMessage = "Failed to update restaurant";
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [currentRestaurant]
  );

  /**
   * Delete restaurant
   */
  const deleteRestaurant = useCallback(
    async (restaurantId) => {
      setLoading(true);
      setError(null);

      try {
        const result = await restaurantApi.deleteRestaurant(restaurantId);

        if (result.success) {
          // Remove restaurant from the list
          setRestaurants((prev) =>
            prev.filter((restaurant) => restaurant.id !== restaurantId)
          );

          // Clear current restaurant if it's the deleted one
          if (currentRestaurant?.id === restaurantId) {
            setCurrentRestaurant(null);
          }

          return result;
        } else {
          setError(result.error);
          return result;
        }
      } catch (error) {
        const errorMessage = "Failed to delete restaurant";
        setError(errorMessage);
        return { success: false, error: errorMessage };
      } finally {
        setLoading(false);
      }
    },
    [currentRestaurant]
  );

  /**
   * Get all restaurants
   */
  const getRestaurants = useCallback(async (params = {}) => {
    setLoading(true);
    setError(null);

    try {
      const result = await restaurantApi.getRestaurants(params);

      if (result.success) {
        setRestaurants(result.data);
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (error) {
      const errorMessage = "Failed to fetch restaurants";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Get restaurants owned by the current user
   */
  const getUserRestaurants = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await restaurantApi.getUserRestaurants();

      if (result.success) {
        setRestaurants(result.data);
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (error) {
      const errorMessage = "Failed to fetch your restaurants";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Reset restaurant state
   */
  const resetRestaurantState = useCallback(() => {
    setRestaurants([]);
    setCurrentRestaurant(null);
    setError(null);
  }, []);

  const value = {
    // State
    restaurants,
    currentRestaurant,
    loading,
    error,

    // Actions
    createRestaurant,
    getRestaurant,
    updateRestaurant,
    patchRestaurant,
    deleteRestaurant,
    getRestaurants,
    getUserRestaurants,
    resetRestaurantState,
    clearError,

    // Setters for direct state management if needed
    setCurrentRestaurant,
    setRestaurants,
  };

  return (
    <RestaurantContext.Provider value={value}>
      {children}
    </RestaurantContext.Provider>
  );
};
