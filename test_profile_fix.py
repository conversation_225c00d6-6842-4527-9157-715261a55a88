#!/usr/bin/env python3
"""
Test script to verify the profile endpoint fix
"""

import requests
import json

# Configuration
BASE_URL = "http://127.0.0.1:8000/api"

def test_profile_endpoint():
    """Test the delivery agent profile endpoint"""
    print("🧪 Testing Delivery Agent Profile Endpoint Fix")
    print("=" * 50)
    
    # Step 1: Login with working credentials
    print("\n1. Logging in with employee credentials...")
    login_data = {
        "user_name": "EMP001",
        "password": "employee123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
        if response.status_code == 200:
            login_result = response.json()
            access_token = login_result['data']['access_token']
            user_info = login_result['data']['user']
            print(f"✅ Login successful for {user_info['name']} (Role: {user_info['role']})")
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(response.json())
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Step 2: Test profile endpoint
    print("\n2. Testing profile endpoint...")
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/delivery-agent/profile/", headers=headers)
        print(f"Profile endpoint status: {response.status_code}")
        
        if response.status_code == 200:
            profile_data = response.json()
            print("✅ Profile loaded successfully!")
            print(f"Agent ID: {profile_data['data']['agent_id']}")
            print(f"Full Name: {profile_data['data']['full_name']}")
            print(f"Employment Status: {profile_data['data']['employment_status']}")
            print(f"Phone: {profile_data['data']['phone_number']}")
            return True
        elif response.status_code == 404:
            print("❌ Profile not found - need to create delivery agent profile")
            print("Response:", response.json())
            return False
        else:
            print(f"❌ Profile endpoint failed: {response.status_code}")
            print("Response:", response.json())
            return False
            
    except Exception as e:
        print(f"❌ Profile request error: {e}")
        return False

def create_delivery_agent_profile():
    """Create a delivery agent profile for the test user"""
    print("\n3. Creating delivery agent profile for test user...")
    
    # Login as admin first
    admin_login = {
        "user_name": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/", json=admin_login)
        if response.status_code == 200:
            admin_token = response.json()['data']['access_token']
            print("✅ Admin login successful")
        else:
            print("❌ Admin login failed")
            return False
    except Exception as e:
        print(f"❌ Admin login error: {e}")
        return False
    
    # Create profile for EMP001 user
    headers = {
        "Authorization": f"Bearer {admin_token}",
        "Content-Type": "application/json"
    }
    
    # Check if profile already exists by trying to get user details
    try:
        # First, let's check if the user has a profile by trying the profile endpoint
        emp_login = {
            "user_name": "EMP001", 
            "password": "employee123"
        }
        emp_response = requests.post(f"{BASE_URL}/auth/login/", json=emp_login)
        if emp_response.status_code == 200:
            emp_token = emp_response.json()['data']['access_token']
            emp_headers = {"Authorization": f"Bearer {emp_token}"}
            
            # Try to get the profile
            profile_response = requests.get(f"{BASE_URL}/delivery-agent/profile/", headers=emp_headers)
            if profile_response.status_code == 404:
                print("Profile doesn't exist, this is expected for EMP001")
                print("EMP001 was created as a basic user, not through the delivery agent system")
                return True
            else:
                print(f"Profile check returned: {profile_response.status_code}")
                return True
                
    except Exception as e:
        print(f"Profile check error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Profile Endpoint Fix Test")
    
    # Test the profile endpoint
    success = test_profile_endpoint()
    
    if not success:
        # Try to create profile if it doesn't exist
        create_delivery_agent_profile()
        
        # Test again
        print("\n4. Retesting profile endpoint...")
        success = test_profile_endpoint()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Profile endpoint is working correctly!")
        print("The 'Failed to load profile' error should be resolved.")
    else:
        print("❌ Profile endpoint still has issues.")
        print("Manual intervention may be required.")
    
    print("\n📋 Next Steps:")
    print("1. Test the frontend profile page")
    print("2. Login with EMP001 / employee123")
    print("3. Navigate to the profile section")
    print("4. Verify profile loads without errors")

if __name__ == "__main__":
    main()
