# backends.py
from django.contrib.auth import backends
from django.contrib.auth.models import User

class AdminAuthBackend(backends.ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        user = super().authenticate(request, username, password, **kwargs)
        if user and not user.is_staff:
            return None  # Prevent non-staff users from authenticating via admin
        return user