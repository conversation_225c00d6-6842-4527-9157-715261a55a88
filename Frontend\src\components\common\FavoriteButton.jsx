import React, { useState } from "react";
import { Heart, Loader2 } from "lucide-react";
import { cn } from "../../utils/cn";
import { useFavorites } from "../../context/FavoritesContext";
import { useToast } from "../../context/ToastContext";
import { useAuth } from "../../context/AuthContext";

const FavoriteButton = ({
  restaurant,
  className = "",
  size = "default", // "small", "default", "large"
  variant = "default", // "default", "outline", "ghost"
  showTooltip = true,
  onClick,
  ...props
}) => {
  const { user } = useAuth();
  const { favorites, toggleFavorite } = useFavorites();

  // Safe toast usage with fallback
  let toastSuccess, toastError;
  try {
    const toast = useToast();
    toastSuccess = toast.success;
    toastError = toast.error;
  } catch (e) {
    console.warn("Toast context not available, using console fallback");
    toastSuccess = (title, message) => console.log(`✅ ${title}: ${message}`);
    toastError = (title, message) => console.error(`❌ ${title}: ${message}`);
  }

  const [isLoading, setIsLoading] = useState(false);

  // Ensure restaurant has required properties
  if (!restaurant) {
    console.warn("FavoriteButton: No restaurant provided");
    return null;
  }

  const isFavorite = favorites.some((fav) => fav.id === restaurant?.id);

  const getSizeClasses = () => {
    switch (size) {
      case "small":
        return "p-1.5";
      case "large":
        return "p-3";
      default:
        return "p-2";
    }
  };

  const getIconSize = () => {
    switch (size) {
      case "small":
        return 14;
      case "large":
        return 24;
      default:
        return 18;
    }
  };

  const getVariantClasses = () => {
    const baseClasses =
      "rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";

    if (isFavorite) {
      switch (variant) {
        case "outline":
          return `${baseClasses} text-red-500 border-2 border-red-500 bg-white hover:bg-red-50 focus:ring-red-500`;
        case "ghost":
          return `${baseClasses} text-red-500 hover:bg-red-50 focus:ring-red-500`;
        default:
          return `${baseClasses} text-red-500 bg-red-50 hover:bg-red-100 focus:ring-red-500`;
      }
    } else {
      switch (variant) {
        case "outline":
          return `${baseClasses} text-gray-400 border-2 border-gray-300 bg-white hover:text-red-500 hover:border-red-500 hover:bg-red-50 focus:ring-gray-500`;
        case "ghost":
          return `${baseClasses} text-gray-400 hover:text-red-500 hover:bg-red-50 focus:ring-gray-500`;
        default:
          return `${baseClasses} text-gray-400 bg-white/90 hover:text-red-500 hover:bg-red-50 focus:ring-gray-500`;
      }
    }
  };

  const handleClick = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (!user) {
      toastError("Login Required", "Please login to manage your favorites");
      return;
    }

    if (isLoading) return;

    // Validate restaurant object
    if (!restaurant || !restaurant.id) {
      console.error("FavoriteButton - Invalid restaurant data:", restaurant);
      toastError("Error", "Invalid restaurant data");
      return;
    }

    setIsLoading(true);

    try {
      const result = await toggleFavorite(restaurant);

      if (result.success) {
        const action =
          result.data?.action || (isFavorite ? "removed" : "added");
        const restaurantName = restaurant.name || "Restaurant";
        const message =
          action === "added"
            ? `${restaurantName} added to favorites`
            : `${restaurantName} removed from favorites`;

        toastSuccess(
          action === "added" ? "Added to Favorites" : "Removed from Favorites",
          message
        );
      } else {
        console.error("Toggle favorite failed:", result);
        toastError("Error", result.error || "Failed to update favorites");
      }
    } catch (err) {
      console.error("Toggle favorite error:", err);
      toastError("Error", "An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }

    // Call custom onClick if provided
    onClick?.(e);
  };

  const tooltipText = isFavorite ? "Remove from favorites" : "Add to favorites";

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className={cn(
        getSizeClasses(),
        getVariantClasses(),
        isLoading && "opacity-50 cursor-not-allowed",
        className
      )}
      title={showTooltip ? tooltipText : undefined}
      aria-label={tooltipText}
      {...props}
    >
      {isLoading ? (
        <Loader2 size={getIconSize()} className='animate-spin' />
      ) : (
        <Heart
          size={getIconSize()}
          fill={isFavorite ? "currentColor" : "none"}
          className='transition-transform duration-200 hover:scale-110'
        />
      )}
    </button>
  );
};

export default FavoriteButton;
