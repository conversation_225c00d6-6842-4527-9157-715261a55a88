import React from 'react';
import { useMenu } from '../../context/MenuContext';

const MenuDebug = () => {
  const { menuItems, loading, error } = useMenu();

  if (loading) return <div>Loading menu items...</div>;
  if (error) return <div>Error: {error}</div>;

  const itemsWithImages = menuItems.filter(item => item.image);
  const itemsWithoutImages = menuItems.filter(item => !item.image);

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'white',
      border: '2px solid #ccc',
      padding: '15px',
      borderRadius: '8px',
      maxWidth: '400px',
      maxHeight: '500px',
      overflow: 'auto',
      zIndex: 9999,
      fontSize: '12px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
    }}>
      <h3 style={{ margin: '0 0 10px 0', color: '#333' }}>Menu Debug Info</h3>
      
      <div style={{ marginBottom: '10px' }}>
        <strong>Total Items:</strong> {menuItems.length}<br/>
        <strong>Items with Images:</strong> {itemsWithImages.length}<br/>
        <strong>Items without Images:</strong> {itemsWithoutImages.length}
      </div>

      {itemsWithImages.length > 0 && (
        <div style={{ marginBottom: '15px' }}>
          <h4 style={{ margin: '0 0 5px 0', color: '#666' }}>Items with Images:</h4>
          {itemsWithImages.slice(0, 3).map(item => (
            <div key={item.id} style={{ 
              marginBottom: '8px', 
              padding: '8px', 
              background: '#f9f9f9',
              borderRadius: '4px'
            }}>
              <div><strong>{item.name}</strong></div>
              <div style={{ fontSize: '10px', wordBreak: 'break-all' }}>
                {item.image}
              </div>
              <div style={{ marginTop: '5px' }}>
                <img 
                  src={item.image} 
                  alt={item.name}
                  style={{ 
                    width: '60px', 
                    height: '40px', 
                    objectFit: 'cover',
                    border: '1px solid #ddd'
                  }}
                  onLoad={() => console.log('✅ Debug image loaded:', item.image)}
                  onError={() => console.log('❌ Debug image failed:', item.image)}
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {itemsWithoutImages.length > 0 && (
        <div>
          <h4 style={{ margin: '0 0 5px 0', color: '#666' }}>Items without Images:</h4>
          {itemsWithoutImages.slice(0, 3).map(item => (
            <div key={item.id} style={{ 
              marginBottom: '5px',
              padding: '5px',
              background: '#fff3cd',
              borderRadius: '3px'
            }}>
              <strong>{item.name}</strong>
              <div style={{ fontSize: '10px', color: '#856404' }}>
                Image: {String(item.image)}
              </div>
            </div>
          ))}
        </div>
      )}

      <button 
        onClick={() => {
          console.log('🍽️ Full menu items data:', menuItems);
          console.log('🖼️ Items with images:', itemsWithImages);
        }}
        style={{
          marginTop: '10px',
          padding: '5px 10px',
          background: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '3px',
          cursor: 'pointer',
          fontSize: '11px'
        }}
      >
        Log to Console
      </button>
    </div>
  );
};

export default MenuDebug;
