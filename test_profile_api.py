#!/usr/bin/env python3
"""
Test the profile API response
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_profile_api():
    """Test the profile API"""
    print("🧪 Testing Profile API")
    print("=" * 40)
    
    # Login as employee
    emp_login = {"user_name": "EMP001", "password": "employee123"}
    response = requests.post(f"{BASE_URL}/auth/login/", json=emp_login)
    
    if response.status_code == 200:
        emp_token = response.json()['data']['access_token']
        print("✅ Employee login successful")
    else:
        print("❌ Employee login failed")
        return
    
    emp_headers = {"Authorization": f"Bearer {emp_token}"}
    
    # Test profile endpoint
    print("\n📋 Testing Profile Endpoint")
    response = requests.get(f"{BASE_URL}/delivery-agent/profile/", headers=emp_headers)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response Headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        try:
            profile_data = response.json()
            print("✅ Profile data retrieved successfully")
            print("\n📊 Profile Data Structure:")
            print(json.dumps(profile_data, indent=2))
            
            # Check specific fields
            if 'data' in profile_data:
                data = profile_data['data']
                print(f"\n🔍 Key Fields:")
                print(f"   Agent ID: {data.get('agent_id', 'N/A')}")
                print(f"   Full Name: {data.get('full_name', 'N/A')}")
                print(f"   Phone: {data.get('phone_number', 'N/A')}")
                print(f"   Email: {data.get('email', 'N/A')}")
                print(f"   Vehicle Type: {data.get('vehicle_type', 'N/A')}")
                print(f"   Vehicle Model: {data.get('vehicle_model', 'N/A')}")
                print(f"   License Plate: {data.get('license_plate', 'N/A')}")
                print(f"   Bank Name: {data.get('bank_name', 'N/A')}")
                print(f"   Account Number: {data.get('account_number', 'N/A')}")
                print(f"   Total Deliveries: {data.get('total_deliveries', 'N/A')}")
                print(f"   Total Earnings: {data.get('total_earnings', 'N/A')}")
                print(f"   Rating: {data.get('rating', 'N/A')}")
        except json.JSONDecodeError:
            print("❌ Invalid JSON response")
            print(f"Raw response: {response.text}")
    else:
        print(f"❌ Profile API failed: {response.status_code}")
        print(f"Response: {response.text}")

if __name__ == "__main__":
    test_profile_api()
