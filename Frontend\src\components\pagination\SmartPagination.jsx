import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Zap,
  TrendingUp,
  Clock,
  Settings,
  BarChart3,
} from "lucide-react";
import Button from "../common/Button";
import {
  useAdvancedPagination,
  usePredictivePagination,
} from "../../hooks/useAdvancedPagination";

const SmartPagination = ({
  fetchFunction,
  renderItem,
  filters = {},
  enablePrediction = true,
  enableAnalytics = true,
  enableVirtualization = false,
  className = "",
  onItemClick,
  itemHeight = 80,
  containerHeight = 600,
}) => {
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [adaptivePageSize, setAdaptivePageSize] = useState(20);

  // Use predictive pagination if enabled
  const pagination = usePredictivePagination({
    fetchFunction,
    initialPageSize: adaptivePageSize,
    learningEnabled: enablePrediction,
  });

  // Adaptive page size based on performance
  const adjustPageSize = useCallback(() => {
    if (!enableAnalytics) return;

    const analytics = pagination.getAnalytics();
    if (analytics) {
      const { avgResponseTime, cacheHitRate } = analytics;

      // Adjust page size based on performance
      if (avgResponseTime > 1000 && adaptivePageSize > 10) {
        // Slow responses, reduce page size
        setAdaptivePageSize((prev) => Math.max(10, prev - 5));
      } else if (
        avgResponseTime < 300 &&
        cacheHitRate > 80 &&
        adaptivePageSize < 50
      ) {
        // Fast responses with good cache, increase page size
        setAdaptivePageSize((prev) => Math.min(50, prev + 5));
      }
    }
  }, [enableAnalytics, pagination, adaptivePageSize]);

  // Adjust page size periodically
  useEffect(() => {
    if (enableAnalytics) {
      const interval = setInterval(adjustPageSize, 60000); // Every minute
      return () => clearInterval(interval);
    }
  }, [enableAnalytics, adjustPageSize]);

  // Smart page number calculation
  const getSmartPageNumbers = useCallback(() => {
    const { currentPage, totalPages } = pagination;
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    // Always show first page
    if (totalPages > 1) {
      rangeWithDots.push(1);
    }

    // Calculate range around current page
    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(totalPages - 1, currentPage + delta);
      i++
    ) {
      range.push(i);
    }

    // Add dots and range
    if (currentPage - delta > 2) {
      rangeWithDots.push("...");
    }

    rangeWithDots.push(...range);

    // Add dots before last page
    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push("...");
    }

    // Always show last page
    if (totalPages > 1 && totalPages !== 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  }, [pagination]);

  // Performance indicator
  const getPerformanceIndicator = useCallback(() => {
    if (!enableAnalytics) return null;

    const analytics = pagination.getAnalytics();
    if (!analytics) return null;

    const { avgResponseTime, cacheHitRate } = analytics;

    if (avgResponseTime < 300 && cacheHitRate > 70) {
      return { level: "excellent", color: "green", icon: Zap };
    } else if (avgResponseTime < 800 && cacheHitRate > 50) {
      return { level: "good", color: "blue", icon: TrendingUp };
    } else {
      return { level: "slow", color: "orange", icon: Clock };
    }
  }, [enableAnalytics, pagination]);

  // Predictive loading indicator
  const getPredictiveStatus = useCallback(() => {
    if (!enablePrediction) return null;

    const behavior = pagination.userBehavior;
    if (!behavior) return null;

    const totalVisits = Object.values(behavior.pageVisits).reduce(
      (a, b) => a + b,
      0
    );

    if (totalVisits > 10) {
      return "Learning your patterns";
    } else if (totalVisits > 5) {
      return "Building preferences";
    } else {
      return "Analyzing behavior";
    }
  }, [enablePrediction, pagination]);

  const performanceIndicator = getPerformanceIndicator();
  const predictiveStatus = getPredictiveStatus();
  const pageNumbers = getSmartPageNumbers();

  return (
    <div className={`smart-pagination ${className}`}>
      {/* Performance and Status Bar */}
      {(enableAnalytics || enablePrediction) && (
        <div className='flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg'>
          <div className='flex items-center space-x-4'>
            {/* Performance Indicator */}
            {performanceIndicator && (
              <div
                className={`flex items-center text-${performanceIndicator.color}-600`}
              >
                <performanceIndicator.icon size={16} className='mr-1' />
                <span className='text-sm font-medium'>
                  {performanceIndicator.level}
                </span>
              </div>
            )}

            {/* Predictive Status */}
            {predictiveStatus && (
              <div className='flex items-center text-purple-600'>
                <TrendingUp size={16} className='mr-1' />
                <span className='text-sm'>{predictiveStatus}</span>
              </div>
            )}

            {/* Adaptive Page Size Indicator */}
            {enableAnalytics && (
              <div className='text-sm text-gray-600'>
                Auto-size: {adaptivePageSize}
              </div>
            )}
          </div>

          {/* Analytics Toggle */}
          {enableAnalytics && (
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setShowAnalytics(!showAnalytics)}
              className='flex items-center'
            >
              <BarChart3 size={16} className='mr-1' />
              Analytics
            </Button>
          )}
        </div>
      )}

      {/* Analytics Panel */}
      {showAnalytics && enableAnalytics && (
        <div className='mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg'>
          <h3 className='font-medium text-blue-900 mb-3'>
            Performance Analytics
          </h3>
          {(() => {
            const analytics = pagination.getAnalytics();
            if (!analytics)
              return <p className='text-blue-700'>No data available</p>;

            return (
              <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                <div>
                  <div className='text-blue-600 font-medium'>Requests</div>
                  <div className='text-blue-900'>{analytics.totalRequests}</div>
                </div>
                <div>
                  <div className='text-blue-600 font-medium'>
                    Cache Hit Rate
                  </div>
                  <div className='text-blue-900'>
                    {analytics.cacheHitRate.toFixed(1)}%
                  </div>
                </div>
                <div>
                  <div className='text-blue-600 font-medium'>Avg Response</div>
                  <div className='text-blue-900'>
                    {analytics.avgResponseTime.toFixed(0)}ms
                  </div>
                </div>
                <div>
                  <div className='text-blue-600 font-medium'>Cache Size</div>
                  <div className='text-blue-900'>
                    {analytics.cacheSize} pages
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      )}

      {/* Data Display */}
      <div className='mb-6'>
        {pagination.loading ? (
          <div className='flex items-center justify-center py-12'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500'></div>
            <span className='ml-3 text-gray-600'>Loading...</span>
          </div>
        ) : pagination.error ? (
          <div className='text-center py-12'>
            <p className='text-red-600 mb-4'>{pagination.error}</p>
            <Button onClick={pagination.refresh} variant='outline'>
              Try Again
            </Button>
          </div>
        ) : pagination.data.length === 0 ? (
          <div className='text-center py-12'>
            <p className='text-gray-500'>No items found</p>
          </div>
        ) : (
          <div className='space-y-4'>
            {pagination.data.map((item, index) => (
              <div
                key={item.id || index}
                onClick={() => onItemClick?.(item)}
                className='cursor-pointer hover:bg-gray-50 transition-colors'
              >
                {renderItem ? (
                  renderItem(item, index)
                ) : (
                  <div className='p-4 border border-gray-200 rounded-lg'>
                    <pre className='text-sm'>
                      {JSON.stringify(item, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Smart Pagination Controls */}
      {pagination.totalPages > 1 && (
        <div className='flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0'>
          {/* Page Info */}
          <div className='text-sm text-gray-600'>
            Showing {(pagination.currentPage - 1) * pagination.pageSize + 1} to{" "}
            {Math.min(
              pagination.currentPage * pagination.pageSize,
              pagination.totalItems
            )}{" "}
            of {pagination.totalItems} results
          </div>

          {/* Page Navigation */}
          <div className='flex items-center space-x-2'>
            {/* Previous Button */}
            <Button
              variant='outline'
              size='sm'
              onClick={() =>
                pagination.onPageChange(pagination.currentPage - 1, filters)
              }
              disabled={!pagination.hasPrevious || pagination.loading}
              className='flex items-center'
            >
              <ChevronLeft size={16} />
              <span className='ml-1 hidden sm:inline'>Previous</span>
            </Button>

            {/* Page Numbers */}
            <div className='flex items-center space-x-1'>
              {pageNumbers.map((page, index) => (
                <React.Fragment key={index}>
                  {page === "..." ? (
                    <span className='px-3 py-2 text-gray-400'>
                      <MoreHorizontal size={16} />
                    </span>
                  ) : (
                    <Button
                      variant={
                        page === pagination.currentPage ? "primary" : "outline"
                      }
                      size='sm'
                      onClick={() => pagination.onPageChange(page, filters)}
                      disabled={pagination.loading}
                      className='min-w-[40px]'
                    >
                      {page}
                    </Button>
                  )}
                </React.Fragment>
              ))}
            </div>

            {/* Next Button */}
            <Button
              variant='outline'
              size='sm'
              onClick={() =>
                pagination.onPageChange(pagination.currentPage + 1, filters)
              }
              disabled={!pagination.hasNext || pagination.loading}
              className='flex items-center'
            >
              <span className='mr-1 hidden sm:inline'>Next</span>
              <ChevronRight size={16} />
            </Button>
          </div>

          {/* Page Size Selector */}
          <div className='flex items-center space-x-2 text-sm'>
            <span className='text-gray-600'>Show:</span>
            <select
              value={pagination.pageSize}
              onChange={(e) =>
                pagination.onPageSizeChange(Number(e.target.value), filters)
              }
              className='border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500'
              disabled={pagination.loading}
            >
              {[10, 20, 30, 50].map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
            <span className='text-gray-600'>per page</span>
          </div>
        </div>
      )}

      {/* Predictive Insights */}
      {enablePrediction && pagination.userBehavior && (
        <div className='mt-4 p-3 bg-purple-50 border border-purple-200 rounded-lg'>
          <div className='text-sm text-purple-700'>
            <strong>Smart Insights:</strong> Based on your usage patterns, you
            typically visit{" "}
            {Object.keys(pagination.userBehavior.pageVisits).length} different
            pages per session.
          </div>
        </div>
      )}
    </div>
  );
};

export default SmartPagination;
