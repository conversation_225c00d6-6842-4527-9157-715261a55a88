import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Send, CheckCircle, AlertCircle, Phone, Mail, MapPin } from 'lucide-react';
import { useSupport } from '../../context/SupportContext';
import { useAuth } from '../../context/AuthContext';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import FormControl from '../common/FormControl';

const ContactForm = () => {
  const { user } = useAuth();
  const { createTicket } = useSupport();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm({
    defaultValues: {
      name: user?.name || '',
      email: user?.email || '',
      category: 'general',
      priority: 'medium',
      subject: '',
      description: '',
      orderId: ''
    }
  });

  const selectedCategory = watch('category');

  const categories = [
    { value: 'general', label: 'General Inquiry', icon: '💬' },
    { value: 'order', label: 'Order Issue', icon: '📦' },
    { value: 'payment', label: 'Payment Problem', icon: '💳' },
    { value: 'delivery', label: 'Delivery Issue', icon: '🚚' },
    { value: 'account', label: 'Account Problem', icon: '👤' },
    { value: 'restaurant', label: 'Restaurant Issue', icon: '🍽️' },
    { value: 'technical', label: 'Technical Problem', icon: '⚙️' },
    { value: 'feedback', label: 'Feedback & Suggestions', icon: '💡' }
  ];

  const priorities = [
    { value: 'low', label: 'Low', color: 'text-green-600' },
    { value: 'medium', label: 'Medium', color: 'text-yellow-600' },
    { value: 'high', label: 'High', color: 'text-red-600' }
  ];

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const ticket = createTicket(data);
      console.log('Ticket created:', ticket);
      
      setIsSubmitted(true);
      reset();
    } catch (error) {
      console.error('Error creating ticket:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <Card className="max-w-2xl mx-auto text-center p-8">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle size={32} className="text-green-600" />
        </div>
        <h2 className="text-2xl font-bold mb-2">Ticket Submitted Successfully!</h2>
        <p className="text-gray-600 mb-6">
          Thank you for contacting us. We've received your support request and will get back to you within 24 hours.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            variant="primary"
            onClick={() => setIsSubmitted(false)}
          >
            Submit Another Ticket
          </Button>
          <Button
            variant="outline"
            to="/support/tickets"
          >
            View My Tickets
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Contact Form */}
        <div className="lg:col-span-2">
          <Card>
            <div className="p-6">
              <h2 className="text-2xl font-bold mb-2">Contact Support</h2>
              <p className="text-gray-600 mb-6">
                Fill out the form below and we'll get back to you as soon as possible.
              </p>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Personal Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormControl
                    label="Full Name"
                    error={errors.name?.message}
                    required
                  >
                    <Input
                      placeholder="Enter your full name"
                      error={errors.name?.message}
                      {...register('name', { required: 'Name is required' })}
                    />
                  </FormControl>

                  <FormControl
                    label="Email Address"
                    error={errors.email?.message}
                    required
                  >
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      error={errors.email?.message}
                      {...register('email', {
                        required: 'Email is required',
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: 'Invalid email address'
                        }
                      })}
                    />
                  </FormControl>
                </div>

                {/* Category */}
                <FormControl
                  label="Category"
                  error={errors.category?.message}
                  required
                >
                  <select
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    {...register('category', { required: 'Please select a category' })}
                  >
                    {categories.map((cat) => (
                      <option key={cat.value} value={cat.value}>
                        {cat.icon} {cat.label}
                      </option>
                    ))}
                  </select>
                </FormControl>

                {/* Order ID (conditional) */}
                {(selectedCategory === 'order' || selectedCategory === 'delivery' || selectedCategory === 'payment') && (
                  <FormControl
                    label="Order ID (Optional)"
                    error={errors.orderId?.message}
                  >
                    <Input
                      placeholder="Enter your order ID if applicable"
                      {...register('orderId')}
                    />
                  </FormControl>
                )}

                {/* Priority */}
                <FormControl
                  label="Priority"
                  error={errors.priority?.message}
                  required
                >
                  <select
                    className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    {...register('priority', { required: 'Please select priority' })}
                  >
                    {priorities.map((priority) => (
                      <option key={priority.value} value={priority.value}>
                        {priority.label}
                      </option>
                    ))}
                  </select>
                </FormControl>

                {/* Subject */}
                <FormControl
                  label="Subject"
                  error={errors.subject?.message}
                  required
                >
                  <Input
                    placeholder="Brief description of your issue"
                    error={errors.subject?.message}
                    {...register('subject', { required: 'Subject is required' })}
                  />
                </FormControl>

                {/* Description */}
                <FormControl
                  label="Description"
                  error={errors.description?.message}
                  required
                >
                  <textarea
                    rows={6}
                    placeholder="Please provide detailed information about your issue..."
                    className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                      errors.description ? 'border-red-500' : 'border-gray-300'
                    }`}
                    {...register('description', {
                      required: 'Description is required',
                      minLength: {
                        value: 10,
                        message: 'Description must be at least 10 characters'
                      }
                    })}
                  />
                </FormControl>

                {/* Submit Button */}
                <Button
                  type="submit"
                  variant="primary"
                  size="large"
                  fullWidth
                  loading={isSubmitting}
                  icon={<Send size={16} />}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Ticket'}
                </Button>
              </form>
            </div>
          </Card>
        </div>

        {/* Contact Information */}
        <div className="lg:col-span-1">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Other Ways to Reach Us</h3>
            
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                  <Phone size={18} className="text-primary-600" />
                </div>
                <div>
                  <h4 className="font-medium">Phone Support</h4>
                  <p className="text-gray-600 text-sm">+****************</p>
                  <p className="text-gray-500 text-xs">Mon-Sun: 9 AM - 11 PM</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                  <Mail size={18} className="text-primary-600" />
                </div>
                <div>
                  <h4 className="font-medium">Email Support</h4>
                  <p className="text-gray-600 text-sm"><EMAIL></p>
                  <p className="text-gray-500 text-xs">Response within 24 hours</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3 flex-shrink-0">
                  <MapPin size={18} className="text-primary-600" />
                </div>
                <div>
                  <h4 className="font-medium">Office Address</h4>
                  <p className="text-gray-600 text-sm">
                    123 Food Street<br />
                    Kabul, Afghanistan
                  </p>
                </div>
              </div>
            </div>
          </Card>

          {/* Quick Tips */}
          <Card className="p-6 mt-6 bg-blue-50 border-blue-200">
            <div className="flex items-start">
              <AlertCircle size={20} className="text-blue-600 mr-3 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-800 mb-2">Quick Tips</h4>
                <ul className="text-blue-700 text-sm space-y-1">
                  <li>• Include your order ID for faster resolution</li>
                  <li>• Be specific about the issue you're experiencing</li>
                  <li>• Check our FAQ section for instant answers</li>
                  <li>• Use live chat for urgent matters</li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ContactForm;
