import axios from "axios";

const API_BASE_URL = "http://127.0.0.1:8000/api/delivery-agent";

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add auth token to requests
apiClient.interceptors.request.use((config) => {
  // Try to get token from the main auth system first
  const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
  const token = user.access_token || localStorage.getItem("access_token");

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle token refresh on 401
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Try to refresh token
      const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
      const refreshToken =
        user.refresh_token || localStorage.getItem("refresh_token");

      if (refreshToken) {
        try {
          const response = await axios.post(
            "http://127.0.0.1:8000/api/auth/token/refresh/",
            {
              refresh: refreshToken,
            }
          );
          const newToken = response.data.access;

          // Update both storage methods
          localStorage.setItem("access_token", newToken);
          const updatedUser = { ...user, access_token: newToken };
          localStorage.setItem("afghanSofraUser", JSON.stringify(updatedUser));

          // Retry original request
          error.config.headers.Authorization = `Bearer ${newToken}`;
          return apiClient.request(error.config);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          localStorage.removeItem("access_token");
          localStorage.removeItem("refresh_token");
          localStorage.removeItem("afghanSofraUser");
          window.location.href = "/login";
        }
      } else {
        // No refresh token, redirect to login
        localStorage.removeItem("afghanSofraUser");
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

export const deliveryAgentApi = {
  // Registration Functions
  async register(registrationData) {
    try {
      const response = await apiClient.post("/register/", registrationData);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async checkApplicationStatus(agentId = null, phoneNumber = null) {
    try {
      const params = {};
      if (agentId) params.agent_id = agentId;
      if (phoneNumber) params.phone_number = phoneNumber;

      const response = await apiClient.get("/application-status/", { params });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async updateStatus(agentId, status, phoneNumber = null) {
    try {
      const data = { status };
      if (agentId) data.agent_id = agentId;
      if (phoneNumber) data.phone_number = phoneNumber;

      const response = await apiClient.post("/update-status/", data);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Employee Clock In/Out Functions
  async clockIn() {
    try {
      const response = await apiClient.post("/clock-in/");
      return { success: true, data: response.data.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async clockOut() {
    try {
      const response = await apiClient.post("/clock-out/");
      return { success: true, data: response.data.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async setBreak() {
    try {
      const response = await apiClient.post("/set-break/");
      return { success: true, data: response.data.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async returnFromBreak() {
    try {
      const response = await apiClient.post("/return-from-break/");
      return { success: true, data: response.data.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // LEGACY: Enhanced Registration (Disabled in employee system)
  async registerEnhanced(formData) {
    try {
      const response = await apiClient.post("/register-enhanced/", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Document Verification
  async uploadDocument(documentType, file) {
    try {
      const formData = new FormData();
      formData.append("document_type", documentType);
      formData.append("file", file);

      const response = await apiClient.post("/upload-document/", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Background Check
  async initiateBackgroundCheck() {
    try {
      const response = await apiClient.post("/background-check/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Profile Management
  async getProfile() {
    try {
      const response = await apiClient.get("/profile/");
      return response;
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async updateProfile(profileData) {
    try {
      const response = await apiClient.patch("/profile/", profileData);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Order Management
  async getMyOrders(params = {}) {
    try {
      const response = await apiClient.get("/my-orders/", { params });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async acceptOrder(orderId) {
    try {
      const response = await apiClient.post("/accept-order/", {
        order_id: orderId,
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async rejectOrder(orderId, reason) {
    try {
      const response = await apiClient.post("/reject-order/", {
        order_id: orderId,
        reason: reason,
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async updateOrderStatus(orderId, statusData) {
    try {
      const payload = {
        order_id: orderId,
        ...statusData,
      };

      const response = await apiClient.post("/update-order-status/", payload);
      return response;
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async collectCash(orderId, amount) {
    try {
      const response = await apiClient.post("/collect-cash/", {
        order_id: orderId,
        amount_collected: amount,
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async registerAgent(registrationData) {
    try {
      const response = await apiClient.post("/register/", registrationData);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Location & Status
  async updateLocation(locationData) {
    try {
      const response = await apiClient.post("/update-location/", locationData);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async updateAvailability(availabilityData) {
    try {
      const response = await apiClient.post(
        "/update-availability/",
        availabilityData
      );
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async toggleOnlineStatus() {
    try {
      const response = await apiClient.post("/toggle-online-status/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Dashboard
  async getDashboard() {
    try {
      const response = await apiClient.get("/dashboard/");
      return response;
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Get assigned orders
  async getAssignedOrders() {
    try {
      const response = await apiClient.get("/assigned-orders/");
      return response;
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Clock in/out with unified method
  async clockInOut(action) {
    try {
      const response = await apiClient.post(`/${action}/`);
      return response;
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Shifts
  async startShift(shiftData) {
    try {
      const response = await apiClient.post("/start-shift/", shiftData);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async endShift(shiftData) {
    try {
      const response = await apiClient.post("/end-shift/", shiftData);
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getShifts() {
    try {
      const response = await apiClient.get("/shifts/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Earnings
  async getEarningsSummary() {
    try {
      const response = await apiClient.get("/earnings-summary/");
      return response;
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Payouts
  async requestPayout(amount, paymentMethod = "bank_transfer") {
    try {
      const response = await apiClient.post("/payout-request/", {
        amount: amount,
        payment_method: paymentMethod,
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  async getPayoutHistory() {
    try {
      const response = await apiClient.get("/payout-request/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Performance
  async getPerformanceMetrics() {
    try {
      const response = await apiClient.get("/performance-metrics/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Location History
  async getLocationHistory(days = 1, limit = 100) {
    try {
      const response = await apiClient.get("/location-history/", {
        params: { days, limit },
      });
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Delivery Zones
  async getDeliveryZones() {
    try {
      const response = await apiClient.get("/zones/");
      return { success: true, data: response.data };
    } catch (error) {
      return { success: false, error: error.response?.data || error.message };
    }
  },

  // Utility functions
  getCurrentLocation() {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported by this browser."));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
          });
        },
        (error) => {
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000,
        }
      );
    });
  },

  // Real-time location tracking
  startLocationTracking(callback, interval = 30000) {
    const trackingInterval = setInterval(async () => {
      try {
        const location = await this.getCurrentLocation();
        const result = await this.updateLocation({
          latitude: location.latitude,
          longitude: location.longitude,
          activity_type: "idle",
        });

        if (callback) {
          callback(result);
        }
      } catch (error) {
        console.error("Location tracking error:", error);
      }
    }, interval);

    return trackingInterval;
  },

  stopLocationTracking(trackingInterval) {
    if (trackingInterval) {
      clearInterval(trackingInterval);
    }
  },

  // Dynamic delivery fee calculation
  async calculateDeliveryFee(
    restaurantLocation,
    deliveryLocation,
    orderValue = 20,
    priority = "normal"
  ) {
    try {
      const response = await apiClient.post("/dynamic-delivery-fee/", {
        restaurant_location: restaurantLocation,
        delivery_location: deliveryLocation,
        order_value: orderValue,
        priority: priority,
      });
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error("Error calculating delivery fee:", error);
      return {
        success: false,
        error: error.response?.data || {
          message: "Failed to calculate delivery fee",
        },
      };
    }
  },

  // Real-time order tracking
  async trackOrder(orderId) {
    try {
      const response = await apiClient.get(`/track-order/${orderId}/`);
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error("Error tracking order:", error);
      return {
        success: false,
        error: error.response?.data || { message: "Failed to track order" },
      };
    }
  },

  // Get agents in area
  async getAgentsInArea(latitude, longitude, radius = 10) {
    try {
      const response = await apiClient.get("/agents-in-area/", {
        params: { lat: latitude, lng: longitude, radius: radius },
      });
      return { success: true, data: response.data };
    } catch (error) {
      console.error("Error getting agents in area:", error);
      return {
        success: false,
        error: error.response?.data || {
          message: "Failed to get agents in area",
        },
      };
    }
  },

  // Performance leaderboard
  async getPerformanceLeaderboard(periodDays = 30, limit = 10) {
    try {
      const response = await apiClient.get("/performance-leaderboard/", {
        params: { period_days: periodDays, limit: limit },
      });
      return { success: true, data: response.data };
    } catch (error) {
      console.error("Error getting performance leaderboard:", error);
      return {
        success: false,
        error: error.response?.data || {
          message: "Failed to get performance leaderboard",
        },
      };
    }
  },

  // Zone management (admin only)
  async getZoneStats() {
    try {
      const response = await apiClient.get("/zone-management/");
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error("Error getting zone stats:", error);
      return {
        success: false,
        error: error.response?.data || { message: "Failed to get zone stats" },
      };
    }
  },

  async assignAgentToZones(agentId, zoneAssignments) {
    try {
      const response = await apiClient.post("/zone-management/", {
        agent_id: agentId,
        zone_assignments: zoneAssignments,
      });
      return { success: true, data: response.data };
    } catch (error) {
      console.error("Error assigning agent to zones:", error);
      return {
        success: false,
        error: error.response?.data || {
          message: "Failed to assign agent to zones",
        },
      };
    }
  },

  // Enhanced performance metrics with dynamic calculation
  async getPerformanceMetrics(periodDays = 30) {
    try {
      const response = await apiClient.get("/performance-metrics/", {
        params: { period_days: periodDays },
      });
      return { success: true, data: response.data.data };
    } catch (error) {
      console.error("Error getting performance metrics:", error);
      return {
        success: false,
        error: error.response?.data || {
          message: "Failed to get performance metrics",
        },
      };
    }
  },
};

export default deliveryAgentApi;
