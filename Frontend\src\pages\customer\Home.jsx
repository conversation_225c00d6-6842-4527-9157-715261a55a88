import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Search, MapPin, Star, Clock, ArrowRight, Filter } from "lucide-react";
import { API_BASE_URL } from "../../config/api";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import Loader from "../../components/common/Loader";
import QuickSearch from "../../components/search/QuickSearch";
import { RestaurantGridSkeleton } from "../../components/skeleton/RestaurantCardSkeleton";

const Home = () => {
  const [activeCategory, setActiveCategory] = useState("All");
  const [featuredRestaurants, setFeaturedRestaurants] = useState([]);
  const [allRestaurants, setAllRestaurants] = useState([]);
  const [cuisineCategories, setCuisineCategories] = useState(["All"]);
  const [searchQuery, setSearchQuery] = useState("");
  const [loading, setLoading] = useState(true);

  // Fetch restaurants from API
  useEffect(() => {
    const fetchRestaurants = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/restaurant/restaurants/`);

        if (response.ok) {
          const restaurants = await response.json();
          setAllRestaurants(restaurants);

          // Extract unique cuisines for categories
          const cuisines = [...new Set(restaurants.map((r) => r.name))]; // Using name as cuisine for now
          setCuisineCategories(["All", ...cuisines.slice(0, 8)]); // Limit to 8 categories
        } else {
          console.error("Failed to fetch restaurants:", response.status);
        }
      } catch (error) {
        console.error("Error fetching restaurants:", error);
        // Fallback to empty arrays
        setAllRestaurants([]);
        setCuisineCategories(["All"]);
      } finally {
        setLoading(false);
      }
    };

    fetchRestaurants();
  }, []);

  // Filter restaurants based on category and search
  useEffect(() => {
    if (allRestaurants.length === 0) return;

    setLoading(true);
    let filtered = allRestaurants;

    if (activeCategory !== "All") {
      filtered = filtered.filter((restaurant) =>
        restaurant.name.toLowerCase().includes(activeCategory.toLowerCase())
      );
    }

    if (searchQuery) {
      filtered = filtered.filter(
        (restaurant) =>
          restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          restaurant.description
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase())
      );
    }

    // Simulate loading delay
    setTimeout(() => {
      setFeaturedRestaurants(filtered.slice(0, 6)); // Show more restaurants
      setLoading(false);
    }, 300);
  }, [activeCategory, searchQuery, allRestaurants]);

  return (
    <div className='animate-fade-in'>
      {/* Hero Section */}
      <section
        className='relative flex items-center bg-cover bg-center h-[500px]'
        style={{
          backgroundImage:
            "url('https://images.pexels.com/photos/958545/pexels-photo-958545.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1')",
        }}
      >
        <div className='absolute inset-0 bg-black bg-opacity-50'></div>
        <div className='z-10 relative mx-auto px-4 container'>
          <div className='max-w-2xl'>
            <h1 className='mb-4 font-poppins font-bold text-white text-4xl md:text-5xl'>
              Experience Authentic Afghan Cuisine
            </h1>
            <p className='mb-8 text-gray-200 text-lg'>
              Discover the richness of Afghan flavors from the best restaurants,
              delivered right to your door.
            </p>
          </div>
        </div>
      </section>

      {/* Quick Search Component */}
      <section className='relative mx-auto container'>
        <QuickSearch />
      </section>

      {/* Cuisine Categories */}
      <section className='bg-background-light py-10'>
        <div className='mx-auto px-4 container'>
          <h2 className='mb-6 font-poppins font-semibold text-2xl'>
            Popular Cuisines
          </h2>

          <div className='flex pb-4 overflow-x-auto hide-scrollbar'>
            <div className='flex space-x-3'>
              {cuisineCategories.map((category) => (
                <button
                  key={category}
                  className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                    activeCategory === category
                      ? "bg-primary-500 text-white"
                      : "bg-white border border-gray-200 text-text-primary hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Restaurants */}
      <section className='bg-white py-12'>
        <div className='mx-auto px-4 container'>
          <div className='flex justify-between items-center mb-8'>
            <h2 className='font-poppins font-semibold text-2xl'>
              Featured Restaurants
            </h2>
            <Link
              to='/restaurants'
              className='flex items-center text-primary-500 hover:text-primary-600'
            >
              View All <ArrowRight size={16} className='ml-1' />
            </Link>
          </div>

          <div className='gap-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3'>
            {loading ? (
              <RestaurantGridSkeleton count={3} />
            ) : featuredRestaurants.length === 0 ? (
              <div className='col-span-3 py-12 text-text-secondary text-center'>
                No restaurants found for this category or search.
              </div>
            ) : (
              featuredRestaurants.map((restaurant) => (
                <Link to={`/restaurants/${restaurant.id}`} key={restaurant.id}>
                  <Card
                    className='h-full transition-transform hover:-translate-y-1 duration-200'
                    hoverable
                  >
                    <div className='relative -mx-5 -mt-5 mb-4 rounded-t-lg h-48 overflow-hidden'>
                      <div className='top-4 left-4 z-10 absolute'>
                        {restaurant.is_active ? (
                          <Badge variant='success' size='small'>
                            Open Now
                          </Badge>
                        ) : (
                          <Badge variant='danger' size='small'>
                            Closed
                          </Badge>
                        )}
                      </div>
                      <img
                        src={restaurant.banner || "/placeholder-restaurant.jpg"}
                        alt={restaurant.name}
                        className='w-full h-full object-cover'
                        onError={(e) => {
                          e.target.src = "/placeholder-restaurant.jpg";
                        }}
                      />
                    </div>

                    <div className='flex items-start'>
                      <div className='flex-shrink-0 bg-gray-100 mr-4 rounded-lg w-16 h-16 overflow-hidden'>
                        <img
                          src={restaurant.logo || "/placeholder-logo.jpg"}
                          alt={restaurant.name}
                          className='w-full h-full object-cover'
                          onError={(e) => {
                            e.target.src = "/placeholder-logo.jpg";
                          }}
                        />
                      </div>

                      <div>
                        <h3 className='font-semibold text-lg'>
                          {restaurant.name}
                        </h3>
                        <div className='flex items-center mt-1 text-text-secondary text-sm'>
                          <Star size={16} className='mr-1 text-yellow-500' />
                          <span>{restaurant.rating}</span>
                          <span className='mx-2'>•</span>
                          <span>
                            $$ •{" "}
                            {restaurant.cuisine_types &&
                            restaurant.cuisine_types.length > 0
                              ? restaurant.cuisine_types.map(ct => typeof ct === 'string' ? ct : ct.name || 'Unknown').join(", ")
                              : "Afghan Cuisine"}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className='flex justify-between items-center mt-4 text-text-secondary text-sm'>
                      <div className='flex items-center'>
                        <Clock size={16} className='mr-1' />
                        <span>
                          {restaurant.average_preparation_time || 30} min
                        </span>
                      </div>
                      <div className='flex items-center'>
                        <MapPin size={16} className='mr-1' />
                        <span>2.5 km</span>
                      </div>
                      <div>
                        $
                        {restaurant.delivery_fee
                          ? parseFloat(restaurant.delivery_fee).toFixed(2)
                          : "0.00"}{" "}
                        delivery
                      </div>
                    </div>
                  </Card>
                </Link>
              ))
            )}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className='bg-background-light py-16'>
        <div className='mx-auto px-4 container'>
          <div className='mx-auto mb-12 max-w-3xl text-center'>
            <h2 className='mb-4 font-poppins font-semibold text-3xl'>
              How Afghan Sofra Works
            </h2>
            <p className='text-text-secondary'>
              Enjoy authentic Afghan cuisine in just a few simple steps.
            </p>
          </div>

          <div className='gap-8 grid grid-cols-1 md:grid-cols-3'>
            <div className='text-center'>
              <div className='flex justify-center items-center bg-primary-100 mx-auto mb-4 rounded-full w-16 h-16'>
                <Search size={24} className='text-primary-500' />
              </div>
              <h3 className='mb-2 font-poppins font-semibold text-lg'>
                Find Restaurants
              </h3>
              <p className='text-text-secondary'>
                Browse restaurants offering authentic Afghan cuisine in your
                area.
              </p>
            </div>

            <div className='text-center'>
              <div className='flex justify-center items-center bg-primary-100 mx-auto mb-4 rounded-full w-16 h-16'>
                <Filter size={24} className='text-primary-500' />
              </div>
              <h3 className='mb-2 font-poppins font-semibold text-lg'>
                Select Your Meal
              </h3>
              <p className='text-text-secondary'>
                Choose from a variety of traditional Afghan dishes from your
                favorite restaurant.
              </p>
            </div>

            <div className='text-center'>
              <div className='flex justify-center items-center bg-primary-100 mx-auto mb-4 rounded-full w-16 h-16'>
                <MapPin size={24} className='text-primary-500' />
              </div>
              <h3 className='mb-2 font-poppins font-semibold text-lg'>
                Get It Delivered
              </h3>
              <p className='text-text-secondary'>
                Your delicious meal is prepared and delivered right to your
                doorstep.
              </p>
            </div>
          </div>

          <div className='mt-12 text-center'>
            <Button to='/restaurants' variant='primary' size='large'>
              Order Now
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className='bg-primary-500 py-16'>
        <div className='mx-auto px-4 container'>
          <div className='flex md:flex-row flex-col justify-between items-center'>
            <div className='mb-8 md:mb-0 max-w-xl'>
              <h2 className='mb-4 font-poppins font-bold text-white text-3xl'>
                Are you a Restaurant Owner?
              </h2>
              <p className='mb-6 text-white text-opacity-90'>
                Partner with Afghan Sofra to reach more customers and grow your
                business. Our platform helps you manage orders, menu, and
                deliveries with ease.
              </p>
              <Button
                variant='outline'
                size='large'
                className='hover:bg-white border-white text-white hover:text-primary-500'
                to='/restaurant-partner'
              >
                Join as Partner
              </Button>
            </div>

            <div className='shadow-lg rounded-lg w-full md:w-1/3 overflow-hidden'>
              <img
                src='https://images.pexels.com/photos/2696064/pexels-photo-2696064.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
                alt='Restaurant Partner'
                className='w-full h-64 object-cover'
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
