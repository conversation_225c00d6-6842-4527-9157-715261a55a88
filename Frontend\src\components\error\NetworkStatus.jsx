import React, { useState, useEffect } from 'react';
import { WifiOff, Wifi, AlertCircle, CheckCircle } from 'lucide-react';
import { useNetworkStatus } from '../../hooks/useRetry';

const NetworkStatus = ({ className = "" }) => {
  const { isOnline, wasOffline, isReconnecting } = useNetworkStatus();
  const [showStatus, setShowStatus] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');

  useEffect(() => {
    if (!isOnline) {
      setShowStatus(true);
      setStatusMessage('You are offline. Some features may not work.');
    } else if (isReconnecting) {
      setShowStatus(true);
      setStatusMessage('Connection restored! Syncing data...');
      
      // Hide the reconnection message after 3 seconds
      const timer = setTimeout(() => {
        setShowStatus(false);
      }, 3000);
      
      return () => clearTimeout(timer);
    } else if (wasOffline) {
      setShowStatus(false);
    }
  }, [isOnline, wasOffline, isReconnecting]);

  if (!showStatus) return null;

  return (
    <div className={`fixed top-0 left-0 right-0 z-50 ${className}`}>
      <div className={`px-4 py-3 text-center text-sm font-medium transition-all duration-300 ${
        isOnline 
          ? 'bg-green-500 text-white' 
          : 'bg-red-500 text-white'
      }`}>
        <div className="flex items-center justify-center space-x-2">
          {isOnline ? (
            <>
              <CheckCircle size={16} />
              <span>{statusMessage}</span>
            </>
          ) : (
            <>
              <WifiOff size={16} />
              <span>{statusMessage}</span>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

// Floating network indicator (bottom corner)
export const FloatingNetworkStatus = () => {
  const { isOnline } = useNetworkStatus();
  const [showIndicator, setShowIndicator] = useState(false);

  useEffect(() => {
    if (!isOnline) {
      setShowIndicator(true);
    } else {
      // Hide after a delay when back online
      const timer = setTimeout(() => {
        setShowIndicator(false);
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [isOnline]);

  if (!showIndicator) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className={`px-3 py-2 rounded-lg shadow-lg text-sm font-medium flex items-center space-x-2 ${
        isOnline 
          ? 'bg-green-500 text-white' 
          : 'bg-red-500 text-white'
      }`}>
        {isOnline ? (
          <>
            <Wifi size={16} />
            <span>Back online</span>
          </>
        ) : (
          <>
            <WifiOff size={16} />
            <span>Offline</span>
          </>
        )}
      </div>
    </div>
  );
};

// Network status hook for components
export const useNetworkStatusIndicator = () => {
  const { isOnline, wasOffline } = useNetworkStatus();
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);

  useEffect(() => {
    if (!isOnline) {
      setShowOfflineMessage(true);
    } else if (wasOffline) {
      // Show reconnection message briefly
      setShowOfflineMessage(false);
    }
  }, [isOnline, wasOffline]);

  return {
    isOnline,
    showOfflineMessage,
    wasOffline
  };
};

export default NetworkStatus;
