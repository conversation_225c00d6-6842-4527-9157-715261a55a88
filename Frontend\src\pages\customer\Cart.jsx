import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useCart } from "../../context/CartContext";
import {
  ArrowLeft,
  Plus,
  Minus,
  Trash2,
  AlertCircle,
  ShoppingBag,
} from "lucide-react";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import { restaurantApi } from "../../utils/restaurantApi";

const Cart = () => {
  const { cart, updateItemQuantity, removeFromCart, clearCart } = useCart();
  const navigate = useNavigate();
  const [restaurant, setRestaurant] = useState(null);
  const [loading, setLoading] = useState(false);

  // Fetch restaurant data when cart has a restaurant
  useEffect(() => {
    const fetchRestaurant = async () => {
      if (cart.restaurantId) {
        try {
          setLoading(true);
          const response = await restaurantApi.getRestaurant(cart.restaurantId);
          if (response.success) {
            setRestaurant(response.data);
          }
        } catch (error) {
          console.error("Error fetching restaurant:", error);
        } finally {
          setLoading(false);
        }
      } else {
        setRestaurant(null);
      }
    };

    fetchRestaurant();
  }, [cart.restaurantId]);

  const handleCheckout = () => {
    if (cart.items.length > 0) {
      navigate("/checkout");
    }
  };

  if (cart.items.length === 0) {
    return (
      <div className='container mx-auto px-4 py-12 max-w-4xl animate-fade-in'>
        <div className='text-center'>
          <div className='w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6'>
            <ShoppingBag size={32} className='text-text-secondary' />
          </div>
          <h1 className='text-2xl font-poppins font-semibold mb-4'>
            Your Cart is Empty
          </h1>
          <p className='text-text-secondary mb-8'>
            Looks like you haven't added any items to your cart yet.
          </p>
          <Button variant='primary' size='large' to='/restaurants'>
            Browse Restaurants
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8 max-w-4xl animate-fade-in'>
      <div className='flex items-center mb-8'>
        <Link
          to='/restaurants'
          className='text-text-primary hover:text-primary-500 mr-3'
        >
          <ArrowLeft size={20} />
        </Link>
        <h1 className='text-2xl font-poppins font-semibold'>Your Cart</h1>
      </div>

      <div className='flex flex-col lg:flex-row gap-8'>
        {/* Cart Items */}
        <div className='lg:w-2/3'>
          <Card className='mb-6'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex items-center'>
                {restaurant?.logo && (
                  <div className='w-12 h-12 rounded-md overflow-hidden bg-gray-100 mr-3'>
                    <img
                      src={restaurant.logo}
                      alt={restaurant.name}
                      className='w-full h-full object-cover'
                    />
                  </div>
                )}
                <div>
                  <h3 className='font-medium'>{cart.restaurantName}</h3>
                  <p className='text-sm text-text-secondary'>
                    {cart.items.length} item{cart.items.length !== 1 ? "s" : ""}
                  </p>
                </div>
              </div>
              <button
                className='text-accent-red hover:text-accent-red-dark text-sm flex items-center'
                onClick={clearCart}
              >
                <Trash2 size={16} className='mr-1' />
                Clear Cart
              </button>
            </div>

            <div className='divide-y divide-gray-100'>
              {cart.items.map((item) => (
                <div key={item.id} className='py-4 first:pt-0 last:pb-0'>
                  <div className='flex items-center'>
                    <div className='w-16 h-16 rounded-md overflow-hidden bg-gray-100 mr-4 flex-shrink-0'>
                      <img
                        src={item.image}
                        alt={item.name}
                        className='w-full h-full object-cover'
                      />
                    </div>

                    <div className='flex-grow'>
                      <h4 className='font-medium'>{item.name}</h4>
                      <div className='flex justify-between items-center mt-2'>
                        <div className='flex items-center border border-gray-200 rounded-md'>
                          <button
                            className='px-2 py-1 text-text-primary disabled:text-gray-300'
                            onClick={() =>
                              updateItemQuantity(item.id, item.quantity - 1)
                            }
                            disabled={item.quantity <= 1}
                          >
                            <Minus size={16} />
                          </button>
                          <span className='px-3 py-1 font-medium'>
                            {item.quantity}
                          </span>
                          <button
                            className='px-2 py-1 text-text-primary'
                            onClick={() =>
                              updateItemQuantity(item.id, item.quantity + 1)
                            }
                          >
                            <Plus size={16} />
                          </button>
                        </div>

                        <div className='text-right'>
                          <p className='font-semibold'>
                            ${(item.price * item.quantity).toFixed(2)}
                          </p>
                          <button
                            className='text-text-secondary hover:text-accent-red text-sm'
                            onClick={() => removeFromCart(item.id)}
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <div className='flex justify-between items-center mb-8 lg:hidden'>
            <Link
              to={`/restaurants/${cart.restaurantId}`}
              className='text-primary-500 hover:text-primary-600'
            >
              Add More Items
            </Link>
          </div>
        </div>

        {/* Order Summary */}
        <div className='lg:w-1/3'>
          <Card>
            <h3 className='font-poppins font-semibold text-lg mb-4'>
              Order Summary
            </h3>

            <div className='space-y-3 text-sm mb-6'>
              <div className='flex justify-between'>
                <span className='text-text-secondary'>Subtotal</span>
                <span>${cart.subtotal.toFixed(2)}</span>
              </div>
              <div className='flex justify-between'>
                <span className='text-text-secondary'>Delivery Fee</span>
                <span>${cart.deliveryFee.toFixed(2)}</span>
              </div>
              <div className='pt-3 border-t border-gray-100 flex justify-between font-semibold'>
                <span>Total</span>
                <span>${cart.total.toFixed(2)}</span>
              </div>
            </div>

            <Button
              variant='primary'
              fullWidth
              size='large'
              onClick={handleCheckout}
            >
              Proceed to Checkout
            </Button>

            <div className='mt-6 text-center'>
              <Link
                to={`/restaurants/${cart.restaurantId}`}
                className='text-primary-500 hover:text-primary-600 text-sm'
              >
                Add More Items
              </Link>
            </div>
          </Card>

          <div className='mt-6 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md'>
            <div className='flex'>
              <AlertCircle
                size={20}
                className='text-yellow-600 mr-2 flex-shrink-0'
              />
              <div className='text-sm text-yellow-800'>
                <p className='font-medium'>Cash on Delivery</p>
                <p className='mt-1'>
                  Please have the exact amount ready when your order arrives.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Cart;
