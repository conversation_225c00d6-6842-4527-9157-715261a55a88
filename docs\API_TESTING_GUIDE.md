# 🧪 Restaurant API Testing Guide

This guide will help you verify that your restaurant API integration is working correctly.

## 🚀 Quick Start Testing

### Method 1: In-App API Test Component

1. **Start your development server**:
   ```bash
   npm run dev
   ```

2. **Login as an admin user** and navigate to:
   ```
   http://localhost:5173/admin/api-test
   ```

3. **Run the test suite** by clicking "Run Full Test Suite"

4. **Check the results** - you should see green checkmarks for successful operations

### Method 2: Browser Console Testing

1. **Open your app** and login
2. **Open browser console** (F12)
3. **Run this test**:
   ```javascript
   // Test API connectivity
   fetch('https://afghansufra.luilala.com/api/restaurant/restaurants/', {
     headers: {
       'Authorization': `Bearer ${JSON.parse(localStorage.getItem('afghanSofraUser')).access_token}`
     }
   })
   .then(response => response.json())
   .then(data => console.log('✅ API Response:', data))
   .catch(error => console.error('❌ API Error:', error));
   ```

### Method 3: Restaurant Registration Test

1. **Navigate to restaurant registration**:
   ```
   http://localhost:5173/register-restaurant
   ```

2. **Fill out the form** with test data:
   - Name: "Test Restaurant"
   - Description: "Testing API integration"
   - Phone: "+1234567890"
   - Address: "123 Test Street"
   - City: "Test City"

3. **Upload test images** for logo and banner

4. **Submit the form** and check for success/error messages

## 🔍 What to Look For

### ✅ Success Indicators

1. **API Test Component**:
   - Green checkmarks for all tests
   - Restaurant creation returns an ID
   - GET requests return data
   - UPDATE/DELETE operations succeed

2. **Restaurant Registration**:
   - Form submits without errors
   - Success message appears
   - Redirect to success page

3. **Browser Network Tab**:
   - API requests show 200/201 status codes
   - Proper Authorization headers
   - Correct Content-Type for file uploads

### ❌ Common Issues & Solutions

#### 1. **401 Unauthorized Error**
```
Error: Request failed with status code 401
```
**Solution**: 
- Check if you're logged in
- Verify token in localStorage: `localStorage.getItem('afghanSofraUser')`
- Token might be expired - try logging in again

#### 2. **CORS Error**
```
Access to fetch blocked by CORS policy
```
**Solution**: 
- API server needs to allow your domain
- Contact backend team to add CORS headers

#### 3. **Network Error**
```
Network Error / Failed to fetch
```
**Solution**: 
- Check if API server is running
- Verify API URL: `https://afghansufra.luilala.com/api`
- Check internet connection

#### 4. **File Upload Error**
```
Error: multipart/form-data error
```
**Solution**: 
- Ensure files are selected
- Check file size limits
- Verify file format (PNG, JPG, GIF)

## 🛠️ Debugging Steps

### 1. Check API Configuration
```javascript
// In browser console
console.log('API Base URL:', process.env.REACT_APP_API_BASE_URL);
console.log('User Token:', JSON.parse(localStorage.getItem('afghanSofraUser') || '{}'));
```

### 2. Test Individual Endpoints

#### Get All Restaurants
```javascript
import { restaurantApi } from './src/utils/restaurantApi';

restaurantApi.getRestaurants()
  .then(result => console.log('Get All:', result))
  .catch(error => console.error('Error:', error));
```

#### Create Restaurant
```javascript
const testData = {
  name: 'Test Restaurant',
  description: 'Testing',
  contact_number: '+1234567890',
  opening_time: '09:00:00',
  closing_time: '21:00:00',
  address: {
    street: '123 Test St',
    city: 'Test City',
    state: 'TS',
    postal_code: '12345',
    country: 'USA',
    latitude: 40.7128,
    longitude: -74.0060
  }
};

restaurantApi.createRestaurant(testData)
  .then(result => console.log('Create:', result))
  .catch(error => console.error('Error:', error));
```

### 3. Check Network Requests

1. **Open Browser DevTools** (F12)
2. **Go to Network tab**
3. **Perform API operation**
4. **Check the request details**:
   - URL should be: `https://afghansufra.luilala.com/api/restaurant/restaurants/`
   - Headers should include: `Authorization: Bearer <token>`
   - For POST: Content-Type should be `multipart/form-data`

## 📊 Expected API Responses

### Successful Restaurant Creation
```json
{
  "success": true,
  "data": {
    "id": 123,
    "name": "Test Restaurant",
    "description": "Testing",
    "contact_number": "+1234567890",
    "opening_time": "09:00:00",
    "closing_time": "21:00:00",
    "address": {
      "street": "123 Test St",
      "city": "Test City",
      "state": "TS",
      "postal_code": "12345",
      "country": "USA",
      "latitude": 40.7128,
      "longitude": -74.0060
    },
    "logo": "https://...",
    "banner": "https://...",
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "Restaurant created successfully"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Validation error",
  "details": {
    "name": ["This field is required"],
    "contact_number": ["Invalid phone number format"]
  }
}
```

## 🔧 Manual Testing Checklist

- [ ] API test component loads without errors
- [ ] Can run full test suite successfully
- [ ] Restaurant registration form works
- [ ] File uploads work (logo/banner)
- [ ] Can view restaurant details
- [ ] Can update restaurant information
- [ ] Can delete restaurants (if authorized)
- [ ] Error messages display properly
- [ ] Loading states work correctly
- [ ] Authentication redirects work

## 📞 Getting Help

If tests are failing:

1. **Check this guide** for common solutions
2. **Look at browser console** for error messages
3. **Check network tab** for failed requests
4. **Verify API server status** at `https://afghansufra.luilala.com/api`
5. **Contact backend team** if server issues persist

## 🎯 Production Testing

Before deploying:

1. Test with real user accounts
2. Test file uploads with various formats/sizes
3. Test error handling scenarios
4. Verify all CRUD operations work
5. Check performance with multiple requests
6. Test authentication token refresh

Your API integration is working correctly if all tests pass and you can successfully create, read, update, and delete restaurants through the interface!
