/**
 * Utility functions for handling category data
 */

/**
 * Safely extracts category name from various category data formats
 * @param {string|object} category - Category data (can be string name or object)
 * @param {string} fallback - Fallback value if category name cannot be extracted
 * @returns {string} Category name
 */
export const getCategoryName = (category, fallback = "Unknown") => {
  if (!category) return fallback;
  
  // If it's already a string, return it
  if (typeof category === 'string') {
    return category;
  }
  
  // If it's an object, try to extract the name
  if (typeof category === 'object') {
    return category.name || category.category_name || fallback;
  }
  
  return fallback;
};

/**
 * Safely extracts category name from menu item data
 * @param {object} item - Menu item object
 * @param {string} fallback - Fallback value if category name cannot be extracted
 * @returns {string} Category name
 */
export const getMenuItemCategoryName = (item, fallback = "Unknown") => {
  if (!item) return fallback;
  
  // Try different possible category field names
  if (item.category_name) {
    return item.category_name;
  }
  
  if (item.category) {
    return getCategoryName(item.category, fallback);
  }
  
  return fallback;
};

/**
 * Validates that a value is safe to render in React components
 * @param {any} value - Value to validate
 * @returns {boolean} True if safe to render
 */
export const isSafeToRender = (value) => {
  return (
    value === null ||
    value === undefined ||
    typeof value === 'string' ||
    typeof value === 'number' ||
    typeof value === 'boolean'
  );
};

/**
 * Safely converts any value to a renderable string
 * @param {any} value - Value to convert
 * @param {string} fallback - Fallback value for non-renderable values
 * @returns {string} Safe string value
 */
export const toSafeString = (value, fallback = "") => {
  if (isSafeToRender(value)) {
    return String(value);
  }
  
  // If it's an object, try to extract meaningful information
  if (typeof value === 'object' && value !== null) {
    if (value.name) return String(value.name);
    if (value.title) return String(value.title);
    if (value.label) return String(value.label);
  }
  
  return fallback;
};
