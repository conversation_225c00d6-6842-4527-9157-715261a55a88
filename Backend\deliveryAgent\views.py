from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from django.contrib.auth.decorators import login_required
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model
from django.db.models import Q, Avg, Count, Sum
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal
from decimal import Decimal
import json
from .models import DeliveryAgentProfile

User = get_user_model()

class DeliveryAgentDashboardView(APIView):
    """
    Clean delivery agent dashboard for hybrid registration system
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get agent dashboard data"""
        try:
            # Get or create agent profile for delivery agents
            try:
                agent = DeliveryAgentProfile.objects.get(user=request.user)
            except DeliveryAgentProfile.DoesNotExist:
                # Auto-create profile for delivery agent users
                if request.user.role == 'delivery_agent':
                    agent = self.create_default_profile(request.user)
                else:
                    return Response({
                        'status': 'error',
                        'message': 'Only delivery agents can access this dashboard'
                    }, status=status.HTTP_403_FORBIDDEN)
            
            # Calculate today's stats
            today = timezone.now().date()
            
            # Basic dashboard data
            dashboard_data = {
                'agent_info': {
                    'agent_id': agent.agent_id,
                    'full_name': agent.full_name,
                    'employment_status': agent.employment_status,
                    'application_status': agent.status,
                    'availability': agent.availability,
                    'is_online': agent.is_online,
                    'is_verified': agent.is_verified,
                    'is_clocked_in': agent.is_clocked_in,
                    'is_on_duty': agent.is_on_duty,
                    'rating': float(agent.rating),
                    'profile_photo': agent.profile_photo.url if agent.profile_photo else None,
                    'employee_number': agent.employee_number,
                    'hire_date': agent.hire_date.isoformat() if agent.hire_date else None
                },
                'today_stats': {
                    'deliveries_completed': 0,  # Will be calculated from orders
                    'earnings': float(Decimal('0.00')),
                    'hours_worked': 0,
                    'completion_rate': agent.completion_rate
                },
                'overall_stats': {
                    'total_deliveries': agent.total_deliveries,
                    'successful_deliveries': agent.successful_deliveries,
                    'total_earnings': float(agent.total_earnings),
                    'average_rating': float(agent.rating)
                },
                'status_info': {
                    'can_accept_orders': (agent.employment_status == 'active' and
                                        agent.status == 'approved' and
                                        agent.availability == 'available'),
                    'next_action': self.get_next_action(agent)
                }
            }
            
            return Response({
                'status': 'success',
                'data': dashboard_data
            })
            
        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def create_default_profile(self, user):
        """Create a default delivery agent profile for a user"""
        # Generate agent ID
        last_agent = DeliveryAgentProfile.objects.filter(
            agent_id__startswith='DA'
        ).order_by('-agent_id').first()

        if last_agent:
            # Extract number from last agent ID (e.g., DA003 -> 3)
            try:
                last_number = int(last_agent.agent_id[2:])
                new_number = last_number + 1
            except (ValueError, IndexError):
                new_number = 1
        else:
            new_number = 1

        agent_id = f"DA{new_number:03d}"  # Format as DA001, DA002, etc.

        # Create profile with default values
        agent = DeliveryAgentProfile.objects.create(
            user=user,
            agent_id=agent_id,
            employee_number=str(new_number),
            full_name=user.name or user.user_name,
            phone_number=user.phone or '',
            email=user.email or '',
            employment_status='active',
            status='approved',  # Auto-approve for now
            availability='offline',
            is_verified=True,  # Auto-verify for admin-created users
            hire_date=timezone.now().date(),
            vehicle_type='motorcycle',
            work_schedule='full_time',
            salary_type='fixed',
            base_salary=Decimal('20000.00'),
            commission_per_delivery=Decimal('150.00')
        )

        return agent

    def get_next_action(self, agent):
        """Get next action for agent based on status"""
        # Check employment status first
        if agent.employment_status != 'active':
            return f'Employment status: {agent.employment_status}. Contact HR.'

        # Check application status
        if agent.status == 'rejected':
            return 'Contact support for more information'
        elif agent.status == 'approved':
            # Check if clocked in
            if not agent.is_clocked_in:
                return 'Clock in to start your shift'
            elif agent.availability == 'offline':
                return 'Go online to start receiving orders'
            elif agent.availability == 'available':
                return 'Ready to accept orders'
            elif agent.availability == 'busy':
                return 'Currently on delivery'
            elif agent.availability == 'break':
                return 'On break - return when ready'

        return 'Update your status'

class AvailableOrdersView(APIView):
    """
    Get available orders for delivery agent
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get available orders"""
        try:
            from orders.models import Order

            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            # Check if agent can accept orders
            if agent.status != 'approved' or agent.availability != 'available':
                return Response({
                    'status': 'error',
                    'message': 'Agent not available for orders',
                    'agent_status': agent.status,
                    'availability': agent.availability
                })

            # Get orders that are ready for pickup and not yet assigned to any delivery agent
            ready_orders = Order.objects.filter(
                status='ready',
                delivery_agent__isnull=True
            ).select_related('restaurant', 'customer', 'delivery_address').order_by('-created_at')

            # Format orders for delivery agent
            available_orders = []
            for order in ready_orders:
                order_data = {
                    'id': order.id,
                    'order_number': f"ORD{order.id:06d}",
                    'restaurant': {
                        'name': order.restaurant.name,
                        'address': str(order.restaurant.address),
                        'phone': order.restaurant.contact_number,
                        'latitude': float(order.restaurant.address.latitude) if order.restaurant.address.latitude else None,
                        'longitude': float(order.restaurant.address.longitude) if order.restaurant.address.longitude else None,
                    },
                    'customer': {
                        'name': order.customer.name,
                        'phone': order.customer.phone,
                        'address': str(order.delivery_address),
                        'latitude': float(order.delivery_address.latitude) if order.delivery_address.latitude else None,
                        'longitude': float(order.delivery_address.longitude) if order.delivery_address.longitude else None,
                    },
                    'order_details': {
                        'total_amount': float(order.total_amount),
                        'delivery_fee': float(order.delivery_fee) if order.delivery_fee else 0.0,
                        'payment_method': order.payment_method,
                        'items_count': order.items.count(),
                        'special_instructions': order.special_instructions or '',
                        'created_at': order.created_at.isoformat(),
                        'ready_at': order.updated_at.isoformat(),
                    },
                    'distance': self._calculate_distance(agent, order),
                    'estimated_time': self._estimate_delivery_time(agent, order),
                    'estimated_earnings': self._calculate_estimated_earnings(order)
                }
                available_orders.append(order_data)

            return Response({
                'status': 'success',
                'data': {
                    'orders': available_orders,
                    'total_count': len(available_orders),
                    'agent_location': {
                        'latitude': float(agent.current_latitude) if agent.current_latitude else None,
                        'longitude': float(agent.current_longitude) if agent.current_longitude else None,
                        'address': agent.current_address
                    }
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _calculate_distance(self, agent, order):
        """Calculate approximate distance between agent and restaurant"""
        try:
            if (agent.current_latitude and agent.current_longitude and
                order.restaurant.address.latitude and order.restaurant.address.longitude):

                from math import radians, cos, sin, asin, sqrt

                # Haversine formula for distance calculation
                lat1, lon1 = radians(float(agent.current_latitude)), radians(float(agent.current_longitude))
                lat2, lon2 = radians(float(order.restaurant.address.latitude)), radians(float(order.restaurant.address.longitude))

                dlat = lat2 - lat1
                dlon = lon2 - lon1
                a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
                c = 2 * asin(sqrt(a))
                r = 6371  # Radius of earth in kilometers
                distance = c * r

                return f"{distance:.1f} km"
            else:
                return "Distance unknown"
        except:
            return "Distance unknown"

    def _estimate_delivery_time(self, agent, order):
        """Estimate delivery time based on distance"""
        try:
            distance_str = self._calculate_distance(agent, order)
            if "km" in distance_str:
                distance = float(distance_str.replace(" km", ""))
                # Assume average speed of 20 km/h in city traffic
                time_hours = distance / 20
                time_minutes = int(time_hours * 60)
                return f"{max(10, time_minutes)} min"  # Minimum 10 minutes
            else:
                return "15-30 min"
        except:
            return "15-30 min"

    def _calculate_estimated_earnings(self, order):
        """Calculate estimated earnings for the delivery"""
        try:
            # Base delivery fee + percentage of order value
            base_fee = float(order.delivery_fee) if order.delivery_fee else 25.0
            order_percentage = float(order.total_amount) * 0.05  # 5% of order value
            estimated_earnings = base_fee + order_percentage
            return round(estimated_earnings, 2)
        except:
            return 50.0  # Default earnings

class AcceptOrderView(APIView):
    """
    Accept an order assignment
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """Accept order"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)
            order_id = request.data.get('order_id')
            
            if not order_id:
                return Response({
                    'status': 'error',
                    'message': 'Order ID required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get the order and validate it's available for acceptance
            from orders.models import Order

            try:
                # Look for orders that are ready and not yet assigned
                order = Order.objects.get(id=order_id, status='ready', delivery_agent__isnull=True)
            except Order.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': 'Order not found or not available for acceptance'
                }, status=status.HTTP_404_NOT_FOUND)

            # Check if agent can accept orders
            if agent.availability != 'available':
                return Response({
                    'status': 'error',
                    'message': f'Agent not available to accept orders. Current status: {agent.availability}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Assign and accept the order
            order.assign_agent(agent.user, request)
            order.accept_delivery(agent.user, request)

            # Update agent availability
            agent.availability = 'busy'
            agent.save()

            return Response({
                'status': 'success',
                'message': 'Order accepted successfully',
                'order_id': order_id,
                'new_status': order.status,
                'next_steps': [
                    'Navigate to restaurant',
                    'Update status when you arrive',
                    'Pick up the order',
                    'Navigate to customer',
                    'Deliver and collect payment'
                ],
                'restaurant': {
                    'name': order.restaurant.name,
                    'address': order.restaurant.address,
                    'phone': order.restaurant.contact_number
                },
                'customer': {
                    'name': order.customer.name,
                    'phone': order.customer.phone,
                    'address': str(order.delivery_address)
                }
            })
            
        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UpdateOrderStatusView(APIView):
    """
    Update order status during delivery
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Update order status"""
        try:
            from orders.models import Order

            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)
            order_id = request.data.get('order_id')
            new_status = request.data.get('status')
            location = request.data.get('location', {})
            notes = request.data.get('notes', '')

            if not order_id or not new_status:
                return Response({
                    'status': 'error',
                    'message': 'Order ID and status required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the order and validate assignment
            try:
                order = Order.objects.get(id=order_id, delivery_agent=request.user)
            except Order.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': 'Order not found or not assigned to you'
                }, status=status.HTTP_404_NOT_FOUND)

            # Update agent location if provided
            if location.get('latitude') and location.get('longitude'):
                agent.update_location(
                    location['latitude'],
                    location['longitude'],
                    location.get('address', '')
                )

            # Validate status transitions - comprehensive workflow
            valid_transitions = {
                'assigned': ['accepted', 'rejected', 'cancelled'],
                'accepted': ['en_route_to_restaurant', 'cancelled'],
                'en_route_to_restaurant': ['arrived_at_restaurant', 'cancelled'],
                'arrived_at_restaurant': ['picked_up', 'cancelled'],
                'picked_up': ['en_route_to_customer', 'out_for_delivery', 'cancelled'],  # Support both workflows
                'en_route_to_customer': ['arrived_at_customer', 'delivered', 'cancelled'],  # Allow direct to delivered
                'out_for_delivery': ['delivered', 'cancelled'],  # Legacy workflow support
                'arrived_at_customer': ['delivered', 'cancelled'],
                'delivered': ['cash_collected', 'completed'],
                'cash_collected': ['completed']
            }

            # Check if transition is valid
            if order.status not in valid_transitions:
                return Response({
                    'status': 'error',
                    'message': f'Invalid current status: {order.status}'
                }, status=status.HTTP_400_BAD_REQUEST)

            if new_status not in valid_transitions[order.status]:
                return Response({
                    'status': 'error',
                    'message': f'Invalid status transition from {order.status} to {new_status}. Valid options: {", ".join(valid_transitions[order.status])}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Update order status
            old_status = order.status
            order.status = new_status
            order._changed_by = request.user
            order._status_change_notes = f"Status updated by agent: {notes}" if notes else f"Status updated by agent from {old_status} to {new_status}"

            # Update timestamp fields based on status
            if new_status == 'accepted':
                order.accepted_at = timezone.now()
            elif new_status == 'picked_up':
                order.picked_up_at = timezone.now()
            elif new_status == 'delivered':
                order.delivered_at = timezone.now()
            elif new_status == 'cash_collected':
                order.cash_collected_at = timezone.now()

            # Add agent notes if provided
            if notes:
                order.agent_notes = notes

            order.save()

            # Handle specific status updates for agent
            if new_status == 'delivered':
                # Update performance metrics
                agent.total_deliveries += 1
                agent.successful_deliveries += 1
                agent.save()
            elif new_status == 'completed':
                # Mark agent as available for new orders
                agent.availability = 'available'
                agent.save()

            # Prepare response with next steps
            next_steps = self.get_next_steps(new_status, order)

            return Response({
                'status': 'success',
                'message': f'Order status updated to {new_status}',
                'order_id': order_id,
                'old_status': old_status,
                'new_status': order.status,
                'agent_status': agent.availability,
                'next_steps': next_steps,
                'order_details': {
                    'customer_name': order.customer.name,
                    'customer_phone': order.customer.phone,
                    'delivery_address': str(order.delivery_address),
                    'total_amount': float(order.total_amount),
                    'payment_method': order.payment_method
                }
            })
            
        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get_next_steps(self, current_status, order):
        """Get next steps based on current status"""
        steps_map = {
            'accepted': ['Navigate to restaurant', 'Update status to "en_route_to_restaurant"'],
            'en_route_to_restaurant': ['Arrive at restaurant', 'Update status to "arrived_at_restaurant"'],
            'arrived_at_restaurant': ['Pick up the order', 'Update status to "picked_up"'],
            'picked_up': ['Navigate to customer', 'Update status to "en_route_to_customer" or "out_for_delivery"'],
            'en_route_to_customer': ['Arrive at customer location', 'Update status to "arrived_at_customer"'],
            'out_for_delivery': ['Navigate to customer', 'Update status to "delivered"'],
            'arrived_at_customer': ['Deliver the order', 'Update status to "delivered"'],
            'delivered': ['Collect payment if cash on delivery', 'Update status to "cash_collected" or "completed"'],
            'cash_collected': ['Complete the delivery', 'Update status to "completed"'],
            'completed': ['Order completed successfully', 'Ready for next order']
        }

        steps = steps_map.get(current_status, ['Contact support for guidance'])

        # Add payment-specific steps
        if current_status == 'delivered':
            if order.payment_method == 'cash_on_delivery':
                steps = [f'Collect ${order.total_amount} cash from customer', 'Update status to "cash_collected"']
            else:
                steps = ['Order delivered successfully', 'Update status to "completed"']

        return steps


class ClockInView(APIView):
    """Employee clock in for work shift"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Clock in employee"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            # Check if employee is active
            if agent.employment_status != 'active':
                return Response({
                    'status': 'error',
                    'message': f'Employee status is {agent.employment_status}. Cannot clock in.'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Attempt to clock in
            if agent.clock_in():
                return Response({
                    'status': 'success',
                    'message': 'Clocked in successfully',
                    'data': {
                        'clock_in_time': agent.last_clock_in,
                        'availability': agent.availability,
                        'shift_start': agent.shift_start_time,
                        'shift_end': agent.shift_end_time
                    }
                })
            else:
                return Response({
                    'status': 'error',
                    'message': 'Already clocked in'
                }, status=status.HTTP_400_BAD_REQUEST)

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Employee profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ClockOutView(APIView):
    """Employee clock out from work shift"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Clock out employee"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            # Check if agent is busy with delivery
            if agent.availability == 'busy':
                return Response({
                    'status': 'error',
                    'message': 'Cannot clock out while on delivery. Complete current delivery first.'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Attempt to clock out
            if agent.clock_out():
                return Response({
                    'status': 'success',
                    'message': 'Clocked out successfully',
                    'data': {
                        'clock_out_time': agent.last_clock_out,
                        'shift_hours': str(agent.current_shift_hours),
                        'total_deliveries_today': agent.total_deliveries,  # This should be filtered by today
                        'earnings_today': str(agent.total_earnings)  # This should be filtered by today
                    }
                })
            else:
                return Response({
                    'status': 'error',
                    'message': 'Not clocked in'
                }, status=status.HTTP_400_BAD_REQUEST)

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Employee profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SetBreakView(APIView):
    """Set employee on break"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Set agent on break"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            if agent.set_on_break():
                return Response({
                    'status': 'success',
                    'message': 'Break started',
                    'data': {
                        'availability': agent.availability,
                        'break_start_time': timezone.now()
                    }
                })
            else:
                return Response({
                    'status': 'error',
                    'message': 'Cannot start break. Not clocked in or not available.'
                }, status=status.HTTP_400_BAD_REQUEST)

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Employee profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ReturnFromBreakView(APIView):
    """Return employee from break"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Return agent from break"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            if agent.return_from_break():
                return Response({
                    'status': 'success',
                    'message': 'Returned from break',
                    'data': {
                        'availability': agent.availability,
                        'break_end_time': timezone.now()
                    }
                })
            else:
                return Response({
                    'status': 'error',
                    'message': 'Not on break or not clocked in.'
                }, status=status.HTTP_400_BAD_REQUEST)

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Employee profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class EarningsSummaryView(APIView):
    """
    Get agent earnings summary
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """Get earnings summary"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)
            
            # Calculate earnings (simplified)
            today = timezone.now().date()
            week_start = today - timedelta(days=today.weekday())
            
            earnings_summary = {
                'today': {
                    'deliveries': 0,  # Would be calculated from actual orders
                    'earnings': 0.00,
                    'hours_worked': 0
                },
                'this_week': {
                    'deliveries': 0,
                    'earnings': 0.00,
                    'average_per_delivery': 0.00
                },
                'total': {
                    'deliveries': agent.total_deliveries,
                    'earnings': float(agent.total_earnings),
                    'rating': float(agent.rating)
                },
                'payment_info': {
                    'bank_name': agent.bank_name,
                    'account_number': agent.account_number[-4:] if agent.account_number else '',
                    'mobile_wallet': agent.mobile_wallet
                }
            }
            
            return Response({
                'status': 'success',
                'earnings': earnings_summary
            })
            
        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class PerformanceMetricsView(APIView):
    """
    Get agent performance metrics
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get performance metrics"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            performance_metrics = {
                'overall_rating': float(agent.rating),
                'completion_rate': agent.completion_rate,
                'total_deliveries': agent.total_deliveries,
                'successful_deliveries': agent.successful_deliveries,
                'total_earnings': float(agent.total_earnings),
                'status': agent.status,
                'verification_status': {
                    'is_verified': agent.is_verified,
                    'document_verification': agent.document_verification_status,
                    'background_check': agent.background_check_status,
                    'training_status': agent.training_status
                },
                'achievements': self.get_achievements(agent),
                'next_goals': self.get_next_goals(agent)
            }

            return Response({
                'status': 'success',
                'metrics': performance_metrics
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ToggleOnlineStatusView(APIView):
    """Toggle agent online/offline status"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Toggle online status"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            # Check if agent is approved and active
            if agent.employment_status != 'active' or agent.status != 'approved':
                return Response({
                    'status': 'error',
                    'message': 'Agent must be approved and active to go online'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Toggle online status
            new_online_status = not agent.is_online

            if new_online_status:
                # Going online
                agent.is_online = True
                agent.availability = 'available'
                agent.last_active = timezone.now()
            else:
                # Going offline
                agent.is_online = False
                agent.availability = 'offline'

            agent.save(update_fields=['is_online', 'availability', 'last_active', 'last_seen'])

            return Response({
                'status': 'success',
                'message': f'Agent is now {"online" if new_online_status else "offline"}',
                'data': {
                    'is_online': agent.is_online,
                    'availability': agent.availability,
                    'last_active': agent.last_active
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UpdateAvailabilityView(APIView):
    """Update agent availability status"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Update availability"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            availability = request.data.get('availability')
            is_online = request.data.get('is_online')

            if availability:
                # Validate availability status
                valid_statuses = ['available', 'busy', 'break', 'offline']
                if availability not in valid_statuses:
                    return Response({
                        'status': 'error',
                        'message': f'Invalid availability status. Must be one of: {valid_statuses}'
                    }, status=status.HTTP_400_BAD_REQUEST)

                agent.availability = availability

                # If going offline, set is_online to False
                if availability == 'offline':
                    agent.is_online = False
                elif availability in ['available', 'busy', 'break']:
                    agent.is_online = True

            if is_online is not None:
                agent.is_online = bool(is_online)
                if not agent.is_online:
                    agent.availability = 'offline'

            agent.last_active = timezone.now()
            agent.save(update_fields=['availability', 'is_online', 'last_active', 'last_seen'])

            return Response({
                'status': 'success',
                'message': 'Availability updated successfully',
                'data': {
                    'availability': agent.availability,
                    'is_online': agent.is_online,
                    'last_active': agent.last_active
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class StartShiftView(APIView):
    """Start work shift"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Start shift"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            # Check if agent is approved and active
            if agent.employment_status != 'active' or agent.status != 'approved':
                return Response({
                    'status': 'error',
                    'message': 'Agent must be approved and active to start shift'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Check if already on shift
            if agent.is_on_duty:
                return Response({
                    'status': 'error',
                    'message': 'Agent is already on duty'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get location data
            latitude = request.data.get('latitude')
            longitude = request.data.get('longitude')
            planned_duration_hours = request.data.get('planned_duration_hours', 8.0)

            # Start shift
            agent.is_on_duty = True
            agent.is_online = True
            agent.availability = 'available'
            agent.shift_start_time = timezone.now()
            agent.current_latitude = latitude
            agent.current_longitude = longitude
            agent.last_location_update = timezone.now()
            agent.last_active = timezone.now()

            agent.save(update_fields=[
                'is_on_duty', 'is_online', 'availability', 'shift_start_time',
                'current_latitude', 'current_longitude', 'last_location_update', 'last_active'
            ])

            return Response({
                'status': 'success',
                'message': 'Shift started successfully',
                'data': {
                    'shift_start_time': agent.shift_start_time,
                    'is_on_duty': agent.is_on_duty,
                    'is_online': agent.is_online,
                    'availability': agent.availability,
                    'planned_duration_hours': planned_duration_hours
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class EndShiftView(APIView):
    """End work shift"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """End shift"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            # Check if on shift
            if not agent.is_on_duty:
                return Response({
                    'status': 'error',
                    'message': 'Agent is not currently on duty'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get location data
            latitude = request.data.get('latitude')
            longitude = request.data.get('longitude')
            notes = request.data.get('notes', '')

            # Calculate shift duration
            shift_duration = timezone.now() - agent.shift_start_time if agent.shift_start_time else timedelta(0)
            shift_hours = shift_duration.total_seconds() / 3600

            # End shift
            agent.is_on_duty = False
            agent.is_online = False
            agent.availability = 'offline'
            agent.shift_end_time = timezone.now()
            agent.current_shift_hours = Decimal(str(round(shift_hours, 2)))
            agent.current_latitude = latitude
            agent.current_longitude = longitude
            agent.last_location_update = timezone.now()

            agent.save(update_fields=[
                'is_on_duty', 'is_online', 'availability', 'shift_end_time',
                'current_shift_hours', 'current_latitude', 'current_longitude', 'last_location_update'
            ])

            return Response({
                'status': 'success',
                'message': 'Shift ended successfully',
                'data': {
                    'shift_end_time': agent.shift_end_time,
                    'shift_duration_hours': float(agent.current_shift_hours),
                    'is_on_duty': agent.is_on_duty,
                    'is_online': agent.is_online,
                    'availability': agent.availability,
                    'notes': notes
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MyOrdersView(APIView):
    """Get agent's assigned orders"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get my orders"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            # Get orders assigned to this agent
            from orders.models import Order
            orders = Order.objects.filter(
                delivery_agent=request.user
            ).select_related(
                'restaurant', 'customer', 'delivery_address'
            ).order_by('-created_at')

            # Filter by status if provided
            status_filter = request.GET.get('status')
            if status_filter:
                orders = orders.filter(status=status_filter)

            # Serialize orders
            orders_data = []
            for order in orders:
                order_data = {
                    'id': order.id,
                    'status': order.status,
                    'total_amount': float(order.total_amount),
                    'delivery_fee': float(order.delivery_fee),
                    'payment_method': order.payment_method,
                    'created_at': order.created_at,
                    'estimated_delivery_time': order.estimated_delivery_time,
                    'restaurant': {
                        'id': order.restaurant.id,
                        'name': order.restaurant.name,
                        'phone': order.restaurant.phone,
                        'address': str(order.restaurant.address) if order.restaurant.address else ''
                    },
                    'customer': {
                        'name': order.customer.name,
                        'phone': order.customer.phone
                    },
                    'delivery_address': {
                        'street': order.delivery_address.street,
                        'city': order.delivery_address.city,
                        'district': order.delivery_address.district,
                        'area': order.delivery_address.area
                    },
                    'items': [
                        {
                            'name': item.menu_item.name,
                            'quantity': item.quantity,
                            'price': float(item.price)
                        } for item in order.items.all()
                    ]
                }
                orders_data.append(order_data)

            return Response({
                'status': 'success',
                'data': {
                    'orders': orders_data,
                    'total_count': orders.count()
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class AvailableOrdersView(APIView):
    """Get available orders for assignment"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get available orders"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            # Check if agent is available
            if agent.availability != 'available' or not agent.is_clocked_in:
                return Response({
                    'status': 'success',
                    'data': {
                        'orders': [],
                        'message': 'You must be clocked in and available to see orders'
                    }
                })

            # Get available orders
            from orders.models import Order
            available_orders = Order.objects.filter(
                status='ready',
                delivery_agent__isnull=True
            ).select_related(
                'restaurant', 'customer', 'delivery_address'
            ).order_by('-created_at')[:10]  # Limit to 10 most recent

            # Serialize orders
            orders_data = []
            for order in available_orders:
                order_data = {
                    'id': order.id,
                    'total_amount': float(order.total_amount),
                    'delivery_fee': float(order.delivery_fee),
                    'payment_method': order.payment_method,
                    'created_at': order.created_at,
                    'estimated_delivery_time': order.estimated_delivery_time,
                    'restaurant': {
                        'id': order.restaurant.id,
                        'name': order.restaurant.name,
                        'phone': order.restaurant.phone,
                        'address': order.restaurant.address
                    },
                    'delivery_address': {
                        'street': order.delivery_address.street,
                        'city': order.delivery_address.city,
                        'district': order.delivery_address.district,
                        'area': order.delivery_address.area
                    },
                    'distance': '2.5 km',  # TODO: Calculate actual distance
                    'estimated_time': '15 mins'  # TODO: Calculate actual time
                }
                orders_data.append(order_data)

            return Response({
                'status': 'success',
                'data': {
                    'orders': orders_data,
                    'total_count': available_orders.count()
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



    
    def get_achievements(self, agent):
        """Get agent achievements"""
        achievements = []
        
        if agent.total_deliveries >= 10:
            achievements.append('First 10 deliveries completed')
        if agent.total_deliveries >= 50:
            achievements.append('50 deliveries milestone')
        if agent.rating >= 4.5:
            achievements.append('High rating achievement')
        if agent.is_verified:
            achievements.append('Verified agent')
            
        return achievements
    
    def get_next_goals(self, agent):
        """Get next goals for agent"""
        goals = []
        
        if agent.total_deliveries < 10:
            goals.append(f'Complete {10 - agent.total_deliveries} more deliveries to reach first milestone')
        elif agent.total_deliveries < 50:
            goals.append(f'Complete {50 - agent.total_deliveries} more deliveries to reach 50 delivery milestone')
        
        if agent.rating < 4.5:
            goals.append('Maintain high customer satisfaction to improve rating')
        
        if not agent.is_verified:
            goals.append('Complete verification process')
            
        return goals


class RejectOrderView(APIView):
    """
    Reject an assigned order
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Reject order"""
        try:
            from orders.models import Order

            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)
            order_id = request.data.get('order_id')
            reason = request.data.get('reason', 'No reason provided')

            if not order_id:
                return Response({
                    'status': 'error',
                    'message': 'Order ID required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the order and validate assignment
            try:
                order = Order.objects.get(id=order_id, delivery_agent=request.user)
            except Order.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': 'Order not found or not assigned to you'
                }, status=status.HTTP_404_NOT_FOUND)

            # Check if order can be rejected
            if order.status not in ['assigned', 'accepted']:
                return Response({
                    'status': 'error',
                    'message': f'Order cannot be rejected. Current status: {order.status}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Reject the order
            order.reject_delivery(request.user, reason, request)

            # Update agent availability
            agent.availability = 'available'
            agent.save()

            return Response({
                'status': 'success',
                'message': 'Order rejected successfully',
                'order_id': order_id,
                'reason': reason,
                'agent_status': agent.availability
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CollectCashView(APIView):
    """
    Record cash collection for cash on delivery orders
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Record cash collection"""
        try:
            from orders.models import Order

            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)
            order_id = request.data.get('order_id')
            amount_collected = request.data.get('amount_collected')

            if not order_id or amount_collected is None:
                return Response({
                    'status': 'error',
                    'message': 'Order ID and amount collected required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the order and validate assignment
            try:
                order = Order.objects.get(id=order_id, delivery_agent=request.user)
            except Order.DoesNotExist:
                return Response({
                    'status': 'error',
                    'message': 'Order not found or not assigned to you'
                }, status=status.HTTP_404_NOT_FOUND)

            # Validate order status and payment method
            if order.status != 'delivered':
                return Response({
                    'status': 'error',
                    'message': 'Cash can only be collected after delivery'
                }, status=status.HTTP_400_BAD_REQUEST)

            if order.payment_method != 'cash_on_delivery':
                return Response({
                    'status': 'error',
                    'message': 'This order is not cash on delivery'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Record cash collection
            order.collect_cash(request.user, amount_collected, request)

            # Update agent earnings
            agent.total_earnings += float(order.delivery_fee)
            agent.save()

            return Response({
                'status': 'success',
                'message': 'Cash collected successfully',
                'order_id': order_id,
                'amount_collected': float(amount_collected),
                'expected_amount': float(order.total_amount),
                'delivery_fee_earned': float(order.delivery_fee),
                'new_status': order.status
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class MyOrdersView(APIView):
    """
    Get orders assigned to the delivery agent
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get assigned orders"""
        try:
            from orders.models import Order

            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            # Get query parameters
            status_filter = request.GET.get('status', None)
            limit = int(request.GET.get('limit', 10))

            # Build query
            orders_query = Order.objects.filter(delivery_agent=request.user)

            if status_filter:
                orders_query = orders_query.filter(status=status_filter)

            orders = orders_query.order_by('-created_at')[:limit]

            # Serialize orders
            orders_data = []
            for order in orders:
                orders_data.append({
                    'id': order.id,
                    'status': order.status,
                    'customer': {
                        'name': order.customer.name,
                        'phone': order.customer.phone
                    },
                    'restaurant': {
                        'name': order.restaurant.name,
                        'address': str(order.restaurant.address),
                        'phone': order.restaurant.contact_number
                    },
                    'delivery_address': str(order.delivery_address),
                    'total_amount': float(order.total_amount),
                    'delivery_fee': float(order.delivery_fee),
                    'payment_method': order.payment_method,
                    'created_at': order.created_at.isoformat(),
                    'accepted_at': order.accepted_at.isoformat() if order.accepted_at else None,
                    'picked_up_at': order.picked_up_at.isoformat() if order.picked_up_at else None,
                    'delivered_at': order.delivered_at.isoformat() if order.delivered_at else None,
                    'cash_collected_at': order.cash_collected_at.isoformat() if order.cash_collected_at else None,
                    'agent_notes': order.agent_notes,
                    'can_accept': order.status == 'assigned',
                    'can_reject': order.status in ['assigned', 'accepted'],
                    'can_update_status': order.status in ['accepted', 'en_route_to_restaurant', 'arrived_at_restaurant', 'picked_up', 'en_route_to_customer', 'arrived_at_customer', 'delivered'],
                    'can_collect_cash': order.status == 'delivered' and order.payment_method == 'cash_on_delivery'
                })

            return Response({
                'status': 'success',
                'data': {
                    'orders': orders_data,
                    'total_count': orders_query.count(),
                    'agent_info': {
                        'agent_id': agent.agent_id,
                        'availability': agent.availability,
                        'total_deliveries': agent.total_deliveries,
                        'total_earnings': float(agent.total_earnings)
                    }
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DeliveryAgentProfileView(APIView):
    """
    Get and update delivery agent profile
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get delivery agent profile"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)

            profile_data = {
                'agent_id': agent.agent_id,
                'full_name': agent.full_name,
                'phone_number': agent.phone_number,
                'email': agent.email,
                'address': f"{agent.street_address}, {agent.area}, {agent.district}, {agent.province}",
                'vehicle_type': agent.vehicle_type,
                'vehicle_model': agent.vehicle_model,
                'vehicle_color': agent.vehicle_color,
                'license_plate': agent.license_plate,
                'driving_license': agent.driving_license,
                'employment_status': agent.employment_status,
                'availability': agent.availability,
                'rating': float(agent.rating),
                'total_deliveries': agent.total_deliveries,
                'total_earnings': float(agent.total_earnings),
                'hire_date': agent.hire_date.isoformat() if agent.hire_date else None,
                'profile_photo': agent.profile_photo.url if agent.profile_photo else None,
                'bank_name': agent.bank_name,
                'account_number': agent.account_number,
                'account_holder_name': agent.account_holder_name,
                'user': {
                    'id': request.user.id,
                    'username': request.user.user_name,
                    'name': request.user.name,
                    'email': request.user.email,
                    'role': request.user.role,
                    'is_verified': request.user.is_verified
                }
            }

            return Response({
                'status': 'success',
                'data': profile_data
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def patch(self, request):
        """Update delivery agent profile"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, user=request.user)
            data = request.data

            # Update allowed fields
            updatable_fields = [
                'phone_number', 'email', 'emergency_contact', 'address',
                'vehicle_model', 'vehicle_color', 'license_plate',
                'bank_name', 'account_number', 'account_holder_name'
            ]

            for field in updatable_fields:
                if field in data:
                    setattr(agent, field, data[field])

            agent.save()

            return Response({
                'status': 'success',
                'message': 'Profile updated successfully',
                'data': {
                    'agent_id': agent.agent_id,
                    'full_name': agent.full_name,
                    'updated_fields': list(data.keys())
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Delivery agent profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
