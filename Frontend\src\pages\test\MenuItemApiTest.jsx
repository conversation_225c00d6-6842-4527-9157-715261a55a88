import React, { useState } from "react";
import { menuItemApi } from "../../utils/menuApi";
import Button from "../../components/common/Button";

const MenuItemApiTest = () => {
  const [loading, setLoading] = useState({});
  const [results, setResults] = useState({});
  const [selectedFile, setSelectedFile] = useState(null);

  const testCreateItem = async () => {
    setLoading((prev) => ({ ...prev, create: true }));
    try {
      const itemData = {
        category: 1, // Replace with actual category ID
        name: "Test Pizza",
        price: 12.99,
        image: selectedFile,
        is_vegetarian: true,
        is_available: true,
        description: "This is a test pizza created via API",
        preparation_time: 30,
      };

      const result = await menuItemApi.createItem(itemData);
      setResults((prev) => ({ ...prev, create: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        create: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, create: false }));
  };

  const testGetItemsByRestaurant = async () => {
    setLoading((prev) => ({ ...prev, getByRestaurant: true }));
    try {
      const result = await menuItemApi.getItemsByRestaurant(2); // Replace with actual restaurant ID
      setResults((prev) => ({ ...prev, getByRestaurant: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getByRestaurant: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getByRestaurant: false }));
  };

  const testGetItemsByCategory = async () => {
    setLoading((prev) => ({ ...prev, getByCategory: true }));
    try {
      const result = await menuItemApi.getItemsByCategory(1); // Replace with actual category ID
      setResults((prev) => ({ ...prev, getByCategory: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getByCategory: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getByCategory: false }));
  };

  const testGetSingleItem = async () => {
    setLoading((prev) => ({ ...prev, getSingle: true }));
    try {
      const result = await menuItemApi.getItem(4); // Replace with actual item ID
      setResults((prev) => ({ ...prev, getSingle: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        getSingle: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, getSingle: false }));
  };

  const testUpdateItem = async () => {
    setLoading((prev) => ({ ...prev, update: true }));
    try {
      const updateData = {
        price: 13.99,
      };
      const result = await menuItemApi.updateItem(4, updateData); // Replace with actual item ID
      setResults((prev) => ({ ...prev, update: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        update: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, update: false }));
  };

  const testDeleteItem = async () => {
    setLoading((prev) => ({ ...prev, delete: true }));
    try {
      const result = await menuItemApi.deleteItem(1); // Replace with actual item ID
      setResults((prev) => ({ ...prev, delete: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        delete: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, delete: false }));
  };

  const handleFileChange = (e) => {
    setSelectedFile(e.target.files[0]);
  };

  const renderResult = (key, title) => {
    const result = results[key];
    if (!result) return null;

    return (
      <div className='mt-4 p-4 border rounded-lg'>
        <h4 className='font-semibold text-lg mb-2'>{title} Result:</h4>
        <div
          className={`p-3 rounded ${
            result.success
              ? "bg-green-50 border-green-200"
              : "bg-red-50 border-red-200"
          }`}
        >
          <p
            className={`font-medium ${
              result.success ? "text-green-800" : "text-red-800"
            }`}
          >
            {result.success ? "Success" : "Error"}
          </p>
          {result.message && <p className='text-sm mt-1'>{result.message}</p>}
          {result.error && (
            <p className='text-sm mt-1 text-red-600'>{result.error}</p>
          )}
          {result.data && (
            <details className='mt-2'>
              <summary className='cursor-pointer text-sm font-medium'>
                View Data
              </summary>
              <pre className='mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto'>
                {JSON.stringify(result.data, null, 2)}
              </pre>
            </details>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className='container mx-auto px-4 py-8'>
      <div className='max-w-4xl mx-auto'>
        <h1 className='text-3xl font-bold mb-8'>Menu Item API Test</h1>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8'>
          {/* File Upload for Create Test */}
          <div className='md:col-span-3 mb-4'>
            <label className='block text-sm font-medium mb-2'>
              Select Image for Create Test:
            </label>
            <input
              type='file'
              accept='image/*'
              onChange={handleFileChange}
              className='block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'
            />
            {selectedFile && (
              <p className='text-sm text-gray-600 mt-1'>
                Selected: {selectedFile.name}
              </p>
            )}
          </div>

          <Button
            onClick={testCreateItem}
            disabled={loading.create}
            className='w-full'
          >
            {loading.create ? "Creating..." : "Create Menu Item"}
          </Button>

          <Button
            onClick={testGetItemsByRestaurant}
            disabled={loading.getByRestaurant}
            className='w-full'
          >
            {loading.getByRestaurant ? "Loading..." : "Get Items by Restaurant"}
          </Button>

          <Button
            onClick={testGetItemsByCategory}
            disabled={loading.getByCategory}
            className='w-full'
          >
            {loading.getByCategory ? "Loading..." : "Get Items by Category"}
          </Button>

          <Button
            onClick={testGetSingleItem}
            disabled={loading.getSingle}
            className='w-full'
          >
            {loading.getSingle ? "Loading..." : "Get Single Item"}
          </Button>

          <Button
            onClick={testUpdateItem}
            disabled={loading.update}
            className='w-full'
          >
            {loading.update ? "Updating..." : "Update Item Price"}
          </Button>

          <Button
            onClick={testDeleteItem}
            disabled={loading.delete}
            className='w-full bg-red-500 hover:bg-red-600'
          >
            {loading.delete ? "Deleting..." : "Delete Item"}
          </Button>
        </div>

        {/* Results */}
        <div className='space-y-4'>
          {renderResult("create", "Create Menu Item")}
          {renderResult("getByRestaurant", "Get Items by Restaurant")}
          {renderResult("getByCategory", "Get Items by Category")}
          {renderResult("getSingle", "Get Single Item")}
          {renderResult("update", "Update Item")}
          {renderResult("delete", "Delete Item")}
        </div>

        <div className='mt-8 bg-blue-50 border-l-4 border-blue-500 p-4'>
          <h3 className='font-semibold text-blue-800 mb-2'>API Information:</h3>
          <ul className='text-blue-700 text-sm space-y-1'>
            <li>
              <strong>Base URL:</strong> https://afghansufra.luilala.com/api
            </li>
            <li>
              <strong>Authentication:</strong> Bearer Token (from localStorage)
            </li>
            <li>
              <strong>Create:</strong> POST /restaurant/menu-items/
              (multipart/form-data)
            </li>
            <li>
              <strong>List by Category:</strong> GET /menu-items/?category_id=1
            </li>
            <li>
              <strong>List by Restaurant:</strong> GET
              /menu-items/?restaurant_id=2
            </li>
            <li>
              <strong>Get Single:</strong> GET /menu-items/4/
            </li>
            <li>
              <strong>Update:</strong> PATCH /menu-items/4/
            </li>
            <li>
              <strong>Delete:</strong> DELETE /menu-items/1/
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default MenuItemApiTest;
