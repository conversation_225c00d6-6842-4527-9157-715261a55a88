// Frontend/src/components/filters/DynamicFilterTest.jsx
import React from "react";
import { useFilters as useDynamicFilters } from "../../hooks/useFilters";
import { useFilters as useFiltersContext } from "../../context/FiltersContext";
import Card from "../common/Card";
import Button from "../common/Button";
import Badge from "../common/Badge";
import { RefreshCw, Filter, CheckCircle, AlertCircle } from "lucide-react";

const DynamicFilterTest = () => {
  const { filterOptions, loading, error, refreshFilters } = useDynamicFilters();
  const {
    filtersLoading,
    filtersError,
    refreshFilters: refreshContextFilters,
  } = useFiltersContext();

  const handleRefresh = () => {
    refreshFilters();
    refreshContextFilters();
  };

  if (loading || filtersLoading) {
    return (
      <Card className='p-6'>
        <div className='flex items-center justify-center'>
          <RefreshCw className='animate-spin mr-2' size={20} />
          <span>Loading dynamic filters...</span>
        </div>
      </Card>
    );
  }

  if (error || filtersError) {
    return (
      <Card className='p-6 border-red-200 bg-red-50'>
        <div className='flex items-center mb-4'>
          <AlertCircle className='text-red-500 mr-2' size={20} />
          <h3 className='text-red-700 font-semibold'>Filter Loading Error</h3>
        </div>
        <p className='text-red-600 mb-4'>{error || filtersError}</p>
        <Button onClick={handleRefresh} variant='outline' size='small'>
          <RefreshCw size={16} className='mr-2' />
          Retry
        </Button>
      </Card>
    );
  }

  return (
    <div className='space-y-6'>
      <Card className='p-6'>
        <div className='flex items-center justify-between mb-6'>
          <div className='flex items-center'>
            <Filter className='text-primary-500 mr-2' size={24} />
            <h2 className='text-xl font-semibold'>
              Dynamic Filter System Test
            </h2>
          </div>
          <div className='flex items-center space-x-2'>
            <Badge variant='success'>
              <CheckCircle size={14} className='mr-1' />
              Active
            </Badge>
            <Button onClick={handleRefresh} variant='outline' size='small'>
              <RefreshCw size={16} className='mr-2' />
              Refresh
            </Button>
          </div>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {/* Cuisines */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-gray-700'>
              Cuisines ({filterOptions.cuisines?.length || 0})
            </h3>
            <div className='space-y-2 max-h-40 overflow-y-auto'>
              {filterOptions.cuisines?.slice(0, 5).map((cuisine, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between p-2 bg-gray-50 rounded'
                >
                  <span className='text-sm'>
                    {cuisine.icon} {cuisine.label}
                  </span>
                  <Badge variant='outline' size='small'>
                    {cuisine.value}
                  </Badge>
                </div>
              ))}
              {filterOptions.cuisines?.length > 5 && (
                <div className='text-xs text-gray-500 text-center'>
                  +{filterOptions.cuisines.length - 5} more
                </div>
              )}
            </div>
          </div>

          {/* Dietary Restrictions */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-gray-700'>
              Dietary Restrictions (
              {filterOptions.dietaryRestrictions?.length || 0})
            </h3>
            <div className='space-y-2 max-h-40 overflow-y-auto'>
              {filterOptions.dietaryRestrictions?.map((restriction, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between p-2 bg-gray-50 rounded'
                >
                  <span className='text-sm'>
                    {restriction.icon} {restriction.label}
                  </span>
                  <Badge variant='outline' size='small'>
                    {restriction.value}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* Delivery Times */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-gray-700'>
              Delivery Times ({filterOptions.deliveryTimes?.length || 0})
            </h3>
            <div className='space-y-2 max-h-40 overflow-y-auto'>
              {filterOptions.deliveryTimes?.map((time, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between p-2 bg-gray-50 rounded'
                >
                  <span className='text-sm'>
                    {time.icon} {time.label}
                  </span>
                  <Badge variant='outline' size='small'>
                    {time.value}min
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* Price Ranges */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-gray-700'>
              Price Ranges ({filterOptions.priceRanges?.length || 0})
            </h3>
            <div className='space-y-2 max-h-40 overflow-y-auto'>
              {filterOptions.priceRanges?.map((price, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between p-2 bg-gray-50 rounded'
                >
                  <span className='text-sm'>
                    {price.label} - {price.description}
                  </span>
                  <Badge variant='outline' size='small'>
                    {price.value}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* Features */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-gray-700'>
              Features ({filterOptions.features?.length || 0})
            </h3>
            <div className='space-y-2 max-h-40 overflow-y-auto'>
              {filterOptions.features?.map((feature, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between p-2 bg-gray-50 rounded'
                >
                  <span className='text-sm'>
                    {feature.icon} {feature.label}
                  </span>
                  <Badge variant='outline' size='small'>
                    {feature.id}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* Sort Options */}
          <div className='space-y-3'>
            <h3 className='font-semibold text-gray-700'>
              Sort Options ({filterOptions.sortOptions?.length || 0})
            </h3>
            <div className='space-y-2 max-h-40 overflow-y-auto'>
              {filterOptions.sortOptions?.map((sort, index) => (
                <div
                  key={index}
                  className='flex items-center justify-between p-2 bg-gray-50 rounded'
                >
                  <span className='text-sm'>
                    {sort.icon} {sort.label}
                  </span>
                  <Badge variant='outline' size='small'>
                    {sort.value}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className='mt-6 p-4 bg-blue-50 rounded-lg'>
          <h4 className='font-semibold text-blue-800 mb-2'>System Status</h4>
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
            <div>
              <span className='text-blue-600'>Total Cuisines:</span>
              <span className='ml-2 font-semibold'>
                {filterOptions.cuisines?.length || 0}
              </span>
            </div>
            <div>
              <span className='text-blue-600'>Dietary Options:</span>
              <span className='ml-2 font-semibold'>
                {filterOptions.dietaryRestrictions?.length || 0}
              </span>
            </div>
            <div>
              <span className='text-blue-600'>Features:</span>
              <span className='ml-2 font-semibold'>
                {filterOptions.features?.length || 0}
              </span>
            </div>
            <div>
              <span className='text-blue-600'>Sort Options:</span>
              <span className='ml-2 font-semibold'>
                {filterOptions.sortOptions?.length || 0}
              </span>
            </div>
          </div>
        </div>
      </Card>

      <Card className='p-6'>
        <h3 className='font-semibold mb-4'>Dynamic Filter Benefits</h3>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div className='space-y-2'>
            <h4 className='font-medium text-green-700'>
              ✅ What's Dynamic Now:
            </h4>
            <ul className='text-sm space-y-1 text-gray-600'>
              <li>• Cuisines extracted from actual restaurants</li>
              <li>• Dietary restrictions from system configuration</li>
              <li>• Delivery times based on system settings</li>
              <li>• Features with dynamic descriptions</li>
              <li>• Price ranges from restaurant data</li>
              <li>• Distance options based on delivery radius</li>
            </ul>
          </div>
          <div className='space-y-2'>
            <h4 className='font-medium text-blue-700'>🔄 Auto-Updates:</h4>
            <ul className='text-sm space-y-1 text-gray-600'>
              <li>• New restaurants add new cuisines automatically</li>
              <li>• System settings changes reflect immediately</li>
              <li>• Cached for performance (10-minute cache)</li>
              <li>• Fallback options if API fails</li>
              <li>• Real-time filtering based on actual data</li>
              <li>• Admin can manage options via backend</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default DynamicFilterTest;
