import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Camera, Star, Upload, X, Heart, ThumbsUp } from 'lucide-react';
import { useSocial } from '../../context/SocialContext';
import Card from '../common/Card';
import Button from '../common/Button';
import FormControl from '../common/FormControl';

const PhotoReview = ({ restaurantId, restaurantName, onClose, onSubmit }) => {
  const { addPhotoReview } = useSocial();
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [photos, setPhotos] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm();

  const handlePhotoUpload = (event) => {
    const files = Array.from(event.target.files);
    
    files.forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPhotos(prev => [...prev, {
            id: Date.now() + Math.random(),
            file,
            url: e.target.result,
            name: file.name
          }]);
        };
        reader.readAsDataURL(file);
      }
    });
  };

  const removePhoto = (photoId) => {
    setPhotos(prev => prev.filter(photo => photo.id !== photoId));
  };

  const handleSubmitReview = async (data) => {
    if (rating === 0) {
      alert('Please select a rating');
      return;
    }

    setIsSubmitting(true);
    
    try {
      const review = addPhotoReview(
        restaurantId,
        restaurantName,
        rating,
        data.comment,
        photos.map(photo => photo.url)
      );

      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      onSubmit?.(review);
      reset();
      setRating(0);
      setPhotos([]);
      onClose?.();
    } catch (error) {
      console.error('Error submitting review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Write a Photo Review</h3>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        )}
      </div>

      <div className="mb-4">
        <h4 className="font-medium text-gray-900">{restaurantName}</h4>
        <p className="text-gray-600 text-sm">Share your experience with photos</p>
      </div>

      <form onSubmit={handleSubmit(handleSubmitReview)} className="space-y-6">
        {/* Rating */}
        <div>
          <label className="block text-sm font-medium mb-2">Your Rating *</label>
          <div className="flex items-center space-x-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                type="button"
                onClick={() => setRating(star)}
                onMouseEnter={() => setHoverRating(star)}
                onMouseLeave={() => setHoverRating(0)}
                className="p-1 transition-colors"
              >
                <Star
                  size={24}
                  className={`${
                    star <= (hoverRating || rating)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  } transition-colors`}
                />
              </button>
            ))}
            <span className="ml-2 text-sm text-gray-600">
              {rating > 0 && (
                <>
                  {rating} star{rating !== 1 ? 's' : ''}
                  {rating === 5 && ' - Excellent!'}
                  {rating === 4 && ' - Very Good'}
                  {rating === 3 && ' - Good'}
                  {rating === 2 && ' - Fair'}
                  {rating === 1 && ' - Poor'}
                </>
              )}
            </span>
          </div>
        </div>

        {/* Photo Upload */}
        <div>
          <label className="block text-sm font-medium mb-2">Add Photos</label>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors">
            <input
              type="file"
              multiple
              accept="image/*"
              onChange={handlePhotoUpload}
              className="hidden"
              id="photo-upload"
            />
            <label htmlFor="photo-upload" className="cursor-pointer">
              <Camera size={32} className="mx-auto text-gray-400 mb-2" />
              <p className="text-gray-600">Click to upload photos</p>
              <p className="text-gray-500 text-sm">PNG, JPG up to 10MB each</p>
            </label>
          </div>

          {/* Photo Preview */}
          {photos.length > 0 && (
            <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-3">
              {photos.map((photo) => (
                <div key={photo.id} className="relative group">
                  <img
                    src={photo.url}
                    alt="Review photo"
                    className="w-full h-24 object-cover rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => removePhoto(photo.id)}
                    className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <X size={12} />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Comment */}
        <FormControl
          label="Your Review"
          error={errors.comment?.message}
          required
        >
          <textarea
            rows={4}
            placeholder="Tell others about your experience..."
            className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
              errors.comment ? 'border-red-500' : 'border-gray-300'
            }`}
            {...register('comment', {
              required: 'Please write a review',
              minLength: {
                value: 10,
                message: 'Review must be at least 10 characters'
              }
            })}
          />
        </FormControl>

        {/* Submit Button */}
        <div className="flex space-x-3">
          <Button
            type="submit"
            variant="primary"
            loading={isSubmitting}
            disabled={rating === 0}
            className="flex-1"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Review'}
          </Button>
          {onClose && (
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Cancel
            </Button>
          )}
        </div>
      </form>
    </Card>
  );
};

// Review Display Component
export const ReviewCard = ({ review, onLike, onMarkHelpful }) => {
  const [liked, setLiked] = useState(false);
  const [markedHelpful, setMarkedHelpful] = useState(false);

  const handleLike = () => {
    if (!liked) {
      onLike?.(review.id);
      setLiked(true);
    }
  };

  const handleMarkHelpful = () => {
    if (!markedHelpful) {
      onMarkHelpful?.(review.id);
      setMarkedHelpful(true);
    }
  };

  return (
    <Card className="p-4">
      <div className="flex items-start space-x-3">
        <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
          <span className="text-primary-600 font-medium text-sm">
            {review.userName?.charAt(0) || 'U'}
          </span>
        </div>
        
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <div>
              <h4 className="font-medium">{review.userName}</h4>
              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    size={14}
                    className={`${
                      star <= review.rating
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
                <span className="ml-2 text-sm text-gray-600">
                  {new Date(review.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          <p className="text-gray-700 mb-3">{review.comment}</p>

          {/* Photos */}
          {review.photos && review.photos.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mb-3">
              {review.photos.map((photo, index) => (
                <img
                  key={index}
                  src={photo}
                  alt="Review photo"
                  className="w-full h-20 object-cover rounded-lg"
                />
              ))}
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center space-x-4 text-sm">
            <button
              onClick={handleLike}
              className={`flex items-center space-x-1 transition-colors ${
                liked ? 'text-red-500' : 'text-gray-500 hover:text-red-500'
              }`}
            >
              <Heart size={14} className={liked ? 'fill-current' : ''} />
              <span>{review.likes + (liked ? 1 : 0)}</span>
            </button>
            
            <button
              onClick={handleMarkHelpful}
              className={`flex items-center space-x-1 transition-colors ${
                markedHelpful ? 'text-blue-500' : 'text-gray-500 hover:text-blue-500'
              }`}
            >
              <ThumbsUp size={14} className={markedHelpful ? 'fill-current' : ''} />
              <span>Helpful ({review.helpful + (markedHelpful ? 1 : 0)})</span>
            </button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default PhotoReview;
