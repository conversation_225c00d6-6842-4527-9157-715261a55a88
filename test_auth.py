#!/usr/bin/env python3
"""
Test script for Afghan Sufra authentication endpoints
"""
import requests
import json
import sys

BASE_URL = "http://127.0.0.1:8000/api"

def test_registration():
    """Test user registration endpoint"""
    print("🧪 Testing Registration Endpoint...")
    
    url = f"{BASE_URL}/auth/register/"
    data = {
        "name": "Test User",
        "user_name": "testuser123",
        "email": "<EMAIL>",
        "password": "testpass123",
        "confirm_password": "testpass123",
        "phone": "+1234567890",
        "role": "customer"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 201:
            print("✅ Registration successful!")
            return response.json()
        else:
            print("❌ Registration failed!")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Backend server is not running!")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_login():
    """Test user login endpoint"""
    print("\n🧪 Testing Login Endpoint...")
    
    url = f"{BASE_URL}/auth/login/"
    data = {
        "user_name": "testuser123",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Login successful!")
            return response.json()
        else:
            print("❌ Login failed!")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Backend server is not running!")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_email_verification():
    """Test email verification endpoint"""
    print("\n🧪 Testing Email Verification Endpoint...")
    
    url = f"{BASE_URL}/auth/verify-email/"
    data = {
        "email": "<EMAIL>",
        "otp": "123456"  # This will fail, but we can test the validation
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Email verification successful!")
            return response.json()
        else:
            print("❌ Email verification failed (expected for test OTP)!")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Backend server is not running!")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_validation_errors():
    """Test validation errors"""
    print("\n🧪 Testing Validation Errors...")
    
    # Test registration with invalid data
    url = f"{BASE_URL}/auth/register/"
    invalid_data = {
        "name": "",  # Empty name
        "user_name": "ab",  # Too short
        "email": "invalid-email",  # Invalid email
        "password": "123",  # Too short
        "confirm_password": "456",  # Doesn't match
        "phone": "invalid",  # Invalid phone
        "role": "customer"
    }
    
    try:
        response = requests.post(url, json=invalid_data, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 400:
            print("✅ Validation errors working correctly!")
            return response.json()
        else:
            print("❌ Validation errors not working as expected!")
            return None
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Backend server is not running!")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def main():
    """Run all authentication tests"""
    print("🚀 Starting Afghan Sufra Authentication Tests\n")
    
    # Test 1: Registration
    registration_result = test_registration()
    
    # Test 2: Login (will fail if user not verified)
    login_result = test_login()
    
    # Test 3: Email Verification
    verification_result = test_email_verification()
    
    # Test 4: Validation Errors
    validation_result = test_validation_errors()
    
    print("\n📊 Test Summary:")
    print(f"Registration: {'✅ PASS' if registration_result else '❌ FAIL'}")
    print(f"Login: {'✅ PASS' if login_result else '❌ FAIL'}")
    print(f"Email Verification: {'✅ PASS' if verification_result else '❌ FAIL'}")
    print(f"Validation Errors: {'✅ PASS' if validation_result else '❌ FAIL'}")

if __name__ == "__main__":
    main()
