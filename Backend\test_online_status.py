#!/usr/bin/env python3
"""
Test script for delivery agent online status functionality
"""
import os
import sys
import django
import requests
import json
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from deliveryAgent.models import DeliveryAgentProfile
from users.models import User

User = get_user_model()

def test_online_status_api():
    """Test the online status toggle API directly"""
    print("🧪 Testing online status API...")
    
    # Get a test agent
    try:
        agent_user = User.objects.get(user_name='test_delivery_agent')
        agent_profile = DeliveryAgentProfile.objects.get(user=agent_user)
        print(f"Testing with agent: {agent_user.name}")
        print(f"Current status: {agent_profile.status}")
        print(f"Employment status: {agent_profile.employment_status}")
        print(f"Is online: {agent_profile.is_online}")
        print(f"Availability: {agent_profile.availability}")
    except (User.DoesNotExist, DeliveryAgentProfile.DoesNotExist):
        print("❌ Test agent not found")
        return False
    
    # Test the API endpoint directly
    try:
        from deliveryAgent.views import ToggleOnlineStatusView
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.post('/toggle-online-status/')
        request.user = agent_user
        
        view = ToggleOnlineStatusView()
        response = view.post(request)
        
        if hasattr(response, 'data'):
            response_data = response.data
        else:
            response_data = json.loads(response.content.decode())
        
        print(f"API Response: {response_data}")
        
        if response_data.get('status') == 'success':
            print("✅ Online status toggle API working!")
            
            # Refresh agent profile
            agent_profile.refresh_from_db()
            print(f"New online status: {agent_profile.is_online}")
            print(f"New availability: {agent_profile.availability}")
            return True
        else:
            print(f"❌ API failed: {response_data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {str(e)}")
        return False

def test_availability_update():
    """Test the availability update API"""
    print("\n🧪 Testing availability update API...")
    
    try:
        agent_user = User.objects.get(user_name='test_delivery_agent')
        agent_profile = DeliveryAgentProfile.objects.get(user=agent_user)
        
        from deliveryAgent.views import UpdateAvailabilityView
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.post('/update-availability/', {
            'availability': 'available',
            'is_online': True
        })
        request.user = agent_user
        request.data = {
            'availability': 'available',
            'is_online': True
        }
        
        view = UpdateAvailabilityView()
        response = view.post(request)
        
        if hasattr(response, 'data'):
            response_data = response.data
        else:
            response_data = json.loads(response.content.decode())
        
        print(f"Availability API Response: {response_data}")
        
        if response_data.get('status') == 'success':
            print("✅ Availability update API working!")
            
            # Refresh agent profile
            agent_profile.refresh_from_db()
            print(f"New online status: {agent_profile.is_online}")
            print(f"New availability: {agent_profile.availability}")
            return True
        else:
            print(f"❌ Availability API failed: {response_data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing availability API: {str(e)}")
        return False

def check_agent_requirements():
    """Check if agents meet the requirements for going online"""
    print("\n🔍 Checking agent requirements...")
    
    agents = DeliveryAgentProfile.objects.all()[:5]  # Check first 5 agents
    
    for agent in agents:
        print(f"\nAgent: {agent.user.name}")
        print(f"  Status: {agent.status}")
        print(f"  Employment: {agent.employment_status}")
        print(f"  Is Online: {agent.is_online}")
        print(f"  Availability: {agent.availability}")
        
        # Check requirements
        can_go_online = (
            agent.employment_status in ['active', 'inactive'] and
            agent.status in ['approved', 'pending']
        )
        print(f"  Can go online: {'✅' if can_go_online else '❌'}")

def main():
    """Main test function"""
    print("🚀 Starting online status tests...")
    
    # Check agent requirements
    check_agent_requirements()
    
    # Test online status toggle
    status_test = test_online_status_api()
    
    # Test availability update
    availability_test = test_availability_update()
    
    print(f"\n📊 Test Results:")
    print(f"Online Status Toggle: {'✅ PASS' if status_test else '❌ FAIL'}")
    print(f"Availability Update: {'✅ PASS' if availability_test else '❌ FAIL'}")
    
    if status_test and availability_test:
        print("\n🎉 All tests passed! Online status functionality is working.")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")

if __name__ == '__main__':
    main()
