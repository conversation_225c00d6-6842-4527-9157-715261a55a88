# financial_management/admin.py
from django.contrib import admin
from .models import (
    CommissionStructure, RestaurantEarnings, RestaurantPayout,
    RestaurantBankAccount, FinancialReport
)


@admin.register(CommissionStructure)
class CommissionStructureAdmin(admin.ModelAdmin):
    list_display = ['restaurant', 'commission_rate', 'payment_processing_fee', 'payout_frequency']
    list_filter = ['payout_frequency', 'commission_rate']
    search_fields = ['restaurant__name']


@admin.register(RestaurantEarnings)
class RestaurantEarningsAdmin(admin.ModelAdmin):
    list_display = ['restaurant', 'order', 'order_total', 'commission_amount', 'net_earnings', 'is_paid']
    list_filter = ['is_paid', 'created_at', 'restaurant']
    search_fields = ['restaurant__name', 'order__id']
    readonly_fields = ['commission_amount', 'payment_processing_fee', 'gross_earnings', 'net_earnings']


@admin.register(RestaurantPayout)
class RestaurantPayoutAdmin(admin.ModelAdmin):
    list_display = ['restaurant', 'payout_amount', 'status', 'payment_method', 'created_at']
    list_filter = ['status', 'payment_method', 'created_at']
    search_fields = ['restaurant__name', 'transaction_id']


@admin.register(RestaurantBankAccount)
class RestaurantBankAccountAdmin(admin.ModelAdmin):
    list_display = ['restaurant', 'bank_name', 'account_holder_name', 'is_verified']
    list_filter = ['is_verified', 'bank_name']
    search_fields = ['restaurant__name', 'account_holder_name']


@admin.register(FinancialReport)
class FinancialReportAdmin(admin.ModelAdmin):
    list_display = ['restaurant', 'report_type', 'period_start', 'total_revenue', 'net_earnings']
    list_filter = ['report_type', 'period_start', 'restaurant']
    search_fields = ['restaurant__name']
