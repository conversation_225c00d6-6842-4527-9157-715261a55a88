#!/usr/bin/env python3
"""
Delivery Agent Workflow Test Script
Tests the complete delivery agent system workflow
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# Add the Backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.append(str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from django.urls import reverse
from deliveryAgent.models import DeliveryAgentProfile
from orders.models import Order
from restaurant.models import Restaurant
from restaurant.models import Address

User = get_user_model()

class DeliveryAgentWorkflowTester:
    def __init__(self):
        self.client = Client()
        self.base_url = "http://localhost:8001"
        self.agent_user = None
        self.agent_profile = None
        self.test_order = None
        
    def print_step(self, step_num, title):
        print(f"\n{'='*60}")
        print(f"STEP {step_num}: {title}")
        print('='*60)
    
    def print_result(self, success, message):
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{status}: {message}")
    
    def test_1_create_delivery_agent(self):
        """Test 1: Create a new delivery agent"""
        self.print_step(1, "CREATE DELIVERY AGENT")
        
        try:
            # Create user account
            self.agent_user = User.objects.create_user(
                user_name='test_agent_001',
                name='Ahmad Ali Test Agent',
                phone='+93 70 123 4567',
                email='<EMAIL>',
                password='testpass123',
                role='delivery_agent'
            )
            
            # Create delivery agent profile
            self.agent_profile = DeliveryAgentProfile.objects.create(
                user=self.agent_user,
                agent_id='DA001',
                phone_number='+93 70 123 4567',
                emergency_contact='+93 78 987 6543',
                address='Shar-e-Naw, Kabul, Afghanistan',
                vehicle_type='motorcycle',
                vehicle_model='Honda 125cc',
                vehicle_color='Red',
                license_plate='KBL-001',
                driving_license='DL123456',
                status='active',
                availability='available',
                is_online=True,
                current_latitude=34.5553,
                current_longitude=69.2075,
                current_address='Shar-e-Naw, Kabul',
                bank_name='Afghanistan International Bank',
                account_number='**********',
                account_holder_name='Ahmad Ali',
                is_verified=True
            )
            
            self.print_result(True, f"Created delivery agent: {self.agent_profile.user.name} (ID: {self.agent_profile.agent_id})")
            print(f"   • User ID: {self.agent_user.id}")
            print(f"   • Agent ID: {self.agent_profile.agent_id}")
            print(f"   • Status: {self.agent_profile.status}")
            print(f"   • Availability: {self.agent_profile.availability}")
            print(f"   • Location: {self.agent_profile.current_address}")
            
            return True
            
        except Exception as e:
            self.print_result(False, f"Error creating delivery agent: {e}")
            return False
    
    def test_2_agent_login_dashboard(self):
        """Test 2: Agent login and dashboard access"""
        self.print_step(2, "AGENT LOGIN & DASHBOARD")
        
        try:
            # Login agent
            login_success = self.client.login(
                username='test_agent_001',
                password='testpass123'
            )
            
            if not login_success:
                self.print_result(False, "Agent login failed")
                return False
            
            # Test dashboard access
            response = self.client.get('/api/delivery-agent/dashboard/')
            
            if response.status_code == 200:
                dashboard_data = response.json()
                self.print_result(True, "Dashboard access successful")
                print(f"   • Response Status: {response.status_code}")
                print(f"   • Dashboard Data Keys: {list(dashboard_data.keys()) if isinstance(dashboard_data, dict) else 'Non-dict response'}")
            else:
                self.print_result(False, f"Dashboard access failed: {response.status_code}")
                return False
            
            return True
            
        except Exception as e:
            self.print_result(False, f"Error in login/dashboard test: {e}")
            return False
    
    def test_3_available_orders(self):
        """Test 3: Get available orders for agent"""
        self.print_step(3, "AVAILABLE ORDERS")
        
        try:
            # Create a test restaurant and order first
            restaurant_user = User.objects.create_user(
                user_name='test_restaurant',
                name='Test Restaurant',
                email='<EMAIL>',
                password='testpass123',
                role='restaurant_owner'
            )
            
            restaurant_address = Address.objects.create(
                street='Test Street 123',
                city='Kabul',
                state='Kabul',
                postal_code='1001',
                country='Afghanistan',
                latitude=34.5555,
                longitude=69.2070
            )
            
            restaurant = Restaurant.objects.create(
                name='Test Kabul Restaurant',
                owner=restaurant_user,
                address=restaurant_address,
                contact_number='+93 70 111 2222',
                email='<EMAIL>',
                cuisine_type='Afghan',
                is_active=True,
                is_approved=True
            )
            
            # Create customer
            customer_user = User.objects.create_user(
                user_name='test_customer',
                name='Test Customer',
                email='<EMAIL>',
                password='testpass123',
                role='customer'
            )
            
            customer_address = Address.objects.create(
                street='Customer Street 456',
                city='Kabul',
                state='Kabul',
                postal_code='1002',
                country='Afghanistan',
                latitude=34.5560,
                longitude=69.2080
            )
            
            # Create test order
            self.test_order = Order.objects.create(
                customer=customer_user,
                restaurant=restaurant,
                delivery_address=customer_address,
                total_amount=850.00,
                delivery_fee=25.00,
                status='confirmed',
                payment_method='cash',
                special_instructions='Test order for delivery agent workflow'
            )
            
            # Test available orders endpoint
            response = self.client.get('/api/delivery-agent/available-orders/')
            
            if response.status_code == 200:
                orders_data = response.json()
                self.print_result(True, "Available orders retrieved successfully")
                print(f"   • Response Status: {response.status_code}")
                print(f"   • Available Orders: {len(orders_data.get('orders', [])) if isinstance(orders_data, dict) else 'Unknown'}")
                print(f"   • Test Order ID: {self.test_order.id}")
                print(f"   • Test Order Status: {self.test_order.status}")
            else:
                self.print_result(False, f"Available orders failed: {response.status_code}")
                return False
            
            return True
            
        except Exception as e:
            self.print_result(False, f"Error in available orders test: {e}")
            return False
    
    def test_4_accept_order(self):
        """Test 4: Accept an order"""
        self.print_step(4, "ACCEPT ORDER")
        
        try:
            if not self.test_order:
                self.print_result(False, "No test order available")
                return False
            
            # Test accept order endpoint
            accept_data = {
                'order_id': self.test_order.id
            }
            
            response = self.client.post(
                '/api/delivery-agent/accept-order/',
                data=json.dumps(accept_data),
                content_type='application/json'
            )
            
            if response.status_code in [200, 201]:
                self.print_result(True, "Order accepted successfully")
                
                # Refresh order from database
                self.test_order.refresh_from_db()
                print(f"   • Response Status: {response.status_code}")
                print(f"   • Order Status: {self.test_order.status}")
                print(f"   • Delivery Agent: {self.test_order.delivery_agent}")
            else:
                response_data = response.json() if response.content else {}
                self.print_result(False, f"Order acceptance failed: {response.status_code}")
                print(f"   • Response: {response_data}")
                return False
            
            return True
            
        except Exception as e:
            self.print_result(False, f"Error in accept order test: {e}")
            return False
    
    def test_5_update_order_status(self):
        """Test 5: Update order status through delivery process"""
        self.print_step(5, "UPDATE ORDER STATUS")
        
        try:
            if not self.test_order:
                self.print_result(False, "No test order available")
                return False
            
            # Test different status updates
            statuses = [
                ('picked_up', 'Order picked up from restaurant'),
                ('in_transit', 'Order in transit to customer'),
                ('delivered', 'Order delivered to customer')
            ]
            
            for status, description in statuses:
                update_data = {
                    'order_id': self.test_order.id,
                    'status': status,
                    'location': {
                        'latitude': 34.5555,
                        'longitude': 69.2075,
                        'address': 'Current location'
                    }
                }
                
                response = self.client.post(
                    '/api/delivery-agent/update-order-status/',
                    data=json.dumps(update_data),
                    content_type='application/json'
                )
                
                if response.status_code in [200, 201]:
                    self.test_order.refresh_from_db()
                    print(f"   ✅ {description}: {self.test_order.status}")
                else:
                    print(f"   ❌ Failed to update to {status}: {response.status_code}")
                    return False
            
            self.print_result(True, "Order status updates completed successfully")
            return True
            
        except Exception as e:
            self.print_result(False, f"Error in order status update test: {e}")
            return False
    
    def test_6_earnings_summary(self):
        """Test 6: Get earnings summary"""
        self.print_step(6, "EARNINGS SUMMARY")
        
        try:
            response = self.client.get('/api/delivery-agent/earnings-summary/')
            
            if response.status_code == 200:
                earnings_data = response.json()
                self.print_result(True, "Earnings summary retrieved successfully")
                print(f"   • Response Status: {response.status_code}")
                print(f"   • Earnings Data: {earnings_data}")
            else:
                self.print_result(False, f"Earnings summary failed: {response.status_code}")
                return False
            
            return True
            
        except Exception as e:
            self.print_result(False, f"Error in earnings summary test: {e}")
            return False
    
    def test_7_performance_metrics(self):
        """Test 7: Get performance metrics"""
        self.print_step(7, "PERFORMANCE METRICS")
        
        try:
            response = self.client.get('/api/delivery-agent/performance-metrics/')
            
            if response.status_code == 200:
                performance_data = response.json()
                self.print_result(True, "Performance metrics retrieved successfully")
                print(f"   • Response Status: {response.status_code}")
                print(f"   • Performance Data: {performance_data}")
            else:
                self.print_result(False, f"Performance metrics failed: {response.status_code}")
                return False
            
            return True
            
        except Exception as e:
            self.print_result(False, f"Error in performance metrics test: {e}")
            return False
    
    def cleanup(self):
        """Clean up test data"""
        self.print_step("CLEANUP", "CLEANING UP TEST DATA")
        
        try:
            # Delete test data
            if self.test_order:
                self.test_order.delete()
            
            # Delete test users and related data
            User.objects.filter(user_name__startswith='test_').delete()
            Restaurant.objects.filter(name__startswith='Test').delete()
            Address.objects.filter(street__startswith='Test').delete()
            Address.objects.filter(street__startswith='Customer').delete()
            
            self.print_result(True, "Test data cleaned up successfully")
            
        except Exception as e:
            self.print_result(False, f"Error during cleanup: {e}")
    
    def run_all_tests(self):
        """Run all workflow tests"""
        print("🚀 DELIVERY AGENT WORKFLOW TEST")
        print("="*60)
        
        tests = [
            self.test_1_create_delivery_agent,
            self.test_2_agent_login_dashboard,
            self.test_3_available_orders,
            self.test_4_accept_order,
            self.test_5_update_order_status,
            self.test_6_earnings_summary,
            self.test_7_performance_metrics
        ]
        
        results = []
        
        for test in tests:
            try:
                result = test()
                results.append(result)
            except Exception as e:
                print(f"❌ Test failed with exception: {e}")
                results.append(False)
        
        # Print summary
        print(f"\n{'='*60}")
        print("TEST SUMMARY")
        print('='*60)
        
        passed = sum(results)
        total = len(results)
        
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("🎉 ALL TESTS PASSED! Delivery agent workflow is working properly.")
        else:
            print("⚠️  Some tests failed. Please check the errors above.")
        
        # Cleanup
        self.cleanup()
        
        return passed == total

def main():
    """Main function"""
    tester = DeliveryAgentWorkflowTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ Delivery Agent System is working properly!")
    else:
        print("\n❌ Delivery Agent System has issues that need to be fixed.")

if __name__ == '__main__':
    main()
