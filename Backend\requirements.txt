# pip install -r requirements.txt
# Afghan Sufra Food Delivery Backend Dependencies

# Core Django Framework
Django==5.0.4
djangorestframework==3.15.2
djangorestframework_simplejwt==5.4.0

# Database and ORM
pillow==10.3.0

# Authentication and Security
bcrypt==4.3.0
cryptography==43.0.1
python-jwt==4.1.0

# API and CORS
django-cors-headers==4.3.1
drf-nested-routers==0.93.4

# WebSocket Support (for real-time features)
channels==4.2.2
channels_redis==4.2.1

# Geolocation and Mapping Services
geopy==2.4.1
googlemaps==4.10.0

# Task Queue and Background Jobs
celery==5.5.2
billiard==4.2.1

# Scheduling
django-apscheduler==0.7.0

# Date and Time Utilities
DateTime==5.5
python-dateutil==2.9.0.post0

# Environment Configuration
python-dotenv==1.0.1

# Web Scraping and HTML Processing
beautifulsoup4==4.12.3
binaryornot==0.4.4

# UI and Forms (if using Django templates)
django-crispy-forms==2.3
crispy-tailwind==1.0.3
django-tailwind==3.8.0

# Utilities
path==16.14.0

# Redis (for channels and caching)
redis==6.2.0

# Message Packing (for channels)
msgpack==1.1.1

# HTTP Requests (for external API calls)
requests==2.32.3
