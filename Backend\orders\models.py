# orders/models.py
from datetime import datetime
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from users.models import User
from restaurant.models import Restaurant, MenuItem, Address

class Order(models.Model):
    ORDER_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('preparing', 'Preparing'),
        ('ready', 'Ready for Pickup'),
        ('assigned', 'Assigned to Delivery Agent'),
        ('accepted', 'Accepted by Agent'),
        ('en_route_to_restaurant', 'En Route to Restaurant'),
        ('arrived_at_restaurant', 'Arrived at Restaurant'),
        ('picked_up', 'Picked Up from Restaurant'),
        ('en_route_to_customer', 'En Route to Customer'),
        ('arrived_at_customer', 'Arrived at Customer'),
        ('delivered', 'Delivered'),
        ('cash_collected', 'Cash Collected'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('rejected', 'Rejected by Agent'),
        ('refunded', 'Refunded'),
    ]
    
    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('refunded', 'Refunded'),
    ]
    
    PAYMENT_METHOD_CHOICES = [
        ('credit_card', 'Credit Card'),
        ('debit_card', 'Debit Card'),
        ('paypal', 'PayPal'),
        ('cash_on_delivery', 'Cash on Delivery'),
    ]

    customer = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='orders',
        limit_choices_to={'role': 'customer'}
    )
    delivery_address = models.ForeignKey(
        Address,
        on_delete=models.PROTECT,
        related_name='orders'
    )
    restaurant = models.ForeignKey(
        Restaurant,
        on_delete=models.PROTECT,
        related_name='orders'
    )
    delivery_agent = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='delivery_orders',
        limit_choices_to={'role': 'delivery_agent'}
    )
    status = models.CharField(
        max_length=30,
        choices=ORDER_STATUS_CHOICES,
        default='pending'
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    delivery_fee = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    tax_amount = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    special_instructions = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    estimated_delivery_time = models.DateTimeField(null=True, blank=True)
    actual_delivery_time = models.DateTimeField(null=True, blank=True)
    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHOD_CHOICES
    )
    payment_status = models.CharField(
        max_length=20,
        choices=PAYMENT_STATUS_CHOICES,
        default='pending'
    )
    transaction_id = models.CharField(max_length=100, blank=True)
    assignment_attempts = models.PositiveIntegerField(default=0)
    last_assigned_at = models.DateTimeField(null=True, blank=True)

    # Delivery tracking fields
    accepted_at = models.DateTimeField(null=True, blank=True)
    picked_up_at = models.DateTimeField(null=True, blank=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    cash_collected_at = models.DateTimeField(null=True, blank=True)
    cash_amount_collected = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0)]
    )

    # Agent notes and feedback
    agent_notes = models.TextField(blank=True, help_text="Notes from delivery agent")
    customer_signature = models.TextField(blank=True, help_text="Digital signature or confirmation")
    delivery_photo = models.ImageField(upload_to='delivery_photos/', null=True, blank=True)

    # Rejection tracking
    rejection_reason = models.TextField(blank=True)
    rejected_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['customer']),
            models.Index(fields=['restaurant']),
            models.Index(fields=['delivery_agent']),
            models.Index(fields=['created_at']),
            models.Index(fields=['payment_status']),
        ]
        verbose_name = 'Order'
        verbose_name_plural = 'Orders'

    def __str__(self):
        return f"Order #{self.id} - {self.customer.name} ({self.status})"
    
    def assign_agent(self, agent, request=None):

        self.delivery_agent = agent
        self.status = 'assigned'
        self.assignment_attempts += 1
        self.last_assigned_at = timezone.now()
        
        if request:
            self._changed_by = request.user
            self._status_change_notes = f"Assigned to {agent.email}"
        
        self.save()
        
        # Add this line to log the assignment
        AssignmentLog.objects.create(
            order=self,
            agent=agent,
            action='assigned'
        )
        
    def unassign_agent(self, request=None):
        self.delivery_agent = None
        self.status = 'ready'
        if request:
            self._changed_by = request.user
            self._status_change_notes = "Agent rejected delivery"
        self.save()

    def accept_delivery(self, agent, request=None):
        """Agent accepts the delivery"""
        if self.delivery_agent != agent:
            raise ValueError("Only assigned agent can accept delivery")

        self.status = 'accepted'
        self.accepted_at = timezone.now()
        if request:
            self._changed_by = request.user
            self._status_change_notes = "Delivery accepted by agent"
        self.save()

    def reject_delivery(self, agent, reason="", request=None):
        """Agent rejects the delivery"""
        if self.delivery_agent != agent:
            raise ValueError("Only assigned agent can reject delivery")

        self.status = 'rejected'
        self.rejection_reason = reason
        self.rejected_at = timezone.now()
        self.delivery_agent = None  # Unassign agent
        if request:
            self._changed_by = request.user
            self._status_change_notes = f"Delivery rejected: {reason}"
        self.save()

    def update_delivery_status(self, new_status, agent, notes="", request=None):
        """Update delivery status with validation"""
        if self.delivery_agent != agent:
            raise ValueError("Only assigned agent can update delivery status")

        # Validate status transitions
        valid_transitions = {
            'accepted': ['en_route_to_restaurant'],
            'en_route_to_restaurant': ['arrived_at_restaurant'],
            'arrived_at_restaurant': ['picked_up'],
            'picked_up': ['en_route_to_customer'],
            'en_route_to_customer': ['arrived_at_customer'],
            'arrived_at_customer': ['delivered'],
            'delivered': ['cash_collected', 'completed'],
            'cash_collected': ['completed']
        }

        if self.status not in valid_transitions or new_status not in valid_transitions.get(self.status, []):
            raise ValueError(f"Invalid status transition from {self.status} to {new_status}")

        old_status = self.status
        self.status = new_status

        # Update timestamp fields
        if new_status == 'picked_up':
            self.picked_up_at = timezone.now()
        elif new_status == 'delivered':
            self.delivered_at = timezone.now()
        elif new_status == 'cash_collected':
            self.cash_collected_at = timezone.now()

        if notes:
            self.agent_notes = notes

        if request:
            self._changed_by = request.user
            self._status_change_notes = f"Status updated from {old_status} to {new_status}"

        self.save()

    def collect_cash(self, agent, amount, request=None):
        """Record cash collection"""
        if self.delivery_agent != agent:
            raise ValueError("Only assigned agent can collect cash")

        if self.payment_method != 'cash_on_delivery':
            raise ValueError("Cash collection only allowed for cash on delivery orders")

        self.cash_amount_collected = amount
        self.cash_collected_at = timezone.now()
        self.status = 'cash_collected'

        if request:
            self._changed_by = request.user
            self._status_change_notes = f"Cash collected: ${amount}"

        self.save()

    def save(self, *args, **kwargs):
        # Calculate totals if not set
        
        super().save(*args, **kwargs)


class OrderItem(models.Model):
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='items'
    )
    menu_item = models.ForeignKey(
        MenuItem,
        on_delete=models.PROTECT,
        related_name='order_items'
    )
    quantity = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1)]
    )
    price_at_order = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)]
    )
    special_requests = models.TextField(blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['order', 'menu_item'],
                name='unique_item_per_order'
            )
        ]
        verbose_name = 'Order Item'
        verbose_name_plural = 'Order Items'

    def __str__(self):
        return f"{self.menu_item.name} x {self.quantity} (Order #{self.order.id})"

    def save(self, *args, **kwargs):
        # Set price_at_order to current menu item price if not set
        if not self.price_at_order:
            self.price_at_order = self.menu_item.price
        super().save(*args, **kwargs)

    @property
    def total_price(self):
        return self.price_at_order * self.quantity
    

from django.conf import settings

class SavedCart(models.Model):
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='saved_cart'
    )
    restaurant = models.ForeignKey(
        'restaurant.Restaurant',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Cart #{self.id} for {self.user.email}"

class SavedCartItem(models.Model):
    cart = models.ForeignKey(
        SavedCart,
        on_delete=models.CASCADE,
        related_name='items'
    )
    menu_item = models.ForeignKey(
        'restaurant.MenuItem',
        on_delete=models.CASCADE
    )
    quantity = models.PositiveIntegerField(default=1)
    special_requests = models.TextField(blank=True)

    class Meta:
        unique_together = ('cart', 'menu_item')



class OrderStatusHistory(models.Model):
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='status_history'
    )
    from_status = models.CharField(
        max_length=30,
        choices=Order.ORDER_STATUS_CHOICES
    )
    to_status = models.CharField(
        max_length=30,
        choices=Order.ORDER_STATUS_CHOICES
    )
    changed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name_plural = 'Order Status Histories'

    def __str__(self):
        return f"Order #{self.order.id}: {self.from_status} → {self.to_status}"
    

class RejectionLog(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    agent = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True)
    reason = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']


class AssignmentLog(models.Model):
    ACTION_CHOICES = [
        ('assigned', 'Assigned'),
        ('rejected', 'Rejected')  # Keeps existing rejection logging
    ]

    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='assignment_logs'
    )
    agent = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)
    

    class Meta:
        ordering = ['-created_at']


class Rating(models.Model):
    """Model for order and restaurant ratings"""

    order = models.OneToOneField(
        Order,
        on_delete=models.CASCADE,
        related_name='rating'
    )
    customer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='ratings_given',
        limit_choices_to={'role': 'customer'}
    )
    restaurant = models.ForeignKey(
        Restaurant,
        on_delete=models.CASCADE,
        related_name='ratings'
    )

    # Rating fields
    food_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Rating for food quality (1-5 stars)"
    )
    delivery_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Rating for delivery service (1-5 stars)"
    )
    overall_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Overall rating (1-5 stars)"
    )

    # Review text
    review_text = models.TextField(
        max_length=1000,
        blank=True,
        null=True,
        help_text="Optional review text"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        unique_together = ['order', 'customer']  # One rating per order per customer

    def __str__(self):
        return f"Rating for Order #{self.order.id} - {self.overall_rating}/5 stars"

    @property
    def average_rating(self):
        """Calculate average of all rating components"""
        return round((self.food_rating + self.delivery_rating + self.overall_rating) / 3, 1)