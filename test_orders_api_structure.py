#!/usr/bin/env python3
"""
Test the orders API response structure
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_orders_api_structure():
    """Test the orders API response structure"""
    print("🧪 Testing Orders API Response Structure")
    print("=" * 50)
    
    # Login as employee
    emp_login = {"user_name": "EMP001", "password": "employee123"}
    response = requests.post(f"{BASE_URL}/auth/login/", json=emp_login)
    
    if response.status_code == 200:
        emp_token = response.json()['data']['access_token']
        print("✅ Employee login successful")
    else:
        print("❌ Employee login failed")
        return
    
    emp_headers = {"Authorization": f"Bearer {emp_token}"}
    
    # Test my orders endpoint
    print("\n📋 Testing My Orders Endpoint")
    response = requests.get(f"{BASE_URL}/delivery-agent/my-orders/", headers=emp_headers)
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print("✅ My Orders API Response Structure:")
            print(json.dumps(data, indent=2))
            
            # Check the structure
            if 'data' in data:
                orders_data = data['data']
                print(f"\n🔍 Data Structure Analysis:")
                print(f"   Top level keys: {list(data.keys())}")
                print(f"   Data keys: {list(orders_data.keys())}")
                
                if 'orders' in orders_data:
                    orders = orders_data['orders']
                    print(f"   Orders count: {len(orders)}")
                    print(f"   Orders type: {type(orders)}")
                    
                    if orders and len(orders) > 0:
                        print(f"   First order keys: {list(orders[0].keys())}")
                        print(f"   Sample order structure:")
                        print(json.dumps(orders[0], indent=4))
                else:
                    print("   ❌ No 'orders' key found in data")
            else:
                print("   ❌ No 'data' key found in response")
                
        except json.JSONDecodeError:
            print("❌ Invalid JSON response")
            print(f"Raw response: {response.text}")
    else:
        print(f"❌ My Orders API failed: {response.status_code}")
        print(f"Response: {response.text}")
    
    # Test available orders endpoint
    print(f"\n📋 Testing Available Orders Endpoint")
    response = requests.get(f"{BASE_URL}/delivery-agent/available-orders/", headers=emp_headers)
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print("✅ Available Orders API Response Structure:")
            print(json.dumps(data, indent=2))
        except json.JSONDecodeError:
            print("❌ Invalid JSON response")
            print(f"Raw response: {response.text}")
    else:
        print(f"❌ Available Orders API failed: {response.status_code}")
        print(f"Response: {response.text}")

def main():
    """Main test function"""
    print("🧪 Orders API Structure Test")
    print("Testing the actual API response structure to fix frontend issues")
    
    test_orders_api_structure()
    
    print("\n" + "=" * 50)
    print("✅ API Structure Test Complete!")
    print("\n📋 Use this information to:")
    print("   1. Fix the frontend data extraction")
    print("   2. Update the filter functions")
    print("   3. Ensure proper array handling")
    print("   4. Match field names correctly")

if __name__ == "__main__":
    main()
