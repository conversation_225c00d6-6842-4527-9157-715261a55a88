import React, { useState } from 'react';
import { Calendar, Clock, Repeat, Trash2, Edit3, AlertCircle } from 'lucide-react';
import { useScheduling } from '../../context/SchedulingContext';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Badge from '../../components/common/Badge';

const ScheduledOrders = () => {
  const { 
    scheduledOrders, 
    cancelScheduledOrder, 
    getUpcomingOrders,
    getTodaysScheduledOrders 
  } = useScheduling();
  
  const [activeTab, setActiveTab] = useState('upcoming');
  const [showCancelModal, setShowCancelModal] = useState(null);

  const upcomingOrders = getUpcomingOrders();
  const todaysOrders = getTodaysScheduledOrders();
  const pastOrders = scheduledOrders.filter(order => 
    new Date(order.scheduledFor) < new Date() || order.status === 'completed'
  );

  const getDisplayOrders = () => {
    switch (activeTab) {
      case 'today':
        return todaysOrders;
      case 'upcoming':
        return upcomingOrders;
      case 'past':
        return pastOrders;
      default:
        return upcomingOrders;
    }
  };

  const handleCancelOrder = (orderId) => {
    cancelScheduledOrder(orderId);
    setShowCancelModal(null);
  };

  const formatDateTime = (dateTime) => {
    const date = new Date(dateTime);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    let dateStr;
    if (diffDays === 0) {
      dateStr = 'Today';
    } else if (diffDays === 1) {
      dateStr = 'Tomorrow';
    } else if (diffDays < 7) {
      dateStr = date.toLocaleDateString('en-US', { weekday: 'long' });
    } else {
      dateStr = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    }

    const timeStr = date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });

    return `${dateStr} at ${timeStr}`;
  };

  const getStatusBadge = (order) => {
    const now = new Date();
    const orderTime = new Date(order.scheduledFor);
    
    if (order.status === 'completed') {
      return <Badge variant="success">Completed</Badge>;
    } else if (order.status === 'cancelled') {
      return <Badge variant="danger">Cancelled</Badge>;
    } else if (orderTime < now) {
      return <Badge variant="warning">Overdue</Badge>;
    } else {
      return <Badge variant="info">Scheduled</Badge>;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 animate-fade-in">
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">Scheduled Orders</h1>
        <p className="text-gray-600">Manage your upcoming and past scheduled orders</p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6 w-fit">
        <button
          onClick={() => setActiveTab('today')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'today'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Today ({todaysOrders.length})
        </button>
        <button
          onClick={() => setActiveTab('upcoming')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'upcoming'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Upcoming ({upcomingOrders.length})
        </button>
        <button
          onClick={() => setActiveTab('past')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'past'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Past ({pastOrders.length})
        </button>
      </div>

      {/* Orders List */}
      <div className="space-y-4">
        {getDisplayOrders().length === 0 ? (
          <div className="text-center py-16">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Calendar size={40} className="text-gray-400" />
            </div>
            <h3 className="text-xl font-medium mb-2">No Scheduled Orders</h3>
            <p className="text-gray-600 mb-6">
              {activeTab === 'today' 
                ? "You don't have any orders scheduled for today."
                : activeTab === 'upcoming'
                ? "You don't have any upcoming scheduled orders."
                : "You don't have any past scheduled orders."
              }
            </p>
            <Button variant="primary" to="/restaurants">
              Browse Restaurants
            </Button>
          </div>
        ) : (
          getDisplayOrders().map((order) => (
            <Card key={order.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <h3 className="font-semibold text-lg mr-3">{order.restaurantName}</h3>
                    {getStatusBadge(order)}
                    {order.recurring && (
                      <Badge variant="secondary" className="ml-2">
                        <Repeat size={12} className="mr-1" />
                        Recurring
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center text-gray-600 mb-3">
                    <Calendar size={16} className="mr-2" />
                    <span>{formatDateTime(order.scheduledFor)}</span>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-medium mb-2">Order Items:</h4>
                    <div className="space-y-1">
                      {order.items?.map((item, index) => (
                        <div key={index} className="flex justify-between text-sm">
                          <span>{item.quantity}x {item.name}</span>
                          <span>${(item.price * item.quantity).toFixed(2)}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="text-lg font-semibold">
                      Total: ${order.total?.toFixed(2)}
                    </div>
                    
                    {activeTab !== 'past' && (
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="small"
                          icon={<Edit3 size={14} />}
                        >
                          Edit
                        </Button>
                        <Button
                          variant="outline"
                          size="small"
                          icon={<Trash2 size={14} />}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                          onClick={() => setShowCancelModal(order.id)}
                        >
                          Cancel
                        </Button>
                      </div>
                    )}
                  </div>

                  {order.recurring && (
                    <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center text-blue-700 text-sm">
                        <Repeat size={14} className="mr-2" />
                        <span>
                          Repeats {order.recurring.frequency}
                          {order.recurring.endDate && 
                            ` until ${new Date(order.recurring.endDate).toLocaleDateString()}`
                          }
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          ))
        )}
      </div>

      {/* Cancel Confirmation Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md">
            <div className="flex items-center mb-4">
              <AlertCircle className="text-red-500 mr-3" size={24} />
              <h3 className="text-lg font-semibold">Cancel Scheduled Order</h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              Are you sure you want to cancel this scheduled order? This action cannot be undone.
            </p>
            
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowCancelModal(null)}
                className="flex-1"
              >
                Keep Order
              </Button>
              <Button
                variant="primary"
                onClick={() => handleCancelOrder(showCancelModal)}
                className="flex-1 bg-red-600 hover:bg-red-700"
              >
                Cancel Order
              </Button>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default ScheduledOrders;
