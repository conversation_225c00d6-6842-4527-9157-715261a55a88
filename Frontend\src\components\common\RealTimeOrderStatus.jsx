import React, { useState, useEffect } from "react";
import {
  Clock,
  CheckCircle,
  Package,
  TruckIcon,
  Home,
  ChefHat,
  AlertCircle,
  MapPin,
  Phone,
  MessageCircle,
} from "lucide-react";
import { useNotifications } from "../../context/NotificationContext";
import Card from "./Card";
import Button from "./Button";
import Badge from "./Badge";
import { useOrderStatuses } from "../../hooks/useConfig";

const RealTimeOrderStatus = ({ orderId, className = "" }) => {
  const { getOrderTracking, trackedOrders, isConnected } = useNotifications();
  const [orderTracking, setOrderTracking] = useState(null);
  const { options: orderStatuses, loading: statusesLoading } =
    useOrderStatuses();
  const [currentStep, setCurrentStep] = useState(0);

  // Transform dynamic order statuses into status steps
  const getStatusSteps = () => {
    if (statusesLoading || !orderStatuses.length) {
      // Fallback to static steps if dynamic data is not available
      return [
        {
          key: "pending",
          label: "Order Placed",
          description: "Your order has been received",
          icon: <Package size={20} />,
          color: "text-gray-500",
          bgColor: "bg-gray-100",
        },
        {
          key: "confirmed",
          label: "Order Confirmed",
          description: "Restaurant has confirmed your order",
          icon: <CheckCircle size={20} />,
          color: "text-blue-500",
          bgColor: "bg-blue-100",
        },
        {
          key: "preparing",
          label: "Preparing",
          description: "Your food is being prepared",
          icon: <ChefHat size={20} />,
          color: "text-orange-500",
          bgColor: "bg-orange-100",
        },
        {
          key: "ready",
          label: "Ready for Pickup",
          description: "Food is ready, waiting for delivery agent",
          icon: <AlertCircle size={20} />,
          color: "text-yellow-500",
          bgColor: "bg-yellow-100",
        },
        {
          key: "on_the_way",
          label: "Out for Delivery",
          description: "Your order is on the way",
          icon: <TruckIcon size={20} />,
          color: "text-purple-500",
          bgColor: "bg-purple-100",
        },
        {
          key: "delivered",
          label: "Delivered",
          description: "Order has been delivered",
          icon: <Home size={20} />,
          color: "text-green-500",
          bgColor: "bg-green-100",
        },
      ];
    }

    // Map dynamic order statuses to status steps
    return orderStatuses.map((status) => {
      // Get appropriate icon based on status value
      let icon = <Package size={20} />;
      let description = `Order is ${status.label.toLowerCase()}`;

      switch (status.value) {
        case "pending":
          icon = <Package size={20} />;
          description = "Your order has been received";
          break;
        case "confirmed":
          icon = <CheckCircle size={20} />;
          description = "Restaurant has confirmed your order";
          break;
        case "preparing":
          icon = <ChefHat size={20} />;
          description = "Your food is being prepared";
          break;
        case "ready":
          icon = <AlertCircle size={20} />;
          description = "Food is ready, waiting for delivery agent";
          break;
        case "assigned":
          icon = <TruckIcon size={20} />;
          description = "Delivery agent has been assigned";
          break;
        case "picked_up":
          icon = <TruckIcon size={20} />;
          description = "Order has been picked up";
          break;
        case "on_the_way":
          icon = <TruckIcon size={20} />;
          description = "Your order is on the way";
          break;
        case "delivered":
          icon = <Home size={20} />;
          description = "Order has been delivered";
          break;
        case "cancelled":
          icon = <AlertCircle size={20} />;
          description = "Order has been cancelled";
          break;
        default:
          break;
      }

      return {
        key: status.value,
        label: status.label,
        description,
        icon,
        color: status.color ? `text-[${status.color}]` : "text-gray-500",
        bgColor: status.color ? `bg-[${status.color}]/10` : "bg-gray-100",
      };
    });
  };

  const statusSteps = getStatusSteps();

  useEffect(() => {
    const tracking = getOrderTracking(orderId);
    setOrderTracking(tracking);

    if (tracking) {
      const stepIndex = statusSteps.findIndex(
        (step) => step.key === tracking.status
      );
      setCurrentStep(stepIndex >= 0 ? stepIndex : 0);
    }
  }, [orderId, trackedOrders, getOrderTracking]);

  const formatTime = (date) => {
    if (!date) return "";
    return new Date(date).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getEstimatedDeliveryTime = () => {
    if (!orderTracking?.estimatedArrival) return "Calculating...";

    const now = new Date();
    const estimated = new Date(orderTracking.estimatedArrival);
    const diffMinutes = Math.ceil((estimated - now) / (1000 * 60));

    if (diffMinutes <= 0) return "Arriving now";
    if (diffMinutes < 60) return `${diffMinutes} min`;

    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    return `${hours}h ${minutes}m`;
  };

  if (!orderTracking) {
    return (
      <Card className={className}>
        <div className='p-6 text-center'>
          <AlertCircle size={48} className='mx-auto text-gray-300 mb-4' />
          <p className='text-gray-500'>Order tracking not available</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <div className='p-6'>
        {/* Header */}
        <div className='flex items-center justify-between mb-6'>
          <div>
            <h3 className='text-lg font-semibold'>Order Status</h3>
            <p className='text-sm text-gray-500'>
              Order #{orderId.split("-")[1]}
            </p>
          </div>
          <div className='flex items-center space-x-2'>
            <div
              className={`w-3 h-3 rounded-full ${
                isConnected ? "bg-green-500" : "bg-red-500"
              }`}
            />
            <span className='text-sm text-gray-500'>
              {isConnected ? "Live" : "Offline"}
            </span>
          </div>
        </div>

        {/* Estimated Delivery Time */}
        {orderTracking.status !== "delivered" && (
          <div className='bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4 mb-6'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-orange-800'>
                  Estimated Delivery
                </p>
                <p className='text-lg font-bold text-orange-900'>
                  {getEstimatedDeliveryTime()}
                </p>
              </div>
              <Clock size={24} className='text-orange-600' />
            </div>
          </div>
        )}

        {/* Status Timeline */}
        <div className='space-y-4'>
          {statusSteps.map((step, index) => {
            const isCompleted = index <= currentStep;
            const isCurrent = index === currentStep;
            const isActive = isCompleted || isCurrent;

            return (
              <div key={step.key} className='flex items-start space-x-4'>
                {/* Status Icon */}
                <div
                  className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ${
                    isCompleted
                      ? `${step.bgColor} ${step.color}`
                      : isCurrent
                      ? `${step.bgColor} ${step.color} ring-2 ring-offset-2 ring-orange-500`
                      : "bg-gray-100 text-gray-400"
                  }`}
                >
                  {step.icon}
                </div>

                {/* Status Content */}
                <div className='flex-grow min-w-0'>
                  <div className='flex items-center justify-between'>
                    <h4
                      className={`font-medium ${
                        isActive ? "text-gray-900" : "text-gray-400"
                      }`}
                    >
                      {step.label}
                    </h4>
                    {isCompleted && (
                      <span className='text-xs text-gray-500'>
                        {formatTime(
                          orderTracking.updates.find(
                            (update) => update.status === step.key
                          )?.timestamp
                        )}
                      </span>
                    )}
                  </div>
                  <p
                    className={`text-sm ${
                      isActive ? "text-gray-600" : "text-gray-400"
                    }`}
                  >
                    {step.description}
                  </p>

                  {/* Live indicator for current step */}
                  {isCurrent && isConnected && (
                    <div className='flex items-center mt-2'>
                      <div className='w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse' />
                      <span className='text-xs text-green-600 font-medium'>
                        Live updates
                      </span>
                    </div>
                  )}
                </div>

                {/* Connecting Line */}
                {index < statusSteps.length - 1 && (
                  <div
                    className={`absolute left-5 mt-10 w-0.5 h-8 ${
                      index < currentStep ? "bg-orange-300" : "bg-gray-200"
                    } transition-colors duration-300`}
                    style={{ marginLeft: "20px" }}
                  />
                )}
              </div>
            );
          })}
        </div>

        {/* Delivery Agent Info (when out for delivery) */}
        {orderTracking.status === "outForDelivery" && (
          <div className='mt-6 pt-6 border-t border-gray-100'>
            <h4 className='font-medium mb-3'>Delivery Agent</h4>
            <div className='bg-gray-50 rounded-lg p-4'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center space-x-3'>
                  <div className='w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center'>
                    <TruckIcon size={20} className='text-white' />
                  </div>
                  <div>
                    <p className='font-medium'>Mohammad Ahmad</p>
                    <div className='flex items-center text-sm text-gray-500'>
                      <MapPin size={14} className='mr-1' />
                      <span>2.3 km away</span>
                    </div>
                  </div>
                </div>
                <div className='flex space-x-2'>
                  <Button
                    variant='outline'
                    size='small'
                    icon={<Phone size={16} />}
                  >
                    Call
                  </Button>
                  <Button
                    variant='outline'
                    size='small'
                    icon={<MessageCircle size={16} />}
                  >
                    Chat
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default RealTimeOrderStatus;
