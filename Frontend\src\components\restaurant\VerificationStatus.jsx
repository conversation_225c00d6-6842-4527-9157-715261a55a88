import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Shield, <PERSON><PERSON>he<PERSON>, Clock, Lock } from 'lucide-react';
import Card from '../common/Card';

const VerificationStatus = ({ 
  restaurant, 
  user, 
  showForUnverified = true, 
  showForVerified = true,
  variant = 'card' // 'card' or 'banner'
}) => {
  // Don't show anything if user is not a restaurant owner
  if (!user || user.role !== 'restaurant') {
    return null;
  }

  // Don't show if restaurant data is not available
  if (!restaurant) {
    return null;
  }

  const isVerified = restaurant.is_verified;

  // Pending verification component
  const PendingVerification = () => (
    <div className={variant === 'banner' ? 
      'bg-yellow-50 border border-yellow-200 rounded-lg p-4' : 
      'border-l-4 border-yellow-500'
    }>
      <div className='flex items-start space-x-3'>
        <AlertCircle className='text-yellow-600 mt-0.5' size={20} />
        <div className='flex-1'>
          <h3 className='text-sm font-medium text-yellow-800 mb-1'>
            Restaurant Verification Pending
          </h3>
          <p className='text-sm text-yellow-700 mb-3'>
            Your restaurant is currently under review by our admin team. You cannot add menu categories or items until your restaurant is verified.
          </p>
          <div className='flex items-center space-x-4 text-xs text-yellow-600'>
            <div className='flex items-center space-x-1'>
              <Shield size={14} />
              <span>Verification Required</span>
            </div>
            <div className='flex items-center space-x-1'>
              <Clock size={14} />
              <span>Usually takes 1-2 business days</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Verified status component
  const VerifiedStatus = () => (
    <div className={variant === 'banner' ? 
      'bg-green-50 border border-green-200 rounded-lg p-4' : 
      'border-l-4 border-green-500'
    }>
      <div className='flex items-center space-x-3'>
        <ShieldCheck className='text-green-600' size={20} />
        <div className='flex-1'>
          <h3 className='text-sm font-medium text-green-800'>
            Restaurant Verified ✅
          </h3>
          <p className='text-sm text-green-700'>
            Your restaurant has been verified! You can now manage your menu categories and items.
          </p>
        </div>
      </div>
    </div>
  );

  // Menu management restriction component
  const MenuRestriction = () => (
    <div className={variant === 'banner' ? 
      'bg-yellow-50 border border-yellow-200 rounded-lg p-4' : 
      'border-l-4 border-yellow-500'
    }>
      <div className='flex items-start space-x-3'>
        <Lock className='text-yellow-500 mt-0.5' size={18} />
        <div className='flex-1'>
          <h3 className='text-sm font-medium text-yellow-800 mb-1'>
            Restaurant Verification Required
          </h3>
          <p className='text-yellow-700 text-sm mb-2'>
            Your restaurant must be verified by admin before you can manage menu items. 
            Menu management is currently disabled.
          </p>
          <div className='flex items-center space-x-4 text-xs text-yellow-600'>
            <div className='flex items-center space-x-1'>
              <Shield size={12} />
              <span>Pending Admin Approval</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // Render based on verification status and props
  if (!isVerified && showForUnverified) {
    if (variant === 'card') {
      return (
        <Card className='mb-6 border-l-4 border-yellow-500'>
          <div className='p-4'>
            <MenuRestriction />
          </div>
        </Card>
      );
    }
    return <PendingVerification />;
  }

  if (isVerified && showForVerified) {
    if (variant === 'card') {
      return (
        <Card className='mb-6 border-l-4 border-green-500'>
          <div className='p-4'>
            <VerifiedStatus />
          </div>
        </Card>
      );
    }
    return <VerifiedStatus />;
  }

  return null;
};

export default VerificationStatus;

// Export individual components for more granular use
export { VerificationStatus };

// Utility function to check if restaurant can manage menus
export const canManageMenus = (user, restaurant) => {
  return user?.role === 'restaurant' && restaurant?.is_verified;
};

// Utility function to get verification status text
export const getVerificationStatusText = (restaurant) => {
  if (!restaurant) return 'Unknown';
  return restaurant.is_verified ? 'Verified' : 'Pending Verification';
};

// Utility function to get verification status color
export const getVerificationStatusColor = (restaurant) => {
  if (!restaurant) return 'gray';
  return restaurant.is_verified ? 'green' : 'yellow';
};
