import React, { useState, useEffect } from 'react';
import { 
  Power, 
  PowerOff, 
  Clock, 
  DollarSign, 
  Package, 
  Star,
  TrendingUp,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Timer,
  Navigation
} from 'lucide-react';
import toast from 'react-hot-toast';

const DeliveryDashboardDebug = () => {
  const [loading, setLoading] = useState(false);
  const [dashboardData, setDashboardData] = useState({
    agent_info: {
      full_name: 'Test Delivery Agent',
      agent_id: 'DA007',
      is_online: false,
      is_clocked_in: false,
      availability: 'offline',
      rating: 4.8
    },
    today_stats: {
      deliveries_completed: 12,
      earnings: 450.00,
      completion_rate: 95
    }
  });
  const [isOnline, setIsOnline] = useState(false);
  const [isClockedIn, setIsClockedIn] = useState(false);
  const [availability, setAvailability] = useState('offline');
  const [assignedOrders, setAssignedOrders] = useState([
    {
      id: 'ORD001',
      status: 'assigned',
      restaurant_name: 'Test Restaurant',
      customer_name: '<PERSON>',
      total_amount: 25.50,
      delivery_address: { street: '123 Test Street' },
      created_at: new Date().toISOString(),
      payment_method: 'cash'
    },
    {
      id: 'ORD002',
      status: 'picked_up',
      restaurant_name: 'Pizza Palace',
      customer_name: 'Jane Smith',
      total_amount: 32.75,
      delivery_address: { street: '456 Main Avenue' },
      created_at: new Date().toISOString(),
      payment_method: 'card'
    }
  ]);
  const [refreshing, setRefreshing] = useState(false);

  // Mock functions for testing
  const toggleOnlineStatus = async () => {
    setIsOnline(!isOnline);
    setAvailability(isOnline ? 'offline' : 'available');
    toast.success(`You are now ${!isOnline ? 'online' : 'offline'}`);
  };

  const toggleClockStatus = async () => {
    setIsClockedIn(!isClockedIn);
    toast.success(isClockedIn ? 'Clocked out successfully' : 'Clocked in successfully');
  };

  const updateAvailability = async (newAvailability) => {
    setAvailability(newAvailability);
    setIsOnline(newAvailability !== 'offline');
    toast.success('Availability updated successfully');
  };

  const agentInfo = dashboardData?.agent_info || {};
  const todayStats = dashboardData?.today_stats || {};

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="mb-4 md:mb-0">
              <h1 className="text-2xl font-bold text-gray-900">
                Welcome, {agentInfo.full_name}
              </h1>
              <p className="text-gray-600">Agent ID: {agentInfo.agent_id}</p>
              <p className="text-sm text-orange-600 font-medium">DEBUG MODE - No API calls</p>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setRefreshing(true)}
                disabled={refreshing}
                className="flex items-center px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                isOnline 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {isOnline ? 'Online' : 'Offline'}
              </div>
            </div>
          </div>
        </div>

        {/* Status Controls */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {/* Online/Offline Toggle */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Online Status</h3>
            <button
              onClick={toggleOnlineStatus}
              className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-colors ${
                isOnline
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-green-600 hover:bg-green-700 text-white'
              }`}
            >
              {isOnline ? (
                <>
                  <PowerOff className="h-5 w-5 mr-2" />
                  Go Offline
                </>
              ) : (
                <>
                  <Power className="h-5 w-5 mr-2" />
                  Go Online
                </>
              )}
            </button>
          </div>

          {/* Clock In/Out */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Work Shift</h3>
            <button
              onClick={toggleClockStatus}
              className={`w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-colors ${
                isClockedIn
                  ? 'bg-orange-600 hover:bg-orange-700 text-white'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              <Clock className="h-5 w-5 mr-2" />
              {isClockedIn ? 'Clock Out' : 'Clock In'}
            </button>
          </div>

          {/* Availability Status */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4">Availability</h3>
            <select
              value={availability}
              onChange={(e) => updateAvailability(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="offline">Offline</option>
              <option value="available">Available</option>
              <option value="busy">Busy</option>
              <option value="break">On Break</option>
            </select>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Today's Deliveries</p>
                <p className="text-2xl font-bold text-gray-900">
                  {todayStats.deliveries_completed || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Today's Earnings</p>
                <p className="text-2xl font-bold text-gray-900">
                  AFN {todayStats.earnings || '0.00'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Rating</p>
                <p className="text-2xl font-bold text-gray-900">
                  {agentInfo.rating || '0.0'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {todayStats.completion_rate || 0}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Assigned Orders */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">Assigned Orders</h2>
            <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-0.5 rounded">
              {assignedOrders.length} orders
            </span>
          </div>

          <div className="space-y-4">
            {assignedOrders.map((order) => (
              <div key={order.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold text-gray-900">Order #{order.id}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        order.status === 'assigned' ? 'bg-blue-100 text-blue-800' :
                        order.status === 'picked_up' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {order.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">
                      📍 {order.delivery_address?.street || 'Address not available'}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">AFN {order.total_amount}</p>
                    <p className="text-sm text-gray-600">
                      <Timer className="h-4 w-4 inline mr-1" />
                      {new Date(order.created_at).toLocaleTimeString()}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="text-sm font-medium text-gray-700">Restaurant</p>
                    <p className="text-sm text-gray-600">{order.restaurant_name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-700">Customer</p>
                    <p className="text-sm text-gray-600">{order.customer_name}</p>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => toast.success('Order status updated (demo)')}
                    className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Update Status
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeliveryDashboardDebug;
