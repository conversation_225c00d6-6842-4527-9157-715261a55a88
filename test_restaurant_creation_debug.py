#!/usr/bin/env python3
"""
Debug restaurant creation issues
"""

import requests
import json
import time

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"

def test_restaurant_creation_debug():
    """Debug restaurant creation with detailed error reporting"""
    
    print("🔍 Debugging Restaurant Creation")
    print("=" * 50)
    
    # Login with existing user
    login_data = {
        "user_name": "testrestaurant",
        "password": "password123"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print("🔐 Logging in...")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=headers,
            data=json.dumps(login_data)
        )
        
        if response.status_code != 200:
            print(f"❌ Login failed: {response.text}")
            return False
        
        result = response.json()
        token = result['data']['access_token']
        print("✅ Login successful!")
        
        # Test restaurant creation with minimal data first
        auth_headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        timestamp = int(time.time())
        
        # Test 1: Minimal JSON data with required fields
        print(f"\n🧪 Test 1: Minimal JSON data with required fields")
        minimal_data = {
            "name": f"Debug Restaurant {timestamp}",
            "description": "A test restaurant for debugging",
            "contact_number": "+93701234567",
            "opening_time": "08:00:00",
            "closing_time": "22:00:00",
            "delivery_fee": "50.00",
            "address": {
                "street": "Test Street",
                "city": "Kabul",
                "state": "Kabul",
                "postal_code": "1001",
                "country": "Afghanistan",
                "latitude": 34.5553,
                "longitude": 69.2075
            }
        }
        
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            data=json.dumps(minimal_data)
        )
        
        print(f"   📡 Status: {response.status_code}")
        print(f"   📄 Response: {response.text}")
        
        if response.status_code == 201:
            print("   ✅ Minimal JSON data works!")
        else:
            print("   ❌ Minimal JSON data failed")
            try:
                error_data = response.json()
                print(f"   🔍 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   🔍 Raw Error: {response.text}")
        
        # Test 2: Multipart form data (like frontend) with required fields
        print(f"\n🧪 Test 2: Multipart form data with required fields")

        form_data = {
            "name": f"Debug Restaurant Form {timestamp}",
            "description": "A test restaurant for debugging multipart",
            "contact_number": "+93701234568",
            "opening_time": "09:00:00",
            "closing_time": "21:00:00",
            "delivery_fee": "60.00",
            "address": json.dumps({
                "street": "Test Street Form",
                "city": "Kabul",
                "state": "Kabul",
                "postal_code": "1002",
                "country": "Afghanistan",
                "latitude": 34.5553,
                "longitude": 69.2075
            })
        }
        
        # Remove Content-Type to let requests set it for multipart
        multipart_headers = {
            "Authorization": f"Bearer {token}"
        }
        
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=multipart_headers,
            files={'dummy': ('', '', 'application/octet-stream')},  # Force multipart
            data=form_data
        )
        
        print(f"   📡 Status: {response.status_code}")
        print(f"   📄 Response: {response.text}")
        
        if response.status_code == 201:
            print("   ✅ Multipart form data works!")
        else:
            print("   ❌ Multipart form data failed")
            try:
                error_data = response.json()
                print(f"   🔍 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   🔍 Raw Error: {response.text}")
        
        # Test 3: Enhanced data with all fields
        print(f"\n🧪 Test 3: Enhanced data with all fields")
        
        enhanced_data = {
            "name": f"Debug Enhanced Restaurant {timestamp}",
            "description": "A comprehensive test restaurant with all enhanced features",
            "contact_number": "+93701234569",
            "opening_time": "08:00:00",
            "closing_time": "23:30:00",
            "delivery_fee": "85.00",
            "minimum_order": "350",
            "average_preparation_time": "45",
            "accepts_cash": "true",
            "accepts_card": "true",
            "accepts_online_payment": "true",
            "website": f"https://debugrestaurant{timestamp}.com",
            "facebook_url": f"https://facebook.com/debugrestaurant{timestamp}",
            "instagram_url": f"https://instagram.com/debugrestaurant{timestamp}",
            "twitter_url": f"https://twitter.com/debugrestaurant{timestamp}",
            "address": json.dumps({
                "street": f"Debug Street {timestamp}",
                "city": "Kabul",
                "state": "Kabul",
                "postal_code": f"{timestamp % 10000}",
                "country": "Afghanistan",
                "latitude": 34.5553,
                "longitude": 69.2075
            })
        }
        
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=multipart_headers,
            files={'dummy': ('', '', 'application/octet-stream')},
            data=enhanced_data
        )
        
        print(f"   📡 Status: {response.status_code}")
        print(f"   📄 Response: {response.text}")
        
        if response.status_code == 201:
            print("   ✅ Enhanced data works!")
            return True
        else:
            print("   ❌ Enhanced data failed")
            try:
                error_data = response.json()
                print(f"   🔍 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"   🔍 Raw Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main debug function"""
    success = test_restaurant_creation_debug()
    
    print("\n" + "=" * 50)
    print("🏁 DEBUG RESULTS")
    print("=" * 50)
    
    if success:
        print("🎉 Restaurant creation is working!")
    else:
        print("❌ Restaurant creation has issues")
        print("🔍 Check the error details above")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
