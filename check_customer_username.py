#!/usr/bin/env python3
"""
Check the actual username for the test customer
"""

import os
import sys
import django

# Add the Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

def check_customer_username():
    """Check the actual username for test customer"""
    
    print("🔍 Checking Customer Username")
    print("=" * 40)
    
    try:
        test_customer = User.objects.get(email='<EMAIL>')
        print(f"✅ Found test customer:")
        print(f"   Email: {test_customer.email}")
        print(f"   Username: {test_customer.user_name}")
        print(f"   Name: {test_customer.name}")
        print(f"   Role: {test_customer.role}")
        print(f"   Phone: {test_customer.phone}")
        
        print(f"\n🔑 Login Credentials:")
        print(f"   user_name: {test_customer.user_name}")
        print(f"   password: testpass123")
        
    except User.DoesNotExist:
        print("❌ Test customer not found")
        
        # Show all customers
        customers = User.objects.filter(role='customer')
        print(f"\n👥 Available customers ({customers.count()}):")
        for customer in customers:
            print(f"   Email: {customer.email}")
            print(f"   Username: {customer.user_name}")
            print(f"   Name: {customer.name}")
            print(f"   ---")

if __name__ == '__main__':
    check_customer_username()
