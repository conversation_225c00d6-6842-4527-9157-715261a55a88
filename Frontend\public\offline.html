<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Afghan Sofra</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .offline-container {
            text-align: center;
            padding: 2rem;
            max-width: 400px;
        }
        
        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.5;
        }
        
        .retry-button {
            background: white;
            color: #f97316;
            border: none;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .retry-button:hover {
            transform: translateY(-2px);
        }
        
        .features-list {
            margin-top: 2rem;
            text-align: left;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            opacity: 0.8;
        }
        
        .feature-icon {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📱</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            No internet connection found. Check your connection and try again.
        </p>
        
        <button class="retry-button" onclick="window.location.reload()">
            Try Again
        </button>
        
        <div class="features-list">
            <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span>Your cart is saved locally</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span>Favorites are still accessible</span>
            </div>
            <div class="feature-item">
                <span class="feature-icon">✓</span>
                <span>Order history is cached</span>
            </div>
        </div>
    </div>

    <script>
        // Check for connection and auto-reload when back online
        window.addEventListener('online', () => {
            window.location.reload();
        });
        
        // Show connection status
        if (navigator.onLine) {
            window.location.reload();
        }
    </script>
</body>
</html>
