import React, { useState, useEffect } from "react";
import {
  CheckCircle,
  XCircle,
  Clock,
  User,
  Phone,
  Mail,
  MapPin,
  Truck,
  Calendar,
  Star,
  Package,
  RefreshCw,
  Eye,
  Filter,
  Search,
  AlertCircle,
  Shield,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { adminApi } from "../../services/adminApi";
import { useAuth } from "../../context/AuthContext";

function DeliveryAgentApprovals() {
  const { user } = useAuth();
  const [agents, setAgents] = useState([]);
  const [filteredAgents, setFilteredAgents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("pending");
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [processing, setProcessing] = useState(false);

  // Load delivery agents
  const loadAgents = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await adminApi.getDeliveryAgents();

      if (result.success) {
        setAgents(result.data.data || []);
      } else {
        setError(result.error?.message || "Failed to load delivery agents");
      }
    } catch (err) {
      console.error("Load agents error:", err);
      setError("Failed to load delivery agents");
    } finally {
      setLoading(false);
    }
  };

  // Filter agents based on search and status
  useEffect(() => {
    let filtered = agents;

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((agent) => agent.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (agent) =>
          agent.user.name.toLowerCase().includes(query) ||
          agent.user.email.toLowerCase().includes(query) ||
          agent.agent_id.toLowerCase().includes(query) ||
          agent.phone_number.includes(query)
      );
    }

    setFilteredAgents(filtered);
  }, [agents, searchQuery, statusFilter]);

  // Handle agent verification
  const handleVerification = async (agentId, action) => {
    try {
      setProcessing(true);

      const result = await adminApi.verifyDeliveryAgent(agentId, action);

      if (result.success) {
        // Refresh the agents list
        await loadAgents();
        setShowDetails(false);
        setSelectedAgent(null);
      } else {
        setError(result.error?.message || `Failed to ${action} agent`);
      }
    } catch (err) {
      console.error("Verification error:", err);
      setError(`Failed to ${action} agent`);
    } finally {
      setProcessing(false);
    }
  };

  // Load agents on component mount
  useEffect(() => {
    loadAgents();
  }, []);

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: {
        className:
          "bg-yellow-100 text-yellow-800 border border-yellow-300 px-3 py-1 rounded-full text-sm font-semibold",
        label: "Pending Review",
        icon: <Clock className='h-4 w-4 mr-1' />,
      },
      active: {
        className:
          "bg-accent-green-light text-accent-green-dark border border-accent-green px-3 py-1 rounded-full text-sm font-semibold",
        label: "Active",
        icon: <CheckCircle className='h-4 w-4 mr-1' />,
      },
      suspended: {
        className:
          "bg-red-100 text-red-800 border border-red-300 px-3 py-1 rounded-full text-sm font-semibold",
        label: "Suspended",
        icon: <XCircle className='h-4 w-4 mr-1' />,
      },
      blocked: {
        className:
          "bg-red-100 text-red-800 border border-red-300 px-3 py-1 rounded-full text-sm font-semibold",
        label: "Blocked",
        icon: <XCircle className='h-4 w-4 mr-1' />,
      },
    };

    const config = statusConfig[status] || {
      className:
        "bg-gray-100 text-gray-800 border border-gray-300 px-3 py-1 rounded-full text-sm font-semibold",
      label: status,
      icon: null,
    };

    return (
      <span className={config.className}>
        <span className='flex items-center'>
          {config.icon}
          {config.label}
        </span>
      </span>
    );
  };

  if (loading) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <RefreshCw className='h-8 w-8 animate-spin mx-auto mb-4 text-blue-600' />
          <p className='text-gray-600'>Loading delivery agents...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <Card className='p-6 bg-white shadow-xl border-0 rounded-2xl'>
        <div className='flex justify-between items-center'>
          <div className='flex items-center space-x-4'>
            <div className='p-3 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full shadow-lg'>
              <Shield className='h-8 w-8 text-white' />
            </div>
            <div>
              <h1 className='text-3xl font-bold bg-gradient-to-r from-primary-500 to-primary-600 bg-clip-text text-transparent'>
                Delivery Agent Approvals
              </h1>
              <p className='text-gray-600 text-lg'>
                Review and approve delivery agent applications
              </p>
            </div>
          </div>
          <div className='flex items-center space-x-4'>
            <div className='text-right'>
              <p className='text-sm text-gray-500'>Total Pending</p>
              <p className='text-2xl font-bold text-primary-600'>
                {
                  filteredAgents.filter((agent) => agent.status === "pending")
                    .length
                }
              </p>
            </div>
            <Button
              onClick={loadAgents}
              disabled={loading}
              className='px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105'
            >
              <RefreshCw
                className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
              />
              <span>Refresh</span>
            </Button>
          </div>
        </div>
      </Card>

      {/* Error Message */}
      {error && (
        <Card className='p-4 bg-red-50 border-red-200'>
          <div className='flex items-center space-x-2'>
            <AlertCircle className='h-5 w-5 text-red-500' />
            <p className='text-red-700'>{error}</p>
            <Button
              variant='outline'
              size='sm'
              onClick={() => setError(null)}
              className='ml-auto'
            >
              Dismiss
            </Button>
          </div>
        </Card>
      )}

      {/* Filters */}
      <Card className='p-6 bg-white shadow-lg border-0 rounded-2xl'>
        <div className='flex flex-col sm:flex-row gap-6'>
          {/* Search */}
          <div className='flex-1'>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Search Agents
            </label>
            <div className='relative'>
              <Search className='absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
              <input
                type='text'
                placeholder='Search by name, email, agent ID, or phone...'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className='w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200'
              />
            </div>
          </div>

          {/* Status Filter */}
          <div className='sm:w-64'>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Filter by Status
            </label>
            <div className='relative'>
              <Filter className='absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400' />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className='w-full pl-12 pr-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 appearance-none bg-white'
              >
                <option value='all'>All Status</option>
                <option value='pending'>Pending Review</option>
                <option value='active'>Active</option>
                <option value='suspended'>Suspended</option>
                <option value='blocked'>Blocked</option>
              </select>
            </div>
          </div>
        </div>
      </Card>

      {/* Agents List */}
      <div className='grid gap-6'>
        {filteredAgents.length === 0 ? (
          <Card className='p-12 text-center bg-white shadow-lg border-0 rounded-2xl'>
            <div className='w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6'>
              <User className='h-10 w-10 text-gray-400' />
            </div>
            <h3 className='text-xl font-semibold text-gray-900 mb-3'>
              No delivery agents found
            </h3>
            <p className='text-gray-600 text-lg'>
              {statusFilter === "pending"
                ? "No agents pending approval at the moment"
                : "Try adjusting your search or filters to find agents"}
            </p>
          </Card>
        ) : (
          filteredAgents.map((agent) => (
            <Card
              key={agent.id}
              className='p-6 bg-white shadow-lg border-0 rounded-2xl hover:shadow-xl transition-all duration-200 hover:scale-[1.02]'
            >
              <div className='flex items-center justify-between'>
                <div className='flex items-center space-x-6'>
                  <div className='w-16 h-16 bg-gradient-to-r from-primary-100 to-orange-100 rounded-full flex items-center justify-center shadow-lg'>
                    <User className='h-8 w-8 text-primary-600' />
                  </div>
                  <div>
                    <h3 className='text-xl font-bold text-gray-900 mb-1'>
                      {agent.user.name}
                    </h3>
                    <p className='text-primary-600 font-semibold mb-2'>
                      ID: {agent.agent_id}
                    </p>
                    <div className='flex items-center space-x-6 mt-2'>
                      <span className='text-sm text-gray-600 flex items-center bg-gray-50 px-3 py-1 rounded-full'>
                        <Mail className='h-4 w-4 mr-2 text-gray-500' />
                        {agent.user.email}
                      </span>
                      <span className='text-sm text-gray-600 flex items-center bg-gray-50 px-3 py-1 rounded-full'>
                        <Phone className='h-4 w-4 mr-2 text-gray-500' />
                        {agent.phone_number}
                      </span>
                    </div>
                  </div>
                </div>

                <div className='flex items-center space-x-4'>
                  {getStatusBadge(agent.status)}

                  <div className='flex items-center space-x-3'>
                    {agent.status === "pending" && (
                      <div className='flex space-x-3'>
                        <Button
                          variant='outline'
                          onClick={() => handleVerification(agent.id, "reject")}
                          disabled={processing}
                          className='px-4 py-2 text-red-600 border-2 border-red-300 hover:bg-red-50 hover:border-red-400 rounded-xl transition-all duration-200'
                        >
                          <XCircle className='h-4 w-4 mr-2' />
                          Reject
                        </Button>
                        <Button
                          onClick={() =>
                            handleVerification(agent.id, "approve")
                          }
                          disabled={processing}
                          className='px-4 py-2 bg-gradient-to-r from-accent-green to-accent-green-dark hover:from-accent-green-dark hover:to-accent-green text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105'
                        >
                          <CheckCircle className='h-4 w-4 mr-2' />
                          Approve
                        </Button>
                      </div>
                    )}

                    <Button
                      variant='outline'
                      onClick={() => {
                        setSelectedAgent(agent);
                        setShowDetails(true);
                      }}
                      className='px-4 py-2 border-2 border-gray-300 hover:border-primary-400 hover:text-primary-600 hover:bg-primary-50 rounded-xl transition-all duration-200'
                    >
                      <Eye className='h-4 w-4 mr-2' />
                      View Details
                    </Button>
                  </div>
                </div>
              </div>

              {/* Quick Info */}
              <div className='mt-6 pt-4 border-t border-gray-100'>
                <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
                  <div className='flex items-center bg-primary-50 p-3 rounded-xl'>
                    <div className='p-2 bg-primary-100 rounded-full mr-3'>
                      <Truck className='h-4 w-4 text-primary-600' />
                    </div>
                    <div>
                      <p className='text-xs text-gray-500 uppercase tracking-wide'>
                        Vehicle
                      </p>
                      <p className='font-semibold text-gray-900'>
                        {agent.vehicle_type}
                      </p>
                    </div>
                  </div>
                  <div className='flex items-center bg-orange-50 p-3 rounded-xl'>
                    <div className='p-2 bg-orange-100 rounded-full mr-3'>
                      <Calendar className='h-4 w-4 text-orange-600' />
                    </div>
                    <div>
                      <p className='text-xs text-gray-500 uppercase tracking-wide'>
                        Joined
                      </p>
                      <p className='font-semibold text-gray-900'>
                        {new Date(agent.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className='flex items-center bg-blue-50 p-3 rounded-xl'>
                    <div className='p-2 bg-blue-100 rounded-full mr-3'>
                      <Package className='h-4 w-4 text-blue-600' />
                    </div>
                    <div>
                      <p className='text-xs text-gray-500 uppercase tracking-wide'>
                        Deliveries
                      </p>
                      <p className='font-semibold text-gray-900'>
                        {agent.total_deliveries}
                      </p>
                    </div>
                  </div>
                  <div className='flex items-center bg-yellow-50 p-3 rounded-xl'>
                    <div className='p-2 bg-yellow-100 rounded-full mr-3'>
                      <Star className='h-4 w-4 text-yellow-600' />
                    </div>
                    <div>
                      <p className='text-xs text-gray-500 uppercase tracking-wide'>
                        Rating
                      </p>
                      <p className='font-semibold text-gray-900'>
                        {agent.rating.toFixed(1)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}

export default DeliveryAgentApprovals;
