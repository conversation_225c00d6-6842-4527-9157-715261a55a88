import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  User,
  Upload,
  Car,
  Shield,
  CreditCard,
  Phone,
  CheckCircle,
  AlertCircle,
  Camera,
  FileText,
  MapPin,
  Clock,
  ArrowRight,
  ArrowLeft,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";
import PersonalInfoStep from "../../components/delivery/registration/PersonalInfoStep";
import DocumentUploadStep from "../../components/delivery/registration/DocumentUploadStep";
import VehicleInfoStep from "../../components/delivery/registration/VehicleInfoStep";
import BackgroundCheckStep from "../../components/delivery/registration/BackgroundCheckStep";
import BankDetailsStep from "../../components/delivery/registration/BankDetailsStep";
import AvailabilityStep from "../../components/delivery/registration/AvailabilityStep";

const EnhancedRegistration = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const [formData, setFormData] = useState({
    // Personal Information
    personalInfo: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      dateOfBirth: "",
      address: "",
      city: "",
      postalCode: "",
      emergencyContactName: "",
      emergencyContactPhone: "",
    },
    // Documents
    documents: {
      nationalId: null,
      drivingLicense: null,
      vehicleRegistration: null,
      insurance: null,
      profilePhoto: null,
    },
    // Vehicle Information
    vehicleInfo: {
      type: "",
      make: "",
      model: "",
      year: "",
      color: "",
      licensePlate: "",
      insuranceExpiry: "",
      vehiclePhotos: [],
    },
    // Background Check
    backgroundCheck: {
      consentGiven: false,
      criminalHistory: false,
      drivingRecord: false,
    },
    // Bank Details
    bankDetails: {
      accountHolderName: "",
      accountNumber: "",
      routingNumber: "",
      bankName: "",
      accountType: "checking",
    },
    // Availability
    availability: {
      preferredZones: [],
      workingHours: {
        monday: { start: "09:00", end: "17:00", available: true },
        tuesday: { start: "09:00", end: "17:00", available: true },
        wednesday: { start: "09:00", end: "17:00", available: true },
        thursday: { start: "09:00", end: "17:00", available: true },
        friday: { start: "09:00", end: "17:00", available: true },
        saturday: { start: "10:00", end: "16:00", available: false },
        sunday: { start: "10:00", end: "16:00", available: false },
      },
    },
  });

  const steps = [
    {
      id: 1,
      title: "Personal Information",
      icon: <User className='h-5 w-5' />,
      description: "Basic personal details and contact information",
    },
    {
      id: 2,
      title: "Document Upload",
      icon: <Upload className='h-5 w-5' />,
      description: "Upload required identification and license documents",
    },
    {
      id: 3,
      title: "Vehicle Information",
      icon: <Car className='h-5 w-5' />,
      description: "Vehicle details and photos for verification",
    },
    {
      id: 4,
      title: "Background Check",
      icon: <Shield className='h-5 w-5' />,
      description: "Authorization for background verification",
    },
    {
      id: 5,
      title: "Bank Details",
      icon: <CreditCard className='h-5 w-5' />,
      description: "Payment information for earnings",
    },
    {
      id: 6,
      title: "Availability",
      icon: <Clock className='h-5 w-5' />,
      description: "Set your preferred working hours and zones",
    },
  ];

  const handleInputChange = (section, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const handleFileUpload = (section, field, file) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: file,
      },
    }));
  };

  const validateStep = (step) => {
    const newErrors = {};

    switch (step) {
      case 1:
        if (!formData.personalInfo.firstName)
          newErrors.firstName = "First name is required";
        if (!formData.personalInfo.lastName)
          newErrors.lastName = "Last name is required";
        if (!formData.personalInfo.email) newErrors.email = "Email is required";
        if (!formData.personalInfo.phone)
          newErrors.phone = "Phone number is required";
        break;
      case 2:
        if (!formData.documents.nationalId)
          newErrors.nationalId = "National ID is required";
        if (!formData.documents.drivingLicense)
          newErrors.drivingLicense = "Driving license is required";
        break;
      case 3:
        if (!formData.vehicleInfo.type)
          newErrors.vehicleType = "Vehicle type is required";
        if (!formData.vehicleInfo.licensePlate)
          newErrors.licensePlate = "License plate is required";
        break;
      case 4:
        if (!formData.backgroundCheck.consentGiven)
          newErrors.consent = "Consent is required";
        break;
      case 5:
        if (!formData.bankDetails.accountNumber)
          newErrors.accountNumber = "Account number is required";
        if (!formData.bankDetails.bankName)
          newErrors.bankName = "Bank name is required";
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length));
    }
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    try {
      setLoading(true);

      // Create FormData for file uploads
      const submitData = new FormData();

      // Add all form data
      Object.keys(formData).forEach((section) => {
        if (section === "documents") {
          Object.keys(formData[section]).forEach((key) => {
            if (formData[section][key]) {
              submitData.append(key, formData[section][key]);
            }
          });
        } else {
          submitData.append(section, JSON.stringify(formData[section]));
        }
      });

      const result = await deliveryAgentApi.registerEnhanced(submitData);

      if (result.success) {
        navigate("/delivery/registration-success");
      } else {
        setErrors({ submit: result.error?.message || "Registration failed" });
      }
    } catch (error) {
      setErrors({ submit: "Registration failed. Please try again." });
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <PersonalInfoStep
            data={formData.personalInfo}
            onChange={(field, value) =>
              handleInputChange("personalInfo", field, value)
            }
            errors={errors}
          />
        );
      case 2:
        return (
          <DocumentUploadStep
            data={formData.documents}
            onChange={handleFileUpload}
            errors={errors}
          />
        );
      case 3:
        return (
          <VehicleInfoStep
            data={formData.vehicleInfo}
            onChange={(field, value) =>
              handleInputChange("vehicleInfo", field, value)
            }
            errors={errors}
          />
        );
      case 4:
        return (
          <BackgroundCheckStep
            data={formData.backgroundCheck}
            onChange={(field, value) =>
              handleInputChange("backgroundCheck", field, value)
            }
            errors={errors}
          />
        );
      case 5:
        return (
          <BankDetailsStep
            data={formData.bankDetails}
            onChange={(field, value) =>
              handleInputChange("bankDetails", field, value)
            }
            errors={errors}
          />
        );
      case 6:
        return (
          <AvailabilityStep
            data={formData.availability}
            onChange={(field, value) =>
              handleInputChange("availability", field, value)
            }
            errors={errors}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className='min-h-screen bg-gradient-to-br from-primary-50 via-orange-50 to-primary-100 py-8'>
      <div className='max-w-5xl mx-auto px-4'>
        {/* Header */}
        <div className='text-center mb-10'>
          <div className='inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full mb-4 shadow-lg'>
            <User className='h-8 w-8 text-white' />
          </div>
          <h1 className='text-4xl font-bold bg-gradient-to-r from-primary-500 to-primary-600 bg-clip-text text-transparent mb-3'>
            Become a Delivery Agent
          </h1>
          <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
            Join our delivery network and start earning with flexible hours and
            competitive rates
          </p>
        </div>

        {/* Progress Steps */}
        <div className='mb-10'>
          <div className='flex items-center justify-between max-w-4xl mx-auto'>
            {steps.map((step, index) => (
              <div key={step.id} className='flex items-center flex-1'>
                <div className='flex flex-col items-center'>
                  <div
                    className={`
                    flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 shadow-lg
                    ${
                      currentStep >= step.id
                        ? "bg-gradient-to-r from-primary-500 to-primary-600 border-transparent text-white shadow-primary-200"
                        : currentStep === step.id - 1
                        ? "bg-primary-50 border-primary-300 text-primary-600"
                        : "bg-white border-gray-300 text-gray-400"
                    }
                  `}
                  >
                    {currentStep > step.id ? (
                      <CheckCircle className='h-6 w-6' />
                    ) : (
                      <span className='text-sm font-bold'>{step.id}</span>
                    )}
                  </div>
                  <div className='mt-2 text-center'>
                    <p
                      className={`text-xs font-medium ${
                        currentStep >= step.id
                          ? "text-primary-600"
                          : "text-gray-500"
                      }`}
                    >
                      {step.title}
                    </p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className='flex-1 mx-4'>
                    <div
                      className={`
                      h-2 rounded-full transition-all duration-300
                      ${
                        currentStep > step.id
                          ? "bg-gradient-to-r from-primary-500 to-primary-600"
                          : "bg-gray-200"
                      }
                    `}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className='mt-6 text-center bg-white rounded-xl p-6 shadow-lg border border-gray-100'>
            <div className='flex items-center justify-center mb-3'>
              {steps[currentStep - 1]?.icon}
              <h2 className='text-2xl font-bold text-gray-900 ml-2'>
                {steps[currentStep - 1]?.title}
              </h2>
            </div>
            <p className='text-gray-600'>
              {steps[currentStep - 1]?.description}
            </p>
          </div>
        </div>

        {/* Step Content */}
        <Card className='p-8 mb-8 shadow-xl border-0 bg-white rounded-2xl'>
          {renderStepContent()}
        </Card>

        {/* Navigation */}
        <div className='flex justify-between items-center bg-white rounded-xl p-6 shadow-lg border border-gray-100'>
          <Button
            variant='outline'
            onClick={handlePrevious}
            disabled={currentStep === 1}
            icon={<ArrowLeft className='h-4 w-4' />}
            className={`px-6 py-3 rounded-xl border-2 transition-all duration-200 ${
              currentStep === 1
                ? "border-gray-200 text-gray-400 cursor-not-allowed"
                : "border-gray-300 text-gray-700 hover:border-blue-400 hover:text-blue-600 hover:bg-blue-50"
            }`}
          >
            Previous
          </Button>

          <div className='flex items-center space-x-2 text-sm text-gray-500'>
            <span>
              Step {currentStep} of {steps.length}
            </span>
          </div>

          {currentStep < steps.length ? (
            <Button
              onClick={handleNext}
              icon={<ArrowRight className='h-4 w-4' />}
              iconPosition='right'
              className='px-6 py-3 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105'
            >
              Continue
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              loading={loading}
              className='px-8 py-3 bg-gradient-to-r from-accent-green to-accent-green-dark hover:from-accent-green-dark hover:to-accent-green text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105'
            >
              {loading ? "Submitting..." : "Submit Application"}
            </Button>
          )}
        </div>

        {/* Error Display */}
        {errors.submit && (
          <div className='mt-6 p-4 bg-red-50 border border-red-200 rounded-xl shadow-lg'>
            <div className='flex items-center'>
              <div className='flex-shrink-0'>
                <AlertCircle className='h-6 w-6 text-red-500' />
              </div>
              <div className='ml-3'>
                <h3 className='text-sm font-medium text-red-800'>
                  Registration Error
                </h3>
                <p className='text-sm text-red-700 mt-1'>{errors.submit}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedRegistration;
