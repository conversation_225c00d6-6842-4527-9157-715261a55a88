#!/usr/bin/env python3
"""
Test OTP functionality through frontend API endpoints
"""

import requests
import json
import time

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_registration_with_real_email():
    """Test registration with real email to verify OTP sending"""
    print("🚀 Testing Registration with Real Email OTP")
    print("=" * 60)
    
    # Get real email from user
    print("📧 Please provide a real email address to test OTP functionality:")
    email = input("Enter your email: ").strip()
    
    if not email or "@" not in email:
        print("❌ Invalid email address")
        return None
    
    # Create test user data
    timestamp = int(time.time())
    test_user = {
        "name": "OTP Test User",
        "user_name": f"otptest_{timestamp}",
        "email": email,
        "phone": f"+1555{timestamp % 10000:04d}",
        "password": "TestPassword123",
        "confirm_password": "TestPassword123",
        "role": "customer"
    }
    
    print(f"\n📝 Registering user with email: {email}")
    print(f"👤 Username: {test_user['user_name']}")
    
    try:
        # Make registration request
        response = requests.post(
            f"{API_BASE_URL}/auth/register/",
            headers=HEADERS,
            data=json.dumps(test_user),
            timeout=30
        )
        
        print(f"\n📡 Registration Response:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Time: {response.elapsed.total_seconds():.2f}s")
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                print("✅ Registration successful!")
                print(f"📧 OTP email should be sent to: {email}")
                print(f"👤 User ID: {result['data']['user_id']}")
                print(f"📛 Username: {result['data']['username']}")
                
                print("\n" + "=" * 60)
                print("📬 IMPORTANT: Check your email inbox AND spam folder!")
                print("📧 Look for email from: <EMAIL>")
                print("📋 Subject: Your OTP Verification Code")
                print("=" * 60)
                
                return result['data']
            else:
                print("❌ Registration failed:", result.get('message'))
                if 'errors' in result:
                    print("📋 Errors:", result['errors'])
                return None
        else:
            print(f"❌ Registration failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"📋 Error details: {error_data}")
            except:
                print(f"📄 Raw response: {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out - server might be slow")
        return None
    except requests.exceptions.ConnectionError:
        print("🔌 Connection error - check if backend server is running")
        return None
    except Exception as e:
        print(f"❌ Exception during registration: {e}")
        return None

def test_otp_verification(email, user_data):
    """Test OTP verification"""
    print(f"\n🔐 Testing OTP Verification for {email}")
    print("=" * 60)
    
    # Get OTP from user
    print("🔑 Enter the 6-digit OTP you received in your email:")
    otp = input("OTP: ").strip()
    
    if len(otp) != 6 or not otp.isdigit():
        print("❌ Invalid OTP format. Must be exactly 6 digits.")
        return False
    
    verification_data = {
        "email": email,
        "otp": otp
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/verify-email/",
            headers=HEADERS,
            data=json.dumps(verification_data),
            timeout=30
        )
        
        print(f"\n📡 Verification Response:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Time: {response.elapsed.total_seconds():.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("🎉 Email verification successful!")
                print(f"🎫 Access token received: {result['data']['access_token'][:50]}...")
                print(f"🔄 Refresh token received: {result['data']['refresh_token'][:50]}...")
                print(f"👤 User verified: {result['data']['user']['name']}")
                print(f"🎯 Redirect to: {result['data']['redirect_to']}")
                return True
            else:
                print("❌ Verification failed:", result.get('message'))
                return False
        else:
            print(f"❌ Verification failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"📋 Error details: {error_data}")
            except:
                print(f"📄 Raw response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during verification: {e}")
        return False

def test_otp_resend(email):
    """Test OTP resend functionality"""
    print(f"\n🔄 Testing OTP Resend for {email}")
    print("=" * 60)
    
    resend_data = {"email": email}
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/resend-otp/",
            headers=HEADERS,
            data=json.dumps(resend_data),
            timeout=30
        )
        
        print(f"📡 Resend Response:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Time: {response.elapsed.total_seconds():.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ OTP resend successful!")
            print(f"📧 New OTP sent to: {result.get('email', email)}")
            print("📬 Check your email for the new OTP!")
            return True
        else:
            print(f"❌ Resend failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during resend: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Afghan Sufra OTP Email Test Suite")
    print("=" * 60)
    print("This test will verify that OTP emails are sent to real email addresses")
    print("=" * 60)
    
    # Test 1: Registration with real email
    user_data = test_registration_with_real_email()
    if not user_data:
        print("\n❌ Registration test failed. Cannot proceed with OTP verification.")
        return
    
    email = user_data['email']
    
    # Give user time to check email
    print(f"\n⏰ Waiting for you to receive the OTP email...")
    input("Press Enter when you're ready to enter the OTP...")
    
    # Test 2: OTP Verification
    verification_success = test_otp_verification(email, user_data)
    
    if not verification_success:
        print("\n🔄 OTP verification failed. Let's try resending the OTP...")
        
        # Test 3: OTP Resend
        resend_success = test_otp_resend(email)
        
        if resend_success:
            print("\n⏰ New OTP sent! Check your email again...")
            input("Press Enter when you're ready to enter the new OTP...")
            verification_success = test_otp_verification(email, user_data)
    
    # Final results
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if verification_success:
        print("🎉 SUCCESS: OTP email system is working correctly!")
        print("✅ Registration: Working")
        print("✅ Email sending: Working") 
        print("✅ OTP verification: Working")
        print("✅ JWT token generation: Working")
    else:
        print("❌ FAILED: OTP verification did not work")
        print("🔧 Possible issues:")
        print("   - Email might be in spam folder")
        print("   - OTP might have expired (10 minutes)")
        print("   - Wrong OTP entered")
        print("   - Email delivery delay")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
