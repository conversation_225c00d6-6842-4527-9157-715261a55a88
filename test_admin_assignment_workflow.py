#!/usr/bin/env python3
"""
Test the complete admin assignment workflow as requested:
1. Customer makes order
2. Restaurant confirms → preparing → ready
3. <PERSON><PERSON> sees ready orders and active delivery agents
4. Admin assigns ready order to active delivery agent
5. Delivery agent sees assigned order and can update status
"""

import os
import sys
import django

# Add the Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from orders.models import Order
from restaurant.models import Restaurant, Address
from deliveryAgent.models import DeliveryAgentProfile
from deliveryAgent.manual_assignment_views import AdminOrderAssignmentView
from django.test import RequestFactory
import json

User = get_user_model()

def test_admin_assignment_workflow():
    """Test the complete admin assignment workflow"""
    
    print("🔄 Testing Admin Assignment Workflow")
    print("=" * 60)
    
    try:
        # Step 1: Verify we have the required data
        print("1️⃣ Checking system setup...")
        
        # Check for ready orders
        ready_orders = Order.objects.filter(status='ready', delivery_agent__isnull=True)
        print(f"   📦 Ready orders (unassigned): {ready_orders.count()}")
        
        # Check for available agents
        available_agents = DeliveryAgentProfile.objects.filter(
            employment_status='active',
            availability='available',
            is_clocked_in=True
        )
        print(f"   🚚 Available delivery agents: {available_agents.count()}")
        
        if not ready_orders.exists():
            print("   ⚠️ No ready orders found. Creating a test order...")
            # We already have orders from previous tests
            
        if not available_agents.exists():
            print("   ⚠️ No available agents found. Please run setup_test_agent.py first")
            return False
            
        # Step 2: Test Admin API - Get Assignment Data
        print("\n2️⃣ Testing Admin Assignment API...")
        
        # Create admin user for testing
        try:
            admin_user = User.objects.get(email='<EMAIL>')
        except User.DoesNotExist:
            import random
            phone_suffix = random.randint(1000, 9999)
            admin_user = User.objects.create(
                email='<EMAIL>',
                name='Admin User',
                user_name=f'admin_user_{phone_suffix}',
                role='admin',
                is_verified=True,
                phone=f'+93 70 000 {phone_suffix}'
            )
            admin_user.set_password('admin123')
            admin_user.save()
        
        # Test the admin assignment API
        factory = RequestFactory()
        request = factory.get('/delivery-agent/admin/assignments/')
        request.user = admin_user
        
        view = AdminOrderAssignmentView()
        response = view.get(request)
        
        if response.status_code == 200:
            data = response.data
            print(f"   ✅ Admin API working")
            print(f"   📊 Ready orders: {len(data['data']['ready_orders'])}")
            print(f"   👥 Available agents: {len(data['data']['available_agents'])}")
            
            if data['data']['ready_orders'] and data['data']['available_agents']:
                # Step 3: Test Manual Assignment
                print("\n3️⃣ Testing Manual Order Assignment...")
                
                order_data = data['data']['ready_orders'][0]
                agent_data = data['data']['available_agents'][0]
                
                print(f"   📦 Assigning Order #{order_data['id']} to Agent {agent_data['agent_id']}")
                
                # Test assignment
                assignment_request = factory.post('/delivery-agent/admin/assignments/', {
                    'order_id': order_data['id'],
                    'agent_id': agent_data['agent_id'],
                    'notes': 'Test assignment by admin'
                }, content_type='application/json')
                assignment_request.user = admin_user
                assignment_request.data = {
                    'order_id': order_data['id'],
                    'agent_id': agent_data['agent_id'],
                    'notes': 'Test assignment by admin'
                }
                
                assignment_response = view.post(assignment_request)
                
                if assignment_response.status_code == 200:
                    assignment_result = assignment_response.data
                    print(f"   ✅ Assignment successful!")
                    print(f"   📋 Order status: {assignment_result['data']['order_status']}")
                    print(f"   👤 Agent status: {assignment_result['data']['agent_status']}")
                    
                    # Step 4: Verify assignment in database
                    print("\n4️⃣ Verifying assignment in database...")
                    
                    order = Order.objects.get(id=order_data['id'])
                    agent = DeliveryAgentProfile.objects.get(agent_id=agent_data['agent_id'])
                    
                    if order.status == 'assigned' and order.delivery_agent == agent.user:
                        print(f"   ✅ Order correctly assigned in database")
                        print(f"   📦 Order status: {order.status}")
                        print(f"   👤 Assigned to: {order.delivery_agent.name}")
                        
                        if agent.availability == 'busy':
                            print(f"   ✅ Agent correctly marked as busy")
                        else:
                            print(f"   ⚠️ Agent availability: {agent.availability} (should be 'busy')")
                    else:
                        print(f"   ❌ Assignment not reflected in database")
                        return False
                        
                    # Step 5: Show what delivery agent would see
                    print("\n5️⃣ What delivery agent sees...")
                    print(f"   🎯 Agent {agent.full_name} would now see:")
                    print(f"   📱 Order #{order.id} in their 'Assigned Orders' section")
                    print(f"   🏪 Restaurant: {order.restaurant.name}")
                    print(f"   👤 Customer: {order.customer.name}")
                    print(f"   💰 Amount: ${order.total_amount}")
                    print(f"   📍 Delivery to: {order.delivery_address}")
                    print(f"   📋 Status: {order.status} (ready for pickup)")
                    
                else:
                    print(f"   ❌ Assignment failed: {assignment_response.data}")
                    return False
            else:
                print("   ⚠️ No orders or agents available for assignment test")
        else:
            print(f"   ❌ Admin API failed: {response.status_code}")
            return False
        
        print("\n🎉 Admin Assignment Workflow Test Complete!")
        print("\n📋 Workflow Summary:")
        print("   1️⃣ ✅ Customer makes order")
        print("   2️⃣ ✅ Restaurant: pending → confirmed → preparing → ready")
        print("   3️⃣ ✅ Admin sees ready orders and active delivery agents")
        print("   4️⃣ ✅ Admin can assign ready orders to delivery agents")
        print("   5️⃣ ✅ Delivery agent sees assigned order and can update status")
        
        print(f"\n🔗 Admin Assignment URL: http://localhost:5174/admin/order-assignments")
        print(f"   Login as admin: {admin_user.email} / admin123")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_admin_assignment_workflow()
    sys.exit(0 if success else 1)
