import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../../utils/cn';

const TouchFeedback = ({
  children,
  className = "",
  feedbackType = 'ripple', // 'ripple', 'scale', 'highlight'
  disabled = false,
  haptic = false,
  ...props
}) => {
  const [ripples, setRipples] = useState([]);
  const [isPressed, setIsPressed] = useState(false);
  const elementRef = useRef(null);
  const rippleId = useRef(0);

  const triggerHaptic = () => {
    if (haptic && 'vibrate' in navigator) {
      navigator.vibrate(10); // Short vibration
    }
  };

  const createRipple = (event) => {
    if (disabled || feedbackType !== 'ripple') return;

    const element = elementRef.current;
    if (!element) return;

    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    const newRipple = {
      id: rippleId.current++,
      x,
      y,
      size
    };

    setRipples(prev => [...prev, newRipple]);
    triggerHaptic();

    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id));
    }, 600);
  };

  const handleMouseDown = (event) => {
    setIsPressed(true);
    createRipple(event);
    props.onMouseDown?.(event);
  };

  const handleMouseUp = (event) => {
    setIsPressed(false);
    props.onMouseUp?.(event);
  };

  const handleTouchStart = (event) => {
    setIsPressed(true);
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      createRipple(touch);
    }
    triggerHaptic();
    props.onTouchStart?.(event);
  };

  const handleTouchEnd = (event) => {
    setIsPressed(false);
    props.onTouchEnd?.(event);
  };

  const getTransformStyle = () => {
    if (disabled) return {};
    
    switch (feedbackType) {
      case 'scale':
        return {
          transform: isPressed ? 'scale(0.95)' : 'scale(1)',
          transition: 'transform 0.1s ease-out'
        };
      case 'highlight':
        return {
          backgroundColor: isPressed ? 'rgba(0, 0, 0, 0.05)' : 'transparent',
          transition: 'background-color 0.1s ease-out'
        };
      default:
        return {};
    }
  };

  return (
    <div
      ref={elementRef}
      className={cn(
        'relative overflow-hidden',
        feedbackType === 'ripple' && 'touch-manipulation',
        disabled && 'pointer-events-none opacity-50',
        className
      )}
      style={getTransformStyle()}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      {...props}
    >
      {children}
      
      {/* Ripple effects */}
      {feedbackType === 'ripple' && ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute rounded-full bg-current opacity-30 pointer-events-none animate-ping"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
            animationDuration: '0.6s',
            animationTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)'
          }}
        />
      ))}
    </div>
  );
};

// Enhanced button with touch feedback
export const TouchButton = ({
  children,
  variant = 'primary',
  size = 'medium',
  feedbackType = 'ripple',
  haptic = true,
  className = "",
  ...props
}) => {
  const baseClasses = cn(
    'inline-flex items-center justify-center font-medium rounded-lg transition-colors',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'select-none touch-manipulation'
  );

  const variantClasses = {
    primary: 'bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500'
  };

  const sizeClasses = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-4 py-2 text-base',
    large: 'px-6 py-3 text-lg'
  };

  return (
    <TouchFeedback
      feedbackType={feedbackType}
      haptic={haptic}
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {children}
    </TouchFeedback>
  );
};

// Touch-optimized card
export const TouchCard = ({
  children,
  hoverable = true,
  pressable = true,
  className = "",
  ...props
}) => {
  return (
    <TouchFeedback
      feedbackType={pressable ? 'scale' : 'none'}
      haptic={pressable}
      className={cn(
        'bg-white rounded-lg border border-gray-200 shadow-sm',
        hoverable && 'hover:shadow-md transition-shadow duration-200',
        pressable && 'cursor-pointer active:shadow-lg',
        'touch-manipulation',
        className
      )}
      {...props}
    >
      {children}
    </TouchFeedback>
  );
};

// Touch-optimized list item
export const TouchListItem = ({
  children,
  onClick,
  className = "",
  ...props
}) => {
  return (
    <TouchFeedback
      feedbackType="highlight"
      haptic={!!onClick}
      onClick={onClick}
      className={cn(
        'block w-full text-left p-4 border-b border-gray-100 last:border-b-0',
        onClick && 'cursor-pointer hover:bg-gray-50',
        'touch-manipulation',
        className
      )}
      {...props}
    >
      {children}
    </TouchFeedback>
  );
};

export default TouchFeedback;
