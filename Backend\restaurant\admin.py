from django.contrib import admin
from .models import (
    Address, Restaurant, MenuCategory, MenuItem, RestaurantReview, ReviewResponse,
    MenuItemVariant, MenuItemAddon, MenuItemCustomizationGroup, MenuItemCustomizationOption,
    CuisineType, RestaurantCategory, ServiceArea, Promotion, PromotionCode
)

class AddressAdmin(admin.ModelAdmin):
    list_display = ('id','street', 'city', 'state', 'user')
    search_fields = ('street', 'city', 'user__username')
    list_filter = ('city', 'state')

class RestaurantAdmin(admin.ModelAdmin):
    list_display = ('id','name', 'owner', 'is_active', 'is_verified', 'rating', 'created_at', 'verification_status')
    search_fields = ('name', 'owner__username', 'owner__email')
    list_filter = ('is_active', 'is_verified', 'created_at')
    raw_id_fields = ('owner', 'address')
    readonly_fields = ('created_at', 'updated_at', 'rating')
    actions = ['verify_restaurants', 'unverify_restaurants', 'activate_restaurants', 'deactivate_restaurants']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'owner', 'address')
        }),
        ('Contact & Media', {
            'fields': ('contact_number', 'logo', 'banner')
        }),
        ('Business Hours', {
            'fields': ('opening_time', 'closing_time')
        }),
        ('Status & Verification', {
            'fields': ('is_active', 'is_verified', 'rating'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def verification_status(self, obj):
        if obj.is_verified:
            return "✅ Verified"
        else:
            return "⏳ Pending Verification"
    verification_status.short_description = "Status"

    def verify_restaurants(self, request, queryset):
        updated = queryset.update(is_verified=True)
        self.message_user(request, f'{updated} restaurants have been verified.')
    verify_restaurants.short_description = "Verify selected restaurants"

    def unverify_restaurants(self, request, queryset):
        updated = queryset.update(is_verified=False)
        self.message_user(request, f'{updated} restaurants have been unverified.')
    unverify_restaurants.short_description = "Unverify selected restaurants"

    def activate_restaurants(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} restaurants have been activated.')
    activate_restaurants.short_description = "Activate selected restaurants"

    def deactivate_restaurants(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} restaurants have been deactivated.')
    deactivate_restaurants.short_description = "Deactivate selected restaurants"

class MenuCategoryAdmin(admin.ModelAdmin):
    list_display = ('id','name', 'restaurant')
    list_filter = ('restaurant',)
    search_fields = ('name', 'restaurant__name')

class MenuItemAdmin(admin.ModelAdmin):
    list_display = ('id','name', 'category', 'price', 'is_available')
    list_filter = ('is_available', 'is_vegetarian', 'category__restaurant')
    search_fields = ('name', 'category__name')
    raw_id_fields = ('category',)

admin.site.register(Address, AddressAdmin)
admin.site.register(Restaurant, RestaurantAdmin)
admin.site.register(MenuCategory, MenuCategoryAdmin)
admin.site.register(MenuItem, MenuItemAdmin)

class RestaurantReviewAdmin(admin.ModelAdmin):
    list_display = ('id', 'restaurant', 'customer', 'rating', 'is_verified', 'is_approved', 'created_at')
    list_filter = ('rating', 'is_verified', 'is_approved', 'created_at')
    search_fields = ('restaurant__name', 'customer__name', 'title', 'comment')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('restaurant', 'customer', 'order')

class ReviewResponseAdmin(admin.ModelAdmin):
    list_display = ('id', 'review', 'restaurant_owner', 'created_at')
    search_fields = ('review__restaurant__name', 'restaurant_owner__name', 'message')
    readonly_fields = ('created_at', 'updated_at')
    raw_id_fields = ('review', 'restaurant_owner')

admin.site.register(RestaurantReview, RestaurantReviewAdmin)
admin.site.register(ReviewResponse, ReviewResponseAdmin)

# Menu Item Variants & Customizations Admin
class MenuItemVariantAdmin(admin.ModelAdmin):
    list_display = ('id', 'menu_item', 'name', 'price_adjustment', 'final_price', 'is_default', 'is_available')
    list_filter = ('is_default', 'is_available', 'menu_item__category__restaurant')
    search_fields = ('name', 'menu_item__name', 'menu_item__category__restaurant__name')
    raw_id_fields = ('menu_item',)

class MenuItemAddonAdmin(admin.ModelAdmin):
    list_display = ('id', 'menu_item', 'name', 'price', 'addon_type', 'is_available')
    list_filter = ('addon_type', 'is_available', 'menu_item__category__restaurant')
    search_fields = ('name', 'menu_item__name', 'menu_item__category__restaurant__name')
    raw_id_fields = ('menu_item',)

class MenuItemCustomizationOptionInline(admin.TabularInline):
    model = MenuItemCustomizationOption
    extra = 1

class MenuItemCustomizationGroupAdmin(admin.ModelAdmin):
    list_display = ('id', 'menu_item', 'name', 'is_required', 'min_selections', 'max_selections', 'display_order')
    list_filter = ('is_required', 'menu_item__category__restaurant')
    search_fields = ('name', 'menu_item__name', 'menu_item__category__restaurant__name')
    raw_id_fields = ('menu_item',)
    inlines = [MenuItemCustomizationOptionInline]

class MenuItemCustomizationOptionAdmin(admin.ModelAdmin):
    list_display = ('id', 'group', 'name', 'price_adjustment', 'is_default', 'is_available', 'display_order')
    list_filter = ('is_default', 'is_available', 'group__menu_item__category__restaurant')
    search_fields = ('name', 'group__name', 'group__menu_item__name')
    raw_id_fields = ('group',)

admin.site.register(MenuItemVariant, MenuItemVariantAdmin)
admin.site.register(MenuItemAddon, MenuItemAddonAdmin)
admin.site.register(MenuItemCustomizationGroup, MenuItemCustomizationGroupAdmin)
admin.site.register(MenuItemCustomizationOption, MenuItemCustomizationOptionAdmin)

# Restaurant Business Details Admin
class CuisineTypeAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description', 'icon', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at',)

class RestaurantCategoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'description', 'icon', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at',)

class ServiceAreaAdmin(admin.ModelAdmin):
    list_display = ('id', 'restaurant', 'area_name', 'delivery_fee', 'min_order_amount', 'estimated_delivery_time', 'is_active')
    list_filter = ('is_active', 'restaurant', 'created_at')
    search_fields = ('area_name', 'restaurant__name', 'postal_codes')
    readonly_fields = ('created_at',)
    raw_id_fields = ('restaurant',)

admin.site.register(CuisineType, CuisineTypeAdmin)
admin.site.register(RestaurantCategory, RestaurantCategoryAdmin)
admin.site.register(ServiceArea, ServiceAreaAdmin)

# Promotions & Discounts Admin
class PromotionCodeInline(admin.TabularInline):
    model = PromotionCode
    extra = 1
    readonly_fields = ('usage_count',)

class PromotionAdmin(admin.ModelAdmin):
    list_display = (
        'id', 'restaurant', 'name', 'promotion_type', 'discount_percentage',
        'discount_amount', 'min_order_amount', 'is_active', 'is_featured',
        'start_date', 'end_date', 'current_usage_count'
    )
    list_filter = (
        'promotion_type', 'discount_applies_to', 'is_active', 'is_featured',
        'restaurant', 'start_date', 'end_date'
    )
    search_fields = ('name', 'description', 'restaurant__name')
    readonly_fields = ('current_usage_count', 'created_at', 'updated_at')
    raw_id_fields = ('restaurant',)
    inlines = [PromotionCodeInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('restaurant', 'name', 'description', 'promotion_type', 'discount_applies_to')
        }),
        ('Discount Settings', {
            'fields': ('discount_percentage', 'discount_amount', 'min_order_amount', 'max_discount_amount')
        }),
        ('Usage Limits', {
            'fields': ('usage_limit', 'usage_limit_per_customer', 'current_usage_count')
        }),
        ('Time Constraints', {
            'fields': ('start_date', 'end_date', 'valid_from_time', 'valid_to_time')
        }),
        ('Days of Week', {
            'fields': (
                'valid_on_monday', 'valid_on_tuesday', 'valid_on_wednesday', 'valid_on_thursday',
                'valid_on_friday', 'valid_on_saturday', 'valid_on_sunday'
            )
        }),
        ('Status', {
            'fields': ('is_active', 'is_featured')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

class PromotionCodeAdmin(admin.ModelAdmin):
    list_display = ('id', 'code', 'promotion', 'usage_count', 'is_active', 'created_at')
    list_filter = ('is_active', 'promotion__restaurant', 'created_at')
    search_fields = ('code', 'promotion__name', 'promotion__restaurant__name')
    readonly_fields = ('usage_count', 'created_at')
    raw_id_fields = ('promotion',)

admin.site.register(Promotion, PromotionAdmin)
admin.site.register(PromotionCode, PromotionCodeAdmin)