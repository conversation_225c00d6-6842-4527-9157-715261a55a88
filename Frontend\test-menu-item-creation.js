// Test script to debug menu item creation
const testMenuItemCreation = async () => {
  const API_BASE_URL = "http://127.0.0.1:8000/api";
  
  // Get auth token from localStorage (you need to be logged in)
  const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
  
  if (!user.access_token) {
    console.error("No auth token found. Please login first.");
    return;
  }
  
  console.log("Auth token:", user.access_token.substring(0, 20) + "...");
  
  // Test data
  const testData = {
    category_id: 1, // Make sure this category exists
    name: "Test Menu Item",
    description: "This is a test menu item",
    price: 15.99,
    preparation_time: 20,
    is_vegetarian: false,
    is_available: true
  };
  
  console.log("Test data:", testData);
  
  // Create FormData
  const formData = new FormData();
  formData.append("category_id", testData.category_id);
  formData.append("name", testData.name);
  formData.append("description", testData.description);
  formData.append("price", testData.price);
  formData.append("preparation_time", testData.preparation_time);
  formData.append("is_vegetarian", testData.is_vegetarian);
  formData.append("is_available", testData.is_available);
  
  console.log("FormData contents:");
  for (let [key, value] of formData.entries()) {
    console.log(`${key}:`, value, typeof value);
  }
  
  try {
    const response = await fetch(`${API_BASE_URL}/restaurant/menu-items/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${user.access_token}`
      },
      body: formData
    });
    
    console.log("Response status:", response.status);
    console.log("Response headers:", Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log("Response text:", responseText);
    
    if (response.ok) {
      console.log("✅ Success! Menu item created");
      try {
        const data = JSON.parse(responseText);
        console.log("Response data:", data);
      } catch (e) {
        console.log("Response is not JSON");
      }
    } else {
      console.error("❌ Failed to create menu item");
      try {
        const errorData = JSON.parse(responseText);
        console.error("Error details:", errorData);
      } catch (e) {
        console.error("Error response is not JSON:", responseText);
      }
    }
  } catch (error) {
    console.error("Network error:", error);
  }
};

// Run the test
console.log("Starting menu item creation test...");
testMenuItemCreation();
