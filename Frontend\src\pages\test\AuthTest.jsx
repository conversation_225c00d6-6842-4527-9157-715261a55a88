import React, { useState } from "react";
import { authApi } from "../../utils/authApi";

const AuthTest = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const addResult = (test, result) => {
    setResults(prev => [...prev, { test, result, timestamp: new Date().toLocaleTimeString() }]);
  };

  const testRegistration = async () => {
    setLoading(true);
    try {
      const testData = {
        name: "Test User",
        user_name: "testuser" + Date.now(),
        email: "<EMAIL>",
        password: "123456",
        phone: "+1234567890",
        role: "customer"
      };

      console.log("🧪 Testing registration with:", testData);
      const result = await authApi.register(testData);
      console.log("🧪 Registration result:", result);
      
      addResult("Registration", result);
    } catch (error) {
      console.error("🧪 Registration test error:", error);
      addResult("Registration", { success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testLogin = async () => {
    setLoading(true);
    try {
      const testData = {
        user_name: "testuser",
        password: "123456"
      };

      console.log("🧪 Testing login with:", testData);
      const result = await authApi.login(testData);
      console.log("🧪 Login result:", result);
      
      addResult("Login", result);
    } catch (error) {
      console.error("🧪 Login test error:", error);
      addResult("Login", { success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testEmailVerification = async () => {
    setLoading(true);
    try {
      const testData = {
        email: "<EMAIL>",
        otp: "123456"
      };

      console.log("🧪 Testing email verification with:", testData);
      const result = await authApi.verifyEmail(testData);
      console.log("🧪 Email verification result:", result);
      
      addResult("Email Verification", result);
    } catch (error) {
      console.error("🧪 Email verification test error:", error);
      addResult("Email Verification", { success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">🧪 Authentication API Test</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <button
          onClick={testRegistration}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Test Registration
        </button>
        
        <button
          onClick={testLogin}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          Test Login
        </button>
        
        <button
          onClick={testEmailVerification}
          disabled={loading}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          Test Email Verification
        </button>
      </div>

      <div className="flex gap-4 mb-6">
        <button
          onClick={clearResults}
          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
        >
          Clear Results
        </button>
      </div>

      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="mt-2">Testing...</p>
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test Results:</h2>
        {results.length === 0 ? (
          <p className="text-gray-500">No tests run yet. Click a test button above.</p>
        ) : (
          results.map((item, index) => (
            <div key={index} className="border rounded p-4 bg-gray-50">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-semibold">{item.test}</h3>
                <span className="text-sm text-gray-500">{item.timestamp}</span>
              </div>
              <div className={`p-3 rounded ${item.result.success ? 'bg-green-100' : 'bg-red-100'}`}>
                <div className="flex items-center mb-2">
                  <span className={`font-semibold ${item.result.success ? 'text-green-700' : 'text-red-700'}`}>
                    {item.result.success ? '✅ Success' : '❌ Failed'}
                  </span>
                </div>
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(item.result, null, 2)}
                </pre>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default AuthTest;
