# Menu Item CRUD API Integration Guide

This document explains how to use the Afghan Sofra Menu Item API integration in your React frontend.

## API Base URL
```
https://afghansufra.luilala.com/api/
```

## Authentication
All endpoints require Bearer token authentication. Tokens are automatically added to requests via the axios interceptor.

## Menu Item API Endpoints

### 1. Create Menu Item
**Endpoint:** `POST /restaurant/menu-items/`
**Content-Type:** `multipart/form-data`
**Authentication:** Bearer Token Required

**Request Body (FormData):**
```javascript
{
  category: 1,                    // Category ID (number)
  name: "Margherita Pizza",       // Item name (string)
  price: 12.99,                   // Price (number)
  image: File,                    // Image file (File object)
  is_vegetarian: true,            // Vegetarian flag (boolean)
  is_available: true,             // Availability flag (boolean)
  description: "Delicious pizza", // Description (string)
  preparation_time: 30            // Prep time in minutes (number)
}
```

**Example Usage:**
```javascript
import { menuItemApi } from '../utils/menuApi';

const createItem = async () => {
  const itemData = {
    category: 1,
    name: "Margherita Pizza",
    price: 12.99,
    image: selectedFile, // File object from input
    is_vegetarian: true,
    is_available: true,
    description: "Classic pizza with tomato and mozzarella",
    preparation_time: 30
  };
  
  const result = await menuItemApi.createItem(itemData);
  if (result.success) {
    console.log('Item created:', result.data);
  } else {
    console.error('Error:', result.error);
  }
};
```

### 2. Get Menu Items by Category
**Endpoint:** `GET /menu-items/?category_id={categoryId}`
**Authentication:** Bearer Token Required

**Example Usage:**
```javascript
const getItemsByCategory = async (categoryId) => {
  const result = await menuItemApi.getItemsByCategory(categoryId);
  if (result.success) {
    console.log('Items:', result.data);
  }
};
```

### 3. Get Menu Items by Restaurant
**Endpoint:** `GET /menu-items/?restaurant_id={restaurantId}`
**Authentication:** Bearer Token Required

**Example Usage:**
```javascript
const getItemsByRestaurant = async (restaurantId) => {
  const result = await menuItemApi.getItemsByRestaurant(restaurantId);
  if (result.success) {
    console.log('Items:', result.data);
  }
};
```

### 4. Get Single Menu Item
**Endpoint:** `GET /menu-items/{itemId}/`
**Authentication:** Bearer Token Required

**Example Usage:**
```javascript
const getItem = async (itemId) => {
  const result = await menuItemApi.getItem(itemId);
  if (result.success) {
    console.log('Item:', result.data);
  }
};
```

### 5. Update Menu Item (Partial)
**Endpoint:** `PATCH /menu-items/{itemId}/`
**Content-Type:** `application/json`
**Authentication:** Bearer Token Required

**Example Usage:**
```javascript
const updateItem = async (itemId) => {
  const updateData = {
    price: 13.99,
    is_available: false
  };
  
  const result = await menuItemApi.updateItem(itemId, updateData);
  if (result.success) {
    console.log('Item updated:', result.data);
  }
};
```

### 6. Delete Menu Item
**Endpoint:** `DELETE /menu-items/{itemId}/`
**Authentication:** Bearer Token Required

**Example Usage:**
```javascript
const deleteItem = async (itemId) => {
  const result = await menuItemApi.deleteItem(itemId);
  if (result.success) {
    console.log('Item deleted successfully');
  }
};
```

## Menu Item Data Structure

**Response Object:**
```javascript
{
  id: 1,
  category: 1,                    // Category ID
  name: "Margherita Pizza",
  price: "12.99",                 // String representation of decimal
  image: "https://...",           // Full URL to uploaded image
  is_vegetarian: true,
  is_available: true,
  description: "Classic pizza...",
  preparation_time: 30,
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z"
}
```

## Integration Components

### 1. MenuContext Provider
Located at `src/context/MenuContext.jsx`
- Manages menu items and categories state
- Provides CRUD operations for menu items
- Handles loading states and error management
- Automatically loads data when restaurant context changes

**Usage:**
```javascript
import { useMenu } from '../context/MenuContext';

const MyComponent = () => {
  const {
    menuItems,
    categories,
    loading,
    error,
    createMenuItem,
    updateMenuItem,
    deleteMenuItem,
    clearError
  } = useMenu();
  
  // Component logic here
};
```

### 2. MenuManagement Component
Located at `src/pages/restaurant/MenuManagement.jsx`
- Full CRUD interface for menu items
- Real-time integration with API
- Image upload support
- Category filtering and search
- Form validation and error handling

### 3. API Utility Functions
Located at `src/utils/menuApi.js`
- `menuItemApi.createItem(itemData)`
- `menuItemApi.getItemsByCategory(categoryId)`
- `menuItemApi.getItemsByRestaurant(restaurantId)`
- `menuItemApi.getItem(itemId)`
- `menuItemApi.updateItem(itemId, updateData)`
- `menuItemApi.deleteItem(itemId)`

## Testing

### API Test Component
Access the test interface at: `/admin/menu-item-api-test`

The test component allows you to:
- Test all CRUD operations
- Upload images for create operations
- View detailed API responses
- Debug authentication and network issues

### Manual Testing Steps

1. **Login as Restaurant Owner**
   - Ensure you have restaurant owner credentials
   - Login and navigate to Menu Management

2. **Test Create Operation**
   - Click "Add Menu Item"
   - Fill in all required fields
   - Upload an image
   - Submit the form

3. **Test Read Operations**
   - View the menu items list
   - Test category filtering
   - Test search functionality

4. **Test Update Operation**
   - Click edit on any menu item
   - Modify some fields
   - Submit the form

5. **Test Delete Operation**
   - Click delete on any menu item
   - Confirm deletion

## Error Handling

The API integration includes comprehensive error handling:

1. **Network Errors**: Handled by axios interceptors
2. **Authentication Errors**: Automatic token refresh and redirect
3. **Validation Errors**: Displayed in form fields
4. **File Upload Errors**: Specific handling for multipart data
5. **Server Errors**: User-friendly error messages

## File Upload Considerations

1. **Supported Formats**: PNG, JPG, GIF
2. **File Size**: Check with backend for limits
3. **Image Processing**: Backend handles resizing and optimization
4. **Error Handling**: Specific error messages for upload failures

## Best Practices

1. **Always check result.success** before proceeding
2. **Handle loading states** for better UX
3. **Clear errors** after user actions
4. **Validate file types** before upload
5. **Use proper error boundaries** for component-level errors
6. **Implement optimistic updates** where appropriate

## Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check token validity and refresh
2. **403 Forbidden**: Verify user has restaurant owner role
3. **400 Bad Request**: Check required fields and data types
4. **413 Payload Too Large**: Reduce image file size
5. **500 Server Error**: Check server logs and API status

### Debug Steps

1. Check browser console for errors
2. Verify localStorage contains valid token
3. Test API endpoints directly with tools like Postman
4. Check network tab for request/response details
5. Use the API test component for isolated testing
