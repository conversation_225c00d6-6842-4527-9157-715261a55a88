import React, { useState, useEffect } from 'react';
import { API_BASE_URL } from '../../config/api';

const ApiConnectionTest = () => {
  const [connectionStatus, setConnectionStatus] = useState('testing');
  const [apiResponse, setApiResponse] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    testApiConnection();
  }, []);

  const testApiConnection = async () => {
    try {
      setConnectionStatus('testing');
      setError(null);
      
      console.log('Testing API connection to:', API_BASE_URL);
      
      // Test a simple GET request to a public endpoint
      const response = await fetch(`${API_BASE_URL}/config/settings/public_config/`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);

      if (response.ok) {
        const data = await response.json();
        setApiResponse(data);
        setConnectionStatus('success');
        console.log('API connection successful:', data);
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (err) {
      console.error('API connection failed:', err);
      setError(err.message);
      setConnectionStatus('failed');
    }
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'testing':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'testing':
        return '🔄';
      case 'success':
        return '✅';
      case 'failed':
        return '❌';
      default:
        return '❓';
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-800 mb-4">
        API Connection Test
      </h2>
      
      <div className={`p-4 rounded-lg border ${getStatusColor()} mb-4`}>
        <div className="flex items-center gap-2 mb-2">
          <span className="text-xl">{getStatusIcon()}</span>
          <span className="font-semibold">
            Status: {connectionStatus.charAt(0).toUpperCase() + connectionStatus.slice(1)}
          </span>
        </div>
        
        <div className="text-sm">
          <p><strong>API Base URL:</strong> {API_BASE_URL}</p>
          <p><strong>Test Endpoint:</strong> /config/settings/public_config/</p>
        </div>
      </div>

      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg mb-4">
          <h3 className="font-semibold text-red-800 mb-2">Error Details:</h3>
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {apiResponse && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg mb-4">
          <h3 className="font-semibold text-green-800 mb-2">API Response:</h3>
          <pre className="text-green-700 text-xs overflow-auto bg-white p-2 rounded border">
            {JSON.stringify(apiResponse, null, 2)}
          </pre>
        </div>
      )}

      <div className="flex gap-2">
        <button
          onClick={testApiConnection}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          Test Again
        </button>
        
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
        >
          Reload Page
        </button>
      </div>

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-800 mb-2">Troubleshooting:</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Make sure the backend server is running on http://127.0.0.1:8000</li>
          <li>• Check that CORS is properly configured</li>
          <li>• Verify the .env file has the correct API_BASE_URL</li>
          <li>• Check browser console for additional error details</li>
          <li>• Ensure no firewall is blocking the connection</li>
        </ul>
      </div>
    </div>
  );
};

export default ApiConnectionTest;
