#!/usr/bin/env python3
"""
Check current restaurants and their owners
"""

import requests
import json

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"

def check_restaurants():
    """Check current restaurants"""
    
    print("🔍 Checking Current Restaurants")
    print("=" * 60)
    
    # Login as admin
    admin_login_data = {
        "user_name": "admin",
        "password": "admin123"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=headers,
            data=json.dumps(admin_login_data)
        )
        
        if response.status_code != 200:
            print(f"❌ Admin login failed: {response.text}")
            return
        
        admin_result = response.json()
        admin_token = admin_result['data']['access_token']
        
        admin_headers = {
            "Authorization": f"Bearer {admin_token}",
            "Content-Type": "application/json"
        }
        
        # Get all restaurants
        response = requests.get(
            f"{API_BASE_URL}/restaurant/restaurants/admin_restaurants/",
            headers=admin_headers
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to get restaurants: {response.text}")
            return
        
        restaurants = response.json()
        print(f"📋 Found {len(restaurants)} total restaurants")
        print()
        
        # Show restaurant details
        for i, restaurant in enumerate(restaurants[:10]):  # Show first 10
            owner_info = restaurant.get('owner', {})
            print(f"{i+1}. {restaurant.get('name', 'N/A')}")
            print(f"   Owner: {owner_info.get('name', 'N/A')} (ID: {owner_info.get('id', 'N/A')})")
            print(f"   Email: {owner_info.get('email', 'N/A')}")
            print(f"   Active: {restaurant.get('is_active', 'N/A')}")
            print(f"   Verified: {restaurant.get('is_verified', 'N/A')}")
            print(f"   Created: {restaurant.get('created_at', 'N/A')}")
            print()
        
        # Count by status
        active_count = sum(1 for r in restaurants if r.get('is_active'))
        verified_count = sum(1 for r in restaurants if r.get('is_verified'))
        pending_count = sum(1 for r in restaurants if not r.get('is_verified'))
        
        print("📊 Restaurant Status Summary:")
        print(f"   Total: {len(restaurants)}")
        print(f"   Active: {active_count}")
        print(f"   Verified: {verified_count}")
        print(f"   Pending: {pending_count}")
        
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    check_restaurants()
