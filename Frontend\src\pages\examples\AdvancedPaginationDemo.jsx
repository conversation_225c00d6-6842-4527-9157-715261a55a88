import React, { useState } from 'react';
import { Search, Filter, Zap, BarChart3, List, Grid } from 'lucide-react';
import SmartPagination from '../../components/pagination/SmartPagination';
import VirtualScrollPagination from '../../components/pagination/VirtualScrollPagination';
import CursorPagination from '../../components/pagination/CursorPagination';
import SearchPagination from '../../components/pagination/SearchPagination';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Badge from '../../components/common/Badge';
import { orderApi } from '../../utils/orderApi';
import restaurantApi from '../../utils/restaurantApi';

const AdvancedPaginationDemo = () => {
  const [activeDemo, setActiveDemo] = useState('smart');
  const [viewMode, setViewMode] = useState('grid');

  // Demo configurations
  const demos = {
    smart: {
      title: 'Smart Pagination',
      description: 'AI-powered pagination with performance optimization and predictive loading',
      icon: Zap,
      color: 'purple',
    },
    virtual: {
      title: 'Virtual Scrolling',
      description: 'High-performance virtual scrolling for large datasets',
      icon: List,
      color: 'blue',
    },
    cursor: {
      title: 'Cursor Pagination',
      description: 'Real-time cursor-based pagination for live data',
      icon: BarChart3,
      color: 'green',
    },
    search: {
      title: 'Advanced Search',
      description: 'Search-optimized pagination with filters and analytics',
      icon: Search,
      color: 'orange',
    },
  };

  // Sample data fetchers
  const fetchOrders = async (params) => {
    return await orderApi.getOrders(params);
  };

  const fetchRestaurants = async (params) => {
    return await restaurantApi.getRestaurants(params);
  };

  // Render item components
  const renderOrderItem = (order, index) => (
    <Card key={order.id} className="p-4 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold text-gray-900">Order #{order.id}</h3>
          <p className="text-sm text-gray-600">
            {order.restaurant?.name || 'Unknown Restaurant'}
          </p>
          <p className="text-xs text-gray-500">
            {new Date(order.created_at).toLocaleDateString()}
          </p>
        </div>
        <div className="text-right">
          <p className="font-semibold text-gray-900">${order.total_amount}</p>
          <Badge variant={getStatusVariant(order.status)}>
            {order.status}
          </Badge>
        </div>
      </div>
    </Card>
  );

  const renderRestaurantItem = (restaurant, index) => (
    <Card key={restaurant.id} className="p-4 hover:shadow-md transition-shadow">
      <div className="flex items-center space-x-4">
        {restaurant.logo && (
          <img
            src={restaurant.logo}
            alt={restaurant.name}
            className="w-12 h-12 rounded-lg object-cover"
          />
        )}
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900">{restaurant.name}</h3>
          <p className="text-sm text-gray-600">{restaurant.cuisine_type}</p>
          <div className="flex items-center mt-1">
            <span className="text-yellow-500">★</span>
            <span className="text-sm text-gray-600 ml-1">
              {restaurant.average_rating || 'No ratings'}
            </span>
          </div>
        </div>
        <div className="text-right">
          <Badge variant={restaurant.is_active ? 'success' : 'error'}>
            {restaurant.is_active ? 'Active' : 'Inactive'}
          </Badge>
        </div>
      </div>
    </Card>
  );

  const getStatusVariant = (status) => {
    const variants = {
      pending: 'warning',
      confirmed: 'info',
      preparing: 'info',
      ready: 'warning',
      assigned: 'info',
      picked_up: 'info',
      on_the_way: 'info',
      delivered: 'success',
      cancelled: 'error',
    };
    return variants[status] || 'default';
  };

  // Search filters for advanced search demo
  const searchFilters = [
    {
      key: 'cuisine_type',
      label: 'Cuisine Type',
      type: 'select',
      options: [
        { value: 'afghan', label: 'Afghan' },
        { value: 'italian', label: 'Italian' },
        { value: 'chinese', label: 'Chinese' },
        { value: 'indian', label: 'Indian' },
      ],
    },
    {
      key: 'rating',
      label: 'Rating',
      type: 'range',
      placeholder: 'Min rating',
    },
    {
      key: 'delivery_fee',
      label: 'Delivery Fee',
      type: 'range',
      placeholder: 'Max fee',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Advanced Pagination Demo</h1>
          <p className="text-gray-600 mt-2">
            Explore different pagination strategies and their performance characteristics
          </p>
        </div>

        {/* Demo Selector */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          {Object.entries(demos).map(([key, demo]) => {
            const IconComponent = demo.icon;
            return (
              <button
                key={key}
                onClick={() => setActiveDemo(key)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  activeDemo === key
                    ? `border-${demo.color}-500 bg-${demo.color}-50`
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center justify-center mb-2">
                  <IconComponent
                    size={24}
                    className={activeDemo === key ? `text-${demo.color}-600` : 'text-gray-400'}
                  />
                </div>
                <h3 className="font-semibold text-gray-900 text-sm">{demo.title}</h3>
                <p className="text-xs text-gray-600 mt-1">{demo.description}</p>
              </button>
            );
          })}
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">View:</span>
            <Button
              variant={viewMode === 'grid' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
              icon={<Grid size={16} />}
            >
              Grid
            </Button>
            <Button
              variant={viewMode === 'list' ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
              icon={<List size={16} />}
            >
              List
            </Button>
          </div>

          <div className="text-sm text-gray-600">
            Active Demo: <span className="font-semibold">{demos[activeDemo].title}</span>
          </div>
        </div>

        {/* Demo Content */}
        <Card className="p-6">
          {activeDemo === 'smart' && (
            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Zap className="mr-2 text-purple-600" />
                Smart Pagination Demo
              </h2>
              <p className="text-gray-600 mb-6">
                This demo shows AI-powered pagination with performance optimization, 
                predictive loading, and adaptive page sizing based on user behavior.
              </p>
              <SmartPagination
                fetchFunction={fetchOrders}
                renderItem={renderOrderItem}
                enablePrediction={true}
                enableAnalytics={true}
                className="space-y-4"
              />
            </div>
          )}

          {activeDemo === 'virtual' && (
            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <List className="mr-2 text-blue-600" />
                Virtual Scrolling Demo
              </h2>
              <p className="text-gray-600 mb-6">
                High-performance virtual scrolling for handling thousands of items 
                with smooth scrolling and minimal memory usage.
              </p>
              <VirtualScrollPagination
                items={[]} // Would be populated with large dataset
                itemHeight={100}
                containerHeight={500}
                renderItem={renderRestaurantItem}
                onLoadMore={async () => {
                  // Load more items
                }}
                hasMore={true}
                loading={false}
              />
            </div>
          )}

          {activeDemo === 'cursor' && (
            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <BarChart3 className="mr-2 text-green-600" />
                Cursor Pagination Demo
              </h2>
              <p className="text-gray-600 mb-6">
                Real-time cursor-based pagination perfect for live data feeds 
                and consistent pagination without page drift.
              </p>
              <div className="space-y-4">
                {/* Sample data display */}
                <div className="grid gap-4">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <Card key={i} className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold">Real-time Item {i}</h3>
                          <p className="text-sm text-gray-600">
                            Updated: {new Date().toLocaleTimeString()}
                          </p>
                        </div>
                        <Badge variant="success">Live</Badge>
                      </div>
                    </Card>
                  ))}
                </div>
                <CursorPagination
                  onPageChange={(params) => console.log('Page change:', params)}
                  hasNext={true}
                  hasPrevious={false}
                  nextCursor="2024-01-01T12:00:00Z"
                  pageSize={20}
                  realTimeUpdates={true}
                  lastUpdated={new Date().toISOString()}
                />
              </div>
            </div>
          )}

          {activeDemo === 'search' && (
            <div>
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Search className="mr-2 text-orange-600" />
                Advanced Search Demo
              </h2>
              <p className="text-gray-600 mb-6">
                Search-optimized pagination with advanced filtering, 
                search suggestions, and performance metrics.
              </p>
              <SearchPagination
                onSearch={(query) => console.log('Search:', query)}
                onFilter={(filters) => console.log('Filters:', filters)}
                searchPlaceholder="Search restaurants..."
                filters={searchFilters}
                results={[]} // Would be populated with search results
                pagination={{
                  currentPage: 1,
                  totalPages: 5,
                  pageSize: 15,
                  count: 75,
                  onPageChange: (page) => console.log('Page:', page),
                  onPageSizeChange: (size) => console.log('Page size:', size),
                }}
                searchMetrics={{
                  results_count: 75,
                  search_time: 150,
                  has_exact_matches: true,
                }}
                showMetrics={true}
                showFilters={true}
              />
            </div>
          )}
        </Card>

        {/* Performance Tips */}
        <Card className="mt-8 p-6 bg-blue-50 border-blue-200">
          <h3 className="font-semibold text-blue-900 mb-3">Performance Tips</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
            <div>
              <h4 className="font-medium mb-2">Smart Pagination</h4>
              <ul className="space-y-1">
                <li>• Automatically adjusts page size based on performance</li>
                <li>• Learns user behavior for predictive loading</li>
                <li>• Provides detailed analytics and insights</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Virtual Scrolling</h4>
              <ul className="space-y-1">
                <li>• Handles thousands of items efficiently</li>
                <li>• Minimal memory footprint</li>
                <li>• Smooth scrolling performance</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Cursor Pagination</h4>
              <ul className="space-y-1">
                <li>• Perfect for real-time data</li>
                <li>• No page drift issues</li>
                <li>• Consistent performance</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Search Pagination</h4>
              <ul className="space-y-1">
                <li>• Optimized for search results</li>
                <li>• Advanced filtering capabilities</li>
                <li>• Search performance metrics</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AdvancedPaginationDemo;
