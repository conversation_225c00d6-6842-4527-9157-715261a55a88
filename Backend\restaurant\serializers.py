# restaurant/serializers.py
from rest_framework import serializers
from .models import (
    Restaurant, MenuCategory, MenuItem, Address, RestaurantReview, ReviewResponse,
    MenuItemVariant, MenuItemAddon, MenuItemCustomizationGroup, MenuItemCustomizationOption,
    CuisineType, RestaurantCategory, ServiceArea, Promotion, PromotionCode
)
from users.models import User

class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user serializer for including in restaurant data"""
    class Meta:
        model = User
        fields = ['id', 'name', 'email', 'phone', 'is_verified']

class AddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = Address
        fields = '__all__'
        read_only_fields = ('user',)

class RestaurantSerializer(serializers.ModelSerializer):
    # For input (write operations)
    address = serializers.JSONField(binary=False, write_only=True)
    
    # For output (read operations)
    address_detail = AddressSerializer(source='address', read_only=True)
    owner = serializers.HiddenField(default=serializers.CurrentUserDefault())
    opening_time = serializers.TimeField(format='%H:%M:%S', input_formats=['%H:%M:%S'])
    closing_time = serializers.TimeField(format='%H:%M:%S', input_formats=['%H:%M:%S'])
    
    class Meta:
        model = Restaurant
        fields = '__all__'
        read_only_fields = ('rating', 'is_verified', 'created_at', 'updated_at')

    def create(self, validated_data):
        # Handle cuisine_type_ids if present
        cuisine_type_ids = validated_data.pop('cuisine_type_ids', None)

        # Handle many-to-many fields that might be in validated_data
        cuisine_types = validated_data.pop('cuisine_types', None)

        # Handle address
        address_data = validated_data.pop('address')
        address_data['user'] = self.context['request'].user

        # Get or create address to avoid unique constraint violations
        address, created = Address.objects.get_or_create(
            user=address_data['user'],
            street=address_data['street'],
            city=address_data['city'],
            postal_code=address_data['postal_code'],
            defaults=address_data
        )

        # Create restaurant without many-to-many fields
        restaurant = Restaurant.objects.create(address=address, **validated_data)

        # Set cuisine types if provided
        if cuisine_type_ids:
            restaurant.cuisine_types.set(cuisine_type_ids)
        elif cuisine_types:
            restaurant.cuisine_types.set(cuisine_types)

        return restaurant

    def update(self, instance, validated_data):
        address_data = validated_data.pop('address', None)
        
        if address_data:
            # Get or create address
            if hasattr(instance, 'address'):
                address_serializer = AddressSerializer(instance.address, data=address_data, partial=True)
            else:
                address_data['user'] = self.context['request'].user
                instance.address = Address.objects.create(**address_data)
            
            if hasattr(instance, 'address'):
                address_serializer.is_valid(raise_exception=True)
                address_serializer.save()
        
        return super().update(instance, validated_data)

class MenuCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = MenuCategory
        fields = '__all__'
        read_only_fields = ('restaurant',)

class MenuItemSerializer(serializers.ModelSerializer):
    category_id = serializers.IntegerField(write_only=True)
    category = serializers.CharField(source='category.name', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    image_url = serializers.SerializerMethodField()
    restaurant_id = serializers.IntegerField(source='category.restaurant.id', read_only=True)
    restaurant_name = serializers.CharField(source='category.restaurant.name', read_only=True)

    class Meta:
        model = MenuItem
        fields = ['id', 'name', 'price', 'category_id', 'category', 'category_name', 'image', 'image_url','description','is_vegetarian','is_available','preparation_time', 'restaurant_id', 'restaurant_name']
        read_only_fields = ('category', 'category_name', 'image_url', 'restaurant_id', 'restaurant_name')

    def get_image_url(self, obj):
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        return None

    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Use image_url for the image field in the response
        data['image'] = data.pop('image_url', None)
        return data

    def validate_category_id(self, value):
        if not MenuCategory.objects.filter(id=value).exists():
            raise serializers.ValidationError("Category does not exist")
        return value

    def create(self, validated_data):
        # Remove category_id from validated_data since it's handled in the view
        validated_data.pop('category_id', None)
        return MenuItem.objects.create(**validated_data)


# REVIEWS & RATINGS SERIALIZERS
class ReviewResponseSerializer(serializers.ModelSerializer):
    restaurant_owner_name = serializers.CharField(source='restaurant_owner.name', read_only=True)

    class Meta:
        model = ReviewResponse
        fields = ['id', 'message', 'restaurant_owner_name', 'created_at', 'updated_at']
        read_only_fields = ('restaurant_owner', 'created_at', 'updated_at')

class RestaurantReviewSerializer(serializers.ModelSerializer):
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    response = ReviewResponseSerializer(read_only=True)

    class Meta:
        model = RestaurantReview
        fields = [
            'id', 'rating', 'title', 'comment',
            'food_rating', 'service_rating', 'delivery_rating',
            'customer_name', 'is_verified', 'created_at', 'updated_at',
            'response'
        ]
        read_only_fields = ('customer', 'restaurant', 'is_verified', 'created_at', 'updated_at')

    def validate_rating(self, value):
        if not (1 <= value <= 5):
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value

    def validate_food_rating(self, value):
        if value is not None and not (1 <= value <= 5):
            raise serializers.ValidationError("Food rating must be between 1 and 5")
        return value

    def validate_service_rating(self, value):
        if value is not None and not (1 <= value <= 5):
            raise serializers.ValidationError("Service rating must be between 1 and 5")
        return value

    def validate_delivery_rating(self, value):
        if value is not None and not (1 <= value <= 5):
            raise serializers.ValidationError("Delivery rating must be between 1 and 5")
        return value

class RestaurantWithReviewsSerializer(RestaurantSerializer):
    """Restaurant serializer with review statistics"""
    review_stats = serializers.SerializerMethodField()
    recent_reviews = serializers.SerializerMethodField()

    class Meta(RestaurantSerializer.Meta):
        fields = '__all__'

    def get_review_stats(self, obj):
        return obj.get_review_stats()

    def get_recent_reviews(self, obj):
        recent_reviews = obj.reviews.filter(is_approved=True).order_by('-created_at')[:3]
        return RestaurantReviewSerializer(recent_reviews, many=True).data


# MENU ITEM VARIANTS & CUSTOMIZATIONS SERIALIZERS
class MenuItemVariantSerializer(serializers.ModelSerializer):
    final_price = serializers.ReadOnlyField()

    class Meta:
        model = MenuItemVariant
        fields = ['id', 'name', 'price_adjustment', 'final_price', 'is_default', 'is_available', 'created_at']
        read_only_fields = ('created_at',)

class MenuItemAddonSerializer(serializers.ModelSerializer):
    class Meta:
        model = MenuItemAddon
        fields = [
            'id', 'name', 'description', 'price', 'addon_type',
            'is_available', 'max_quantity', 'created_at'
        ]
        read_only_fields = ('created_at',)

class MenuItemCustomizationOptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = MenuItemCustomizationOption
        fields = [
            'id', 'name', 'price_adjustment', 'is_default',
            'is_available', 'display_order'
        ]

class MenuItemCustomizationGroupSerializer(serializers.ModelSerializer):
    options = MenuItemCustomizationOptionSerializer(many=True, read_only=True)

    class Meta:
        model = MenuItemCustomizationGroup
        fields = [
            'id', 'name', 'description', 'is_required',
            'min_selections', 'max_selections', 'display_order', 'options'
        ]

class MenuItemDetailSerializer(MenuItemSerializer):
    """Enhanced menu item serializer with variants and customizations"""
    variants = MenuItemVariantSerializer(many=True, read_only=True)
    addons = MenuItemAddonSerializer(many=True, read_only=True)
    customization_groups = MenuItemCustomizationGroupSerializer(many=True, read_only=True)
    restaurant_id = serializers.IntegerField(source='category.restaurant.id', read_only=True)
    restaurant_name = serializers.CharField(source='category.restaurant.name', read_only=True)

    class Meta(MenuItemSerializer.Meta):
        fields = MenuItemSerializer.Meta.fields + ['variants', 'addons', 'customization_groups', 'restaurant_id', 'restaurant_name']


# RESTAURANT BUSINESS DETAILS SERIALIZERS
class CuisineTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = CuisineType
        fields = ['id', 'name', 'description', 'icon', 'is_active']

class RestaurantCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = RestaurantCategory
        fields = ['id', 'name', 'description', 'icon', 'is_active']

class ServiceAreaSerializer(serializers.ModelSerializer):
    postal_codes_list = serializers.ReadOnlyField(source='get_postal_codes_list')

    class Meta:
        model = ServiceArea
        fields = [
            'id', 'area_name', 'postal_codes', 'postal_codes_list',
            'delivery_fee', 'min_order_amount', 'estimated_delivery_time',
            'is_active', 'created_at'
        ]
        read_only_fields = ('created_at',)

class RestaurantDetailSerializer(RestaurantSerializer):
    """Enhanced restaurant serializer with business details"""
    cuisine_types = CuisineTypeSerializer(many=True, read_only=True)
    restaurant_category = RestaurantCategorySerializer(read_only=True)
    service_areas = ServiceAreaSerializer(many=True, read_only=True)

    # Add cuisine_type_ids and category_id for write operations
    cuisine_type_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    category_id = serializers.IntegerField(write_only=True, required=False)

    class Meta(RestaurantSerializer.Meta):
        fields = '__all__'

    def update(self, instance, validated_data):
        # Handle cuisine types
        cuisine_type_ids = validated_data.pop('cuisine_type_ids', None)
        if cuisine_type_ids is not None:
            cuisine_types = CuisineType.objects.filter(id__in=cuisine_type_ids)
            instance.cuisine_types.set(cuisine_types)

        # Handle restaurant category
        category_id = validated_data.pop('category_id', None)
        if category_id is not None:
            try:
                category = RestaurantCategory.objects.get(id=category_id)
                instance.restaurant_category = category
            except RestaurantCategory.DoesNotExist:
                pass

        return super().update(instance, validated_data)

class RestaurantAdminSerializer(RestaurantDetailSerializer):
    """Restaurant serializer for admin use - includes owner information"""
    owner = UserBasicSerializer(read_only=True)

    class Meta(RestaurantDetailSerializer.Meta):
        fields = '__all__'


# PROMOTIONS & DISCOUNTS SERIALIZERS
class PromotionCodeSerializer(serializers.ModelSerializer):
    can_be_used = serializers.SerializerMethodField()

    class Meta:
        model = PromotionCode
        fields = ['id', 'code', 'usage_count', 'is_active', 'can_be_used', 'created_at']
        read_only_fields = ('usage_count', 'created_at')

    def get_can_be_used(self, obj):
        request = self.context.get('request')
        customer = request.user if request and hasattr(request, 'user') else None
        return obj.can_be_used(customer)

class PromotionSerializer(serializers.ModelSerializer):
    codes = PromotionCodeSerializer(many=True, read_only=True)
    is_valid_now = serializers.ReadOnlyField()

    class Meta:
        model = Promotion
        fields = [
            'id', 'name', 'description', 'promotion_type', 'discount_applies_to',
            'discount_percentage', 'discount_amount', 'min_order_amount', 'max_discount_amount',
            'usage_limit', 'usage_limit_per_customer', 'current_usage_count',
            'start_date', 'end_date',
            'valid_on_monday', 'valid_on_tuesday', 'valid_on_wednesday', 'valid_on_thursday',
            'valid_on_friday', 'valid_on_saturday', 'valid_on_sunday',
            'valid_from_time', 'valid_to_time',
            'is_active', 'is_featured', 'is_valid_now', 'codes',
            'created_at', 'updated_at'
        ]
        read_only_fields = ('current_usage_count', 'created_at', 'updated_at')

    def validate(self, data):
        """Validate promotion data"""
        promotion_type = data.get('promotion_type')

        if promotion_type == 'percentage':
            if not data.get('discount_percentage'):
                raise serializers.ValidationError("Percentage discount requires discount_percentage")
            if data.get('discount_percentage', 0) <= 0 or data.get('discount_percentage', 0) > 100:
                raise serializers.ValidationError("Discount percentage must be between 1 and 100")

        elif promotion_type == 'fixed_amount':
            if not data.get('discount_amount'):
                raise serializers.ValidationError("Fixed amount discount requires discount_amount")
            if data.get('discount_amount', 0) <= 0:
                raise serializers.ValidationError("Discount amount must be greater than 0")

        # Validate date range
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        if start_date and end_date and start_date >= end_date:
            raise serializers.ValidationError("End date must be after start date")

        # Validate time range
        valid_from_time = data.get('valid_from_time')
        valid_to_time = data.get('valid_to_time')
        if valid_from_time and valid_to_time and valid_from_time >= valid_to_time:
            raise serializers.ValidationError("Valid to time must be after valid from time")

        return data

class PromotionCreateSerializer(PromotionSerializer):
    """Serializer for creating promotions with codes"""
    promotion_codes = serializers.ListField(
        child=serializers.CharField(max_length=50),
        write_only=True,
        required=False,
        help_text="List of promotion codes to create"
    )

    class Meta(PromotionSerializer.Meta):
        fields = PromotionSerializer.Meta.fields + ['promotion_codes']

    def create(self, validated_data):
        promotion_codes = validated_data.pop('promotion_codes', [])
        promotion = super().create(validated_data)

        # Create promotion codes
        for code in promotion_codes:
            PromotionCode.objects.create(
                promotion=promotion,
                code=code.upper().strip()
            )

        return promotion