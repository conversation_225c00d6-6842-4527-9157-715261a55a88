import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import {
  ChevronDown,
  Plus,
  Store,
  Shield,
  ShieldCheck,
  AlertCircle,
} from "lucide-react";
import Button from "../common/Button";
import Card from "../common/Card";
import Badge from "../common/Badge";

const RestaurantSelector = ({ onRestaurantSelect, selectedRestaurant }) => {
  const { user } = useAuth();
  const { restaurants, getRestaurants, loading } = useRestaurant();
  const [isOpen, setIsOpen] = useState(false);
  const [userRestaurants, setUserRestaurants] = useState([]);

  // Fetch restaurants owned by current user
  useEffect(() => {
    const fetchUserRestaurants = async () => {
      if (user && user.role === "restaurant") {
        try {
          const result = await getRestaurants();
          if (result.success) {
            // Filter restaurants owned by current user
            const ownedRestaurants = result.data.filter(
              (r) => r.owner === user.id
            );
            setUserRestaurants(ownedRestaurants);

            // Auto-select first restaurant if none selected
            if (!selectedRestaurant && ownedRestaurants.length > 0) {
              onRestaurantSelect(ownedRestaurants[0]);
            }
          }
        } catch (error) {
          console.error("Error fetching restaurants:", error);
        }
      }
    };

    fetchUserRestaurants();
  }, [user, getRestaurants, selectedRestaurant, onRestaurantSelect]);

  const getVerificationBadge = (restaurant) => {
    if (restaurant.is_verified) {
      return (
        <Badge variant='success' size='small' className='ml-2'>
          <ShieldCheck size={12} className='mr-1' />
          Verified
        </Badge>
      );
    }
    return (
      <Badge variant='warning' size='small' className='ml-2'>
        <AlertCircle size={12} className='mr-1' />
        Pending
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className='flex items-center space-x-2 p-3 bg-gray-50 rounded-lg'>
        <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-primary-500'></div>
        <span className='text-sm text-gray-600'>Loading restaurants...</span>
      </div>
    );
  }

  // Show add restaurant prompt if no restaurants
  if (userRestaurants.length === 0) {
    return (
      <Card className='p-6 text-center'>
        <div className='w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4'>
          <Store className='text-primary-600' size={32} />
        </div>
        <h3 className='text-lg font-semibold mb-2'>No Restaurants Yet</h3>
        <p className='text-gray-600 mb-4'>
          Create your first restaurant to start managing menus and orders.
        </p>
        <Button
          variant='primary'
          icon={<Plus size={18} />}
          to='/add-restaurant'
        >
          Add Your First Restaurant
        </Button>
      </Card>
    );
  }

  return (
    <div className='relative'>
      {/* Restaurant Selector Dropdown */}
      <div className='relative'>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className='w-full flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent'
        >
          <div className='flex items-center space-x-3'>
            {selectedRestaurant ? (
              <>
                <div className='w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center'>
                  <Store className='text-primary-600' size={16} />
                </div>
                <div className='text-left'>
                  <div className='flex items-center'>
                    <span className='font-medium text-gray-900'>
                      {selectedRestaurant.name}
                    </span>
                    {getVerificationBadge(selectedRestaurant)}
                  </div>
                  <span className='text-sm text-gray-500'>
                    {selectedRestaurant.address?.street || "No address"}
                  </span>
                </div>
              </>
            ) : (
              <span className='text-gray-500'>Select a restaurant</span>
            )}
          </div>
          <ChevronDown
            className={`text-gray-400 transition-transform ${
              isOpen ? "rotate-180" : ""
            }`}
            size={20}
          />
        </button>

        {/* Dropdown Menu */}
        {isOpen && (
          <div className='absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto'>
            {userRestaurants.map((restaurant) => (
              <button
                key={restaurant.id}
                onClick={() => {
                  onRestaurantSelect(restaurant);
                  setIsOpen(false);
                }}
                className={`w-full flex items-center space-x-3 p-3 hover:bg-gray-50 transition-colors ${
                  selectedRestaurant?.id === restaurant.id
                    ? "bg-primary-50 border-r-2 border-primary-500"
                    : ""
                }`}
              >
                <div className='w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center'>
                  <Store className='text-primary-600' size={16} />
                </div>
                <div className='flex-1 text-left'>
                  <div className='flex items-center'>
                    <span className='font-medium text-gray-900'>
                      {restaurant.name}
                    </span>
                    {getVerificationBadge(restaurant)}
                  </div>
                  <span className='text-sm text-gray-500'>
                    {restaurant.address?.street || "No address"}
                  </span>
                </div>
              </button>
            ))}

            {/* Add New Restaurant Option */}
            <div className='border-t border-gray-100'>
              <button
                onClick={() => {
                  setIsOpen(false);
                  // Navigate to add restaurant page
                  window.location.href = "/add-restaurant";
                }}
                className='w-full flex items-center space-x-3 p-3 hover:bg-gray-50 transition-colors text-primary-600'
              >
                <div className='w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center'>
                  <Plus className='text-primary-600' size={16} />
                </div>
                <span className='font-medium'>Add New Restaurant</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Restaurant Count Info */}
      <div className='mt-2 text-xs text-gray-500 text-center'>
        {userRestaurants.length} restaurant
        {userRestaurants.length !== 1 ? "s" : ""} •
        {userRestaurants.filter((r) => r.is_verified).length} verified
      </div>
    </div>
  );
};

export default RestaurantSelector;
