import React, { useState } from 'react';
import { Search, Filter, MoreVertical, Eye } from 'lucide-react';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Badge from '../../components/common/Badge';

function ManageOrders() {
  const [selectedStatus, setSelectedStatus] = useState('all');
  
  return (
    <div className="p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <h1 className="text-2xl font-bold">Manage Orders</h1>
      </div>
      
      <Card>
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search orders..."
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex gap-4">
            <select 
              className="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="preparing">Preparing</option>
              <option value="delivering">Delivering</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
            
            <Button
              variant="secondary"
              icon={<Filter size={18} />}
            >
              Filters
            </Button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-4 px-4 font-semibold">Order ID</th>
                <th className="text-left py-4 px-4 font-semibold">Customer</th>
                <th className="text-left py-4 px-4 font-semibold">Restaurant</th>
                <th className="text-left py-4 px-4 font-semibold">Status</th>
                <th className="text-left py-4 px-4 font-semibold">Total</th>
                <th className="text-left py-4 px-4 font-semibold">Date</th>
                <th className="text-right py-4 px-4 font-semibold">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b hover:bg-gray-50">
                <td className="py-4 px-4">
                  <span className="font-medium">#12345</span>
                </td>
                <td className="py-4 px-4">
                  <p>John Doe</p>
                  <p className="text-sm text-text-secondary"><EMAIL></p>
                </td>
                <td className="py-4 px-4">
                  <p>Kabul Kitchen</p>
                  <p className="text-sm text-text-secondary">Afghan Cuisine</p>
                </td>
                <td className="py-4 px-4">
                  <Badge variant="success" size="small">Delivered</Badge>
                </td>
                <td className="py-4 px-4">
                  <span className="font-medium">$45.99</span>
                </td>
                <td className="py-4 px-4">
                  <p>Jan 15, 2024</p>
                  <p className="text-sm text-text-secondary">2:30 PM</p>
                </td>
                <td className="py-4 px-4">
                  <div className="flex justify-end gap-2">
                    <button className="p-1 hover:text-primary-500">
                      <Eye size={18} />
                    </button>
                    <button className="p-1">
                      <MoreVertical size={18} />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div className="mt-4 flex items-center justify-between">
          <p className="text-sm text-text-secondary">
            Showing 1 of 1 orders
          </p>
          <div className="flex gap-2">
            <Button variant="secondary" disabled>Previous</Button>
            <Button variant="secondary" disabled>Next</Button>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default ManageOrders;