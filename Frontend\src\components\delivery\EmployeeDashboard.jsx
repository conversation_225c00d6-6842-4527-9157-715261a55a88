import React, { useState, useEffect } from 'react';
import {
  Clock,
  LogIn,
  LogOut,
  Coffee,
  UserCheck,
  Package,
  DollarSign,
  Star,
  TrendingUp,
  MapPin,
  Phone,
  Calendar,
  Timer,
  Activity,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import Card from '../common/Card';
import Button from '../common/Button';
import Badge from '../common/Badge';
import { deliveryAgentApi } from '../../services/deliveryAgentApi';
import { useAuth } from '../../context/AuthContext';

const EmployeeDashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [clockLoading, setClockLoading] = useState(false);
  const [dashboardData, setDashboardData] = useState(null);
  const [error, setError] = useState(null);
  
  // Employee status
  const [isClocked, setIsClocked] = useState(false);
  const [employeeStatus, setEmployeeStatus] = useState('offline');
  const [shiftStartTime, setShiftStartTime] = useState(null);
  const [currentShiftHours, setCurrentShiftHours] = useState(0);

  useEffect(() => {
    loadDashboardData();
    // Refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await deliveryAgentApi.getDashboard();
      
      if (result.success) {
        setDashboardData(result.data);
        setIsClocked(result.data.agent_info?.is_clocked_in || false);
        setEmployeeStatus(result.data.agent_info?.availability || 'offline');
        setShiftStartTime(result.data.agent_info?.last_clock_in);
        setCurrentShiftHours(result.data.agent_info?.current_shift_hours || 0);
      } else {
        setError(result.error?.message || 'Failed to load dashboard data');
      }
    } catch (err) {
      console.error('Dashboard load error:', err);
      setError('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleClockIn = async () => {
    try {
      setClockLoading(true);
      const result = await deliveryAgentApi.clockIn();
      
      if (result.success) {
        setIsClocked(true);
        setShiftStartTime(result.data.clock_in_time);
        setEmployeeStatus(result.data.availability);
        await loadDashboardData();
      } else {
        setError(result.error?.message || 'Failed to clock in');
      }
    } catch (err) {
      console.error('Clock in error:', err);
      setError('Failed to clock in');
    } finally {
      setClockLoading(false);
    }
  };

  const handleClockOut = async () => {
    try {
      setClockLoading(true);
      const result = await deliveryAgentApi.clockOut();
      
      if (result.success) {
        setIsClocked(false);
        setShiftStartTime(null);
        setCurrentShiftHours(result.data.shift_hours);
        setEmployeeStatus('offline');
        await loadDashboardData();
      } else {
        setError(result.error?.message || 'Failed to clock out');
      }
    } catch (err) {
      console.error('Clock out error:', err);
      setError('Failed to clock out');
    } finally {
      setClockLoading(false);
    }
  };

  const handleSetBreak = async () => {
    try {
      const result = await deliveryAgentApi.setBreak();
      
      if (result.success) {
        setEmployeeStatus('break');
        await loadDashboardData();
      } else {
        setError(result.error?.message || 'Failed to set break');
      }
    } catch (err) {
      console.error('Set break error:', err);
      setError('Failed to set break');
    }
  };

  const handleReturnFromBreak = async () => {
    try {
      const result = await deliveryAgentApi.returnFromBreak();
      
      if (result.success) {
        setEmployeeStatus('available');
        await loadDashboardData();
      } else {
        setError(result.error?.message || 'Failed to return from break');
      }
    } catch (err) {
      console.error('Return from break error:', err);
      setError('Failed to return from break');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      available: { variant: 'success', label: 'Available', icon: CheckCircle },
      busy: { variant: 'warning', label: 'On Delivery', icon: Package },
      break: { variant: 'info', label: 'On Break', icon: Coffee },
      offline: { variant: 'secondary', label: 'Off Duty', icon: Clock }
    };
    
    const config = statusConfig[status] || statusConfig.offline;
    const IconComponent = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center">
        <IconComponent size={14} className="mr-1" />
        {config.label}
      </Badge>
    );
  };

  const formatTime = (timeString) => {
    if (!timeString) return '--:--';
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (hours) => {
    const h = Math.floor(hours);
    const m = Math.floor((hours - h) * 60);
    return `${h}h ${m}m`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Welcome, {user?.name || 'Employee'}
              </h1>
              <p className="text-gray-600 mt-1">
                Employee ID: {dashboardData?.agent_info?.agent_id || 'N/A'}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {getStatusBadge(employeeStatus)}
              <div className="text-right">
                <p className="text-sm text-gray-600">Current Time</p>
                <p className="text-lg font-semibold">
                  {new Date().toLocaleTimeString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={16} />
            <span className="text-red-700">{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        )}

        {/* Clock In/Out Section */}
        <Card className="mb-8">
          <div className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <Clock className="mr-2" size={24} />
              Time Management
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Clock Status */}
              <div className="text-center">
                <div className="mb-4">
                  {isClocked ? (
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                      <UserCheck className="text-green-600" size={32} />
                    </div>
                  ) : (
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
                      <Clock className="text-gray-600" size={32} />
                    </div>
                  )}
                  <p className="text-lg font-semibold">
                    {isClocked ? 'Clocked In' : 'Clocked Out'}
                  </p>
                  <p className="text-sm text-gray-600">
                    {isClocked ? `Since ${formatTime(shiftStartTime)}` : 'Not on duty'}
                  </p>
                </div>
                
                <div className="space-y-2">
                  {!isClocked ? (
                    <Button
                      onClick={handleClockIn}
                      loading={clockLoading}
                      icon={<LogIn size={16} />}
                      className="w-full"
                    >
                      Clock In
                    </Button>
                  ) : (
                    <Button
                      onClick={handleClockOut}
                      loading={clockLoading}
                      variant="outline"
                      icon={<LogOut size={16} />}
                      className="w-full"
                    >
                      Clock Out
                    </Button>
                  )}
                </div>
              </div>

              {/* Shift Information */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-4">Shift Information</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Start Time:</span>
                    <span className="font-medium">{formatTime(shiftStartTime)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Current Hours:</span>
                    <span className="font-medium">{formatDuration(currentShiftHours)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className="font-medium capitalize">{employeeStatus}</span>
                  </div>
                </div>
              </div>

              {/* Break Management */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-4">Break Management</h3>
                <div className="space-y-2">
                  {isClocked && employeeStatus === 'available' && (
                    <Button
                      onClick={handleSetBreak}
                      variant="outline"
                      icon={<Coffee size={16} />}
                      className="w-full"
                    >
                      Take Break
                    </Button>
                  )}
                  
                  {isClocked && employeeStatus === 'break' && (
                    <Button
                      onClick={handleReturnFromBreak}
                      icon={<Activity size={16} />}
                      className="w-full"
                    >
                      Return from Break
                    </Button>
                  )}
                  
                  {!isClocked && (
                    <p className="text-sm text-gray-500 text-center py-4">
                      Clock in to manage breaks
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Today's Performance */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Today's Deliveries</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardData?.today_stats?.completed_orders || 0}
                  </p>
                </div>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-green-100 rounded-lg">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Today's Earnings</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardData?.today_stats?.earnings?.toFixed(0) || '0'} AFN
                  </p>
                </div>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-yellow-100 rounded-lg">
                  <Star className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Rating</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardData?.agent_info?.rating?.toFixed(1) || '0.0'}
                  </p>
                </div>
              </div>
            </div>
          </Card>
          
          <Card>
            <div className="p-6">
              <div className="flex items-center">
                <div className="p-3 bg-purple-100 rounded-lg">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dashboardData?.agent_info?.completion_rate?.toFixed(0) || '0'}%
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Current Assignment */}
        {dashboardData?.current_order && (
          <Card>
            <div className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Package className="mr-2" size={24} />
                Current Assignment
              </h2>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-blue-900">
                    Order #{dashboardData.current_order.id}
                  </h3>
                  <Badge variant="info">In Progress</Badge>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-blue-700 font-medium">Restaurant:</p>
                    <p className="text-blue-900">{dashboardData.current_order.restaurant_name}</p>
                  </div>
                  <div>
                    <p className="text-blue-700 font-medium">Customer:</p>
                    <p className="text-blue-900">{dashboardData.current_order.customer_name}</p>
                  </div>
                  <div>
                    <p className="text-blue-700 font-medium">Delivery Address:</p>
                    <p className="text-blue-900">{dashboardData.current_order.delivery_address}</p>
                  </div>
                  <div>
                    <p className="text-blue-700 font-medium">Order Value:</p>
                    <p className="text-blue-900">{dashboardData.current_order.total_amount} AFN</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default EmployeeDashboard;
