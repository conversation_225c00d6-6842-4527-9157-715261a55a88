import requests
import json

def test_simple_admin_api():
    """Simple test of the admin assignment API"""
    
    print("🔍 Simple Admin Assignment API Test")
    print("=" * 50)
    
    # Login as admin first
    login_url = "http://127.0.0.1:8000/api/auth/login/"
    login_data = {
        "user_name": "admin_user_3602",
        "password": "admin123"
    }
    
    print("1. Logging in as admin...")
    login_response = requests.post(login_url, json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Admin login failed: {login_response.status_code}")
        return
    
    login_result = login_response.json()
    if not login_result.get('success'):
        print(f"❌ Admin login failed: {login_result}")
        return
    
    token = login_result['data']['access_token']
    print("✅ Admin login successful")
    
    # Test the admin assignment API
    assignment_url = "http://127.0.0.1:8000/api/delivery-agent/admin/assignments/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\n2. Testing admin assignment API...")
    assignment_response = requests.get(assignment_url, headers=headers)
    
    print(f"Status Code: {assignment_response.status_code}")
    
    if assignment_response.status_code == 200:
        try:
            data = assignment_response.json()
            print("✅ API working - JSON response received")
            
            if data.get('status') == 'success':
                assignment_data = data.get('data', {})
                ready_orders = assignment_data.get('ready_orders', [])
                available_agents = assignment_data.get('available_agents', [])
                
                print(f"📦 Ready orders found: {len(ready_orders)}")
                print(f"👥 Available agents: {len(available_agents)}")
                
                # Look for Order #65
                order_65_found = False
                for order in ready_orders:
                    if order.get('id') == 65:
                        order_65_found = True
                        print(f"\n✅ Order #65 FOUND in API response!")
                        print(f"   Customer: {order.get('customer_name')}")
                        print(f"   Restaurant: {order.get('restaurant_name')}")
                        print(f"   Amount: ${order.get('total_amount')}")
                        break
                
                if not order_65_found:
                    print(f"\n❌ Order #65 NOT found in API response")
                    if ready_orders:
                        print(f"Sample orders found:")
                        for order in ready_orders[:3]:  # Show first 3
                            print(f"   Order #{order.get('id')}: {order.get('customer_name')} - ${order.get('total_amount')}")
                    else:
                        print("   No ready orders found at all")
            else:
                print(f"❌ API returned error status: {data.get('status')}")
                print(f"   Message: {data.get('message', 'No message')}")
        except json.JSONDecodeError:
            print("❌ API returned non-JSON response")
            print(f"Response preview: {assignment_response.text[:200]}...")
    else:
        print(f"❌ API failed with status {assignment_response.status_code}")
        if assignment_response.status_code == 500:
            print("   This is a server error - check Django logs")
        elif assignment_response.status_code == 404:
            print("   URL not found - check URL routing")
        elif assignment_response.status_code == 403:
            print("   Permission denied - check admin authentication")

if __name__ == "__main__":
    test_simple_admin_api()
