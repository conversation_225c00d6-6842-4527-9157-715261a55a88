import React, { useState, useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import {
  ShoppingCart,
  Menu,
  X,
  User,
  Bell,
  LogOut,
  Heart,
  Calendar,
  HelpCircle,
  Users,
} from "lucide-react";
import { useAuth } from "../context/AuthContext";
import { useCart } from "../context/CartContext";
import NetworkStatus from "../components/error/NetworkStatus";
import BottomNavigation from "../components/mobile/BottomNavigation";
import { Link } from "react-router-dom";
import Button from "../components/common/Button";
import InstallBanner from "../components/pwa/InstallBanner";
import OfflineIndicator from "../components/pwa/OfflineIndicator";
import NotificationManager from "../components/pwa/NotificationManager";
import SearchBox from "../components/search/SearchBox";
import ChatWidget from "../components/support/ChatWidget";
import {
  useSiteName,
  useSiteDescription,
  useContactEmail,
  useContactPhone,
} from "../hooks/useConfig";

const MainLayout = () => {
  const { user, logout, isAuthenticated } = useAuth();
  const { cart, itemCount } = useCart();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  // Dynamic configuration
  const { value: siteName } = useSiteName();
  const { value: siteDescription } = useSiteDescription();
  const { value: contactEmail } = useContactEmail();
  const { value: contactPhone } = useContactPhone();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  useEffect(() => {
    setMobileMenuOpen(false);
    setUserMenuOpen(false);
  }, [location.pathname]);

  const handleLogout = () => {
    logout();
    setUserMenuOpen(false);
  };

  return (
    <div className='min-h-screen flex flex-col bg-background-light'>
      {/* PWA Components */}
      <NetworkStatus />
      <OfflineIndicator />
      <NotificationManager />
      <InstallBanner />
      <ChatWidget />
      {/* Header */}
      <header
        className={`sticky top-0 z-30 transition-all duration-300 ${
          isScrolled ? "bg-white shadow-nav" : "bg-transparent"
        }`}
      >
        <div className='container mx-auto px-4 py-4 flex justify-between items-center'>
          {/* Logo */}
          <Link to='/' className='flex items-center'>
            <span className='text-primary-500 font-poppins font-bold text-xl md:text-2xl'>
              {siteName || "Afghan Sufra"}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className='hidden md:flex items-center space-x-6'>
            <Link
              to='/'
              className='font-medium text-text-primary hover:text-primary-500 transition-colors'
            >
              Home
            </Link>
            <Link
              to='/restaurants'
              className='font-medium text-text-primary hover:text-primary-500 transition-colors'
            >
              Restaurants
            </Link>

            {isAuthenticated && (
              <>
                <Link
                  to='/orders'
                  className='font-medium text-text-primary hover:text-primary-500 transition-colors'
                >
                  My Orders
                </Link>
                <Link
                  to='/favorites'
                  className='font-medium text-text-primary hover:text-primary-500 transition-colors'
                >
                  Favorites
                </Link>
              </>
            )}
          </nav>

          {/* Desktop Search */}
          <div className='hidden lg:block flex-1 max-w-md mx-6'>
            <SearchBox placeholder='Search restaurants, dishes...' />
          </div>

          {/* Desktop Cart & User */}
          <div className='hidden md:flex items-center space-x-4'>
            <Link
              to='/cart'
              className='relative p-2 text-text-primary hover:text-primary-500 transition-colors'
            >
              <ShoppingCart size={20} />
              {itemCount > 0 && (
                <span className='absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center'>
                  {itemCount}
                </span>
              )}
            </Link>

            {isAuthenticated ? (
              <div className='relative'>
                <button
                  className='flex items-center space-x-2 focus:outline-none'
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                >
                  <div className='w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center overflow-hidden'>
                    {user.avatar ? (
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className='w-full h-full object-cover'
                      />
                    ) : (
                      <User size={18} className='text-primary-500' />
                    )}
                  </div>
                </button>

                {userMenuOpen && (
                  <div className='absolute right-0 mt-2 w-48 py-2 bg-white rounded-lg shadow-lg z-50 animate-fade-in'>
                    <div className='px-4 py-2 border-b border-gray-100'>
                      <p className='text-sm font-medium'>{user.name}</p>
                      <p className='text-xs text-text-secondary'>
                        {user.email}
                      </p>
                    </div>
                    <Link
                      to='/profile'
                      className='block px-4 py-2 text-sm text-text-primary hover:bg-gray-50'
                      onClick={() => setUserMenuOpen(false)}
                    >
                      Profile
                    </Link>
                    <Link
                      to='/orders'
                      className='block px-4 py-2 text-sm text-text-primary hover:bg-gray-50'
                      onClick={() => setUserMenuOpen(false)}
                    >
                      My Orders
                    </Link>
                    <Link
                      to='/favorites'
                      className='flex items-center px-4 py-2 text-sm text-text-primary hover:bg-gray-50'
                      onClick={() => setUserMenuOpen(false)}
                    >
                      <Heart size={16} className='mr-2' />
                      Favorites
                    </Link>
                    <Link
                      to='/scheduled-orders'
                      className='flex items-center px-4 py-2 text-sm text-text-primary hover:bg-gray-50'
                      onClick={() => setUserMenuOpen(false)}
                    >
                      <Calendar size={16} className='mr-2' />
                      Scheduled Orders
                    </Link>
                    <Link
                      to='/social'
                      className='flex items-center px-4 py-2 text-sm text-text-primary hover:bg-gray-50'
                      onClick={() => setUserMenuOpen(false)}
                    >
                      <Users size={16} className='mr-2' />
                      Social Hub
                    </Link>
                    <Link
                      to='/support'
                      className='flex items-center px-4 py-2 text-sm text-text-primary hover:bg-gray-50'
                      onClick={() => setUserMenuOpen(false)}
                    >
                      <HelpCircle size={16} className='mr-2' />
                      Help & Support
                    </Link>
                    {user.role !== "customer" && (
                      <Link
                        to={`/${user.role}`}
                        className='block px-4 py-2 text-sm text-text-primary hover:bg-gray-50'
                        onClick={() => setUserMenuOpen(false)}
                      >
                        Dashboard
                      </Link>
                    )}
                    {user.role === "customer" && (
                      <Link
                        to='/register-restaurant'
                        className='block px-4 py-2 text-sm text-primary-500 hover:bg-gray-50'
                        onClick={() => setUserMenuOpen(false)}
                      >
                        Register Your Restaurant
                      </Link>
                    )}
                    <button
                      className='w-full text-left px-4 py-2 text-sm text-accent-red hover:bg-gray-50 flex items-center'
                      onClick={handleLogout}
                    >
                      <LogOut size={16} className='mr-2' />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Button to='/login' variant='primary' size='small'>
                Login
              </Button>
            )}
          </div>

          {/* Mobile Nav Toggle */}
          <div className='flex items-center space-x-4 md:hidden'>
            <Link to='/cart' className='relative p-2 text-text-primary'>
              <ShoppingCart size={20} />
              {itemCount > 0 && (
                <span className='absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center'>
                  {itemCount}
                </span>
              )}
            </Link>

            <button
              className='p-2 text-text-primary focus:outline-none'
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className='md:hidden bg-white border-t border-gray-100 shadow-nav animate-slide-down'>
            <div className='container mx-auto px-4 py-4 space-y-4'>
              {/* Mobile Search */}
              <div className='lg:hidden'>
                <SearchBox placeholder='Search restaurants, dishes...' />
              </div>
              <Link
                to='/'
                className='block py-2 font-medium text-text-primary'
                onClick={() => setMobileMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                to='/restaurants'
                className='block py-2 font-medium text-text-primary'
                onClick={() => setMobileMenuOpen(false)}
              >
                Restaurants
              </Link>

              {isAuthenticated ? (
                <>
                  <Link
                    to='/orders'
                    className='block py-2 font-medium text-text-primary'
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    My Orders
                  </Link>
                  <Link
                    to='/favorites'
                    className='block py-2 font-medium text-text-primary'
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Favorites
                  </Link>
                  <Link
                    to='/scheduled-orders'
                    className='block py-2 font-medium text-text-primary'
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Scheduled Orders
                  </Link>
                  <Link
                    to='/social'
                    className='block py-2 font-medium text-text-primary'
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Social Hub
                  </Link>
                  <Link
                    to='/support'
                    className='block py-2 font-medium text-text-primary'
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Help & Support
                  </Link>
                  <Link
                    to='/profile'
                    className='block py-2 font-medium text-text-primary'
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Profile
                  </Link>
                  {user.role !== "customer" && (
                    <Link
                      to={`/${user.role}`}
                      className='block py-2 font-medium text-text-primary'
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                  )}
                  {user.role === "customer" && (
                    <Link
                      to='/register-restaurant'
                      className='block py-2 font-medium text-primary-500'
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Register Your Restaurant
                    </Link>
                  )}
                  <button
                    className='w-full py-2 text-left text-accent-red font-medium flex items-center'
                    onClick={handleLogout}
                  >
                    <LogOut size={16} className='mr-2' />
                    Logout
                  </button>
                </>
              ) : (
                <div className='pt-2 border-t border-gray-100'>
                  <Button to='/login' variant='primary' fullWidth>
                    Login
                  </Button>
                  <div className='mt-2'>
                    <Button to='/register' variant='outline' fullWidth>
                      Register
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </header>

      {/* Main Content */}
      <main className='flex-grow pb-16 md:pb-0'>
        <Outlet />
      </main>

      {/* Bottom Navigation (Mobile Only) */}
      <BottomNavigation />

      {/* Footer */}
      <footer className='bg-text-primary py-8 mt-auto'>
        <div className='container mx-auto px-4'>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
            <div>
              <h3 className='text-white font-poppins font-semibold text-lg mb-4'>
                {siteName || "Afghan Sufra"}
              </h3>
              <p className='text-gray-300 text-sm'>
                {siteDescription ||
                  "Bringing the finest Afghan cuisine right to your doorstep. Experience authentic flavors with just a few clicks."}
              </p>
            </div>

            <div>
              <h4 className='text-white font-poppins font-semibold mb-4'>
                Quick Links
              </h4>
              <ul className='space-y-2'>
                <li>
                  <Link
                    to='/'
                    className='text-gray-300 hover:text-white text-sm transition-colors'
                  >
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    to='/restaurants'
                    className='text-gray-300 hover:text-white text-sm transition-colors'
                  >
                    Restaurants
                  </Link>
                </li>
                <li>
                  <Link
                    to='/about'
                    className='text-gray-300 hover:text-white text-sm transition-colors'
                  >
                    About Us
                  </Link>
                </li>
                <li>
                  <Link
                    to='/contact'
                    className='text-gray-300 hover:text-white text-sm transition-colors'
                  >
                    Contact
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className='text-white font-poppins font-semibold mb-4'>
                Contact Us
              </h4>
              <address className='text-gray-300 text-sm not-italic'>
                <p>123 Food Street</p>
                <p>Kabul, Afghanistan</p>
                <p className='mt-2'>{contactEmail || "<EMAIL>"}</p>
                <p>{contactPhone || "+93 79 123 4567"}</p>
              </address>
            </div>
          </div>

          <div className='mt-8 pt-6 border-t border-gray-700 text-center text-gray-400 text-sm'>
            <p>
              &copy; {new Date().getFullYear()} Afghan Sofra. All rights
              reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default MainLayout;
