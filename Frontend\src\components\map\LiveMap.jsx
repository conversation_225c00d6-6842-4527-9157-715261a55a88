import React, { useEffect, useState, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  useMap as useLeafletMap,
} from "react-leaflet";
import { TruckIcon, MapPin, Store } from "lucide-react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { useMap } from "../../context/MapContext";

// Fix Leaflet icon issue
import icon from "leaflet/dist/images/marker-icon.png";
import iconShadow from "leaflet/dist/images/marker-shadow.png";

let DefaultIcon = L.icon({
  iconUrl: icon,
  shadowUrl: iconShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
});

L.Marker.prototype.options.icon = DefaultIcon;

// Component to update map view when locations change
const MapUpdater = ({ bounds }) => {
  const map = useLeafletMap();

  useEffect(() => {
    if (bounds) {
      map.fitBounds(bounds, { padding: [50, 50] });
    }
  }, [bounds, map]);

  return null;
};

// Custom marker components
const RestaurantMarker = ({ position }) => {
  const customIcon = L.divIcon({
    className: "custom-icon",
    html: `
      <div class="flex flex-col items-center">
        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#ea580c" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2z"></path>
          </svg>
        </div>
        <div class="text-xs font-medium bg-white px-2 py-1 rounded-md shadow-md mt-1">
          Restaurant
        </div>
      </div>
    `,
    iconSize: [40, 60],
    iconAnchor: [20, 60],
  });

  return (
    <Marker position={position} icon={customIcon}>
      <Popup>Restaurant</Popup>
    </Marker>
  );
};

const CustomerMarker = ({ position }) => {
  const customIcon = L.divIcon({
    className: "custom-icon",
    html: `
      <div class="flex flex-col items-center">
        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#2563eb" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
        </div>
        <div class="text-xs font-medium bg-white px-2 py-1 rounded-md shadow-md mt-1">
          Delivery
        </div>
      </div>
    `,
    iconSize: [40, 60],
    iconAnchor: [20, 60],
  });

  return (
    <Marker position={position} icon={customIcon}>
      <Popup>Delivery Location</Popup>
    </Marker>
  );
};

const AgentMarker = ({ position, heading }) => {
  const customIcon = L.divIcon({
    className: "custom-icon",
    html: `
      <div class="w-12 h-12 bg-primary-500 rounded-full flex items-center justify-center shadow-lg" style="transform: rotate(${heading}deg); transition: all 0.5s ease-in-out;">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11"></path>
          <path d="M14 9h4l4 4v4c0 .6-.4 1-1 1h-2"></path>
          <circle cx="7" cy="18" r="2"></circle>
          <path d="M15 18H9"></path>
          <circle cx="17" cy="18" r="2"></circle>
        </svg>
      </div>
    `,
    iconSize: [48, 48],
    iconAnchor: [24, 24],
  });

  return (
    <Marker position={position} icon={customIcon}>
      <Popup>Delivery Agent</Popup>
    </Marker>
  );
};

const LiveMap = ({
  orderId,
  width = "100%",
  height = "400px",
  showControls = true,
  interactive = true,
  className = "",
}) => {
  const {
    defaultCenter,
    defaultZoom,
    agentLocation,
    restaurantLocation,
    customerLocation,
    route,
    agentHeading,
    isSimulating,
    getMapBounds,
  } = useMap();

  const [bounds, setBounds] = useState(null);

  // Update bounds when locations change
  useEffect(() => {
    const mapBounds = getMapBounds();
    if (mapBounds) {
      setBounds(mapBounds);
    }
  }, [agentLocation, restaurantLocation, customerLocation, getMapBounds]);

  return (
    <div
      className={`relative rounded-lg overflow-hidden ${className}`}
      style={{ width, height }}
    >
      <MapContainer
        center={defaultCenter}
        zoom={defaultZoom}
        style={{ width: "100%", height: "100%" }}
        zoomControl={showControls}
        dragging={interactive}
        touchZoom={interactive}
        doubleClickZoom={interactive}
        scrollWheelZoom={interactive}
        attributionControl={false}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
        />

        {/* Update map view when locations change */}
        {bounds && <MapUpdater bounds={bounds} />}

        {/* Restaurant Marker */}
        {restaurantLocation && (
          <RestaurantMarker position={restaurantLocation} />
        )}

        {/* Customer Marker */}
        {customerLocation && <CustomerMarker position={customerLocation} />}

        {/* Delivery Agent Marker */}
        {agentLocation && (
          <AgentMarker position={agentLocation} heading={agentHeading} />
        )}

        {/* Route Line */}
        {route && (
          <Polyline
            positions={route}
            color='#FF6B00'
            weight={4}
            opacity={0.8}
          />
        )}
      </MapContainer>

      {/* Simulation Indicator */}
      {isSimulating && (
        <div className='absolute bottom-4 left-4 bg-white px-3 py-2 rounded-md shadow-md z-[1000]'>
          <div className='flex items-center'>
            <div className='w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse'></div>
            <span className='text-sm font-medium'>Live Tracking</span>
          </div>
        </div>
      )}

      {/* Map Attribution */}
      <div className='absolute bottom-1 right-1 text-xs text-gray-600 z-[1000]'>
        © OpenStreetMap contributors
      </div>
    </div>
  );
};

export default LiveMap;
