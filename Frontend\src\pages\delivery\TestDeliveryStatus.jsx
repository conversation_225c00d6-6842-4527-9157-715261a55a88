import React, { useState, useEffect } from 'react';
import { deliveryAgentApi } from '../../services/deliveryAgentApi';
import { Button } from '../../components/common/Button';
import { Card } from '../../components/common/Card';

const TestDeliveryStatus = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Fetch orders
  const fetchOrders = async () => {
    try {
      setLoading(true);
      const result = await deliveryAgentApi.getMyOrders();
      if (result.success) {
        setOrders(result.data.results || result.data || []);
      } else {
        setError(result.error?.message || 'Failed to fetch orders');
      }
    } catch (err) {
      setError('Failed to fetch orders');
    } finally {
      setLoading(false);
    }
  };

  // Update order status
  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      setLoading(true);
      setError('');
      setSuccess('');

      const result = await deliveryAgentApi.updateOrderStatus(orderId, {
        status: newStatus,
        notes: `Status updated to ${newStatus} via test page`
      });

      if (result.data?.status === 'success') {
        setSuccess(`Order ${orderId} updated to ${newStatus}`);
        await fetchOrders(); // Refresh orders
      } else {
        setError(result.data?.message || 'Failed to update order status');
      }
    } catch (err) {
      console.error('Update error:', err);
      setError(err.response?.data?.message || 'Failed to update order status');
    } finally {
      setLoading(false);
    }
  };

  // Get next status for an order
  const getNextStatus = (currentStatus) => {
    const statusFlow = {
      'assigned': 'accepted',
      'accepted': 'en_route_to_restaurant',
      'en_route_to_restaurant': 'arrived_at_restaurant',
      'arrived_at_restaurant': 'picked_up',
      'picked_up': 'en_route_to_customer',
      'en_route_to_customer': 'delivered',
      'arrived_at_customer': 'delivered',
      'delivered': 'completed'
    };
    return statusFlow[currentStatus];
  };

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'assigned': 'bg-blue-100 text-blue-800',
      'accepted': 'bg-indigo-100 text-indigo-800',
      'en_route_to_restaurant': 'bg-orange-100 text-orange-800',
      'arrived_at_restaurant': 'bg-amber-100 text-amber-800',
      'picked_up': 'bg-yellow-100 text-yellow-800',
      'en_route_to_customer': 'bg-purple-100 text-purple-800',
      'arrived_at_customer': 'bg-violet-100 text-violet-800',
      'delivered': 'bg-green-100 text-green-800',
      'completed': 'bg-emerald-100 text-emerald-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Delivery Status Test Page
          </h1>
          <p className="text-gray-600">
            Test the delivery status update functionality
          </p>
        </div>

        {/* Controls */}
        <Card className="mb-6 p-4">
          <div className="flex space-x-4">
            <Button onClick={fetchOrders} disabled={loading}>
              {loading ? 'Loading...' : 'Refresh Orders'}
            </Button>
          </div>
        </Card>

        {/* Messages */}
        {error && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}
        {success && (
          <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
            {success}
          </div>
        )}

        {/* Orders List */}
        <div className="space-y-4">
          {orders.length === 0 ? (
            <Card className="p-8 text-center">
              <p className="text-gray-600">
                {loading ? 'Loading orders...' : 'No orders found'}
              </p>
            </Card>
          ) : (
            orders.map((order) => {
              const nextStatus = getNextStatus(order.status);
              return (
                <Card key={order.id} className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="font-semibold text-gray-900">
                          Order #{order.id}
                        </h3>
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                            order.status
                          )}`}
                        >
                          {order.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">
                        Customer: {order.customer?.name || 'N/A'}
                      </p>
                      <p className="text-sm text-gray-600">
                        Restaurant: {order.restaurant?.name || 'N/A'}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        AFN {order.total_amount}
                      </p>
                      <p className="text-sm text-gray-600">
                        {new Date(order.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  {/* Action Button */}
                  {nextStatus && (
                    <div className="flex justify-end">
                      <Button
                        onClick={() => updateOrderStatus(order.id, nextStatus)}
                        disabled={loading}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        {loading ? 'Updating...' : `Update to ${nextStatus.replace('_', ' ')}`}
                      </Button>
                    </div>
                  )}

                  {/* Order Details */}
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Payment Method:</span>{' '}
                        {order.payment_method || 'N/A'}
                      </div>
                      <div>
                        <span className="font-medium">Delivery Fee:</span>{' '}
                        AFN {order.delivery_fee || '0'}
                      </div>
                    </div>
                  </div>
                </Card>
              );
            })
          )}
        </div>
      </div>
    </div>
  );
};

export default TestDeliveryStatus;
