import React from "react";
import { useLocation, <PERSON> } from "react-router-dom";
import {
  Home,
  Search,
  ShoppingBag,
  Heart,
  User,
  MapPin,
  Clock,
  Star,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import { useCart } from "../../context/CartContext";
import { cn } from "../../utils/cn";

const BottomNavigation = ({ className = "" }) => {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const { itemCount } = useCart();

  // Don't show on auth pages or dashboard pages
  if (
    location.pathname.includes("/login") ||
    location.pathname.includes("/register") ||
    location.pathname.includes("/restaurant/") ||
    location.pathname.includes("/delivery/") ||
    location.pathname.includes("/admin/")
  ) {
    return null;
  }

  const navItems = [
    {
      id: "home",
      label: "Home",
      icon: Home,
      path: "/",
      active: location.pathname === "/",
    },
    {
      id: "restaurants",
      label: "Restaurants",
      icon: Search,
      path: "/restaurants",
      active: location.pathname.startsWith("/restaurants"),
    },
    {
      id: "orders",
      label: "Orders",
      icon: ShoppingBag,
      path: "/orders",
      active: location.pathname.startsWith("/orders"),
      badge: itemCount > 0 ? itemCount : null,
      requireAuth: true,
    },
    {
      id: "favorites",
      label: "Favorites",
      icon: Heart,
      path: "/favorites",
      active: location.pathname === "/favorites",
      requireAuth: true,
    },
    {
      id: "profile",
      label: "Profile",
      icon: User,
      path: isAuthenticated ? "/profile" : "/login",
      active: location.pathname === "/profile",
    },
  ];

  const visibleItems = navItems.filter(
    (item) => !item.requireAuth || isAuthenticated
  );

  return (
    <nav
      className={cn(
        "fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 safe-area-pb",
        "md:hidden", // Only show on mobile
        className
      )}
    >
      <div className='flex items-center justify-around py-2'>
        {visibleItems.map((item) => {
          const IconComponent = item.icon;

          return (
            <Link
              key={item.id}
              to={item.path}
              className={cn(
                "flex flex-col items-center justify-center px-3 py-2 min-w-0 flex-1 relative",
                "transition-colors duration-200",
                item.active
                  ? "text-primary-600"
                  : "text-gray-500 hover:text-gray-700"
              )}
            >
              <div className='relative'>
                <IconComponent
                  size={24}
                  className={cn(
                    "transition-transform duration-200",
                    item.active && "scale-110"
                  )}
                />

                {/* Badge for notifications/cart count */}
                {item.badge && (
                  <span className='absolute -top-2 -right-2 bg-primary-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium'>
                    {item.badge > 99 ? "99+" : item.badge}
                  </span>
                )}
              </div>

              <span
                className={cn(
                  "text-xs mt-1 font-medium truncate w-full text-center",
                  "transition-colors duration-200",
                  item.active && "text-primary-600"
                )}
              >
                {item.label}
              </span>

              {/* Active indicator */}
              {item.active && (
                <div className='absolute top-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full' />
              )}
            </Link>
          );
        })}
      </div>
    </nav>
  );
};

// Alternative bottom navigation with different layout
export const TabBottomNavigation = ({ className = "" }) => {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const { itemCount } = useCart();

  const tabs = [
    {
      id: "home",
      label: "Home",
      icon: Home,
      path: "/",
      color: "text-blue-600",
    },
    {
      id: "search",
      label: "Search",
      icon: Search,
      path: "/restaurants",
      color: "text-green-600",
    },
    {
      id: "orders",
      label: "Orders",
      icon: Clock,
      path: "/orders",
      color: "text-orange-600",
      requireAuth: true,
    },
    {
      id: "favorites",
      label: "Saved",
      icon: Heart,
      path: "/favorites",
      color: "text-red-600",
      requireAuth: true,
    },
    {
      id: "profile",
      label: "Account",
      icon: User,
      path: isAuthenticated ? "/profile" : "/login",
      color: "text-purple-600",
    },
  ];

  const visibleTabs = tabs.filter((tab) => !tab.requireAuth || isAuthenticated);

  return (
    <div
      className={cn(
        "fixed bottom-0 left-0 right-0 z-40 bg-white shadow-lg",
        "md:hidden",
        className
      )}
    >
      <div className='flex'>
        {visibleTabs.map((tab, index) => {
          const IconComponent = tab.icon;
          const isActive =
            location.pathname === tab.path ||
            (tab.path !== "/" && location.pathname.startsWith(tab.path));

          return (
            <Link
              key={tab.id}
              to={tab.path}
              className={cn(
                "flex-1 flex flex-col items-center justify-center py-3 px-2",
                "transition-all duration-200 relative",
                isActive
                  ? `${tab.color} bg-gray-50`
                  : "text-gray-400 hover:text-gray-600"
              )}
            >
              {/* Active background */}
              {isActive && (
                <div className='absolute inset-0 bg-gradient-to-t from-gray-100 to-transparent opacity-50' />
              )}

              <div className='relative z-10 flex flex-col items-center'>
                <div className='relative'>
                  <IconComponent
                    size={22}
                    className={cn(
                      "transition-all duration-200",
                      isActive && "scale-110"
                    )}
                  />

                  {/* Cart badge */}
                  {tab.id === "orders" && itemCount > 0 && (
                    <span className='absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center'>
                      {itemCount}
                    </span>
                  )}
                </div>

                <span
                  className={cn(
                    "text-xs mt-1 font-medium",
                    isActive && "font-semibold"
                  )}
                >
                  {tab.label}
                </span>
              </div>

              {/* Active indicator line */}
              {isActive && (
                <div
                  className={cn(
                    "absolute top-0 left-0 right-0 h-0.5",
                    tab.color.replace("text-", "bg-")
                  )}
                />
              )}
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default BottomNavigation;
