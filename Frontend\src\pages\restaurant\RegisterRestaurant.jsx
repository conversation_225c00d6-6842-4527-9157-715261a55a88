import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { restaurantApi } from "../../utils/restaurantApi";
import {
  Store,
  MapPin,
  Phone,
  Clock,
  Upload,
  ArrowLeft,
  CheckCircle,
  X,
  Image,
  Globe,
  Facebook,
  Instagram,
  Twitter,
  CreditCard,
  Banknote,
  Smartphone,
} from "lucide-react";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";

const RegisterRestaurant = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    address: "",
    contact_number: "",
    opening_time: "09:00",
    closing_time: "22:00",
    cuisine_type: "",
    delivery_fee: "50",
    minimum_order: "200",
    website: "",
    facebook_url: "",
    instagram_url: "",
    twitter_url: "",
    average_preparation_time: "30",
    accepts_cash: true,
    accepts_card: true,
    accepts_online_payment: true,
  });
  const [files, setFiles] = useState({
    logo: null,
    banner: null,
  });
  const [previews, setPreviews] = useState({
    logo: null,
    banner: null,
  });
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleFileChange = (e, fileType) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        setErrors((prev) => ({
          ...prev,
          [fileType]: "Please select a valid image file",
        }));
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors((prev) => ({
          ...prev,
          [fileType]: "File size must be less than 5MB",
        }));
        return;
      }

      setFiles((prev) => ({
        ...prev,
        [fileType]: file,
      }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviews((prev) => ({
          ...prev,
          [fileType]: e.target.result,
        }));
      };
      reader.readAsDataURL(file);

      // Clear error
      if (errors[fileType]) {
        setErrors((prev) => ({
          ...prev,
          [fileType]: "",
        }));
      }
    }
  };

  const removeFile = (fileType) => {
    setFiles((prev) => ({
      ...prev,
      [fileType]: null,
    }));
    setPreviews((prev) => ({
      ...prev,
      [fileType]: null,
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) newErrors.name = "Restaurant name is required";
    if (!formData.description.trim())
      newErrors.description = "Description is required";
    if (!formData.address.trim()) newErrors.address = "Address is required";
    if (!formData.contact_number.trim())
      newErrors.contact_number = "Contact number is required";
    if (!formData.cuisine_type.trim())
      newErrors.cuisine_type = "Cuisine type is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);

    try {
      // Format time fields to include seconds (HH:MM:SS)
      const formatTime = (time) => {
        if (!time) return null;
        return time.includes(":") && time.split(":").length === 2
          ? `${time}:00`
          : time;
      };

      // Combine form data with files and format times
      const restaurantData = {
        ...formData,
        opening_time: formatTime(formData.opening_time),
        closing_time: formatTime(formData.closing_time),
        logo: files.logo,
        banner: files.banner,
      };

      const result = await restaurantApi.createRestaurant(restaurantData);

      if (result.success) {
        setSuccess(true);
        setTimeout(() => {
          navigate("/restaurant");
        }, 3000);
      } else {
        setErrors({ submit: result.message || "Failed to create restaurant" });
      }
    } catch (error) {
      console.error("Restaurant creation error:", error);
      setErrors({ submit: "Failed to create restaurant. Please try again." });
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <Card className='max-w-md mx-auto text-center p-8'>
          <div className='bg-green-100 rounded-full p-4 w-20 h-20 mx-auto mb-6'>
            <CheckCircle className='h-12 w-12 text-green-600' />
          </div>
          <h2 className='text-2xl font-semibold text-gray-900 mb-4'>
            Restaurant Created Successfully!
          </h2>
          <p className='text-gray-600 mb-6'>
            Your restaurant profile has been submitted for admin review. You'll
            receive an email notification once approved.
          </p>
          <div className='animate-pulse text-sm text-gray-500'>
            Redirecting to dashboard...
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50 py-12'>
      <div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Header */}
        <div className='mb-8'>
          <Button
            variant='outline'
            onClick={() => navigate("/restaurant")}
            className='mb-4 text-gray-600 hover:text-gray-800'
          >
            <ArrowLeft className='h-4 w-4 mr-2' />
            Back to Dashboard
          </Button>

          <div className='text-center'>
            <Store className='h-12 w-12 text-orange-500 mx-auto mb-4' />
            <h1 className='text-3xl font-bold text-gray-900 mb-2'>
              Register Your Restaurant
            </h1>
            <p className='text-lg text-gray-600'>
              Join Afghan Sufra and start serving customers today
            </p>
          </div>
        </div>

        {/* Form */}
        <Card className='max-w-2xl mx-auto'>
          <form onSubmit={handleSubmit} className='p-8 space-y-6'>
            {/* Restaurant Name */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                Restaurant Name *
              </label>
              <input
                type='text'
                name='name'
                value={formData.name}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                  errors.name ? "border-red-500" : "border-gray-300"
                }`}
                placeholder='Enter your restaurant name'
              />
              {errors.name && (
                <p className='mt-1 text-sm text-red-600'>{errors.name}</p>
              )}
            </div>

            {/* Description */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                Description *
              </label>
              <textarea
                name='description'
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                  errors.description ? "border-red-500" : "border-gray-300"
                }`}
                placeholder='Describe your restaurant and cuisine'
              />
              {errors.description && (
                <p className='mt-1 text-sm text-red-600'>
                  {errors.description}
                </p>
              )}
            </div>

            {/* Logo Upload */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                <Image className='h-4 w-4 inline mr-1' />
                Restaurant Logo
              </label>
              <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-orange-400 transition-colors'>
                {previews.logo ? (
                  <div className='relative'>
                    <img
                      src={previews.logo}
                      alt='Logo preview'
                      className='mx-auto h-32 w-32 object-cover rounded-lg'
                    />
                    <button
                      type='button'
                      onClick={() => removeFile("logo")}
                      className='absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600'
                    >
                      <X className='h-4 w-4' />
                    </button>
                  </div>
                ) : (
                  <div>
                    <Upload className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                    <p className='text-gray-600 mb-2'>
                      Upload your restaurant logo
                    </p>
                    <p className='text-sm text-gray-500'>PNG, JPG up to 5MB</p>
                  </div>
                )}
                <input
                  type='file'
                  accept='image/*'
                  onChange={(e) => handleFileChange(e, "logo")}
                  className='hidden'
                  id='logo-upload'
                />
                <label
                  htmlFor='logo-upload'
                  className='mt-4 inline-block bg-orange-50 text-orange-600 px-4 py-2 rounded-lg cursor-pointer hover:bg-orange-100 transition-colors'
                >
                  Choose Logo
                </label>
              </div>
              {errors.logo && (
                <p className='mt-1 text-sm text-red-600'>{errors.logo}</p>
              )}
            </div>

            {/* Banner Upload */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                <Image className='h-4 w-4 inline mr-1' />
                Cover Banner
              </label>
              <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-orange-400 transition-colors'>
                {previews.banner ? (
                  <div className='relative'>
                    <img
                      src={previews.banner}
                      alt='Banner preview'
                      className='mx-auto h-32 w-full object-cover rounded-lg'
                    />
                    <button
                      type='button'
                      onClick={() => removeFile("banner")}
                      className='absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600'
                    >
                      <X className='h-4 w-4' />
                    </button>
                  </div>
                ) : (
                  <div>
                    <Upload className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                    <p className='text-gray-600 mb-2'>
                      Upload your restaurant banner
                    </p>
                    <p className='text-sm text-gray-500'>
                      PNG, JPG up to 5MB (Recommended: 1200x400px)
                    </p>
                  </div>
                )}
                <input
                  type='file'
                  accept='image/*'
                  onChange={(e) => handleFileChange(e, "banner")}
                  className='hidden'
                  id='banner-upload'
                />
                <label
                  htmlFor='banner-upload'
                  className='mt-4 inline-block bg-orange-50 text-orange-600 px-4 py-2 rounded-lg cursor-pointer hover:bg-orange-100 transition-colors'
                >
                  Choose Banner
                </label>
              </div>
              {errors.banner && (
                <p className='mt-1 text-sm text-red-600'>{errors.banner}</p>
              )}
            </div>

            {/* Address */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                <MapPin className='h-4 w-4 inline mr-1' />
                Address *
              </label>
              <input
                type='text'
                name='address'
                value={formData.address}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                  errors.address ? "border-red-500" : "border-gray-300"
                }`}
                placeholder='Enter your restaurant address'
              />
              {errors.address && (
                <p className='mt-1 text-sm text-red-600'>{errors.address}</p>
              )}
            </div>

            {/* Contact Number */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                <Phone className='h-4 w-4 inline mr-1' />
                Contact Number *
              </label>
              <input
                type='tel'
                name='contact_number'
                value={formData.contact_number}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                  errors.contact_number ? "border-red-500" : "border-gray-300"
                }`}
                placeholder='Enter contact number'
              />
              {errors.contact_number && (
                <p className='mt-1 text-sm text-red-600'>
                  {errors.contact_number}
                </p>
              )}
            </div>

            {/* Operating Hours */}
            <div className='grid md:grid-cols-2 gap-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  <Clock className='h-4 w-4 inline mr-1' />
                  Opening Time
                </label>
                <input
                  type='time'
                  name='opening_time'
                  value={formData.opening_time}
                  onChange={handleInputChange}
                  className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
                />
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Closing Time
                </label>
                <input
                  type='time'
                  name='closing_time'
                  value={formData.closing_time}
                  onChange={handleInputChange}
                  className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
                />
              </div>
            </div>

            {/* Cuisine Type */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                Cuisine Type *
              </label>
              <select
                name='cuisine_type'
                value={formData.cuisine_type}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                  errors.cuisine_type ? "border-red-500" : "border-gray-300"
                }`}
              >
                <option value=''>Select cuisine type</option>
                <option value='Afghan'>Afghan</option>
                <option value='Pakistani'>Pakistani</option>
                <option value='Indian'>Indian</option>
                <option value='Middle Eastern'>Middle Eastern</option>
                <option value='Mediterranean'>Mediterranean</option>
                <option value='International'>International</option>
              </select>
              {errors.cuisine_type && (
                <p className='mt-1 text-sm text-red-600'>
                  {errors.cuisine_type}
                </p>
              )}
            </div>

            {/* Delivery Settings */}
            <div className='grid md:grid-cols-2 gap-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Delivery Fee (AFN)
                </label>
                <input
                  type='number'
                  name='delivery_fee'
                  value={formData.delivery_fee}
                  onChange={handleInputChange}
                  min='0'
                  className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
                />
              </div>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Minimum Order (AFN)
                </label>
                <input
                  type='number'
                  name='minimum_order'
                  value={formData.minimum_order}
                  onChange={handleInputChange}
                  min='0'
                  className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
                />
              </div>
            </div>

            {/* Average Preparation Time */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                <Clock className='h-4 w-4 inline mr-1' />
                Average Preparation Time (minutes)
              </label>
              <input
                type='number'
                name='average_preparation_time'
                value={formData.average_preparation_time}
                onChange={handleInputChange}
                min='10'
                max='120'
                className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
                placeholder='30'
              />
            </div>

            {/* Payment Methods */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-4'>
                Accepted Payment Methods
              </label>
              <div className='space-y-3'>
                <label className='flex items-center'>
                  <input
                    type='checkbox'
                    name='accepts_cash'
                    checked={formData.accepts_cash}
                    onChange={handleInputChange}
                    className='rounded border-gray-300 text-orange-600 focus:ring-orange-500'
                  />
                  <Banknote className='h-5 w-5 ml-3 mr-2 text-green-600' />
                  <span className='text-gray-700'>Cash on Delivery</span>
                </label>
                <label className='flex items-center'>
                  <input
                    type='checkbox'
                    name='accepts_card'
                    checked={formData.accepts_card}
                    onChange={handleInputChange}
                    className='rounded border-gray-300 text-orange-600 focus:ring-orange-500'
                  />
                  <CreditCard className='h-5 w-5 ml-3 mr-2 text-blue-600' />
                  <span className='text-gray-700'>Credit/Debit Card</span>
                </label>
                <label className='flex items-center'>
                  <input
                    type='checkbox'
                    name='accepts_online_payment'
                    checked={formData.accepts_online_payment}
                    onChange={handleInputChange}
                    className='rounded border-gray-300 text-orange-600 focus:ring-orange-500'
                  />
                  <Smartphone className='h-5 w-5 ml-3 mr-2 text-purple-600' />
                  <span className='text-gray-700'>Online Payment</span>
                </label>
              </div>
            </div>

            {/* Website */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                <Globe className='h-4 w-4 inline mr-1' />
                Website (Optional)
              </label>
              <input
                type='url'
                name='website'
                value={formData.website}
                onChange={handleInputChange}
                className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
                placeholder='https://yourrestaurant.com'
              />
            </div>

            {/* Social Media Links */}
            <div>
              <label className='block text-sm font-medium text-gray-700 mb-4'>
                Social Media (Optional)
              </label>
              <div className='space-y-4'>
                <div>
                  <label className='block text-sm text-gray-600 mb-1'>
                    <Facebook className='h-4 w-4 inline mr-1 text-blue-600' />
                    Facebook Page
                  </label>
                  <input
                    type='url'
                    name='facebook_url'
                    value={formData.facebook_url}
                    onChange={handleInputChange}
                    className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
                    placeholder='https://facebook.com/yourrestaurant'
                  />
                </div>
                <div>
                  <label className='block text-sm text-gray-600 mb-1'>
                    <Instagram className='h-4 w-4 inline mr-1 text-pink-600' />
                    Instagram Profile
                  </label>
                  <input
                    type='url'
                    name='instagram_url'
                    value={formData.instagram_url}
                    onChange={handleInputChange}
                    className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
                    placeholder='https://instagram.com/yourrestaurant'
                  />
                </div>
                <div>
                  <label className='block text-sm text-gray-600 mb-1'>
                    <Twitter className='h-4 w-4 inline mr-1 text-blue-400' />
                    Twitter Profile
                  </label>
                  <input
                    type='url'
                    name='twitter_url'
                    value={formData.twitter_url}
                    onChange={handleInputChange}
                    className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500'
                    placeholder='https://twitter.com/yourrestaurant'
                  />
                </div>
              </div>
            </div>

            {/* Submit Error */}
            {errors.submit && (
              <div className='bg-red-50 border border-red-200 rounded-lg p-4'>
                <p className='text-red-600 text-sm'>{errors.submit}</p>
              </div>
            )}

            {/* Submit Button */}
            <div className='pt-4'>
              <Button
                type='submit'
                disabled={loading}
                className='w-full bg-orange-600 hover:bg-orange-700 text-white py-3 px-6 rounded-lg font-medium disabled:opacity-50'
              >
                {loading ? (
                  <div className='flex items-center justify-center'>
                    <div className='animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2'></div>
                    Creating Restaurant...
                  </div>
                ) : (
                  <>
                    <Store className='h-5 w-5 mr-2' />
                    Create Restaurant Profile
                  </>
                )}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default RegisterRestaurant;
