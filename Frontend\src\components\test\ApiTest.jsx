import React, { useState } from 'react';
import { dashboardApi } from '../../utils/dashboardApi';

const ApiTest = () => {
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState(false);
  const [restaurantId, setRestaurantId] = useState('20'); // Default to first restaurant

  const testOrders = async () => {
    setLoading(true);
    try {
      const result = await dashboardApi.getRestaurantOrders(restaurantId);
      setResults(prev => ({ ...prev, orders: result }));
    } catch (error) {
      setResults(prev => ({ ...prev, orders: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  const testMenuItems = async () => {
    setLoading(true);
    try {
      const result = await dashboardApi.getRestaurantMenuItems(restaurantId);
      setResults(prev => ({ ...prev, menuItems: result }));
    } catch (error) {
      setResults(prev => ({ ...prev, menuItems: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  const testDashboardData = async () => {
    setLoading(true);
    try {
      const result = await dashboardApi.getDashboardData(restaurantId);
      setResults(prev => ({ ...prev, dashboard: result }));
    } catch (error) {
      setResults(prev => ({ ...prev, dashboard: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  const testAllApis = async () => {
    setLoading(true);
    try {
      // Test basic API endpoints
      const ordersResponse = await fetch('http://127.0.0.1:8000/api/order/orders/');
      const restaurantsResponse = await fetch('http://127.0.0.1:8000/api/restaurant/restaurants/');
      const menuItemsResponse = await fetch('http://127.0.0.1:8000/api/restaurant/menu-items/');

      const ordersData = await ordersResponse.json();
      const restaurantsData = await restaurantsResponse.json();
      const menuItemsData = await menuItemsResponse.json();

      setResults(prev => ({
        ...prev,
        basicApis: {
          orders: { success: ordersResponse.ok, data: ordersData, count: ordersData.length },
          restaurants: { success: restaurantsResponse.ok, data: restaurantsData, count: restaurantsData.length },
          menuItems: { success: menuItemsResponse.ok, data: menuItemsData, count: menuItemsData.length }
        }
      }));
    } catch (error) {
      setResults(prev => ({ ...prev, basicApis: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API Integration Test</h1>
      
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Restaurant ID:</label>
        <input
          type="text"
          value={restaurantId}
          onChange={(e) => setRestaurantId(e.target.value)}
          className="border rounded px-3 py-2 w-32"
          placeholder="Restaurant ID"
        />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <button
          onClick={testAllApis}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Test All APIs
        </button>
        <button
          onClick={testOrders}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          Test Orders
        </button>
        <button
          onClick={testMenuItems}
          disabled={loading}
          className="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600 disabled:opacity-50"
        >
          Test Menu Items
        </button>
        <button
          onClick={testDashboardData}
          disabled={loading}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          Test Dashboard
        </button>
      </div>

      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="mt-2">Testing APIs...</p>
        </div>
      )}

      <div className="space-y-6">
        {Object.entries(results).map(([key, result]) => (
          <div key={key} className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-2 capitalize">{key} Results</h3>
            <div className="bg-gray-100 p-3 rounded overflow-auto max-h-96">
              <pre className="text-sm">{JSON.stringify(result, null, 2)}</pre>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ApiTest;
