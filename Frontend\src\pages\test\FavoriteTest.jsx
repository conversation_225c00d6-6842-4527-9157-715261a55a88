import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import { useFavorites } from "../../context/FavoritesContext";
import FavoriteButton from "../../components/common/FavoriteButton";

const FavoriteTest = () => {
  const { user } = useAuth();
  const { favorites, loading } = useFavorites();
  const [testRestaurant, setTestRestaurant] = useState(null);
  const [apiTestResult, setApiTestResult] = useState(null);

  useEffect(() => {
    // Create a test restaurant object
    setTestRestaurant({
      id: 19, // Using the test restaurant ID from our backend test
      name: "Test Afghan Restaurant",
      description: "A test restaurant for favorite functionality",
      logo: "/placeholder-restaurant.jpg",
      banner: "/placeholder-restaurant.jpg",
      rating: 4.5,
      delivery_fee: 2.99,
      cuisine_types: [{ name: "Afghan" }, { name: "Traditional" }],
      is_active: true,
      is_verified: true,
    });
  }, []);

  if (!user) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <h1 className='text-2xl font-bold mb-4'>Favorite Test Page</h1>
        <p>Please log in to test the favorite functionality.</p>
      </div>
    );
  }

  if (!testRestaurant) {
    return (
      <div className='container mx-auto px-4 py-8'>
        <h1 className='text-2xl font-bold mb-4'>Favorite Test Page</h1>
        <p>Loading test restaurant...</p>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4 py-8'>
      <h1 className='text-2xl font-bold mb-4'>Favorite Test Page</h1>

      <div className='bg-white p-6 rounded-lg shadow-md mb-6'>
        <h2 className='text-lg font-semibold mb-4'>User Info</h2>
        <p>
          <strong>User:</strong> {user.email}
        </p>
        <p>
          <strong>User ID:</strong> {user.id}
        </p>
        <p>
          <strong>Access Token:</strong>{" "}
          {user.access_token ? "Present" : "Missing"}
        </p>
      </div>

      <div className='bg-white p-6 rounded-lg shadow-md mb-6'>
        <h2 className='text-lg font-semibold mb-4'>Favorites Info</h2>
        <p>
          <strong>Loading:</strong> {loading ? "Yes" : "No"}
        </p>
        <p>
          <strong>Favorites Count:</strong> {favorites.length}
        </p>
        <p>
          <strong>Favorites:</strong>
        </p>
        <pre className='bg-gray-100 p-2 rounded text-sm overflow-auto'>
          {JSON.stringify(favorites, null, 2)}
        </pre>
      </div>

      <div className='bg-white p-6 rounded-lg shadow-md mb-6'>
        <h2 className='text-lg font-semibold mb-4'>Test Restaurant</h2>
        <div className='flex items-center justify-between p-4 border rounded-lg'>
          <div>
            <h3 className='font-semibold'>{testRestaurant.name}</h3>
            <p className='text-gray-600'>{testRestaurant.description}</p>
            <p className='text-sm text-gray-500'>ID: {testRestaurant.id}</p>
          </div>
          <div className='flex items-center space-x-4'>
            <FavoriteButton
              restaurant={testRestaurant}
              size='large'
              variant='default'
            />
            <FavoriteButton
              restaurant={testRestaurant}
              size='default'
              variant='outline'
            />
            <FavoriteButton
              restaurant={testRestaurant}
              size='small'
              variant='ghost'
            />
          </div>
        </div>
      </div>

      <div className='bg-white p-6 rounded-lg shadow-md'>
        <h2 className='text-lg font-semibold mb-4'>Test Restaurant Data</h2>
        <pre className='bg-gray-100 p-2 rounded text-sm overflow-auto'>
          {JSON.stringify(testRestaurant, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default FavoriteTest;
