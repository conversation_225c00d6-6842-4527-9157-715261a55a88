import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import {
  Truck,
  User,
  Lock,
  Eye,
  EyeOff,
  AlertCircle,
  Shield,
  MapPin,
  Clock,
  ArrowLeft,
  Zap,
  CheckCircle,
} from "lucide-react";
import Button from "../../components/common/Button";
import Input from "../../components/common/Input";
import Card from "../../components/common/Card";

const DeliveryAgentLogin = () => {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [backendStatus, setBackendStatus] = useState(null);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (error) setError("");
  };

  const testBackendConnection = async () => {
    try {
      setBackendStatus("testing");
      const response = await fetch("http://127.0.0.1:8000/api/auth/login/", {
        method: "OPTIONS",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok || response.status === 405) {
        setBackendStatus("connected");
      } else {
        setBackendStatus("error");
      }
    } catch (error) {
      console.error("Backend connection test failed:", error);
      setBackendStatus("disconnected");
    }
  };

  const fillTestCredentials = (option) => {
    if (option === 1) {
      setFormData({
        username: "delivery_agent1",
        password: "delivery123",
      });
    } else if (option === 2) {
      setFormData({
        username: "DA001",
        password: "",
      });
    }
    setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const result = await login(formData.username, formData.password);

      if (result.success) {
        console.log("Login successful, user role:", result.user.role);

        // Check if user is a delivery agent
        if (result.user.role === "delivery_agent") {
          console.log("Redirecting to delivery dashboard...");
          setSuccess(true);
          // Small delay to show success state
          setTimeout(() => {
            navigate("/delivery");
          }, 1000);
        } else {
          setError(
            `Access denied. This login is for delivery agents only. Your role: ${result.user.role}`
          );
        }
      } else {
        setError(result.error?.message || "Invalid username or password");
      }
    } catch (error) {
      console.error("Login error:", error);

      // More detailed error handling
      let errorMessage = "Login failed. Please try again.";

      if (error.response) {
        // Server responded with error
        const status = error.response.status;
        const data = error.response.data;

        if (status === 500) {
          errorMessage =
            "Server error. Please check if the backend is running.";
        } else if (status === 404) {
          errorMessage =
            "Login endpoint not found. Please check API configuration.";
        } else if (status === 400) {
          errorMessage = data?.message || data?.error || "Invalid credentials.";
        } else {
          errorMessage = data?.message || `Server error (${status})`;
        }
      } else if (error.request) {
        // Network error
        errorMessage =
          "Cannot connect to server. Please check if the backend is running on http://127.0.0.1:8000";
      } else {
        // Other error
        errorMessage = error.message || "Login failed. Please try again.";
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='min-h-screen bg-gradient-to-br from-primary-50 via-orange-50 to-primary-100 flex items-center justify-center p-4 relative overflow-hidden'>
      {/* Background Pattern */}
      <div className='absolute inset-0 opacity-5'>
        <div className='absolute top-10 left-10 w-20 h-20 bg-primary-500 rounded-full'></div>
        <div className='absolute top-32 right-20 w-16 h-16 bg-primary-400 rounded-full'></div>
        <div className='absolute bottom-20 left-20 w-24 h-24 bg-primary-300 rounded-full'></div>
        <div className='absolute bottom-40 right-10 w-12 h-12 bg-primary-600 rounded-full'></div>
      </div>

      <div className='w-full max-w-lg relative z-10'>
        {/* Header */}
        <div className='text-center mb-8 animate-fade-in'>
          <div className='flex justify-center mb-6'>
            <div className='relative'>
              <div className='bg-gradient-to-br from-primary-500 to-primary-600 p-4 rounded-2xl shadow-button transform hover:scale-105 transition-transform duration-300'>
                <Truck className='h-10 w-10 text-white' />
              </div>
              <div className='absolute -top-1 -right-1 w-6 h-6 bg-accent-green rounded-full flex items-center justify-center'>
                <Zap className='h-3 w-3 text-white' />
              </div>
            </div>
          </div>
          <h1 className='text-4xl font-bold font-poppins bg-gradient-to-r from-primary-600 to-primary-500 bg-clip-text text-transparent mb-3'>
            Delivery Agent Portal
          </h1>
          <p className='text-text-secondary text-lg'>
            Sign in to access your delivery dashboard
          </p>
          <div className='flex items-center justify-center space-x-6 mt-4 text-sm text-text-secondary'>
            <div className='flex items-center space-x-2'>
              <Shield className='h-4 w-4 text-accent-green' />
              <span>Secure Login</span>
            </div>
            <div className='flex items-center space-x-2'>
              <Clock className='h-4 w-4 text-primary-500' />
              <span>24/7 Access</span>
            </div>
          </div>
        </div>

        {/* Login Form */}
        <Card className='p-8 backdrop-blur-sm bg-white/90 border-0 shadow-2xl animate-slide-up'>
          <form onSubmit={handleSubmit} className='space-y-6'>
            {/* Error Message */}
            {error && (
              <div className='bg-accent-red/10 border border-accent-red/20 rounded-xl p-4 flex items-center space-x-3 animate-fade-in'>
                <AlertCircle className='h-5 w-5 text-accent-red flex-shrink-0' />
                <p className='text-accent-red-dark text-sm font-medium'>
                  {error}
                </p>
              </div>
            )}

            {/* Username Field */}
            <div className='space-y-2'>
              <label className='block text-sm font-semibold text-text-primary mb-2'>
                Agent ID / Username
              </label>
              <div className='relative group'>
                <div className='absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none'>
                  <User className='h-5 w-5 text-primary-400 group-focus-within:text-primary-500 transition-colors' />
                </div>
                <input
                  type='text'
                  name='username'
                  value={formData.username}
                  onChange={handleInputChange}
                  placeholder='Enter your Agent ID (e.g., DA001)'
                  className='block w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-primary-100 focus:border-primary-500 transition-all duration-300 text-text-primary placeholder-gray-400 bg-white hover:border-primary-300'
                  required
                />
              </div>
              <p className='text-xs text-text-secondary mt-2 flex items-center space-x-1'>
                <MapPin className='h-3 w-3' />
                <span>Use the Agent ID provided by your supervisor</span>
              </p>
            </div>

            {/* Password Field */}
            <div className='space-y-2'>
              <label className='block text-sm font-semibold text-text-primary mb-2'>
                Password
              </label>
              <div className='relative group'>
                <div className='absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none'>
                  <Lock className='h-5 w-5 text-primary-400 group-focus-within:text-primary-500 transition-colors' />
                </div>
                <input
                  type={showPassword ? "text" : "password"}
                  name='password'
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder='Enter your password'
                  className='block w-full pl-12 pr-14 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-primary-100 focus:border-primary-500 transition-all duration-300 text-text-primary placeholder-gray-400 bg-white hover:border-primary-300'
                  required
                />
                <button
                  type='button'
                  onClick={() => setShowPassword(!showPassword)}
                  className='absolute inset-y-0 right-0 pr-4 flex items-center hover:bg-primary-50 rounded-r-xl transition-colors'
                >
                  {showPassword ? (
                    <EyeOff className='h-5 w-5 text-primary-400 hover:text-primary-600' />
                  ) : (
                    <Eye className='h-5 w-5 text-primary-400 hover:text-primary-600' />
                  )}
                </button>
              </div>
              <p className='text-xs text-text-secondary mt-2 flex items-center space-x-1'>
                <Shield className='h-3 w-3' />
                <span>
                  Use the temporary password provided by your supervisor
                </span>
              </p>
            </div>

            {/* Login Button */}
            <Button
              type='submit'
              className={`w-full py-4 text-lg font-semibold rounded-xl shadow-button transform transition-all duration-300 disabled:cursor-not-allowed disabled:transform-none ${
                success
                  ? "bg-gradient-to-r from-accent-green to-accent-green-light text-white"
                  : "bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white hover:scale-[1.02]"
              } ${loading || success ? "opacity-90" : ""}`}
              disabled={loading || success}
            >
              {success ? (
                <div className='flex items-center justify-center space-x-2'>
                  <CheckCircle className='h-5 w-5' />
                  <span>Login Successful! Redirecting...</span>
                </div>
              ) : loading ? (
                <div className='flex items-center justify-center space-x-2'>
                  <div className='w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin'></div>
                  <span>Signing in...</span>
                </div>
              ) : (
                <div className='flex items-center justify-center space-x-2'>
                  <Truck className='h-5 w-5' />
                  <span>Sign In to Dashboard</span>
                </div>
              )}
            </Button>
          </form>

          {/* Additional Info */}
          <div className='mt-8 pt-6 border-t border-gray-100'>
            <div className='text-center space-y-3'>
              <div className='flex items-center justify-center space-x-2'>
                <div className='w-2 h-2 bg-accent-green rounded-full animate-pulse'></div>
                <p className='text-sm font-medium text-text-primary'>
                  First time logging in?
                </p>
              </div>
              <p className='text-xs text-text-secondary'>
                You'll be prompted to change your password after first login
              </p>
            </div>
          </div>

          {/* Help Section */}
          <div className='mt-6 p-6 bg-gradient-to-r from-primary-50 to-orange-50 rounded-xl border border-primary-100'>
            <h4 className='text-sm font-bold text-primary-700 mb-3 flex items-center space-x-2'>
              <Shield className='h-4 w-4' />
              <span>Need Help?</span>
            </h4>
            <ul className='text-xs text-primary-600 space-y-2'>
              <li className='flex items-center space-x-2'>
                <div className='w-1.5 h-1.5 bg-primary-500 rounded-full'></div>
                <span>Contact your supervisor for login credentials</span>
              </li>
              <li className='flex items-center space-x-2'>
                <div className='w-1.5 h-1.5 bg-primary-500 rounded-full'></div>
                <span>Agent ID format: DA001, DA002, etc.</span>
              </li>
              <li className='flex items-center space-x-2'>
                <div className='w-1.5 h-1.5 bg-primary-500 rounded-full'></div>
                <span>Password format: DA1234AB (temporary)</span>
              </li>
            </ul>
          </div>

          {/* Development Helper - Only show in development */}
          {import.meta.env.DEV && (
            <div className='mt-4 p-4 bg-blue-50 rounded-xl border border-blue-200'>
              <h4 className='text-sm font-bold text-blue-700 mb-2 flex items-center space-x-2'>
                <Zap className='h-4 w-4' />
                <span>Development Test Credentials</span>
              </h4>
              <div className='text-xs text-blue-600 space-y-3'>
                <div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
                  <div className='p-3 bg-blue-50 rounded-lg border border-blue-200'>
                    <p className='font-semibold mb-2'>Option 1 (Seed Data):</p>
                    <p>Username: delivery_agent1</p>
                    <p>Password: delivery123</p>
                    <button
                      onClick={() => fillTestCredentials(1)}
                      className='mt-2 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors'
                    >
                      Auto-fill
                    </button>
                  </div>
                  <div className='p-3 bg-amber-50 rounded-lg border border-amber-200'>
                    <p className='font-semibold mb-2'>Option 2 (Employee):</p>
                    <p>Username: DA001, DA002, etc.</p>
                    <p>Password: (Admin generated)</p>
                    <button
                      onClick={() => fillTestCredentials(2)}
                      className='mt-2 px-3 py-1 bg-amber-600 text-white text-xs rounded hover:bg-amber-700 transition-colors'
                    >
                      Fill DA001
                    </button>
                  </div>
                </div>
                <p className='text-blue-500 mt-2'>
                  ⚠️ Make sure backend is running on http://127.0.0.1:8000
                </p>
                <p className='text-amber-600 text-xs'>
                  💡 If DA007 doesn't exist, try "delivery_agent1" or create a
                  delivery agent via Admin → Employee Management
                </p>
              </div>
            </div>
          )}
        </Card>

        {/* Footer */}
        <div className='text-center mt-8 animate-fade-in'>
          <Link
            to='/'
            className='inline-flex items-center space-x-2 text-sm text-text-secondary hover:text-primary-600 transition-all duration-300 group'
          >
            <ArrowLeft className='h-4 w-4 group-hover:-translate-x-1 transition-transform' />
            <span className='font-medium'>Back to Home</span>
          </Link>

          {/* Backend Status & Additional Footer Info */}
          <div className='mt-4 pt-4 border-t border-gray-200 space-y-3'>
            {/* Backend Connection Status */}
            <div className='flex items-center justify-center space-x-3'>
              <button
                onClick={testBackendConnection}
                className='text-xs text-gray-500 hover:text-primary-600 transition-colors flex items-center space-x-1'
                disabled={backendStatus === "testing"}
              >
                <div
                  className={`w-2 h-2 rounded-full ${
                    backendStatus === "connected"
                      ? "bg-green-500"
                      : backendStatus === "disconnected"
                      ? "bg-red-500"
                      : backendStatus === "error"
                      ? "bg-yellow-500"
                      : backendStatus === "testing"
                      ? "bg-blue-500 animate-pulse"
                      : "bg-gray-400"
                  }`}
                ></div>
                <span>
                  {backendStatus === "connected"
                    ? "Backend Connected"
                    : backendStatus === "disconnected"
                    ? "Backend Disconnected"
                    : backendStatus === "error"
                    ? "Backend Error"
                    : backendStatus === "testing"
                    ? "Testing..."
                    : "Test Backend Connection"}
                </span>
              </button>
            </div>

            <p className='text-xs text-text-secondary text-center'>
              Powered by{" "}
              <span className='font-semibold text-primary-600'>
                Afghan Sufra
              </span>{" "}
              Delivery Network
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeliveryAgentLogin;
