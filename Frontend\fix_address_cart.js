// Fix address cart issue - Run this in browser console
// This script will clear problematic localStorage data and set up valid addresses

console.log('🔧 Fixing address cart issue...');

// Clear problematic localStorage data
localStorage.removeItem('afghanSofraSelectedAddress');
localStorage.removeItem('afghanSofraAddresses');
localStorage.removeItem('afghanSofraCart');

console.log('✅ Cleared problematic localStorage data');

// Create a valid test address that matches backend
const validTestAddress = {
  id: 43, // Use a valid backend ID
  backendId: 43, // Ensure backend ID is set
  type: 'saved',
  label: 'Test Address',
  address: '123 Main Street, Kabul, Afghanistan',
  street: '123 Main Street',
  city: 'Kabul',
  state: 'Kabul',
  postal_code: '1001',
  country: 'Afghanistan',
  coordinates: [34.5553, 69.2075],
  latitude: 34.5553,
  longitude: 69.2075,
  isDefault: true,
  createdAt: Date.now(),
  source: 'backend'
};

// Set up valid addresses in localStorage
localStorage.setItem('afghanSofraAddresses', JSON.stringify([validTestAddress]));
localStorage.setItem('afghanSofraSelectedAddress', JSON.stringify(validTestAddress));

console.log('✅ Set up valid test address with backend ID 43');
console.log('Address:', validTestAddress);

// Create a clean cart
const cleanCart = {
  items: [],
  total: 0,
  restaurantId: null,
  restaurantName: '',
  addresses: [validTestAddress],
  selectedAddress: validTestAddress
};

localStorage.setItem('afghanSofraCart', JSON.stringify(cleanCart));

console.log('✅ Created clean cart with valid address');
console.log('🎉 Address issue fixed! Please refresh the page.');

// Instructions
console.log(`
📋 Next steps:
1. Refresh the page
2. Add items to cart from a restaurant
3. Go to checkout
4. The address should now work correctly

🔍 Valid address IDs in database: 43, 46, 47
✅ Using address ID 43 for testing
`);
