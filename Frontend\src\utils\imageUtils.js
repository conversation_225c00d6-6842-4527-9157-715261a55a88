/**
 * Image utility functions for handling menu item images
 */

/**
 * Get image URL with fallback to placeholder
 * @param {string} imageUrl - The image URL from API
 * @param {string} itemName - Name of the item for alt text
 * @returns {object} Image props object
 */
export const getImageProps = (imageUrl, itemName = "Menu Item") => {
  if (imageUrl && imageUrl !== "null" && imageUrl !== null) {
    return {
      src: imageUrl,
      alt: itemName,
      hasImage: true
    };
  }
  
  return {
    src: null,
    alt: itemName,
    hasImage: false
  };
};

/**
 * Generate a placeholder image data URL
 * @param {number} width - Image width
 * @param {number} height - Image height
 * @param {string} text - Text to display
 * @param {string} bgColor - Background color
 * @param {string} textColor - Text color
 * @returns {string} Data URL for placeholder image
 */
export const generatePlaceholderImage = (
  width = 300,
  height = 200,
  text = "No Image",
  bgColor = "#f3f4f6",
  textColor = "#9ca3af"
) => {
  const canvas = document.createElement("canvas");
  canvas.width = width;
  canvas.height = height;
  
  const ctx = canvas.getContext("2d");
  
  // Fill background
  ctx.fillStyle = bgColor;
  ctx.fillRect(0, 0, width, height);
  
  // Add text
  ctx.fillStyle = textColor;
  ctx.font = "16px Arial";
  ctx.textAlign = "center";
  ctx.textBaseline = "middle";
  ctx.fillText(text, width / 2, height / 2);
  
  return canvas.toDataURL();
};

/**
 * Check if image URL is valid and accessible
 * @param {string} imageUrl - Image URL to check
 * @returns {Promise<boolean>} Promise that resolves to true if image is valid
 */
export const isImageValid = (imageUrl) => {
  return new Promise((resolve) => {
    if (!imageUrl || imageUrl === "null" || imageUrl === null) {
      resolve(false);
      return;
    }
    
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = imageUrl;
  });
};

/**
 * Create a file preview URL
 * @param {File} file - File object
 * @returns {Promise<string>} Promise that resolves to preview URL
 */
export const createFilePreview = (file) => {
  return new Promise((resolve, reject) => {
    if (!file) {
      reject(new Error("No file provided"));
      return;
    }
    
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.onerror = (e) => reject(e);
    reader.readAsDataURL(file);
  });
};
