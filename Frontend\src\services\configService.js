// Frontend/src/services/configService.js
import { API_BASE_URL } from '../config/api';

class ConfigService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = new Map();
    this.CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  }

  // Cache management
  isCacheValid(key) {
    const expiry = this.cacheExpiry.get(key);
    return expiry && Date.now() < expiry;
  }

  setCache(key, data) {
    this.cache.set(key, data);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_DURATION);
  }

  getCache(key) {
    if (this.isCacheValid(key)) {
      return this.cache.get(key);
    }
    this.cache.delete(key);
    this.cacheExpiry.delete(key);
    return null;
  }

  // API calls
  async fetchWithCache(url, cacheKey) {
    // Check cache first
    const cached = this.getCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await fetch(`${API_BASE_URL}${url}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      
      // Cache the result
      this.setCache(cacheKey, data);
      return data;
    } catch (error) {
      console.error(`Error fetching ${url}:`, error);
      throw error;
    }
  }

  // Get public system configuration
  async getPublicConfig() {
    return this.fetchWithCache('/config/settings/public_config/', 'public_config');
  }

  // Get choice options by type
  async getChoiceOptions(type = null) {
    const url = type 
      ? `/config/choice-options/?type=${type}`
      : '/config/choice-options/by_type/';
    const cacheKey = type ? `choice_options_${type}` : 'choice_options_all';
    
    return this.fetchWithCache(url, cacheKey);
  }

  // Get specific choice option types
  async getUserRoles() {
    return this.getChoiceOptions('user_role');
  }

  async getOrderStatuses() {
    return this.getChoiceOptions('order_status');
  }

  async getPaymentMethods() {
    return this.getChoiceOptions('payment_method');
  }

  async getDietaryRestrictions() {
    return this.getChoiceOptions('dietary_restriction');
  }

  async getVehicleTypes() {
    return this.getChoiceOptions('vehicle_type');
  }

  async getCurrencies() {
    return this.getChoiceOptions('currency');
  }

  // Get system settings
  async getSystemSettings() {
    const config = await this.getPublicConfig();
    return config.settings || [];
  }

  // Get specific system setting by key
  async getSystemSetting(key) {
    const settings = await this.getSystemSettings();
    const setting = settings.find(s => s.key === key);
    return setting ? setting.typed_value : null;
  }

  // Get commonly used settings
  async getSiteName() {
    return this.getSystemSetting('site_name');
  }

  async getSiteDescription() {
    return this.getSystemSetting('site_description');
  }

  async getContactEmail() {
    return this.getSystemSetting('contact_email');
  }

  async getContactPhone() {
    return this.getSystemSetting('contact_phone');
  }

  async getDefaultCurrency() {
    return this.getSystemSetting('default_currency');
  }

  async getDefaultDeliveryFee() {
    return this.getSystemSetting('default_delivery_fee');
  }

  async getFreeDeliveryThreshold() {
    return this.getSystemSetting('free_delivery_threshold');
  }

  async getEstimatedDeliveryTime() {
    return this.getSystemSetting('estimated_delivery_time');
  }

  async getTaxRate() {
    return this.getSystemSetting('tax_rate');
  }

  // Get filter configurations
  async getFilterConfigurations() {
    return this.fetchWithCache('/config/filter-configs/', 'filter_configs');
  }

  // Clear cache (useful for admin updates)
  clearCache() {
    this.cache.clear();
    this.cacheExpiry.clear();
  }

  // Clear specific cache entry
  clearCacheEntry(key) {
    this.cache.delete(key);
    this.cacheExpiry.delete(key);
  }

  // Get all configuration data at once
  async getAllConfig() {
    try {
      const [publicConfig, choiceOptions] = await Promise.all([
        this.getPublicConfig(),
        this.getChoiceOptions()
      ]);

      return {
        settings: publicConfig.settings || [],
        choiceOptions: publicConfig.choice_options || choiceOptions || {},
        filterConfigurations: publicConfig.filter_configurations || []
      };
    } catch (error) {
      console.error('Error fetching all configuration:', error);
      throw error;
    }
  }

  // Transform choice options for frontend use
  transformChoiceOptions(options) {
    return options.map(option => ({
      id: option.value,
      value: option.value,
      label: option.label,
      icon: option.icon,
      color: option.color,
      isDefault: option.is_default
    }));
  }

  // Get transformed dietary restrictions for filters
  async getDietaryRestrictionsForFilters() {
    const options = await this.getDietaryRestrictions();
    return this.transformChoiceOptions(options);
  }

  // Get transformed payment methods for checkout
  async getPaymentMethodsForCheckout() {
    const options = await this.getPaymentMethods();
    return this.transformChoiceOptions(options);
  }

  // Get order status with colors for UI
  async getOrderStatusesWithColors() {
    const options = await this.getOrderStatuses();
    return options.map(option => ({
      value: option.value,
      label: option.label,
      icon: option.icon,
      color: option.color || '#6B7280'
    }));
  }
}

// Create singleton instance
const configService = new ConfigService();

export default configService;
