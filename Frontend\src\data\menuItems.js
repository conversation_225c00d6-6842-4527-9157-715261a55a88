export const mockMenuItems = [
  {
    id: 1,
    name: "Kabuli Pulao",
    description:
      "Traditional Afghan rice dish with tender lamb, raisins, and carrots",
    price: 18.99,
    category: "Main Dishes",
    restaurant: "Afghan Delights",
    image: "/images/kabuli-pulao.jpg",
    isAvailable: true,
    preparationTime: 25,
    ingredients: [
      "Basmati Rice",
      "Lamb",
      "Carrots",
      "Raisins",
      "Onions",
      "Spices",
    ],
    allergens: ["None"],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: true,
    calories: 450,
    rating: 4.8,
    orders: 156,
  },
  {
    id: 2,
    name: "Man<PERSON>",
    description:
      "Steamed dumplings filled with seasoned ground beef and onions",
    price: 14.99,
    category: "Appetizers",
    restaurant: "Afghan Delights",
    image: "/images/mantu.jpg",
    isAvailable: true,
    preparationTime: 20,
    ingredients: ["Ground Beef", "Onions", "Flour", "Yogurt", "Garlic", "Mint"],
    allergens: ["Gluten", "Dairy"],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    calories: 320,
    rating: 4.6,
    orders: 89,
  },
  {
    id: 3,
    name: "Qorma Sabzi",
    description: "Herb stew with tender chunks of lamb and kidney beans",
    price: 16.5,
    category: "Main Dishes",
    restaurant: "Kabul Kitchen",
    image: "/images/qorma-sabzi.jpg",
    isAvailable: true,
    preparationTime: 30,
    ingredients: [
      "Lamb",
      "Fresh Herbs",
      "Kidney Beans",
      "Onions",
      "Garlic",
      "Spices",
    ],
    allergens: ["None"],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: true,
    calories: 380,
    rating: 4.7,
    orders: 124,
  },
  {
    id: 4,
    name: "Ashak",
    description: "Leek-filled dumplings topped with meat sauce and yogurt",
    price: 13.99,
    category: "Main Dishes",
    restaurant: "Pamir Restaurant",
    image: "/images/ashak.jpg",
    isAvailable: true,
    preparationTime: 25,
    ingredients: [
      "Leeks",
      "Ground Beef",
      "Flour",
      "Yogurt",
      "Tomatoes",
      "Spices",
    ],
    allergens: ["Gluten", "Dairy"],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    calories: 350,
    rating: 4.5,
    orders: 67,
  },
  {
    id: 5,
    name: "Bolani",
    description: "Crispy flatbread stuffed with potatoes and herbs",
    price: 8.99,
    category: "Appetizers",
    restaurant: "Afghan Delights",
    image: "/images/bolani.jpg",
    isAvailable: true,
    preparationTime: 15,
    ingredients: [
      "Flour",
      "Potatoes",
      "Green Onions",
      "Cilantro",
      "Oil",
      "Salt",
    ],
    allergens: ["Gluten"],
    isVegetarian: true,
    isVegan: true,
    isGlutenFree: false,
    calories: 220,
    rating: 4.4,
    orders: 98,
  },
  {
    id: 6,
    name: "Chicken Karahi",
    description: "Spicy chicken curry cooked in traditional karahi",
    price: 15.99,
    category: "Main Dishes",
    restaurant: "Kabul Kitchen",
    image: "/images/chicken-karahi.jpg",
    isAvailable: true,
    preparationTime: 22,
    ingredients: [
      "Chicken",
      "Tomatoes",
      "Onions",
      "Ginger",
      "Garlic",
      "Spices",
    ],
    allergens: ["None"],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: true,
    calories: 420,
    rating: 4.6,
    orders: 143,
  },
  {
    id: 7,
    name: "Afghan Naan",
    description: "Traditional Afghan bread baked in tandoor oven",
    price: 3.99,
    category: "Breads",
    restaurant: "Afghan Delights",
    image: "/images/afghan-naan.jpg",
    isAvailable: true,
    preparationTime: 10,
    ingredients: ["Flour", "Yeast", "Milk", "Oil", "Salt", "Nigella Seeds"],
    allergens: ["Gluten", "Dairy"],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    calories: 180,
    rating: 4.3,
    orders: 234,
  },
  {
    id: 8,
    name: "Doogh",
    description: "Refreshing yogurt drink with mint and cucumber",
    price: 4.5,
    category: "Beverages",
    restaurant: "Kabul Kitchen",
    image: "/images/doogh.jpg",
    isAvailable: true,
    preparationTime: 5,
    ingredients: ["Yogurt", "Water", "Mint", "Cucumber", "Salt"],
    allergens: ["Dairy"],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: true,
    calories: 80,
    rating: 4.2,
    orders: 76,
  },
  {
    id: 9,
    name: "Lamb Kebab",
    description: "Grilled lamb skewers marinated in Afghan spices",
    price: 19.99,
    category: "Main Dishes",
    restaurant: "Pamir Restaurant",
    image: "/images/lamb-kebab.jpg",
    isAvailable: true,
    preparationTime: 18,
    ingredients: ["Lamb", "Onions", "Yogurt", "Garlic", "Spices", "Herbs"],
    allergens: ["Dairy"],
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: true,
    calories: 480,
    rating: 4.9,
    orders: 187,
  },
  {
    id: 10,
    name: "Firni",
    description: "Traditional Afghan rice pudding with cardamom and pistachios",
    price: 6.99,
    category: "Desserts",
    restaurant: "Afghan Delights",
    image: "/images/firni.jpg",
    isAvailable: true,
    preparationTime: 12,
    ingredients: [
      "Rice",
      "Milk",
      "Sugar",
      "Cardamom",
      "Pistachios",
      "Rose Water",
    ],
    allergens: ["Dairy", "Nuts"],
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: true,
    calories: 250,
    rating: 4.4,
    orders: 92,
  },
];

export const menuCategories = [
  {
    id: 1,
    name: "Main Dishes",
    description: "Traditional Afghan main courses",
    itemCount: 6,
    isActive: true,
  },
  {
    id: 2,
    name: "Appetizers",
    description: "Delicious starters and small plates",
    itemCount: 2,
    isActive: true,
  },
  {
    id: 3,
    name: "Breads",
    description: "Fresh baked traditional breads",
    itemCount: 1,
    isActive: true,
  },
  {
    id: 4,
    name: "Beverages",
    description: "Refreshing drinks and traditional beverages",
    itemCount: 1,
    isActive: true,
  },
  {
    id: 5,
    name: "Desserts",
    description: "Sweet traditional Afghan desserts",
    itemCount: 1,
    isActive: true,
  },
];
