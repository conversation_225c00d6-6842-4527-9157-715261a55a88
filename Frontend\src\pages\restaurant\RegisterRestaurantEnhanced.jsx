import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useNavigate, Link } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import Input from "../../components/common/Input";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import {
  Store,
  MapPin,
  Phone,
  Clock,
  AlertCircle,
  Upload,
  Utensils,
  DollarSign,
  Info,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  FileText,
  Camera,
  Mail,
  User,
  Building,
  CreditCard,
  Shield,
} from "lucide-react";

const RegisterRestaurantEnhanced = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    trigger,
  } = useForm();
  const { user, updateProfile } = useAuth();
  const {
    createRestaurant,
    loading: restaurantLoading,
    error: restaurantError,
  } = useRestaurant();
  const navigate = useNavigate();
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState(new Set());

  // File uploads
  const [logoFile, setLogoFile] = useState(null);
  const [bannerFile, setBannerFile] = useState(null);
  const [businessLicense, setBusinessLicense] = useState(null);
  const [foodLicense, setFoodLicense] = useState(null);
  const [menuFile, setMenuFile] = useState(null);

  // Previews
  const [logoPreview, setLogoPreview] = useState(null);
  const [bannerPreview, setBannerPreview] = useState(null);

  // Cuisine types
  const [cuisineTypes] = useState([
    { id: "afghan", name: "Afghan" },
    { id: "indian", name: "Indian" },
    { id: "pakistani", name: "Pakistani" },
    { id: "middle-eastern", name: "Middle Eastern" },
    { id: "bbq", name: "BBQ" },
    { id: "vegetarian", name: "Vegetarian" },
    { id: "fast-food", name: "Fast Food" },
    { id: "desserts", name: "Desserts" },
  ]);
  const [selectedCuisines, setSelectedCuisines] = useState([]);

  const steps = [
    {
      id: 1,
      title: "Basic Information",
      description: "Restaurant details and contact information",
      icon: <Store size={20} />,
    },
    {
      id: 2,
      title: "Location & Hours",
      description: "Address and operating hours",
      icon: <MapPin size={20} />,
    },
    {
      id: 3,
      title: "Business Documents",
      description: "Licenses and legal documents",
      icon: <FileText size={20} />,
    },
    {
      id: 4,
      title: "Menu & Photos",
      description: "Restaurant images and menu",
      icon: <Camera size={20} />,
    },
    {
      id: 5,
      title: "Payment & Final",
      description: "Payment details and review",
      icon: <CreditCard size={20} />,
    },
  ];

  // Check if user is logged in and has restaurant role
  useEffect(() => {
    if (!user) {
      navigate("/login?redirect=register-restaurant");
    } else if (user.role !== "restaurant") {
      // Redirect non-restaurant users to partner page
      navigate("/restaurant-partner");
    }
  }, [user, navigate]);

  const handleFileUpload = (file, type) => {
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      switch (type) {
        case "logo":
          setLogoFile(file);
          setLogoPreview(e.target.result);
          break;
        case "banner":
          setBannerFile(file);
          setBannerPreview(e.target.result);
          break;
        case "businessLicense":
          setBusinessLicense(file);
          break;
        case "foodLicense":
          setFoodLicense(file);
          break;
        case "menu":
          setMenuFile(file);
          break;
        default:
          break;
      }
    };
    reader.readAsDataURL(file);
  };

  const handleCuisineChange = (cuisineId) => {
    if (selectedCuisines.includes(cuisineId)) {
      setSelectedCuisines(selectedCuisines.filter((id) => id !== cuisineId));
    } else {
      if (selectedCuisines.length < 3) {
        setSelectedCuisines([...selectedCuisines, cuisineId]);
      }
    }
  };

  const validateStep = async (step) => {
    let fieldsToValidate = [];

    switch (step) {
      case 1:
        fieldsToValidate = ["restaurantName", "email", "phone", "description"];
        break;
      case 2:
        fieldsToValidate = [
          "address",
          "city",
          "state",
          "postalCode",
          "openingTime",
          "closingTime",
        ];
        break;
      case 3:
        // Document validation will be handled separately
        return businessLicense && foodLicense;
      case 4:
        // Image validation
        return logoFile && bannerFile && selectedCuisines.length > 0;
      case 5:
        fieldsToValidate = ["bankName", "accountNumber", "routingNumber"];
        break;
      default:
        return true;
    }

    const result = await trigger(fieldsToValidate);
    return result;
  };

  const nextStep = async () => {
    const isValid = await validateStep(currentStep);
    if (isValid) {
      setCompletedSteps((prev) => new Set([...prev, currentStep]));
      setCurrentStep((prev) => Math.min(prev + 1, steps.length));
    }
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const onSubmit = async (data) => {
    setLoading(true);
    setError(null);

    try {
      // Prepare restaurant data according to API specification
      const restaurantData = {
        name: data.restaurantName,
        description: data.description,
        contact_number: data.phone,
        email: data.email,
        opening_time: data.openingTime || "09:00:00",
        closing_time: data.closingTime || "21:00:00",
        address: {
          street: data.address,
          city: data.city,
          state: data.state || "",
          postal_code: data.postalCode || "",
          country: "USA",
          latitude: 0, // You might want to get this from geocoding
          longitude: 0, // You might want to get this from geocoding
        },
        logo: logoFile,
        banner: bannerFile,
        cuisine_types: selectedCuisines,
        business_license: businessLicense,
        food_license: foodLicense,
        menu_file: menuFile,
        payment_details: {
          bank_name: data.bankName,
          account_number: data.accountNumber,
          routing_number: data.routingNumber,
        },
      };

      // Call the API to create restaurant
      const result = await createRestaurant(restaurantData);

      if (result.success) {
        // Update user profile with restaurant ID
        await updateProfile({
          ...user,
          restaurantId: result.data.id,
        });

        // Redirect to success page
        navigate("/restaurant-registration-success");
      } else {
        setError(result.error || "Failed to create restaurant");
      }
    } catch (err) {
      console.error("Error registering restaurant:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div>
                <Input
                  label='Restaurant Name'
                  placeholder='Enter restaurant name'
                  icon={<Store size={18} />}
                  error={errors.restaurantName?.message}
                  required
                  {...register("restaurantName", {
                    required: "Restaurant name is required",
                    minLength: {
                      value: 3,
                      message: "Name must be at least 3 characters",
                    },
                  })}
                />
              </div>

              <div>
                <Input
                  label='Email Address'
                  type='email'
                  placeholder='Restaurant email address'
                  icon={<Mail size={18} />}
                  error={errors.email?.message}
                  required
                  {...register("email", {
                    required: "Email is required",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: "Please enter a valid email address",
                    },
                  })}
                />
              </div>

              <div>
                <Input
                  label='Phone Number'
                  placeholder='Restaurant phone number'
                  icon={<Phone size={18} />}
                  error={errors.phone?.message}
                  required
                  {...register("phone", {
                    required: "Phone number is required",
                    pattern: {
                      value: /^[0-9+\-\s()]*$/,
                      message: "Please enter a valid phone number",
                    },
                  })}
                />
              </div>

              <div>
                <Input
                  label='Owner Name'
                  placeholder='Restaurant owner name'
                  icon={<User size={18} />}
                  error={errors.ownerName?.message}
                  required
                  {...register("ownerName", {
                    required: "Owner name is required",
                  })}
                />
              </div>
            </div>

            <div>
              <label className='block text-sm font-medium text-text-primary mb-2'>
                Restaurant Description *
              </label>
              <textarea
                className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                rows={4}
                placeholder='Describe your restaurant, cuisine, and what makes it special...'
                {...register("description", {
                  required: "Description is required",
                  minLength: {
                    value: 50,
                    message: "Description must be at least 50 characters",
                  },
                })}
              />
              {errors.description && (
                <p className='text-red-500 text-sm mt-1'>
                  {errors.description.message}
                </p>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div className='md:col-span-2'>
                <Input
                  label='Street Address'
                  placeholder='Restaurant street address'
                  icon={<MapPin size={18} />}
                  error={errors.address?.message}
                  required
                  {...register("address", {
                    required: "Address is required",
                  })}
                />
              </div>

              <div>
                <Input
                  label='City'
                  placeholder='City'
                  icon={<Building size={18} />}
                  error={errors.city?.message}
                  required
                  {...register("city", {
                    required: "City is required",
                  })}
                />
              </div>

              <div>
                <Input
                  label='State'
                  placeholder='State'
                  error={errors.state?.message}
                  {...register("state")}
                />
              </div>

              <div>
                <Input
                  label='Postal Code'
                  placeholder='Postal code'
                  error={errors.postalCode?.message}
                  {...register("postalCode")}
                />
              </div>

              <div>
                <Input
                  label='Opening Time'
                  type='time'
                  icon={<Clock size={18} />}
                  error={errors.openingTime?.message}
                  required
                  {...register("openingTime", {
                    required: "Opening time is required",
                  })}
                />
              </div>

              <div>
                <Input
                  label='Closing Time'
                  type='time'
                  icon={<Clock size={18} />}
                  error={errors.closingTime?.message}
                  required
                  {...register("closingTime", {
                    required: "Closing time is required",
                  })}
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-6'>
              <Shield className='mx-auto text-primary-500 mb-4' size={48} />
              <h3 className='text-lg font-semibold mb-2'>Business Documents</h3>
              <p className='text-text-secondary'>
                Please upload the required business documents. All documents
                should be clear and valid.
              </p>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div>
                <label className='block text-sm font-medium text-text-primary mb-2'>
                  Business License *
                </label>
                <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors'>
                  <input
                    type='file'
                    accept='.pdf,.jpg,.jpeg,.png'
                    onChange={(e) =>
                      handleFileUpload(e.target.files[0], "businessLicense")
                    }
                    className='hidden'
                    id='businessLicense'
                  />
                  <label htmlFor='businessLicense' className='cursor-pointer'>
                    <Upload className='mx-auto text-gray-400 mb-2' size={32} />
                    <p className='text-sm text-gray-600'>
                      {businessLicense
                        ? businessLicense.name
                        : "Click to upload business license"}
                    </p>
                    <p className='text-xs text-gray-500 mt-1'>
                      PDF, JPG, PNG (Max 5MB)
                    </p>
                  </label>
                </div>
              </div>

              <div>
                <label className='block text-sm font-medium text-text-primary mb-2'>
                  Food Safety License *
                </label>
                <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors'>
                  <input
                    type='file'
                    accept='.pdf,.jpg,.jpeg,.png'
                    onChange={(e) =>
                      handleFileUpload(e.target.files[0], "foodLicense")
                    }
                    className='hidden'
                    id='foodLicense'
                  />
                  <label htmlFor='foodLicense' className='cursor-pointer'>
                    <Upload className='mx-auto text-gray-400 mb-2' size={32} />
                    <p className='text-sm text-gray-600'>
                      {foodLicense
                        ? foodLicense.name
                        : "Click to upload food safety license"}
                    </p>
                    <p className='text-xs text-gray-500 mt-1'>
                      PDF, JPG, PNG (Max 5MB)
                    </p>
                  </label>
                </div>
              </div>
            </div>

            <div className='bg-blue-50 p-4 rounded-lg'>
              <h4 className='font-medium text-blue-800 mb-2'>
                Document Requirements:
              </h4>
              <ul className='text-sm text-blue-700 space-y-1'>
                <li>• Business license must be current and valid</li>
                <li>
                  • Food safety certification from local health department
                </li>
                <li>• Documents should be clearly readable</li>
                <li>• File size should not exceed 5MB</li>
              </ul>
            </div>
          </div>
        );

      case 4:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-6'>
              <Camera className='mx-auto text-primary-500 mb-4' size={48} />
              <h3 className='text-lg font-semibold mb-2'>
                Restaurant Images & Menu
              </h3>
              <p className='text-text-secondary'>
                Upload high-quality images and your menu to attract customers.
              </p>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div>
                <label className='block text-sm font-medium text-text-primary mb-2'>
                  Restaurant Logo *
                </label>
                <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors'>
                  <input
                    type='file'
                    accept='.jpg,.jpeg,.png'
                    onChange={(e) =>
                      handleFileUpload(e.target.files[0], "logo")
                    }
                    className='hidden'
                    id='logo'
                  />
                  <label htmlFor='logo' className='cursor-pointer'>
                    {logoPreview ? (
                      <img
                        src={logoPreview}
                        alt='Logo preview'
                        className='mx-auto h-20 w-20 object-cover rounded'
                      />
                    ) : (
                      <Upload
                        className='mx-auto text-gray-400 mb-2'
                        size={32}
                      />
                    )}
                    <p className='text-sm text-gray-600 mt-2'>
                      {logoFile ? logoFile.name : "Click to upload logo"}
                    </p>
                    <p className='text-xs text-gray-500 mt-1'>
                      JPG, PNG (Max 2MB)
                    </p>
                  </label>
                </div>
              </div>

              <div>
                <label className='block text-sm font-medium text-text-primary mb-2'>
                  Cover Photo *
                </label>
                <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors'>
                  <input
                    type='file'
                    accept='.jpg,.jpeg,.png'
                    onChange={(e) =>
                      handleFileUpload(e.target.files[0], "banner")
                    }
                    className='hidden'
                    id='banner'
                  />
                  <label htmlFor='banner' className='cursor-pointer'>
                    {bannerPreview ? (
                      <img
                        src={bannerPreview}
                        alt='Banner preview'
                        className='mx-auto h-20 w-32 object-cover rounded'
                      />
                    ) : (
                      <Upload
                        className='mx-auto text-gray-400 mb-2'
                        size={32}
                      />
                    )}
                    <p className='text-sm text-gray-600 mt-2'>
                      {bannerFile
                        ? bannerFile.name
                        : "Click to upload cover photo"}
                    </p>
                    <p className='text-xs text-gray-500 mt-1'>
                      JPG, PNG (Max 5MB)
                    </p>
                  </label>
                </div>
              </div>
            </div>

            <div>
              <label className='block text-sm font-medium text-text-primary mb-2'>
                Cuisine Types * (Select up to 3)
              </label>
              <div className='grid grid-cols-2 md:grid-cols-4 gap-3'>
                {cuisineTypes.map((cuisine) => (
                  <button
                    key={cuisine.id}
                    type='button'
                    onClick={() => handleCuisineChange(cuisine.id)}
                    className={`p-3 rounded-lg border text-sm font-medium transition-colors ${
                      selectedCuisines.includes(cuisine.id)
                        ? "bg-primary-500 text-white border-primary-500"
                        : "bg-white text-text-primary border-gray-300 hover:border-primary-300"
                    }`}
                    disabled={
                      !selectedCuisines.includes(cuisine.id) &&
                      selectedCuisines.length >= 3
                    }
                  >
                    {cuisine.name}
                  </button>
                ))}
              </div>
              <p className='text-xs text-gray-500 mt-2'>
                Selected: {selectedCuisines.length}/3
              </p>
            </div>

            <div>
              <label className='block text-sm font-medium text-text-primary mb-2'>
                Menu (Optional)
              </label>
              <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-400 transition-colors'>
                <input
                  type='file'
                  accept='.pdf,.jpg,.jpeg,.png'
                  onChange={(e) => handleFileUpload(e.target.files[0], "menu")}
                  className='hidden'
                  id='menu'
                />
                <label htmlFor='menu' className='cursor-pointer'>
                  <Upload className='mx-auto text-gray-400 mb-2' size={32} />
                  <p className='text-sm text-gray-600'>
                    {menuFile
                      ? menuFile.name
                      : "Click to upload menu (optional)"}
                  </p>
                  <p className='text-xs text-gray-500 mt-1'>
                    PDF, JPG, PNG (Max 10MB)
                  </p>
                </label>
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-6'>
              <CreditCard className='mx-auto text-primary-500 mb-4' size={48} />
              <h3 className='text-lg font-semibold mb-2'>
                Payment Information
              </h3>
              <p className='text-text-secondary'>
                Provide your bank details to receive payments from orders.
              </p>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <div>
                <Input
                  label='Bank Name'
                  placeholder='Enter bank name'
                  icon={<Building size={18} />}
                  error={errors.bankName?.message}
                  required
                  {...register("bankName", {
                    required: "Bank name is required",
                  })}
                />
              </div>

              <div>
                <Input
                  label='Account Number'
                  placeholder='Enter account number'
                  error={errors.accountNumber?.message}
                  required
                  {...register("accountNumber", {
                    required: "Account number is required",
                  })}
                />
              </div>

              <div>
                <Input
                  label='Routing Number'
                  placeholder='Enter routing number'
                  error={errors.routingNumber?.message}
                  required
                  {...register("routingNumber", {
                    required: "Routing number is required",
                  })}
                />
              </div>

              <div>
                <Input
                  label='Account Holder Name'
                  placeholder='Enter account holder name'
                  icon={<User size={18} />}
                  error={errors.accountHolderName?.message}
                  required
                  {...register("accountHolderName", {
                    required: "Account holder name is required",
                  })}
                />
              </div>
            </div>

            <div className='bg-green-50 p-4 rounded-lg'>
              <h4 className='font-medium text-green-800 mb-2'>
                Payment Terms:
              </h4>
              <ul className='text-sm text-green-700 space-y-1'>
                <li>• Payments are processed weekly on Fridays</li>
                <li>• Commission rate: 15-20% per order</li>
                <li>• No setup fees or monthly charges</li>
                <li>• Secure payment processing with bank-level encryption</li>
              </ul>
            </div>

            <div className='bg-gray-50 p-6 rounded-lg'>
              <h4 className='font-medium text-gray-800 mb-4'>
                Application Summary
              </h4>
              <div className='space-y-2 text-sm'>
                <div className='flex justify-between'>
                  <span>Restaurant Name:</span>
                  <span className='font-medium'>
                    {watch("restaurantName") || "Not provided"}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Email:</span>
                  <span className='font-medium'>
                    {watch("email") || "Not provided"}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Phone:</span>
                  <span className='font-medium'>
                    {watch("phone") || "Not provided"}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Address:</span>
                  <span className='font-medium'>
                    {watch("address") || "Not provided"}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Cuisine Types:</span>
                  <span className='font-medium'>
                    {selectedCuisines.length} selected
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Documents:</span>
                  <span className='font-medium'>
                    {businessLicense && foodLicense ? "Complete" : "Incomplete"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return <div>Step content for step {currentStep}</div>;
    }
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='container mx-auto px-4 py-8 max-w-4xl'>
        {/* Header */}
        <div className='text-center mb-8'>
          <h1 className='text-3xl font-bold text-text-primary mb-2'>
            Restaurant Registration
          </h1>
          <p className='text-text-secondary'>
            Join Afghan Sofra as a restaurant partner - Complete the application
            in 5 easy steps
          </p>
        </div>

        {/* Progress Steps */}
        <div className='mb-8'>
          <div className='flex items-center justify-between mb-4'>
            {steps.map((step, index) => (
              <div key={step.id} className='flex items-center'>
                <div
                  className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors ${
                    currentStep === step.id
                      ? "bg-primary-500 border-primary-500 text-white"
                      : completedSteps.has(step.id)
                      ? "bg-green-500 border-green-500 text-white"
                      : "bg-white border-gray-300 text-gray-500"
                  }`}
                >
                  {completedSteps.has(step.id) ? (
                    <CheckCircle size={20} />
                  ) : (
                    <span className='text-sm font-medium'>{step.id}</span>
                  )}
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`w-16 h-0.5 mx-2 transition-colors ${
                      completedSteps.has(step.id)
                        ? "bg-green-500"
                        : "bg-gray-300"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>

          <div className='text-center'>
            <h2 className='text-xl font-semibold text-text-primary mb-1'>
              {steps[currentStep - 1]?.title}
            </h2>
            <p className='text-text-secondary text-sm'>
              {steps[currentStep - 1]?.description}
            </p>
          </div>
        </div>

        {/* Error Display */}
        {(error || restaurantError) && (
          <Card className='mb-6 border-l-4 border-red-500'>
            <div className='flex items-start p-4'>
              <AlertCircle
                size={18}
                className='text-red-500 mr-2 mt-0.5 flex-shrink-0'
              />
              <p className='text-red-600 text-sm'>{error || restaurantError}</p>
            </div>
          </Card>
        )}

        {/* Main Form */}
        <Card className='p-6'>
          <form onSubmit={handleSubmit(onSubmit)}>
            {renderStepContent()}

            {/* Navigation Buttons */}
            <div className='flex justify-between items-center mt-8 pt-6 border-t'>
              <div>
                {currentStep > 1 && (
                  <Button
                    type='button'
                    variant='outline'
                    onClick={prevStep}
                    icon={<ArrowLeft size={18} />}
                  >
                    Previous
                  </Button>
                )}
              </div>

              <div className='flex items-center space-x-4'>
                <span className='text-sm text-text-secondary'>
                  Step {currentStep} of {steps.length}
                </span>

                {currentStep < steps.length ? (
                  <Button
                    type='button'
                    variant='primary'
                    onClick={nextStep}
                    icon={<ArrowRight size={18} />}
                    iconPosition='right'
                  >
                    Next Step
                  </Button>
                ) : (
                  <Button
                    type='submit'
                    variant='primary'
                    loading={loading}
                    icon={<CheckCircle size={18} />}
                  >
                    Submit Application
                  </Button>
                )}
              </div>
            </div>
          </form>
        </Card>

        {/* Help Section */}
        <div className='mt-8 text-center'>
          <p className='text-text-secondary text-sm mb-4'>
            Need help with your application?
          </p>
          <div className='flex justify-center space-x-4'>
            <Link
              to='/contact'
              className='text-primary-500 hover:text-primary-600 text-sm font-medium'
            >
              Contact Support
            </Link>
            <span className='text-gray-300'>|</span>
            <Link
              to='/restaurant-partner'
              className='text-primary-500 hover:text-primary-600 text-sm font-medium'
            >
              Learn More
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterRestaurantEnhanced;
