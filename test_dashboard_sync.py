#!/usr/bin/env python3
"""
Test dashboard synchronization with employee accounts
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_dashboard_sync(username, password, description):
    """Test dashboard sync for a specific user"""
    print(f"\n🧪 Testing Dashboard Sync: {description}")
    print("-" * 50)
    
    # Step 1: Login
    login_data = {"user_name": username, "password": password}
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
        if response.status_code == 200:
            login_result = response.json()
            access_token = login_result['data']['access_token']
            user_info = login_result['data']['user']
            print(f"✅ Login: {user_info['name']} (ID: {user_info['id']}, Role: {user_info['role']})")
        else:
            print(f"❌ Login failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Login error: {e}")
        return False
    
    # Step 2: Test Dashboard API
    headers = {"Authorization": f"Bearer {access_token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/delivery-agent/dashboard/", headers=headers)
        print(f"Dashboard API Status: {response.status_code}")
        
        if response.status_code == 200:
            dashboard_data = response.json()
            print("✅ Dashboard API Success!")
            
            # Check if data is user-specific
            agent_info = dashboard_data.get('data', {}).get('agent_info', {})
            print(f"   Agent ID: {agent_info.get('agent_id', 'N/A')}")
            print(f"   Full Name: {agent_info.get('full_name', 'N/A')}")
            print(f"   Status: {agent_info.get('status', 'N/A')}")
            print(f"   Availability: {agent_info.get('availability', 'N/A')}")
            
            # Check if agent info matches logged-in user
            if agent_info.get('full_name') == user_info.get('name'):
                print("✅ Dashboard data matches logged-in user!")
                return True
            else:
                print(f"⚠️  Dashboard name '{agent_info.get('full_name')}' doesn't match user name '{user_info.get('name')}'")
                print("   This might be expected if names are different in profile vs user table")
                return True
                
        elif response.status_code == 404:
            print("❌ Dashboard API: Profile not found")
            print("   This user doesn't have a delivery agent profile")
            return False
        else:
            print(f"❌ Dashboard API failed: {response.status_code}")
            error_data = response.json() if response.content else {}
            print(f"   Error: {error_data}")
            return False
            
    except Exception as e:
        print(f"❌ Dashboard API error: {e}")
        return False

def test_profile_vs_dashboard_consistency():
    """Test if profile and dashboard return consistent data"""
    print(f"\n🔍 Testing Profile vs Dashboard Consistency")
    print("-" * 50)
    
    # Login with EMP001
    login_data = {"user_name": "EMP001", "password": "employee123"}
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
        access_token = response.json()['data']['access_token']
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Get profile data
        profile_response = requests.get(f"{BASE_URL}/delivery-agent/profile/", headers=headers)
        profile_data = profile_response.json()['data']
        
        # Get dashboard data
        dashboard_response = requests.get(f"{BASE_URL}/delivery-agent/dashboard/", headers=headers)
        dashboard_data = dashboard_response.json()['data']['agent_info']
        
        print("Profile Data:")
        print(f"   Agent ID: {profile_data.get('agent_id')}")
        print(f"   Full Name: {profile_data.get('full_name')}")
        print(f"   Employment Status: {profile_data.get('employment_status')}")
        
        print("\nDashboard Data:")
        print(f"   Agent ID: {dashboard_data.get('agent_id')}")
        print(f"   Full Name: {dashboard_data.get('full_name')}")
        print(f"   Status: {dashboard_data.get('status')}")
        
        # Check consistency
        if (profile_data.get('agent_id') == dashboard_data.get('agent_id') and
            profile_data.get('full_name') == dashboard_data.get('full_name')):
            print("\n✅ Profile and Dashboard data are consistent!")
            return True
        else:
            print("\n⚠️  Profile and Dashboard data have inconsistencies")
            return False
            
    except Exception as e:
        print(f"❌ Consistency test error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing Dashboard Synchronization with Employee Accounts")
    print("=" * 60)
    
    test_cases = [
        ("EMP001", "employee123", "Test Employee"),
        ("delivery", "delivery123", "Delivery Agent Jan"),
    ]
    
    results = []
    for username, password, description in test_cases:
        success = test_dashboard_sync(username, password, description)
        results.append((description, success))
    
    # Test consistency
    consistency_ok = test_profile_vs_dashboard_consistency()
    
    print("\n" + "=" * 60)
    print("📊 DASHBOARD SYNC TEST RESULTS")
    print("=" * 60)
    
    for description, success in results:
        status = "✅ SYNCED" if success else "❌ NOT SYNCED"
        print(f"{status} {description}")
    
    consistency_status = "✅ CONSISTENT" if consistency_ok else "❌ INCONSISTENT"
    print(f"{consistency_status} Profile vs Dashboard Data")
    
    working_dashboards = sum(1 for _, success in results if success)
    print(f"\n🎯 {working_dashboards}/{len(results)} dashboards properly synced")
    
    if working_dashboards == len(results) and consistency_ok:
        print("✅ All dashboards are properly synced with employee accounts!")
        print("The frontend should display user-specific data correctly.")
    else:
        print("⚠️  Some dashboards have sync issues.")
        print("Check the backend API responses and frontend data handling.")
    
    print("\n📋 Next Steps:")
    print("1. Login to frontend with EMP001 / employee123")
    print("2. Navigate to /delivery dashboard")
    print("3. Verify that the dashboard shows:")
    print("   - Correct Agent ID (EMP001)")
    print("   - Correct Full Name (Test Employee)")
    print("   - User-specific employment status and stats")

if __name__ == "__main__":
    main()
