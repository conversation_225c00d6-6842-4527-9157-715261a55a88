import React, { useState } from "react";
import { Car, Camera, Upload, Calendar, Palette } from "lucide-react";

const VehicleInfoStep = ({ data, onChange, errors }) => {
  const [photoPreview, setPhotoPreview] = useState(null);

  const vehicleTypes = [
    { value: "motorcycle", label: "Motorcycle", icon: "🏍️" },
    { value: "bicycle", label: "Bicycle", icon: "🚲" },
    { value: "car", label: "Car", icon: "🚗" },
    { value: "scooter", label: "Scooter", icon: "🛵" },
  ];

  const handlePhotoUpload = (file) => {
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target.result);
      };
      reader.readAsDataURL(file);

      // Add to vehicle photos array
      const newPhotos = [...(data.vehiclePhotos || []), file];
      onChange("vehiclePhotos", newPhotos);
    }
  };

  return (
    <div className='space-y-6'>
      {/* Vehicle Type Selection */}
      <div>
        <label className='block text-sm font-medium text-gray-700 mb-3'>
          <Car className='inline h-4 w-4 mr-1' />
          Vehicle Type *
        </label>
        <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
          {vehicleTypes.map((type) => (
            <button
              key={type.value}
              type='button'
              onClick={() => onChange("type", type.value)}
              className={`p-6 border-2 rounded-xl text-center transition-all duration-200 hover:shadow-lg transform hover:scale-105 ${
                data.type === type.value
                  ? "border-primary-500 bg-gradient-to-br from-primary-50 to-orange-50 text-primary-700 shadow-lg"
                  : "border-gray-300 hover:border-primary-300 hover:bg-gray-50"
              }`}
            >
              <div className='text-3xl mb-3'>{type.icon}</div>
              <div className='text-sm font-semibold'>{type.label}</div>
            </button>
          ))}
        </div>
        {errors.vehicleType && (
          <p className='mt-1 text-sm text-red-600'>{errors.vehicleType}</p>
        )}
      </div>

      {/* Vehicle Details */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Make
          </label>
          <input
            type='text'
            value={data.make}
            onChange={(e) => onChange("make", e.target.value)}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            placeholder='e.g., Honda, Toyota'
          />
        </div>

        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Model
          </label>
          <input
            type='text'
            value={data.model}
            onChange={(e) => onChange("model", e.target.value)}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            placeholder='e.g., Civic, Corolla'
          />
        </div>

        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            <Calendar className='inline h-4 w-4 mr-1' />
            Year
          </label>
          <input
            type='number'
            value={data.year}
            onChange={(e) => onChange("year", e.target.value)}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            placeholder='2020'
            min='1990'
            max={new Date().getFullYear() + 1}
          />
        </div>

        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            <Palette className='inline h-4 w-4 mr-1' />
            Color
          </label>
          <input
            type='text'
            value={data.color}
            onChange={(e) => onChange("color", e.target.value)}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            placeholder='e.g., Red, Blue, Black'
          />
        </div>
      </div>

      {/* License Plate */}
      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            License Plate Number *
          </label>
          <input
            type='text'
            value={data.licensePlate}
            onChange={(e) =>
              onChange("licensePlate", e.target.value.toUpperCase())
            }
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.licensePlate ? "border-red-500" : "border-gray-300"
            }`}
            placeholder='ABC-123'
            style={{ textTransform: "uppercase" }}
          />
          {errors.licensePlate && (
            <p className='mt-1 text-sm text-red-600'>{errors.licensePlate}</p>
          )}
        </div>

        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Insurance Expiry Date
          </label>
          <input
            type='date'
            value={data.insuranceExpiry}
            onChange={(e) => onChange("insuranceExpiry", e.target.value)}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
            min={new Date().toISOString().split("T")[0]}
          />
        </div>
      </div>

      {/* Vehicle Photos */}
      <div>
        <label className='block text-sm font-medium text-gray-700 mb-3'>
          <Camera className='inline h-4 w-4 mr-1' />
          Vehicle Photos
        </label>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          {/* Photo Upload Areas */}
          {["Front View", "Side View", "License Plate", "Interior"].map(
            (photoType, index) => (
              <div
                key={photoType}
                className='border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 hover:shadow-lg'
              >
                <div className='space-y-3'>
                  <Camera className='h-10 w-10 text-gray-400 mx-auto' />
                  <p className='text-sm font-semibold text-gray-700'>
                    {photoType}
                  </p>
                  <label className='cursor-pointer bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-lg text-sm hover:from-primary-600 hover:to-primary-700 transition-all duration-200 inline-block shadow-md hover:shadow-lg transform hover:scale-105'>
                    Upload Photo
                    <input
                      type='file'
                      accept='image/*'
                      onChange={(e) => handlePhotoUpload(e.target.files[0])}
                      className='hidden'
                    />
                  </label>
                </div>
              </div>
            )
          )}
        </div>

        {/* Photo Preview */}
        {data.vehiclePhotos && data.vehiclePhotos.length > 0 && (
          <div className='mt-4'>
            <p className='text-sm font-medium text-gray-700 mb-2'>
              Uploaded Photos ({data.vehiclePhotos.length})
            </p>
            <div className='flex space-x-2 overflow-x-auto'>
              {data.vehiclePhotos.map((photo, index) => (
                <div key={index} className='flex-shrink-0'>
                  <img
                    src={URL.createObjectURL(photo)}
                    alt={`Vehicle photo ${index + 1}`}
                    className='w-20 h-20 object-cover rounded-lg border'
                  />
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Vehicle Requirements */}
      <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
        <h3 className='text-sm font-medium text-blue-800 mb-2'>
          Vehicle Requirements
        </h3>
        <ul className='text-sm text-blue-700 space-y-1'>
          <li>• Vehicle must be in good working condition</li>
          <li>• Valid registration and insurance required</li>
          <li>• Regular maintenance records preferred</li>
          <li>• Vehicle should be suitable for food delivery</li>
        </ul>
      </div>
    </div>
  );
};

export default VehicleInfoStep;
