import React, { useState } from "react";
import {
  Star,
  Gift,
  Trophy,
  TrendingUp,
  Users,
  Crown,
  Award,
  Sparkles,
  Play,
  RotateCcw,
  ShoppingBag,
} from "lucide-react";
import { useLoyalty } from "../../context/LoyaltyContext";
import { useAuth } from "../../context/AuthContext";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import LoyaltyWidget from "../../components/loyalty/LoyaltyWidget";
import LoyaltyDashboard from "../../components/loyalty/LoyaltyDashboard";
import RewardsStore from "../../components/loyalty/RewardsStore";

const LoyaltyDemo = () => {
  const { user } = useAuth();
  const {
    loyaltyData,
    addPoints,
    redeemReward,
    LOYALTY_TIERS,
    REWARDS,
    POINTS_RULES,
    POINTS_VALUES,
  } = useLoyalty();

  const [activeDemo, setActiveDemo] = useState("overview");
  const [isSimulating, setIsSimulating] = useState(false);

  const simulateOrderCompletion = () => {
    if (!loyaltyData || isSimulating) return;

    setIsSimulating(true);
    
    // Simulate different order amounts
    const orderAmounts = [25.99, 45.50, 18.75, 62.30];
    const randomAmount = orderAmounts[Math.floor(Math.random() * orderAmounts.length)];
    
    const tier = LOYALTY_TIERS[loyaltyData.tier];
    const basePoints = Math.floor(randomAmount * POINTS_VALUES[POINTS_RULES.ORDER_COMPLETION]);
    const earnedPoints = Math.floor(basePoints * tier.pointsMultiplier);

    setTimeout(() => {
      addPoints(earnedPoints, `Order completion - $${randomAmount.toFixed(2)}`, `order-${Date.now()}`);
      setIsSimulating(false);
    }, 1500);
  };

  const simulateReferral = () => {
    if (!loyaltyData) return;
    addPoints(POINTS_VALUES[POINTS_RULES.REFERRAL_SIGNUP], "Friend referral signup");
  };

  const simulateReview = () => {
    if (!loyaltyData) return;
    addPoints(POINTS_VALUES[POINTS_RULES.REVIEW_SUBMISSION], "Restaurant review submitted");
  };

  const simulateBirthdayBonus = () => {
    if (!loyaltyData) return;
    addPoints(POINTS_VALUES[POINTS_RULES.BIRTHDAY_BONUS], "Birthday bonus points");
  };

  const resetDemo = () => {
    // This would require additional context methods to reset data
    window.location.reload();
  };

  const TierIcon = ({ tier, size = 24 }) => {
    const icons = {
      BRONZE: <Award className="text-amber-600" size={size} />,
      SILVER: <Star className="text-gray-500" size={size} />,
      GOLD: <Crown className="text-yellow-500" size={size} />,
      PLATINUM: <Trophy className="text-purple-600" size={size} />,
    };
    return icons[tier] || icons.BRONZE;
  };

  if (!user || user.role !== "customer") {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="text-center p-8">
          <Trophy size={48} className="mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">
            Customer Loyalty Demo
          </h3>
          <p className="text-gray-500">
            Please log in as a customer to access the loyalty program demo.
          </p>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Customer Loyalty & Rewards Demo
        </h1>
        <p className="text-gray-600">
          Experience our comprehensive loyalty program with points, tiers, and rewards
        </p>
      </div>

      {/* Demo Controls */}
      <Card>
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <Sparkles size={20} className="text-orange-500 mr-2" />
            Demo Controls
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Button
              variant="primary"
              icon={<ShoppingBag size={16} />}
              onClick={simulateOrderCompletion}
              disabled={isSimulating}
              className="w-full"
            >
              {isSimulating ? "Processing..." : "Complete Order"}
            </Button>

            <Button
              variant="outline"
              icon={<Users size={16} />}
              onClick={simulateReferral}
              className="w-full"
            >
              Refer Friend
            </Button>

            <Button
              variant="outline"
              icon={<Star size={16} />}
              onClick={simulateReview}
              className="w-full"
            >
              Submit Review
            </Button>

            <Button
              variant="outline"
              icon={<Gift size={16} />}
              onClick={simulateBirthdayBonus}
              className="w-full"
            >
              Birthday Bonus
            </Button>

            <Button
              variant="outline"
              icon={<RotateCcw size={16} />}
              onClick={resetDemo}
              className="w-full"
            >
              Reset Demo
            </Button>
          </div>

          {loyaltyData && (
            <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <span className="text-orange-800">
                  Current Status: {LOYALTY_TIERS[loyaltyData.tier].name} Member
                </span>
                <span className="text-orange-600 font-semibold">
                  {loyaltyData.availablePoints} points available
                </span>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Demo Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: "overview", label: "Overview", icon: <Trophy size={16} /> },
            { id: "widget", label: "Loyalty Widget", icon: <Star size={16} /> },
            { id: "dashboard", label: "Full Dashboard", icon: <TrendingUp size={16} /> },
            { id: "rewards", label: "Rewards Store", icon: <Gift size={16} /> },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveDemo(tab.id)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeDemo === tab.id
                  ? "border-orange-500 text-orange-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {tab.icon}
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Demo Content */}
      <div className="mt-6">
        {activeDemo === "overview" && (
          <div className="space-y-6">
            {/* Tier Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Object.entries(LOYALTY_TIERS).map(([tierKey, tier]) => (
                <Card key={tierKey} className={loyaltyData?.tier === tierKey ? "ring-2 ring-orange-500" : ""}>
                  <div className="p-6 text-center">
                    <div
                      className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4"
                      style={{ backgroundColor: tier.bgColor }}
                    >
                      <TierIcon tier={tierKey} />
                    </div>
                    <h3 className="font-semibold text-lg mb-2" style={{ color: tier.color }}>
                      {tier.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3">
                      {tier.minPoints === 0 ? "0" : tier.minPoints}
                      {tier.maxPoints === Infinity ? "+" : ` - ${tier.maxPoints}`} points
                    </p>
                    <div className="space-y-1">
                      {tier.benefits.slice(0, 2).map((benefit, index) => (
                        <p key={index} className="text-xs text-gray-500">
                          {benefit}
                        </p>
                      ))}
                    </div>
                    {loyaltyData?.tier === tierKey && (
                      <Badge variant="primary" size="small" className="mt-3">
                        Current Tier
                      </Badge>
                    )}
                  </div>
                </Card>
              ))}
            </div>

            {/* Points Earning Rules */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">How to Earn Points</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                      <ShoppingBag size={20} className="text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium">Complete Orders</p>
                      <p className="text-sm text-gray-500">
                        {POINTS_VALUES[POINTS_RULES.ORDER_COMPLETION]} point per $1 spent (multiplied by tier)
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <Users size={20} className="text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">Refer Friends</p>
                      <p className="text-sm text-gray-500">
                        {POINTS_VALUES[POINTS_RULES.REFERRAL_SIGNUP]} points per successful referral
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                      <Star size={20} className="text-yellow-600" />
                    </div>
                    <div>
                      <p className="font-medium">Write Reviews</p>
                      <p className="text-sm text-gray-500">
                        {POINTS_VALUES[POINTS_RULES.REVIEW_SUBMISSION]} points per restaurant review
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                      <Gift size={20} className="text-purple-600" />
                    </div>
                    <div>
                      <p className="font-medium">Birthday Bonus</p>
                      <p className="text-sm text-gray-500">
                        {POINTS_VALUES[POINTS_RULES.BIRTHDAY_BONUS]} points on your birthday
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Available Rewards Preview */}
            <Card>
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Available Rewards</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {Object.values(REWARDS).map((reward) => (
                    <div key={reward.id} className="border border-gray-200 rounded-lg p-4 text-center">
                      <Gift size={24} className="text-orange-500 mx-auto mb-2" />
                      <h4 className="font-medium text-sm mb-1">{reward.name}</h4>
                      <p className="text-xs text-gray-600 mb-2">{reward.description}</p>
                      <Badge variant="outline" size="small">
                        {reward.pointsCost} pts
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        )}

        {activeDemo === "widget" && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Loyalty Widget Variants</h3>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-3">Compact Widget</h4>
                <LoyaltyWidget variant="compact" />
              </div>
              
              <div>
                <h4 className="font-medium mb-3">Full Widget</h4>
                <LoyaltyWidget variant="full" />
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3">Minimal Widget (for headers/navigation)</h4>
              <div className="bg-gray-100 p-4 rounded-lg">
                <LoyaltyWidget variant="minimal" />
              </div>
            </div>
          </div>
        )}

        {activeDemo === "dashboard" && <LoyaltyDashboard />}
        {activeDemo === "rewards" && <RewardsStore />}
      </div>
    </div>
  );
};

export default LoyaltyDemo;
