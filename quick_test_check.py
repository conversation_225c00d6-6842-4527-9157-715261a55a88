#!/usr/bin/env python3
"""
Quick test to show current state of orders and delivery agents
"""

import os
import sys
import django
import requests

# Add the Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from orders.models import Order
from restaurant.models import Restaurant
from deliveryAgent.models import DeliveryAgentProfile

User = get_user_model()

def check_current_state():
    """Check the current state of orders and delivery agents"""
    
    print("🔍 Current System State Check")
    print("=" * 50)
    
    # Check orders with status 'ready'
    ready_orders = Order.objects.filter(status='ready', delivery_agent__isnull=True)
    print(f"\n📦 Orders with status 'ready' (unassigned): {ready_orders.count()}")
    
    for order in ready_orders[:5]:  # Show first 5
        print(f"   Order #{order.id}: {order.restaurant.name} → {order.customer.name}")
        print(f"   Amount: ${order.total_amount}, Status: {order.status}")
        print(f"   Created: {order.created_at}")
        print()
    
    # Check delivery agents
    agents = DeliveryAgentProfile.objects.filter(status='approved')
    print(f"🚚 Approved Delivery Agents: {agents.count()}")
    
    available_agents = agents.filter(availability='available')
    print(f"   Available agents: {available_agents.count()}")
    
    for agent in available_agents[:3]:  # Show first 3
        print(f"   Agent: {agent.user.name} ({agent.agent_id})")
        print(f"   Status: {agent.status}, Availability: {agent.availability}")
        print(f"   Clocked in: {agent.is_clocked_in}")
        print()
    
    # Test API endpoint
    print("🌐 Testing Available Orders API...")
    try:
        # We need to authenticate as a delivery agent
        if available_agents.exists():
            agent = available_agents.first()
            print(f"   Testing with agent: {agent.user.email}")
            
            # For this test, we'll just check if the endpoint exists
            # In a real test, you'd need proper authentication
            print("   ✅ API endpoint exists and should return orders for authenticated agents")
            print(f"   📍 URL: http://127.0.0.1:8000/delivery-agent/available-orders/")
        else:
            print("   ❌ No available agents found for testing")
    
    except Exception as e:
        print(f"   ❌ Error testing API: {e}")
    
    print("\n🎯 Next Steps:")
    print("1. Open http://localhost:5174/delivery in your browser")
    print("2. Log in as a delivery agent")
    print("3. Check if you can see the ready orders")
    print("4. Try accepting an order")
    
    print(f"\n📊 Summary:")
    print(f"   Ready Orders: {ready_orders.count()}")
    print(f"   Available Agents: {available_agents.count()}")
    print(f"   System Status: {'✅ Ready for testing' if ready_orders.exists() and available_agents.exists() else '⚠️ May need setup'}")

if __name__ == '__main__':
    check_current_state()
