#!/usr/bin/env python3
"""
Test script to verify the delivery address selection fix
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

def test_address_creation_and_selection():
    """Test creating addresses and selecting them for checkout"""
    
    print("🧪 Testing Delivery Address Selection Fix")
    print("=" * 50)
    
    # Step 1: Login as a customer
    print("\n1. Logging in as customer...")
    login_data = {
        "email": "<EMAIL>",
        "password": "testpass123"
    }
    
    try:
        response = requests.post(f"{API_BASE}/auth/login/", json=login_data)
        if response.status_code == 200:
            token = response.json().get('access_token')
            headers = {'Authorization': f'Bearer {token}'}
            print("   ✅ Login successful")
        else:
            print(f"   ❌ Login failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Login error: {e}")
        return False
    
    # Step 2: Check existing addresses
    print("\n2. Checking existing addresses...")
    try:
        response = requests.get(f"{API_BASE}/restaurant/addresses/", headers=headers)
        if response.status_code == 200:
            addresses = response.json()
            print(f"   ✅ Found {len(addresses)} existing addresses")
            for addr in addresses:
                print(f"      - ID: {addr['id']}, Street: {addr['street']}, City: {addr['city']}")
        else:
            print(f"   ❌ Failed to get addresses: {response.status_code}")
            addresses = []
    except Exception as e:
        print(f"   ❌ Error getting addresses: {e}")
        addresses = []
    
    # Step 3: Create a new address if none exist
    if not addresses:
        print("\n3. Creating a new saved address...")
        address_data = {
            "street": "123 Test Street",
            "city": "Kabul",
            "state": "Kabul Province",
            "postal_code": "1001",
            "country": "Afghanistan",
            "latitude": "34.5553",
            "longitude": "69.2075"
        }
        
        try:
            response = requests.post(f"{API_BASE}/restaurant/addresses/", json=address_data, headers=headers)
            if response.status_code == 201:
                new_address = response.json()
                print(f"   ✅ Created new address with ID: {new_address['id']}")
                addresses = [new_address]
            else:
                print(f"   ❌ Failed to create address: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
        except Exception as e:
            print(f"   ❌ Error creating address: {e}")
            return False
    else:
        print("\n3. Using existing addresses...")
    
    # Step 4: Test address validation logic
    print("\n4. Testing address validation logic...")
    
    if addresses:
        test_address = addresses[0]
        
        # Simulate frontend address object (saved type)
        saved_address = {
            "id": test_address['id'],
            "backendId": test_address['id'],
            "type": "saved",
            "address": f"{test_address['street']}, {test_address['city']}, {test_address['state']}, {test_address['country']}",
            "coordinates": [float(test_address['latitude']), float(test_address['longitude'])]
        }
        
        # Test validation logic
        def validate_address_for_delivery(address):
            """Simulate the frontend validation logic"""
            if not address:
                return False, "No address selected"
            
            # Check if address has a backend ID (meaning it's saved in the database)
            has_backend_id = address.get('backendId') or (
                address.get('id') and not str(address['id']).startswith('current_')
            )
            
            is_valid_for_delivery = (
                address.get('type') == "saved" or 
                (address.get('type') == "current" and address.get('coordinates')) or
                has_backend_id
            )
            
            if not is_valid_for_delivery:
                return False, "Address not valid for delivery"
            
            # For current location addresses, we need to save them first
            if address.get('type') == "current" and not address.get('backendId'):
                return False, "Current location needs to be saved first"
            
            return True, "Address is valid"
        
        # Test saved address
        is_valid, message = validate_address_for_delivery(saved_address)
        print(f"   Saved address validation: {'✅' if is_valid else '❌'} {message}")
        
        # Test current location address (not saved)
        current_address = {
            "id": "current_123456",
            "type": "current",
            "address": "Current Location, Kabul",
            "coordinates": [34.5553, 69.2075]
        }
        is_valid, message = validate_address_for_delivery(current_address)
        print(f"   Current location validation: {'✅' if is_valid else '❌'} {message}")
        
        # Test current location address (saved)
        saved_current_address = {
            "id": test_address['id'],
            "backendId": test_address['id'],
            "type": "current",
            "address": "Current Location, Kabul",
            "coordinates": [34.5553, 69.2075]
        }
        is_valid, message = validate_address_for_delivery(saved_current_address)
        print(f"   Saved current location validation: {'✅' if is_valid else '❌'} {message}")
        
        return True
    else:
        print("   ❌ No addresses available for testing")
        return False

def main():
    """Main test function"""
    print("🚀 Starting Delivery Address Fix Test")
    
    success = test_address_creation_and_selection()
    
    if success:
        print("\n" + "=" * 50)
        print("✅ All tests passed! The delivery address fix should work correctly.")
        print("\nKey improvements:")
        print("- Accepts saved addresses (type: 'saved')")
        print("- Accepts current location addresses that have been saved (have backendId)")
        print("- Rejects temporary current location addresses (need to be saved first)")
        print("- Provides clear error messages for each case")
    else:
        print("\n" + "=" * 50)
        print("❌ Some tests failed. Please check the backend setup.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
