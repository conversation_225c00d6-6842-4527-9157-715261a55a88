import requests
import json

def test_agents_api_structure():
    """Test the agents API to see its exact structure"""
    
    print("🔍 Testing Agents API Structure")
    print("=" * 50)
    
    # Login as admin first
    login_url = "http://127.0.0.1:8000/api/auth/login/"
    login_data = {
        "user_name": "admin_user_3602",
        "password": "admin123"
    }
    
    print("1. Logging in as admin...")
    login_response = requests.post(login_url, json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Admin login failed: {login_response.status_code}")
        return
    
    login_result = login_response.json()
    if not login_result.get('success'):
        print(f"❌ Admin login failed: {login_result}")
        return
    
    token = login_result['data']['access_token']
    print("✅ Admin login successful")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test the agents API
    print("\n2. Testing agents API structure...")
    agents_url = "http://127.0.0.1:8000/api/delivery-agent/admin/employees/"
    agents_response = requests.get(agents_url, headers=headers)
    
    print(f"Agents API Status Code: {agents_response.status_code}")
    
    if agents_response.status_code == 200:
        try:
            agents_data = agents_response.json()
            print(f"✅ Agents API working")
            print(f"\n📋 Response Structure:")
            print(f"   Type: {type(agents_data)}")
            print(f"   Keys: {list(agents_data.keys()) if isinstance(agents_data, dict) else 'Not a dict'}")
            
            if isinstance(agents_data, dict):
                for key, value in agents_data.items():
                    print(f"   {key}: {type(value)} (length: {len(value) if hasattr(value, '__len__') else 'N/A'})")
                    if key == 'data':
                        if isinstance(value, dict):
                            print(f"      Data keys: {list(value.keys())}")
                            for sub_key, sub_value in value.items():
                                print(f"      {sub_key}: {type(sub_value)} (length: {len(sub_value) if hasattr(sub_value, '__len__') else 'N/A'})")
                                if isinstance(sub_value, list) and len(sub_value) > 0:
                                    print(f"         Sample item: {sub_value[0]}")
                        elif isinstance(value, list) and len(value) > 0:
                            print(f"      Sample agent: {value[0]}")
            
            print(f"\n🔧 Recommended Fix:")
            if isinstance(agents_data, dict) and 'data' in agents_data:
                if isinstance(agents_data['data'], list):
                    print(f"   ✅ Use: agentsResponse.data.data (it's an array)")
                else:
                    print(f"   ❌ agentsResponse.data.data is not an array: {type(agents_data['data'])}")
            else:
                print(f"   ❌ No 'data' key found in response")
                
        except json.JSONDecodeError:
            print("❌ Agents API returned non-JSON response")
            print(f"Response text: {agents_response.text[:200]}...")
    else:
        print(f"❌ Agents API failed with status {agents_response.status_code}")
        print(f"Response: {agents_response.text[:200]}...")

if __name__ == "__main__":
    test_agents_api_structure()
