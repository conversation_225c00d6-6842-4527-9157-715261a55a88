/**
 * Initialize New Address System
 * Run this script in the browser console to set up the new address system
 */

console.log('🚀 Initializing New Address System...');

// Clear all old address data
console.log('🧹 Clearing old address data...');
localStorage.removeItem('afghanSofraAddresses');
localStorage.removeItem('afghanSofraSelectedAddress');
localStorage.removeItem('afghanSofra_addresses');
localStorage.removeItem('afghanSofra_selectedAddress');
localStorage.removeItem('afghanSofraCart');

// Clear any other potential address-related keys
const keysToRemove = [];
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  if (key && (key.includes('address') || key.includes('Address') || key.includes('location'))) {
    keysToRemove.push(key);
  }
}

keysToRemove.forEach(key => {
  localStorage.removeItem(key);
  console.log(`🗑️ Removed: ${key}`);
});

console.log('✅ Old address data cleared');

// Instructions for the user
console.log(`
🎉 New Address System Initialized!

📋 Next Steps:
1. Refresh the page (F5)
2. The new address system will automatically sync with the backend
3. Go to checkout and you should see the new address selector
4. Your existing addresses from the database will be loaded automatically

🔧 Features of the New System:
✅ Automatic backend synchronization
✅ Robust error handling
✅ Proper address validation
✅ No more localStorage conflicts
✅ Real-time address management

🐛 If you encounter any issues:
1. Check the browser console for detailed logs
2. All address operations are logged with 🏠 prefix
3. The system will fallback gracefully if backend is unavailable

🎯 The address validation error should now be completely resolved!
`);

// Create a clean cart object
const cleanCart = {
  items: [],
  total: 0,
  restaurantId: null,
  restaurantName: '',
  deliveryFee: 0,
  tax: 0,
  subtotal: 0
};

localStorage.setItem('afghanSofraCart', JSON.stringify(cleanCart));
console.log('🛒 Clean cart created');

console.log('🎉 Initialization complete! Please refresh the page.');

// Auto-refresh after 2 seconds
setTimeout(() => {
  console.log('🔄 Auto-refreshing page...');
  window.location.reload();
}, 2000);
