import requests
import json

def test_streamlined_assignment():
    """Test the streamlined professional assignment interface"""
    
    print("🎯 Testing Streamlined Professional Assignment Interface")
    print("=" * 70)
    
    # Login as admin first
    login_url = "http://127.0.0.1:8000/api/auth/login/"
    login_data = {
        "user_name": "admin_user_3602",
        "password": "admin123"
    }
    
    print("1. Logging in as admin...")
    login_response = requests.post(login_url, json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Admin login failed: {login_response.status_code}")
        return
    
    login_result = login_response.json()
    if not login_result.get('success'):
        print(f"❌ Admin login failed: {login_result}")
        return
    
    token = login_result['data']['access_token']
    print("✅ Admin login successful")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test the APIs
    print("\n2. Testing assignment APIs...")
    
    orders_url = "http://127.0.0.1:8000/api/order/orders/?status=ready"
    agents_url = "http://127.0.0.1:8000/api/delivery-agent/admin/employees/"
    deliveries_url = "http://127.0.0.1:8000/api/delivery-agent/admin/active-deliveries/"
    
    orders_response = requests.get(orders_url, headers=headers)
    agents_response = requests.get(agents_url, headers=headers)
    deliveries_response = requests.get(deliveries_url, headers=headers)
    
    print(f"   📦 Orders API: {orders_response.status_code} ✅" if orders_response.status_code == 200 else f"   📦 Orders API: {orders_response.status_code} ❌")
    print(f"   👥 Agents API: {agents_response.status_code} ✅" if agents_response.status_code == 200 else f"   👥 Agents API: {agents_response.status_code} ❌")
    print(f"   🚚 Deliveries API: {deliveries_response.status_code} ✅" if deliveries_response.status_code == 200 else f"   🚚 Deliveries API: {deliveries_response.status_code} ❌")
    
    if orders_response.status_code == 200 and agents_response.status_code == 200:
        orders_data = orders_response.json()
        agents_data = agents_response.json()
        
        ready_orders = orders_data
        available_agents = agents_data.get('data', {}).get('employees', [])
        
        # Analyze agent availability
        online_agents = [agent for agent in available_agents if agent.get('availability') == 'online']
        offline_agents = [agent for agent in available_agents if agent.get('availability') == 'offline']
        
        print(f"\n3. Data Analysis:")
        print(f"   📦 Ready Orders: {len(ready_orders)}")
        print(f"   👥 Total Agents: {len(available_agents)}")
        print(f"   🟢 Online Agents: {len(online_agents)}")
        print(f"   🔴 Offline Agents: {len(offline_agents)}")
        
        # Check for Order #65 specifically
        order_65 = next((order for order in ready_orders if order.get('id') == 65), None)
        if order_65:
            print(f"\n🎯 Order #65 Status:")
            print(f"   ✅ Found: {order_65.get('customer', {}).get('name')} - ${order_65.get('total_amount')}")
            print(f"   📍 Restaurant: {order_65.get('restaurant', {}).get('name')}")
            print(f"   📞 Customer Phone: {order_65.get('customer', {}).get('phone')}")
        else:
            print(f"\n❌ Order #65 not found in ready orders")
        
        print(f"\n🎨 Streamlined Interface Features:")
        print(f"   ✅ Single professional assignment table")
        print(f"   ✅ Beautiful agents panel with online/offline status")
        print(f"   ✅ Quick assignment dropdowns")
        print(f"   ✅ Bulk selection and assignment")
        print(f"   ✅ Real-time agent availability tracking")
        print(f"   ✅ Priority indicators for orders")
        print(f"   ✅ Agent performance metrics")
        print(f"   ✅ Professional 3-column layout")
        
        print(f"\n📋 Assignment Table Features:")
        print(f"   • Professional table with all order details")
        print(f"   • Checkbox selection for bulk operations")
        print(f"   • Quick assign dropdown per order")
        print(f"   • Priority badges (🔥 Urgent, ⚡ High, 💎 High Value)")
        print(f"   • Customer and restaurant information")
        print(f"   • Order amounts and timestamps")
        
        print(f"\n👥 Agents Panel Features:")
        print(f"   • Beautiful agent cards with status indicators")
        print(f"   • Toggle between Available/Offline/All views")
        print(f"   • Agent statistics (online vs offline count)")
        print(f"   • Performance metrics (ratings, delivery count)")
        print(f"   • Color-coded status (green=online, red=offline)")
        print(f"   • Agent contact information")
        
        print(f"\n🔄 Bulk Assignment Features:")
        print(f"   • Select multiple orders with checkboxes")
        print(f"   • View selected orders summary with total value")
        print(f"   • Choose agent for all selected orders")
        print(f"   • One-click bulk assignment")
        print(f"   • Clear selection functionality")
        
        print(f"\n🚀 How to Use the Streamlined Interface:")
        print(f"   1. Navigate to: http://localhost:5173/admin/order-assignments")
        print(f"   2. Login with: admin_user_3602 / admin123")
        print(f"   3. View the professional assignment table on the left")
        print(f"   4. Monitor agent availability on the right panel")
        print(f"   5. Assign orders using:")
        print(f"      • Quick dropdowns for individual orders")
        print(f"      • Bulk selection for multiple orders")
        print(f"   6. Track active deliveries at the bottom")
        
        print(f"\n💡 Professional Benefits:")
        print(f"   • Streamlined single-view interface")
        print(f"   • Efficient space utilization")
        print(f"   • Clear visual hierarchy")
        print(f"   • Real-time status updates")
        print(f"   • Professional enterprise design")
        print(f"   • Mobile-responsive layout")
        
        if online_agents:
            print(f"\n🟢 Available Agents for Assignment:")
            for agent in online_agents[:3]:  # Show first 3
                print(f"   • {agent.get('full_name')} ({agent.get('agent_id')}) - ⭐{agent.get('rating', 0)} - {agent.get('total_deliveries', 0)} deliveries")
        
        if offline_agents:
            print(f"\n🔴 Offline Agents:")
            print(f"   • {len(offline_agents)} agents currently offline")
            
    else:
        print(f"\n❌ Some APIs are not working properly")

if __name__ == "__main__":
    test_streamlined_assignment()
