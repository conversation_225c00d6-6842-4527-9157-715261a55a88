# orders/urls.py
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import CartViewSet, DeliveryAssignmentViewSet, OrderStatusHistoryViewSet, OrderViewSet, OrderItemViewSet, RatingViewSet

router = DefaultRouter()
router.register(r'orders', OrderViewSet, basename='order')
router.register(r'orders/(?P<order_id>\d+)/items', OrderItemViewSet, basename='orderitem')
router.register(r'carts', CartViewSet, basename='cart')
router.register(r'orders/(?P<order_id>\d+)/status-history',OrderStatusHistoryViewSet,basename='order-status-history')
router.register(r'delivery-assignment', DeliveryAssignmentViewSet, basename='delivery-assignment')
router.register(r'ratings', RatingViewSet, basename='rating')

urlpatterns = [
    path('', include(router.urls)),
]