import React, { createContext, useContext, useState, useEffect } from "react";
import { menuCategoryApi, menuItemApi } from "../utils/menuApi";
import { useAuth } from "./AuthContext";

const MenuContext = createContext();

export const useMenu = () => {
  const context = useContext(MenuContext);
  if (!context) {
    throw new Error("useMenu must be used within a MenuProvider");
  }
  return context;
};

export const MenuProvider = ({ children, selectedRestaurantId = null }) => {
  const { user } = useAuth();
  const [categories, setCategories] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Use selected restaurant ID if provided, otherwise try to get from user data
  let restaurantId =
    selectedRestaurantId ||
    user?.restaurant_id ||
    user?.restaurant?.id ||
    user?.restaurantId;

  // For restaurant users without a selected restaurant, use fallback logic
  if (!restaurantId && user?.role === "restaurant") {
    // Use the first restaurant for this user (ID 14 for user 46)
    if (user.id === 46) {
      restaurantId = 14; // Kabul Palace Restaurant
    } else {
      restaurantId = 1; // Fallback for other users
    }
  }

  // Load categories and menu items when component mounts or restaurant changes
  useEffect(() => {
    if (restaurantId && user?.role === "restaurant") {
      loadCategories();
      loadMenuItems();
    }
  }, [restaurantId, user?.role]);

  /**
   * Load all categories for the restaurant
   */
  const loadCategories = async () => {
    if (!restaurantId) {
      console.warn("No restaurant ID available");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await menuCategoryApi.getCategories(restaurantId);

      if (result.success) {
        setCategories(result.data);
      } else {
        setError(result.error);
        console.error("Failed to load categories:", result.error);
      }
    } catch (err) {
      const errorMessage = "Failed to load menu categories";
      setError(errorMessage);
      console.error("Load categories error:", err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Create a new category
   */
  const createCategory = async (categoryData) => {
    console.log("🏪 MenuContext createCategory called with:", {
      categoryData,
      restaurantId,
      user: user?.id || user?.user_id,
      userRole: user?.role,
    });

    if (!restaurantId) {
      console.log("❌ No restaurant ID available");
      return {
        success: false,
        error: "No restaurant ID available",
      };
    }

    setLoading(true);
    setError(null);

    try {
      const requestData = {
        ...categoryData,
        restaurant: restaurantId,
      };
      console.log("📤 Sending category creation request:", requestData);

      const result = await menuCategoryApi.createCategory(requestData);
      console.log("📥 Category creation API result:", result);

      if (result.success) {
        // Add new category to the list
        setCategories((prev) => [...prev, result.data]);
        console.log("✅ Category added to state successfully");
        return {
          success: true,
          data: result.data,
          message: "Category created successfully",
        };
      } else {
        console.log("❌ Category creation failed:", result.error);
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to create category";
      setError(errorMessage);
      console.error("❌ Create category error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Update an existing category
   */
  const updateCategory = async (categoryId, updateData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await menuCategoryApi.updateCategory(
        categoryId,
        updateData
      );

      if (result.success) {
        // Update category in the list
        setCategories((prev) =>
          prev.map((cat) =>
            cat.id === categoryId ? { ...cat, ...result.data } : cat
          )
        );
        return {
          success: true,
          data: result.data,
          message: "Category updated successfully",
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to update category";
      setError(errorMessage);
      console.error("Update category error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Delete a category
   */
  const deleteCategory = async (categoryId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await menuCategoryApi.deleteCategory(categoryId);

      if (result.success) {
        // Remove category from the list
        setCategories((prev) => prev.filter((cat) => cat.id !== categoryId));
        return {
          success: true,
          message: "Category deleted successfully",
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to delete category";
      setError(errorMessage);
      console.error("Delete category error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get a single category by ID
   */
  const getCategory = async (categoryId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await menuCategoryApi.getCategory(categoryId);

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to get category";
      setError(errorMessage);
      console.error("Get category error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load all menu items for the restaurant
   */
  const loadMenuItems = async () => {
    if (!restaurantId) {
      console.warn("No restaurant ID available for menu items");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await menuItemApi.getItemsByRestaurant(restaurantId);

      if (result.success) {
        setMenuItems(result.data);
      } else {
        setError(result.error);
        console.error("Failed to load menu items:", result.error);
      }
    } catch (err) {
      const errorMessage = "Failed to load menu items";
      setError(errorMessage);
      console.error("Load menu items error:", err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Create a new menu item
   */
  const createMenuItem = async (itemData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await menuItemApi.createItem(itemData);

      if (result.success) {
        // Add new item to the list
        setMenuItems((prev) => [...prev, result.data]);
        return {
          success: true,
          data: result.data,
          message: "Menu item created successfully",
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to create menu item";
      setError(errorMessage);
      console.error("Create menu item error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Update an existing menu item
   */
  const updateMenuItem = async (itemId, updateData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await menuItemApi.updateItem(itemId, updateData);

      if (result.success) {
        // Update item in the list
        setMenuItems((prev) =>
          prev.map((item) =>
            item.id === itemId ? { ...item, ...result.data } : item
          )
        );
        return {
          success: true,
          data: result.data,
          message: "Menu item updated successfully",
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to update menu item";
      setError(errorMessage);
      console.error("Update menu item error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Delete a menu item
   */
  const deleteMenuItem = async (itemId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await menuItemApi.deleteItem(itemId);

      if (result.success) {
        // Remove item from the list
        setMenuItems((prev) => prev.filter((item) => item.id !== itemId));
        return {
          success: true,
          message: "Menu item deleted successfully",
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to delete menu item";
      setError(errorMessage);
      console.error("Delete menu item error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get a single menu item by ID
   */
  const getMenuItem = async (itemId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await menuItemApi.getItem(itemId);

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to get menu item";
      setError(errorMessage);
      console.error("Get menu item error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get menu items by category
   */
  const getMenuItemsByCategory = async (categoryId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await menuItemApi.getItemsByCategory(categoryId);

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to get menu items by category";
      setError(errorMessage);
      console.error("Get menu items by category error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Clear error state
   */
  const clearError = () => {
    setError(null);
  };

  /**
   * Refresh categories
   */
  const refreshCategories = () => {
    loadCategories();
  };

  /**
   * Refresh menu items
   */
  const refreshMenuItems = () => {
    loadMenuItems();
  };

  const value = {
    // State
    categories,
    menuItems,
    loading,
    error,
    restaurantId,

    // Category Actions
    createCategory,
    updateCategory,
    deleteCategory,
    getCategory,
    loadCategories,
    refreshCategories,

    // Menu Item Actions
    createMenuItem,
    updateMenuItem,
    deleteMenuItem,
    getMenuItem,
    getMenuItemsByCategory,
    loadMenuItems,
    refreshMenuItems,

    // Common Actions
    clearError,
  };

  return <MenuContext.Provider value={value}>{children}</MenuContext.Provider>;
};

export default MenuContext;
