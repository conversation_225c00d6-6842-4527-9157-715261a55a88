#!/usr/bin/env python3
"""
Simple test to verify restaurant creation API works
"""

import requests
import json

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"

def test_restaurant_creation_with_existing_user():
    """Test restaurant creation using existing verified user"""
    
    # Try to login with the existing verified user
    # From the logs, <NAME_EMAIL> was verified
    login_data = {
        "user_name": "bismullahwafadar5",  # This should be the username, not email
        "password": "password123"  # Common test password
    }
    
    print("🔐 Testing login with existing user...")
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        # Try different common passwords
        passwords = ["password123", "Password123", "12345678", "testpass", "admin123"]
        
        for password in passwords:
            login_data["password"] = password
            print(f"   Trying password: {password}")
            
            response = requests.post(
                f"{API_BASE_URL}/auth/login/",
                headers=headers,
                data=json.dumps(login_data)
            )
            
            if response.status_code == 200:
                print(f"✅ Login successful with password: {password}")
                result = response.json()
                token = result['data']['access_token']
                return test_restaurant_creation(token)
            else:
                print(f"   ❌ Failed: {response.status_code}")
        
        print("❌ Could not login with any common password")
        return False
        
    except Exception as e:
        print(f"❌ Exception during login: {e}")
        return False

def test_restaurant_creation(token):
    """Test restaurant creation with valid token"""
    
    auth_headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    # Simple restaurant data that matches our frontend form
    restaurant_data = {
        "name": "Test Restaurant API",
        "description": "Testing restaurant creation via API",
        "address": {
            "street": "Test Street 123",
            "city": "Kabul",
            "state": "Kabul",
            "postal_code": "1001",
            "country": "Afghanistan",
            "latitude": 34.5553,
            "longitude": 69.2075
        },
        "contact_number": "+93701234567",
        "opening_time": "09:00:00",
        "closing_time": "22:00:00",
        "delivery_fee": "50.00"
    }
    
    print(f"🏗️ Testing restaurant creation...")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            data=json.dumps(restaurant_data)
        )
        
        print(f"📡 Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 201:
            print("✅ Restaurant created successfully!")
            return True
        else:
            print("❌ Restaurant creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def test_without_auth():
    """Test what error we get without authentication"""
    print("🔍 Testing without authentication...")
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    restaurant_data = {
        "name": "Test Restaurant",
        "description": "Test",
        "contact_number": "+93701234567",
        "opening_time": "09:00:00",
        "closing_time": "22:00:00",
        "delivery_fee": "50.00"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=headers,
            data=json.dumps(restaurant_data)
        )
        
        print(f"📡 Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Exception: {e}")

def main():
    """Main test function"""
    print("🧪 Simple Restaurant Creation Test")
    print("=" * 50)
    
    # Test without auth first to see the error format
    test_without_auth()
    
    print("\n" + "=" * 50)
    
    # Test with existing user
    success = test_restaurant_creation_with_existing_user()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS: Restaurant creation works!")
    else:
        print("❌ FAILED: Still having issues")
    print("=" * 50)

if __name__ == "__main__":
    main()
