import React, { createContext, useContext, useState, useEffect } from "react";

// Create the context
const MapContext = createContext(null);

// Custom hook to use the map context
export const useMap = () => useContext(MapContext);

export const MapProvider = ({ children }) => {
  // Map configuration
  const [defaultCenter, setDefaultCenter] = useState([34.526, 69.1776]); // Kabul, Afghanistan (lat, lng)
  const [defaultZoom, setDefaultZoom] = useState(12);

  // Delivery agent location
  const [agentLocation, setAgentLocation] = useState(null);
  const [agentHeading, setAgentHeading] = useState(0);
  const [agentSpeed, setAgentSpeed] = useState(0);

  // Restaurant and customer locations
  const [restaurantLocation, setRestaurantLocation] = useState(null);
  const [customerLocation, setCustomerLocation] = useState(null);

  // Route information
  const [route, setRoute] = useState(null);
  const [routeDistance, setRouteDistance] = useState(null);
  const [routeDuration, setRouteDuration] = useState(null);
  const [estimatedArrival, setEstimatedArrival] = useState(null);

  // Simulation state for demo purposes
  const [isSimulating, setIsSimulating] = useState(false);
  const [simulationProgress, setSimulationProgress] = useState(0);

  // Calculate route between two points
  const calculateRoute = async (start, end) => {
    try {
      // For demo purposes, we'll create a simple straight line
      // In a real app, you would use a routing API
      setRoute([start, end]);

      // Calculate distance in kilometers using Haversine formula
      const distance = calculateDistance(start[0], start[1], end[0], end[1]);
      setRouteDistance(distance);

      // Estimate duration (assuming 30 km/h average speed)
      const durationMinutes = Math.round((distance / 30) * 60);
      setRouteDuration(durationMinutes);

      // Calculate estimated arrival time
      const arrival = new Date();
      arrival.setMinutes(arrival.getMinutes() + durationMinutes);
      setEstimatedArrival(arrival);

      return {
        route: [start, end],
        distance,
        duration: durationMinutes,
        arrival,
      };
    } catch (error) {
      console.error("Error calculating route:", error);
      return null;
    }
  };

  // Haversine formula to calculate distance between two points
  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371; // Radius of the earth in km
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(lat1)) *
        Math.cos(deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in km
    return distance;
  };

  const deg2rad = (deg) => {
    return deg * (Math.PI / 180);
  };

  // Start route simulation for demo purposes
  const startSimulation = (startPoint, endPoint, durationSeconds = 60) => {
    if (!startPoint || !endPoint) return;

    setAgentLocation(startPoint);
    setIsSimulating(true);
    setSimulationProgress(0);

    // Calculate the bearing (direction) from start to end
    const bearing = calculateBearing(
      startPoint[0],
      startPoint[1],
      endPoint[0],
      endPoint[1]
    );
    setAgentHeading(bearing);

    // Calculate the distance
    const distance = calculateDistance(
      startPoint[0],
      startPoint[1],
      endPoint[0],
      endPoint[1]
    );

    // Set speed (km/h)
    setAgentSpeed((distance / (durationSeconds / 3600)).toFixed(1));

    // Create simulation interval
    const interval = setInterval(() => {
      setSimulationProgress((prev) => {
        const newProgress = prev + 100 / durationSeconds;

        if (newProgress >= 100) {
          clearInterval(interval);
          setIsSimulating(false);
          setAgentLocation(endPoint);
          setSimulationProgress(100);
          return 100;
        }

        // Calculate new position by linear interpolation
        const ratio = newProgress / 100;
        const newLat = startPoint[0] + (endPoint[0] - startPoint[0]) * ratio;
        const newLng = startPoint[1] + (endPoint[1] - startPoint[1]) * ratio;

        setAgentLocation([newLat, newLng]);

        return newProgress;
      });
    }, 1000);

    return () => clearInterval(interval);
  };

  // Calculate bearing between two points
  const calculateBearing = (lat1, lon1, lat2, lon2) => {
    const y = Math.sin(deg2rad(lon2 - lon1)) * Math.cos(deg2rad(lat2));
    const x =
      Math.cos(deg2rad(lat1)) * Math.sin(deg2rad(lat2)) -
      Math.sin(deg2rad(lat1)) *
        Math.cos(deg2rad(lat2)) *
        Math.cos(deg2rad(lon2 - lon1));
    const brng = Math.atan2(y, x);
    return ((brng * 180) / Math.PI + 360) % 360;
  };

  // Stop simulation
  const stopSimulation = () => {
    setIsSimulating(false);
    setSimulationProgress(0);
  };

  // Initialize delivery tracking for an order
  const initializeTracking = async (order) => {
    if (!order) return null;

    // Extract locations from order
    const restaurant = order.restaurantLocation
      ? [order.restaurantLocation.lat, order.restaurantLocation.lng]
      : defaultCenter;

    const customer = order.customerLocation
      ? [order.customerLocation.lat, order.customerLocation.lng]
      : [defaultCenter[0] + 0.01, defaultCenter[1] + 0.02]; // Slightly offset for demo

    setRestaurantLocation(restaurant);
    setCustomerLocation(customer);

    // For demo purposes, set agent at restaurant location initially
    setAgentLocation(restaurant);

    // Calculate route
    const routeInfo = await calculateRoute(restaurant, customer);

    return {
      restaurant,
      customer,
      ...routeInfo,
    };
  };

  // Get map bounds that include all relevant points
  const getMapBounds = () => {
    const points = [agentLocation, restaurantLocation, customerLocation].filter(
      Boolean
    );

    if (points.length < 2) return null;

    // Find min and max lat/lng
    const lats = points.map((p) => p[0]);
    const lngs = points.map((p) => p[1]);

    const minLat = Math.min(...lats);
    const maxLat = Math.max(...lats);
    const minLng = Math.min(...lngs);
    const maxLng = Math.max(...lngs);

    // Add padding
    return [
      [minLat - 0.01, minLng - 0.01], // Southwest
      [maxLat + 0.01, maxLng + 0.01], // Northeast
    ];
  };

  // Value to be provided by the context
  const value = {
    // Map configuration
    defaultCenter,
    defaultZoom,

    // Locations
    agentLocation,
    agentHeading,
    agentSpeed,
    restaurantLocation,
    customerLocation,

    // Route information
    route,
    routeDistance,
    routeDuration,
    estimatedArrival,

    // Simulation
    isSimulating,
    simulationProgress,

    // Functions
    calculateRoute,
    calculateDistance,
    calculateBearing,
    startSimulation,
    stopSimulation,
    initializeTracking,
    getMapBounds,

    // Setters
    setAgentLocation,
    setRestaurantLocation,
    setCustomerLocation,
  };

  return <MapContext.Provider value={value}>{children}</MapContext.Provider>;
};
