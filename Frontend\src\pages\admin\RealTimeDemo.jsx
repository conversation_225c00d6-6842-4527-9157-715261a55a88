import React, { useState } from "react";
import {
  Zap,
  Bell,
  TruckIcon,
  Package,
  Play,
  Pause,
  RotateCcw,
  Settings,
  Monitor,
  Volume2,
} from "lucide-react";
import { useNotifications } from "../../context/NotificationContext";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import RealTimeOrderStatus from "../../components/common/RealTimeOrderStatus";
import LiveTrackingPanel from "../../components/delivery/LiveTrackingPanel";
import NotificationSettings from "../../components/common/NotificationSettings";

const RealTimeDemo = () => {
  const {
    isConnected,
    notifications,
    unreadCount,
    handleOrderStatusUpdate,
    handleNewOrder,
    handleAgentLocationUpdate,
    startOrderTracking,
    trackedOrders,
  } = useNotifications();

  const [demoOrderId] = useState("order-demo-12345");
  const [isSimulating, setIsSimulating] = useState(false);

  // Demo order data
  const demoOrder = {
    id: demoOrderId,
    restaurantName: "Kabul Kitchen",
    restaurantId: "restaurant-1",
    status: "pending",
    totalAmount: 24.99,
    deliveryFee: 2.99,
    orderItems: [
      { id: "1", name: "Kabuli Pulao", quantity: 1, price: 18.99 },
      { id: "2", name: "Mantu", quantity: 1, price: 6.00 },
    ],
    createdAt: new Date().toISOString(),
    estimatedDeliveryTime: new Date(Date.now() + 45 * 60 * 1000).toISOString(),
    deliveryAddress: "123 Main St, Kabul, Afghanistan",
  };

  const simulateOrderFlow = () => {
    if (isSimulating) return;

    setIsSimulating(true);
    
    // Start tracking the demo order
    startOrderTracking(demoOrderId);

    const statusFlow = [
      { status: "confirmed", delay: 2000 },
      { status: "preparing", delay: 5000 },
      { status: "readyForPickup", delay: 8000 },
      { status: "outForDelivery", delay: 10000 },
      { status: "delivered", delay: 15000 },
    ];

    statusFlow.forEach(({ status, delay }) => {
      setTimeout(() => {
        handleOrderStatusUpdate({
          orderId: demoOrderId,
          status,
          timestamp: new Date().toISOString(),
          agentLocation: status === "outForDelivery" ? {
            lat: 34.5553 + Math.random() * 0.01,
            lng: 69.2075 + Math.random() * 0.01,
          } : null,
        });

        if (status === "delivered") {
          setIsSimulating(false);
        }
      }, delay);
    });
  };

  const simulateNewOrder = () => {
    const newOrderData = {
      id: `order-${Date.now()}`,
      restaurantName: "Herat Delights",
      totalAmount: 32.50,
    };

    handleNewOrder(newOrderData);
  };

  const simulateAgentLocation = () => {
    if (trackedOrders[demoOrderId]) {
      handleAgentLocationUpdate({
        orderId: demoOrderId,
        location: {
          lat: 34.5553 + Math.random() * 0.01,
          lng: 69.2075 + Math.random() * 0.01,
        },
      });
    }
  };

  const resetDemo = () => {
    setIsSimulating(false);
    // Reset would require additional context methods
    window.location.reload();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Real-time Features Demo
          </h1>
          <p className="mt-2 text-gray-600">
            Test and showcase the real-time order tracking and notification system
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <div
            className={`w-3 h-3 rounded-full ${
              isConnected ? "bg-green-500" : "bg-red-500"
            }`}
          />
          <span className="text-sm text-gray-600">
            {isConnected ? "Connected" : "Disconnected"}
          </span>
        </div>
      </div>

      {/* Demo Controls */}
      <Card>
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4 flex items-center">
            <Zap size={20} className="text-orange-500 mr-2" />
            Demo Controls
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              variant="primary"
              icon={<Play size={16} />}
              onClick={simulateOrderFlow}
              disabled={isSimulating}
              className="w-full"
            >
              {isSimulating ? "Simulating..." : "Start Order Flow"}
            </Button>

            <Button
              variant="outline"
              icon={<Bell size={16} />}
              onClick={simulateNewOrder}
              className="w-full"
            >
              New Order Alert
            </Button>

            <Button
              variant="outline"
              icon={<TruckIcon size={16} />}
              onClick={simulateAgentLocation}
              className="w-full"
            >
              Update Location
            </Button>

            <Button
              variant="outline"
              icon={<RotateCcw size={16} />}
              onClick={resetDemo}
              className="w-full"
            >
              Reset Demo
            </Button>
          </div>

          {isSimulating && (
            <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-orange-500 rounded-full mr-2 animate-pulse" />
                <p className="text-sm text-orange-800">
                  Order simulation in progress. Watch for real-time updates!
                </p>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Notification Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <Bell size={24} className="text-blue-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Total Notifications
                </h3>
                <p className="text-2xl font-bold">{notifications.length}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                <Bell size={24} className="text-red-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Unread Notifications
                </h3>
                <p className="text-2xl font-bold">{unreadCount}</p>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className="p-6">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                <Package size={24} className="text-green-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Tracked Orders
                </h3>
                <p className="text-2xl font-bold">
                  {Object.keys(trackedOrders).length}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Demo Components */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Real-time Order Status */}
        <div>
          <h2 className="text-lg font-semibold mb-4">Real-time Order Status</h2>
          <RealTimeOrderStatus orderId={demoOrderId} />
        </div>

        {/* Live Tracking Panel */}
        <div>
          <h2 className="text-lg font-semibold mb-4">Live Tracking Panel</h2>
          <LiveTrackingPanel orderId={demoOrderId} />
        </div>
      </div>

      {/* Notification Settings */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Notification Settings</h2>
        <NotificationSettings />
      </div>

      {/* Recent Notifications */}
      <Card>
        <div className="p-6">
          <h2 className="text-lg font-semibold mb-4">Recent Notifications</h2>
          
          {notifications.length === 0 ? (
            <div className="text-center py-8">
              <Bell size={48} className="mx-auto text-gray-300 mb-4" />
              <p className="text-gray-500">No notifications yet</p>
              <p className="text-sm text-gray-400 mt-1">
                Use the demo controls above to generate notifications
              </p>
            </div>
          ) : (
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {notifications.slice(0, 10).map((notification) => (
                <div
                  key={notification.id}
                  className={`p-3 rounded-lg border ${
                    notification.read
                      ? "bg-gray-50 border-gray-200"
                      : "bg-blue-50 border-blue-200"
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-grow">
                      <h4 className="font-medium text-sm">
                        {notification.title}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        {new Date(notification.createdAt).toLocaleTimeString()}
                      </p>
                    </div>
                    {!notification.read && (
                      <Badge variant="primary" size="small">
                        New
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default RealTimeDemo;
