import React, { useState } from 'react';
import { <PERSON>, Eye, EyeOff, Co<PERSON>, LogIn, CheckCircle } from 'lucide-react';
import Button from '../../components/common/Button';
import Card from '../../components/common/Card';
import toast from '../../utils/toast';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';

const LoginTest = () => {
  const [showPasswords, setShowPasswords] = useState({});
  const [testingLogin, setTestingLogin] = useState(null);
  const { login } = useAuth();
  const navigate = useNavigate();

  // Test users with known credentials
  const testUsers = [
    {
      id: 'admin',
      username: 'admin',
      password: 'admin123',
      name: 'System Administrator',
      email: '<EMAIL>',
      role: 'admin',
      description: 'Full system access'
    },
    {
      id: 'restaurant1',
      username: 'restaurant_owner1',
      password: 'restaurant123',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'restaurant',
      description: 'Restaurant management'
    },
    {
      id: 'restaurant2',
      username: 'restaurant_owner2',
      password: 'restaurant123',
      name: 'Fatima Ahmadi',
      email: '<EMAIL>',
      role: 'restaurant',
      description: 'Restaurant management'
    },
    {
      id: 'customer1',
      username: 'customer1',
      password: 'customer123',
      name: 'Maryam Nazari',
      email: '<EMAIL>',
      role: 'customer',
      description: 'Customer ordering'
    },
    {
      id: 'customer2',
      username: 'customer2',
      password: 'customer123',
      name: 'Ali Rezaei',
      email: '<EMAIL>',
      role: 'customer',
      description: 'Customer ordering'
    },
    {
      id: 'delivery1',
      username: 'delivery_agent1',
      password: 'delivery123',
      name: 'Hassan Ali',
      email: '<EMAIL>',
      role: 'delivery_agent',
      description: 'Order delivery'
    },
    {
      id: 'delivery2',
      username: 'delivery_agent2',
      password: 'delivery123',
      name: 'Omar Karimi',
      email: '<EMAIL>',
      role: 'delivery_agent',
      description: 'Order delivery'
    },
    {
      id: 'recent1',
      username: 'khan',
      password: 'khan1234',
      name: 'jhan',
      email: '<EMAIL>',
      role: 'restaurant',
      description: 'Recently registered'
    }
  ];

  const togglePasswordVisibility = (userId) => {
    setShowPasswords(prev => ({
      ...prev,
      [userId]: !prev[userId]
    }));
  };

  const copyToClipboard = (text, type) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success(`${type} copied to clipboard!`);
    });
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'restaurant':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'customer':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'delivery_agent':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const testLogin = async (username, password, userRole) => {
    setTestingLogin(username);
    try {
      const result = await login({ user_name: username, password });
      if (result.success) {
        toast.success(`Successfully logged in as ${username}!`);
        // Navigate to appropriate dashboard based on role
        setTimeout(() => {
          navigate(`/dashboard/${userRole}`);
        }, 1000);
      } else {
        toast.error(`Login failed: ${result.error}`);
      }
    } catch (error) {
      toast.error(`Login error: ${error.message}`);
    } finally {
      setTestingLogin(null);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-6xl mx-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Users className="h-6 w-6 text-primary-500 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Login Test Page</h1>
              <p className="text-gray-600 mt-1">
                Test login functionality with pre-configured user accounts
              </p>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="grid gap-4">
            {testUsers.map((user) => (
              <div
                key={user.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {user.name}
                      </h3>
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium border ${getRoleColor(
                          user.role
                        )}`}
                      >
                        {user.role.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                    <p className="text-gray-600 text-sm">{user.email}</p>
                    <p className="text-gray-500 text-xs mt-1">{user.description}</p>
                  </div>
                  
                  <div className="flex items-center space-x-6">
                    <div className="text-right space-y-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-700 w-20">Username:</span>
                        <code className="bg-gray-100 px-3 py-1 rounded text-sm font-mono">
                          {user.username}
                        </code>
                        <button
                          onClick={() => copyToClipboard(user.username, 'Username')}
                          className="text-gray-400 hover:text-gray-600 p-1"
                          title="Copy username"
                        >
                          <Copy size={14} />
                        </button>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-gray-700 w-20">Password:</span>
                        <code className="bg-gray-100 px-3 py-1 rounded text-sm font-mono">
                          {showPasswords[user.id] ? user.password : '••••••••'}
                        </code>
                        <button
                          onClick={() => togglePasswordVisibility(user.id)}
                          className="text-gray-400 hover:text-gray-600 p-1"
                          title={showPasswords[user.id] ? 'Hide password' : 'Show password'}
                        >
                          {showPasswords[user.id] ? <EyeOff size={14} /> : <Eye size={14} />}
                        </button>
                        <button
                          onClick={() => copyToClipboard(user.password, 'Password')}
                          className="text-gray-400 hover:text-gray-600 p-1"
                          title="Copy password"
                        >
                          <Copy size={14} />
                        </button>
                      </div>
                    </div>
                    
                    <Button
                      onClick={() => testLogin(user.username, user.password, user.role)}
                      variant="primary"
                      size="sm"
                      disabled={testingLogin === user.username}
                      className="min-w-[100px]"
                    >
                      {testingLogin === user.username ? (
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                          <span>Testing...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <LogIn size={14} />
                          <span>Test Login</span>
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">How to use:</h4>
                <ul className="text-blue-800 text-sm mt-2 space-y-1">
                  <li>• Click "Test Login" to automatically log in with the selected user</li>
                  <li>• Use the eye icon to show/hide passwords</li>
                  <li>• Use the copy icon to copy credentials to clipboard</li>
                  <li>• After successful login, you'll be redirected to the appropriate dashboard</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default LoginTest;
