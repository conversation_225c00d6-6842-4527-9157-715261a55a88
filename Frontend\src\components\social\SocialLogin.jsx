import React, { useState } from 'react';
import { Chrome, Facebook, Apple } from 'lucide-react';
import { useSocial } from '../../context/SocialContext';
import { useAuth } from '../../context/AuthContext';
import Button from '../common/Button';

const SocialLogin = ({ onSuccess, onError }) => {
  const { connectSocialAccount } = useSocial();
  const { login } = useAuth();
  const [loading, setLoading] = useState({});

  const handleSocialLogin = async (provider) => {
    setLoading(prev => ({ ...prev, [provider]: true }));

    try {
      // Simulate social login process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock social login data
      const mockSocialData = {
        google: {
          id: 'google-user-123',
          name: '<PERSON>',
          email: '<EMAIL>',
          avatar: 'https://images.unsplash.com/photo-*************-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
          provider: 'google'
        },
        facebook: {
          id: 'facebook-user-456',
          name: '<PERSON>',
          email: '<EMAIL>',
          avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150&h=150&fit=crop&crop=face',
          provider: 'facebook'
        },
        apple: {
          id: 'apple-user-789',
          name: 'Mike Johnson',
          email: '<EMAIL>',
          avatar: 'https://images.unsplash.com/photo-*************-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
          provider: 'apple'
        }
      };

      const userData = mockSocialData[provider];
      
      // Create user account
      const user = {
        id: userData.id,
        name: userData.name,
        email: userData.email,
        avatar: userData.avatar,
        role: 'customer',
        socialProvider: userData.provider,
        createdAt: new Date().toISOString()
      };

      // Login user
      login(user);
      
      // Connect social account
      connectSocialAccount(provider, userData);
      
      onSuccess?.(user);
    } catch (error) {
      console.error(`${provider} login error:`, error);
      onError?.(error);
    } finally {
      setLoading(prev => ({ ...prev, [provider]: false }));
    }
  };

  const socialProviders = [
    {
      id: 'google',
      name: 'Google',
      icon: Chrome,
      color: 'border-red-500 text-red-600 hover:bg-red-50',
      bgColor: 'bg-white'
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: Facebook,
      color: 'border-blue-600 text-blue-600 hover:bg-blue-50',
      bgColor: 'bg-white'
    },
    {
      id: 'apple',
      name: 'Apple',
      icon: Apple,
      color: 'border-gray-900 text-gray-900 hover:bg-gray-50',
      bgColor: 'bg-white'
    }
  ];

  return (
    <div className="space-y-3">
      {socialProviders.map((provider) => {
        const IconComponent = provider.icon;
        return (
          <button
            key={provider.id}
            onClick={() => handleSocialLogin(provider.id)}
            disabled={loading[provider.id]}
            className={`w-full flex items-center justify-center px-4 py-3 border-2 rounded-lg font-medium transition-all duration-200 ${provider.color} ${provider.bgColor} ${
              loading[provider.id] ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md'
            }`}
          >
            {loading[provider.id] ? (
              <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-3" />
            ) : (
              <IconComponent size={20} className="mr-3" />
            )}
            <span>
              {loading[provider.id] 
                ? `Connecting to ${provider.name}...` 
                : `Continue with ${provider.name}`
              }
            </span>
          </button>
        );
      })}
    </div>
  );
};

// Social Account Connection Component (for settings)
export const SocialAccountSettings = () => {
  const { connectSocialAccount } = useSocial();
  const [connectedAccounts, setConnectedAccounts] = useState({
    google: false,
    facebook: false,
    apple: false
  });

  const handleConnect = async (provider) => {
    try {
      // Simulate connection process
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setConnectedAccounts(prev => ({ ...prev, [provider]: true }));
      connectSocialAccount(provider, { name: `${provider} Account` });
    } catch (error) {
      console.error(`Error connecting ${provider}:`, error);
    }
  };

  const handleDisconnect = (provider) => {
    setConnectedAccounts(prev => ({ ...prev, [provider]: false }));
  };

  const socialProviders = [
    {
      id: 'google',
      name: 'Google',
      icon: Chrome,
      description: 'Connect your Google account for easy sign-in'
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: Facebook,
      description: 'Share your favorite restaurants with friends'
    },
    {
      id: 'apple',
      name: 'Apple',
      icon: Apple,
      description: 'Use Apple ID for secure authentication'
    }
  ];

  return (
    <div className="space-y-4">
      <h3 className="font-semibold text-lg">Connected Accounts</h3>
      <p className="text-gray-600 text-sm">
        Connect your social accounts for easier sign-in and sharing
      </p>
      
      {socialProviders.map((provider) => {
        const IconComponent = provider.icon;
        const isConnected = connectedAccounts[provider.id];
        
        return (
          <div key={provider.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mr-3">
                <IconComponent size={20} className="text-gray-600" />
              </div>
              <div>
                <h4 className="font-medium">{provider.name}</h4>
                <p className="text-gray-600 text-sm">{provider.description}</p>
              </div>
            </div>
            
            <div className="flex items-center">
              {isConnected ? (
                <>
                  <span className="text-green-600 text-sm font-medium mr-3">Connected</span>
                  <Button
                    variant="outline"
                    size="small"
                    onClick={() => handleDisconnect(provider.id)}
                  >
                    Disconnect
                  </Button>
                </>
              ) : (
                <Button
                  variant="primary"
                  size="small"
                  onClick={() => handleConnect(provider.id)}
                >
                  Connect
                </Button>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default SocialLogin;
