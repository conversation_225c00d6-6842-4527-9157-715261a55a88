import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Clock, Phone, Star, Navigation, RefreshCw } from 'lucide-react';
import deliveryAgentApi from '../../services/deliveryAgentApi';
import websocketService from '../../services/websocketService';

const RealTimeOrderTracking = ({ orderId, onClose }) => {
  const [trackingData, setTrackingData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);
  const intervalRef = useRef(null);
  const wsConnected = useRef(false);

  useEffect(() => {
    if (orderId) {
      initializeTracking();
      setupWebSocketConnection();
    }

    return () => {
      cleanup();
    };
  }, [orderId]);

  const initializeTracking = async () => {
    try {
      setLoading(true);
      const result = await deliveryAgentApi.trackOrder(orderId);
      
      if (result.success) {
        setTrackingData(result.data);
        setLastUpdate(new Date());
        setError(null);
      } else {
        setError(result.error?.message || 'Failed to load tracking data');
      }
    } catch (err) {
      setError('Failed to load tracking data');
      console.error('Tracking initialization error:', err);
    } finally {
      setLoading(false);
    }
  };

  const setupWebSocketConnection = () => {
    try {
      // Connect to order tracking WebSocket
      const wsUrl = `ws://localhost:8000/ws/order-tracking/${orderId}/`;
      
      // For now, use polling as fallback since WebSocket might not be fully configured
      startPolling();
      
      // TODO: Implement WebSocket connection when backend is fully configured
      // websocketService.connectToOrderTracking(orderId, handleWebSocketMessage);
    } catch (err) {
      console.error('WebSocket connection error:', err);
      startPolling();
    }
  };

  const startPolling = () => {
    // Poll for updates every 30 seconds
    intervalRef.current = setInterval(async () => {
      try {
        const result = await deliveryAgentApi.trackOrder(orderId);
        if (result.success) {
          setTrackingData(result.data);
          setLastUpdate(new Date());
        }
      } catch (err) {
        console.error('Polling error:', err);
      }
    }, 30000);
  };

  const handleWebSocketMessage = (message) => {
    try {
      const data = JSON.parse(message);
      
      if (data.type === 'location_update') {
        setTrackingData(prev => ({
          ...prev,
          current_location: {
            ...prev.current_location,
            latitude: data.latitude,
            longitude: data.longitude,
            timestamp: data.timestamp
          }
        }));
        setLastUpdate(new Date());
      } else if (data.type === 'status_update') {
        setTrackingData(prev => ({
          ...prev,
          order_status: data.status
        }));
        setLastUpdate(new Date());
      }
    } catch (err) {
      console.error('WebSocket message parsing error:', err);
    }
  };

  const cleanup = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    // websocketService.disconnect();
  };

  const refreshTracking = () => {
    initializeTracking();
  };

  const formatTime = (minutes) => {
    if (minutes < 60) {
      return `${Math.round(minutes)} mins`;
    } else {
      const hours = Math.floor(minutes / 60);
      const mins = Math.round(minutes % 60);
      return `${hours}h ${mins}m`;
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'text-yellow-600 bg-yellow-100',
      'confirmed': 'text-blue-600 bg-blue-100',
      'preparing': 'text-orange-600 bg-orange-100',
      'ready': 'text-purple-600 bg-purple-100',
      'assigned': 'text-indigo-600 bg-indigo-100',
      'picked_up': 'text-green-600 bg-green-100',
      'on_the_way': 'text-green-700 bg-green-200',
      'delivered': 'text-green-800 bg-green-300',
      'cancelled': 'text-red-600 bg-red-100'
    };
    return colors[status] || 'text-gray-600 bg-gray-100';
  };

  const getStatusText = (status) => {
    const texts = {
      'pending': 'Order Pending',
      'confirmed': 'Order Confirmed',
      'preparing': 'Being Prepared',
      'ready': 'Ready for Pickup',
      'assigned': 'Driver Assigned',
      'picked_up': 'Picked Up',
      'on_the_way': 'On the Way',
      'delivered': 'Delivered',
      'cancelled': 'Cancelled'
    };
    return texts[status] || status;
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <div className="flex items-center justify-center">
            <RefreshCw className="animate-spin h-8 w-8 text-blue-600" />
            <span className="ml-2 text-lg">Loading tracking data...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="text-red-600 text-lg font-semibold mb-2">Error</div>
            <div className="text-gray-600 mb-4">{error}</div>
            <div className="flex space-x-3">
              <button
                onClick={refreshTracking}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                Retry
              </button>
              <button
                onClick={onClose}
                className="flex-1 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!trackingData) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">Order Tracking</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            Order #{trackingData.order_id}
          </div>
        </div>

        {/* Status */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(trackingData.order_status)}`}>
              {getStatusText(trackingData.order_status)}
            </div>
            <button
              onClick={refreshTracking}
              className="text-blue-600 hover:text-blue-700"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
          
          {lastUpdate && (
            <div className="text-xs text-gray-500">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </div>
          )}
        </div>

        {/* Delivery Agent Info */}
        {trackingData.agent && (
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Your Delivery Agent</h3>
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold">
                  {trackingData.agent.name.charAt(0)}
                </span>
              </div>
              <div className="flex-1">
                <div className="font-medium text-gray-900">{trackingData.agent.name}</div>
                <div className="text-sm text-gray-600 capitalize">
                  {trackingData.agent.vehicle_type}
                </div>
                <div className="flex items-center mt-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600 ml-1">
                    {trackingData.agent.rating.toFixed(1)}
                  </span>
                </div>
              </div>
              <a
                href={`tel:${trackingData.agent.phone}`}
                className="bg-green-600 text-white p-2 rounded-full hover:bg-green-700"
              >
                <Phone className="h-4 w-4" />
              </a>
            </div>
          </div>
        )}

        {/* Location & Time Info */}
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Delivery Progress</h3>
          
          {/* Current Location */}
          {trackingData.current_location && (
            <div className="flex items-start space-x-3 mb-4">
              <MapPin className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <div className="font-medium text-gray-900">Current Location</div>
                <div className="text-sm text-gray-600">
                  {trackingData.current_location.address || 
                   `${trackingData.current_location.latitude.toFixed(4)}, ${trackingData.current_location.longitude.toFixed(4)}`}
                </div>
              </div>
            </div>
          )}

          {/* Distance & Time */}
          <div className="grid grid-cols-2 gap-4">
            {trackingData.distances && (
              <div className="flex items-start space-x-3">
                <Navigation className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <div className="font-medium text-gray-900">Distance</div>
                  <div className="text-sm text-gray-600">
                    {trackingData.order_status === 'picked_up' || trackingData.order_status === 'on_the_way'
                      ? `${trackingData.distances.to_customer_km} km to you`
                      : `${trackingData.distances.to_restaurant_km} km to restaurant`}
                  </div>
                </div>
              </div>
            )}

            {trackingData.estimated_times && (
              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-orange-600 mt-0.5" />
                <div>
                  <div className="font-medium text-gray-900">ETA</div>
                  <div className="text-sm text-gray-600">
                    {trackingData.order_status === 'picked_up' || trackingData.order_status === 'on_the_way'
                      ? formatTime(trackingData.estimated_times.to_customer_minutes)
                      : formatTime(trackingData.estimated_times.to_restaurant_minutes)}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Map placeholder */}
        <div className="p-6">
          <div className="bg-gray-100 rounded-lg h-48 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <MapPin className="h-8 w-8 mx-auto mb-2" />
              <div>Live map coming soon</div>
              <div className="text-sm">Track your delivery in real-time</div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 bg-gray-50 rounded-b-lg">
          <div className="text-center">
            <button
              onClick={onClose}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              Close Tracking
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealTimeOrderTracking;
