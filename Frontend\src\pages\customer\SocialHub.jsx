import React, { useState } from 'react';
import { Users, Gift, Camera, Share2, Heart, TrendingUp } from 'lucide-react';
import { useSocial } from '../../context/SocialContext';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Badge from '../../components/common/Badge';
import ReferralProgram from '../../components/social/ReferralProgram';
import SocialFeed from '../../components/social/SocialFeed';
import { SocialAccountSettings } from '../../components/social/SocialLogin';

const SocialHub = () => {
  const { getSocialStats, userReviews } = useSocial();
  const [activeTab, setActiveTab] = useState('overview');
  
  const socialStats = getSocialStats();

  const tabs = [
    { id: 'overview', label: 'Overview', icon: TrendingUp },
    { id: 'referrals', label: 'Refer Friends', icon: Gift },
    { id: 'activity', label: 'Activity', icon: Share2 },
    { id: 'reviews', label: 'My Reviews', icon: Camera },
    { id: 'settings', label: 'Social Settings', icon: Users }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewTab socialStats={socialStats} />;
      case 'referrals':
        return <ReferralProgram />;
      case 'activity':
        return <SocialFeed limit={20} />;
      case 'reviews':
        return <ReviewsTab reviews={userReviews} />;
      case 'settings':
        return <SocialAccountSettings />;
      default:
        return <OverviewTab socialStats={socialStats} />;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 animate-fade-in">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Users size={32} className="text-white" />
        </div>
        <h1 className="text-3xl font-bold mb-2">Social Hub</h1>
        <p className="text-gray-600">
          Connect, share, and earn rewards with the Afghan Sofra community
        </p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-8 overflow-x-auto">
        {tabs.map((tab) => {
          const IconComponent = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-3 rounded-md text-sm font-medium transition-colors whitespace-nowrap ${
                activeTab === tab.id
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <IconComponent size={16} />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      {renderTabContent()}
    </div>
  );
};

// Overview Tab Component
const OverviewTab = ({ socialStats }) => {
  return (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="text-center p-6">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <Users size={24} className="text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-blue-600">{socialStats.referrals.completed}</div>
          <div className="text-sm text-gray-600">Friends Referred</div>
        </Card>

        <Card className="text-center p-6">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <Gift size={24} className="text-green-600" />
          </div>
          <div className="text-2xl font-bold text-green-600">${socialStats.referrals.totalRewards}</div>
          <div className="text-sm text-gray-600">Rewards Earned</div>
        </Card>

        <Card className="text-center p-6">
          <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <Camera size={24} className="text-purple-600" />
          </div>
          <div className="text-2xl font-bold text-purple-600">{socialStats.reviews.total}</div>
          <div className="text-sm text-gray-600">Reviews Written</div>
        </Card>

        <Card className="text-center p-6">
          <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <Share2 size={24} className="text-orange-600" />
          </div>
          <div className="text-2xl font-bold text-orange-600">{socialStats.social.shares}</div>
          <div className="text-sm text-gray-600">Items Shared</div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="font-semibold text-lg mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            variant="primary"
            icon={<Gift size={16} />}
            to="/social?tab=referrals"
            className="justify-center"
          >
            Refer Friends
          </Button>
          <Button
            variant="outline"
            icon={<Camera size={16} />}
            to="/restaurants"
            className="justify-center"
          >
            Write Review
          </Button>
          <Button
            variant="outline"
            icon={<Share2 size={16} />}
            to="/restaurants"
            className="justify-center"
          >
            Share Restaurant
          </Button>
        </div>
      </Card>

      {/* Recent Activity Preview */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold text-lg">Recent Activity</h3>
          <Button variant="outline" size="small" to="/social?tab=activity">
            View All
          </Button>
        </div>
        <SocialFeed limit={5} />
      </div>
    </div>
  );
};

// Reviews Tab Component
const ReviewsTab = ({ reviews }) => {
  if (reviews.length === 0) {
    return (
      <Card className="text-center py-12">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Camera size={24} className="text-gray-400" />
        </div>
        <h3 className="font-semibold mb-2">No Reviews Yet</h3>
        <p className="text-gray-600 mb-6">
          Start writing reviews to share your experiences with the community
        </p>
        <Button variant="primary" to="/restaurants">
          Browse Restaurants
        </Button>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-lg">My Reviews ({reviews.length})</h3>
        <Badge variant="secondary" size="small">
          Avg Rating: {(reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1)}⭐
        </Badge>
      </div>

      {reviews.map((review) => (
        <Card key={review.id} className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div>
              <h4 className="font-medium">{review.restaurantName}</h4>
              <div className="flex items-center mt-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <span
                    key={star}
                    className={`text-lg ${
                      star <= review.rating ? 'text-yellow-400' : 'text-gray-300'
                    }`}
                  >
                    ⭐
                  </span>
                ))}
                <span className="ml-2 text-sm text-gray-600">
                  {new Date(review.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
            <Badge variant="info" size="small">
              {review.photos?.length || 0} photos
            </Badge>
          </div>

          <p className="text-gray-700 mb-3">{review.comment}</p>

          {review.photos && review.photos.length > 0 && (
            <div className="grid grid-cols-3 gap-2 mb-3">
              {review.photos.slice(0, 3).map((photo, index) => (
                <img
                  key={index}
                  src={photo}
                  alt="Review photo"
                  className="w-full h-20 object-cover rounded-lg"
                />
              ))}
            </div>
          )}

          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <span className="flex items-center">
                <Heart size={14} className="mr-1" />
                {review.likes} likes
              </span>
              <span>{review.helpful} found helpful</span>
            </div>
            <Button variant="outline" size="small" to={`/restaurants/${review.restaurantId}`}>
              View Restaurant
            </Button>
          </div>
        </Card>
      ))}
    </div>
  );
};

export default SocialHub;
