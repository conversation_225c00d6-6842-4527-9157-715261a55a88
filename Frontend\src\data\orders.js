export const mockOrders = [
  {
    id: 1,
    customer: "<PERSON>",
    restaurant: "Afghan Delights",
    total: 45.99,
    status: "delivered",
    date: "2024-01-15",
    items: ["Kabuli Pulao", "Mantu"],
  },
  {
    id: 2,
    customer: "<PERSON>",
    restaurant: "Kabul Kitchen",
    total: 32.5,
    status: "in_progress",
    date: "2024-01-15",
    items: ["Qorma", "Naan"],
  },
  {
    id: 3,
    customer: "<PERSON>",
    restaurant: "Pamir Restaurant",
    total: 28.75,
    status: "pending",
    date: "2024-01-14",
    items: ["Ashak", "Green Tea"],
  },
  {
    id: 4,
    customer: "<PERSON>",
    restaurant: "Afghan Delights",
    total: 55.2,
    status: "delivered",
    date: "2024-01-14",
    items: ["Lamb Karahi", "Basmati Rice", "Salad"],
  },
  {
    id: 5,
    customer: "David Brown",
    restaurant: "Kabul Kitchen",
    total: 41.8,
    status: "cancelled",
    date: "2024-01-13",
    items: ["Chicken Tikka", "<PERSON>an", "Yogurt Drink"],
  },
  {
    id: 6,
    customer: "<PERSON>",
    restaurant: "Afghan Delights",
    total: 23.97,
    status: "preparing",
    date: "2024-01-15",
    items: ["Bolani", "Firni"],
    orderNumber: "ORD-006",
    customerPhone: "+93 70 123 1006",
    deliveryAddress: "987 Cedar Ln, Kabul",
    paymentMethod: "Credit Card",
    notes: "Vegetarian order",
  },
  {
    id: 7,
    customer: "Ahmed Hassan",
    restaurant: "Afghan Delights",
    total: 67.45,
    status: "confirmed",
    date: "2024-01-15",
    items: ["Lamb Kebab", "Kabuli Pulao", "Afghan Naan"],
    orderNumber: "ORD-007",
    customerPhone: "+93 70 123 1007",
    deliveryAddress: "123 Garden St, Kabul",
    paymentMethod: "Cash on Delivery",
    notes: "Extra rice please",
  },
];

export const orderStatuses = [
  {
    id: "pending",
    name: "Pending",
    color: "bg-yellow-100 text-yellow-800",
    description: "Order received, waiting for confirmation",
  },
  {
    id: "confirmed",
    name: "Confirmed",
    color: "bg-blue-100 text-blue-800",
    description: "Order confirmed by restaurant",
  },
  {
    id: "preparing",
    name: "Preparing",
    color: "bg-orange-100 text-orange-800",
    description: "Food is being prepared",
  },
  {
    id: "ready",
    name: "Ready",
    color: "bg-purple-100 text-purple-800",
    description: "Order ready for pickup/delivery",
  },
  {
    id: "out_for_delivery",
    name: "Out for Delivery",
    color: "bg-indigo-100 text-indigo-800",
    description: "Order is on the way",
  },
  {
    id: "delivered",
    name: "Delivered",
    color: "bg-green-100 text-green-800",
    description: "Order successfully delivered",
  },
  {
    id: "cancelled",
    name: "Cancelled",
    color: "bg-red-100 text-red-800",
    description: "Order was cancelled",
  },
];
