from rest_framework import generics, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from django.shortcuts import get_object_or_404
from django.db import transaction, models
from django.utils import timezone
from datetime import datetime, timedelta

from .models import DeliveryAgentProfile, DeliveryAgentDocument
from .serializers import (
    DeliveryAgentProfileSerializer, DeliveryAgentProfileUpdateSerializer,
    LocationUpdateSerializer, StatusUpdateSerializer, DeliveryAgentOrderSerializer,
    OrderActionSerializer, EarningsSerializer, DeliveryAgentStatsSerializer,
    DeliveryAgentDocumentSerializer
)
from orders.models import Order
from api.permissions import IsAssignedDeliveryAgent


class DeliveryAgentProfileViewSet(ModelViewSet):
    """
    ViewSet for delivery agent profile management
    """
    serializer_class = DeliveryAgentProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # Only allow agents to access their own profile
        if self.request.user.role == 'delivery_agent':
            return DeliveryAgentProfile.objects.filter(user=self.request.user)
        # <PERSON><PERSON> can access all profiles
        elif self.request.user.role == 'admin':
            return DeliveryAgentProfile.objects.all()
        return DeliveryAgentProfile.objects.none()

    def get_serializer_class(self):
        if self.action in ['update', 'partial_update']:
            return DeliveryAgentProfileUpdateSerializer
        return DeliveryAgentProfileSerializer

    @action(detail=False, methods=['get'])
    def me(self, request):
        """Get current agent's profile"""
        if request.user.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can access this endpoint'},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            profile = DeliveryAgentProfile.objects.get(user=request.user)
            serializer = self.get_serializer(profile)
            return Response(serializer.data)
        except DeliveryAgentProfile.DoesNotExist:
            return Response(
                {'error': 'Delivery agent profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['post'])
    def update_location(self, request):
        """Update agent's current location"""
        if request.user.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can update location'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = LocationUpdateSerializer(data=request.data)
        if serializer.is_valid():
            try:
                profile = DeliveryAgentProfile.objects.get(user=request.user)
                profile.update_location(
                    latitude=serializer.validated_data['latitude'],
                    longitude=serializer.validated_data['longitude'],
                    address=serializer.validated_data.get('address', '')
                )
                return Response({
                    'message': 'Location updated successfully',
                    'location': profile.current_location
                })
            except DeliveryAgentProfile.DoesNotExist:
                return Response(
                    {'error': 'Delivery agent profile not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def update_status(self, request):
        """Update agent's availability status"""
        if request.user.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can update status'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = StatusUpdateSerializer(data=request.data)
        if serializer.is_valid():
            try:
                profile = DeliveryAgentProfile.objects.get(user=request.user)
                profile.set_status(serializer.validated_data['status'])
                return Response({
                    'message': 'Status updated successfully',
                    'status': profile.status,
                    'is_online': profile.is_online
                })
            except DeliveryAgentProfile.DoesNotExist:
                return Response(
                    {'error': 'Delivery agent profile not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get agent's performance statistics"""
        if request.user.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can access stats'},
                status=status.HTTP_403_FORBIDDEN
            )

        try:
            profile = DeliveryAgentProfile.objects.get(user=request.user)
            stats_data = {
                'total_deliveries': profile.total_deliveries,
                'completed_orders': profile.completed_orders,
                'cancelled_orders': profile.cancelled_orders,
                'completion_rate': profile.completion_rate,
                'average_rating': profile.rating,
                'average_delivery_time': profile.average_delivery_time,
                'earnings': profile.earnings or {}
            }
            serializer = DeliveryAgentStatsSerializer(stats_data)
            return Response(serializer.data)
        except DeliveryAgentProfile.DoesNotExist:
            return Response(
                {'error': 'Delivery agent profile not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class DeliveryAgentOrderViewSet(ModelViewSet):
    """
    ViewSet for delivery agent order management
    """
    serializer_class = DeliveryAgentOrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.role != 'delivery_agent':
            return Order.objects.none()

        # Get orders assigned to this agent or available orders
        return Order.objects.filter(
            models.Q(delivery_agent=self.request.user) |
            models.Q(status='ready', assignment_attempts__lt=3)
        ).order_by('-created_at')

    @action(detail=False, methods=['get'])
    def available(self, request):
        """Get available orders for assignment"""
        if request.user.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can access this endpoint'},
                status=status.HTTP_403_FORBIDDEN
            )

        orders = Order.objects.filter(
            status='ready',
            assignment_attempts__lt=3
        ).exclude(
            delivery_agent=request.user
        ).order_by('created_at')

        serializer = self.get_serializer(orders, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def assigned(self, request):
        """Get orders assigned to current agent"""
        if request.user.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can access this endpoint'},
                status=status.HTTP_403_FORBIDDEN
            )

        orders = Order.objects.filter(
            delivery_agent=request.user,
            status__in=['assigned', 'picked_up', 'out_for_delivery']
        ).order_by('created_at')

        serializer = self.get_serializer(orders, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def accept_order(self, request):
        """Accept an available order"""
        if request.user.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can accept orders'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = OrderActionSerializer(data=request.data)
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    order = get_object_or_404(
                        Order.objects.select_for_update(),
                        id=serializer.validated_data['order_id'],
                        status='ready'
                    )

                    # Check if agent is available
                    try:
                        profile = DeliveryAgentProfile.objects.get(user=request.user)
                        if not profile.is_available_for_delivery:
                            return Response(
                                {'error': 'Agent is not available for delivery'},
                                status=status.HTTP_400_BAD_REQUEST
                            )
                    except DeliveryAgentProfile.DoesNotExist:
                        return Response(
                            {'error': 'Delivery agent profile not found'},
                            status=status.HTTP_404_NOT_FOUND
                        )

                    # Assign order to agent
                    order.assign_agent(request.user, request)

                    # Update agent status to busy
                    profile.set_status('busy')

                    return Response({
                        'message': 'Order accepted successfully',
                        'order_id': order.id,
                        'status': order.status
                    })

            except Order.DoesNotExist:
                return Response(
                    {'error': 'Order not found or not available'},
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def reject_order(self, request):
        """Reject an assigned order"""
        if request.user.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can reject orders'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = OrderActionSerializer(data=request.data)
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    order = get_object_or_404(
                        Order.objects.select_for_update(),
                        id=serializer.validated_data['order_id'],
                        delivery_agent=request.user,
                        status='assigned'
                    )

                    # Unassign the order
                    order.unassign_agent(request)

                    # Update agent status back to available
                    try:
                        profile = DeliveryAgentProfile.objects.get(user=request.user)
                        profile.set_status('available')
                    except DeliveryAgentProfile.DoesNotExist:
                        pass

                    return Response({
                        'message': 'Order rejected successfully',
                        'order_id': order.id,
                        'status': order.status
                    })

            except Order.DoesNotExist:
                return Response(
                    {'error': 'Order not found or not assigned to you'},
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def pickup_order(self, request):
        """Mark order as picked up"""
        if request.user.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can pickup orders'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = OrderActionSerializer(data=request.data)
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    order = get_object_or_404(
                        Order.objects.select_for_update(),
                        id=serializer.validated_data['order_id'],
                        delivery_agent=request.user,
                        status='assigned'
                    )

                    # Update order status
                    order.status = 'picked_up'
                    order.save()

                    return Response({
                        'message': 'Order picked up successfully',
                        'order_id': order.id,
                        'status': order.status
                    })

            except Order.DoesNotExist:
                return Response(
                    {'error': 'Order not found or not assigned to you'},
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def deliver_order(self, request):
        """Mark order as delivered"""
        if request.user.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can deliver orders'},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = OrderActionSerializer(data=request.data)
        if serializer.is_valid():
            try:
                with transaction.atomic():
                    order = get_object_or_404(
                        Order.objects.select_for_update(),
                        id=serializer.validated_data['order_id'],
                        delivery_agent=request.user,
                        status__in=['picked_up', 'out_for_delivery']
                    )

                    # Update order status
                    order.status = 'delivered'
                    order.actual_delivery_time = timezone.now()
                    order.save()

                    # Update agent statistics
                    try:
                        profile = DeliveryAgentProfile.objects.get(user=request.user)
                        profile.completed_orders += 1
                        profile.total_deliveries += 1

                        # Update earnings (assuming delivery fee goes to agent)
                        profile.update_earnings(float(order.delivery_fee), 'today')

                        # Set status back to available
                        profile.set_status('available')
                        profile.save()

                    except DeliveryAgentProfile.DoesNotExist:
                        pass

                    return Response({
                        'message': 'Order delivered successfully',
                        'order_id': order.id,
                        'status': order.status
                    })

            except Order.DoesNotExist:
                return Response(
                    {'error': 'Order not found or not assigned to you'},
                    status=status.HTTP_404_NOT_FOUND
                )

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DeliveryAgentDocumentViewSet(ModelViewSet):
    """
    ViewSet for delivery agent document management
    """
    serializer_class = DeliveryAgentDocumentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.role == 'delivery_agent':
            try:
                profile = DeliveryAgentProfile.objects.get(user=self.request.user)
                return DeliveryAgentDocument.objects.filter(agent=profile)
            except DeliveryAgentProfile.DoesNotExist:
                return DeliveryAgentDocument.objects.none()
        elif self.request.user.role == 'admin':
            return DeliveryAgentDocument.objects.all()
        return DeliveryAgentDocument.objects.none()

    def perform_create(self, serializer):
        if self.request.user.role == 'delivery_agent':
            try:
                profile = DeliveryAgentProfile.objects.get(user=self.request.user)
                serializer.save(agent=profile)
            except DeliveryAgentProfile.DoesNotExist:
                raise serializers.ValidationError("Delivery agent profile not found")
