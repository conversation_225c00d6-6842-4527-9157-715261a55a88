import React, { useState, useEffect, useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingBag,
  Users,
  Star,
  Clock,
  MapPin,
  Phone,
  Mail,
  Calendar,
  Package,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Award,
  Zap,
  Eye,
  Edit,
  Plus,
  Filter,
  Download,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  TrendingUp as TrendUp,
  Info,
  Shield,
  ShieldCheck,
  AlertCircle,
} from "lucide-react";
import { mockOrders } from "../../data/orders";
import { mockMenuItems } from "../../data/menuItems";
import { mockRestaurants } from "../../data/restaurants";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { DashboardSkeleton } from "../../components/skeleton/DashboardSkeleton";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";

function Dashboard() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { restaurantId } = useParams();
  const {
    currentRestaurant,
    getRestaurant,
    getUserRestaurants,
    loading: restaurantLoading,
    error: restaurantError,
  } = useRestaurant();

  const [restaurant, setRestaurant] = useState(null);
  const [orders, setOrders] = useState([]);
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("week");
  const [activeTab, setActiveTab] = useState("overview");
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState({
    totalOrders: 0,
    totalRevenue: 0,
    todayOrders: 0,
    todayRevenue: 0,
    pendingOrders: 0,
    completedOrders: 0,
    popularItems: [],
    revenueByDay: [],
    ordersByStatus: {},
    revenueByCategory: {},
    revenueGrowth: 0,
    avgOrderValue: 0,
    customerRetention: 0,
    peakHours: [],
    menuPerformance: [],
    profitMargins: {},
    forecastData: [],
    goals: {
      monthlyRevenue: 5000,
      dailyOrders: 20,
      avgOrderValue: 25,
    },
    achievements: [],
  });

  // Fetch restaurant data for current user
  useEffect(() => {
    const fetchRestaurantData = async () => {
      if (user && user.role === "restaurant") {
        try {
          setLoading(true);

          if (restaurantId) {
            // If restaurantId is provided in URL, fetch that specific restaurant
            const result = await getRestaurant(restaurantId);
            if (result.success) {
              setRestaurant(result.data);

              // Get orders for this restaurant (using mock data for now)
              const restaurantOrders = mockOrders.filter(
                (order) => order.restaurantId === parseInt(restaurantId)
              );
              setOrders(restaurantOrders);

              // Get menu items for this restaurant (using mock data for now)
              const restaurantMenuItems = mockMenuItems.filter(
                (item) => item.restaurantId === parseInt(restaurantId)
              );
              setMenuItems(restaurantMenuItems);

              // Calculate statistics
              calculateStats(restaurantOrders, restaurantMenuItems);
            } else {
              console.error("Failed to fetch restaurant:", result.error);
              navigate("/restaurant/my-restaurants");
              return;
            }
          } else {
            // Fetch all restaurants owned by the current user
            const result = await getUserRestaurants();

            if (result.success && result.data.length > 0) {
              // If user has multiple restaurants, redirect to My Restaurants page
              if (result.data.length > 1) {
                navigate("/restaurant/my-restaurants");
                return;
              }

              // If user has only one restaurant, show its dashboard
              const userRestaurant = result.data[0];
              setRestaurant(userRestaurant);

              // Get orders for this restaurant (using mock data for now)
              const restaurantOrders = mockOrders.filter(
                (order) => order.restaurantId === userRestaurant.id
              );
              setOrders(restaurantOrders);

              // Get menu items for this restaurant (using mock data for now)
              const restaurantMenuItems = mockMenuItems.filter(
                (item) => item.restaurantId === userRestaurant.id
              );
              setMenuItems(restaurantMenuItems);

              // Calculate statistics
              calculateStats(restaurantOrders, restaurantMenuItems);
            } else {
              // Fallback to mock data if no restaurants found
              let userRestaurant = mockRestaurants.find(
                (r) => r.ownerId === user.id
              );

              // If no restaurant found for current user, use first restaurant as demo
              if (!userRestaurant && mockRestaurants.length > 0) {
                userRestaurant = mockRestaurants[0];
                // Update the restaurant to show it belongs to current user
                userRestaurant = { ...userRestaurant, ownerId: user.id };
              }

              if (userRestaurant) {
                setRestaurant(userRestaurant);

                // Get orders for this restaurant
                const restaurantOrders = mockOrders.filter(
                  (order) => order.restaurantId === userRestaurant.id
                );
                setOrders(restaurantOrders);

                // Get menu items for this restaurant
                const restaurantMenuItems = mockMenuItems.filter(
                  (item) => item.restaurantId === userRestaurant.id
                );
                setMenuItems(restaurantMenuItems);

                // Calculate statistics
                calculateStats(restaurantOrders, restaurantMenuItems);
              }
            }
          }
        } catch (error) {
          console.error("Error fetching restaurant data:", error);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    fetchRestaurantData();
  }, [user, navigate, restaurantId, getRestaurant, getUserRestaurants]);

  // Recalculate stats when time range changes
  useEffect(() => {
    if (orders.length > 0 && menuItems.length > 0) {
      calculateStats(orders, menuItems);
    }
  }, [timeRange, orders, menuItems]);

  const calculateStats = (orders, menuItems) => {
    // Calculate basic statistics
    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);

    // Today's stats
    const today = new Date().toISOString().split("T")[0];
    const todayOrders = orders.filter((order) => order.date === today);
    const todayRevenue = todayOrders.reduce(
      (sum, order) => sum + order.total,
      0
    );

    // Order status counts
    const pendingOrders = orders.filter(
      (order) => order.status === "pending"
    ).length;
    const completedOrders = orders.filter(
      (order) => order.status === "delivered"
    ).length;

    // Calculate other metrics
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    const revenueGrowth = Math.random() * 20 - 10; // Mock growth percentage

    // Popular items
    const itemCounts = {};
    orders.forEach((order) => {
      if (order.items) {
        order.items.forEach((item) => {
          itemCounts[item] = (itemCounts[item] || 0) + 1;
        });
      }
    });

    const popularItems = Object.entries(itemCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([name, count]) => ({ name, count }));

    setStats({
      totalOrders,
      totalRevenue,
      todayOrders: todayOrders.length,
      todayRevenue,
      pendingOrders,
      completedOrders,
      popularItems,
      revenueByDay: [],
      ordersByStatus: {},
      revenueByCategory: {},
      revenueGrowth,
      avgOrderValue,
      customerRetention: 85,
      peakHours: [],
      menuPerformance: [],
      profitMargins: {},
      forecastData: [],
      goals: {
        monthlyRevenue: 5000,
        dailyOrders: 20,
        avgOrderValue: 25,
      },
      achievements: [],
    });
  };

  // Handle refresh functionality
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      // Reload data
      await loadData();
    } catch (error) {
      console.error("Error refreshing data:", error);
    } finally {
      // Stop refreshing animation after a short delay
      setTimeout(() => {
        setRefreshing(false);
      }, 1000);
    }
  };

  if (loading) {
    return <DashboardSkeleton />;
  }

  if (!restaurant) {
    return (
      <div className='p-6'>
        <Card className='p-8 text-center'>
          <AlertCircle className='mx-auto mb-4 text-yellow-500' size={48} />
          <h2 className='text-xl font-semibold mb-2'>No Restaurant Found</h2>
          <p className='text-gray-600 mb-4'>
            You don't have any restaurants yet. Create your first restaurant to
            get started.
          </p>
          <Button variant='primary' to='/add-restaurant'>
            Create Restaurant
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className='p-6 animate-fade-in'>
      {/* Header */}
      <div className='mb-8'>
        <div className='flex items-center justify-between mb-4'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>
              {restaurant.name} Dashboard
            </h1>
            <p className='text-gray-600'>
              Welcome back! Here's what's happening with your restaurant.
            </p>
          </div>
          <div className='flex items-center space-x-3'>
            <Button
              variant='outline'
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw
                size={18}
                className={refreshing ? "animate-spin" : ""}
              />
              Refresh
            </Button>
            <Button variant='primary' to='/restaurant/my-restaurants'>
              <Eye size={18} />
              All Restaurants
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8'>
        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Total Revenue</p>
              <p className='text-2xl font-bold text-gray-900'>
                ${stats.totalRevenue.toFixed(2)}
              </p>
            </div>
            <div className='p-3 bg-green-100 rounded-full'>
              <DollarSign className='text-green-600' size={24} />
            </div>
          </div>
          <div className='mt-4 flex items-center'>
            <TrendingUp className='text-green-500 mr-1' size={16} />
            <span className='text-sm text-green-600'>
              +{stats.revenueGrowth.toFixed(1)}% from last month
            </span>
          </div>
        </Card>

        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Total Orders</p>
              <p className='text-2xl font-bold text-gray-900'>
                {stats.totalOrders}
              </p>
            </div>
            <div className='p-3 bg-blue-100 rounded-full'>
              <ShoppingBag className='text-blue-600' size={24} />
            </div>
          </div>
          <div className='mt-4 flex items-center'>
            <span className='text-sm text-gray-600'>
              {stats.todayOrders} orders today
            </span>
          </div>
        </Card>

        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>
                Pending Orders
              </p>
              <p className='text-2xl font-bold text-gray-900'>
                {stats.pendingOrders}
              </p>
            </div>
            <div className='p-3 bg-yellow-100 rounded-full'>
              <Clock className='text-yellow-600' size={24} />
            </div>
          </div>
        </Card>

        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>
                Avg Order Value
              </p>
              <p className='text-2xl font-bold text-gray-900'>
                ${stats.avgOrderValue.toFixed(2)}
              </p>
            </div>
            <div className='p-3 bg-purple-100 rounded-full'>
              <Target className='text-purple-600' size={24} />
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className='grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'>
        <Button
          variant='outline'
          className='p-6 h-auto flex-col'
          to={`/restaurant/menu-manager${
            restaurantId ? `/${restaurantId}` : ""
          }`}
        >
          <Package size={32} className='mb-3 text-blue-600' />
          <span className='text-lg font-medium'>Manage Menu</span>
          <span className='text-sm text-gray-500'>
            Add, edit, or remove items
          </span>
        </Button>

        <Button
          variant='outline'
          className='p-6 h-auto flex-col'
          to={`/restaurant/order-manager${
            restaurantId ? `/${restaurantId}` : ""
          }`}
        >
          <ShoppingBag size={32} className='mb-3 text-green-600' />
          <span className='text-lg font-medium'>Manage Orders</span>
          <span className='text-sm text-gray-500'>Track and manage orders</span>
        </Button>

        <Button
          variant='outline'
          className='p-6 h-auto flex-col'
          to='/restaurant/profile'
        >
          <Edit size={32} className='mb-3 text-purple-600' />
          <span className='text-lg font-medium'>Edit Profile</span>
          <span className='text-sm text-gray-500'>Update restaurant info</span>
        </Button>
      </div>

      {/* Popular Items */}
      {stats.popularItems.length > 0 && (
        <Card className='p-6'>
          <h3 className='text-lg font-semibold mb-4'>Popular Items</h3>
          <div className='space-y-3'>
            {stats.popularItems.map((item, index) => (
              <div key={index} className='flex items-center justify-between'>
                <span className='font-medium'>{item.name}</span>
                <Badge variant='secondary'>{item.count} orders</Badge>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
}

export default Dashboard;
