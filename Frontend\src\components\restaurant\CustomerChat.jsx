import React, { useState, useEffect, useRef } from 'react';
import { Send, X, User, Phone } from 'lucide-react';
import Button from '../common/Button';
import { mockUsers } from '../../data/users';

const CustomerChat = ({ order, onClose }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  
  const customer = mockUsers.find(user => user.id === order.customerId);

  // Load initial messages (in a real app, these would come from an API)
  useEffect(() => {
    // Simulate loading messages
    const initialMessages = [
      {
        id: 'msg-1',
        sender: 'customer',
        text: 'Hello, I wanted to check on my order status.',
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString() // 15 minutes ago
      },
      {
        id: 'msg-2',
        sender: 'restaurant',
        text: 'Hi there! Your order is being prepared and should be ready soon.',
        timestamp: new Date(Date.now() - 1000 * 60 * 14).toISOString() // 14 minutes ago
      },
      {
        id: 'msg-3',
        sender: 'customer',
        text: 'Great, thank you! Could you make sure the food is not too spicy?',
        timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString() // 10 minutes ago
      }
    ];
    
    setMessages(initialMessages);
    
    // Focus the input field when component mounts
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [order.id]);

  // Scroll to bottom whenever messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    
    if (!newMessage.trim()) return;
    
    const message = {
      id: `msg-${Date.now()}`,
      sender: 'restaurant',
      text: newMessage.trim(),
      timestamp: new Date().toISOString()
    };
    
    setMessages([...messages, message]);
    setNewMessage('');
    
    // In a real app, you would send this message to an API
    
    // Simulate customer response after a delay
    if (messages.length < 5) {
      setTimeout(() => {
        const customerResponse = {
          id: `msg-${Date.now()}`,
          sender: 'customer',
          text: 'Thank you for the update!',
          timestamp: new Date().toISOString()
        };
        
        setMessages(prev => [...prev, customerResponse]);
      }, 3000);
    }
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Group messages by date
  const groupMessagesByDate = () => {
    const groups = {};
    
    messages.forEach(message => {
      const date = new Date(message.timestamp);
      const dateKey = date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
      
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      
      groups[dateKey].push(message);
    });
    
    return groups;
  };

  const messageGroups = groupMessagesByDate();

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl h-[600px] flex flex-col animate-fade-in">
        {/* Chat Header */}
        <div className="p-4 border-b flex justify-between items-center">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center mr-3">
              <User size={20} className="text-gray-600" />
            </div>
            <div>
              <h3 className="font-medium">{customer?.name || 'Customer'}</h3>
              <div className="flex items-center text-sm text-gray-500">
                <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-1"></span>
                Online
              </div>
            </div>
          </div>
          <div className="flex items-center">
            <Button
              variant="outline"
              size="small"
              icon={<Phone size={16} />}
              className="mr-2"
              onClick={() => window.open(`tel:${customer?.phone}`)}
            >
              Call
            </Button>
            <button
              className="text-gray-400 hover:text-gray-600"
              onClick={onClose}
            >
              <X size={24} />
            </button>
          </div>
        </div>
        
        {/* Chat Messages */}
        <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
          {Object.entries(messageGroups).map(([date, msgs]) => (
            <div key={date}>
              <div className="text-center my-4">
                <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                  {date}
                </span>
              </div>
              
              {msgs.map(message => (
                <div
                  key={message.id}
                  className={`flex mb-4 ${
                    message.sender === 'restaurant' ? 'justify-end' : 'justify-start'
                  }`}
                >
                  <div
                    className={`max-w-[70%] rounded-lg px-4 py-2 ${
                      message.sender === 'restaurant'
                        ? 'bg-primary-500 text-white'
                        : 'bg-white border border-gray-200'
                    }`}
                  >
                    <p className="text-sm">{message.text}</p>
                    <div
                      className={`text-xs mt-1 ${
                        message.sender === 'restaurant' ? 'text-primary-100' : 'text-gray-500'
                      }`}
                    >
                      {formatTime(message.timestamp)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
        
        {/* Chat Input */}
        <form onSubmit={handleSendMessage} className="p-4 border-t flex">
          <input
            ref={inputRef}
            type="text"
            className="flex-1 border rounded-l-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
          />
          <Button
            type="submit"
            variant="primary"
            className="rounded-l-none"
            icon={<Send size={16} />}
          >
            Send
          </Button>
        </form>
      </div>
    </div>
  );
};

export default CustomerChat;
