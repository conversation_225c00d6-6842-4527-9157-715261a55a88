from django.contrib import admin
from .models import *
# Register your models here.
@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ('id', 'customer', 'restaurant', 'status', 'total_amount', 'created_at')
    list_filter = ('status', 'payment_method', 'payment_status')
    search_fields = ('customer__username', 'restaurant__name', 'transaction_id')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-created_at',)

@admin.register(OrderItem)
class OrderItemAdmin(admin.ModelAdmin):
    list_display = ('id', 'order', 'menu_item', 'quantity', 'price_at_order')
    list_filter = ('order__restaurant',)
    search_fields = ('menu_item__name', 'order__id')




class SavedCartItemInline(admin.TabularInline):
    model = SavedCartItem
    extra = 1  # Number of empty forms to display

class SavedCartAdmin(admin.ModelAdmin):
    list_display = ('user', 'restaurant', 'created_at', 'updated_at')
    inlines = [SavedCartItemInline]


@admin.register(OrderStatusHistory)
class OrderStatusHistoryAdmin(admin.ModelAdmin):
    list_display = ('order', 'from_status', 'to_status', 'changed_by', 'created_at')
    list_filter = ('to_status', 'created_at')

@admin.register(RejectionLog)
class RejectionLogAdmin(admin.ModelAdmin):
    list_display = ('id','order', 'agent', 'reason', 'created_at')
    list_filter = ('agent', 'order')


@admin.register(AssignmentLog)
class AssignmentLogAdmin(admin.ModelAdmin):
    list_display = ('id','order', 'agent',  'created_at')
    list_filter = ('agent', 'order')
    
admin.site.register(SavedCart, SavedCartAdmin)
admin.site.register(SavedCartItem)