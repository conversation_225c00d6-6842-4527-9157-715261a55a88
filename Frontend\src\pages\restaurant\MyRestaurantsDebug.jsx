import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import restaurantApi from "../../utils/restaurantApi";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";

const MyRestaurantsDebug = () => {
  const { user } = useAuth();
  const [restaurants, setRestaurants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [debugInfo, setDebugInfo] = useState({});

  useEffect(() => {
    const fetchRestaurants = async () => {
      console.log("🔍 Debug: Starting to fetch restaurants");
      console.log("🔍 Debug: User:", user);

      // Check localStorage for authentication
      const storedUser = JSON.parse(
        localStorage.getItem("afghanSofraUser") || "{}"
      );
      console.log("🔍 Debug: Stored user:", storedUser);

      setDebugInfo((prev) => ({
        ...prev,
        user: user,
        userRole: user?.role,
        userEmail: user?.email,
        storedUser: storedUser,
        hasToken: !!storedUser.access_token,
        timestamp: new Date().toISOString(),
      }));

      if (!user) {
        setError("No user found");
        setLoading(false);
        return;
      }

      if (user.role !== "restaurant") {
        setError("User is not a restaurant owner");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        console.log("🔍 Debug: Calling getUserRestaurants API");
        const result = await restaurantApi.getUserRestaurants();

        console.log("🔍 Debug: API Result:", result);

        setDebugInfo((prev) => ({
          ...prev,
          apiResult: result,
          apiSuccess: result.success,
          apiError: result.error,
          apiData: result.data,
        }));

        if (result.success) {
          setRestaurants(result.data || []);
          console.log("🔍 Debug: Restaurants set:", result.data);
        } else {
          setError(result.error || "Failed to fetch restaurants");
          console.error("🔍 Debug: API Error:", result.error);
        }
      } catch (err) {
        console.error("🔍 Debug: Exception:", err);
        setError("Exception occurred: " + err.message);
        setDebugInfo((prev) => ({
          ...prev,
          exception: err.message,
          exceptionStack: err.stack,
        }));
      } finally {
        setLoading(false);
      }
    };

    fetchRestaurants();
  }, [user]);

  if (loading) {
    return (
      <div className='p-6'>
        <h1 className='text-2xl font-bold mb-4'>My Restaurants (Debug)</h1>
        <div className='animate-pulse'>
          <div className='h-4 bg-gray-200 rounded w-1/4 mb-4'></div>
          <div className='h-32 bg-gray-200 rounded'></div>
        </div>
      </div>
    );
  }

  return (
    <div className='p-6'>
      <h1 className='text-2xl font-bold mb-4'>My Restaurants (Debug)</h1>

      {/* Debug Information */}
      <Card className='mb-6 p-4 bg-blue-50'>
        <h3 className='font-semibold mb-2'>Debug Information:</h3>
        <pre className='text-sm bg-white p-2 rounded overflow-auto max-h-40'>
          {JSON.stringify(debugInfo, null, 2)}
        </pre>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className='mb-6 p-4 bg-red-50 border-red-200'>
          <h3 className='font-semibold text-red-800 mb-2'>Error:</h3>
          <p className='text-red-700'>{error}</p>
        </Card>
      )}

      {/* Restaurants Display */}
      <Card className='p-4'>
        <h3 className='font-semibold mb-4'>
          Restaurants ({restaurants.length}):
        </h3>

        {restaurants.length === 0 ? (
          <div className='text-center py-8'>
            <p className='text-gray-500 mb-4'>No restaurants found</p>
            <Button variant='primary' to='/add-restaurant'>
              Add Your First Restaurant
            </Button>
          </div>
        ) : (
          <div className='space-y-4'>
            {restaurants.map((restaurant, index) => (
              <Card key={restaurant.id || index} className='p-4 border'>
                <h4 className='font-semibold'>
                  {restaurant.name || "Unnamed Restaurant"}
                </h4>
                <div className='mt-2 text-sm text-gray-600'>
                  <p>ID: {restaurant.id}</p>
                  <p>Active: {restaurant.is_active ? "Yes" : "No"}</p>
                  <p>Verified: {restaurant.is_verified ? "Yes" : "No"}</p>
                  <p>Created: {restaurant.created_at}</p>
                  <p>Owner ID: {restaurant.owner}</p>
                </div>
                <div className='mt-3'>
                  <Button
                    variant='primary'
                    size='small'
                    to={`/restaurant/dashboard/${restaurant.id}`}
                  >
                    Manage Restaurant
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        )}
      </Card>

      {/* Raw Data Display */}
      <Card className='mt-6 p-4 bg-gray-50'>
        <h3 className='font-semibold mb-2'>Raw Restaurant Data:</h3>
        <pre className='text-xs bg-white p-2 rounded overflow-auto max-h-60'>
          {JSON.stringify(restaurants, null, 2)}
        </pre>
      </Card>
    </div>
  );
};

export default MyRestaurantsDebug;
