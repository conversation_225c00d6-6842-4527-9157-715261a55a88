"""
Cash on Delivery (COD) Serializers

This module provides serializers for COD financial operations.
"""

from rest_framework import serializers
from .cod_models import (
    CODTransaction,
    DailyAgentSettlement,
    AgentCashFloat,
    CODFinancialSummary
)


class CODTransactionSerializer(serializers.ModelSerializer):
    """Serializer for COD transactions"""
    order_details = serializers.SerializerMethodField()
    agent_name = serializers.CharField(source='delivery_agent.get_full_name', read_only=True)
    collection_time_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = CODTransaction
        fields = '__all__'
        read_only_fields = [
            'agent_delivery_earnings', 'amount_to_remit', 'reported_time',
            'settlement_status', 'settled_at', 'settlement_reference'
        ]
    
    def get_order_details(self, obj):
        return {
            'id': obj.order.id,
            'customer_name': obj.order.customer.get_full_name() if obj.order.customer else 'Unknown',
            'restaurant_name': obj.order.restaurant.name,
            'delivery_address': str(obj.order.delivery_address),
            'order_status': obj.order.status,
            'created_at': obj.order.created_at,
        }
    
    def get_collection_time_formatted(self, obj):
        return obj.collection_time.strftime('%Y-%m-%d %H:%M:%S') if obj.collection_time else None


class DailyAgentSettlementSerializer(serializers.ModelSerializer):
    """Serializer for daily agent settlements"""
    agent_name = serializers.CharField(source='delivery_agent.get_full_name', read_only=True)
    agent_email = serializers.CharField(source='delivery_agent.email', read_only=True)
    verified_by_name = serializers.CharField(source='verified_by.get_full_name', read_only=True)
    settlement_date_formatted = serializers.SerializerMethodField()
    days_overdue = serializers.SerializerMethodField()
    
    class Meta:
        model = DailyAgentSettlement
        fields = '__all__'
        read_only_fields = [
            'total_orders', 'total_cash_collected', 'total_agent_earnings',
            'total_remittance_due', 'is_verified', 'verified_by', 'verified_at'
        ]
    
    def get_settlement_date_formatted(self, obj):
        return obj.settlement_date.strftime('%Y-%m-%d')
    
    def get_days_overdue(self, obj):
        if obj.status == 'overdue' and obj.settlement_deadline:
            from django.utils import timezone
            days = (timezone.now().date() - obj.settlement_deadline.date()).days
            return max(0, days)
        return 0


class AgentCashFloatSerializer(serializers.ModelSerializer):
    """Serializer for agent cash floats"""
    agent_name = serializers.CharField(source='delivery_agent.get_full_name', read_only=True)
    issued_by_name = serializers.CharField(source='issued_by.get_full_name', read_only=True)
    
    class Meta:
        model = AgentCashFloat
        fields = '__all__'
        read_only_fields = ['issued_by', 'created_at', 'updated_at']


class CODFinancialSummarySerializer(serializers.ModelSerializer):
    """Serializer for COD financial summaries"""
    period_formatted = serializers.SerializerMethodField()
    
    class Meta:
        model = CODFinancialSummary
        fields = '__all__'
        read_only_fields = ['created_at']
    
    def get_period_formatted(self, obj):
        return f"{obj.period_start.strftime('%Y-%m-%d')} to {obj.period_end.strftime('%Y-%m-%d')}"


# Request/Response Serializers

class CashCollectionSerializer(serializers.Serializer):
    """Serializer for cash collection requests"""
    order_id = serializers.IntegerField()
    cash_collected = serializers.DecimalField(max_digits=10, decimal_places=2)
    customer_paid_amount = serializers.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        required=False,
        help_text="Amount customer actually paid (if different from order total)"
    )
    gps_latitude = serializers.DecimalField(
        max_digits=10, 
        decimal_places=7, 
        required=False,
        help_text="GPS latitude of collection location"
    )
    gps_longitude = serializers.DecimalField(
        max_digits=10, 
        decimal_places=7, 
        required=False,
        help_text="GPS longitude of collection location"
    )
    notes = serializers.CharField(
        max_length=500, 
        required=False, 
        allow_blank=True,
        help_text="Any notes about the collection"
    )
    
    def validate_cash_collected(self, value):
        if value <= 0:
            raise serializers.ValidationError("Cash collected must be greater than 0")
        return value
    
    def validate(self, data):
        customer_paid = data.get('customer_paid_amount', data['cash_collected'])
        if customer_paid < data['cash_collected']:
            raise serializers.ValidationError(
                "Customer paid amount cannot be less than cash collected"
            )
        return data


class SettlementRequestSerializer(serializers.Serializer):
    """Serializer for settlement requests"""
    settlement_date = serializers.DateField(required=False)
    settlement_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    settlement_method = serializers.ChoiceField(choices=[
        ('bank_deposit', 'Bank Deposit'),
        ('digital_transfer', 'Digital Transfer'),
        ('office_dropoff', 'Office Drop-off'),
        ('mobile_money', 'Mobile Money'),
        ('cash_pickup', 'Cash Pickup'),
    ])
    reference = serializers.CharField(
        max_length=100, 
        required=False, 
        allow_blank=True,
        help_text="Transaction reference or receipt number"
    )
    notes = serializers.CharField(
        max_length=500, 
        required=False, 
        allow_blank=True,
        help_text="Additional notes about the settlement"
    )
    
    def validate_settlement_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("Settlement amount must be greater than 0")
        return value


class FloatRequestSerializer(serializers.Serializer):
    """Serializer for cash float requests"""
    float_amount = serializers.DecimalField(max_digits=8, decimal_places=2)
    requested_denominations = serializers.JSONField(
        required=False,
        help_text="Requested breakdown of notes/coins"
    )
    reason = serializers.CharField(
        max_length=200,
        help_text="Reason for requesting float"
    )
    
    def validate_float_amount(self, value):
        if value <= 0:
            raise serializers.ValidationError("Float amount must be greater than 0")
        if value > 1000:  # Maximum float limit
            raise serializers.ValidationError("Float amount cannot exceed $1000")
        return value


class CODDashboardSerializer(serializers.Serializer):
    """Serializer for COD dashboard data"""
    # Today's metrics
    today_orders = serializers.IntegerField()
    today_collected = serializers.DecimalField(max_digits=12, decimal_places=2)
    today_agent_earnings = serializers.DecimalField(max_digits=10, decimal_places=2)
    today_remittance_due = serializers.DecimalField(max_digits=12, decimal_places=2)
    
    # Settlement metrics
    pending_settlements = serializers.IntegerField()
    overdue_settlements = serializers.IntegerField()
    pending_amount = serializers.DecimalField(max_digits=12, decimal_places=2)
    
    # Performance metrics
    collection_success_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    settlement_success_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    avg_settlement_time = serializers.DecimalField(max_digits=5, decimal_places=2)
    
    # Agent metrics
    active_agents = serializers.IntegerField()
    agents_with_pending = serializers.IntegerField()
    top_collecting_agents = serializers.ListField()
    
    # Financial health
    cash_flow_status = serializers.CharField()
    total_float_issued = serializers.DecimalField(max_digits=10, decimal_places=2)
    platform_cash_position = serializers.DecimalField(max_digits=15, decimal_places=2)


class AgentCODSummarySerializer(serializers.Serializer):
    """Serializer for agent COD summary"""
    agent_id = serializers.IntegerField()
    agent_name = serializers.CharField()
    
    # Today's activity
    today_orders = serializers.IntegerField()
    today_collected = serializers.DecimalField(max_digits=10, decimal_places=2)
    today_earnings = serializers.DecimalField(max_digits=8, decimal_places=2)
    today_to_remit = serializers.DecimalField(max_digits=10, decimal_places=2)
    
    # Current status
    current_float = serializers.DecimalField(max_digits=8, decimal_places=2)
    cash_in_hand = serializers.DecimalField(max_digits=10, decimal_places=2)
    pending_settlements = serializers.IntegerField()
    pending_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    
    # Performance
    weekly_orders = serializers.IntegerField()
    weekly_earnings = serializers.DecimalField(max_digits=10, decimal_places=2)
    collection_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    settlement_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    
    # Status indicators
    settlement_status = serializers.CharField()
    last_settlement_date = serializers.DateField(allow_null=True)
    next_settlement_due = serializers.DateField(allow_null=True)


class CODAnalyticsSerializer(serializers.Serializer):
    """Serializer for COD analytics data"""
    period = serializers.CharField()
    start_date = serializers.DateField()
    end_date = serializers.DateField()
    
    # Volume metrics
    total_cod_orders = serializers.IntegerField()
    total_cash_collected = serializers.DecimalField(max_digits=15, decimal_places=2)
    avg_order_value = serializers.DecimalField(max_digits=8, decimal_places=2)
    
    # Revenue distribution
    total_agent_earnings = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_restaurant_earnings = serializers.DecimalField(max_digits=12, decimal_places=2)
    total_platform_revenue = serializers.DecimalField(max_digits=12, decimal_places=2)
    
    # Operational metrics
    collection_success_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    settlement_success_rate = serializers.DecimalField(max_digits=5, decimal_places=2)
    avg_settlement_time_hours = serializers.DecimalField(max_digits=5, decimal_places=2)
    
    # Trends
    daily_collections = serializers.ListField()
    weekly_trends = serializers.ListField()
    agent_performance = serializers.ListField()
    
    # Risk metrics
    overdue_settlements = serializers.IntegerField()
    high_risk_agents = serializers.IntegerField()
    cash_variance_percentage = serializers.DecimalField(max_digits=5, decimal_places=2)
