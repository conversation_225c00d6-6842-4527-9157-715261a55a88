"""
Delivery Agent Payment System

This module implements the missing delivery agent payment functionality
to complete the cash flow system for Afghan Sofra.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class DeliveryAgentCommission(models.Model):
    """Commission structure for delivery agents"""
    delivery_agent = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='delivery_commission',
        limit_choices_to={'role': 'delivery_agent'}
    )
    
    # Base earnings structure
    base_delivery_fee = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=5.00,
        help_text="Base fee per delivery"
    )
    
    # Distance-based earnings
    distance_rate_per_km = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0.50,
        help_text="Additional fee per kilometer"
    )
    
    # Time-based bonuses
    time_bonus_per_minute = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0.10,
        help_text="Bonus per minute for fast delivery"
    )
    
    # Platform share
    platform_commission_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=30.00,
        validators=[MinValueValidator(0), MaxValueValidator(50)],
        help_text="Platform's share of delivery fee (0-50%)"
    )
    
    # Payout settings
    minimum_payout_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=25.00,
        help_text="Minimum amount for payout"
    )
    
    payout_frequency = models.CharField(
        max_length=20,
        choices=[
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
            ('bi_weekly', 'Bi-Weekly'),
            ('monthly', 'Monthly'),
        ],
        default='weekly'
    )
    
    # Performance bonuses
    performance_bonus_threshold = models.PositiveIntegerField(
        default=20,
        help_text="Minimum deliveries per week for bonus"
    )
    
    performance_bonus_amount = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=10.00,
        help_text="Weekly performance bonus amount"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.delivery_agent.email} - Commission Structure"


class DeliveryAgentEarnings(models.Model):
    """Individual delivery earnings for agents"""
    delivery_agent = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='delivery_earnings',
        limit_choices_to={'role': 'delivery_agent'}
    )
    
    order = models.OneToOneField(
        'orders.Order',
        on_delete=models.CASCADE,
        related_name='delivery_earnings'
    )
    
    # Delivery details
    delivery_distance_km = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0,
        help_text="Delivery distance in kilometers"
    )
    
    delivery_time_minutes = models.PositiveIntegerField(
        default=0,
        help_text="Actual delivery time in minutes"
    )
    
    # Earnings breakdown
    base_fee = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    distance_bonus = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    time_bonus = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    tips = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    
    # Platform fees
    gross_earnings = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    platform_commission = models.DecimalField(max_digits=6, decimal_places=2, default=0)
    net_earnings = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    # Status
    is_paid = models.BooleanField(default=False)
    paid_at = models.DateTimeField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.delivery_agent.email} - Order #{self.order.id} - ${self.net_earnings}"

    def calculate_earnings(self):
        """Calculate delivery agent earnings based on commission structure"""
        try:
            commission = self.delivery_agent.delivery_commission
        except DeliveryAgentCommission.DoesNotExist:
            # Create default commission structure
            commission = DeliveryAgentCommission.objects.create(
                delivery_agent=self.delivery_agent
            )
        
        # Calculate base fee
        self.base_fee = commission.base_delivery_fee
        
        # Calculate distance bonus
        self.distance_bonus = self.delivery_distance_km * commission.distance_rate_per_km
        
        # Calculate time bonus (for deliveries faster than expected)
        expected_time = 30  # Expected 30 minutes
        if self.delivery_time_minutes < expected_time:
            time_saved = expected_time - self.delivery_time_minutes
            self.time_bonus = time_saved * commission.time_bonus_per_minute
        
        # Calculate gross earnings
        self.gross_earnings = (
            self.base_fee + 
            self.distance_bonus + 
            self.time_bonus + 
            self.tips
        )
        
        # Calculate platform commission
        self.platform_commission = (
            (self.base_fee + self.distance_bonus) * 
            commission.platform_commission_rate / 100
        )
        
        # Calculate net earnings (tips are not subject to commission)
        self.net_earnings = self.gross_earnings - self.platform_commission
        
        self.save()
        
        logger.info(
            f"Calculated earnings for agent {self.delivery_agent.email}: "
            f"Gross: ${self.gross_earnings}, Net: ${self.net_earnings}"
        )


class DeliveryAgentPayout(models.Model):
    """Delivery agent payout records"""
    delivery_agent = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='delivery_payouts',
        limit_choices_to={'role': 'delivery_agent'}
    )
    
    # Payout details
    payout_amount = models.DecimalField(max_digits=12, decimal_places=2)
    payout_period_start = models.DateTimeField()
    payout_period_end = models.DateTimeField()
    
    # Deliveries included in this payout
    deliveries_count = models.PositiveIntegerField()
    total_gross_earnings = models.DecimalField(max_digits=12, decimal_places=2)
    total_commission = models.DecimalField(max_digits=12, decimal_places=2)
    total_tips = models.DecimalField(max_digits=12, decimal_places=2)
    performance_bonus = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    # Payout status
    PAYOUT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    status = models.CharField(
        max_length=20,
        choices=PAYOUT_STATUS_CHOICES,
        default='pending'
    )
    
    # Payment details
    payment_method = models.CharField(
        max_length=50,
        choices=[
            ('bank_transfer', 'Bank Transfer'),
            ('paypal', 'PayPal'),
            ('stripe', 'Stripe'),
            ('digital_wallet', 'Digital Wallet'),
            ('cash', 'Cash'),
        ],
        default='bank_transfer'
    )
    transaction_id = models.CharField(max_length=100, blank=True)
    payment_reference = models.CharField(max_length=100, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.delivery_agent.email} - ${self.payout_amount} - {self.status}"

    def calculate_performance_bonus(self):
        """Calculate performance bonus based on delivery count"""
        try:
            commission = self.delivery_agent.delivery_commission
            if self.deliveries_count >= commission.performance_bonus_threshold:
                self.performance_bonus = commission.performance_bonus_amount
            else:
                self.performance_bonus = Decimal('0.00')
        except DeliveryAgentCommission.DoesNotExist:
            self.performance_bonus = Decimal('0.00')
        
        # Update total payout amount
        self.payout_amount = (
            self.total_gross_earnings - 
            self.total_commission + 
            self.performance_bonus
        )
        self.save()


class DeliveryAgentFinancialSummary(models.Model):
    """Weekly/monthly financial summary for delivery agents"""
    delivery_agent = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='financial_summaries',
        limit_choices_to={'role': 'delivery_agent'}
    )
    
    # Period
    period_type = models.CharField(
        max_length=20,
        choices=[
            ('weekly', 'Weekly'),
            ('monthly', 'Monthly'),
        ]
    )
    period_start = models.DateTimeField()
    period_end = models.DateTimeField()
    
    # Financial metrics
    total_deliveries = models.PositiveIntegerField(default=0)
    total_distance_km = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    total_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_tips = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_bonuses = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    platform_commission = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    net_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    
    # Performance metrics
    average_delivery_time = models.DecimalField(max_digits=5, decimal_places=2, default=0)
    average_earnings_per_delivery = models.DecimalField(max_digits=8, decimal_places=2, default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-period_start']
        unique_together = ['delivery_agent', 'period_type', 'period_start']

    def __str__(self):
        return f"{self.delivery_agent.email} - {self.period_type} - {self.period_start.date()}"
