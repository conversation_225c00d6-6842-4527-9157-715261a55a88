import React, { createContext, useContext, useState, useEffect } from "react";
import configService from "../services/configService";
import {
  useFilters as useDynamicFilters,
  useRestaurantFiltering,
} from "../hooks/useFilters";

const FiltersContext = createContext();

export const useFilters = () => {
  const context = useContext(FiltersContext);
  if (!context) {
    throw new Error("useFilters must be used within a FiltersProvider");
  }
  return context;
};

export const FiltersProvider = ({ children }) => {
  const [filters, setFilters] = useState({
    priceRange: [1, 4], // $1 to $$$$
    cuisines: [],
    dietaryRestrictions: [],
    deliveryTime: null, // in minutes
    rating: null,
    distance: null, // in km
    features: [], // free delivery, new, popular
    sortBy: "relevance",
  });

  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [restaurants, setRestaurants] = useState([]);

  // Use dynamic filter system
  const {
    filterOptions,
    loading: filtersLoading,
    error: filtersError,
    refreshFilters,
  } = useDynamicFilters();
  const { filteredRestaurants, applyFilters } =
    useRestaurantFiltering(restaurants);

  // Apply filters whenever filters or restaurants change
  useEffect(() => {
    applyFilters(filters);
  }, [filters, restaurants, applyFilters]);

  const [appliedFiltersCount, setAppliedFiltersCount] = useState(0);

  // Quick filter presets
  const quickFilters = [
    {
      id: "popular",
      label: "Popular",
      icon: "🔥",
      filters: { features: ["popular"], sortBy: "popularity" },
    },
    {
      id: "fast-delivery",
      label: "Fast Delivery",
      icon: "⚡",
      filters: { deliveryTime: 30, features: ["fast-delivery"] },
    },
    {
      id: "highly-rated",
      label: "Top Rated",
      icon: "⭐",
      filters: { rating: 4.5, sortBy: "rating" },
    },
    {
      id: "free-delivery",
      label: "Free Delivery",
      icon: "🚚",
      filters: { features: ["free-delivery"] },
    },
    {
      id: "vegetarian",
      label: "Vegetarian",
      icon: "🥬",
      filters: { dietaryRestrictions: ["vegetarian"] },
    },
    {
      id: "budget-friendly",
      label: "Budget Friendly",
      icon: "💰",
      filters: { priceRange: [1, 2], sortBy: "price-low" },
    },
  ];

  // Load saved filters from localStorage
  useEffect(() => {
    const savedFilters = localStorage.getItem("restaurantFilters");
    if (savedFilters) {
      try {
        const parsed = JSON.parse(savedFilters);
        setFilters((prev) => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error("Error loading saved filters:", error);
      }
    }
  }, []);

  // Save filters to localStorage
  useEffect(() => {
    localStorage.setItem("restaurantFilters", JSON.stringify(filters));
  }, [filters]);

  // Note: applyFilters is called automatically by the useRestaurantFiltering hook

  // Count applied filters
  useEffect(() => {
    let count = 0;
    if (filters.cuisines.length > 0) count++;
    if (filters.dietaryRestrictions.length > 0) count++;
    if (filters.deliveryTime !== null) count++;
    if (filters.rating !== null) count++;
    if (filters.distance !== null) count++;
    if (filters.features.length > 0) count++;
    if (filters.priceRange[0] !== 1 || filters.priceRange[1] !== 4) count++;

    setAppliedFiltersCount(count);
  }, [filters]);

  // Using the sortRestaurants function from the useRestaurantFiltering hook
  const sortRestaurants = (sortBy) => {
    // Update the sort option and let the hook handle the sorting
    updateFilter("sortBy", sortBy);
  };

  const updateFilter = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const toggleArrayFilter = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: prev[key].includes(value)
        ? prev[key].filter((item) => item !== value)
        : [...prev[key], value],
    }));
  };

  const applyQuickFilter = (quickFilter) => {
    setFilters((prev) => ({ ...prev, ...quickFilter.filters }));
  };

  const clearAllFilters = () => {
    setFilters({
      priceRange: [1, 4],
      cuisines: [],
      dietaryRestrictions: [],
      deliveryTime: null,
      rating: null,
      distance: null,
      features: [],
      sortBy: "relevance",
    });
  };

  const clearFilter = (filterType) => {
    switch (filterType) {
      case "priceRange":
        updateFilter("priceRange", [1, 4]);
        break;
      case "cuisines":
        updateFilter("cuisines", []);
        break;
      case "dietaryRestrictions":
        updateFilter("dietaryRestrictions", []);
        break;
      case "deliveryTime":
        updateFilter("deliveryTime", null);
        break;
      case "rating":
        updateFilter("rating", null);
        break;
      case "distance":
        updateFilter("distance", null);
        break;
      case "features":
        updateFilter("features", []);
        break;
      default:
        break;
    }
  };

  const value = {
    filters,
    filteredRestaurants,
    filterOptions,
    quickFilters,
    isFilterPanelOpen,
    setIsFilterPanelOpen,
    appliedFiltersCount,
    updateFilter,
    toggleArrayFilter,
    applyQuickFilter,
    clearAllFilters,
    clearFilter,
    sortRestaurants,
    restaurants,
    setRestaurants,
    filtersLoading,
    filtersError,
    refreshFilters,
  };

  return (
    <FiltersContext.Provider value={value}>{children}</FiltersContext.Provider>
  );
};

export default FiltersContext;
