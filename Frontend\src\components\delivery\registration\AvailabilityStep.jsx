import React from 'react';
import { Clock, MapPin, Calendar, Star, TrendingUp } from 'lucide-react';

const AvailabilityStep = ({ data, onChange, errors }) => {
  const daysOfWeek = [
    { key: 'monday', label: 'Monday' },
    { key: 'tuesday', label: 'Tuesday' },
    { key: 'wednesday', label: 'Wednesday' },
    { key: 'thursday', label: 'Thursday' },
    { key: 'friday', label: 'Friday' },
    { key: 'saturday', label: 'Saturday' },
    { key: 'sunday', label: 'Sunday' },
  ];

  const zones = [
    { id: 'downtown', name: 'Downtown', demand: 'High', color: 'bg-red-100 text-red-800' },
    { id: 'university', name: 'University Area', demand: 'High', color: 'bg-red-100 text-red-800' },
    { id: 'suburbs', name: 'Suburbs', demand: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
    { id: 'industrial', name: 'Industrial District', demand: 'Low', color: 'bg-green-100 text-green-800' },
    { id: 'airport', name: 'Airport Area', demand: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
    { id: 'mall', name: 'Shopping Mall Area', demand: 'High', color: 'bg-red-100 text-red-800' },
  ];

  const handleDayToggle = (day) => {
    const newWorkingHours = {
      ...data.workingHours,
      [day]: {
        ...data.workingHours[day],
        available: !data.workingHours[day].available,
      },
    };
    onChange('workingHours', newWorkingHours);
  };

  const handleTimeChange = (day, timeType, value) => {
    const newWorkingHours = {
      ...data.workingHours,
      [day]: {
        ...data.workingHours[day],
        [timeType]: value,
      },
    };
    onChange('workingHours', newWorkingHours);
  };

  const handleZoneToggle = (zoneId) => {
    const newZones = data.preferredZones.includes(zoneId)
      ? data.preferredZones.filter(id => id !== zoneId)
      : [...data.preferredZones, zoneId];
    onChange('preferredZones', newZones);
  };

  return (
    <div className="space-y-6">
      {/* Availability Header */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
        <div className="flex items-start">
          <Calendar className="h-6 w-6 text-purple-600 mt-1 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-purple-800 mb-2">
              Set Your Availability
            </h3>
            <p className="text-purple-700">
              Choose your preferred working hours and delivery zones. You can always 
              update these settings later in your dashboard.
            </p>
          </div>
        </div>
      </div>

      {/* Working Hours */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          <Clock className="inline h-5 w-5 mr-2" />
          Working Hours
        </h3>
        <div className="space-y-4">
          {daysOfWeek.map((day) => (
            <div key={day.key} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id={day.key}
                  checked={data.workingHours[day.key].available}
                  onChange={() => handleDayToggle(day.key)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor={day.key} className="ml-3 text-sm font-medium text-gray-700 w-20">
                  {day.label}
                </label>
              </div>
              
              {data.workingHours[day.key].available && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">From:</span>
                  <input
                    type="time"
                    value={data.workingHours[day.key].start}
                    onChange={(e) => handleTimeChange(day.key, 'start', e.target.value)}
                    className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-600">To:</span>
                  <input
                    type="time"
                    value={data.workingHours[day.key].end}
                    onChange={(e) => handleTimeChange(day.key, 'end', e.target.value)}
                    className="px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}
              
              {!data.workingHours[day.key].available && (
                <span className="text-sm text-gray-400">Not available</span>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Preferred Zones */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          <MapPin className="inline h-5 w-5 mr-2" />
          Preferred Delivery Zones
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          Select the areas where you'd like to make deliveries. You can choose multiple zones.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {zones.map((zone) => (
            <div
              key={zone.id}
              onClick={() => handleZoneToggle(zone.id)}
              className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                data.preferredZones.includes(zone.id)
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">{zone.name}</h4>
                  <div className="flex items-center mt-1">
                    <span className="text-sm text-gray-600 mr-2">Demand:</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${zone.color}`}>
                      {zone.demand}
                    </span>
                  </div>
                </div>
                <div className="flex items-center">
                  {data.preferredZones.includes(zone.id) && (
                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Earnings Insights */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-green-800 mb-3">
          <TrendingUp className="inline h-5 w-5 mr-2" />
          Earnings Insights
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded-lg border border-green-200">
            <div className="flex items-center mb-2">
              <Star className="h-4 w-4 text-yellow-500 mr-1" />
              <span className="text-sm font-medium text-gray-900">Peak Hours</span>
            </div>
            <p className="text-sm text-gray-600">
              12:00-14:00 & 18:00-21:00 typically have the highest demand and earnings potential.
            </p>
          </div>
          
          <div className="bg-white p-4 rounded-lg border border-green-200">
            <div className="flex items-center mb-2">
              <MapPin className="h-4 w-4 text-blue-500 mr-1" />
              <span className="text-sm font-medium text-gray-900">High-Demand Zones</span>
            </div>
            <p className="text-sm text-gray-600">
              Downtown and University areas typically offer more delivery opportunities.
            </p>
          </div>
        </div>
      </div>

      {/* Flexibility Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-800 mb-2">
          Flexible Schedule
        </h3>
        <p className="text-sm text-blue-700">
          Remember, these are just your preferred hours and zones. You can always:
        </p>
        <ul className="mt-2 text-sm text-blue-700 space-y-1">
          <li>• Go online/offline anytime during your available hours</li>
          <li>• Accept deliveries outside your preferred zones if you choose</li>
          <li>• Update your availability settings anytime in your dashboard</li>
          <li>• Take breaks whenever you need them</li>
        </ul>
      </div>
    </div>
  );
};

export default AvailabilityStep;
