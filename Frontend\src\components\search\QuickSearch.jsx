import React from 'react';
import { Search, TrendingUp } from 'lucide-react';
import { useSearch } from '../../context/SearchContext';
import { useNavigate } from 'react-router-dom';
import SearchBox from './SearchBox';

const QuickSearch = () => {
  const { popularSearches } = useSearch();
  const navigate = useNavigate();

  const handleQuickSearch = (query) => {
    navigate(`/search?q=${encodeURIComponent(query)}`);
  };

  return (
    <div className="bg-white rounded-2xl shadow-lg p-8 -mt-8 relative z-10 mx-4">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          What are you craving today?
        </h2>
        <p className="text-gray-600">
          Search for restaurants, dishes, or cuisines
        </p>
      </div>

      {/* Main Search Box */}
      <div className="mb-8">
        <SearchBox 
          placeholder="Search for restaurants, dishes, cuisines..."
          className="w-full"
        />
      </div>

      {/* Popular Searches */}
      <div>
        <div className="flex items-center mb-4">
          <TrendingUp size={18} className="text-primary-500 mr-2" />
          <h3 className="font-semibold text-gray-900">Popular Searches</h3>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {popularSearches.slice(0, 8).map((search, index) => (
            <button
              key={index}
              onClick={() => handleQuickSearch(search)}
              className="px-4 py-2 bg-gray-100 hover:bg-primary-100 text-gray-700 hover:text-primary-700 rounded-full text-sm font-medium transition-colors duration-200"
            >
              {search}
            </button>
          ))}
        </div>
      </div>

      {/* Search Categories */}
      <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
        {[
          { name: 'Afghan Cuisine', emoji: '🍛', query: 'afghan' },
          { name: 'Fast Food', emoji: '🍔', query: 'fast food' },
          { name: 'Desserts', emoji: '🍰', query: 'dessert' },
          { name: 'Healthy', emoji: '🥗', query: 'healthy' }
        ].map((category, index) => (
          <button
            key={index}
            onClick={() => handleQuickSearch(category.query)}
            className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-colors duration-200 group"
          >
            <span className="text-2xl mb-2">{category.emoji}</span>
            <span className="text-sm font-medium text-gray-700 group-hover:text-primary-700">
              {category.name}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default QuickSearch;
