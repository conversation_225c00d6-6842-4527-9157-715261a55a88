import websocketService from "./websocketService";
import { deliveryAgent<PERSON>pi } from "./deliveryAgentApi";

class LocationService {
  constructor() {
    this.watchId = null;
    this.isTracking = false;
    this.lastKnownPosition = null;
    this.trackingInterval = null;
    this.options = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000,
    };
    this.updateInterval = 30000; // Update every 30 seconds
    this.listeners = new Map();
  }

  // Start location tracking
  async startTracking(options = {}) {
    try {
      // Check if geolocation is supported
      if (!navigator.geolocation) {
        throw new Error("Geolocation is not supported by this browser");
      }

      // Merge options
      this.options = { ...this.options, ...options };
      this.updateInterval = options.updateInterval || this.updateInterval;

      // Request permission first
      const permission = await this.requestPermission();
      if (permission !== "granted") {
        throw new Error("Location permission denied");
      }

      // Start watching position
      this.watchId = navigator.geolocation.watchPosition(
        this.onPositionSuccess.bind(this),
        this.onPositionError.bind(this),
        this.options
      );

      // Start periodic updates
      this.startPeriodicUpdates();

      this.isTracking = true;
      this.emit("tracking_started", { tracking: true });

      console.log("Location tracking started");
      return { success: true, message: "Location tracking started" };
    } catch (error) {
      console.error("Failed to start location tracking:", error);
      this.emit("tracking_error", { error: error.message });
      return { success: false, error: error.message };
    }
  }

  // Stop location tracking
  stopTracking() {
    try {
      // Stop watching position
      if (this.watchId !== null) {
        navigator.geolocation.clearWatch(this.watchId);
        this.watchId = null;
      }

      // Stop periodic updates
      this.stopPeriodicUpdates();

      this.isTracking = false;
      this.emit("tracking_stopped", { tracking: false });

      console.log("Location tracking stopped");
      return { success: true, message: "Location tracking stopped" };
    } catch (error) {
      console.error("Failed to stop location tracking:", error);
      return { success: false, error: error.message };
    }
  }

  // Request location permission
  async requestPermission() {
    try {
      if ("permissions" in navigator) {
        const permission = await navigator.permissions.query({
          name: "geolocation",
        });
        return permission.state;
      } else {
        // Fallback for browsers that don't support permissions API
        return new Promise((resolve) => {
          navigator.geolocation.getCurrentPosition(
            () => resolve("granted"),
            () => resolve("denied"),
            { timeout: 5000 }
          );
        });
      }
    } catch (error) {
      console.error("Error requesting location permission:", error);
      return "denied";
    }
  }

  // Handle successful position update
  onPositionSuccess(position) {
    const { latitude, longitude, accuracy, speed, heading } = position.coords;
    const timestamp = new Date(position.timestamp);

    const locationData = {
      latitude,
      longitude,
      accuracy,
      speed: speed || null,
      heading: heading || null,
      timestamp,
    };

    this.lastKnownPosition = locationData;
    this.emit("position_updated", locationData);

    // Send to server via WebSocket if connected
    if (websocketService.isConnected) {
      websocketService.sendLocationUpdate(latitude, longitude);
    }

    console.log("Location updated:", locationData);
  }

  // Handle position error
  onPositionError(error) {
    let errorMessage = "Unknown location error";

    switch (error.code) {
      case error.PERMISSION_DENIED:
        errorMessage = "Location access denied by user";
        break;
      case error.POSITION_UNAVAILABLE:
        errorMessage = "Location information unavailable";
        break;
      case error.TIMEOUT:
        errorMessage = "Location request timed out";
        break;
    }

    console.error("Location error:", errorMessage);
    this.emit("position_error", { error: errorMessage, code: error.code });
  }

  // Start periodic location updates
  startPeriodicUpdates() {
    this.trackingInterval = setInterval(async () => {
      if (this.lastKnownPosition) {
        try {
          // Update location on server
          await deliveryAgentApi.updateLocation({
            latitude: this.lastKnownPosition.latitude,
            longitude: this.lastKnownPosition.longitude,
            activity_type: "idle",
          });
        } catch (error) {
          console.error("Failed to update location on server:", error);
        }
      }
    }, this.updateInterval);
  }

  // Stop periodic updates
  stopPeriodicUpdates() {
    if (this.trackingInterval) {
      clearInterval(this.trackingInterval);
      this.trackingInterval = null;
    }
  }

  // Get current position (one-time)
  async getCurrentPosition(options = {}) {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported"));
        return;
      }

      const mergedOptions = { ...this.options, ...options };

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude, accuracy } = position.coords;
          const locationData = {
            latitude,
            longitude,
            accuracy,
            timestamp: new Date(position.timestamp),
          };
          resolve(locationData);
        },
        (error) => {
          reject(new Error(this.getErrorMessage(error)));
        },
        mergedOptions
      );
    });
  }

  // Get error message from error code
  getErrorMessage(error) {
    switch (error.code) {
      case error.PERMISSION_DENIED:
        return "Location access denied";
      case error.POSITION_UNAVAILABLE:
        return "Location unavailable";
      case error.TIMEOUT:
        return "Location request timeout";
      default:
        return "Unknown location error";
    }
  }

  // Calculate distance between two points (Haversine formula)
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;

    return distance; // Distance in kilometers
  }

  // Convert degrees to radians
  toRadians(degrees) {
    return degrees * (Math.PI / 180);
  }

  // Calculate estimated time of arrival
  calculateETA(distance, averageSpeed = 30) {
    // distance in km, speed in km/h
    const timeInHours = distance / averageSpeed;
    const timeInMinutes = Math.round(timeInHours * 60);
    return timeInMinutes;
  }

  // Get address from coordinates (reverse geocoding)
  async getAddressFromCoordinates(latitude, longitude) {
    try {
      // Using a free geocoding service (in production, use a proper service)
      const response = await fetch(
        `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
      );

      if (response.ok) {
        const data = await response.json();
        return {
          success: true,
          address: data.display_name || `${latitude}, ${longitude}`,
          city: data.city || "",
          country: data.countryName || "",
        };
      } else {
        throw new Error("Geocoding service unavailable");
      }
    } catch (error) {
      console.error("Reverse geocoding error:", error);
      return {
        success: false,
        address: `${latitude}, ${longitude}`,
        error: error.message,
      };
    }
  }

  // Add event listener
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remove event listener
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Emit event to listeners
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach((callback) => {
        try {
          callback(data);
        } catch (error) {
          console.error("Error in location event listener:", error);
        }
      });
    }
  }

  // Get tracking status
  getTrackingStatus() {
    return {
      isTracking: this.isTracking,
      lastKnownPosition: this.lastKnownPosition,
      watchId: this.watchId,
      updateInterval: this.updateInterval,
    };
  }

  // Update activity type
  updateActivityType(activityType, orderId = "") {
    if (this.lastKnownPosition && websocketService.isConnected) {
      websocketService.sendLocationUpdate(
        this.lastKnownPosition.latitude,
        this.lastKnownPosition.longitude,
        "",
        activityType
      );
    }
  }
}

// Create singleton instance
const locationService = new LocationService();

export default locationService;
