import sqlite3
import os

def check_admin_user():
    """Check admin user in the database"""
    
    # Find the database file
    db_path = os.path.join('Backend', 'db.sqlite3')
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return
    
    print(f"✅ Database found at {db_path}")
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check admin user
    print("\n🔍 Checking Admin User")
    print("=" * 30)
    
    cursor.execute("""
        SELECT id, user_name, email, role, is_active, is_staff, is_superuser
        FROM users_user
        WHERE user_name = 'admin' OR email LIKE '%admin%' OR role = 'admin'
    """)
    
    admin_users = cursor.fetchall()
    
    if admin_users:
        for user in admin_users:
            id, user_name, email, role, is_active, is_staff, is_superuser = user
            print(f"✅ Admin user found:")
            print(f"   ID: {id}")
            print(f"   Username: {user_name}")
            print(f"   Email: {email}")
            print(f"   Role: {role}")
            print(f"   Active: {is_active}")
            print(f"   Staff: {is_staff}")
            print(f"   Superuser: {is_superuser}")
            print()
    else:
        print("❌ No admin user found")
        
        # Show all users
        print("\n📋 All users:")
        cursor.execute("SELECT id, user_name, email, role FROM users_user LIMIT 10")
        all_users = cursor.fetchall()
        for user in all_users:
            id, user_name, email, role = user
            print(f"   {id}: {user_name} ({email}) - {role}")
    
    conn.close()

if __name__ == "__main__":
    check_admin_user()
