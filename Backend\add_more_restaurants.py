#!/usr/bin/env python
"""
Add more restaurants with complete menus
"""

from users.models import User
from restaurant.models import Restaurant, MenuCategory, MenuItem, Address

print("🏪 Adding more restaurants to complete the system...")

try:
    # Get the restaurant owner
    owner = User.objects.get(user_name='restaurant_owner1')
    print(f"✅ Found owner: {owner.name}")
    
    # Check if we already have enough restaurants
    existing_count = Restaurant.objects.filter(is_active=True).count()
    print(f"📊 Current restaurants: {existing_count}")
    
    if existing_count >= 20:
        print("✅ Already have enough restaurants!")
    else:
        # Restaurant 1: Kabul Spice House
        print("\n🏪 Creating Kabul Spice House...")
        address1 = Address.objects.create(
            user=owner,
            street='789 Spice Market Street',
            city='Kabul',
            state='Kabul Province',
            postal_code='1003',
            country='Afghanistan',
            latitude=34.5555,
            longitude=69.2080
        )
        
        restaurant1 = Restaurant.objects.create(
            owner=owner,
            name='Kabul Spice House',
            description='Authentic Afghan spices and traditional dishes with family recipes',
            address=address1,
            contact_number='+93 70 555 2345',
            opening_time='08:00:00',
            closing_time='23:00:00',
            delivery_fee='3.99',
            min_order_amount='10.00',
            average_preparation_time=25,
            is_active=True,
            is_verified=True,
            rating='4.8'
        )
        
        # Categories for Kabul Spice House
        appetizers1 = MenuCategory.objects.create(
            restaurant=restaurant1,
            name='Appetizers',
            description='Traditional Afghan starters'
        )
        
        mains1 = MenuCategory.objects.create(
            restaurant=restaurant1,
            name='Main Dishes',
            description='Hearty traditional meals'
        )
        
        beverages1 = MenuCategory.objects.create(
            restaurant=restaurant1,
            name='Beverages',
            description='Traditional drinks and teas'
        )
        
        # Menu items for Kabul Spice House
        menu_items1 = [
            # Appetizers
            {'cat': appetizers1, 'name': 'Sambosa', 'desc': 'Crispy pastries filled with spiced meat', 'price': '6.99', 'time': 15, 'veg': False},
            {'cat': appetizers1, 'name': 'Pakora', 'desc': 'Deep-fried vegetable fritters', 'price': '5.99', 'time': 12, 'veg': True},
            
            # Main Dishes
            {'cat': mains1, 'name': 'Qabuli Pulao', 'desc': 'Premium rice with lamb and carrots', 'price': '24.99', 'time': 50, 'veg': False},
            {'cat': mains1, 'name': 'Chapli Kebab', 'desc': 'Spiced ground meat patties', 'price': '19.99', 'time': 35, 'veg': False},
            {'cat': mains1, 'name': 'Sabzi Chalaw', 'desc': 'Spinach stew with white rice', 'price': '16.99', 'time': 30, 'veg': True},
            
            # Beverages
            {'cat': beverages1, 'name': 'Kahwah', 'desc': 'Traditional green tea with cardamom', 'price': '4.99', 'time': 8, 'veg': True},
            {'cat': beverages1, 'name': 'Sheer Chai', 'desc': 'Pink tea with milk and salt', 'price': '3.99', 'time': 10, 'veg': True}
        ]
        
        for item_data in menu_items1:
            item = MenuItem.objects.create(
                category=item_data['cat'],
                name=item_data['name'],
                description=item_data['desc'],
                price=item_data['price'],
                preparation_time=item_data['time'],
                is_vegetarian=item_data['veg'],
                is_available=True
            )
            print(f"✅ Created: {item.name} - ${item.price}")
        
        print(f"✅ Created {restaurant1.name} with {len(menu_items1)} menu items")
        
        # Restaurant 2: Herat Garden
        print("\n🏪 Creating Herat Garden...")
        address2 = Address.objects.create(
            user=owner,
            street='456 Garden Avenue',
            city='Herat',
            state='Herat Province',
            postal_code='2001',
            country='Afghanistan',
            latitude=34.3482,
            longitude=62.1997
        )
        
        restaurant2 = Restaurant.objects.create(
            owner=owner,
            name='Herat Garden Restaurant',
            description='Fresh garden-to-table dining with organic ingredients',
            address=address2,
            contact_number='+93 70 555 3456',
            opening_time='11:00:00',
            closing_time='22:30:00',
            delivery_fee='5.99',
            min_order_amount='15.00',
            average_preparation_time=35,
            is_active=True,
            is_verified=True,
            rating='4.5'
        )
        
        # Categories for Herat Garden
        salads2 = MenuCategory.objects.create(
            restaurant=restaurant2,
            name='Fresh Salads',
            description='Garden-fresh salads and healthy options'
        )
        
        grills2 = MenuCategory.objects.create(
            restaurant=restaurant2,
            name='Grilled Specialties',
            description='Charcoal-grilled meats and vegetables'
        )
        
        # Menu items for Herat Garden
        menu_items2 = [
            # Salads
            {'cat': salads2, 'name': 'Afghan Garden Salad', 'desc': 'Mixed greens with pomegranate', 'price': '9.99', 'time': 10, 'veg': True},
            {'cat': salads2, 'name': 'Cucumber Yogurt Salad', 'desc': 'Fresh cucumbers with mint yogurt', 'price': '7.99', 'time': 8, 'veg': True},
            
            # Grills
            {'cat': grills2, 'name': 'Lamb Tikka', 'desc': 'Marinated lamb chunks grilled to perfection', 'price': '26.99', 'time': 45, 'veg': False},
            {'cat': grills2, 'name': 'Chicken Seekh Kebab', 'desc': 'Spiced ground chicken on skewers', 'price': '18.99', 'time': 30, 'veg': False},
            {'cat': grills2, 'name': 'Grilled Vegetables', 'desc': 'Seasonal vegetables with herbs', 'price': '14.99', 'time': 25, 'veg': True}
        ]
        
        for item_data in menu_items2:
            item = MenuItem.objects.create(
                category=item_data['cat'],
                name=item_data['name'],
                description=item_data['desc'],
                price=item_data['price'],
                preparation_time=item_data['time'],
                is_vegetarian=item_data['veg'],
                is_available=True
            )
            print(f"✅ Created: {item.name} - ${item.price}")
        
        print(f"✅ Created {restaurant2.name} with {len(menu_items2)} menu items")
    
    # Final count
    final_count = Restaurant.objects.filter(is_active=True).count()
    total_categories = MenuCategory.objects.count()
    total_items = MenuItem.objects.count()
    
    print("\n" + "="*60)
    print("🎉 SUCCESS! Restaurant system is complete!")
    print(f"📊 Total restaurants: {final_count}")
    print(f"📂 Total categories: {total_categories}")
    print(f"🍽️ Total menu items: {total_items}")
    print("\n🎯 System is ready for testing:")
    print("   ✅ Browse all restaurants")
    print("   ✅ View detailed menus")
    print("   ✅ Add items to cart")
    print("   ✅ Place orders")
    print("   ✅ Test search and filters")
    print("="*60)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
