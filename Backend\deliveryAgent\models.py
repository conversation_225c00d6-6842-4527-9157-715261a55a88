from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator, RegexValidator
from django.utils import timezone
from decimal import Decimal
import uuid

User = get_user_model()

# Afghan-specific choices
AFGHAN_PROVINCES = [
    ('kabul', 'کابل (Kabul)'),
    ('herat', 'هرات (Herat)'),
    ('kandahar', 'کندهار (Kandahar)'),
    ('balkh', 'بلخ (Balkh)'),
    ('nangarhar', 'ننګرهار (Nangarhar)'),
    ('kunduz', 'کندز (Kunduz)'),
    ('takhar', 'تخار (Takhar)'),
    ('baghlan', 'بغلان (Baghlan)'),
    ('ghazni', 'غزني (Ghazni)'),
    ('paktia', 'پکتیا (Paktia)'),
    ('khost', 'خوست (Khost)'),
    ('laghman', 'لغمان (Laghman)'),
    ('kapisa', 'کاپیسا (Kapisa)'),
    ('parwan', 'پروان (Parwan)'),
    ('wardak', 'وردک (Wardak)'),
    ('logar', 'لوګر (Logar)'),
    ('bamyan', 'بامیان (Bamyan)'),
    ('panjshir', 'پنجشیر (Panjshir)'),
    ('badakhshan', 'بدخشان (Badakhshan)'),
    ('faryab', 'فاریاب (Faryab)'),
    ('jawzjan', 'جوزجان (Jawzjan)'),
    ('sar_e_pol', 'سرپل (Sar-e Pol)'),
    ('samangan', 'سمنګان (Samangan)'),
    ('badghis', 'بادغیس (Badghis)'),
    ('ghor', 'غور (Ghor)'),
    ('daykundi', 'دایکندي (Daykundi)'),
    ('uruzgan', 'ارزګان (Uruzgan)'),
    ('zabul', 'زابل (Zabul)'),
    ('paktika', 'پکتیکا (Paktika)'),
    ('kunar', 'کنړ (Kunar)'),
    ('nuristan', 'نورستان (Nuristan)'),
    ('farah', 'فراه (Farah)'),
    ('nimroz', 'نیمروز (Nimroz)'),
    ('helmand', 'هلمند (Helmand)'),
]

VEHICLE_TYPES = [
    ('motorcycle', 'موټرسایکل (Motorcycle)'),
    ('bicycle', 'بایسکل (Bicycle)'),
    ('car', 'موټر (Car)'),
    ('rickshaw', 'رکشا (Rickshaw)'),
    ('scooter', 'سکوټر (Scooter)'),
]

AGENT_STATUS = [
    ('pending', 'انتظار کې (Pending)'),
    ('approved', 'تصویب شوی (Approved)'),
    ('active', 'فعال (Active)'),
    ('inactive', 'غیر فعال (Inactive)'),
    ('suspended', 'تعلیق شوی (Suspended)'),
    ('rejected', 'رد شوی (Rejected)'),
]

AVAILABILITY_STATUS = [
    ('available', 'شته (Available)'),
    ('busy', 'بوخت (Busy)'),
    ('offline', 'آفلاین (Offline)'),
    ('break', 'وقفه (Break)'),
]

GENDER_CHOICES = [
    ('male', 'نارینه (Male)'),
    ('female', 'ښځینه (Female)'),
]

MARITAL_STATUS = [
    ('single', 'مجرد (Single)'),
    ('married', 'واده شوی (Married)'),
    ('divorced', 'طلاق شوی (Divorced)'),
    ('widowed', 'کونډه (Widowed)'),
]


class DeliveryAgentProfile(models.Model):
    """
    Employee-Based Delivery Agent Profile
    Admin creates and manages delivery agent employees
    """
    # Basic Information
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='delivery_agent_profile')
    agent_id = models.CharField(max_length=20, unique=True, help_text="Employee ID (e.g., DA001, DA002)")
    employee_number = models.CharField(max_length=10, unique=True, null=True, blank=True, help_text="Sequential employee number")

    # Employment Information
    employment_status = models.CharField(
        max_length=20,
        choices=[
            ('active', 'Active Employee'),
            ('inactive', 'Inactive'),
            ('terminated', 'Terminated'),
            ('suspended', 'Suspended'),
        ],
        default='active'
    )
    hire_date = models.DateField(null=True, blank=True, help_text="Date of employment")
    termination_date = models.DateField(null=True, blank=True)

    # Salary and Compensation
    salary_type = models.CharField(
        max_length=20,
        choices=[
            ('monthly', 'Monthly Salary'),
            ('hourly', 'Hourly Rate'),
            ('commission', 'Commission Only'),
            ('hybrid', 'Salary + Commission'),
        ],
        default='monthly'
    )
    base_salary = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('15000.00'), help_text="Base salary in AFN")
    commission_per_delivery = models.DecimalField(max_digits=6, decimal_places=2, default=Decimal('100.00'), help_text="Commission per delivery in AFN")
    hourly_rate = models.DecimalField(max_digits=6, decimal_places=2, default=Decimal('0.00'), help_text="Hourly rate in AFN")

    # Work Schedule
    work_schedule = models.CharField(
        max_length=20,
        choices=[
            ('full_time', 'Full Time (8 hours)'),
            ('part_time', 'Part Time (4-6 hours)'),
            ('flexible', 'Flexible Hours'),
        ],
        default='full_time'
    )
    shift_start_time = models.TimeField(default='08:00', help_text="Shift start time")
    shift_end_time = models.TimeField(default='17:00', help_text="Shift end time")
    working_days = models.CharField(max_length=20, default='monday_to_saturday', help_text="Working days pattern")

    # Employee Status Tracking
    is_clocked_in = models.BooleanField(default=False, help_text="Currently clocked in")
    last_clock_in = models.DateTimeField(null=True, blank=True)
    last_clock_out = models.DateTimeField(null=True, blank=True)
    current_shift_hours = models.DecimalField(max_digits=4, decimal_places=2, default=Decimal('0.00'))

    # Manager/Supervisor
    supervisor = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='supervised_agents',
        limit_choices_to={'role': 'admin'}
    )
    
    # Personal Information (Afghan Context)
    full_name = models.CharField(max_length=200, help_text="نوم (Full Name)")
    father_name = models.CharField(max_length=200, help_text="د پلار نوم (Father's Name)")
    national_id = models.CharField(
        max_length=20, 
        unique=True,
        validators=[RegexValidator(r'^\d{10,15}$', 'د تذکرې شمیره باید ۱۰-۱۵ عدد وي')],
        help_text="د تذکرې شمیره (National ID)"
    )
    date_of_birth = models.DateField(null=True, blank=True, help_text="د زیږیدو نیټه (Date of Birth)")
    gender = models.CharField(max_length=10, choices=GENDER_CHOICES, default='male')
    marital_status = models.CharField(max_length=10, choices=MARITAL_STATUS, default='single')
    
    # Contact Information
    phone_number = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+93\d{9}$', 'د تلیفون شمیره باید +93 څخه پیل شي')],
        help_text="اصلي تلیفون شمیره (Primary Phone)"
    )
    secondary_phone = models.CharField(
        max_length=20, 
        blank=True,
        validators=[RegexValidator(r'^\+93\d{9}$', 'د تلیفون شمیره باید +93 څخه پیل شي')],
        help_text="دویمه تلیفون شمیره (Secondary Phone)"
    )
    email = models.EmailField(blank=True, help_text="بریښنالیک (Email)")
    
    # Address Information
    province = models.CharField(max_length=50, choices=AFGHAN_PROVINCES, help_text="ولایت (Province)")
    district = models.CharField(max_length=100, help_text="ولسوالۍ (District)")
    area = models.CharField(max_length=100, help_text="سیمه (Area)")
    street_address = models.TextField(help_text="د کوڅې پته (Street Address)")
    nearby_landmark = models.CharField(max_length=200, blank=True, help_text="نږدې ځای (Nearby Landmark)")
    
    # Vehicle Information
    vehicle_type = models.CharField(max_length=20, choices=VEHICLE_TYPES)
    vehicle_model = models.CharField(max_length=100, blank=True, help_text="د وسایطو ماډل (Vehicle Model)")
    vehicle_year = models.IntegerField(
        null=True, blank=True,
        validators=[MinValueValidator(1990), MaxValueValidator(2030)],
        help_text="د وسایطو کال (Vehicle Year)"
    )
    license_plate = models.CharField(max_length=20, blank=True, help_text="د پلیټ شمیره (License Plate)")
    vehicle_color = models.CharField(max_length=50, blank=True, help_text="د وسایطو رنګ (Vehicle Color)")
    driving_license = models.CharField(max_length=50, blank=True, help_text="د موټر چلولو جواز (Driving License)")
    
    # Documents (File uploads)
    tazkira_front_image = models.ImageField(upload_to='agent_docs/tazkira/', blank=True, help_text="د تذکرې مخ (Tazkira Front)")
    tazkira_back_image = models.ImageField(upload_to='agent_docs/tazkira/', blank=True, help_text="د تذکرې شا (Tazkira Back)")
    driving_license_image = models.ImageField(upload_to='agent_docs/license/', blank=True, help_text="د موټر چلولو جواز (Driving License)")
    vehicle_registration_image = models.ImageField(upload_to='agent_docs/vehicle/', blank=True, help_text="د وسایطو ثبت (Vehicle Registration)")
    profile_photo = models.ImageField(upload_to='agent_photos/', blank=True, help_text="د پروفایل انځور (Profile Photo)")
    
    # References (Important in Afghan culture)
    reference1_name = models.CharField(max_length=200, blank=True, help_text="د لومړي سپارښتونکي نوم")
    reference1_phone = models.CharField(max_length=20, blank=True, help_text="د لومړي سپارښتونکي تلیفون")
    reference1_relation = models.CharField(max_length=100, blank=True, help_text="اړیکه")
    reference2_name = models.CharField(max_length=200, blank=True, help_text="د دویم سپارښتونکي نوم")
    reference2_phone = models.CharField(max_length=20, blank=True, help_text="د دویم سپارښتونکي تلیفون")
    reference2_relation = models.CharField(max_length=100, blank=True, help_text="اړیکه")
    
    # Emergency Contact
    emergency_contact = models.CharField(max_length=20, blank=True, help_text="د بیړني اړیکې تلیفون")
    emergency_relation = models.CharField(max_length=100, blank=True, help_text="د بیړني اړیکې اړیکه")
    
    # Banking Information
    bank_name = models.CharField(max_length=100, blank=True, help_text="د بانک نوم")
    account_number = models.CharField(max_length=50, blank=True, help_text="د حساب شمیره")
    account_holder_name = models.CharField(max_length=200, blank=True, help_text="د حساب خاوند")
    mobile_wallet = models.CharField(max_length=20, blank=True, help_text="د موبایل والټ شمیره")
    
    # Agent Application Status (controlled by delivery agent)
    status = models.CharField(
        max_length=20,
        choices=AGENT_STATUS,
        default='pending',
        help_text="Application status - controlled by delivery agent"
    )

    # Employee Work Status
    availability = models.CharField(
        max_length=20,
        choices=[
            ('available', 'Available for Orders'),
            ('busy', 'On Delivery'),
            ('break', 'On Break'),
            ('offline', 'Off Duty'),
        ],
        default='offline'
    )
    is_online = models.BooleanField(default=False, help_text="Whether agent is currently online")

    # Employee Verification and Training
    is_verified = models.BooleanField(default=True, help_text="Employee verification status")
    training_completed = models.BooleanField(default=False, help_text="Training completion status")
    training_completion_date = models.DateField(null=True, blank=True)

    # Document Status for Employee Records
    documents_complete = models.BooleanField(default=False, help_text="All required documents submitted")
    background_check_completed = models.BooleanField(default=False, help_text="Background check completed")
    medical_clearance = models.BooleanField(default=False, help_text="Medical clearance obtained")

    # Application Processing Fields
    approval_date = models.DateTimeField(null=True, blank=True, help_text="Date when application was approved")
    rejection_date = models.DateTimeField(null=True, blank=True, help_text="Date when application was rejected")
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_agents',
        help_text="Admin who approved the application"
    )
    rejected_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='rejected_agents',
        help_text="Admin who rejected the application"
    )

    # Employee Notes and Records
    admin_notes = models.TextField(blank=True, help_text="Administrative notes about employee")
    performance_notes = models.TextField(blank=True, help_text="Performance evaluation notes")
    
    # Performance Metrics (Basic)
    total_deliveries = models.PositiveIntegerField(default=0)
    successful_deliveries = models.PositiveIntegerField(default=0)
    total_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    rating = models.DecimalField(
        max_digits=3, 
        decimal_places=2, 
        default=Decimal('0.00'),
        validators=[MinValueValidator(0), MaxValueValidator(5)]
    )
    
    # Location Tracking (Basic)
    current_latitude = models.DecimalField(max_digits=10, decimal_places=8, null=True, blank=True)
    current_longitude = models.DecimalField(max_digits=11, decimal_places=8, null=True, blank=True)
    current_address = models.CharField(max_length=500, blank=True)
    last_location_update = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_active = models.DateTimeField(null=True, blank=True)
    last_seen = models.DateTimeField(auto_now=True, help_text="Last time agent was seen/active")
    
    class Meta:
        db_table = 'delivery_agent_profiles'
        verbose_name = 'Delivery Agent Employee'
        verbose_name_plural = 'Delivery Agent Employees'
        ordering = ['employee_number']

    def __str__(self):
        return f"{self.full_name} ({self.agent_id}) - Employee #{self.employee_number}"

    def save(self, *args, **kwargs):
        # Only generate agent_id if it's not already set and this is for self-registration
        if not self.agent_id and not self.employee_number:
            # This is for self-registration - generate a random agent ID
            import random
            import string

            while True:
                # Generate random agent ID for self-registered agents
                random_part = ''.join(random.choices(string.digits, k=6))
                agent_id = f"DA{random_part}"

                # Check if it already exists
                if not DeliveryAgentProfile.objects.filter(agent_id=agent_id).exists():
                    self.agent_id = agent_id
                    break

        super().save(*args, **kwargs)
    
    @property
    def completion_rate(self):
        """Calculate delivery completion rate"""
        if self.total_deliveries == 0:
            return 0
        return (self.successful_deliveries / self.total_deliveries) * 100
    
    @property
    def age(self):
        """Calculate agent's age"""
        if not self.date_of_birth:
            return None
        from datetime import date
        today = date.today()
        return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))
    
    def update_location(self, latitude, longitude, address=""):
        """Update agent's current location"""
        self.current_latitude = latitude
        self.current_longitude = longitude
        self.current_address = address
        self.last_location_update = timezone.now()
        self.last_active = timezone.now()
        self.save(update_fields=['current_latitude', 'current_longitude', 'current_address', 'last_location_update', 'last_active', 'last_seen'])
    
    def mark_online(self):
        """Mark agent as online and available"""
        self.is_online = True
        self.availability = 'available'
        self.last_active = timezone.now()
        self.save(update_fields=['is_online', 'availability', 'last_active', 'last_seen'])

    def mark_offline(self):
        """Mark agent as offline"""
        self.is_online = False
        self.availability = 'offline'
        self.save(update_fields=['is_online', 'availability', 'last_seen'])

    # Employee-specific methods
    def clock_in(self):
        """Clock in for work shift"""
        from django.utils import timezone
        if not self.is_clocked_in:
            self.is_clocked_in = True
            self.is_online = True
            self.last_clock_in = timezone.now()
            self.availability = 'available'
            self.save(update_fields=['is_clocked_in', 'is_online', 'last_clock_in', 'availability'])
            return True
        return False

    def clock_out(self):
        """Clock out from work shift"""
        from django.utils import timezone
        if self.is_clocked_in:
            self.is_clocked_in = False
            self.is_online = False
            self.last_clock_out = timezone.now()
            self.availability = 'offline'

            # Calculate shift hours
            if self.last_clock_in:
                shift_duration = timezone.now() - self.last_clock_in
                hours = shift_duration.total_seconds() / 3600
                self.current_shift_hours = Decimal(str(round(hours, 2)))

            self.save(update_fields=['is_clocked_in', 'is_online', 'last_clock_out', 'availability', 'current_shift_hours'])
            return True
        return False

    def set_on_break(self):
        """Set agent on break"""
        if self.is_clocked_in and self.availability == 'available':
            self.availability = 'break'
            self.save(update_fields=['availability'])
            return True
        return False

    def return_from_break(self):
        """Return from break"""
        if self.is_clocked_in and self.availability == 'break':
            self.availability = 'available'
            self.save(update_fields=['availability'])
            return True
        return False

    @property
    def is_on_duty(self):
        """Check if employee is currently on duty"""
        return self.is_clocked_in and self.employment_status == 'active'

    @property
    def current_shift_duration(self):
        """Get current shift duration in hours"""
        if self.is_clocked_in and self.last_clock_in:
            from django.utils import timezone
            duration = timezone.now() - self.last_clock_in
            return round(duration.total_seconds() / 3600, 2)
        return 0

    @property
    def monthly_earnings(self):
        """Calculate monthly earnings based on salary type"""
        if self.salary_type == 'monthly':
            return self.base_salary
        elif self.salary_type == 'commission':
            return self.total_deliveries * self.commission_per_delivery
        elif self.salary_type == 'hybrid':
            return self.base_salary + (self.total_deliveries * self.commission_per_delivery)
        return Decimal('0.00')

    @property
    def completion_rate(self):
        """Calculate order completion rate"""
        total = self.total_deliveries
        if total == 0:
            return 0
        return round((self.successful_deliveries / total) * 100, 2)

    @property
    def is_available_for_delivery(self):
        """Check if agent is available for new deliveries"""
        return (self.is_online and
                self.availability == 'available' and
                self.is_verified and
                self.employment_status == 'active')
    
    def approve_application(self, admin_user, notes=""):
        """Approve agent application"""
        self.status = 'approved'
        self.is_verified = True
        self.approval_date = timezone.now()
        self.approved_by = admin_user
        self.admin_notes = notes
        self.user.is_active = True
        self.user.save()
        self.save()
    
    def reject_application(self, admin_user, notes=""):
        """Reject agent application"""
        self.status = 'rejected'
        self.rejection_date = timezone.now()
        self.rejected_by = admin_user
        self.admin_notes = notes
        self.user.is_active = False
        self.user.save()
        self.save()
