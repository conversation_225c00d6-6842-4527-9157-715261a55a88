#!/usr/bin/env python3
"""
Test script for delivery agent status update functionality
"""
import os
import sys
import django
import requests
import json
from datetime import datetime

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from deliveryAgent.models import DeliveryAgentProfile
from orders.models import Order
from restaurant.models import Restaurant
from users.models import User

User = get_user_model()

def create_test_data():
    """Create test data for delivery testing"""
    print("Creating test data...")
    
    # Create a test delivery agent
    try:
        agent_user = User.objects.get(user_name='test_delivery_agent')
        print("Using existing delivery agent")
    except User.DoesNotExist:
        agent_user = User.objects.create_user(
            user_name='test_delivery_agent',
            email='<EMAIL>',
            password='testpass123',
            role='delivery_agent',
            name='Test Agent',
            phone='1234567890'
        )
        print("Created new delivery agent")
    
    # Create or get delivery agent profile
    try:
        agent_profile = DeliveryAgentProfile.objects.get(user=agent_user)
        print("Using existing agent profile")
    except DeliveryAgentProfile.DoesNotExist:
        # Find a unique agent_id
        import random
        agent_id = f'DA{random.randint(100, 999)}'
        while DeliveryAgentProfile.objects.filter(agent_id=agent_id).exists():
            agent_id = f'DA{random.randint(100, 999)}'

        agent_profile = DeliveryAgentProfile.objects.create(
            user=agent_user,
            agent_id=agent_id,
            full_name='Test Agent',
            father_name='Test Father',
            national_id=f'123456789{random.randint(0, 9)}',
            phone_number=f'+9312345678{random.randint(0, 9)}',
            province='kabul',
            district='Test District',
            area='Test Area',
            street_address='Test Street',
            vehicle_type='motorcycle',
            employment_status='active',
            availability='available'
        )
        print("Created new agent profile")
    
    # Create a test customer
    try:
        customer = User.objects.get(user_name='test_customer')
        print("Using existing customer")
    except User.DoesNotExist:
        customer = User.objects.create_user(
            user_name='test_customer',
            email='<EMAIL>',
            password='testpass123',
            role='customer',
            name='Test Customer',
            phone='0987654321'
        )
        print("Created new customer")
    
    # Create a test restaurant
    try:
        restaurant = Restaurant.objects.get(name='Test Restaurant')
        print("Using existing restaurant")
    except Restaurant.DoesNotExist:
        restaurant_user = User.objects.create_user(
            user_name='test_restaurant',
            email='<EMAIL>',
            password='testpass123',
            role='restaurant',
            name='Test Restaurant Owner',
            phone='1122334455'
        )
        restaurant = Restaurant.objects.create(
            owner=restaurant_user,
            name='Test Restaurant',
            description='Test restaurant for delivery testing',
            address='123 Test Street',
            contact_number='1122334455',
            is_active=True
        )
        print("Created new restaurant")
    
    # Create a delivery address
    from restaurant.models import Address
    try:
        delivery_address = Address.objects.get(
            user=customer,
            street='123 Test Street'
        )
        print("Using existing delivery address")
    except Address.DoesNotExist:
        delivery_address = Address.objects.create(
            user=customer,
            street='123 Test Street',
            city='Kabul',
            state='Kabul',
            postal_code='1001',
            country='Afghanistan',
            latitude=34.5553,
            longitude=69.2075
        )
        print("Created new delivery address")

    # Create a test order
    order = Order.objects.create(
        customer=customer,
        restaurant=restaurant,
        delivery_agent=agent_user,
        delivery_address=delivery_address,
        status='assigned',
        total_amount=25.50,
        delivery_fee=3.50,
        tax_amount=2.00,
        payment_method='cash_on_delivery',
        special_instructions='Test delivery order'
    )
    print(f"Created test order #{order.id}")
    
    return {
        'agent_user': agent_user,
        'agent_profile': agent_profile,
        'customer': customer,
        'restaurant': restaurant,
        'order': order
    }

def test_status_transitions(order, agent_user):
    """Test various status transitions"""
    print(f"\nTesting status transitions for order #{order.id}")
    print(f"Initial status: {order.status}")
    
    # Test status transitions
    test_transitions = [
        ('assigned', 'accepted'),
        ('accepted', 'en_route_to_restaurant'),
        ('en_route_to_restaurant', 'arrived_at_restaurant'),
        ('arrived_at_restaurant', 'picked_up'),
        ('picked_up', 'en_route_to_customer'),
        ('en_route_to_customer', 'arrived_at_customer'),
        ('arrived_at_customer', 'delivered'),
        ('delivered', 'cash_collected'),
        ('cash_collected', 'completed')
    ]
    
    for from_status, to_status in test_transitions:
        if order.status == from_status:
            print(f"Testing transition: {from_status} -> {to_status}")
            
            # Simulate API call data
            test_data = {
                'order_id': order.id,
                'status': to_status,
                'notes': f'Test transition from {from_status} to {to_status}'
            }
            
            # Test the status update logic directly
            try:
                from deliveryAgent.views import UpdateOrderStatusView
                from django.test import RequestFactory
                from django.contrib.auth.models import AnonymousUser
                
                factory = RequestFactory()
                request = factory.post('/update-order-status/', test_data, content_type='application/json')
                request.user = agent_user
                request.data = test_data
                
                view = UpdateOrderStatusView()
                response = view.post(request)
                
                if hasattr(response, 'data'):
                    response_data = response.data
                else:
                    response_data = json.loads(response.content.decode())
                
                if response_data.get('status') == 'success':
                    print(f"✅ Successfully updated to {to_status}")
                    # Refresh order from database
                    order.refresh_from_db()
                    print(f"   New status: {order.status}")
                else:
                    print(f"❌ Failed to update to {to_status}: {response_data.get('message')}")
                    break
                    
            except Exception as e:
                print(f"❌ Error testing transition {from_status} -> {to_status}: {str(e)}")
                break
        else:
            print(f"Skipping {from_status} -> {to_status} (current status: {order.status})")

def test_invalid_transitions(order, agent_user):
    """Test invalid status transitions"""
    print(f"\nTesting invalid transitions for order #{order.id}")
    
    # Test invalid transitions
    invalid_transitions = [
        'invalid_status',
        'pending',
        'confirmed'
    ]
    
    for invalid_status in invalid_transitions:
        print(f"Testing invalid transition: {order.status} -> {invalid_status}")
        
        test_data = {
            'order_id': order.id,
            'status': invalid_status,
            'notes': f'Test invalid transition to {invalid_status}'
        }
        
        try:
            from deliveryAgent.views import UpdateOrderStatusView
            from django.test import RequestFactory
            
            factory = RequestFactory()
            request = factory.post('/update-order-status/', test_data, content_type='application/json')
            request.user = agent_user
            request.data = test_data
            
            view = UpdateOrderStatusView()
            response = view.post(request)
            
            if hasattr(response, 'data'):
                response_data = response.data
            else:
                response_data = json.loads(response.content.decode())
            
            if response_data.get('status') == 'error':
                print(f"✅ Correctly rejected invalid transition: {response_data.get('message')}")
            else:
                print(f"❌ Unexpectedly accepted invalid transition")
                
        except Exception as e:
            print(f"❌ Error testing invalid transition: {str(e)}")

def main():
    """Main test function"""
    print("🚀 Starting delivery status update tests...")
    
    # Create test data
    test_data = create_test_data()
    
    # Test valid status transitions
    test_status_transitions(test_data['order'], test_data['agent_user'])
    
    # Test invalid transitions
    test_invalid_transitions(test_data['order'], test_data['agent_user'])
    
    print("\n✅ Delivery status update tests completed!")

if __name__ == '__main__':
    main()
