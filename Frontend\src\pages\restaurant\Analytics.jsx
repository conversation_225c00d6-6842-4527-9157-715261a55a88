import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import {
  TrendingUp,
  DollarSign,
  ShoppingBag,
  Users,
  Star,
  Clock,
  Calendar,
  BarChart3,
  PieChart,
  Target,
  Award,
  ArrowUp,
  ArrowDown,
  RefreshCw,
} from "lucide-react";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";

const Analytics = () => {
  const { restaurantId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("week");
  const [analytics, setAnalytics] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [restaurant, setRestaurant] = useState(null);
  const [error, setError] = useState(null);

  // Mock analytics data - replace with real API calls
  const mockAnalytics = {
    overview: {
      totalRevenue: 15420.5,
      revenueGrowth: 12.5,
      totalOrders: 342,
      ordersGrowth: 8.3,
      avgOrderValue: 45.12,
      avgOrderGrowth: 3.8,
      customerCount: 156,
      customerGrowth: 15.2,
      rating: 4.6,
      ratingGrowth: 0.2,
    },
    revenueData: [
      { date: "Mon", revenue: 1200, orders: 28 },
      { date: "Tue", revenue: 1800, orders: 35 },
      { date: "Wed", revenue: 2200, orders: 42 },
      { date: "Thu", revenue: 1900, orders: 38 },
      { date: "Fri", revenue: 2800, orders: 55 },
      { date: "Sat", revenue: 3200, orders: 68 },
      { date: "Sun", revenue: 2300, orders: 48 },
    ],
    popularItems: [
      { name: "Kabuli Pulao", orders: 45, revenue: 1350 },
      { name: "Mantu", orders: 38, revenue: 1140 },
      { name: "Qorma Chalaw", orders: 32, revenue: 960 },
      { name: "Ashak", orders: 28, revenue: 840 },
      { name: "Kebab Platter", orders: 25, revenue: 750 },
    ],
    ordersByStatus: [
      { name: "Completed", value: 285, color: "#10B981" },
      { name: "Pending", value: 32, color: "#F59E0B" },
      { name: "Cancelled", value: 25, color: "#EF4444" },
    ],
    peakHours: [
      { hour: "11:00", orders: 8 },
      { hour: "12:00", orders: 15 },
      { hour: "13:00", orders: 22 },
      { hour: "14:00", orders: 18 },
      { hour: "18:00", orders: 25 },
      { hour: "19:00", orders: 35 },
      { hour: "20:00", orders: 42 },
      { hour: "21:00", orders: 28 },
    ],
    customerInsights: {
      newCustomers: 45,
      returningCustomers: 111,
      avgOrdersPerCustomer: 2.2,
      customerRetentionRate: 68.5,
    },
  };

  useEffect(() => {
    loadAnalytics();
  }, [restaurantId, timeRange]);

  const loadAnalytics = async () => {
    setLoading(true);
    try {
      // If no restaurantId in URL, get user's first restaurant
      if (!restaurantId) {
        const token = localStorage.getItem("afghanSofraUser");
        if (token) {
          const userData = JSON.parse(token);
          const response = await fetch(
            "http://127.0.0.1:8000/api/restaurant/restaurants/my_restaurants/",
            {
              headers: {
                Authorization: `Bearer ${userData.access_token}`,
                "Content-Type": "application/json",
              },
            }
          );

          if (response.ok) {
            const restaurants = await response.json();
            if (restaurants.length > 0) {
              // Use the first restaurant
              const firstRestaurant = restaurants[0];
              setRestaurant(firstRestaurant);
              // Redirect to URL with restaurant ID for consistency
              navigate(`/restaurant/analytics/${firstRestaurant.id}`, {
                replace: true,
              });
              return;
            } else {
              setError("No restaurants found for this user");
              setLoading(false);
              return;
            }
          } else {
            setError("Failed to load restaurants");
            setLoading(false);
            return;
          }
        } else {
          setError("Authentication required");
          setLoading(false);
          return;
        }
      }

      // Load analytics data for the restaurant
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setAnalytics(mockAnalytics);
    } catch (error) {
      console.error("Error loading analytics:", error);
      setError("Failed to load analytics data");
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAnalytics();
    setRefreshing(false);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatGrowth = (growth) => {
    const isPositive = growth > 0;
    return (
      <span
        className={`flex items-center ${
          isPositive ? "text-green-600" : "text-red-600"
        }`}
      >
        {isPositive ? <ArrowUp size={16} /> : <ArrowDown size={16} />}
        {Math.abs(growth)}%
      </span>
    );
  };

  if (loading) {
    return (
      <div className='p-6 animate-fade-in'>
        <div className='animate-pulse space-y-6'>
          <div className='h-8 bg-gray-200 rounded w-1/4'></div>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6'>
            {[...Array(5)].map((_, i) => (
              <div key={i} className='h-32 bg-gray-200 rounded'></div>
            ))}
          </div>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            <div className='h-80 bg-gray-200 rounded'></div>
            <div className='h-80 bg-gray-200 rounded'></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='p-6 animate-fade-in'>
        <Card className='p-8 text-center'>
          <div className='w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4'>
            <AlertCircle size={32} className='text-red-600' />
          </div>
          <h3 className='text-lg font-semibold mb-2'>
            Error Loading Analytics
          </h3>
          <p className='text-gray-600 mb-4'>{error}</p>
          <Button variant='primary' onClick={loadAnalytics}>
            Try Again
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className='p-6 animate-fade-in'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900 mb-2'>
            Analytics Dashboard
          </h1>
          <p className='text-gray-600'>
            Track your restaurant's performance and insights
          </p>
        </div>
        <div className='flex items-center space-x-4 mt-4 sm:mt-0'>
          {/* Time Range Selector */}
          <div className='flex bg-gray-100 rounded-lg p-1'>
            {["day", "week", "month", "year"].map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  timeRange === range
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </button>
            ))}
          </div>
          <Button
            variant='outline'
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw size={18} className={refreshing ? "animate-spin" : ""} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8'>
        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Total Revenue</p>
              <p className='text-2xl font-bold text-gray-900'>
                {formatCurrency(analytics.overview.totalRevenue)}
              </p>
              <div className='mt-1'>
                {formatGrowth(analytics.overview.revenueGrowth)}
              </div>
            </div>
            <div className='p-3 bg-green-100 rounded-full'>
              <DollarSign size={24} className='text-green-600' />
            </div>
          </div>
        </Card>

        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Total Orders</p>
              <p className='text-2xl font-bold text-gray-900'>
                {analytics.overview.totalOrders}
              </p>
              <div className='mt-1'>
                {formatGrowth(analytics.overview.ordersGrowth)}
              </div>
            </div>
            <div className='p-3 bg-blue-100 rounded-full'>
              <ShoppingBag size={24} className='text-blue-600' />
            </div>
          </div>
        </Card>

        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>
                Avg Order Value
              </p>
              <p className='text-2xl font-bold text-gray-900'>
                {formatCurrency(analytics.overview.avgOrderValue)}
              </p>
              <div className='mt-1'>
                {formatGrowth(analytics.overview.avgOrderGrowth)}
              </div>
            </div>
            <div className='p-3 bg-purple-100 rounded-full'>
              <Target size={24} className='text-purple-600' />
            </div>
          </div>
        </Card>

        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Customers</p>
              <p className='text-2xl font-bold text-gray-900'>
                {analytics.overview.customerCount}
              </p>
              <div className='mt-1'>
                {formatGrowth(analytics.overview.customerGrowth)}
              </div>
            </div>
            <div className='p-3 bg-orange-100 rounded-full'>
              <Users size={24} className='text-orange-600' />
            </div>
          </div>
        </Card>

        <Card className='p-6'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Rating</p>
              <p className='text-2xl font-bold text-gray-900'>
                {analytics.overview.rating}
              </p>
              <div className='mt-1'>
                {formatGrowth(analytics.overview.ratingGrowth)}
              </div>
            </div>
            <div className='p-3 bg-yellow-100 rounded-full'>
              <Star size={24} className='text-yellow-600' />
            </div>
          </div>
        </Card>
      </div>

      {/* Charts Section */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8'>
        {/* Revenue Trend */}
        <Card className='p-6'>
          <h3 className='text-lg font-semibold mb-4'>Revenue Trend</h3>
          <ResponsiveContainer width='100%' height={300}>
            <AreaChart data={analytics.revenueData}>
              <CartesianGrid strokeDasharray='3 3' />
              <XAxis dataKey='date' />
              <YAxis />
              <Tooltip formatter={(value) => formatCurrency(value)} />
              <Area
                type='monotone'
                dataKey='revenue'
                stroke='#10B981'
                fill='#10B981'
                fillOpacity={0.1}
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>

        {/* Orders by Status */}
        <Card className='p-6'>
          <h3 className='text-lg font-semibold mb-4'>Orders by Status</h3>
          <ResponsiveContainer width='100%' height={300}>
            <RechartsPieChart>
              <Pie
                data={analytics.ordersByStatus}
                cx='50%'
                cy='50%'
                outerRadius={100}
                dataKey='value'
              >
                {analytics.ordersByStatus.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </RechartsPieChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Additional Analytics */}
      <div className='grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8'>
        {/* Popular Items */}
        <Card className='p-6'>
          <h3 className='text-lg font-semibold mb-4'>Popular Items</h3>
          <div className='space-y-4'>
            {analytics.popularItems.map((item, index) => (
              <div key={index} className='flex items-center justify-between'>
                <div className='flex items-center'>
                  <div className='w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3'>
                    <span className='text-sm font-medium'>{index + 1}</span>
                  </div>
                  <div>
                    <p className='font-medium'>{item.name}</p>
                    <p className='text-sm text-gray-600'>
                      {item.orders} orders
                    </p>
                  </div>
                </div>
                <div className='text-right'>
                  <p className='font-medium'>{formatCurrency(item.revenue)}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Peak Hours */}
        <Card className='p-6'>
          <h3 className='text-lg font-semibold mb-4'>Peak Hours</h3>
          <ResponsiveContainer width='100%' height={200}>
            <BarChart data={analytics.peakHours}>
              <CartesianGrid strokeDasharray='3 3' />
              <XAxis dataKey='hour' />
              <YAxis />
              <Tooltip />
              <Bar dataKey='orders' fill='#3B82F6' />
            </BarChart>
          </ResponsiveContainer>
        </Card>

        {/* Customer Insights */}
        <Card className='p-6'>
          <h3 className='text-lg font-semibold mb-4'>Customer Insights</h3>
          <div className='space-y-4'>
            <div className='flex justify-between items-center'>
              <span className='text-gray-600'>New Customers</span>
              <span className='font-medium'>
                {analytics.customerInsights.newCustomers}
              </span>
            </div>
            <div className='flex justify-between items-center'>
              <span className='text-gray-600'>Returning Customers</span>
              <span className='font-medium'>
                {analytics.customerInsights.returningCustomers}
              </span>
            </div>
            <div className='flex justify-between items-center'>
              <span className='text-gray-600'>Avg Orders/Customer</span>
              <span className='font-medium'>
                {analytics.customerInsights.avgOrdersPerCustomer}
              </span>
            </div>
            <div className='flex justify-between items-center'>
              <span className='text-gray-600'>Retention Rate</span>
              <span className='font-medium text-green-600'>
                {analytics.customerInsights.customerRetentionRate}%
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Goals and Targets */}
      <Card className='p-6'>
        <h3 className='text-lg font-semibold mb-4'>Performance Goals</h3>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          <div className='text-center'>
            <div className='w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3'>
              <Target size={32} className='text-blue-600' />
            </div>
            <p className='text-sm text-gray-600'>Monthly Revenue Goal</p>
            <p className='text-2xl font-bold'>{formatCurrency(20000)}</p>
            <div className='w-full bg-gray-200 rounded-full h-2 mt-2'>
              <div
                className='bg-blue-600 h-2 rounded-full'
                style={{
                  width: `${(analytics.overview.totalRevenue / 20000) * 100}%`,
                }}
              ></div>
            </div>
            <p className='text-sm text-gray-600 mt-1'>
              {Math.round((analytics.overview.totalRevenue / 20000) * 100)}%
              achieved
            </p>
          </div>

          <div className='text-center'>
            <div className='w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3'>
              <ShoppingBag size={32} className='text-green-600' />
            </div>
            <p className='text-sm text-gray-600'>Monthly Orders Goal</p>
            <p className='text-2xl font-bold'>500</p>
            <div className='w-full bg-gray-200 rounded-full h-2 mt-2'>
              <div
                className='bg-green-600 h-2 rounded-full'
                style={{
                  width: `${(analytics.overview.totalOrders / 500) * 100}%`,
                }}
              ></div>
            </div>
            <p className='text-sm text-gray-600 mt-1'>
              {Math.round((analytics.overview.totalOrders / 500) * 100)}%
              achieved
            </p>
          </div>

          <div className='text-center'>
            <div className='w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3'>
              <Star size={32} className='text-yellow-600' />
            </div>
            <p className='text-sm text-gray-600'>Rating Goal</p>
            <p className='text-2xl font-bold'>4.8</p>
            <div className='w-full bg-gray-200 rounded-full h-2 mt-2'>
              <div
                className='bg-yellow-600 h-2 rounded-full'
                style={{ width: `${(analytics.overview.rating / 4.8) * 100}%` }}
              ></div>
            </div>
            <p className='text-sm text-gray-600 mt-1'>
              {Math.round((analytics.overview.rating / 4.8) * 100)}% achieved
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default Analytics;
