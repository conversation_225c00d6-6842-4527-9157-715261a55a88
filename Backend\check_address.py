#!/usr/bin/env python
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from users.models import User
from restaurant.models import Address

# Find customer
customer = User.objects.filter(role='customer').first()
if customer:
    print(f"Customer found: {customer.user_name} ({customer.email})")
    
    # Check addresses
    addresses = Address.objects.filter(user=customer)
    print(f"Number of addresses: {addresses.count()}")
    
    for addr in addresses:
        print(f"Address ID: {addr.id}")
        print(f"Street: {addr.street}")
        print(f"City: {addr.city}")
        print(f"Postal Code: {addr.postal_code}")
        print("---")

    # If no addresses, create one
    if addresses.count() == 0:
        print("Creating a default address for customer...")
        address = Address.objects.create(
            user=customer,
            street="123 Main Street",
            city="Kabul",
            state="Kabul",
            postal_code="1001",
            country="Afghanistan",
            latitude=34.5553,
            longitude=69.2075
        )
        print(f"Created address with ID: {address.id}")
    else:
        print(f"Customer's first address ID: {addresses.first().id}")
        
else:
    print("No customer found")
