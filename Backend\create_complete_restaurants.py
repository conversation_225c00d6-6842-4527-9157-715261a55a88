#!/usr/bin/env python
"""
Complete restaurant creation script with full menus
Run with: python manage.py shell < create_complete_restaurants.py
"""

from users.models import User
from restaurant.models import Restaurant, MenuCategory, MenuItem, Address
from django.core.files.base import ContentFile
from PIL import Image
import io

def create_food_image(color, name):
    """Create a simple food image"""
    img = Image.new('RGB', (300, 200), color=color)
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG', quality=85)
    img_bytes.seek(0)
    return ContentFile(img_bytes.getvalue(), name=f'{name.lower().replace(" ", "_")}.jpg')

print("🏪 Creating complete restaurant system...")

try:
    # Get the restaurant owner
    owner = User.objects.get(user_name='restaurant_owner1')
    print(f"✅ Found owner: {owner.name}")
    
    # Restaurant 1: Kabul Spice House
    print("\n🏪 Creating Kabul Spice House...")
    address1 = Address.objects.create(
        user=owner,
        street='789 Spice Market Street',
        city='Kabul',
        state='Kabul Province',
        postal_code='1003',
        country='Afghanistan',
        latitude=34.5555,
        longitude=69.2080
    )
    
    restaurant1 = Restaurant.objects.create(
        owner=owner,
        name='Kabul Spice House',
        description='Authentic Afghan spices and traditional dishes with family recipes passed down for generations',
        address=address1,
        contact_number='+93 70 555 2345',
        opening_time='08:00:00',
        closing_time='23:00:00',
        delivery_fee='3.99',
        min_order_amount='10.00',
        average_preparation_time=25,
        is_active=True,
        is_verified=True,
        rating='4.8'
    )
    
    # Categories for Kabul Spice House
    appetizers1 = MenuCategory.objects.create(
        restaurant=restaurant1,
        name='Appetizers',
        description='Traditional Afghan starters'
    )
    
    mains1 = MenuCategory.objects.create(
        restaurant=restaurant1,
        name='Main Dishes',
        description='Hearty traditional meals'
    )
    
    desserts1 = MenuCategory.objects.create(
        restaurant=restaurant1,
        name='Desserts',
        description='Sweet traditional treats'
    )
    
    beverages1 = MenuCategory.objects.create(
        restaurant=restaurant1,
        name='Beverages',
        description='Traditional drinks and teas'
    )
    
    # Menu items for Kabul Spice House
    menu_items1 = [
        # Appetizers
        {'cat': appetizers1, 'name': 'Sambosa', 'desc': 'Crispy pastries filled with spiced meat and vegetables', 'price': '6.99', 'time': 15, 'veg': False},
        {'cat': appetizers1, 'name': 'Pakora', 'desc': 'Deep-fried vegetable fritters with mint chutney', 'price': '5.99', 'time': 12, 'veg': True},
        {'cat': appetizers1, 'name': 'Kachalu', 'desc': 'Spiced potato cakes with yogurt sauce', 'price': '7.99', 'time': 18, 'veg': True},
        
        # Main Dishes
        {'cat': mains1, 'name': 'Qabuli Pulao', 'desc': 'Premium rice with lamb, carrots, and raisins', 'price': '24.99', 'time': 50, 'veg': False},
        {'cat': mains1, 'name': 'Chapli Kebab', 'desc': 'Spiced ground meat patties with fresh herbs', 'price': '19.99', 'time': 35, 'veg': False},
        {'cat': mains1, 'name': 'Sabzi Chalaw', 'desc': 'Spinach stew with white rice', 'price': '16.99', 'time': 30, 'veg': True},
        {'cat': mains1, 'name': 'Kofta Chalaw', 'desc': 'Meatballs in tomato sauce with rice', 'price': '21.99', 'time': 40, 'veg': False},
        
        # Desserts
        {'cat': desserts1, 'name': 'Sheer Khurma', 'desc': 'Sweet vermicelli with milk and nuts', 'price': '8.99', 'time': 10, 'veg': True},
        {'cat': desserts1, 'name': 'Baklava', 'desc': 'Layered pastry with honey and pistachios', 'price': '6.99', 'time': 5, 'veg': True},
        
        # Beverages
        {'cat': beverages1, 'name': 'Kahwah', 'desc': 'Traditional green tea with cardamom and almonds', 'price': '4.99', 'time': 8, 'veg': True},
        {'cat': beverages1, 'name': 'Sheer Chai', 'desc': 'Pink tea with milk and salt', 'price': '3.99', 'time': 10, 'veg': True},
        {'cat': beverages1, 'name': 'Fresh Mint Lemonade', 'desc': 'Refreshing lemonade with fresh mint', 'price': '5.99', 'time': 5, 'veg': True}
    ]
    
    for item_data in menu_items1:
        item = MenuItem.objects.create(
            category=item_data['cat'],
            name=item_data['name'],
            description=item_data['desc'],
            price=item_data['price'],
            preparation_time=item_data['time'],
            is_vegetarian=item_data['veg'],
            is_available=True
        )
        print(f"✅ Created: {item.name} - ${item.price}")
    
    print(f"✅ Created {restaurant1.name} with {len(menu_items1)} menu items")
    
    # Restaurant 2: Herat Garden
    print("\n🏪 Creating Herat Garden...")
    address2 = Address.objects.create(
        user=owner,
        street='456 Garden Avenue',
        city='Herat',
        state='Herat Province',
        postal_code='2001',
        country='Afghanistan',
        latitude=34.3482,
        longitude=62.1997
    )
    
    restaurant2 = Restaurant.objects.create(
        owner=owner,
        name='Herat Garden Restaurant',
        description='Fresh garden-to-table dining with organic ingredients and modern Afghan cuisine',
        address=address2,
        contact_number='+93 70 555 3456',
        opening_time='11:00:00',
        closing_time='22:30:00',
        delivery_fee='5.99',
        min_order_amount='15.00',
        average_preparation_time=35,
        is_active=True,
        is_verified=True,
        rating='4.5'
    )
    
    # Categories for Herat Garden
    salads2 = MenuCategory.objects.create(
        restaurant=restaurant2,
        name='Fresh Salads',
        description='Garden-fresh salads and healthy options'
    )
    
    grills2 = MenuCategory.objects.create(
        restaurant=restaurant2,
        name='Grilled Specialties',
        description='Charcoal-grilled meats and vegetables'
    )
    
    rice2 = MenuCategory.objects.create(
        restaurant=restaurant2,
        name='Rice Dishes',
        description='Aromatic rice preparations'
    )
    
    # Menu items for Herat Garden
    menu_items2 = [
        # Salads
        {'cat': salads2, 'name': 'Afghan Garden Salad', 'desc': 'Mixed greens with pomegranate and walnuts', 'price': '9.99', 'time': 10, 'veg': True},
        {'cat': salads2, 'name': 'Cucumber Yogurt Salad', 'desc': 'Fresh cucumbers with mint yogurt dressing', 'price': '7.99', 'time': 8, 'veg': True},
        
        # Grills
        {'cat': grills2, 'name': 'Lamb Tikka', 'desc': 'Marinated lamb chunks grilled to perfection', 'price': '26.99', 'time': 45, 'veg': False},
        {'cat': grills2, 'name': 'Chicken Seekh Kebab', 'desc': 'Spiced ground chicken on skewers', 'price': '18.99', 'time': 30, 'veg': False},
        {'cat': grills2, 'name': 'Grilled Vegetables', 'desc': 'Seasonal vegetables with herbs', 'price': '14.99', 'time': 25, 'veg': True},
        
        # Rice
        {'cat': rice2, 'name': 'Saffron Rice', 'desc': 'Basmati rice with saffron and almonds', 'price': '12.99', 'time': 25, 'veg': True},
        {'cat': rice2, 'name': 'Chicken Biryani', 'desc': 'Layered rice with spiced chicken', 'price': '22.99', 'time': 40, 'veg': False}
    ]
    
    for item_data in menu_items2:
        item = MenuItem.objects.create(
            category=item_data['cat'],
            name=item_data['name'],
            description=item_data['desc'],
            price=item_data['price'],
            preparation_time=item_data['time'],
            is_vegetarian=item_data['veg'],
            is_available=True
        )
        print(f"✅ Created: {item.name} - ${item.price}")
    
    print(f"✅ Created {restaurant2.name} with {len(menu_items2)} menu items")
    
    print("\n" + "="*60)
    print("🎉 SUCCESS! Complete restaurant system created!")
    print(f"📊 Total restaurants: {Restaurant.objects.filter(is_active=True).count()}")
    print(f"📂 Total categories: {MenuCategory.objects.count()}")
    print(f"🍽️ Total menu items: {MenuItem.objects.count()}")
    print("\n🎯 You can now test the complete system:")
    print("   ✅ Browse all restaurants")
    print("   ✅ View detailed menus")
    print("   ✅ Add items to cart")
    print("   ✅ Place orders")
    print("   ✅ Test search and filters")
    print("="*60)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
