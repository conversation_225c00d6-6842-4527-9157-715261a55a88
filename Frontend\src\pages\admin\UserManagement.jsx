import React, { useState, useEffect } from "react";
import {
  Search,
  UserPlus,
  UserMinus,
  Edit2,
  Shield,
  Mail,
  Phone,
  MapPin,
  Clock,
  AlertCircle,
  Filter,
  Download,
  Upload,
  MoreVertical,
  Eye,
  Trash2,
  Users,
  Calendar,
  Activity,
  TrendingUp,
  X,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { useAuth } from "../../context/AuthContext";

function UserManagement() {
  const { user: currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [selectedUser, setSelectedUser] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showAddUser, setShowAddUser] = useState(false);
  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    phone: "",
    role: "customer",
    password: "",
  });

  const [userStats, setUserStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    newThisMonth: 0,
    byRole: {
      customer: 0,
      restaurant: 0,
      delivery: 0,
      admin: 0,
    },
  });

  useEffect(() => {
    // Simulate API call to fetch users
    setTimeout(() => {
      const mockUsers = [
        {
          id: 1,
          name: "Ahmad Khan",
          email: "<EMAIL>",
          phone: "+93 70 123 4567",
          role: "customer",
          address: "Kabul, District 1",
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30), // 30 days ago
          status: "active",
        },
        {
          id: 2,
          name: "Mohammad Ali",
          email: "<EMAIL>",
          phone: "+93 70 234 5678",
          role: "restaurant",
          address: "Herat, District 3",
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15), // 15 days ago
          status: "active",
        },
        {
          id: 3,
          name: "Fatima Zahra",
          email: "<EMAIL>",
          phone: "+93 70 345 6789",
          role: "delivery",
          address: "Mazar-e-Sharif, District 2",
          createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7), // 7 days ago
          status: "inactive",
        },
      ];

      setUsers(mockUsers);
      setFilteredUsers(mockUsers);
      setLoading(false);
    }, 1000);
  }, []);

  useEffect(() => {
    let filtered = [...users];

    // Filter by role
    if (roleFilter !== "all") {
      filtered = filtered.filter((u) => u.role === roleFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (u) =>
          u.name.toLowerCase().includes(query) ||
          u.email.toLowerCase().includes(query) ||
          u.phone.includes(query)
      );
    }

    setFilteredUsers(filtered);
  }, [users, roleFilter, searchQuery]);

  const handleAddUser = () => {
    // In a real app, this would be an API call
    const userToAdd = {
      id: Date.now(),
      ...newUser,
      createdAt: new Date(),
      status: "active",
    };

    setUsers([...users, userToAdd]);
    setShowAddUser(false);
    setNewUser({
      name: "",
      email: "",
      phone: "",
      role: "customer",
      password: "",
    });
  };

  const handleDeleteUser = (userId) => {
    // In a real app, this would be an API call
    setUsers(users.filter((u) => u.id !== userId));
    setShowDetails(false);
  };

  const handleUpdateUser = (updatedUser) => {
    // In a real app, this would be an API call
    setUsers(users.map((u) => (u.id === updatedUser.id ? updatedUser : u)));
    setShowDetails(false);
  };

  const handleUpdateRole = (userId, newRole) => {
    // In a real app, this would be an API call
    setUsers(users.map((u) => (u.id === userId ? { ...u, role: newRole } : u)));
  };

  const getRoleBadge = (role) => {
    switch (role) {
      case "admin":
        return <Badge className='bg-red-100 text-red-800'>Admin</Badge>;
      case "restaurant":
        return (
          <Badge className='bg-green-100 text-green-800'>Restaurant</Badge>
        );
      case "delivery":
        return <Badge className='bg-blue-100 text-blue-800'>Delivery</Badge>;
      case "customer":
        return <Badge className='bg-gray-100 text-gray-800'>Customer</Badge>;
      default:
        return null;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "active":
        return <Badge className='bg-green-100 text-green-800'>Active</Badge>;
      case "inactive":
        return <Badge className='bg-red-100 text-red-800'>Inactive</Badge>;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500'></div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex justify-between items-center'>
        <h1 className='text-2xl font-bold'>User Management</h1>
        <div className='flex items-center space-x-4'>
          <div className='relative'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={20}
            />
            <input
              type='text'
              placeholder='Search users...'
              className='pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <select
            className='px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
            value={roleFilter}
            onChange={(e) => setRoleFilter(e.target.value)}
          >
            <option value='all'>All Roles</option>
            <option value='admin'>Admin</option>
            <option value='restaurant'>Restaurant</option>
            <option value='delivery'>Delivery</option>
            <option value='customer'>Customer</option>
          </select>
          <Button
            variant='primary'
            icon={<UserPlus size={20} />}
            onClick={() => setShowAddUser(true)}
          >
            Add User
          </Button>
        </div>
      </div>

      {/* User List */}
      <div className='grid gap-4'>
        {filteredUsers.map((user) => (
          <Card key={user.id}>
            <div className='p-4'>
              <div className='flex justify-between items-start'>
                <div>
                  <div className='flex items-center space-x-3'>
                    <h3 className='text-lg font-semibold'>{user.name}</h3>
                    {getRoleBadge(user.role)}
                    {getStatusBadge(user.status)}
                  </div>
                  <div className='mt-2 space-y-1'>
                    <div className='flex items-center text-sm text-gray-600'>
                      <Mail size={16} className='mr-2' />
                      {user.email}
                    </div>
                    <div className='flex items-center text-sm text-gray-600'>
                      <Phone size={16} className='mr-2' />
                      {user.phone}
                    </div>
                    <div className='flex items-center text-sm text-gray-600'>
                      <MapPin size={16} className='mr-2' />
                      {user.address}
                    </div>
                  </div>
                </div>
                <div className='flex items-center space-x-2'>
                  <Button
                    variant='outline'
                    size='small'
                    onClick={() => {
                      setSelectedUser(user);
                      setShowDetails(true);
                    }}
                  >
                    View Details
                  </Button>
                  {currentUser.role === "admin" && (
                    <>
                      <Button
                        variant='danger'
                        size='small'
                        icon={<UserMinus size={16} />}
                        onClick={() => handleDeleteUser(user.id)}
                      >
                        Delete
                      </Button>
                      <select
                        className='px-3 py-1 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                        value={user.role}
                        onChange={(e) =>
                          handleUpdateRole(user.id, e.target.value)
                        }
                      >
                        <option value='customer'>Customer</option>
                        <option value='restaurant'>Restaurant</option>
                        <option value='delivery'>Delivery</option>
                        <option value='admin'>Admin</option>
                      </select>
                    </>
                  )}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Add User Modal */}
      {showAddUser && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg w-full max-w-md'>
            <div className='p-6 border-b'>
              <div className='flex justify-between items-center'>
                <h2 className='text-xl font-semibold'>Add New User</h2>
                <button
                  className='text-gray-400 hover:text-gray-600'
                  onClick={() => setShowAddUser(false)}
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className='p-6'>
              <div className='space-y-4'>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Name
                  </label>
                  <input
                    type='text'
                    className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    value={newUser.name}
                    onChange={(e) =>
                      setNewUser({ ...newUser, name: e.target.value })
                    }
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Email
                  </label>
                  <input
                    type='email'
                    className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    value={newUser.email}
                    onChange={(e) =>
                      setNewUser({ ...newUser, email: e.target.value })
                    }
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Phone
                  </label>
                  <input
                    type='tel'
                    className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    value={newUser.phone}
                    onChange={(e) =>
                      setNewUser({ ...newUser, phone: e.target.value })
                    }
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Role
                  </label>
                  <select
                    className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    value={newUser.role}
                    onChange={(e) =>
                      setNewUser({ ...newUser, role: e.target.value })
                    }
                  >
                    <option value='customer'>Customer</option>
                    <option value='restaurant'>Restaurant</option>
                    <option value='delivery'>Delivery</option>
                    <option value='admin'>Admin</option>
                  </select>
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Password
                  </label>
                  <input
                    type='password'
                    className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    value={newUser.password}
                    onChange={(e) =>
                      setNewUser({ ...newUser, password: e.target.value })
                    }
                  />
                </div>
              </div>

              <div className='mt-6 flex justify-end space-x-3'>
                <Button variant='outline' onClick={() => setShowAddUser(false)}>
                  Cancel
                </Button>
                <Button variant='primary' onClick={handleAddUser}>
                  Add User
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* User Details Modal */}
      {showDetails && selectedUser && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg w-full max-w-2xl'>
            <div className='p-6 border-b'>
              <div className='flex justify-between items-start'>
                <div>
                  <h2 className='text-xl font-semibold'>{selectedUser.name}</h2>
                  <div className='flex items-center mt-1'>
                    <Clock size={16} className='text-gray-500 mr-1' />
                    <span className='text-sm text-gray-500'>
                      Joined {new Date(selectedUser.createdAt).toLocaleString()}
                    </span>
                  </div>
                </div>
                <button
                  className='text-gray-400 hover:text-gray-600'
                  onClick={() => setShowDetails(false)}
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className='p-6'>
              <div className='space-y-4'>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Email
                  </label>
                  <input
                    type='email'
                    className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    value={selectedUser.email}
                    onChange={(e) =>
                      setSelectedUser({
                        ...selectedUser,
                        email: e.target.value,
                      })
                    }
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Phone
                  </label>
                  <input
                    type='tel'
                    className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    value={selectedUser.phone}
                    onChange={(e) =>
                      setSelectedUser({
                        ...selectedUser,
                        phone: e.target.value,
                      })
                    }
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Address
                  </label>
                  <input
                    type='text'
                    className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    value={selectedUser.address}
                    onChange={(e) =>
                      setSelectedUser({
                        ...selectedUser,
                        address: e.target.value,
                      })
                    }
                  />
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Role
                  </label>
                  <select
                    className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    value={selectedUser.role}
                    onChange={(e) =>
                      setSelectedUser({ ...selectedUser, role: e.target.value })
                    }
                  >
                    <option value='customer'>Customer</option>
                    <option value='restaurant'>Restaurant</option>
                    <option value='delivery'>Delivery</option>
                    <option value='admin'>Admin</option>
                  </select>
                </div>
                <div>
                  <label className='block text-sm font-medium text-gray-700'>
                    Status
                  </label>
                  <select
                    className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                    value={selectedUser.status}
                    onChange={(e) =>
                      setSelectedUser({
                        ...selectedUser,
                        status: e.target.value,
                      })
                    }
                  >
                    <option value='active'>Active</option>
                    <option value='inactive'>Inactive</option>
                  </select>
                </div>
              </div>

              <div className='mt-6 flex justify-end space-x-3'>
                <Button
                  variant='danger'
                  onClick={() => handleDeleteUser(selectedUser.id)}
                >
                  Delete User
                </Button>
                <Button
                  variant='primary'
                  onClick={() => handleUpdateUser(selectedUser)}
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default UserManagement;
