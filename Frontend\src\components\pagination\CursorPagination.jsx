import React, { useState, useCallback, useEffect } from 'react';
import { ChevronLeft, ChevronRight, RefreshCw, Clock } from 'lucide-react';
import Button from '../common/Button';

const CursorPagination = ({
  onPageChange,
  loading = false,
  hasNext = false,
  hasPrevious = false,
  nextCursor = null,
  previousCursor = null,
  pageSize = 20,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showPageSizeSelector = false,
  showRefresh = true,
  onRefresh,
  className = '',
  realTimeUpdates = false,
  lastUpdated = null,
}) => {
  const [cursors, setCursors] = useState([]);
  const [currentCursorIndex, setCurrentCursorIndex] = useState(-1);

  // Handle next page
  const handleNext = useCallback(() => {
    if (hasNext && nextCursor && onPageChange) {
      // Add current cursor to history if not already there
      const newCursors = [...cursors];
      if (currentCursorIndex === cursors.length - 1) {
        newCursors.push(nextCursor);
        setCursors(newCursors);
        setCurrentCursorIndex(newCursors.length - 1);
      } else {
        setCurrentCursorIndex(currentCursorIndex + 1);
      }
      
      onPageChange({ cursor: nextCursor, direction: 'next' });
    }
  }, [hasNext, nextCursor, onPageChange, cursors, currentCursorIndex]);

  // Handle previous page
  const handlePrevious = useCallback(() => {
    if (hasPrevious && currentCursorIndex > 0) {
      const prevCursor = cursors[currentCursorIndex - 1];
      setCurrentCursorIndex(currentCursorIndex - 1);
      onPageChange({ cursor: prevCursor, direction: 'previous' });
    } else if (hasPrevious && currentCursorIndex === 0) {
      // Go to first page (no cursor)
      setCurrentCursorIndex(-1);
      onPageChange({ cursor: null, direction: 'previous' });
    }
  }, [hasPrevious, currentCursorIndex, cursors, onPageChange]);

  // Handle page size change
  const handlePageSizeChange = useCallback((newPageSize) => {
    if (onPageSizeChange) {
      // Reset cursor history when page size changes
      setCursors([]);
      setCurrentCursorIndex(-1);
      onPageSizeChange(newPageSize);
    }
  }, [onPageSizeChange]);

  // Handle refresh
  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      // Reset to first page
      setCursors([]);
      setCurrentCursorIndex(-1);
      onRefresh();
    }
  }, [onRefresh]);

  // Auto-refresh for real-time updates
  useEffect(() => {
    if (realTimeUpdates && !loading) {
      const interval = setInterval(() => {
        if (currentCursorIndex === -1) {
          // Only auto-refresh if on first page
          handleRefresh();
        }
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(interval);
    }
  }, [realTimeUpdates, loading, currentCursorIndex, handleRefresh]);

  // Format last updated time
  const formatLastUpdated = useCallback((timestamp) => {
    if (!timestamp) return '';
    
    const now = new Date();
    const updated = new Date(timestamp);
    const diffMs = now - updated;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return updated.toLocaleDateString();
  }, []);

  return (
    <div className={`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 ${className}`}>
      {/* Navigation Info */}
      <div className="flex items-center space-x-4">
        {/* Page Position Indicator */}
        <div className="text-sm text-gray-600">
          {currentCursorIndex === -1 ? 'First page' : `Page ${currentCursorIndex + 2}`}
          {pageSize && (
            <span className="ml-2">• {pageSize} per page</span>
          )}
        </div>

        {/* Real-time indicator */}
        {realTimeUpdates && (
          <div className="flex items-center text-xs text-gray-500">
            <Clock size={12} className="mr-1" />
            <span>Live updates</span>
          </div>
        )}

        {/* Last updated */}
        {lastUpdated && (
          <div className="text-xs text-gray-500">
            Updated {formatLastUpdated(lastUpdated)}
          </div>
        )}
      </div>

      {/* Navigation Controls */}
      <div className="flex items-center space-x-2">
        {/* Refresh Button */}
        {showRefresh && onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading}
            className="flex items-center"
            title="Refresh data"
          >
            <RefreshCw size={14} className={loading ? 'animate-spin' : ''} />
          </Button>
        )}

        {/* Previous Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handlePrevious}
          disabled={!hasPrevious || loading}
          className="flex items-center"
          title="Previous page"
        >
          <ChevronLeft size={14} />
          <span className="ml-1 hidden sm:inline">Previous</span>
        </Button>

        {/* Next Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleNext}
          disabled={!hasNext || loading}
          className="flex items-center"
          title="Next page"
        >
          <span className="mr-1 hidden sm:inline">Next</span>
          <ChevronRight size={14} />
        </Button>
      </div>

      {/* Page Size Selector */}
      {showPageSizeSelector && onPageSizeChange && (
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-gray-600">Show:</span>
          <select
            value={pageSize}
            onChange={(e) => handlePageSizeChange(Number(e.target.value))}
            className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
            disabled={loading}
          >
            {pageSizeOptions.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
          <span className="text-gray-600">per page</span>
        </div>
      )}
    </div>
  );
};

// Hook for cursor-based pagination
export const useCursorPagination = ({
  fetchFunction,
  pageSize = 20,
  realTimeUpdates = false,
}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasNext, setHasNext] = useState(false);
  const [hasPrevious, setHasPrevious] = useState(false);
  const [nextCursor, setNextCursor] = useState(null);
  const [previousCursor, setPreviousCursor] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  const fetchData = useCallback(async (params = {}) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetchFunction({
        page_size: pageSize,
        cursor: params.cursor,
        ...params,
      });

      if (response.success) {
        setData(response.data.results || response.data);
        setHasNext(response.data.has_next || false);
        setHasPrevious(response.data.has_previous || false);
        setNextCursor(response.data.next_cursor || null);
        setPreviousCursor(response.data.previous_cursor || null);
        setLastUpdated(new Date().toISOString());
      } else {
        throw new Error(response.error || 'Failed to fetch data');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [fetchFunction, pageSize]);

  const handlePageChange = useCallback((params) => {
    fetchData(params);
  }, [fetchData]);

  const handlePageSizeChange = useCallback((newPageSize) => {
    fetchData({ page_size: newPageSize });
  }, [fetchData]);

  const handleRefresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  // Initial load
  useEffect(() => {
    fetchData();
  }, []);

  return {
    data,
    loading,
    error,
    hasNext,
    hasPrevious,
    nextCursor,
    previousCursor,
    lastUpdated,
    onPageChange: handlePageChange,
    onPageSizeChange: handlePageSizeChange,
    onRefresh: handleRefresh,
    refetch: handleRefresh,
  };
};

export default CursorPagination;
