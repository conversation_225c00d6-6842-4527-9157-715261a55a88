import React from 'react';
import { CreditCard, DollarSign, Shield, AlertCircle, Building } from 'lucide-react';

const BankDetailsStep = ({ data, onChange, errors }) => {
  const accountTypes = [
    { value: 'checking', label: 'Checking Account' },
    { value: 'savings', label: 'Savings Account' },
  ];

  return (
    <div className="space-y-6">
      {/* Payment Information Header */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-start">
          <DollarSign className="h-6 w-6 text-green-600 mt-1 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-green-800 mb-2">
              Payment Information
            </h3>
            <p className="text-green-700">
              Provide your bank account details to receive your delivery earnings. 
              All information is encrypted and securely stored.
            </p>
          </div>
        </div>
      </div>

      {/* Bank Account Information */}
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Building className="inline h-4 w-4 mr-1" />
            Bank Name *
          </label>
          <input
            type="text"
            value={data.bankName}
            onChange={(e) => onChange('bankName', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.bankName ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Enter your bank name"
          />
          {errors.bankName && (
            <p className="mt-1 text-sm text-red-600">{errors.bankName}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Account Holder Name *
          </label>
          <input
            type="text"
            value={data.accountHolderName}
            onChange={(e) => onChange('accountHolderName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Full name as it appears on your account"
          />
          <p className="mt-1 text-xs text-gray-500">
            Must match the name on your bank account exactly
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Account Number *
            </label>
            <input
              type="text"
              value={data.accountNumber}
              onChange={(e) => onChange('accountNumber', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.accountNumber ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter account number"
            />
            {errors.accountNumber && (
              <p className="mt-1 text-sm text-red-600">{errors.accountNumber}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Routing Number
            </label>
            <input
              type="text"
              value={data.routingNumber}
              onChange={(e) => onChange('routingNumber', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="9-digit routing number"
              maxLength={9}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Account Type
          </label>
          <div className="grid grid-cols-2 gap-4">
            {accountTypes.map((type) => (
              <button
                key={type.value}
                type="button"
                onClick={() => onChange('accountType', type.value)}
                className={`p-4 border-2 rounded-lg text-center transition-colors ${
                  data.accountType === type.value
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-300 hover:border-gray-400'
                }`}
              >
                <CreditCard className="h-6 w-6 mx-auto mb-2" />
                <div className="text-sm font-medium">{type.label}</div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Payment Schedule Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-800 mb-3">
          Payment Schedule
        </h3>
        <div className="space-y-3">
          <div className="flex items-start">
            <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
              <span className="text-xs font-medium text-blue-600">1</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-800">Daily Earnings</p>
              <p className="text-sm text-blue-700">
                Your daily earnings are calculated and available for withdrawal
              </p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
              <span className="text-xs font-medium text-blue-600">2</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-800">Instant Payout</p>
              <p className="text-sm text-blue-700">
                Request instant payout anytime (small fee may apply)
              </p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5">
              <span className="text-xs font-medium text-blue-600">3</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-blue-800">Weekly Auto-Payout</p>
              <p className="text-sm text-blue-700">
                Automatic weekly transfers every Monday (free)
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Security Notice */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <div className="flex items-start">
          <Shield className="h-6 w-6 text-gray-600 mt-1 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Security & Privacy
            </h3>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• All bank information is encrypted using industry-standard security</li>
              <li>• We never store your complete account details on our servers</li>
              <li>• Your banking information is only used for payment processing</li>
              <li>• You can update your payment information anytime in your profile</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Tax Information Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
          <div>
            <h3 className="text-sm font-medium text-yellow-800">Tax Information</h3>
            <p className="mt-1 text-sm text-yellow-700">
              You will receive tax documents (1099) at the end of the year for your earnings. 
              Please consult with a tax professional regarding your tax obligations as an 
              independent contractor.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BankDetailsStep;
