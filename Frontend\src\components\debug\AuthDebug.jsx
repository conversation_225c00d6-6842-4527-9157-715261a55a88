import React from "react";
import { useAuth } from "../../context/AuthContext";

const AuthDebug = () => {
  const { user, loading, isAuthenticated } = useAuth();

  const storedUser = localStorage.getItem("afghanSofraUser");

  return (
    <div
      style={{
        position: "fixed",
        top: "10px",
        right: "10px",
        background: "#f0f0f0",
        border: "1px solid #ccc",
        padding: "10px",
        borderRadius: "5px",
        fontSize: "12px",
        maxWidth: "300px",
        zIndex: 9999,
      }}
    >
      <h4>Auth Debug Info</h4>
      <div>
        <strong>Loading:</strong> {loading ? "true" : "false"}
      </div>
      <div>
        <strong>Is Authenticated:</strong> {isAuthenticated ? "true" : "false"}
      </div>
      <div>
        <strong>User Object:</strong> {user ? "exists" : "null"}
      </div>
      <div>
        <strong>User Role:</strong> {user?.role || "none"}
      </div>
      <div>
        <strong>User ID:</strong> {user?.id || "none"}
      </div>
      <div>
        <strong>User Email:</strong> {user?.email || "none"}
      </div>
      <div>
        <strong>LocalStorage:</strong> {storedUser ? "exists" : "empty"}
      </div>

      <details style={{ marginTop: "10px" }}>
        <summary>Raw Data</summary>
        <pre style={{ fontSize: "10px", overflow: "auto", maxHeight: "100px" }}>
          User: {JSON.stringify(user, null, 2)}
        </pre>
        <pre style={{ fontSize: "10px", overflow: "auto", maxHeight: "100px" }}>
          LocalStorage: {storedUser}
        </pre>
      </details>

      <button
        onClick={() => {
          localStorage.removeItem("afghanSofraUser");
          window.location.reload();
        }}
        style={{ marginTop: "5px", fontSize: "10px" }}
      >
        Clear Auth & Reload
      </button>
    </div>
  );
};

export default AuthDebug;
