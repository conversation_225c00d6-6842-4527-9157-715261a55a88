import React, { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';
import { Printer, Download } from 'lucide-react';
import Button from '../common/Button';
import { mockUsers } from '../../data/users';

const OrderReceipt = ({ order, restaurant, onClose }) => {
  const receiptRef = useRef();

  const handlePrint = useReactToPrint({
    content: () => receiptRef.current,
    documentTitle: `Order_${order.id}`,
  });

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getCustomerName = (customerId) => {
    const customer = mockUsers.find((u) => u.id === customerId);
    return customer ? customer.name : 'Unknown Customer';
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl animate-fade-in">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Order Receipt</h2>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="small"
                icon={<Printer size={16} />}
                onClick={handlePrint}
              >
                Print
              </Button>
              <Button
                variant="outline"
                size="small"
                icon={<Download size={16} />}
                onClick={handlePrint}
              >
                Save PDF
              </Button>
              <Button variant="primary" size="small" onClick={onClose}>
                Close
              </Button>
            </div>
          </div>

          <div ref={receiptRef} className="p-6 bg-white">
            {/* Receipt Content */}
            <div className="text-center mb-6">
              <h1 className="text-xl font-bold">{restaurant.name}</h1>
              <p className="text-sm text-gray-600">{restaurant.address}</p>
              <p className="text-sm text-gray-600">{restaurant.phone}</p>
            </div>

            <div className="border-t border-b border-gray-200 py-4 mb-4">
              <div className="flex justify-between mb-2">
                <span className="font-medium">Order #:</span>
                <span>{order.id.split('-')[1]}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="font-medium">Date:</span>
                <span>{formatDate(order.createdAt)}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="font-medium">Time:</span>
                <span>{formatTime(order.createdAt)}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Customer:</span>
                <span>{getCustomerName(order.customerId)}</span>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="font-medium mb-2">Order Items:</h3>
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-2">Item</th>
                    <th className="text-center py-2">Qty</th>
                    <th className="text-right py-2">Price</th>
                    <th className="text-right py-2">Total</th>
                  </tr>
                </thead>
                <tbody>
                  {order.orderItems.map((item) => (
                    <tr key={item.id} className="border-b border-gray-200">
                      <td className="py-2">{item.name}</td>
                      <td className="text-center py-2">{item.quantity}</td>
                      <td className="text-right py-2">${item.price.toFixed(2)}</td>
                      <td className="text-right py-2">
                        ${(item.price * item.quantity).toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="border-t border-gray-200 pt-4">
              <div className="flex justify-between mb-2">
                <span className="font-medium">Subtotal:</span>
                <span>${(order.totalAmount - order.deliveryFee).toFixed(2)}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span className="font-medium">Delivery Fee:</span>
                <span>${order.deliveryFee.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-bold text-lg">
                <span>Total:</span>
                <span>${order.totalAmount.toFixed(2)}</span>
              </div>
            </div>

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">Payment Method: {order.paymentMethod === 'cashOnDelivery' ? 'Cash on Delivery' : order.paymentMethod}</p>
              <p className="text-sm text-gray-600 mt-4">Thank you for your order!</p>
              <p className="text-xs text-gray-500 mt-2">Afghan Sofra Food Delivery</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderReceipt;
