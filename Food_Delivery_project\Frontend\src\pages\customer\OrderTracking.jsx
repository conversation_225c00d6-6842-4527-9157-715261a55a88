import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import {
  ArrowLeft,
  Phone,
  MessageCircle,
  UserCheck,
  Clock,
  CheckCircle,
  ShoppingBag,
  Package,
  TruckIcon,
  Home,
  CalendarClock,
  MapPin,
  CreditCard,
  AlertCircle,
} from "lucide-react";
import { orderApi } from "../../utils/orderApi";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import DeliveryTracker from "../../components/delivery/DeliveryTracker";
import RealTimeOrderStatus from "../../components/common/RealTimeOrderStatus";
import LiveTrackingPanel from "../../components/delivery/LiveTrackingPanel";
import { useNotifications } from "../../context/NotificationContext";
import RatingModal from "../../components/rating/RatingModal";

const OrderTracking = () => {
  const { id } = useParams();
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const { startOrderTracking } = useNotifications();

  // Rating modal state
  const [showRatingModal, setShowRatingModal] = useState(false);

  useEffect(() => {
    // Log the order ID for debugging
    console.log("OrderTracking - Looking for order with ID:", id);

    if (!id) {
      console.error("Invalid order ID received");
      setLoading(false);
      return;
    }

    // Fetch order from API
    fetchOrderDetails();

    // Set up real-time polling for order updates
    const interval = setInterval(fetchOrderDetails, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, [id]);

  const fetchOrderDetails = async () => {
    try {
      const response = await orderApi.getOrder(id);
      const foundOrder = response.data;

      if (foundOrder) {
        console.log("Found order:", foundOrder);
        setOrder(foundOrder);

        // Start real-time tracking notifications
        if (startOrderTracking) {
          startOrderTracking(foundOrder.id, foundOrder.status);
        }
      }
      setLoading(false);
    } catch (error) {
      console.error("Error fetching order:", error);
      setLoading(false);
    }
  };

  const getOrderStatus = (status) => {
    return (
      orderStatuses.find((s) => s.value === status) || {
        label: "Unknown",
        color: "bg-gray-500",
      }
    );
  };

  const getStepStatus = (step, orderStatus) => {
    const statusOrder = [
      "pending",
      "confirmed",
      "preparing",
      "readyForPickup",
      "outForDelivery",
      "delivered",
    ];

    // Default to 'confirmed' if orderStatus is invalid or undefined
    const safeOrderStatus =
      orderStatus && statusOrder.includes(orderStatus)
        ? orderStatus
        : "confirmed";

    const orderIndex = statusOrder.indexOf(safeOrderStatus);
    const stepIndex = statusOrder.indexOf(step);

    // Handle invalid step
    if (stepIndex === -1) return "incomplete";

    if (stepIndex < orderIndex) return "complete";
    if (stepIndex === orderIndex) return "current";
    return "incomplete";
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='border-primary-500 border-t-2 border-b-2 rounded-full w-12 h-12 animate-spin'></div>
      </div>
    );
  }

  // Safety check - if order is null or undefined, show error message
  if (!order) {
    return (
      <div className='mx-auto px-4 py-8 max-w-4xl animate-fade-in container'>
        <div className='flex items-center mb-8'>
          <Link
            to='/orders'
            className='mr-3 text-text-primary hover:text-primary-500'
          >
            <ArrowLeft size={20} />
          </Link>
          <h1 className='font-poppins font-semibold text-2xl'>
            Order Tracking
          </h1>
        </div>

        <Card>
          <div className='py-8 text-center'>
            <div className='flex justify-center items-center bg-red-100 mx-auto mb-4 rounded-full w-16 h-16'>
              <AlertCircle size={32} className='text-red-500' />
            </div>
            <h2 className='mb-2 font-semibold text-xl'>Order Not Found</h2>
            <p className='mb-6 text-gray-600'>
              We couldn't find the order you're looking for.
            </p>
            <Button variant='primary' to='/restaurants'>
              Browse Restaurants
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  try {
    const orderStatus = getOrderStatus(order.status);
    const orderTime = new Date(order.createdAt);
    const estimatedTime = order.estimatedDeliveryTime
      ? new Date(order.estimatedDeliveryTime)
      : null;

    return (
      <div className='mx-auto px-4 py-8 max-w-4xl animate-fade-in container'>
        <div className='flex items-center mb-8'>
          <Link
            to='/orders'
            className='mr-3 text-text-primary hover:text-primary-500'
          >
            <ArrowLeft size={20} />
          </Link>
          <h1 className='font-poppins font-semibold text-2xl'>
            Order Tracking
          </h1>
        </div>

        <div className='gap-8 grid grid-cols-1 lg:grid-cols-3'>
          {/* Order Status */}
          <div className='lg:col-span-2'>
            {/* Real-time Order Status */}
            <RealTimeOrderStatus orderId={order.id} className='mb-6' />

            <Card className='mb-6'>
              <div className='flex justify-between items-center mb-6'>
                <div>
                  <h3 className='font-medium text-text-secondary'>
                    Order #
                    {order.id && order.id.includes("-")
                      ? order.id.split("-")[1]
                      : order.id}
                  </h3>
                  <h2 className='font-semibold text-xl'>
                    {order.restaurantName}
                  </h2>
                </div>
                <Badge
                  className={orderStatus.color + " text-white"}
                  size='medium'
                  rounded='full'
                >
                  {orderStatus.label}
                </Badge>
              </div>

              <div className='mb-8'>
                <div className='flex items-center mb-4'>
                  <CalendarClock
                    size={20}
                    className='mr-2 text-text-secondary'
                  />
                  <div>
                    <p className='text-text-secondary text-sm'>
                      Order placed on
                    </p>
                    <p className='font-medium'>
                      {orderTime.toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </p>
                  </div>
                </div>

                {estimatedTime && (
                  <div className='flex items-center'>
                    <Clock size={20} className='mr-2 text-text-secondary' />
                    <div>
                      <p className='text-text-secondary text-sm'>
                        Estimated delivery by
                      </p>
                      <p className='font-medium'>
                        {estimatedTime.toLocaleTimeString("en-US", {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Order Timeline */}
              <div className='mb-6'>
                <h3 className='mb-4 font-semibold'>Order Status</h3>

                <div className='space-y-6'>
                  <div className='flex'>
                    <div className='flex flex-col items-center mr-4'>
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          getStepStatus("confirmed", order.status) ===
                          "complete"
                            ? "bg-accent-green text-white"
                            : getStepStatus("confirmed", order.status) ===
                              "current"
                            ? "bg-primary-500 text-white"
                            : "bg-gray-200 text-gray-500"
                        }`}
                      >
                        {getStepStatus("confirmed", order.status) ===
                        "complete" ? (
                          <CheckCircle size={16} />
                        ) : (
                          <ShoppingBag size={16} />
                        )}
                      </div>
                      <div className='bg-gray-200 my-1 w-0.5 h-10'></div>
                    </div>
                    <div>
                      <h4 className='font-medium'>Order Confirmed</h4>
                      <p className='text-text-secondary text-sm'>
                        Your order has been received by the restaurant.
                      </p>
                    </div>
                  </div>

                  <div className='flex'>
                    <div className='flex flex-col items-center mr-4'>
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          getStepStatus("preparing", order.status) ===
                          "complete"
                            ? "bg-accent-green text-white"
                            : getStepStatus("preparing", order.status) ===
                              "current"
                            ? "bg-primary-500 text-white"
                            : "bg-gray-200 text-gray-500"
                        }`}
                      >
                        {getStepStatus("preparing", order.status) ===
                        "complete" ? (
                          <CheckCircle size={16} />
                        ) : (
                          <Package size={16} />
                        )}
                      </div>
                      <div className='bg-gray-200 my-1 w-0.5 h-10'></div>
                    </div>
                    <div>
                      <h4 className='font-medium'>Preparing Your Food</h4>
                      <p className='text-text-secondary text-sm'>
                        The restaurant is preparing your food.
                      </p>
                    </div>
                  </div>

                  <div className='flex'>
                    <div className='flex flex-col items-center mr-4'>
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          getStepStatus("outForDelivery", order.status) ===
                          "complete"
                            ? "bg-accent-green text-white"
                            : getStepStatus("outForDelivery", order.status) ===
                              "current"
                            ? "bg-primary-500 text-white"
                            : "bg-gray-200 text-gray-500"
                        }`}
                      >
                        {getStepStatus("outForDelivery", order.status) ===
                        "complete" ? (
                          <CheckCircle size={16} />
                        ) : (
                          <TruckIcon size={16} />
                        )}
                      </div>
                      <div className='bg-gray-200 my-1 w-0.5 h-10'></div>
                    </div>
                    <div>
                      <h4 className='font-medium'>Out for Delivery</h4>
                      <p className='text-text-secondary text-sm'>
                        Your order is on its way to you.
                      </p>
                    </div>
                  </div>

                  <div className='flex'>
                    <div className='flex flex-col items-center mr-4'>
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          getStepStatus("delivered", order.status) ===
                          "complete"
                            ? "bg-accent-green text-white"
                            : getStepStatus("delivered", order.status) ===
                              "current"
                            ? "bg-primary-500 text-white"
                            : "bg-gray-200 text-gray-500"
                        }`}
                      >
                        {getStepStatus("delivered", order.status) ===
                        "complete" ? (
                          <CheckCircle size={16} />
                        ) : (
                          <Home size={16} />
                        )}
                      </div>
                    </div>
                    <div>
                      <h4 className='font-medium'>Delivered</h4>
                      <p className='text-text-secondary text-sm'>
                        Your order has been delivered. Enjoy!
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Live Tracking - Show when out for delivery */}
              {order.status === "outForDelivery" && (
                <div className='pt-6 border-gray-100 border-t'>
                  <h3 className='mb-4 font-semibold'>Live Tracking</h3>

                  {/* Import and use the DeliveryTracker component */}
                  <div className='mb-6'>
                    <div className='bg-gray-50 p-4 rounded-lg text-center'>
                      <p className='mb-2 text-gray-600 text-sm'>
                        Real-time tracking is available for this order.
                      </p>
                      <Button
                        variant='primary'
                        onClick={() => {
                          // Scroll to the map section
                          document
                            .getElementById("delivery-map-section")
                            .scrollIntoView({
                              behavior: "smooth",
                            });
                        }}
                      >
                        View Live Map
                      </Button>
                    </div>
                  </div>

                  {/* Driver Info */}
                  <h3 className='mb-4 font-semibold'>Delivery Agent</h3>
                  <div className='flex items-center'>
                    <div className='flex justify-center items-center bg-gray-200 mr-4 rounded-full w-12 h-12'>
                      <UserCheck size={20} className='text-gray-500' />
                    </div>
                    <div className='flex-grow'>
                      <h4 className='font-medium'>Delivery Agent</h4>
                      <p className='text-text-secondary text-sm'>
                        License Plate: AFG 1234
                      </p>
                    </div>
                    <div className='flex space-x-2'>
                      <button className='flex justify-center items-center bg-green-100 rounded-full w-10 h-10 text-accent-green'>
                        <Phone size={20} />
                      </button>
                      <button className='flex justify-center items-center bg-blue-100 rounded-full w-10 h-10 text-blue-600'>
                        <MessageCircle size={20} />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </Card>

            {/* Delivery Address */}
            <Card>
              <h3 className='mb-4 font-semibold'>Delivery Address</h3>

              <div className='flex items-start'>
                <MapPin size={20} className='mt-0.5 mr-2 text-text-secondary' />
                <p>{order.deliveryAddress}</p>
              </div>
            </Card>

            {/* Live Map Section - Only show for orders that are out for delivery */}
            {order.status === "outForDelivery" && (
              <div id='delivery-map-section' className='mt-6'>
                <h3 className='mb-4 font-semibold'>Live Delivery Tracking</h3>
                <DeliveryTracker order={order} />
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div>
            {/* Live Tracking Panel */}
            <LiveTrackingPanel orderId={order.id} className='mb-6' />

            <Card>
              <h3 className='mb-4 font-poppins font-semibold text-lg'>
                Order Summary
              </h3>

              <div className='space-y-3 mb-4'>
                {order.orderItems.map((item) => (
                  <div key={item.id} className='flex justify-between'>
                    <span>
                      {item.quantity}x {item.name}
                    </span>
                    <span>${(item.price * item.quantity).toFixed(2)}</span>
                  </div>
                ))}
              </div>

              <div className='space-y-3 mt-4 pt-4 border-gray-100 border-t text-sm'>
                <div className='flex justify-between'>
                  <span className='text-text-secondary'>Subtotal</span>
                  <span>
                    ${(order.totalAmount - order.deliveryFee).toFixed(2)}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span className='text-text-secondary'>Delivery Fee</span>
                  <span>${order.deliveryFee.toFixed(2)}</span>
                </div>
                <div className='flex justify-between pt-3 border-gray-100 border-t font-semibold text-base'>
                  <span>Total</span>
                  <span>${order.totalAmount.toFixed(2)}</span>
                </div>
              </div>

              <div className='mt-6'>
                <p className='flex items-center mb-2 text-sm'>
                  <CreditCard size={16} className='mr-2 text-text-secondary' />
                  <span>Payment Method:</span>
                  <span className='ml-1 font-medium'>Cash on Delivery</span>
                </p>
              </div>

              <div className='mt-4 pt-4 border-gray-100 border-t'>
                {/* Rating Button for Delivered Orders */}
                {order.status === 'delivered' && (
                  <Button
                    variant='primary'
                    fullWidth
                    className='mb-3'
                    onClick={() => setShowRatingModal(true)}
                  >
                    ⭐ Rate Your Order
                  </Button>
                )}

                <Button
                  variant='primary'
                  fullWidth
                  to={`/restaurants/${order.restaurantId}`}
                >
                  Order Again
                </Button>

                <Button
                  variant='outline'
                  fullWidth
                  className='mt-3'
                  to='/restaurants'
                >
                  Browse Restaurants
                </Button>
              </div>
            </Card>
          </div>
        </div>

        {/* Rating Modal */}
        <RatingModal
          isOpen={showRatingModal}
          onClose={() => setShowRatingModal(false)}
          order={order}
          onRatingSubmitted={() => {
            setShowRatingModal(false);
            // Optionally refresh order data to show that rating was submitted
            fetchOrderDetails();
          }}
        />
      </div>
    );
  } catch (error) {
    console.error("Error rendering order tracking:", error);

    // Show error UI
    return (
      <div className='mx-auto px-4 py-8 max-w-4xl animate-fade-in container'>
        <div className='flex items-center mb-8'>
          <Link
            to='/orders'
            className='mr-3 text-text-primary hover:text-primary-500'
          >
            <ArrowLeft size={20} />
          </Link>
          <h1 className='font-poppins font-semibold text-2xl'>
            Order Tracking
          </h1>
        </div>

        <Card>
          <div className='py-8 text-center'>
            <div className='flex justify-center items-center bg-red-100 mx-auto mb-4 rounded-full w-16 h-16'>
              <AlertCircle size={32} className='text-red-500' />
            </div>
            <h2 className='mb-2 font-semibold text-xl'>Something Went Wrong</h2>
            <p className='mb-6 text-gray-600'>
              We encountered an error while loading your order details.
            </p>
            <Button variant='primary' to='/restaurants'>
              Browse Restaurants
            </Button>
          </div>
        </Card>
      </div>
    );
  }
};

export default OrderTracking;
