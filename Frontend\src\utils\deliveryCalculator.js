/**
 * Dynamic Delivery Fee Calculator
 * Calculates delivery fees based on distance, time, and other factors
 * Now integrates with backend dynamic calculation service
 */

import deliveryAgentApi from "../services/deliveryAgentApi";

// Delivery pricing configuration (fallback values)
export const DELIVERY_CONFIG = {
  // Base delivery fee (minimum charge)
  baseFee: 2.0,

  // Price per kilometer
  pricePerKm: 0.5,

  // Maximum delivery distance (km)
  maxDeliveryDistance: 15,

  // Minimum delivery distance for base fee (km)
  minDistanceForBaseFee: 2,

  // Peak hour multiplier (rush hours)
  peakHourMultiplier: 1.5,

  // Peak hours (24-hour format)
  peakHours: [
    { start: 11, end: 14 }, // Lunch rush
    { start: 18, end: 21 }, // Dinner rush
  ],

  // Weekend multiplier
  weekendMultiplier: 1.2,

  // Bad weather multiplier
  badWeatherMultiplier: 1.3,

  // Express delivery multiplier (faster delivery)
  expressMultiplier: 1.8,

  // Free delivery threshold (order amount)
  freeDeliveryThreshold: 50.0,

  // Distance zones with different pricing
  zones: [
    { maxDistance: 2, fee: 2.0, name: "Zone 1 (Close)" },
    { maxDistance: 5, fee: 3.5, name: "Zone 2 (Medium)" },
    { maxDistance: 8, fee: 5.0, name: "Zone 3 (Far)" },
    { maxDistance: 12, fee: 7.5, name: "Zone 4 (Very Far)" },
    { maxDistance: 15, fee: 10.0, name: "Zone 5 (Maximum)" },
  ],
};

/**
 * Calculate delivery fee using dynamic backend service
 * @param {Object} restaurantLocation - {lat, lng}
 * @param {Object} deliveryLocation - {lat, lng}
 * @param {number} orderValue - Order total value
 * @param {string} priority - 'normal', 'express', 'urgent'
 * @returns {Promise<Object>} Delivery fee calculation result
 */
export async function calculateDynamicDeliveryFee(
  restaurantLocation,
  deliveryLocation,
  orderValue = 20,
  priority = "normal"
) {
  try {
    // Use backend dynamic calculation service
    const result = await deliveryAgentApi.calculateDeliveryFee(
      restaurantLocation,
      deliveryLocation,
      orderValue,
      priority
    );

    if (result.success) {
      return {
        success: true,
        fee: result.data.total_fee,
        breakdown: result.data.breakdown,
        distance: result.data.distance_info,
        time: result.data.time_info,
        estimatedDeliveryTime: result.data.estimated_delivery_time,
        isDynamic: true,
        isDeliverable: true,
      };
    } else {
      // Fallback to static calculation
      console.warn("Dynamic calculation failed, using fallback:", result.error);
      return calculateStaticDeliveryFee(
        restaurantLocation,
        deliveryLocation,
        orderValue
      );
    }
  } catch (error) {
    console.error("Error in dynamic delivery fee calculation:", error);
    // Fallback to static calculation
    return calculateStaticDeliveryFee(
      restaurantLocation,
      deliveryLocation,
      orderValue
    );
  }
}

/**
 * Main delivery fee calculation function (automatically chooses dynamic or static)
 * @param {Object} restaurantLocation - {lat, lng}
 * @param {Object} deliveryLocation - {lat, lng}
 * @param {number} orderValue - Order total value
 * @param {string} priority - 'normal', 'express', 'urgent'
 * @returns {Promise<Object>} Delivery fee calculation result
 */
export async function calculateDeliveryFee(
  restaurantLocation,
  deliveryLocation,
  orderValue = 20,
  priority = "normal"
) {
  try {
    // Try dynamic calculation first
    const dynamicResult = await calculateDynamicDeliveryFee(
      restaurantLocation,
      deliveryLocation,
      orderValue,
      priority
    );

    if (dynamicResult.success) {
      return dynamicResult;
    } else {
      // Fallback to static calculation
      console.warn("Dynamic calculation failed, using static fallback");
      return calculateStaticDeliveryFee(
        restaurantLocation,
        deliveryLocation,
        orderValue
      );
    }
  } catch (error) {
    console.error("Error in delivery fee calculation:", error);
    // Fallback to static calculation
    return calculateStaticDeliveryFee(
      restaurantLocation,
      deliveryLocation,
      orderValue
    );
  }
}

/**
 * Calculate distance between two points using Haversine formula
 * @param {number} lat1 - Latitude of first point
 * @param {number} lng1 - Longitude of first point
 * @param {number} lat2 - Latitude of second point
 * @param {number} lng2 - Longitude of second point
 * @returns {number} Distance in kilometers
 */
export const calculateDistance = (lat1, lng1, lat2, lng2) => {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = ((lat2 - lat1) * Math.PI) / 180;
  const dLng = ((lng2 - lng1) * Math.PI) / 180;

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;

  return Math.round(distance * 100) / 100; // Round to 2 decimal places
};

/**
 * Calculate estimated delivery time based on distance
 * @param {number} distance - Distance in kilometers
 * @param {object} options - Additional options
 * @returns {object} Estimated delivery time
 */
export const calculateDeliveryTime = (distance, options = {}) => {
  const { isExpress = false, isPeakHour = false, weather = "normal" } = options;

  // Base time calculation (assuming 30 km/h average speed)
  let baseTimeMinutes = (distance / 30) * 60;

  // Add preparation time (restaurant cooking time)
  const preparationTime = 15; // 15 minutes average

  // Add pickup and delivery buffer
  const bufferTime = 10; // 10 minutes buffer

  let totalTime = baseTimeMinutes + preparationTime + bufferTime;

  // Apply modifiers
  if (isPeakHour) {
    totalTime *= 1.3; // 30% longer during peak hours
  }

  if (weather === "bad") {
    totalTime *= 1.2; // 20% longer in bad weather
  }

  if (isExpress) {
    totalTime *= 0.7; // 30% faster for express delivery
  }

  const minTime = Math.max(20, Math.floor(totalTime - 5)); // Minimum 20 minutes
  const maxTime = Math.ceil(totalTime + 10);

  return {
    min: minTime,
    max: maxTime,
    average: Math.round((minTime + maxTime) / 2),
  };
};

/**
 * Check if current time is peak hour
 * @returns {boolean} True if current time is peak hour
 */
export const isPeakHour = () => {
  const now = new Date();
  const currentHour = now.getHours();

  return DELIVERY_CONFIG.peakHours.some(
    (peak) => currentHour >= peak.start && currentHour < peak.end
  );
};

/**
 * Check if current day is weekend
 * @returns {boolean} True if current day is weekend
 */
export const isWeekend = () => {
  const now = new Date();
  const dayOfWeek = now.getDay();
  return dayOfWeek === 0 || dayOfWeek === 6; // Sunday = 0, Saturday = 6
};

/**
 * Get delivery zone based on distance
 * @param {number} distance - Distance in kilometers
 * @returns {object} Zone information
 */
export const getDeliveryZone = (distance) => {
  const zone = DELIVERY_CONFIG.zones.find((z) => distance <= z.maxDistance);
  return zone || DELIVERY_CONFIG.zones[DELIVERY_CONFIG.zones.length - 1];
};

/**
 * Get time-based multiplier for delivery fee
 * @returns {number} Time multiplier
 */
export const getTimeMultiplier = () => {
  const now = new Date();
  const currentHour = now.getHours();
  const dayOfWeek = now.getDay();

  // Peak hours: lunch (11-14), dinner (17-21), weekend evenings
  if (
    (11 <= currentHour && currentHour <= 14) ||
    (17 <= currentHour && currentHour <= 21)
  ) {
    if (dayOfWeek >= 5) {
      // Weekend
      return DELIVERY_CONFIG.peakHourMultiplier * 1.2;
    } else {
      return DELIVERY_CONFIG.peakHourMultiplier;
    }
  } else if (dayOfWeek >= 5 && 18 <= currentHour && currentHour <= 23) {
    // Weekend evenings
    return DELIVERY_CONFIG.peakHourMultiplier;
  } else {
    return 1.0;
  }
};

/**
 * Static fallback delivery fee calculation
 * @param {Object} restaurantLocation - {lat, lng}
 * @param {Object} deliveryLocation - {lat, lng}
 * @param {number} orderValue - Order total value
 * @returns {Object} Delivery fee calculation result
 */
export function calculateStaticDeliveryFee(
  restaurantLocation,
  deliveryLocation,
  orderValue = 20
) {
  try {
    // Calculate distance using Haversine formula
    const distance = calculateDistance(
      restaurantLocation.lat,
      restaurantLocation.lng,
      deliveryLocation.lat,
      deliveryLocation.lng
    );

    // Check if within delivery range
    if (distance > DELIVERY_CONFIG.maxDeliveryDistance) {
      return {
        success: false,
        fee: 0,
        distance,
        isDeliverable: false,
        error: `Delivery not available. Maximum distance is ${DELIVERY_CONFIG.maxDeliveryDistance}km`,
        isDynamic: false,
      };
    }

    // Check for free delivery
    if (orderValue >= DELIVERY_CONFIG.freeDeliveryThreshold) {
      return {
        success: true,
        fee: 0,
        distance,
        isDeliverable: true,
        breakdown: {
          baseFee: 0,
          distanceFee: 0,
          modifiers: [],
          discount:
            "Free delivery for orders over $" +
            DELIVERY_CONFIG.freeDeliveryThreshold,
        },
        estimatedDeliveryTime: estimateDeliveryTime(distance),
        isDynamic: false,
      };
    }

    // Find appropriate zone
    const zone = DELIVERY_CONFIG.zones.find((z) => distance <= z.maxDistance);
    let fee = zone
      ? zone.fee
      : DELIVERY_CONFIG.zones[DELIVERY_CONFIG.zones.length - 1].fee;

    // Apply time-based multipliers
    const timeMultiplier = getTimeMultiplier();
    fee *= timeMultiplier;

    // Round to 2 decimal places
    fee = Math.round(fee * 100) / 100;

    return {
      success: true,
      fee,
      distance,
      isDeliverable: true,
      breakdown: {
        baseFee: zone
          ? zone.fee
          : DELIVERY_CONFIG.zones[DELIVERY_CONFIG.zones.length - 1].fee,
        distanceFee: 0,
        modifiers: [`Time multiplier: ${timeMultiplier}x`],
        total: fee,
      },
      estimatedDeliveryTime: estimateDeliveryTime(distance),
      isDynamic: false,
    };
  } catch (error) {
    console.error("Error in static delivery fee calculation:", error);
    return {
      success: false,
      fee: DELIVERY_CONFIG.baseFee,
      distance: 0,
      isDeliverable: true,
      error: "Calculation error, using base fee",
      isDynamic: false,
    };
  }
}

/**
 * Legacy delivery fee calculation (kept for compatibility)
 * @param {object} params - Calculation parameters
 * @returns {object} Delivery fee breakdown
 */
export const calculateDeliveryFeeLegacy = ({
  restaurantLocation,
  customerLocation,
  orderAmount = 0,
  isExpress = false,
  weather = "normal",
}) => {
  // Validate inputs
  if (!restaurantLocation || !customerLocation) {
    return {
      fee: 0,
      distance: 0,
      isDeliverable: false,
      error: "Missing location data",
    };
  }

  // Calculate distance
  const distance = calculateDistance(
    restaurantLocation.lat,
    restaurantLocation.lng,
    customerLocation.lat,
    customerLocation.lng
  );

  // Check if within delivery range
  if (distance > DELIVERY_CONFIG.maxDeliveryDistance) {
    return {
      fee: 0,
      distance,
      isDeliverable: false,
      error: `Delivery not available. Maximum distance is ${DELIVERY_CONFIG.maxDeliveryDistance}km`,
    };
  }

  // Check for free delivery
  if (orderAmount >= DELIVERY_CONFIG.freeDeliveryThreshold) {
    return {
      fee: 0,
      distance,
      isDeliverable: true,
      breakdown: {
        baseFee: 0,
        distanceFee: 0,
        modifiers: [],
        discount:
          "Free delivery for orders over $" +
          DELIVERY_CONFIG.freeDeliveryThreshold,
      },
      estimatedTime: calculateDeliveryTime(distance, { isExpress, weather }),
    };
  }

  // Get zone-based fee
  const zone = getDeliveryZone(distance);
  let fee = zone.fee;

  // Alternative: Distance-based calculation
  // let fee = DELIVERY_CONFIG.baseFee;
  // if (distance > DELIVERY_CONFIG.minDistanceForBaseFee) {
  //   fee += (distance - DELIVERY_CONFIG.minDistanceForBaseFee) * DELIVERY_CONFIG.pricePerKm;
  // }

  const breakdown = {
    baseFee: zone.fee,
    distanceFee: 0,
    modifiers: [],
    zone: zone.name,
  };

  // Apply peak hour multiplier
  const currentlyPeakHour = isPeakHour();
  if (currentlyPeakHour) {
    const peakSurcharge = fee * (DELIVERY_CONFIG.peakHourMultiplier - 1);
    fee *= DELIVERY_CONFIG.peakHourMultiplier;
    breakdown.modifiers.push({
      name: "Peak Hour Surcharge",
      amount: peakSurcharge,
      multiplier: DELIVERY_CONFIG.peakHourMultiplier,
    });
  }

  // Apply weekend multiplier
  if (isWeekend()) {
    const weekendSurcharge = fee * (DELIVERY_CONFIG.weekendMultiplier - 1);
    fee *= DELIVERY_CONFIG.weekendMultiplier;
    breakdown.modifiers.push({
      name: "Weekend Surcharge",
      amount: weekendSurcharge,
      multiplier: DELIVERY_CONFIG.weekendMultiplier,
    });
  }

  // Apply weather multiplier
  if (weather === "bad") {
    const weatherSurcharge = fee * (DELIVERY_CONFIG.badWeatherMultiplier - 1);
    fee *= DELIVERY_CONFIG.badWeatherMultiplier;
    breakdown.modifiers.push({
      name: "Weather Surcharge",
      amount: weatherSurcharge,
      multiplier: DELIVERY_CONFIG.badWeatherMultiplier,
    });
  }

  // Apply express delivery multiplier
  if (isExpress) {
    const expressSurcharge = fee * (DELIVERY_CONFIG.expressMultiplier - 1);
    fee *= DELIVERY_CONFIG.expressMultiplier;
    breakdown.modifiers.push({
      name: "Express Delivery",
      amount: expressSurcharge,
      multiplier: DELIVERY_CONFIG.expressMultiplier,
    });
  }

  // Round to 2 decimal places
  fee = Math.round(fee * 100) / 100;

  return {
    fee,
    distance,
    isDeliverable: true,
    breakdown,
    estimatedTime: calculateDeliveryTime(distance, {
      isExpress,
      isPeakHour: currentlyPeakHour,
      weather,
    }),
    zone: zone.name,
  };
};

/**
 * Get delivery fee display text
 * @param {object} deliveryInfo - Delivery calculation result
 * @returns {string} Display text for delivery fee
 */
export const getDeliveryFeeText = (deliveryInfo) => {
  if (!deliveryInfo.isDeliverable) {
    return deliveryInfo.error || "Delivery not available";
  }

  if (deliveryInfo.fee === 0) {
    return "Free Delivery";
  }

  return `$${deliveryInfo.fee.toFixed(2)} delivery`;
};

/**
 * Get estimated delivery time text
 * @param {object} timeInfo - Time calculation result
 * @returns {string} Display text for delivery time
 */
export const getDeliveryTimeText = (timeInfo) => {
  if (!timeInfo) return "Time not available";

  if (timeInfo.min === timeInfo.max) {
    return `${timeInfo.min} min`;
  }

  return `${timeInfo.min}-${timeInfo.max} min`;
};

export default {
  calculateDistance,
  calculateDeliveryFee,
  calculateDeliveryTime,
  getDeliveryZone,
  getDeliveryFeeText,
  getDeliveryTimeText,
  isPeakHour,
  isWeekend,
  DELIVERY_CONFIG,
};
