class NotificationService {
  constructor() {
    this.permission = 'default';
    this.isSupported = 'Notification' in window;
    this.listeners = new Map();
    this.notificationQueue = [];
    this.maxNotifications = 5;
    this.activeNotifications = new Map();
  }

  // Initialize notification service
  async init() {
    if (!this.isSupported) {
      console.warn('Browser notifications are not supported');
      return { success: false, error: 'Notifications not supported' };
    }

    try {
      this.permission = await this.requestPermission();
      return { 
        success: this.permission === 'granted', 
        permission: this.permission 
      };
    } catch (error) {
      console.error('Failed to initialize notifications:', error);
      return { success: false, error: error.message };
    }
  }

  // Request notification permission
  async requestPermission() {
    if (!this.isSupported) {
      return 'denied';
    }

    if (this.permission === 'granted') {
      return 'granted';
    }

    try {
      const permission = await Notification.requestPermission();
      this.permission = permission;
      return permission;
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return 'denied';
    }
  }

  // Show notification
  show(title, options = {}) {
    if (!this.canShowNotifications()) {
      console.warn('Cannot show notification - permission denied or not supported');
      return null;
    }

    const defaultOptions = {
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: 'delivery-agent',
      requireInteraction: false,
      silent: false,
      ...options
    };

    try {
      const notification = new Notification(title, defaultOptions);
      
      // Store active notification
      const notificationId = Date.now().toString();
      this.activeNotifications.set(notificationId, notification);

      // Set up event listeners
      notification.onclick = (event) => {
        event.preventDefault();
        this.emit('notification_click', { 
          id: notificationId, 
          title, 
          options: defaultOptions 
        });
        
        // Focus window if possible
        if (window.focus) {
          window.focus();
        }
        
        notification.close();
      };

      notification.onclose = () => {
        this.activeNotifications.delete(notificationId);
        this.emit('notification_close', { 
          id: notificationId, 
          title 
        });
      };

      notification.onerror = (error) => {
        console.error('Notification error:', error);
        this.activeNotifications.delete(notificationId);
        this.emit('notification_error', { 
          id: notificationId, 
          error 
        });
      };

      // Auto-close after specified time
      if (defaultOptions.autoClose !== false) {
        const autoCloseTime = defaultOptions.autoCloseTime || 5000;
        setTimeout(() => {
          if (this.activeNotifications.has(notificationId)) {
            notification.close();
          }
        }, autoCloseTime);
      }

      this.emit('notification_show', { 
        id: notificationId, 
        title, 
        options: defaultOptions 
      });

      return notificationId;

    } catch (error) {
      console.error('Failed to show notification:', error);
      return null;
    }
  }

  // Show order notification
  showOrderNotification(order, type = 'new') {
    const titles = {
      new: '🆕 New Order Available',
      assigned: '✅ Order Assigned',
      updated: '📦 Order Updated',
      completed: '🎉 Order Completed',
      cancelled: '❌ Order Cancelled'
    };

    const title = titles[type] || 'Order Notification';
    
    const body = type === 'new' 
      ? `${order.restaurant?.name || 'Restaurant'} - $${order.total_amount?.toFixed(2) || '0.00'}`
      : `Order #${order.id} from ${order.restaurant_name || order.restaurant?.name}`;

    return this.show(title, {
      body,
      icon: '/icons/order-icon.png',
      tag: `order-${order.id}`,
      data: { orderId: order.id, type },
      actions: type === 'new' ? [
        { action: 'accept', title: 'Accept Order' },
        { action: 'dismiss', title: 'Dismiss' }
      ] : [],
      requireInteraction: type === 'new'
    });
  }

  // Show earnings notification
  showEarningsNotification(amount, type = 'delivery') {
    const titles = {
      delivery: '💰 Delivery Completed',
      tip: '🎁 Tip Received',
      bonus: '🌟 Bonus Earned',
      payout: '💳 Payout Processed'
    };

    const title = titles[type] || 'Earnings Update';
    const body = `You earned $${amount.toFixed(2)}`;

    return this.show(title, {
      body,
      icon: '/icons/money-icon.png',
      tag: 'earnings',
      data: { amount, type }
    });
  }

  // Show status notification
  showStatusNotification(message, type = 'info') {
    const icons = {
      info: '/icons/info-icon.png',
      success: '/icons/success-icon.png',
      warning: '/icons/warning-icon.png',
      error: '/icons/error-icon.png'
    };

    const titles = {
      info: 'ℹ️ Information',
      success: '✅ Success',
      warning: '⚠️ Warning',
      error: '❌ Error'
    };

    return this.show(titles[type], {
      body: message,
      icon: icons[type],
      tag: `status-${type}`,
      data: { type, message }
    });
  }

  // Show location notification
  showLocationNotification(message) {
    return this.show('📍 Location Update', {
      body: message,
      icon: '/icons/location-icon.png',
      tag: 'location',
      data: { type: 'location', message }
    });
  }

  // Show shift notification
  showShiftNotification(type, data = {}) {
    const titles = {
      start: '🚀 Shift Started',
      end: '🏁 Shift Ended',
      break: '☕ Break Time',
      reminder: '⏰ Shift Reminder'
    };

    const messages = {
      start: 'Your delivery shift has started. Good luck!',
      end: `Shift ended. You completed ${data.orders || 0} orders and earned $${data.earnings?.toFixed(2) || '0.00'}`,
      break: 'Time for a break! You\'ve been working hard.',
      reminder: 'Don\'t forget to end your shift when you\'re done.'
    };

    return this.show(titles[type], {
      body: messages[type],
      icon: '/icons/shift-icon.png',
      tag: `shift-${type}`,
      data: { type, ...data }
    });
  }

  // Close notification by ID
  closeNotification(notificationId) {
    if (this.activeNotifications.has(notificationId)) {
      const notification = this.activeNotifications.get(notificationId);
      notification.close();
      return true;
    }
    return false;
  }

  // Close all notifications
  closeAllNotifications() {
    this.activeNotifications.forEach((notification, id) => {
      notification.close();
    });
    this.activeNotifications.clear();
  }

  // Close notifications by tag
  closeNotificationsByTag(tag) {
    this.activeNotifications.forEach((notification, id) => {
      if (notification.tag === tag) {
        notification.close();
      }
    });
  }

  // Check if notifications can be shown
  canShowNotifications() {
    return this.isSupported && this.permission === 'granted';
  }

  // Get notification permission status
  getPermissionStatus() {
    return {
      supported: this.isSupported,
      permission: this.permission,
      canShow: this.canShowNotifications()
    };
  }

  // Add event listener
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  // Remove event listener
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  // Emit event to listeners
  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in notification event listener:', error);
        }
      });
    }
  }

  // Schedule notification
  scheduleNotification(title, options, delay) {
    return setTimeout(() => {
      this.show(title, options);
    }, delay);
  }

  // Show in-app notification (fallback for when browser notifications are disabled)
  showInAppNotification(title, message, type = 'info', duration = 5000) {
    const notification = {
      id: Date.now().toString(),
      title,
      message,
      type,
      timestamp: new Date(),
      duration
    };

    this.emit('in_app_notification', notification);

    // Auto-remove after duration
    if (duration > 0) {
      setTimeout(() => {
        this.emit('remove_in_app_notification', { id: notification.id });
      }, duration);
    }

    return notification.id;
  }

  // Test notification
  testNotification() {
    return this.show('🧪 Test Notification', {
      body: 'This is a test notification from Afghan Sofra Delivery',
      icon: '/favicon.ico',
      tag: 'test',
      requireInteraction: false
    });
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
