import React, { useState } from 'react';

function CuisineUpdateTest() {
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState(false);
  const [restaurantId, setRestaurantId] = useState('20');

  const testCuisineUpdate = async () => {
    setLoading(true);
    try {
      // Get authentication token
      const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
      const token = user.access_token || user.token;

      if (!token) {
        setResults({ error: "No authentication token found" });
        setLoading(false);
        return;
      }

      console.log("=== CUISINE UPDATE TEST ===");

      // Step 1: Get current restaurant data
      console.log("1. Getting current restaurant data...");
      const getCurrentResponse = await fetch(`http://127.0.0.1:8000/api/restaurant/restaurants/${restaurantId}/`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (!getCurrentResponse.ok) {
        throw new Error(`Failed to get current data: ${getCurrentResponse.status}`);
      }

      const currentData = await getCurrentResponse.json();
      console.log("Current cuisine types:", currentData.cuisine_types);

      // Step 2: Update cuisine type
      console.log("2. Updating cuisine type...");
      const updateData = {
        cuisine_type_ids: [1] // Italian cuisine
      };

      const updateResponse = await fetch(`http://127.0.0.1:8000/api/restaurant/restaurants/${restaurantId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updateData)
      });

      if (!updateResponse.ok) {
        throw new Error(`Update failed: ${updateResponse.status} - ${await updateResponse.text()}`);
      }

      const updatedData = await updateResponse.json();
      console.log("Updated cuisine types:", updatedData.cuisine_types);

      // Step 3: Verify the change
      console.log("3. Verifying the change...");
      const verifyResponse = await fetch(`http://127.0.0.1:8000/api/restaurant/restaurants/${restaurantId}/`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const verifiedData = await verifyResponse.json();
      console.log("Verified cuisine types:", verifiedData.cuisine_types);

      setResults({
        success: true,
        before: currentData.cuisine_types,
        after: updatedData.cuisine_types,
        verified: verifiedData.cuisine_types,
        updateWorked: updatedData.cuisine_types?.[0]?.name === 'Italian'
      });

    } catch (error) {
      console.error("Test error:", error);
      setResults({ success: false, error: error.message });
    }
    setLoading(false);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Cuisine Update Test</h1>
      
      <div className="mb-4">
        <label className="block mb-2">Restaurant ID:</label>
        <input
          type="text"
          value={restaurantId}
          onChange={(e) => setRestaurantId(e.target.value)}
          className="px-3 py-2 border rounded w-32"
        />
      </div>

      <button
        onClick={testCuisineUpdate}
        disabled={loading}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50 mb-6"
      >
        {loading ? "Testing..." : "Test Cuisine Update"}
      </button>

      {results.success !== undefined && (
        <div className="bg-gray-100 p-4 rounded">
          <h3 className="font-bold mb-2">Test Results:</h3>
          {results.success ? (
            <div>
              <p className="text-green-600 font-bold">✅ Test {results.updateWorked ? 'PASSED' : 'FAILED'}</p>
              <div className="mt-2">
                <p><strong>Before:</strong> {JSON.stringify(results.before)}</p>
                <p><strong>After:</strong> {JSON.stringify(results.after)}</p>
                <p><strong>Verified:</strong> {JSON.stringify(results.verified)}</p>
                <p><strong>Update Worked:</strong> {results.updateWorked ? 'YES' : 'NO'}</p>
              </div>
            </div>
          ) : (
            <div>
              <p className="text-red-600 font-bold">❌ Test FAILED</p>
              <p className="text-red-600">{results.error}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default CuisineUpdateTest;
