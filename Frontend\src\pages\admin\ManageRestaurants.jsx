import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Store,
  CheckCircle,
  XCircle,
  Eye,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { mockRestaurants } from "../../data/restaurants";
import { mockUsers } from "../../data/users";

function ManageRestaurants() {
  const [selectedStatus, setSelectedStatus] = useState("all");

  return (
    <div className='p-6'>
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6'>
        <h1 className='text-2xl font-bold mb-4 sm:mb-0'>Manage Restaurants</h1>
        <Button variant='primary' icon={<Store size={18} />}>
          Add Restaurant
        </Button>
      </div>

      <Card>
        <div className='flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6'>
          <div className='flex-1 relative'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={20}
            />
            <input
              type='text'
              placeholder='Search restaurants...'
              className='w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
            />
          </div>

          <div className='flex gap-4'>
            <select
              className='px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
            >
              <option value='all'>All Status</option>
              <option value='active'>Active</option>
              <option value='pending'>Pending</option>
              <option value='suspended'>Suspended</option>
            </select>

            <Button variant='secondary' icon={<Filter size={18} />}>
              Filters
            </Button>
          </div>
        </div>

        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead>
              <tr className='border-b'>
                <th className='text-left py-4 px-4 font-semibold'>
                  Restaurant
                </th>
                <th className='text-left py-4 px-4 font-semibold'>Owner</th>
                <th className='text-left py-4 px-4 font-semibold'>Cuisine</th>
                <th className='text-left py-4 px-4 font-semibold'>Status</th>
                <th className='text-left py-4 px-4 font-semibold'>Rating</th>
                <th className='text-right py-4 px-4 font-semibold'>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className='border-b hover:bg-gray-50'>
                <td className='py-4 px-4'>
                  <div className='flex items-center'>
                    <div className='w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center'>
                      <img
                        src='https://images.pexels.com/photos/5409013/pexels-photo-5409013.jpeg?auto=compress&cs=tinysrgb&w=600'
                        alt='Kabul Kitchen'
                        className='w-full h-full object-cover rounded-lg'
                      />
                    </div>
                    <div className='ml-3'>
                      <p className='font-medium'>Kabul Kitchen</p>
                      <p className='text-sm text-text-secondary'>
                        Kabul, Afghanistan
                      </p>
                    </div>
                  </div>
                </td>
                <td className='py-4 px-4'>
                  <p>Ahmad Khan</p>
                  <p className='text-sm text-text-secondary'>
                    <EMAIL>
                  </p>
                </td>
                <td className='py-4 px-4'>
                  <div className='flex flex-wrap gap-1'>
                    <Badge variant='secondary' size='small'>
                      Afghan
                    </Badge>
                    <Badge variant='secondary' size='small'>
                      BBQ
                    </Badge>
                  </div>
                </td>
                <td className='py-4 px-4'>
                  <Badge variant='success' size='small'>
                    Active
                  </Badge>
                </td>
                <td className='py-4 px-4'>
                  <div className='flex items-center'>
                    <span className='text-yellow-500'>★</span>
                    <span className='ml-1'>4.8</span>
                  </div>
                </td>
                <td className='py-4 px-4'>
                  <div className='flex justify-end gap-2'>
                    <button className='p-1 hover:text-primary-500'>
                      <Edit size={18} />
                    </button>
                    <button className='p-1 hover:text-accent-red'>
                      <Trash2 size={18} />
                    </button>
                    <button className='p-1'>
                      <MoreVertical size={18} />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div className='mt-4 flex items-center justify-between'>
          <p className='text-sm text-text-secondary'>
            Showing 1 of 1 restaurants
          </p>
          <div className='flex gap-2'>
            <Button variant='secondary' disabled>
              Previous
            </Button>
            <Button variant='secondary' disabled>
              Next
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default ManageRestaurants;
