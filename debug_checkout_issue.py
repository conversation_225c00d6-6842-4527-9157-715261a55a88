#!/usr/bin/env python3
"""
Debug the checkout issue - check if users have saved addresses
"""

import os
import sys
import django

# Add the Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from restaurant.models import Address

User = get_user_model()

def debug_checkout_issue():
    """Debug why the Place Order button is disabled"""
    
    print("🔍 Debugging Checkout Issue")
    print("=" * 50)
    
    # Check users and their addresses
    customers = User.objects.filter(role='customer')
    print(f"📊 Total customers: {customers.count()}")
    
    if customers.exists():
        print(f"\n👥 Customer Address Status:")
        for customer in customers[:5]:  # Show first 5
            addresses = Address.objects.filter(user=customer)
            print(f"   {customer.name} ({customer.email}): {addresses.count()} addresses")
            
            for addr in addresses:
                print(f"      - {addr.street}, {addr.city} (ID: {addr.id})")
    
    # Check if there are any addresses at all
    total_addresses = Address.objects.count()
    print(f"\n📍 Total addresses in system: {total_addresses}")
    
    if total_addresses == 0:
        print("\n❌ ISSUE FOUND: No addresses in the system!")
        print("   The checkout requires saved addresses, but none exist.")
        print("\n💡 SOLUTION:")
        print("   1. Users need to add delivery addresses first")
        print("   2. Or we need to allow temporary addresses for checkout")
        
        # Create a test address for the test customer
        try:
            test_customer = User.objects.get(email='<EMAIL>')
            test_address = Address.objects.create(
                user=test_customer,
                street='Test Delivery Street',
                city='Kabul',
                state='Kabul',
                postal_code='1002',
                country='Afghanistan',
                latitude=34.5600,
                longitude=69.2100
            )
            print(f"\n✅ Created test address for {test_customer.email}")
            print(f"   Address ID: {test_address.id}")
            print(f"   Address: {test_address}")
        except User.DoesNotExist:
            print("\n⚠️ Test customer not found")
        except Exception as e:
            print(f"\n❌ Error creating test address: {e}")
    
    else:
        print("\n✅ Addresses exist in the system")
        
        # Show some sample addresses
        sample_addresses = Address.objects.all()[:3]
        print(f"\n📍 Sample addresses:")
        for addr in sample_addresses:
            print(f"   {addr.user.name}: {addr.street}, {addr.city} (ID: {addr.id})")
    
    print(f"\n🎯 Checkout Requirements:")
    print(f"   1. User must be logged in as a customer")
    print(f"   2. User must have items in cart")
    print(f"   3. User must have at least one saved address")
    print(f"   4. User must select a saved address in checkout")
    
    print(f"\n🔗 To test checkout:")
    print(f"   1. Go to: http://localhost:5174/customer/profile")
    print(f"   2. Add a delivery address")
    print(f"   3. Go to: http://localhost:5174/checkout")
    print(f"   4. Select the saved address")
    print(f"   5. Place Order button should be enabled")

if __name__ == '__main__':
    debug_checkout_issue()
