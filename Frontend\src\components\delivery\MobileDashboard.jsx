import React, { useState } from 'react';
import {
  TruckIcon,
  Activity,
  Package,
  DollarSign,
  Star,
  Play,
  Square,
  RefreshCw,
  Navigation,
  Clock,
  Target,
  TrendingUp,
  Bell,
  Settings,
  Menu,
  X
} from 'lucide-react';
import Button from '../common/Button';
import Card from '../common/Card';
import Badge from '../common/Badge';

const MobileDashboard = ({
  dashboardData,
  isOnline,
  currentShift,
  isConnected,
  isLocationTracking,
  onToggleOnlineStatus,
  onStartShift,
  onEndShift,
  onRefresh,
  refreshing
}) => {
  const [showMenu, setShowMenu] = useState(false);

  const QuickStatCard = ({ icon: Icon, label, value, color = 'blue' }) => (
    <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
      <div className="flex items-center space-x-3">
        <div className={`p-2 rounded-full bg-${color}-100`}>
          <Icon className={`h-5 w-5 text-${color}-600`} />
        </div>
        <div>
          <p className="text-xs text-gray-500 uppercase tracking-wide">{label}</p>
          <p className={`text-lg font-bold text-${color}-600`}>{value}</p>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                isOnline ? 'bg-green-100' : 'bg-gray-100'
              }`}>
                <TruckIcon className={`h-5 w-5 ${
                  isOnline ? 'text-green-600' : 'text-gray-400'
                }`} />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900">
                  {dashboardData?.agent_info?.name || 'Agent'}
                </h1>
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${
                    isConnected ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <span className={`text-xs ${
                    isConnected ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {isConnected ? 'Connected' : 'Offline'}
                  </span>
                  {isLocationTracking && (
                    <>
                      <Navigation className="w-3 h-3 text-blue-500" />
                      <span className="text-xs text-blue-600">GPS</span>
                    </>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={onRefresh}
                disabled={refreshing}
                className="p-2"
              >
                <RefreshCw className={`h-5 w-5 ${refreshing ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMenu(!showMenu)}
                className="p-2"
              >
                {showMenu ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {showMenu && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-20" onClick={() => setShowMenu(false)}>
          <div className="absolute right-0 top-0 h-full w-64 bg-white shadow-lg">
            <div className="p-4 border-b">
              <h3 className="font-semibold text-gray-900">Menu</h3>
            </div>
            <div className="p-4 space-y-2">
              <Button variant="ghost" className="w-full justify-start">
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Button>
              <Button variant="ghost" className="w-full justify-start">
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className="p-4 space-y-4">
        {/* Online Status Toggle */}
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-gray-900">Status</h3>
              <p className="text-sm text-gray-500">
                {isOnline ? 'You are online and can receive orders' : 'You are offline'}
              </p>
            </div>
            <Button
              onClick={onToggleOnlineStatus}
              className={`flex items-center space-x-2 ${
                isOnline 
                  ? 'bg-red-600 hover:bg-red-700' 
                  : 'bg-green-600 hover:bg-green-700'
              }`}
            >
              {isOnline ? <Square className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              <span>{isOnline ? 'Go Offline' : 'Go Online'}</span>
            </Button>
          </div>
        </Card>

        {/* Quick Stats Grid */}
        <div className="grid grid-cols-2 gap-3">
          <QuickStatCard
            icon={Package}
            label="Today's Orders"
            value={dashboardData?.today_stats?.completed_orders || 0}
            color="blue"
          />
          <QuickStatCard
            icon={DollarSign}
            label="Today's Earnings"
            value={`$${dashboardData?.today_stats?.earnings?.toFixed(2) || '0.00'}`}
            color="green"
          />
          <QuickStatCard
            icon={Star}
            label="Rating"
            value={dashboardData?.agent_info?.rating?.toFixed(1) || '0.0'}
            color="yellow"
          />
          <QuickStatCard
            icon={Target}
            label="Completion"
            value={`${dashboardData?.agent_info?.completion_rate?.toFixed(0) || '0'}%`}
            color="purple"
          />
        </div>

        {/* Current Shift */}
        <Card className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900">Current Shift</h3>
            {currentShift?.is_active ? (
              <Badge variant="success" className="flex items-center space-x-1">
                <Clock className="h-3 w-3" />
                <span>Active</span>
              </Badge>
            ) : (
              <Badge variant="secondary">Inactive</Badge>
            )}
          </div>
          
          {currentShift?.is_active ? (
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Duration</p>
                  <p className="font-medium">
                    {currentShift.duration_hours?.toFixed(1) || '0.0'}h
                  </p>
                </div>
                <div>
                  <p className="text-gray-500">Orders</p>
                  <p className="font-medium">{currentShift.orders_completed || 0}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Start Time</p>
                  <p className="font-medium">
                    {new Date(currentShift.start_time).toLocaleTimeString()}
                  </p>
                </div>
                <div>
                  <p className="text-gray-500">Earnings</p>
                  <p className="font-medium text-green-600">
                    ${currentShift.earnings?.toFixed(2) || '0.00'}
                  </p>
                </div>
              </div>
              <Button 
                onClick={onEndShift}
                variant="outline"
                className="w-full flex items-center justify-center space-x-2"
              >
                <Square className="h-4 w-4" />
                <span>End Shift</span>
              </Button>
            </div>
          ) : (
            <div className="text-center py-4">
              <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600 mb-3">No active shift</p>
              <Button 
                onClick={onStartShift}
                className="flex items-center space-x-2"
              >
                <Play className="h-4 w-4" />
                <span>Start Shift</span>
              </Button>
            </div>
          )}
        </Card>

        {/* Active Orders */}
        {dashboardData?.active_orders?.length > 0 && (
          <Card className="p-4">
            <h3 className="font-semibold text-gray-900 mb-3">Active Orders</h3>
            <div className="space-y-3">
              {dashboardData.active_orders.slice(0, 3).map((order) => (
                <div key={order.id} className="border rounded-lg p-3 bg-gray-50">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900 text-sm">
                        {order.restaurant_name}
                      </h4>
                      <p className="text-xs text-gray-500">
                        {order.customer_name}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-green-600 text-sm">
                        ${order.total_amount?.toFixed(2)}
                      </p>
                      <Badge variant={
                        order.status === 'assigned' ? 'warning' :
                        order.status === 'picked_up' ? 'info' :
                        order.status === 'on_the_way' ? 'success' : 'secondary'
                      } className="text-xs">
                        {order.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 truncate">
                    {order.delivery_address}
                  </p>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Weekly Performance */}
        <Card className="p-4">
          <h3 className="font-semibold text-gray-900 mb-3">This Week</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full mx-auto mb-1">
                <Target className="h-4 w-4 text-blue-600" />
              </div>
              <p className="text-lg font-bold text-blue-600">
                {dashboardData?.weekly_performance?.total_orders || 0}
              </p>
              <p className="text-xs text-gray-600">Orders</p>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full mx-auto mb-1">
                <DollarSign className="h-4 w-4 text-green-600" />
              </div>
              <p className="text-lg font-bold text-green-600">
                ${dashboardData?.weekly_performance?.total_earnings?.toFixed(0) || '0'}
              </p>
              <p className="text-xs text-gray-600">Earnings</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default MobileDashboard;
