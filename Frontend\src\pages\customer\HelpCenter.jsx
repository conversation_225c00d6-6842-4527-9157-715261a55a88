import React from 'react';
import { MessageCircle, FileText, Phone, Mail, ArrowRight, HelpCircle, Headphones, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useSupport } from '../../context/SupportContext';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import FAQ from '../../components/support/FAQ';

const HelpCenter = () => {
  const { faqCategories, setIsChatOpen } = useSupport();

  const supportOptions = [
    {
      icon: MessageCircle,
      title: 'Live Chat',
      description: 'Get instant help from our support team',
      action: 'Start Chat',
      onClick: () => setIsChatOpen(true),
      available: true,
      responseTime: 'Usually responds in minutes'
    },
    {
      icon: FileText,
      title: 'Submit a Ticket',
      description: 'Create a detailed support request',
      action: 'Create Ticket',
      to: '/support/contact',
      available: true,
      responseTime: 'Response within 24 hours'
    },
    {
      icon: Phone,
      title: 'Phone Support',
      description: 'Speak directly with our support team',
      action: 'Call Now',
      href: 'tel:+15551234567',
      available: true,
      responseTime: 'Mon-Sun: 9 AM - 11 PM'
    },
    {
      icon: Mail,
      title: 'Email Support',
      description: 'Send us a detailed email',
      action: 'Send Email',
      href: 'mailto:<EMAIL>',
      available: true,
      responseTime: 'Response within 24 hours'
    }
  ];

  const quickLinks = [
    {
      title: 'Track Your Order',
      description: 'Check the status of your current orders',
      to: '/orders',
      icon: '📦'
    },
    {
      title: 'Manage Account',
      description: 'Update your profile and preferences',
      to: '/profile',
      icon: '👤'
    },
    {
      title: 'Order History',
      description: 'View your past orders and receipts',
      to: '/orders',
      icon: '📋'
    },
    {
      title: 'Loyalty Program',
      description: 'Learn about points and rewards',
      to: '/loyalty-demo',
      icon: '🏆'
    },
    {
      title: 'Scheduled Orders',
      description: 'Manage your scheduled deliveries',
      to: '/scheduled-orders',
      icon: '📅'
    },
    {
      title: 'Favorites',
      description: 'View your saved restaurants',
      to: '/favorites',
      icon: '❤️'
    }
  ];

  return (
    <div className="animate-fade-in">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-500 to-primary-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <div className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6">
            <HelpCircle size={40} />
          </div>
          <h1 className="text-4xl font-bold mb-4">How can we help you?</h1>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Find answers to your questions, get support, and learn how to make the most of Afghan Sofra
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        {/* Support Options */}
        <section className="mb-16">
          <h2 className="text-2xl font-bold text-center mb-8">Get Support</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {supportOptions.map((option, index) => {
              const IconComponent = option.icon;
              const cardContent = (
                <Card className="text-center p-6 h-full hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <IconComponent size={24} className="text-primary-600" />
                  </div>
                  <h3 className="font-semibold mb-2">{option.title}</h3>
                  <p className="text-gray-600 text-sm mb-4">{option.description}</p>
                  <div className="flex items-center justify-center text-primary-600 text-sm font-medium">
                    {option.action} <ArrowRight size={14} className="ml-1" />
                  </div>
                  <div className="mt-3 flex items-center justify-center text-xs text-gray-500">
                    <Clock size={12} className="mr-1" />
                    {option.responseTime}
                  </div>
                </Card>
              );

              if (option.onClick) {
                return (
                  <button key={index} onClick={option.onClick} className="text-left">
                    {cardContent}
                  </button>
                );
              } else if (option.to) {
                return (
                  <Link key={index} to={option.to}>
                    {cardContent}
                  </Link>
                );
              } else if (option.href) {
                return (
                  <a key={index} href={option.href}>
                    {cardContent}
                  </a>
                );
              }
              return cardContent;
            })}
          </div>
        </section>

        {/* Quick Links */}
        <section className="mb-16">
          <h2 className="text-2xl font-bold text-center mb-8">Quick Links</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickLinks.map((link, index) => (
              <Link key={index} to={link.to}>
                <Card className="p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start">
                    <span className="text-2xl mr-4">{link.icon}</span>
                    <div>
                      <h3 className="font-semibold mb-1">{link.title}</h3>
                      <p className="text-gray-600 text-sm">{link.description}</p>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </section>

        {/* FAQ Categories Preview */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold mb-2">Popular Help Topics</h2>
            <p className="text-gray-600">Quick answers to common questions</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {faqCategories.map((category) => (
              <Link key={category.id} to={`/support/faq#${category.id}`}>
                <Card className="text-center p-6 hover:shadow-lg transition-shadow">
                  <span className="text-3xl mb-3 block">{category.icon}</span>
                  <h3 className="font-semibold mb-2">{category.name}</h3>
                  <p className="text-gray-600 text-sm">
                    {category.questions.length} articles
                  </p>
                </Card>
              </Link>
            ))}
          </div>

          <div className="text-center">
            <Button variant="outline" to="/support/faq">
              View All FAQs
            </Button>
          </div>
        </section>

        {/* Contact Information */}
        <section>
          <Card className="p-8 bg-gradient-to-r from-gray-50 to-gray-100">
            <div className="text-center">
              <Headphones size={48} className="mx-auto text-primary-600 mb-4" />
              <h2 className="text-2xl font-bold mb-2">Still Need Help?</h2>
              <p className="text-gray-600 mb-6">
                Our support team is available 24/7 to assist you with any questions or concerns.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="primary"
                  onClick={() => setIsChatOpen(true)}
                  icon={<MessageCircle size={16} />}
                >
                  Start Live Chat
                </Button>
                <Button
                  variant="outline"
                  to="/support/contact"
                  icon={<FileText size={16} />}
                >
                  Submit a Ticket
                </Button>
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                  <div>
                    <h4 className="font-semibold mb-1">Phone Support</h4>
                    <p className="text-gray-600">+****************</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">Email Support</h4>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-1">Response Time</h4>
                    <p className="text-gray-600">Within 24 hours</p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </section>
      </div>
    </div>
  );
};

export default HelpCenter;
