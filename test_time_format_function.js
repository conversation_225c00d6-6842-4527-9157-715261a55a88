// Test the time formatting function
const formatTime = (time) => {
  if (!time) return null;
  return time.includes(":") && time.split(":").length === 2
    ? `${time}:00`
    : time;
};

// Test cases
console.log("Testing time formatting function:");
console.log("formatTime('09:00') =", formatTime('09:00'));
console.log("formatTime('22:00') =", formatTime('22:00'));
console.log("formatTime('09:00:00') =", formatTime('09:00:00'));
console.log("formatTime('') =", formatTime(''));
console.log("formatTime(null) =", formatTime(null));
console.log("formatTime(undefined) =", formatTime(undefined));
