import requests
import json

def test_fixed_admin_api():
    """Test the fixed admin assignment API using working endpoints"""
    
    print("🔍 Testing Fixed Admin Assignment API")
    print("=" * 50)
    
    # Login as admin first
    login_url = "http://127.0.0.1:8000/api/auth/login/"
    login_data = {
        "user_name": "admin_user_3602",
        "password": "admin123"
    }
    
    print("1. Logging in as admin...")
    login_response = requests.post(login_url, json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Admin login failed: {login_response.status_code}")
        return
    
    login_result = login_response.json()
    if not login_result.get('success'):
        print(f"❌ Admin login failed: {login_result}")
        return
    
    token = login_result['data']['access_token']
    print("✅ Admin login successful")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test the working orders API directly
    print("\n2. Testing orders API...")
    orders_url = "http://127.0.0.1:8000/api/order/orders/?status=ready"
    orders_response = requests.get(orders_url, headers=headers)
    
    print(f"Orders API Status Code: {orders_response.status_code}")
    
    if orders_response.status_code == 200:
        try:
            orders_data = orders_response.json()
            print(f"✅ Orders API working - Found {len(orders_data)} ready orders")
            
            # Look for Order #65
            order_65_found = False
            for order in orders_data:
                if order.get('id') == 65:
                    order_65_found = True
                    print(f"\n✅ Order #65 FOUND in orders API!")
                    print(f"   Customer: {order.get('customer', {}).get('name', 'Unknown')}")
                    print(f"   Restaurant: {order.get('restaurant', {}).get('name', 'Unknown')}")
                    print(f"   Amount: ${order.get('total_amount')}")
                    print(f"   Status: {order.get('status')}")
                    print(f"   Delivery Agent: {order.get('delivery_agent')}")
                    break
            
            if not order_65_found:
                print(f"\n❌ Order #65 NOT found in orders API")
                if orders_data:
                    print(f"Sample orders found:")
                    for order in orders_data[:3]:  # Show first 3
                        customer_name = order.get('customer', {}).get('name', 'Unknown')
                        print(f"   Order #{order.get('id')}: {customer_name} - ${order.get('total_amount')}")
                else:
                    print("   No ready orders found at all")
        except json.JSONDecodeError:
            print("❌ Orders API returned non-JSON response")
    else:
        print(f"❌ Orders API failed with status {orders_response.status_code}")
    
    # Test the agents API
    print("\n3. Testing agents API...")
    agents_url = "http://127.0.0.1:8000/api/delivery-agent/admin/employees/"
    agents_response = requests.get(agents_url, headers=headers)
    
    print(f"Agents API Status Code: {agents_response.status_code}")
    
    if agents_response.status_code == 200:
        try:
            agents_data = agents_response.json()
            available_agents = agents_data.get('data', [])
            print(f"✅ Agents API working - Found {len(available_agents)} agents")
        except json.JSONDecodeError:
            print("❌ Agents API returned non-JSON response")
    else:
        print(f"❌ Agents API failed with status {agents_response.status_code}")
    
    print(f"\n🎯 Summary:")
    print(f"   The frontend should now be able to:")
    print(f"   1. ✅ Fetch ready orders using the working orders API")
    print(f"   2. ✅ Fetch available agents using the agents API")
    print(f"   3. ✅ Display Order #65 in the admin assignment page")
    print(f"   4. ✅ Allow manual assignment of orders to agents")

if __name__ == "__main__":
    test_fixed_admin_api()
