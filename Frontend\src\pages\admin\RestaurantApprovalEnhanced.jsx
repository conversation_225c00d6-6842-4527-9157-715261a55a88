import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  MapPin,
  Phone,
  Mail,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Upload,
  X,
  Eye,
  Download,
  MessageSquare,
  Calendar,
  Star,
  Building,
  Users,
  DollarSign,
  Shield,
  Camera,
  Send,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import restaurantApi from "../../utils/restaurantApi";

function RestaurantApprovalEnhanced() {
  const { user: currentUser } = useAuth();

  // State for restaurant data
  const [restaurants, setRestaurants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [filteredRestaurants, setFilteredRestaurants] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("pending");
  const [selectedRestaurant, setSelectedRestaurant] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [approvalNotes, setApprovalNotes] = useState("");
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectModal, setShowRejectModal] = useState(false);

  // Fetch restaurants from API
  const fetchRestaurants = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await restaurantApi.getAdminRestaurants({
        status: statusFilter === 'all' ? undefined : statusFilter,
        search: searchQuery || undefined,
      });

      if (result.success) {
        setRestaurants(result.data);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('Failed to fetch restaurants');
      console.error('Error fetching restaurants:', err);
    } finally {
      setLoading(false);
    }
  };
    {
      id: 2,
      name: "Spice Garden",
      owner: "Fatima Ali",
      email: "<EMAIL>",
      phone: "+****************",
      address: "456 Oak Ave, City, State 12345",
      cuisine: ["Indian", "Pakistani"],
      status: "approved",
      submittedAt: new Date("2024-01-10"),
      approvedAt: new Date("2024-01-12"),
      documents: {
        businessLicense: { name: "business_license.pdf", verified: true },
        foodLicense: { name: "food_safety_cert.pdf", verified: true },
        menu: { name: "restaurant_menu.pdf", verified: true },
      },
      images: {
        logo: "/api/placeholder/100/100",
        banner: "/api/placeholder/400/200",
      },
      description: "Fresh Indian and Pakistani spices with authentic flavors.",
      operatingHours: {
        opening: "11:00",
        closing: "23:00",
      },
      estimatedRevenue: "$20,000/month",
      experience: "8 years",
      notes: [
        {
          id: 1,
          author: "Admin",
          message: "All documents verified. Excellent application.",
          timestamp: new Date("2024-01-12"),
          type: "approval",
        },
      ],
    },
    {
      id: 3,
      name: "Royal Biryani",
      owner: "Omar Khan",
      email: "<EMAIL>",
      phone: "+****************",
      address: "789 Pine St, City, State 12345",
      cuisine: ["Pakistani", "Indian"],
      status: "rejected",
      submittedAt: new Date("2024-01-08"),
      rejectedAt: new Date("2024-01-11"),
      documents: {
        businessLicense: { name: "business_license.pdf", verified: false },
        foodLicense: { name: "food_safety_cert.pdf", verified: false },
        menu: { name: "restaurant_menu.pdf", verified: true },
      },
      images: {
        logo: "/api/placeholder/100/100",
        banner: "/api/placeholder/400/200",
      },
      description: "Traditional biryani and Pakistani cuisine.",
      operatingHours: {
        opening: "12:00",
        closing: "22:00",
      },
      estimatedRevenue: "$12,000/month",
      experience: "3 years",
      notes: [
        {
          id: 1,
          author: "Admin",
          message:
            "Business license expired. Please resubmit with valid documents.",
          timestamp: new Date("2024-01-11"),
          type: "rejection",
        },
      ],
    },
  ]);

  useEffect(() => {
    // Filter restaurants based on search and status
    let filtered = mockApplications;

    if (searchQuery) {
      filtered = filtered.filter(
        (restaurant) =>
          restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          restaurant.owner.toLowerCase().includes(searchQuery.toLowerCase()) ||
          restaurant.email.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter(
        (restaurant) => restaurant.status === statusFilter
      );
    }

    setFilteredRestaurants(filtered);
  }, [searchQuery, statusFilter, mockApplications]);

  const getStatusBadge = (status) => {
    switch (status) {
      case "pending":
        return <Badge variant='warning'>Pending Review</Badge>;
      case "approved":
        return <Badge variant='success'>Approved</Badge>;
      case "rejected":
        return <Badge variant='danger'>Rejected</Badge>;
      case "under_review":
        return <Badge variant='info'>Under Review</Badge>;
      default:
        return <Badge variant='secondary'>Unknown</Badge>;
    }
  };

  const handleApprove = async (restaurant) => {
    try {
      // In a real app, this would be an API call
      const updatedRestaurant = {
        ...restaurant,
        status: "approved",
        approvedAt: new Date(),
        notes: [
          ...restaurant.notes,
          {
            id: Date.now(),
            author: currentUser.name,
            message: approvalNotes || "Application approved.",
            timestamp: new Date(),
            type: "approval",
          },
        ],
      };

      // Update the restaurant status
      console.log("Approving restaurant:", updatedRestaurant);

      // Send approval email notification
      await sendApprovalNotification(restaurant.email, restaurant.name);

      setApprovalNotes("");
      setShowDetails(false);

      // Refresh the list
      // await getRestaurants();
    } catch (error) {
      console.error("Error approving restaurant:", error);
    }
  };

  const handleReject = async (restaurant) => {
    try {
      // In a real app, this would be an API call
      const updatedRestaurant = {
        ...restaurant,
        status: "rejected",
        rejectedAt: new Date(),
        notes: [
          ...restaurant.notes,
          {
            id: Date.now(),
            author: currentUser.name,
            message: rejectionReason,
            timestamp: new Date(),
            type: "rejection",
          },
        ],
      };

      // Update the restaurant status
      console.log("Rejecting restaurant:", updatedRestaurant);

      // Send rejection email notification
      await sendRejectionNotification(
        restaurant.email,
        restaurant.name,
        rejectionReason
      );

      setRejectionReason("");
      setShowRejectModal(false);
      setShowDetails(false);

      // Refresh the list
      // await getRestaurants();
    } catch (error) {
      console.error("Error rejecting restaurant:", error);
    }
  };

  const sendApprovalNotification = async (email, restaurantName) => {
    // In a real app, this would send an actual email
    console.log(`Sending approval email to ${email} for ${restaurantName}`);
  };

  const sendRejectionNotification = async (email, restaurantName, reason) => {
    // In a real app, this would send an actual email
    console.log(
      `Sending rejection email to ${email} for ${restaurantName}: ${reason}`
    );
  };

  const handleViewDetails = (restaurant) => {
    setSelectedRestaurant(restaurant);
    setShowDetails(true);
  };

  const handleDownloadDocument = (document) => {
    // In a real app, this would download the actual document
    console.log("Downloading document:", document.name);
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const statusCounts = {
    all: mockApplications.length,
    pending: mockApplications.filter((r) => r.status === "pending").length,
    approved: mockApplications.filter((r) => r.status === "approved").length,
    rejected: mockApplications.filter((r) => r.status === "rejected").length,
    under_review: mockApplications.filter((r) => r.status === "under_review")
      .length,
  };

  return (
    <div className='p-6 animate-fade-in'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6'>
        <div>
          <h1 className='text-2xl font-bold mb-2'>Restaurant Applications</h1>
          <p className='text-text-secondary'>
            Review and approve restaurant partnership applications
          </p>
        </div>
        <div className='flex items-center space-x-4'>
          <div className='relative'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={20}
            />
            <input
              type='text'
              placeholder='Search applications...'
              className='pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Status Filter Tabs */}
      <div className='flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg w-fit'>
        {[
          { key: "all", label: "All" },
          { key: "pending", label: "Pending" },
          { key: "under_review", label: "Under Review" },
          { key: "approved", label: "Approved" },
          { key: "rejected", label: "Rejected" },
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setStatusFilter(tab.key)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              statusFilter === tab.key
                ? "bg-white text-primary-600 shadow-sm"
                : "text-gray-600 hover:text-gray-900"
            }`}
          >
            {tab.label} ({statusCounts[tab.key]})
          </button>
        ))}
      </div>

      {/* Error Display */}
      {error && (
        <Card className='mb-6 border-l-4 border-red-500'>
          <div className='flex items-start p-4'>
            <AlertCircle
              size={18}
              className='text-red-500 mr-2 mt-0.5 flex-shrink-0'
            />
            <div>
              <p className='text-red-600 text-sm'>{error}</p>
              <button
                onClick={clearError}
                className='text-red-500 text-sm underline mt-1'
              >
                Dismiss
              </button>
            </div>
          </div>
        </Card>
      )}

      {/* Applications List */}
      <div className='grid gap-6'>
        {loading ? (
          <div className='text-center py-8'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto'></div>
            <p className='text-text-secondary mt-2'>Loading applications...</p>
          </div>
        ) : filteredRestaurants.length === 0 ? (
          <Card className='text-center py-12'>
            <FileText className='mx-auto text-gray-400 mb-4' size={48} />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              No applications found
            </h3>
            <p className='text-gray-500'>
              {searchQuery
                ? "Try adjusting your search criteria"
                : "No restaurant applications match the selected status"}
            </p>
          </Card>
        ) : (
          filteredRestaurants.map((restaurant) => (
            <Card key={restaurant.id} className='overflow-hidden'>
              <div className='p-6'>
                <div className='flex items-start justify-between'>
                  <div className='flex items-start space-x-4'>
                    <img
                      src={restaurant.images.logo}
                      alt={restaurant.name}
                      className='w-16 h-16 rounded-lg object-cover'
                    />
                    <div className='flex-1'>
                      <div className='flex items-center space-x-3 mb-2'>
                        <h3 className='text-xl font-semibold'>
                          {restaurant.name}
                        </h3>
                        {getStatusBadge(restaurant.status)}
                      </div>
                      <div className='space-y-1 text-sm text-gray-600'>
                        <div className='flex items-center'>
                          <Users size={16} className='mr-2' />
                          <span>Owner: {restaurant.owner}</span>
                        </div>
                        <div className='flex items-center'>
                          <Mail size={16} className='mr-2' />
                          <span>{restaurant.email}</span>
                        </div>
                        <div className='flex items-center'>
                          <Phone size={16} className='mr-2' />
                          <span>{restaurant.phone}</span>
                        </div>
                        <div className='flex items-center'>
                          <MapPin size={16} className='mr-2' />
                          <span>{restaurant.address}</span>
                        </div>
                        <div className='flex items-center'>
                          <Calendar size={16} className='mr-2' />
                          <span>
                            Applied: {formatDate(restaurant.submittedAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Button
                      variant='outline'
                      size='small'
                      onClick={() => handleViewDetails(restaurant)}
                      icon={<Eye size={16} />}
                    >
                      View Details
                    </Button>
                    {restaurant.status === "pending" && (
                      <>
                        <Button
                          variant='success'
                          size='small'
                          onClick={() => handleApprove(restaurant)}
                          icon={<CheckCircle size={16} />}
                        >
                          Approve
                        </Button>
                        <Button
                          variant='danger'
                          size='small'
                          onClick={() => {
                            setSelectedRestaurant(restaurant);
                            setShowRejectModal(true);
                          }}
                          icon={<XCircle size={16} />}
                        >
                          Reject
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {/* Quick Info */}
                <div className='mt-4 pt-4 border-t'>
                  <div className='grid grid-cols-2 md:grid-cols-4 gap-4 text-sm'>
                    <div>
                      <span className='text-gray-500'>Cuisine:</span>
                      <div className='font-medium'>
                        {restaurant.cuisine.join(", ")}
                      </div>
                    </div>
                    <div>
                      <span className='text-gray-500'>Experience:</span>
                      <div className='font-medium'>{restaurant.experience}</div>
                    </div>
                    <div>
                      <span className='text-gray-500'>Est. Revenue:</span>
                      <div className='font-medium'>
                        {restaurant.estimatedRevenue}
                      </div>
                    </div>
                    <div>
                      <span className='text-gray-500'>Documents:</span>
                      <div className='font-medium'>
                        {
                          Object.values(restaurant.documents).filter(
                            (doc) => doc.verified
                          ).length
                        }
                        /{Object.values(restaurant.documents).length} verified
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>

      {/* Restaurant Details Modal */}
      {showDetails && selectedRestaurant && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
          <div className='bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto'>
            <div className='p-6 border-b'>
              <div className='flex items-center justify-between'>
                <h2 className='text-2xl font-bold'>Application Details</h2>
                <button
                  onClick={() => setShowDetails(false)}
                  className='text-gray-400 hover:text-gray-600'
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className='p-6 space-y-6'>
              {/* Restaurant Info */}
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <h3 className='text-lg font-semibold mb-4'>
                    Restaurant Information
                  </h3>
                  <div className='space-y-3'>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Name
                      </label>
                      <p className='text-lg font-medium'>
                        {selectedRestaurant.name}
                      </p>
                    </div>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Owner
                      </label>
                      <p>{selectedRestaurant.owner}</p>
                    </div>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Email
                      </label>
                      <p>{selectedRestaurant.email}</p>
                    </div>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Phone
                      </label>
                      <p>{selectedRestaurant.phone}</p>
                    </div>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Address
                      </label>
                      <p>{selectedRestaurant.address}</p>
                    </div>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Description
                      </label>
                      <p>{selectedRestaurant.description}</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-semibold mb-4'>
                    Business Details
                  </h3>
                  <div className='space-y-3'>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Cuisine Types
                      </label>
                      <div className='flex flex-wrap gap-2 mt-1'>
                        {selectedRestaurant.cuisine.map((type, index) => (
                          <Badge key={index} variant='secondary'>
                            {type}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Operating Hours
                      </label>
                      <p>
                        {selectedRestaurant.operatingHours.opening} -{" "}
                        {selectedRestaurant.operatingHours.closing}
                      </p>
                    </div>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Experience
                      </label>
                      <p>{selectedRestaurant.experience}</p>
                    </div>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Estimated Revenue
                      </label>
                      <p>{selectedRestaurant.estimatedRevenue}</p>
                    </div>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Application Date
                      </label>
                      <p>{formatDate(selectedRestaurant.submittedAt)}</p>
                    </div>
                    <div>
                      <label className='text-sm font-medium text-gray-500'>
                        Status
                      </label>
                      <div className='mt-1'>
                        {getStatusBadge(selectedRestaurant.status)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Images */}
              <div>
                <h3 className='text-lg font-semibold mb-4'>
                  Restaurant Images
                </h3>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Logo
                    </label>
                    <img
                      src={selectedRestaurant.images.logo}
                      alt='Restaurant logo'
                      className='w-full h-32 object-cover rounded-lg border mt-2'
                    />
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Cover Photo
                    </label>
                    <img
                      src={selectedRestaurant.images.banner}
                      alt='Restaurant banner'
                      className='w-full h-32 object-cover rounded-lg border mt-2'
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default RestaurantApprovalEnhanced;
