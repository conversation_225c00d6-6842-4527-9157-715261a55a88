# api/urls.py (updated)
from django.urls import path, include
from django.urls import path
from .views import (
    UserLoginView,
    UserRegistrationView,
    VerifyEmailView,
    ResendOTPView,
    PasswordChangeView,
    RequestPasswordChangeOTPView,
    TokenRefreshAPIView
)

urlpatterns = [
    path('auth/login/', UserLoginView.as_view(), name='user-login'),
    path('auth/register/', UserRegistrationView.as_view(), name='user-registration'),
    path('auth/verify-email/', VerifyEmailView.as_view(), name='verify-email'),
    path('auth/resend-otp/', ResendOTPView.as_view(), name='resend-otp'),
    path('auth/request-password-change-otp/', RequestPasswordChangeOTPView.as_view(), name='request-password-change-otp'),
    path('auth/change-password/', PasswordChangeView.as_view(), name='change-password'),
    path('auth/refresh/', TokenRefreshAPIView.as_view(), name='token-refresh'),
    path('restaurant/', include('restaurant.urls')),
    path('order/', include('orders.urls')),
    path('config/', include('system_config.urls')),
    path('delivery-agent/', include('deliveryAgent.urls')),

]