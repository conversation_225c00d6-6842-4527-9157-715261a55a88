import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { useLoyalty } from "../../context/LoyaltyContext";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Key,
  Shield,
  Camera,
  Star,
  Gift,
  Navigation,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Input from "../../components/common/Input";
import FormControl from "../../components/common/FormControl";
import LoyaltyDashboard from "../../components/loyalty/LoyaltyDashboard";
import RewardsStore from "../../components/loyalty/RewardsStore";

const Profile = () => {
  const { user, updateProfile, logout, changePassword } = useAuth();
  const { loyaltyData } = useLoyalty();
  const [activeTab, setActiveTab] = useState("personal");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm({
    defaultValues: {
      name: user?.name || "",
      email: user?.email || "",
      phone: user?.phone || "",
      address: user?.address || "",
    },
  });

  const {
    register: registerPassword,
    handleSubmit: handleSubmitPassword,
    formState: { errors: errorsPassword },
    reset: resetPassword,
    watch: watchPassword,
  } = useForm({
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data) => {
    setLoading(true);
    setSuccess(false);

    // Simulate API call
    setTimeout(() => {
      updateProfile(data);
      setSuccess(true);
      setLoading(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
    }, 1000);
  };

  const onPasswordSubmit = async (data) => {
    setLoading(true);
    setSuccess(false);
    setError(null);

    try {
      // Validate that passwords match
      if (data.newPassword !== data.confirmPassword) {
        setError("Passwords do not match");
        setLoading(false);
        return;
      }

      // Call the real API to change password
      const result = await changePassword(
        data.currentPassword,
        data.newPassword
      );

      if (result.success) {
        setSuccess(true);
        resetPassword(); // Clear the form

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccess(false);
        }, 3000);
      } else {
        setError(result.error || "Password change failed");
      }
    } catch (err) {
      console.error("Password change error:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='container mx-auto px-4 py-8 max-w-4xl animate-fade-in'>
      <h1 className='text-2xl font-poppins font-semibold mb-8'>Your Profile</h1>

      <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
        {/* Sidebar */}
        <div className='md:col-span-1'>
          <Card className='text-center p-8'>
            <div className='relative w-24 h-24 mx-auto bg-primary-100 rounded-full flex items-center justify-center mb-4'>
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.name}
                  className='w-full h-full rounded-full object-cover'
                />
              ) : (
                <User size={40} className='text-primary-500' />
              )}
              <button className='absolute bottom-0 right-0 bg-primary-500 text-white p-1.5 rounded-full'>
                <Camera size={16} />
              </button>
            </div>

            <h2 className='font-poppins font-semibold text-xl mb-1'>
              {user.name}
            </h2>
            <p className='text-text-secondary text-sm capitalize mb-4'>
              {user.role}
            </p>

            <div className='text-left space-y-1 text-sm mb-6'>
              <div className='flex items-center text-text-secondary'>
                <Mail size={16} className='mr-2' />
                <span>{user.email}</span>
              </div>
              {user.phone && (
                <div className='flex items-center text-text-secondary'>
                  <Phone size={16} className='mr-2' />
                  <span>{user.phone}</span>
                </div>
              )}
            </div>

            <button
              className='text-accent-red hover:text-accent-red-dark font-medium text-sm'
              onClick={logout}
            >
              Sign Out
            </button>
          </Card>

          <div className='mt-6 bg-white rounded-lg shadow-sm overflow-hidden'>
            <button
              className={`w-full py-3 px-4 text-left flex items-center ${
                activeTab === "personal"
                  ? "bg-primary-50 text-primary-500 border-l-4 border-primary-500"
                  : "text-text-primary hover:bg-gray-50"
              }`}
              onClick={() => setActiveTab("personal")}
            >
              <User size={18} className='mr-2' />
              <span>Personal Information</span>
            </button>

            <button
              className={`w-full py-3 px-4 text-left flex items-center ${
                activeTab === "security"
                  ? "bg-primary-50 text-primary-500 border-l-4 border-primary-500"
                  : "text-text-primary hover:bg-gray-50"
              }`}
              onClick={() => setActiveTab("security")}
            >
              <Shield size={18} className='mr-2' />
              <span>Security</span>
            </button>

            {/* Loyalty Program Tabs - Only show for customers */}
            {user.role === "customer" && loyaltyData && (
              <>
                <button
                  className={`w-full py-3 px-4 text-left flex items-center ${
                    activeTab === "loyalty"
                      ? "bg-primary-50 text-primary-500 border-l-4 border-primary-500"
                      : "text-text-primary hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveTab("loyalty")}
                >
                  <Star size={18} className='mr-2' />
                  <span>Loyalty Program</span>
                </button>

                <button
                  className={`w-full py-3 px-4 text-left flex items-center ${
                    activeTab === "rewards"
                      ? "bg-primary-50 text-primary-500 border-l-4 border-primary-500"
                      : "text-text-primary hover:bg-gray-50"
                  }`}
                  onClick={() => setActiveTab("rewards")}
                >
                  <Gift size={18} className='mr-2' />
                  <span>Rewards Store</span>
                </button>
              </>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className='md:col-span-2'>
          {activeTab === "personal" && (
            <Card>
              <h2 className='text-xl font-poppins font-semibold mb-6'>
                Personal Information
              </h2>

              {success && (
                <div className='mb-6 p-4 bg-green-50 border-l-4 border-accent-green rounded-md'>
                  <p className='text-accent-green-dark'>
                    Your profile has been updated successfully.
                  </p>
                </div>
              )}

              <form onSubmit={handleSubmit(onSubmit)}>
                <div className='space-y-4'>
                  <FormControl
                    label='Full Name'
                    htmlFor='name'
                    error={errors.name?.message}
                    required
                  >
                    <Input
                      id='name'
                      placeholder='Enter your full name'
                      icon={<User size={18} />}
                      error={errors.name?.message}
                      {...register("name", {
                        required: "Full name is required",
                      })}
                    />
                  </FormControl>

                  <FormControl
                    label='Email Address'
                    htmlFor='email'
                    error={errors.email?.message}
                    required
                  >
                    <Input
                      id='email'
                      type='email'
                      placeholder='Enter your email address'
                      icon={<Mail size={18} />}
                      error={errors.email?.message}
                      disabled
                      {...register("email")}
                    />
                    <p className='mt-1 text-xs text-text-secondary'>
                      Email address cannot be changed.
                    </p>
                  </FormControl>

                  <FormControl
                    label='Phone Number'
                    htmlFor='phone'
                    error={errors.phone?.message}
                  >
                    <Input
                      id='phone'
                      placeholder='Enter your phone number'
                      icon={<Phone size={18} />}
                      error={errors.phone?.message}
                      {...register("phone", {
                        pattern: {
                          value: /^[0-9+\-\s()]*$/,
                          message: "Please enter a valid phone number",
                        },
                      })}
                    />
                  </FormControl>

                  <FormControl
                    label='Delivery Address'
                    htmlFor='address'
                    error={errors.address?.message}
                  >
                    <textarea
                      id='address'
                      placeholder='Enter your delivery address'
                      className={`w-full px-4 py-2 bg-white border rounded-md outline-none transition-colors duration-200 resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                        errors.address ? "border-accent-red" : "border-gray-300"
                      }`}
                      rows={3}
                      {...register("address")}
                    ></textarea>

                    {/* My Addresses Link */}
                    <div className='mt-2'>
                      <Link
                        to='/addresses'
                        className='inline-flex items-center text-sm text-primary-600 hover:text-primary-700 transition-colors'
                      >
                        <Navigation size={14} className='mr-1' />
                        Manage My Addresses
                      </Link>
                    </div>
                  </FormControl>
                </div>

                <div className='mt-6 flex justify-end'>
                  <Button
                    type='button'
                    variant='secondary'
                    className='mr-3'
                    onClick={() =>
                      reset({
                        name: user.name,
                        email: user.email,
                        phone: user.phone,
                        address: user.address,
                      })
                    }
                  >
                    Cancel
                  </Button>
                  <Button type='submit' variant='primary' loading={loading}>
                    Save Changes
                  </Button>
                </div>
              </form>
            </Card>
          )}

          {activeTab === "security" && (
            <Card>
              <h2 className='text-xl font-poppins font-semibold mb-6'>
                Security
              </h2>

              {success && (
                <div className='mb-6 p-4 bg-green-50 border-l-4 border-accent-green rounded-md'>
                  <p className='text-accent-green-dark'>
                    Your password has been updated successfully.
                  </p>
                </div>
              )}

              {error && (
                <div className='mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-md'>
                  <p className='text-red-700'>{error}</p>
                </div>
              )}

              <form onSubmit={handleSubmitPassword(onPasswordSubmit)}>
                <div className='space-y-4'>
                  <FormControl
                    label='Current Password'
                    htmlFor='currentPassword'
                    error={errorsPassword.currentPassword?.message}
                    required
                  >
                    <Input
                      id='currentPassword'
                      type='password'
                      placeholder='Enter your current password'
                      icon={<Key size={18} />}
                      error={errorsPassword.currentPassword?.message}
                      {...registerPassword("currentPassword", {
                        required: "Current password is required",
                      })}
                    />
                  </FormControl>

                  <FormControl
                    label='New Password'
                    htmlFor='newPassword'
                    error={errorsPassword.newPassword?.message}
                    required
                  >
                    <Input
                      id='newPassword'
                      type='password'
                      placeholder='Enter your new password'
                      icon={<Key size={18} />}
                      error={errorsPassword.newPassword?.message}
                      {...registerPassword("newPassword", {
                        required: "New password is required",
                        minLength: {
                          value: 6,
                          message: "Password must be at least 6 characters",
                        },
                      })}
                    />
                  </FormControl>

                  <FormControl
                    label='Confirm New Password'
                    htmlFor='confirmPassword'
                    error={errorsPassword.confirmPassword?.message}
                    required
                  >
                    <Input
                      id='confirmPassword'
                      type='password'
                      placeholder='Confirm your new password'
                      icon={<Key size={18} />}
                      error={errorsPassword.confirmPassword?.message}
                      {...registerPassword("confirmPassword", {
                        required: "Please confirm your new password",
                        validate: (value) =>
                          value === watchPassword("newPassword") ||
                          "Passwords do not match",
                      })}
                    />
                  </FormControl>
                </div>

                <div className='mt-6 flex justify-end'>
                  <Button
                    type='button'
                    variant='secondary'
                    className='mr-3'
                    onClick={() => {
                      resetPassword();
                      setError(null);
                      setSuccess(false);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button type='submit' variant='primary' loading={loading}>
                    Update Password
                  </Button>
                </div>
              </form>
            </Card>
          )}

          {/* Loyalty Program Tab */}
          {activeTab === "loyalty" && user.role === "customer" && (
            <LoyaltyDashboard />
          )}

          {/* Rewards Store Tab */}
          {activeTab === "rewards" && user.role === "customer" && (
            <RewardsStore />
          )}
        </div>
      </div>
    </div>
  );
};

export default Profile;
