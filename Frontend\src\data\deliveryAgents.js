// Mock delivery agents data
export const mockDeliveryAgents = [
  {
    id: "agent-1",
    userId: 4, // <PERSON> - delivery_agent1
    name: "<PERSON>",
    phone: "+93 70 123 0004",
    email: "<EMAIL>",
    avatar: "/placeholder-avatar.jpg",
    rating: 4.8,
    totalDeliveries: 1250,
    vehicleType: "motorcycle",
    vehicleNumber: "KBL-123",
    status: "available", // available, busy, offline
    currentLocation: {
      lat: 34.5553,
      lng: 69.2075,
      address: "Shar-e-Naw, Kabul",
    },
    isOnline: true,
    lastSeen: new Date().toISOString(),
    joinedDate: "2023-01-15",
    completedOrders: 1180,
    cancelledOrders: 15,
    averageDeliveryTime: 28, // minutes
    earnings: {
      today: 850.0,
      thisWeek: 4200.0,
      thisMonth: 18500.0,
    },
    deliveryZones: ["Central Kabul", "<PERSON>har-e-<PERSON>w", "<PERSON><PERSON><PERSON>"],
    vehicleColor: "Black",
    bankName: "Afghanistan Bank",
    accountNumber: "****1234",
    accountName: "<PERSON>",
  },
  {
    id: "agent-2",
    userId: 5, // <PERSON> - delivery_agent2
    name: "<PERSON>",
    phone: "+93 70 123 0005",
    email: "<EMAIL>",
    avatar: "/placeholder-avatar.jpg",
    rating: 4.9,
    totalDeliveries: 980,
    vehicleType: "bicycle",
    vehicleNumber: "BIC-456",
    status: "busy",
    currentLocation: {
      lat: 34.5583,
      lng: 69.2105,
      address: "Wazir Akbar Khan, Kabul",
    },
    isOnline: true,
    lastSeen: new Date().toISOString(),
    joinedDate: "2023-03-20",
    completedOrders: 945,
    cancelledOrders: 8,
    averageDeliveryTime: 25,
    earnings: {
      today: 720.0,
      thisWeek: 3800.0,
      thisMonth: 16200.0,
    },
    deliveryZones: ["Wazir Akbar Khan", "Taimani"],
    vehicleColor: "Blue",
    bankName: "Da Afghanistan Bank",
    accountNumber: "****5678",
    accountName: "Omar Karimi",
  },
  {
    id: "agent-3",
    userId: 6, // Additional test user
    name: "Mohammad Nazir",
    phone: "+93 70 345 6789",
    email: "<EMAIL>",
    avatar: "/placeholder-avatar.jpg",
    rating: 4.7,
    totalDeliveries: 1450,
    vehicleType: "motorcycle",
    vehicleNumber: "KBL-789",
    status: "available",
    currentLocation: {
      lat: 34.5523,
      lng: 69.2045,
      address: "Karte Char, Kabul",
    },
    isOnline: true,
    lastSeen: new Date().toISOString(),
    joinedDate: "2022-11-10",
    completedOrders: 1380,
    cancelledOrders: 22,
    averageDeliveryTime: 30,
    earnings: {
      today: 950.0,
      thisWeek: 4500.0,
      thisMonth: 19800.0,
    },
    deliveryZones: ["Karte Char", "Central Kabul", "North Kabul"],
    vehicleColor: "Red",
    bankName: "Azizi Bank",
    accountNumber: "****9012",
    accountName: "Mohammad Nazir",
  },
  {
    id: "agent-4",
    userId: 4, // Add userId field
    name: "Zahra Karimi",
    phone: "+93 70 456 7890",
    email: "<EMAIL>",
    avatar: "/placeholder-avatar.jpg",
    rating: 4.6,
    totalDeliveries: 750,
    vehicleType: "car",
    vehicleNumber: "KBL-321",
    status: "offline",
    currentLocation: {
      lat: 34.5493,
      lng: 69.1985,
      address: "Khair Khana, Kabul",
    },
    isOnline: false,
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    joinedDate: "2023-06-05",
    completedOrders: 720,
    cancelledOrders: 12,
    averageDeliveryTime: 35,
    earnings: {
      today: 0.0,
      thisWeek: 3200.0,
      thisMonth: 14500.0,
    },
    deliveryZones: ["Khair Khana", "West Kabul"],
    vehicleColor: "White",
    bankName: "First MicroFinance Bank",
    accountNumber: "****3456",
    accountName: "Zahra Karimi",
  },
  {
    id: "agent-5",
    userId: 5, // Add userId field
    name: "Hassan Rahimi",
    phone: "+93 70 567 8901",
    email: "<EMAIL>",
    avatar: "/placeholder-avatar.jpg",
    rating: 4.8,
    totalDeliveries: 1100,
    vehicleType: "motorcycle",
    vehicleNumber: "KBL-654",
    status: "busy",
    currentLocation: {
      lat: 34.5613,
      lng: 69.2135,
      address: "Taimani, Kabul",
    },
    isOnline: true,
    lastSeen: new Date().toISOString(),
    joinedDate: "2023-02-28",
    completedOrders: 1050,
    cancelledOrders: 18,
    averageDeliveryTime: 27,
    earnings: {
      today: 800.0,
      thisWeek: 4100.0,
      thisMonth: 17300.0,
    },
    deliveryZones: ["Taimani", "Central Kabul", "Shar-e-Naw"],
    vehicleColor: "Green",
    bankName: "Maiwand Bank",
    accountNumber: "****7890",
    accountName: "Hassan Rahimi",
  },
];

// Helper functions
export const getAvailableAgents = () => {
  return mockDeliveryAgents.filter(
    (agent) => agent.status === "available" && agent.isOnline
  );
};

export const getBusyAgents = () => {
  return mockDeliveryAgents.filter(
    (agent) => agent.status === "busy" && agent.isOnline
  );
};

export const getOfflineAgents = () => {
  return mockDeliveryAgents.filter(
    (agent) => !agent.isOnline || agent.status === "offline"
  );
};

export const getAgentById = (id) => {
  return mockDeliveryAgents.find((agent) => agent.id === id);
};

export const getAgentsByVehicleType = (vehicleType) => {
  return mockDeliveryAgents.filter(
    (agent) => agent.vehicleType === vehicleType
  );
};

// Mock delivery orders data
export const mockDeliveryOrders = [
  {
    id: "order-1",
    agentId: "agent-1",
    customerId: "customer-1",
    restaurantId: "restaurant-1",
    customerName: "Ali Ahmad",
    customerPhone: "+93 70 111 2222",
    restaurantName: "Kabul Kitchen",
    pickupAddress: "Shar-e-Naw, Kabul",
    deliveryAddress: "Wazir Akbar Khan, Kabul",
    orderTotal: 1250.0,
    deliveryFee: 150.0,
    status: "assigned", // available, assigned, pickedUp, delivered, cancelled
    estimatedDeliveryTime: 30,
    actualDeliveryTime: null,
    assignedAt: new Date().toISOString(),
    pickedUpAt: null,
    deliveredAt: null,
    items: [
      { name: "Kabuli Pulao", quantity: 2, price: 500.0 },
      { name: "Mantu", quantity: 1, price: 250.0 },
    ],
  },
  {
    id: "order-2",
    agentId: "agent-2",
    customerId: "customer-2",
    restaurantId: "restaurant-2",
    customerName: "Fatima Khan",
    customerPhone: "+93 70 222 3333",
    restaurantName: "Afghan Delights",
    pickupAddress: "Karte Char, Kabul",
    deliveryAddress: "Taimani, Kabul",
    orderTotal: 800.0,
    deliveryFee: 100.0,
    status: "pickedUp",
    estimatedDeliveryTime: 25,
    actualDeliveryTime: null,
    assignedAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    pickedUpAt: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
    deliveredAt: null,
    items: [
      { name: "Qabili Palau", quantity: 1, price: 450.0 },
      { name: "Bolani", quantity: 2, price: 175.0 },
    ],
  },
  {
    id: "order-3",
    agentId: "agent-1",
    customerId: "customer-3",
    restaurantId: "restaurant-3",
    customerName: "Mohammad Nazir",
    customerPhone: "+93 70 333 4444",
    restaurantName: "Herat House",
    pickupAddress: "Khair Khana, Kabul",
    deliveryAddress: "Shar-e-Naw, Kabul",
    orderTotal: 950.0,
    deliveryFee: 120.0,
    status: "delivered",
    estimatedDeliveryTime: 35,
    actualDeliveryTime: 32,
    assignedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    pickedUpAt: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
    deliveredAt:
      new Date().toISOString().split("T")[0] +
      "T" +
      new Date(Date.now() - 30 * 60 * 1000).toISOString().split("T")[1],
    items: [
      { name: "Lamb Karahi", quantity: 1, price: 600.0 },
      { name: "Naan", quantity: 3, price: 150.0 },
      { name: "Green Tea", quantity: 2, price: 100.0 },
    ],
  },
  {
    id: "order-4",
    agentId: null,
    customerId: "customer-4",
    restaurantId: "restaurant-4",
    customerName: "Zahra Ahmadi",
    customerPhone: "+93 70 444 5555",
    restaurantName: "Mazar Cuisine",
    pickupAddress: "Taimani, Kabul",
    deliveryAddress: "Wazir Akbar Khan, Kabul",
    orderTotal: 1100.0,
    deliveryFee: 130.0,
    status: "available",
    estimatedDeliveryTime: 28,
    actualDeliveryTime: null,
    assignedAt: null,
    pickedUpAt: null,
    deliveredAt: null,
    items: [
      { name: "Fish Curry", quantity: 1, price: 550.0 },
      { name: "Rice", quantity: 2, price: 200.0 },
      { name: "Salad", quantity: 1, price: 150.0 },
    ],
  },
  {
    id: "order-5",
    agentId: null,
    customerId: "customer-5",
    restaurantId: "restaurant-5",
    customerName: "Hassan Karimi",
    customerPhone: "+93 70 555 6666",
    restaurantName: "Kandahar Grill",
    pickupAddress: "Karte Char, Kabul",
    deliveryAddress: "Khair Khana, Kabul",
    orderTotal: 750.0,
    deliveryFee: 110.0,
    status: "available",
    estimatedDeliveryTime: 40,
    actualDeliveryTime: null,
    assignedAt: null,
    pickedUpAt: null,
    deliveredAt: null,
    items: [
      { name: "Chicken Tikka", quantity: 2, price: 400.0 },
      { name: "Bread", quantity: 2, price: 100.0 },
      { name: "Yogurt Drink", quantity: 2, price: 150.0 },
    ],
  },
];

// Mock delivery earnings data
export const mockDeliveryEarnings = [
  {
    id: "earnings-1",
    agentId: "agent-1",
    date: new Date().toISOString().split("T")[0], // Today
    totalOrders: 8,
    totalEarnings: 850.0,
    baseFee: 600.0,
    distanceBonus: 150.0,
    timeBonus: 50.0,
    tips: 50.0,
    platformCommission: 180.0,
    netEarnings: 670.0,
    hoursWorked: 8.5,
    averageDeliveryTime: 28,
  },
  {
    id: "earnings-2",
    agentId: "agent-2",
    date: new Date().toISOString().split("T")[0], // Today
    totalOrders: 6,
    totalEarnings: 720.0,
    baseFee: 450.0,
    distanceBonus: 120.0,
    timeBonus: 80.0,
    tips: 70.0,
    platformCommission: 162.0,
    netEarnings: 558.0,
    hoursWorked: 7.0,
    averageDeliveryTime: 25,
  },
  {
    id: "earnings-3",
    agentId: "agent-3",
    date: new Date().toISOString().split("T")[0], // Today
    totalOrders: 10,
    totalEarnings: 950.0,
    baseFee: 750.0,
    distanceBonus: 100.0,
    timeBonus: 50.0,
    tips: 50.0,
    platformCommission: 210.0,
    netEarnings: 740.0,
    hoursWorked: 9.0,
    averageDeliveryTime: 30,
  },
  {
    id: "earnings-4",
    agentId: "agent-4",
    date: new Date().toISOString().split("T")[0], // Today
    totalOrders: 0,
    totalEarnings: 0.0,
    baseFee: 0.0,
    distanceBonus: 0.0,
    timeBonus: 0.0,
    tips: 0.0,
    platformCommission: 0.0,
    netEarnings: 0.0,
    hoursWorked: 0,
    averageDeliveryTime: 0,
  },
  {
    id: "earnings-5",
    agentId: "agent-5",
    date: new Date().toISOString().split("T")[0], // Today
    totalOrders: 7,
    totalEarnings: 800.0,
    baseFee: 525.0,
    distanceBonus: 140.0,
    timeBonus: 70.0,
    tips: 65.0,
    platformCommission: 168.0,
    netEarnings: 632.0,
    hoursWorked: 8.0,
    averageDeliveryTime: 27,
  },
];

// Helper functions for orders and earnings
export const getAvailableOrders = () => {
  return mockDeliveryOrders.filter((order) => order.status === "available");
};

export const getAgentActiveOrders = (agentId) => {
  return mockDeliveryOrders.filter(
    (order) =>
      order.agentId === agentId &&
      ["assigned", "pickedUp"].includes(order.status)
  );
};

export const getAgentCompletedOrders = (agentId, date = null) => {
  const targetDate = date || new Date().toISOString().split("T")[0];
  return mockDeliveryOrders.filter(
    (order) =>
      order.agentId === agentId &&
      order.status === "delivered" &&
      order.deliveredAt &&
      order.deliveredAt.startsWith(targetDate)
  );
};

export const getAgentEarnings = (agentId, date = null) => {
  const targetDate = date || new Date().toISOString().split("T")[0];
  return mockDeliveryEarnings.find(
    (earnings) => earnings.agentId === agentId && earnings.date === targetDate
  );
};

export const getTopRatedAgents = (limit = 5) => {
  return mockDeliveryAgents.sort((a, b) => b.rating - a.rating).slice(0, limit);
};

export const getAgentStats = () => {
  const total = mockDeliveryAgents.length;
  const available = getAvailableAgents().length;
  const busy = getBusyAgents().length;
  const offline = getOfflineAgents().length;

  return {
    total,
    available,
    busy,
    offline,
    onlinePercentage: (((available + busy) / total) * 100).toFixed(1),
  };
};

export default mockDeliveryAgents;
