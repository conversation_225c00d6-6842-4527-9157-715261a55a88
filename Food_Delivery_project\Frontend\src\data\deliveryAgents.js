// Mock delivery agents data
export const mockDeliveryAgents = [
  {
    id: "agent-1",
    name: "<PERSON>",
    phone: "+93 70 123 4567",
    email: "<EMAIL>",
    avatar: "/placeholder-avatar.jpg",
    rating: 4.8,
    totalDeliveries: 1250,
    vehicleType: "motorcycle",
    vehicleNumber: "KBL-123",
    status: "available", // available, busy, offline
    currentLocation: {
      lat: 34.5553,
      lng: 69.2075,
      address: "Shar-e-Naw, Kabul"
    },
    isOnline: true,
    lastSeen: new Date().toISOString(),
    joinedDate: "2023-01-15",
    completedOrders: 1180,
    cancelledOrders: 15,
    averageDeliveryTime: 28, // minutes
    earnings: {
      today: 850.00,
      thisWeek: 4200.00,
      thisMonth: 18500.00
    }
  },
  {
    id: "agent-2", 
    name: "<PERSON><PERSON>",
    phone: "+93 70 234 5678",
    email: "<EMAIL>",
    avatar: "/placeholder-avatar.jpg",
    rating: 4.9,
    totalDeliveries: 980,
    vehicleType: "bicycle",
    vehicleNumber: "BIC-456",
    status: "busy",
    currentLocation: {
      lat: 34.5583,
      lng: 69.2105,
      address: "Wazir <PERSON>, Kabul"
    },
    isOnline: true,
    lastSeen: new Date().toISOString(),
    joinedDate: "2023-03-20",
    completedOrders: 945,
    cancelledOrders: 8,
    averageDeliveryTime: 25,
    earnings: {
      today: 720.00,
      thisWeek: 3800.00,
      thisMonth: 16200.00
    }
  },
  {
    id: "agent-3",
    name: "Mohammad Nazir",
    phone: "+93 70 345 6789", 
    email: "<EMAIL>",
    avatar: "/placeholder-avatar.jpg",
    rating: 4.7,
    totalDeliveries: 1450,
    vehicleType: "motorcycle",
    vehicleNumber: "KBL-789",
    status: "available",
    currentLocation: {
      lat: 34.5523,
      lng: 69.2045,
      address: "Karte Char, Kabul"
    },
    isOnline: true,
    lastSeen: new Date().toISOString(),
    joinedDate: "2022-11-10",
    completedOrders: 1380,
    cancelledOrders: 22,
    averageDeliveryTime: 30,
    earnings: {
      today: 950.00,
      thisWeek: 4500.00,
      thisMonth: 19800.00
    }
  },
  {
    id: "agent-4",
    name: "Zahra Karimi",
    phone: "+93 70 456 7890",
    email: "<EMAIL>", 
    avatar: "/placeholder-avatar.jpg",
    rating: 4.6,
    totalDeliveries: 750,
    vehicleType: "car",
    vehicleNumber: "KBL-321",
    status: "offline",
    currentLocation: {
      lat: 34.5493,
      lng: 69.1985,
      address: "Khair Khana, Kabul"
    },
    isOnline: false,
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    joinedDate: "2023-06-05",
    completedOrders: 720,
    cancelledOrders: 12,
    averageDeliveryTime: 35,
    earnings: {
      today: 0.00,
      thisWeek: 3200.00,
      thisMonth: 14500.00
    }
  },
  {
    id: "agent-5",
    name: "Hassan Rahimi",
    phone: "+93 70 567 8901",
    email: "<EMAIL>",
    avatar: "/placeholder-avatar.jpg", 
    rating: 4.8,
    totalDeliveries: 1100,
    vehicleType: "motorcycle",
    vehicleNumber: "KBL-654",
    status: "busy",
    currentLocation: {
      lat: 34.5613,
      lng: 69.2135,
      address: "Taimani, Kabul"
    },
    isOnline: true,
    lastSeen: new Date().toISOString(),
    joinedDate: "2023-02-28",
    completedOrders: 1050,
    cancelledOrders: 18,
    averageDeliveryTime: 27,
    earnings: {
      today: 800.00,
      thisWeek: 4100.00,
      thisMonth: 17300.00
    }
  }
];

// Helper functions
export const getAvailableAgents = () => {
  return mockDeliveryAgents.filter(agent => 
    agent.status === "available" && agent.isOnline
  );
};

export const getBusyAgents = () => {
  return mockDeliveryAgents.filter(agent => 
    agent.status === "busy" && agent.isOnline
  );
};

export const getOfflineAgents = () => {
  return mockDeliveryAgents.filter(agent => 
    !agent.isOnline || agent.status === "offline"
  );
};

export const getAgentById = (id) => {
  return mockDeliveryAgents.find(agent => agent.id === id);
};

export const getAgentsByVehicleType = (vehicleType) => {
  return mockDeliveryAgents.filter(agent => agent.vehicleType === vehicleType);
};

export const getTopRatedAgents = (limit = 5) => {
  return mockDeliveryAgents
    .sort((a, b) => b.rating - a.rating)
    .slice(0, limit);
};

export const getAgentStats = () => {
  const total = mockDeliveryAgents.length;
  const available = getAvailableAgents().length;
  const busy = getBusyAgents().length;
  const offline = getOfflineAgents().length;
  
  return {
    total,
    available,
    busy,
    offline,
    onlinePercentage: ((available + busy) / total * 100).toFixed(1)
  };
};

// Mock delivery orders data
export const mockDeliveryOrders = [
  {
    id: "order-1",
    customerId: "customer-1",
    customerName: "Ali Ahmad",
    customerPhone: "+93 70 123 4567",
    restaurantId: "restaurant-1",
    restaurantName: "Kabul Kitchen",
    deliveryAgentId: "agent-1",
    status: "outForDelivery",
    orderDate: new Date().toISOString(),
    deliveryAddress: "House 123, Street 5, Shar-e-Naw, Kabul",
    totalAmount: 850.00,
    deliveryFee: 50.00,
    estimatedDeliveryTime: 25,
    items: [
      { name: "Kabuli Pulao", quantity: 2, price: 400.00 },
      { name: "Mantu", quantity: 1, price: 300.00 }
    ]
  },
  {
    id: "order-2",
    customerId: "customer-2",
    customerName: "Fatima Khan",
    customerPhone: "+93 70 234 5678",
    restaurantId: "restaurant-2",
    restaurantName: "Afghan Delights",
    deliveryAgentId: "agent-2",
    status: "preparing",
    orderDate: new Date().toISOString(),
    deliveryAddress: "Apartment 45, Wazir Akbar Khan, Kabul",
    totalAmount: 720.00,
    deliveryFee: 40.00,
    estimatedDeliveryTime: 30,
    items: [
      { name: "Qorma Chalaw", quantity: 1, price: 350.00 },
      { name: "Bolani", quantity: 2, price: 150.00 }
    ]
  }
];

// Mock delivery earnings data
export const mockDeliveryEarnings = [
  {
    agentId: "agent-1",
    date: new Date().toISOString().split('T')[0],
    orders: 12,
    earnings: 850.00,
    tips: 120.00,
    totalEarnings: 970.00
  },
  {
    agentId: "agent-2",
    date: new Date().toISOString().split('T')[0],
    orders: 10,
    earnings: 720.00,
    tips: 80.00,
    totalEarnings: 800.00
  },
  {
    agentId: "agent-3",
    date: new Date().toISOString().split('T')[0],
    orders: 15,
    earnings: 950.00,
    tips: 150.00,
    totalEarnings: 1100.00
  }
];

export default mockDeliveryAgents;
