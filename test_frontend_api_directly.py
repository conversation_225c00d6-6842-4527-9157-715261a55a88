#!/usr/bin/env python3
"""
Test the frontend API time formatting directly
"""

import requests
import json
import time

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"

def test_time_formatting():
    """Test the time formatting logic from the frontend API"""
    
    print("🧪 Testing Time Formatting Logic")
    print("=" * 50)
    
    # Login with existing user
    login_data = {
        "user_name": "testrestaurant",
        "password": "password123"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print("🔐 Logging in...")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=headers,
            data=json.dumps(login_data)
        )
        
        if response.status_code != 200:
            print(f"❌ Login failed: {response.text}")
            return False
        
        result = response.json()
        token = result['data']['access_token']
        print("✅ Login successful!")
        
        # Test the time formatting logic
        auth_headers = {
            "Authorization": f"Bearer {token}"
        }
        
        timestamp = int(time.time())
        
        # Test with properly formatted times (HH:MM:SS)
        print(f"\n🧪 Test 1: Properly formatted times (HH:MM:SS)")
        
        form_data_1 = {
            "name": f"Time Test 1 - {timestamp}",
            "description": "Testing with HH:MM:SS format",
            "contact_number": f"+93701{timestamp % 100000:05d}",
            "opening_time": "09:00:00",  # Already properly formatted
            "closing_time": "22:00:00",  # Already properly formatted
            "delivery_fee": "75.00",
            "address": json.dumps({
                "street": f"Time Test Street 1 - {timestamp}",
                "city": "Kabul",
                "state": "Kabul",
                "postal_code": f"{timestamp % 10000}",
                "country": "Afghanistan",
                "latitude": 34.5553,
                "longitude": 69.2075
            })
        }
        
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            files={'dummy': ('', '', 'application/octet-stream')},
            data=form_data_1
        )
        
        print(f"   📡 Status: {response.status_code}")
        if response.status_code == 201:
            print("   ✅ HH:MM:SS format works!")
        else:
            print(f"   ❌ HH:MM:SS format failed: {response.text}")
        
        # Test with frontend format (HH:MM) - this should fail without conversion
        print(f"\n🧪 Test 2: Frontend format (HH:MM) - should fail")
        
        form_data_2 = {
            "name": f"Time Test 2 - {timestamp}",
            "description": "Testing with HH:MM format (should fail)",
            "contact_number": f"+93701{(timestamp + 1) % 100000:05d}",
            "opening_time": "09:00",  # Frontend format - should fail
            "closing_time": "22:00",  # Frontend format - should fail
            "delivery_fee": "75.00",
            "address": json.dumps({
                "street": f"Time Test Street 2 - {timestamp}",
                "city": "Kabul",
                "state": "Kabul",
                "postal_code": f"{(timestamp + 1) % 10000}",
                "country": "Afghanistan",
                "latitude": 34.5553,
                "longitude": 69.2075
            })
        }
        
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            files={'dummy': ('', '', 'application/octet-stream')},
            data=form_data_2
        )
        
        print(f"   📡 Status: {response.status_code}")
        if response.status_code == 400:
            print("   ✅ HH:MM format correctly fails (as expected)")
            print(f"   📄 Error: {response.text}")
        else:
            print(f"   ❌ HH:MM format unexpectedly succeeded: {response.text}")
        
        # Test manual time conversion
        print(f"\n🧪 Test 3: Manual time conversion (HH:MM -> HH:MM:SS)")
        
        def format_time(time_str):
            """Format time from HH:MM to HH:MM:SS"""
            if not time_str:
                return None
            return time_str + ":00" if ":" in time_str and len(time_str.split(":")) == 2 else time_str
        
        form_data_3 = {
            "name": f"Time Test 3 - {timestamp}",
            "description": "Testing with manual time conversion",
            "contact_number": f"+93701{(timestamp + 2) % 100000:05d}",
            "opening_time": format_time("09:00"),  # Manually converted
            "closing_time": format_time("22:00"),  # Manually converted
            "delivery_fee": "75.00",
            "address": json.dumps({
                "street": f"Time Test Street 3 - {timestamp}",
                "city": "Kabul",
                "state": "Kabul",
                "postal_code": f"{(timestamp + 2) % 10000}",
                "country": "Afghanistan",
                "latitude": 34.5553,
                "longitude": 69.2075
            })
        }
        
        print(f"   🔍 Converted times:")
        print(f"      opening_time: '09:00' -> '{form_data_3['opening_time']}'")
        print(f"      closing_time: '22:00' -> '{form_data_3['closing_time']}'")
        
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            files={'dummy': ('', '', 'application/octet-stream')},
            data=form_data_3
        )
        
        print(f"   📡 Status: {response.status_code}")
        if response.status_code == 201:
            print("   ✅ Manual time conversion works!")
            result = response.json()
            print(f"      Opening time saved as: {result.get('opening_time')}")
            print(f"      Closing time saved as: {result.get('closing_time')}")
            return True
        else:
            print(f"   ❌ Manual time conversion failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main test function"""
    success = test_time_formatting()
    
    print("\n" + "=" * 50)
    print("🏁 TIME FORMATTING TEST RESULTS")
    print("=" * 50)
    
    if success:
        print("🎉 Time formatting logic works correctly!")
        print("✅ The frontend needs to convert HH:MM to HH:MM:SS")
    else:
        print("❌ Time formatting has issues")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
