import React from 'react';
import { MapPin, Clock, DollarSign, Info, Zap, Gift } from 'lucide-react';
import { useDeliveryZones } from '../../hooks/useDeliveryFee';
import { DELIVERY_CONFIG } from '../../utils/deliveryCalculator';
import Card from '../../components/common/Card';
import Badge from '../../components/common/Badge';

const DeliveryZones = () => {
  const { zones, maxDistance, freeDeliveryThreshold } = useDeliveryZones();

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl animate-fade-in">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Delivery Zones & Pricing</h1>
        <p className="text-gray-600">
          Our delivery fees are calculated based on distance to ensure fair pricing for everyone
        </p>
      </div>

      {/* Key Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="p-6 text-center">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <MapPin size={24} className="text-blue-600" />
          </div>
          <h3 className="font-semibold mb-2">Maximum Distance</h3>
          <p className="text-2xl font-bold text-blue-600 mb-1">{maxDistance} km</p>
          <p className="text-sm text-gray-600">We deliver within Kabul city limits</p>
        </Card>

        <Card className="p-6 text-center">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Gift size={24} className="text-green-600" />
          </div>
          <h3 className="font-semibold mb-2">Free Delivery</h3>
          <p className="text-2xl font-bold text-green-600 mb-1">${freeDeliveryThreshold}+</p>
          <p className="text-sm text-gray-600">Orders above this amount get free delivery</p>
        </Card>

        <Card className="p-6 text-center">
          <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Zap size={24} className="text-orange-600" />
          </div>
          <h3 className="font-semibold mb-2">Express Delivery</h3>
          <p className="text-2xl font-bold text-orange-600 mb-1">+80%</p>
          <p className="text-sm text-gray-600">Get your order 30% faster</p>
        </Card>
      </div>

      {/* Delivery Zones */}
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-semibold mb-6 flex items-center">
          <MapPin size={20} className="mr-2 text-primary-500" />
          Delivery Zones
        </h2>
        
        <div className="space-y-4">
          {zones.map((zone, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-semibold ${
                  index === 0 ? 'bg-green-500' :
                  index === 1 ? 'bg-blue-500' :
                  index === 2 ? 'bg-yellow-500' :
                  index === 3 ? 'bg-orange-500' : 'bg-red-500'
                }`}>
                  {index + 1}
                </div>
                
                <div>
                  <h3 className="font-medium">{zone.name}</h3>
                  <p className="text-sm text-gray-600">{zone.description}</p>
                </div>
              </div>
              
              <div className="text-right">
                <div className="font-semibold text-lg">${zone.fee.toFixed(2)}</div>
                <div className="text-sm text-gray-500">delivery fee</div>
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Pricing Factors */}
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-semibold mb-6 flex items-center">
          <DollarSign size={20} className="mr-2 text-primary-500" />
          Pricing Factors
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium mb-3">Base Factors</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Distance from restaurant</span>
                <Badge variant="primary">Primary</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Order amount</span>
                <Badge variant="secondary">Discount factor</Badge>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="font-medium mb-3">Additional Charges</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Peak hours (11AM-2PM, 6PM-9PM)</span>
                <Badge variant="warning">+{((DELIVERY_CONFIG.peakHourMultiplier - 1) * 100).toFixed(0)}%</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Weekend delivery</span>
                <Badge variant="warning">+{((DELIVERY_CONFIG.weekendMultiplier - 1) * 100).toFixed(0)}%</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Bad weather conditions</span>
                <Badge variant="warning">+{((DELIVERY_CONFIG.badWeatherMultiplier - 1) * 100).toFixed(0)}%</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-700">Express delivery</span>
                <Badge variant="warning">+{((DELIVERY_CONFIG.expressMultiplier - 1) * 100).toFixed(0)}%</Badge>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* How It Works */}
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-semibold mb-6 flex items-center">
          <Info size={20} className="mr-2 text-primary-500" />
          How Dynamic Pricing Works
        </h2>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-semibold mt-1">
              1
            </div>
            <div>
              <h3 className="font-medium">Distance Calculation</h3>
              <p className="text-gray-600 text-sm">
                We calculate the exact distance between the restaurant and your delivery address using GPS coordinates.
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-semibold mt-1">
              2
            </div>
            <div>
              <h3 className="font-medium">Zone Assignment</h3>
              <p className="text-gray-600 text-sm">
                Your delivery address is assigned to one of our 5 delivery zones based on distance from the restaurant.
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-semibold mt-1">
              3
            </div>
            <div>
              <h3 className="font-medium">Dynamic Adjustments</h3>
              <p className="text-gray-600 text-sm">
                Additional factors like peak hours, weather, and express delivery options are applied to the base fee.
              </p>
            </div>
          </div>
          
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-semibold mt-1">
              4
            </div>
            <div>
              <h3 className="font-medium">Free Delivery Check</h3>
              <p className="text-gray-600 text-sm">
                If your order total exceeds ${freeDeliveryThreshold}, delivery becomes completely free regardless of distance.
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Tips */}
      <Card className="p-6 bg-blue-50 border-blue-200">
        <h3 className="font-semibold text-blue-900 mb-3">💡 Tips to Save on Delivery</h3>
        <ul className="space-y-2 text-sm text-blue-800">
          <li>• Order during off-peak hours (avoid 11AM-2PM and 6PM-9PM)</li>
          <li>• Combine orders with friends or family to reach the ${freeDeliveryThreshold} free delivery threshold</li>
          <li>• Choose restaurants closer to your location for lower delivery fees</li>
          <li>• Plan ahead and avoid express delivery unless urgent</li>
          <li>• Check for restaurants offering free delivery promotions</li>
        </ul>
      </Card>
    </div>
  );
};

export default DeliveryZones;
