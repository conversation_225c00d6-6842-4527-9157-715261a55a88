import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import Loader from "../common/Loader";

const ProtectedRoute = ({ children, role }) => {
  const { user, loading, isAuthenticated } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return <Loader fullPage />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to='/login' state={{ from: location }} replace />;
  }

  // Check role if specified
  if (role && user?.role !== role) {
    console.log(
      `🛡️ Access denied: Role mismatch. Required: ${role}, User has: ${user?.role}`
    );

    // Redirect to appropriate dashboard based on user role
    const roleRedirects = {
      customer: "/",
      restaurant: "/restaurant",
      delivery_agent: "/delivery",
      admin: "/admin",
    };

    return <Navigate to={roleRedirects[user.role] || "/"} replace />;
  }

  console.log("🛡️ Access granted to protected route");
  return children;
};

export default ProtectedRoute;
