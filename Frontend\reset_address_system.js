/**
 * Complete Address System Reset and Initialization
 * Run this script in the browser console to completely reset and set up the new address system
 */

console.log('🚀 Starting Complete Address System Reset...');

// Step 1: Clear ALL old address-related data
console.log('🧹 Step 1: Clearing all old address data...');

// List of all possible localStorage keys that might contain address data
const addressKeys = [
  'afghanSofraAddresses',
  'afghanSofraSelectedAddress', 
  'afghanSofra_addresses',
  'afghanSofra_selectedAddress',
  'afghanSofraCart',
  'afghanSofraUser',
  'afghanSofraLocation',
  'afghanSofraCurrentLocation'
];

// Clear known keys
addressKeys.forEach(key => {
  if (localStorage.getItem(key)) {
    localStorage.removeItem(key);
    console.log(`🗑️ Removed: ${key}`);
  }
});

// Clear any other keys that might contain address data
const allKeys = [];
for (let i = 0; i < localStorage.length; i++) {
  allKeys.push(localStorage.key(i));
}

allKeys.forEach(key => {
  if (key && (
    key.toLowerCase().includes('address') || 
    key.toLowerCase().includes('location') ||
    key.toLowerCase().includes('cart')
  )) {
    localStorage.removeItem(key);
    console.log(`🗑️ Removed additional key: ${key}`);
  }
});

console.log('✅ Step 1 Complete: All old data cleared');

// Step 2: Initialize clean state
console.log('🏗️ Step 2: Initializing clean state...');

// Create a completely clean cart
const cleanCart = {
  items: [],
  total: 0,
  subtotal: 0,
  deliveryFee: 0,
  tax: 0,
  restaurantId: null,
  restaurantName: '',
  timestamp: Date.now()
};

localStorage.setItem('afghanSofraCart', JSON.stringify(cleanCart));
console.log('✅ Clean cart created');

// Step 3: Verify user authentication
console.log('🔐 Step 3: Checking authentication...');

const userStr = localStorage.getItem('afghanSofraUser');
if (userStr) {
  try {
    const user = JSON.parse(userStr);
    console.log(`✅ User authenticated: ${user.name || user.email || 'Unknown'}`);
    console.log(`   Role: ${user.role || 'Unknown'}`);
    console.log(`   Token: ${user.access_token ? 'Present' : 'Missing'}`);
  } catch (e) {
    console.log('⚠️ Invalid user data in localStorage, clearing...');
    localStorage.removeItem('afghanSofraUser');
  }
} else {
  console.log('⚠️ No user authentication found');
  console.log('   You may need to log in for the address system to work properly');
}

// Step 4: Test API connectivity
console.log('🌐 Step 4: Testing API connectivity...');

const testAPI = async () => {
  try {
    const response = await fetch('http://127.0.0.1:8000/api/restaurant/addresses/', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(userStr ? { 'Authorization': `Bearer ${JSON.parse(userStr).access_token || JSON.parse(userStr).token}` } : {})
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ API connectivity successful`);
      console.log(`   Found ${Array.isArray(data) ? data.length : 'unknown'} addresses in backend`);
      return true;
    } else {
      console.log(`⚠️ API responded with status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ API connectivity failed: ${error.message}`);
    return false;
  }
};

// Step 5: Provide instructions
const showInstructions = (apiWorking) => {
  console.log('📋 Step 5: Setup complete! Next steps:');
  console.log('');
  console.log('🎯 IMMEDIATE ACTIONS:');
  console.log('1. Refresh the page (F5 or Ctrl+R)');
  console.log('2. The new address system will automatically initialize');
  console.log('');
  
  if (apiWorking) {
    console.log('✅ API is working - your existing addresses will be loaded automatically');
  } else {
    console.log('⚠️ API connectivity issues detected:');
    console.log('   - Make sure the backend server is running');
    console.log('   - Check if you are logged in');
    console.log('   - Verify your authentication token');
  }
  
  console.log('');
  console.log('🧪 TESTING:');
  console.log('1. Go to: http://localhost:3000/test-address-system');
  console.log('2. Run the built-in tests to verify everything works');
  console.log('3. Try adding a new address');
  console.log('4. Test the checkout process');
  console.log('');
  console.log('🛒 CHECKOUT TESTING:');
  console.log('1. Add items to cart from any restaurant');
  console.log('2. Go to checkout');
  console.log('3. You should see the new address selector');
  console.log('4. Select or add an address');
  console.log('5. Complete the order - no more address validation errors!');
  console.log('');
  console.log('🔧 TROUBLESHOOTING:');
  console.log('- All operations are logged with 🏠 prefix in console');
  console.log('- Check browser console for detailed error messages');
  console.log('- Use the test page for debugging');
  console.log('');
  console.log('🎉 The new address system is now ready to use!');
};

// Run the API test and show instructions
testAPI().then(apiWorking => {
  showInstructions(apiWorking);
  
  // Auto-refresh after showing instructions
  console.log('');
  console.log('🔄 Auto-refreshing page in 3 seconds...');
  setTimeout(() => {
    window.location.reload();
  }, 3000);
});

// Export for manual use
window.resetAddressSystem = () => {
  console.log('🔄 Manual address system reset triggered...');
  window.location.reload();
};
