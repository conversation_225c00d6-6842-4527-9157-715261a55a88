import React, { useState, useEffect } from "react";
import {
  MapPin,
  Navigation,
  Clock,
  Phone,
  MessageCircle,
  TruckIcon,
  Star,
  Route,
  Zap,
  AlertCircle,
} from "lucide-react";
import { useNotifications } from "../../context/NotificationContext";
import Card from "../common/Card";
import Button from "../common/Button";
import Badge from "../common/Badge";

const LiveTrackingPanel = ({ orderId, className = "" }) => {
  const { getOrderTracking, isConnected } = useNotifications();
  const [orderTracking, setOrderTracking] = useState(null);
  const [estimatedTime, setEstimatedTime] = useState(null);
  const [distance, setDistance] = useState(null);

  useEffect(() => {
    const tracking = getOrderTracking(orderId);
    setOrderTracking(tracking);

    // Simulate distance and time calculations
    if (tracking?.deliveryAgentLocation) {
      // In a real app, this would calculate actual distance and time
      setDistance((Math.random() * 3 + 0.5).toFixed(1));
      setEstimatedTime(Math.floor(Math.random() * 20 + 5));
    }
  }, [orderId, getOrderTracking]);

  const formatTime = (minutes) => {
    if (minutes < 60) return `${minutes} min`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  if (!orderTracking || orderTracking.status !== "outForDelivery") {
    return (
      <Card className={className}>
        <div className="p-6 text-center">
          <TruckIcon size={48} className="mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">
            Live Tracking Unavailable
          </h3>
          <p className="text-gray-500">
            Live tracking will be available when your order is out for delivery.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <TruckIcon size={24} className="text-orange-500" />
              {isConnected && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
              )}
            </div>
            <div>
              <h3 className="text-lg font-semibold">Live Tracking</h3>
              <p className="text-sm text-gray-500">
                Order #{orderId.split("-")[1]}
              </p>
            </div>
          </div>
          <Badge
            variant={isConnected ? "success" : "danger"}
            className="flex items-center space-x-1"
          >
            <div
              className={`w-2 h-2 rounded-full ${
                isConnected ? "bg-green-500" : "bg-red-500"
              } animate-pulse`}
            />
            <span>{isConnected ? "Live" : "Offline"}</span>
          </Badge>
        </div>

        {/* Delivery Status */}
        <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-orange-800">
                Your order is on the way!
              </p>
              <div className="flex items-center space-x-4 mt-2">
                {distance && (
                  <div className="flex items-center text-orange-700">
                    <MapPin size={16} className="mr-1" />
                    <span className="text-sm font-medium">{distance} km away</span>
                  </div>
                )}
                {estimatedTime && (
                  <div className="flex items-center text-orange-700">
                    <Clock size={16} className="mr-1" />
                    <span className="text-sm font-medium">
                      {formatTime(estimatedTime)}
                    </span>
                  </div>
                )}
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-orange-900">
                {estimatedTime ? formatTime(estimatedTime) : "Calculating..."}
              </div>
              <p className="text-xs text-orange-700">Estimated arrival</p>
            </div>
          </div>
        </div>

        {/* Delivery Agent Info */}
        <div className="border border-gray-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium">Your Delivery Agent</h4>
            <div className="flex items-center space-x-1">
              <Star size={16} className="text-yellow-500" />
              <span className="text-sm font-medium">4.8</span>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-orange-600 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-lg">MA</span>
            </div>
            <div className="flex-grow">
              <h5 className="font-medium">Mohammad Ahmad</h5>
              <p className="text-sm text-gray-500">Delivery Agent</p>
              <div className="flex items-center text-xs text-gray-500 mt-1">
                <TruckIcon size={12} className="mr-1" />
                <span>Honda Motorcycle • ABC-123</span>
              </div>
            </div>
          </div>

          {/* Contact Buttons */}
          <div className="flex space-x-3 mt-4">
            <Button
              variant="outline"
              size="small"
              icon={<Phone size={16} />}
              className="flex-1"
            >
              Call Agent
            </Button>
            <Button
              variant="outline"
              size="small"
              icon={<MessageCircle size={16} />}
              className="flex-1"
            >
              Chat
            </Button>
          </div>
        </div>

        {/* Live Updates */}
        <div className="space-y-3">
          <h4 className="font-medium flex items-center">
            <Zap size={16} className="text-orange-500 mr-2" />
            Live Updates
          </h4>

          <div className="space-y-2 max-h-32 overflow-y-auto">
            {orderTracking.updates
              .slice()
              .reverse()
              .map((update, index) => (
                <div
                  key={index}
                  className="flex items-start space-x-3 p-2 bg-gray-50 rounded-lg"
                >
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                  <div className="flex-grow min-w-0">
                    <p className="text-sm font-medium capitalize">
                      {update.status.replace(/([A-Z])/g, " $1").trim()}
                    </p>
                    <p className="text-xs text-gray-500">
                      {new Date(update.timestamp).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}

            {/* Real-time location update simulation */}
            {isConnected && (
              <div className="flex items-start space-x-3 p-2 bg-green-50 rounded-lg border border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0 animate-pulse" />
                <div className="flex-grow min-w-0">
                  <p className="text-sm font-medium text-green-800">
                    Location updated
                  </p>
                  <p className="text-xs text-green-600">
                    {new Date().toLocaleTimeString()} • Live
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              size="small"
              icon={<Navigation size={16} />}
              className="text-center"
            >
              Share Location
            </Button>
            <Button
              variant="outline"
              size="small"
              icon={<Route size={16} />}
              className="text-center"
            >
              View Route
            </Button>
          </div>
        </div>

        {/* Connection Status */}
        {!isConnected && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle size={16} className="text-yellow-600 mr-2" />
              <p className="text-sm text-yellow-800">
                Connection lost. Trying to reconnect...
              </p>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default LiveTrackingPanel;
