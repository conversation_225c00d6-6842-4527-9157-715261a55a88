import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import {
  Package,
  Clock,
  CheckCircle,
  DollarSign,
  Search,
  RefreshCw,
  Bell,
  Eye,
  User,
  X,
  MessageCircle,
} from "lucide-react";
import Button from "../../components/ui/Button";
import { orderStatuses } from "../../data/orders";
import NotificationCenter from "../../components/restaurant/NotificationCenter";
import OrderReceipt from "../../components/restaurant/OrderReceipt";
import CustomerChat from "../../components/restaurant/CustomerChat";

function Orders() {
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // State management
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sortBy, setSortBy] = useState("date");
  const [sortOrder, setSortOrder] = useState("desc");
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [orderDetailsModal, setOrderDetailsModal] = useState(null);
  const [notificationModal, setNotificationModal] = useState(null);
  const [customerChatModal, setCustomerChatModal] = useState(null);
  const [receiptModal, setReceiptModal] = useState(null);

  // Simple version with basic API call
  useEffect(() => {
    const loadOrders = async () => {
      // Check authentication before making API call
      if (!user || !user.access_token || user.role !== "restaurant") {
        console.log("Orders_clean: User not authenticated or not restaurant role");
        return;
      }

      try {
        console.log("Loading orders for restaurant user:", user.name);

        const response = await fetch(
          "http://127.0.0.1:8000/api/order/orders/",
          {
            headers: {
              Authorization: `Bearer ${user.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const ordersData = await response.json();
          console.log("Orders loaded:", ordersData.length);
          setOrders(ordersData);
        } else {
          console.error("Failed to load orders:", response.status);
          if (response.status === 401) {
            console.log("Orders_clean: 401 Unauthorized - clearing user data");
            localStorage.removeItem("afghanSofraUser");
          }
        }
        } catch (error) {
          console.error("Error loading orders:", error);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadOrders();
  }, [user]);

  // Filter and search functionality
  useEffect(() => {
    let filtered = [...orders];

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((order) => order.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (order) =>
          order.id.toString().toLowerCase().includes(query) ||
          (order.customer && order.customer.toString().includes(query)) ||
          (order.special_instructions &&
            order.special_instructions.toLowerCase().includes(query))
      );
    }

    // Sort orders
    filtered.sort((a, b) => {
      if (sortBy === "date") {
        const dateA = new Date(a.created_at);
        const dateB = new Date(b.created_at);
        return sortOrder === "asc" ? dateA - dateB : dateB - dateA;
      } else if (sortBy === "amount") {
        const amountA = parseFloat(a.total_amount);
        const amountB = parseFloat(b.total_amount);
        return sortOrder === "asc" ? amountA - amountB : amountB - amountA;
      }
      return 0;
    });

    setFilteredOrders(filtered);
  }, [orders, statusFilter, searchQuery, sortBy, sortOrder]);

  // Refresh orders
  const handleRefreshOrders = async () => {
    setRefreshing(true);
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const response = await fetch(
          "http://127.0.0.1:8000/api/order/orders/",
          {
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const ordersData = await response.json();
          setOrders(ordersData);
        }
      }
    } catch (error) {
      console.error("Error refreshing orders:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Update order status
  const handleUpdateOrderStatus = async (orderId, newStatus) => {
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const response = await fetch(
          `http://127.0.0.1:8000/api/order/orders/${orderId}/`,
          {
            method: "PATCH",
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ status: newStatus }),
          }
        );

        if (response.ok) {
          // Refresh orders to get updated data
          handleRefreshOrders();
        }
      }
    } catch (error) {
      console.error("Error updating order status:", error);
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Restaurant Orders</h1>
        <p>Loading orders...</p>
      </div>
    );
  }

  const getStatusBadge = (status) => {
    const statusColors = {
      pending: "bg-yellow-100 text-yellow-800",
      confirmed: "bg-blue-100 text-blue-800",
      preparing: "bg-orange-100 text-orange-800",
      ready: "bg-green-100 text-green-800",
      delivered: "bg-gray-100 text-gray-800",
      cancelled: "bg-red-100 text-red-800",
    };

    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          statusColors[status] || "bg-gray-100 text-gray-800"
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Orders Management
              </h1>
              <p className="mt-1 text-sm text-gray-600">
                Manage and track all restaurant orders
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={handleRefreshOrders}
                disabled={refreshing}
                className="flex items-center space-x-2"
              >
                <RefreshCw
                  size={16}
                  className={refreshing ? "animate-spin" : ""}
                />
                <span>{refreshing ? "Refreshing..." : "Refresh"}</span>
              </Button>
              <Button
                onClick={() => setNotificationModal(true)}
                className="flex items-center space-x-2"
              >
                <Bell size={16} />
                <span>Notifications</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Package className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">
                  {orders.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-gray-900">
                  {orders.filter((o) => o.status === "pending").length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {orders.filter((o) => o.status === "delivered").length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  $
                  {orders
                    .reduce(
                      (sum, order) => sum + parseFloat(order.total_amount),
                      0
                    )
                    .toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>
