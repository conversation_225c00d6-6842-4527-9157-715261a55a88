import { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "./AuthContext";

const LoyaltyContext = createContext(null);

export const useLoyalty = () => useContext(LoyaltyContext);

// Loyalty tier configuration
export const LOYALTY_TIERS = {
  BRONZE: {
    name: "Bronze",
    minPoints: 0,
    maxPoints: 499,
    color: "#CD7F32",
    bgColor: "#FDF2E9",
    benefits: [
      "Earn 1 point per $1 spent",
      "Birthday discount: 5%",
      "Free delivery on orders over $30",
    ],
    pointsMultiplier: 1,
    birthdayDiscount: 5,
    freeDeliveryThreshold: 30,
  },
  SILVER: {
    name: "Silver",
    minPoints: 500,
    maxPoints: 1499,
    color: "#C0C0C0",
    bgColor: "#F8F9FA",
    benefits: [
      "Earn 1.2 points per $1 spent",
      "Birthday discount: 10%",
      "Free delivery on orders over $25",
      "Early access to new restaurants",
    ],
    pointsMultiplier: 1.2,
    birthdayDiscount: 10,
    freeDeliveryThreshold: 25,
  },
  GOLD: {
    name: "Gold",
    minPoints: 1500,
    maxPoints: 2999,
    color: "#FFD700",
    bgColor: "#FFFBEB",
    benefits: [
      "Earn 1.5 points per $1 spent",
      "Birthday discount: 15%",
      "Free delivery on orders over $20",
      "Priority customer support",
      "Monthly exclusive offers",
    ],
    pointsMultiplier: 1.5,
    birthdayDiscount: 15,
    freeDeliveryThreshold: 20,
  },
  PLATINUM: {
    name: "Platinum",
    minPoints: 3000,
    maxPoints: Infinity,
    color: "#E5E4E2",
    bgColor: "#F9FAFB",
    benefits: [
      "Earn 2 points per $1 spent",
      "Birthday discount: 20%",
      "Free delivery on all orders",
      "VIP customer support",
      "Weekly exclusive offers",
      "Complimentary appetizer monthly",
    ],
    pointsMultiplier: 2,
    birthdayDiscount: 20,
    freeDeliveryThreshold: 0,
  },
};

// Points earning rules
export const POINTS_RULES = {
  ORDER_COMPLETION: "order_completion", // Points per dollar spent
  REFERRAL_SIGNUP: "referral_signup", // Points for successful referral
  REVIEW_SUBMISSION: "review_submission", // Points for restaurant review
  BIRTHDAY_BONUS: "birthday_bonus", // Bonus points on birthday
  FIRST_ORDER: "first_order", // Bonus points for first order
};

export const POINTS_VALUES = {
  [POINTS_RULES.ORDER_COMPLETION]: 1, // Base points per dollar
  [POINTS_RULES.REFERRAL_SIGNUP]: 100,
  [POINTS_RULES.REVIEW_SUBMISSION]: 25,
  [POINTS_RULES.BIRTHDAY_BONUS]: 50,
  [POINTS_RULES.FIRST_ORDER]: 100,
};

// Reward types and costs
export const REWARDS = {
  DISCOUNT_5: {
    id: "discount_5",
    name: "$5 Off Your Order",
    description: "Get $5 off on orders over $25",
    pointsCost: 500,
    discountAmount: 5,
    minimumOrder: 25,
    type: "discount",
    validityDays: 30,
  },
  DISCOUNT_10: {
    id: "discount_10",
    name: "$10 Off Your Order",
    description: "Get $10 off on orders over $50",
    pointsCost: 1000,
    discountAmount: 10,
    minimumOrder: 50,
    type: "discount",
    validityDays: 30,
  },
  FREE_DELIVERY: {
    id: "free_delivery",
    name: "Free Delivery",
    description: "Free delivery on your next order",
    pointsCost: 200,
    type: "free_delivery",
    validityDays: 14,
  },
  APPETIZER: {
    id: "free_appetizer",
    name: "Free Appetizer",
    description: "Get a free appetizer with your next order",
    pointsCost: 300,
    type: "free_item",
    validityDays: 21,
  },
};

export const LoyaltyProvider = ({ children }) => {
  const { user } = useAuth();
  const [loyaltyData, setLoyaltyData] = useState(null);
  const [pointsHistory, setPointsHistory] = useState([]);
  const [availableRewards, setAvailableRewards] = useState([]);
  const [redeemedRewards, setRedeemedRewards] = useState([]);
  const [referralCode, setReferralCode] = useState("");

  // Initialize loyalty data for user
  useEffect(() => {
    if (user && user.role === "customer" && (user.name || user.username)) {
      initializeLoyaltyData();
    } else {
      setLoyaltyData(null);
      setPointsHistory([]);
      setAvailableRewards([]);
      setRedeemedRewards([]);
    }
  }, [user]);

  const initializeLoyaltyData = () => {
    // Safety check - ensure user exists and has required properties
    if (!user || !user.id || (!user.name && !user.username)) {
      console.warn(
        "Cannot initialize loyalty data: user data incomplete",
        user
      );
      return;
    }

    // Load from localStorage or create new
    const savedData = localStorage.getItem(`loyalty_${user.id}`);

    if (savedData) {
      const data = JSON.parse(savedData);
      setLoyaltyData(data.loyaltyData);
      setPointsHistory(data.pointsHistory || []);
      setAvailableRewards(data.availableRewards || []);
      setRedeemedRewards(data.redeemedRewards || []);
      setReferralCode(data.referralCode || generateReferralCode());
    } else {
      // Create new loyalty profile
      const newLoyaltyData = {
        userId: user.id,
        totalPoints: 0,
        availablePoints: 0,
        tier: "BRONZE",
        joinDate: new Date().toISOString(),
        totalSpent: 0,
        totalOrders: 0,
        referralsCount: 0,
      };

      const newReferralCode = generateReferralCode();

      setLoyaltyData(newLoyaltyData);
      setReferralCode(newReferralCode);

      // Save to localStorage
      saveLoyaltyData(newLoyaltyData, [], [], [], newReferralCode);
    }
  };

  const generateReferralCode = () => {
    // Safely get user name with fallback
    const userName = user?.name || user?.username || "USER";
    const namePrefix = userName.replace(/\s+/g, "").toUpperCase().slice(0, 3);
    const randomSuffix = Math.random().toString(36).substr(2, 5).toUpperCase();

    return `${namePrefix}${randomSuffix}`;
  };

  const saveLoyaltyData = (loyalty, history, rewards, redeemed, refCode) => {
    const dataToSave = {
      loyaltyData: loyalty,
      pointsHistory: history,
      availableRewards: rewards,
      redeemedRewards: redeemed,
      referralCode: refCode,
    };
    localStorage.setItem(`loyalty_${user.id}`, JSON.stringify(dataToSave));
  };

  const getCurrentTier = (points) => {
    for (const [tierKey, tier] of Object.entries(LOYALTY_TIERS)) {
      if (points >= tier.minPoints && points <= tier.maxPoints) {
        return tierKey;
      }
    }
    return "BRONZE";
  };

  const getNextTier = (currentTier) => {
    const tiers = ["BRONZE", "SILVER", "GOLD", "PLATINUM"];
    const currentIndex = tiers.indexOf(currentTier);
    return currentIndex < tiers.length - 1 ? tiers[currentIndex + 1] : null;
  };

  const getPointsToNextTier = (currentPoints, currentTier) => {
    const nextTier = getNextTier(currentTier);
    if (!nextTier) return 0;
    return LOYALTY_TIERS[nextTier].minPoints - currentPoints;
  };

  const addPoints = (points, reason, orderId = null) => {
    if (!loyaltyData) return;

    const newTotalPoints = loyaltyData.totalPoints + points;
    const newAvailablePoints = loyaltyData.availablePoints + points;
    const newTier = getCurrentTier(newTotalPoints);

    const updatedLoyaltyData = {
      ...loyaltyData,
      totalPoints: newTotalPoints,
      availablePoints: newAvailablePoints,
      tier: newTier,
    };

    const newHistoryEntry = {
      id: `points_${Date.now()}`,
      points,
      reason,
      orderId,
      timestamp: new Date().toISOString(),
      type: "earned",
    };

    const updatedHistory = [newHistoryEntry, ...pointsHistory];

    setLoyaltyData(updatedLoyaltyData);
    setPointsHistory(updatedHistory);

    saveLoyaltyData(
      updatedLoyaltyData,
      updatedHistory,
      availableRewards,
      redeemedRewards,
      referralCode
    );

    return { success: true, newTier: newTier !== loyaltyData.tier };
  };

  const redeemReward = (rewardId) => {
    const reward = REWARDS[rewardId];
    if (!reward || !loyaltyData)
      return { success: false, error: "Invalid reward" };

    if (loyaltyData.availablePoints < reward.pointsCost) {
      return { success: false, error: "Insufficient points" };
    }

    const newAvailablePoints = loyaltyData.availablePoints - reward.pointsCost;
    const updatedLoyaltyData = {
      ...loyaltyData,
      availablePoints: newAvailablePoints,
    };

    const redeemedReward = {
      id: `reward_${Date.now()}`,
      rewardId,
      ...reward,
      redeemedAt: new Date().toISOString(),
      expiresAt: new Date(
        Date.now() + reward.validityDays * 24 * 60 * 60 * 1000
      ).toISOString(),
      used: false,
    };

    const newHistoryEntry = {
      id: `points_${Date.now()}`,
      points: -reward.pointsCost,
      reason: `Redeemed: ${reward.name}`,
      timestamp: new Date().toISOString(),
      type: "redeemed",
    };

    const updatedHistory = [newHistoryEntry, ...pointsHistory];
    const updatedRedeemed = [redeemedReward, ...redeemedRewards];

    setLoyaltyData(updatedLoyaltyData);
    setPointsHistory(updatedHistory);
    setRedeemedRewards(updatedRedeemed);

    saveLoyaltyData(
      updatedLoyaltyData,
      updatedHistory,
      availableRewards,
      updatedRedeemed,
      referralCode
    );

    return { success: true, reward: redeemedReward };
  };

  const processOrderCompletion = (orderAmount, orderId) => {
    if (!loyaltyData) return;

    const tier = LOYALTY_TIERS[loyaltyData.tier];
    const basePoints = Math.floor(
      orderAmount * POINTS_VALUES[POINTS_RULES.ORDER_COMPLETION]
    );
    const earnedPoints = Math.floor(basePoints * tier.pointsMultiplier);

    const updatedLoyaltyData = {
      ...loyaltyData,
      totalSpent: loyaltyData.totalSpent + orderAmount,
      totalOrders: loyaltyData.totalOrders + 1,
    };

    setLoyaltyData(updatedLoyaltyData);

    return addPoints(earnedPoints, "Order completion", orderId);
  };

  const value = {
    loyaltyData,
    pointsHistory,
    availableRewards,
    redeemedRewards,
    referralCode,

    // Functions
    addPoints,
    redeemReward,
    processOrderCompletion,
    getCurrentTier,
    getNextTier,
    getPointsToNextTier,

    // Constants
    LOYALTY_TIERS,
    REWARDS,
    POINTS_RULES,
    POINTS_VALUES,
  };

  return (
    <LoyaltyContext.Provider value={value}>{children}</LoyaltyContext.Provider>
  );
};
