import React from "react";
import { Outlet, Navigate } from "react-router-dom";
import { useAuth } from "../context/AuthContext";
import { Link } from "react-router-dom";
import { useSiteName } from "../hooks/useConfig";

const AuthLayout = () => {
  const { isAuthenticated, user } = useAuth();
  const { value: siteName } = useSiteName();

  console.log("🔐 AuthLayout: Checking authentication", {
    isAuthenticated,
    user,
    userRole: user?.role,
  });

  // Redirect if already authenticated
  if (isAuthenticated) {
    const redirectMap = {
      customer: "/",
      restaurant: "/restaurant",
      delivery_agent: "/delivery",
      admin: "/admin",
    };

    const redirectTo = redirectMap[user.role] || "/";
    console.log(
      "✅ AuthLayout: User authenticated, redirecting to:",
      redirectTo
    );
    return <Navigate to={redirectTo} replace />;
  }

  return (
    <div className='min-h-screen flex flex-col bg-background-light'>
      <div className='flex min-h-screen'>
        {/* Left side - Auth form */}
        <div className='w-full lg:w-1/2 flex flex-col justify-center p-4 sm:p-8 md:p-12'>
          <div className='mb-8'>
            <Link to='/' className='flex items-center'>
              <span className='text-primary-500 font-poppins font-bold text-xl md:text-2xl'>
                {siteName || "Afghan Sufra"}
              </span>
            </Link>
          </div>

          <div className='w-full max-w-md mx-auto'>
            <Outlet />
          </div>
        </div>

        {/* Right side - Image */}
        <div
          className='hidden lg:block lg:w-1/2 bg-cover bg-center'
          style={{
            backgroundImage:
              "url('https://images.pexels.com/photos/8445858/pexels-photo-8445858.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1')",
          }}
        >
          <div className='h-full w-full bg-black bg-opacity-40 flex flex-col justify-center items-center p-12'>
            <div className='max-w-lg text-center'>
              <h1 className='text-white font-poppins font-bold text-4xl mb-4'>
                Authentic Afghan Cuisine Delivered To Your Door
              </h1>
              <p className='text-gray-200 text-lg'>
                Join our platform to enjoy the best Afghan dishes from top
                restaurants in your area.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
