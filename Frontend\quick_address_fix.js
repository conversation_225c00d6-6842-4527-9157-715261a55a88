/**
 * Quick Address System Fix
 * Run this in browser console to immediately fix address issues
 */

console.log('🔧 Quick Address System Fix...');

// Step 1: Clear problematic data
console.log('🧹 Clearing problematic data...');
const keysToRemove = [
  'afghanSofraAddresses',
  'afghanSofraSelectedAddress', 
  'afghanSofra_addresses',
  'afghanSofra_selectedAddress',
  'afghanSofraCart'
];

keysToRemove.forEach(key => {
  if (localStorage.getItem(key)) {
    localStorage.removeItem(key);
    console.log(`✅ Removed: ${key}`);
  }
});

// Step 2: Create clean cart
console.log('🛒 Creating clean cart...');
const cleanCart = {
  items: [],
  total: 0,
  subtotal: 0,
  deliveryFee: 0,
  tax: 0,
  restaurantId: null,
  restaurantName: ''
};

localStorage.setItem('afghanSofraCart', JSON.stringify(cleanCart));
console.log('✅ Clean cart created');

// Step 3: Check authentication
console.log('🔐 Checking authentication...');
const user = localStorage.getItem('afghanSofraUser');
if (user) {
  try {
    const userData = JSON.parse(user);
    console.log(`✅ User: ${userData.name || userData.email}`);
  } catch (e) {
    console.log('⚠️ Invalid user data, clearing...');
    localStorage.removeItem('afghanSofraUser');
  }
} else {
  console.log('⚠️ No user found - you may need to log in');
}

console.log('');
console.log('🎉 Quick fix complete!');
console.log('');
console.log('📋 Next steps:');
console.log('1. Refresh the page');
console.log('2. Go to: http://localhost:3000/test-address-system');
console.log('3. Test the address system');
console.log('4. Try checkout with a real order');
console.log('');
console.log('🔄 Refreshing page in 2 seconds...');

setTimeout(() => {
  window.location.reload();
}, 2000);
