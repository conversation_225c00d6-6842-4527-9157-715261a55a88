import axios from "axios";

// Base URL for the API
const BASE_URL =
  (typeof process !== "undefined" && process.env?.REACT_APP_API_BASE_URL) ||
  import.meta.env?.VITE_API_BASE_URL ||
  "http://127.0.0.1:8000/api";

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 30000, // 30 seconds timeout
});

// Request interceptor to add authentication token
apiClient.interceptors.request.use(
  (config) => {
    // Get token from localStorage or your auth context
    const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
    const token = user.access_token || user.token;

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem("afghanSofraUser");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

export const dashboardApi = {
  /**
   * Get restaurant orders for dashboard
   * @param {string} restaurantId - Restaurant ID
   * @param {Object} params - Query parameters (status, date_range, etc.)
   * @returns {Promise} API response
   */
  async getRestaurantOrders(restaurantId, params = {}) {
    try {
      console.log("Fetching orders for restaurant:", restaurantId);
      const response = await apiClient.get(`/order/orders/`, {
        params: {
          restaurant: restaurantId,
          ...params,
        },
      });

      console.log("Orders API response:", response.data);
      return {
        success: true,
        data: response.data,
        message: "Orders fetched successfully",
      };
    } catch (error) {
      console.error("Get restaurant orders error:", error);
      console.error("Error details:", error.response?.data);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch orders",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get restaurant menu items for dashboard
   * @param {string} restaurantId - Restaurant ID
   * @returns {Promise} API response
   */
  async getRestaurantMenuItems(restaurantId) {
    try {
      console.log("Fetching menu items for restaurant:", restaurantId);
      const response = await apiClient.get(`/restaurant/menu-items/`, {
        params: {
          restaurant: restaurantId,
        },
      });

      console.log("Menu items API response:", response.data);
      return {
        success: true,
        data: response.data,
        message: "Menu items fetched successfully",
      };
    } catch (error) {
      console.error("Get restaurant menu items error:", error);
      console.error("Error details:", error.response?.data);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch menu items",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get restaurant ratings and reviews
   * @param {string} restaurantId - Restaurant ID
   * @returns {Promise} API response
   */
  async getRestaurantRatings(restaurantId) {
    try {
      const response = await apiClient.get(`/order/ratings/restaurant_summary/`, {
        params: {
          restaurant_id: restaurantId,
        },
      });

      return {
        success: true,
        data: response.data,
        message: "Ratings fetched successfully",
      };
    } catch (error) {
      console.error("Get restaurant ratings error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch ratings",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get restaurant reviews
   * @param {string} restaurantId - Restaurant ID
   * @returns {Promise} API response
   */
  async getRestaurantReviews(restaurantId) {
    try {
      const response = await apiClient.get(`/restaurant/restaurant-reviews/`, {
        params: {
          restaurant_id: restaurantId,
        },
      });

      return {
        success: true,
        data: response.data,
        message: "Reviews fetched successfully",
      };
    } catch (error) {
      console.error("Get restaurant reviews error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch reviews",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get restaurant analytics data
   * @param {string} restaurantId - Restaurant ID
   * @param {Object} params - Query parameters (date_range, metrics, etc.)
   * @returns {Promise} API response
   */
  async getRestaurantAnalytics(restaurantId, params = {}) {
    try {
      // For now, we'll calculate analytics from orders data
      // In the future, this could be a dedicated analytics endpoint
      const ordersResult = await this.getRestaurantOrders(restaurantId, params);
      
      if (!ordersResult.success) {
        return ordersResult;
      }

      const orders = ordersResult.data;
      const analytics = this.calculateAnalytics(orders);

      return {
        success: true,
        data: analytics,
        message: "Analytics calculated successfully",
      };
    } catch (error) {
      console.error("Get restaurant analytics error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch analytics",
        details: error.response?.data,
      };
    }
  },

  /**
   * Calculate analytics from orders data
   * @param {Array} orders - Array of orders
   * @returns {Object} Analytics data
   */
  calculateAnalytics(orders) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Filter orders by time periods
    const todayOrders = orders.filter(order => {
      const orderDate = new Date(order.created_at || order.date);
      return orderDate >= today;
    });

    const weekOrders = orders.filter(order => {
      const orderDate = new Date(order.created_at || order.date);
      return orderDate >= thisWeek;
    });

    const monthOrders = orders.filter(order => {
      const orderDate = new Date(order.created_at || order.date);
      return orderDate >= thisMonth;
    });

    // Calculate totals
    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + (parseFloat(order.total_amount || order.total) || 0), 0);
    const todayRevenue = todayOrders.reduce((sum, order) => sum + (parseFloat(order.total_amount || order.total) || 0), 0);

    // Calculate order status counts
    const pendingOrders = orders.filter(order => 
      ['pending', 'confirmed', 'preparing'].includes(order.status)
    ).length;
    
    const completedOrders = orders.filter(order => 
      order.status === 'delivered' || order.status === 'completed'
    ).length;

    const cancelledOrders = orders.filter(order => 
      order.status === 'cancelled'
    ).length;

    // Calculate average order value
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calculate revenue by day (last 7 days)
    const revenueByDay = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
      const dayOrders = orders.filter(order => {
        const orderDate = new Date(order.created_at || order.date);
        return orderDate.toDateString() === date.toDateString();
      });
      const dayRevenue = dayOrders.reduce((sum, order) => sum + (parseFloat(order.total_amount || order.total) || 0), 0);
      
      revenueByDay.push({
        day: date.toLocaleDateString('en-US', { weekday: 'short' }),
        date: date.toISOString().split('T')[0],
        revenue: dayRevenue,
        orders: dayOrders.length
      });
    }

    // Calculate orders by status
    const ordersByStatus = {
      pending: pendingOrders,
      completed: completedOrders,
      cancelled: cancelledOrders,
      in_progress: orders.filter(order => 
        ['assigned', 'picked_up', 'out_for_delivery'].includes(order.status)
      ).length
    };

    // Calculate revenue growth (comparing this month to last month)
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
    
    const lastMonthOrders = orders.filter(order => {
      const orderDate = new Date(order.created_at || order.date);
      return orderDate >= lastMonth && orderDate <= lastMonthEnd;
    });
    
    const lastMonthRevenue = lastMonthOrders.reduce((sum, order) => sum + (parseFloat(order.total_amount || order.total) || 0), 0);
    const thisMonthRevenue = monthOrders.reduce((sum, order) => sum + (parseFloat(order.total_amount || order.total) || 0), 0);
    
    const revenueGrowth = lastMonthRevenue > 0 ? 
      ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;

    return {
      totalOrders,
      totalRevenue,
      todayOrders: todayOrders.length,
      todayRevenue,
      pendingOrders,
      completedOrders,
      cancelledOrders,
      avgOrderValue,
      revenueByDay,
      ordersByStatus,
      revenueGrowth,
      weekOrders: weekOrders.length,
      monthOrders: monthOrders.length,
      weekRevenue: weekOrders.reduce((sum, order) => sum + (parseFloat(order.total_amount || order.total) || 0), 0),
      monthRevenue: thisMonthRevenue,
      completionRate: totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0,
    };
  },

  /**
   * Get all dashboard data for a restaurant
   * @param {string} restaurantId - Restaurant ID
   * @returns {Promise} Combined dashboard data
   */
  async getDashboardData(restaurantId) {
    try {
      const [ordersResult, menuItemsResult, ratingsResult] = await Promise.all([
        this.getRestaurantOrders(restaurantId),
        this.getRestaurantMenuItems(restaurantId),
        this.getRestaurantRatings(restaurantId),
      ]);

      const analytics = ordersResult.success ? 
        this.calculateAnalytics(ordersResult.data) : {};

      return {
        success: true,
        data: {
          orders: ordersResult.success ? ordersResult.data : [],
          menuItems: menuItemsResult.success ? menuItemsResult.data : [],
          ratings: ratingsResult.success ? ratingsResult.data : {},
          analytics,
        },
        message: "Dashboard data fetched successfully",
      };
    } catch (error) {
      console.error("Get dashboard data error:", error);
      return {
        success: false,
        error: "Failed to fetch dashboard data",
        details: error,
      };
    }
  },
};
