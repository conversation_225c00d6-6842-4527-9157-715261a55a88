// Simple Node.js script to test the Restaurant API
// Run with: node test-api.js

const https = require("https");

const API_BASE_URL = "http://127.0.0.1:8000/api";

// You'll need to replace this with a valid token from your app
const ACCESS_TOKEN = "YOUR_ACCESS_TOKEN_HERE";

function makeRequest(method, path, data = null, isFormData = false) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, API_BASE_URL);

    const options = {
      hostname: url.hostname,
      port: 443,
      path: url.pathname,
      method: method,
      headers: {
        Authorization: `Bearer ${ACCESS_TOKEN}`,
        "Content-Type": isFormData ? "multipart/form-data" : "application/json",
      },
    };

    const req = https.request(options, (res) => {
      let responseData = "";

      res.on("data", (chunk) => {
        responseData += chunk;
      });

      res.on("end", () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsedData,
            headers: res.headers,
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData,
            headers: res.headers,
          });
        }
      });
    });

    req.on("error", (error) => {
      reject(error);
    });

    if (data && method !== "GET") {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testAPI() {
  console.log("🧪 Testing Restaurant API Integration...\n");
  console.log(`📍 API Base URL: ${API_BASE_URL}`);
  console.log(`🔑 Using Token: ${ACCESS_TOKEN.substring(0, 10)}...\n`);

  // Test 1: Get all restaurants
  console.log("1️⃣ Testing GET /restaurant/restaurants/");
  try {
    const response = await makeRequest("GET", "/restaurant/restaurants/");
    console.log(`   ✅ Status: ${response.status}`);
    if (response.status === 200) {
      console.log(
        `   📊 Found ${
          Array.isArray(response.data) ? response.data.length : "unknown"
        } restaurants`
      );
    } else {
      console.log(`   ❌ Error: ${JSON.stringify(response.data)}`);
    }
  } catch (error) {
    console.log(`   ❌ Network Error: ${error.message}`);
  }
  console.log("");

  // Test 2: Try to create a restaurant (will likely fail without proper form data)
  console.log("2️⃣ Testing POST /restaurant/restaurants/ (basic test)");
  try {
    const testData = {
      name: "Test Restaurant API",
      description: "Testing API integration",
      contact_number: "+1234567890",
      opening_time: "09:00:00",
      closing_time: "21:00:00",
      address: JSON.stringify({
        street: "123 Test St",
        city: "Test City",
        state: "TS",
        postal_code: "12345",
        country: "USA",
        latitude: 40.7128,
        longitude: -74.006,
      }),
    };

    const response = await makeRequest(
      "POST",
      "/restaurant/restaurants/",
      testData
    );
    console.log(`   ✅ Status: ${response.status}`);
    if (response.status === 201) {
      console.log(`   🎉 Restaurant created successfully!`);
      console.log(`   📝 Restaurant ID: ${response.data.id}`);
    } else {
      console.log(
        `   ⚠️  Expected error (missing files): ${JSON.stringify(
          response.data
        )}`
      );
    }
  } catch (error) {
    console.log(`   ❌ Network Error: ${error.message}`);
  }
  console.log("");

  // Test 3: Check API connectivity
  console.log("3️⃣ Testing API Connectivity");
  try {
    const response = await makeRequest("GET", "/");
    console.log(`   ✅ API is reachable`);
    console.log(`   📡 Status: ${response.status}`);
  } catch (error) {
    console.log(`   ❌ API not reachable: ${error.message}`);
  }
  console.log("");

  console.log("🏁 API Test Complete!\n");
  console.log("📋 Next Steps:");
  console.log("   1. Replace YOUR_ACCESS_TOKEN_HERE with a real token");
  console.log("   2. Test in your React app at /admin/api-test");
  console.log(
    "   3. Use the RestaurantManager component for full CRUD operations"
  );
  console.log("   4. Check browser console for detailed error messages");
}

// Check if token is provided
if (ACCESS_TOKEN === "YOUR_ACCESS_TOKEN_HERE") {
  console.log(
    "⚠️  Please update the ACCESS_TOKEN in this script with a real token from your app"
  );
  console.log("   You can get this from localStorage in your browser:");
  console.log("   1. Login to your app");
  console.log("   2. Open browser console");
  console.log(
    '   3. Run: JSON.parse(localStorage.getItem("afghanSofraUser")).access_token'
  );
  console.log("   4. Copy the token and replace YOUR_ACCESS_TOKEN_HERE\n");
}

testAPI().catch(console.error);
