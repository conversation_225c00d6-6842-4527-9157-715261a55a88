import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import { restaurantApi } from "../../utils/restaurantApi";
import {
  Store,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  FileText,
  Shield,
} from "lucide-react";
import Button from "../common/Button";
import Card from "../common/Card";
import { useNavigate } from "react-router-dom";

const RestaurantStatusChecker = ({ children }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [restaurantStatus, setRestaurantStatus] = useState({
    loading: true,
    hasRestaurant: false,
    restaurant: null,
    isVerified: false,
    error: null,
  });

  useEffect(() => {
    const checkRestaurantStatus = async () => {
      if (!user || user.role !== "restaurant") {
        setRestaurantStatus((prev) => ({ ...prev, loading: false }));
        return;
      }

      try {
        // Get user's restaurants (including inactive ones)
        const result = await restaurantApi.getUserRestaurants();

        if (result.success && result.data && result.data.length > 0) {
          // User has restaurants, check the first one's verification status
          const restaurant = result.data[0];
          setRestaurantStatus({
            loading: false,
            hasRestaurant: true,
            restaurant: restaurant,
            isVerified: restaurant.is_verified || false,
            error: null,
          });
        } else {
          // User has no restaurants
          setRestaurantStatus({
            loading: false,
            hasRestaurant: false,
            restaurant: null,
            isVerified: false,
            error: null,
          });
        }
      } catch (error) {
        console.error("Error checking restaurant status:", error);
        setRestaurantStatus({
          loading: false,
          hasRestaurant: false,
          restaurant: null,
          isVerified: false,
          error: "Failed to check restaurant status",
        });
      }
    };

    checkRestaurantStatus();
  }, [user]);

  // Loading state
  if (restaurantStatus.loading) {
    return (
      <div className='min-h-screen bg-gray-50 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4'></div>
          <p className='text-gray-600'>Checking restaurant status...</p>
        </div>
      </div>
    );
  }

  // User doesn't have a restaurant - show create restaurant option
  if (!restaurantStatus.hasRestaurant) {
    return (
      <div className='min-h-screen bg-gray-50 py-12'>
        <div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='text-center mb-12'>
            <Store className='h-16 w-16 text-orange-500 mx-auto mb-4' />
            <h1 className='text-3xl font-bold text-gray-900 mb-4'>
              Welcome to Afghan Sufra Restaurant Portal
            </h1>
            <p className='text-lg text-gray-600 max-w-2xl mx-auto'>
              Start your journey as a restaurant partner. Create your restaurant
              profile and begin serving delicious food to customers.
            </p>
          </div>

          <Card className='max-w-2xl mx-auto'>
            <div className='text-center p-8'>
              <div className='bg-orange-100 rounded-full p-4 w-20 h-20 mx-auto mb-6'>
                <Plus className='h-12 w-12 text-orange-600' />
              </div>

              <h2 className='text-2xl font-semibold text-gray-900 mb-4'>
                Create Your Restaurant
              </h2>

              <p className='text-gray-600 mb-8'>
                Set up your restaurant profile with menu, location, and business
                details. Once submitted, our team will review and approve your
                restaurant.
              </p>

              <div className='space-y-4'>
                <Button
                  onClick={() => navigate("/register-restaurant")}
                  className='w-full bg-orange-600 hover:bg-orange-700 text-white py-3 px-6 rounded-lg font-medium'
                >
                  <Plus className='h-5 w-5 mr-2' />
                  Create Restaurant Profile
                </Button>

                <Button
                  variant='outline'
                  onClick={() => navigate("/restaurant-partner")}
                  className='w-full border-gray-300 text-gray-700 hover:bg-gray-50 py-3 px-6 rounded-lg font-medium'
                >
                  <FileText className='h-5 w-5 mr-2' />
                  Learn More About Partnership
                </Button>
              </div>
            </div>
          </Card>

          <div className='mt-12 grid md:grid-cols-3 gap-6'>
            <Card className='text-center p-6'>
              <div className='bg-blue-100 rounded-full p-3 w-12 h-12 mx-auto mb-4'>
                <FileText className='h-6 w-6 text-blue-600' />
              </div>
              <h3 className='font-semibold text-gray-900 mb-2'>
                1. Create Profile
              </h3>
              <p className='text-sm text-gray-600'>
                Fill out your restaurant details, menu, and business information
              </p>
            </Card>

            <Card className='text-center p-6'>
              <div className='bg-yellow-100 rounded-full p-3 w-12 h-12 mx-auto mb-4'>
                <Clock className='h-6 w-6 text-yellow-600' />
              </div>
              <h3 className='font-semibold text-gray-900 mb-2'>
                2. Admin Review
              </h3>
              <p className='text-sm text-gray-600'>
                Our team reviews your application and verifies your restaurant
              </p>
            </Card>

            <Card className='text-center p-6'>
              <div className='bg-green-100 rounded-full p-3 w-12 h-12 mx-auto mb-4'>
                <CheckCircle className='h-6 w-6 text-green-600' />
              </div>
              <h3 className='font-semibold text-gray-900 mb-2'>
                3. Start Selling
              </h3>
              <p className='text-sm text-gray-600'>
                Once approved, access all restaurant management features
              </p>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  // User has a restaurant but it's not verified - show pending approval
  if (restaurantStatus.hasRestaurant && !restaurantStatus.isVerified) {
    return (
      <div className='min-h-screen bg-gray-50 py-12'>
        <div className='max-w-4xl mx-auto px-4 sm:px-6 lg:px-8'>
          <Card className='max-w-2xl mx-auto'>
            <div className='text-center p-8'>
              <div className='bg-yellow-100 rounded-full p-4 w-20 h-20 mx-auto mb-6'>
                <Clock className='h-12 w-12 text-yellow-600' />
              </div>

              <h2 className='text-2xl font-semibold text-gray-900 mb-4'>
                Restaurant Under Review
              </h2>

              <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6'>
                <div className='flex items-center'>
                  <AlertCircle className='h-5 w-5 text-yellow-600 mr-2' />
                  <span className='text-yellow-800 font-medium'>
                    Pending Admin Approval
                  </span>
                </div>
              </div>

              <div className='text-left bg-gray-50 rounded-lg p-4 mb-6'>
                <h3 className='font-semibold text-gray-900 mb-2'>
                  Restaurant Details:
                </h3>
                <div className='space-y-2 text-sm text-gray-600'>
                  <p>
                    <span className='font-medium'>Name:</span>{" "}
                    {restaurantStatus.restaurant?.name}
                  </p>
                  <p>
                    <span className='font-medium'>Contact:</span>{" "}
                    {restaurantStatus.restaurant?.contact_number}
                  </p>
                  <p>
                    <span className='font-medium'>Status:</span>
                    <span className='ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800'>
                      Under Review
                    </span>
                  </p>
                </div>
              </div>

              <p className='text-gray-600 mb-8'>
                Thank you for submitting your restaurant profile! Our admin team
                is currently reviewing your application. You'll receive an email
                notification once your restaurant is approved and you can access
                all management features.
              </p>

              <div className='space-y-4'>
                <Button
                  variant='outline'
                  onClick={() => window.location.reload()}
                  className='w-full border-gray-300 text-gray-700 hover:bg-gray-50 py-3 px-6 rounded-lg font-medium mb-3'
                >
                  <Shield className='h-5 w-5 mr-2' />
                  Check Approval Status
                </Button>

                <Button
                  variant='primary'
                  onClick={() =>
                    (window.location.href = "http://localhost:5173")
                  }
                  className='w-full py-3 px-6 rounded-lg font-medium'
                >
                  Return to Home Page
                </Button>
              </div>
            </div>
          </Card>

          <div className='mt-8 text-center'>
            <p className='text-sm text-gray-500'>
              Questions about the approval process? Contact our support team.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // User has a verified restaurant - show full dashboard
  return children;
};

export default RestaurantStatusChecker;
