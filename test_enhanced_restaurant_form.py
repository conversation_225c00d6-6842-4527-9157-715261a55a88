#!/usr/bin/env python3
"""
Test enhanced restaurant registration form
"""

import requests
import json
import time
import os

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_enhanced_restaurant_creation():
    """Test restaurant creation with all enhanced fields"""
    print("🏪 Testing Enhanced Restaurant Creation...")
    print("=" * 60)
    
    # First, we need to authenticate as a restaurant user
    # Let's create a test restaurant user
    timestamp = int(time.time())
    
    # Create test user
    test_user = {
        "name": "Test Restaurant Owner",
        "user_name": f"restaurant_owner_{timestamp}",
        "email": f"restaurant{timestamp}@example.com",
        "phone": f"+93701{timestamp % 100000:05d}",
        "password": "TestPassword123",
        "confirm_password": "TestPassword123",
        "role": "restaurant"
    }
    
    print(f"📝 Creating restaurant user: {test_user['email']}")
    
    # Register user
    response = requests.post(
        f"{API_BASE_URL}/auth/register/",
        headers=HEADERS,
        data=json.dumps(test_user)
    )
    
    if response.status_code != 201:
        print(f"❌ User registration failed: {response.text}")
        return False
    
    user_result = response.json()
    print(f"✅ User created: {user_result['data']['email']}")
    
    # Verify email (simulate OTP verification)
    # For testing, we'll skip OTP and directly login
    
    # Login to get token
    login_data = {
        "email": test_user['email'],
        "password": test_user['password']
    }
    
    login_response = requests.post(
        f"{API_BASE_URL}/auth/login/",
        headers=HEADERS,
        data=json.dumps(login_data)
    )
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.text}")
        return False
    
    login_result = login_response.json()
    token = login_result['data']['access_token']
    
    # Update headers with token
    auth_headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    print(f"✅ User authenticated")
    
    # Now test restaurant creation with enhanced data
    restaurant_data = {
        "name": f"Test Restaurant {timestamp}",
        "description": "A beautiful Afghan restaurant serving traditional cuisine with modern touches. We pride ourselves on authentic flavors and excellent service.",
        "address": "123 Main Street, Kabul, Afghanistan",
        "contact_number": f"+93701{timestamp % 100000:05d}",
        "opening_time": "09:00",
        "closing_time": "23:00",
        "cuisine_type": "Afghan",
        "delivery_fee": "75",
        "minimum_order": "250",
        "average_preparation_time": "35",
        "accepts_cash": True,
        "accepts_card": True,
        "accepts_online_payment": True,
        "website": "https://testrestaurant.com",
        "facebook_url": "https://facebook.com/testrestaurant",
        "instagram_url": "https://instagram.com/testrestaurant",
        "twitter_url": "https://twitter.com/testrestaurant"
    }
    
    print(f"\n🏗️ Creating restaurant: {restaurant_data['name']}")
    
    # Create FormData equivalent for testing
    # Since we're testing via Python, we'll send as JSON for now
    # In real frontend, files would be sent as FormData
    
    restaurant_response = requests.post(
        f"{API_BASE_URL}/restaurant/restaurants/",
        headers=auth_headers,
        data=json.dumps(restaurant_data)
    )
    
    print(f"📡 Restaurant Creation Response:")
    print(f"   Status Code: {restaurant_response.status_code}")
    print(f"   Response: {restaurant_response.text}")
    
    if restaurant_response.status_code == 201:
        restaurant_result = restaurant_response.json()
        print("✅ Restaurant created successfully!")
        print(f"   Restaurant ID: {restaurant_result.get('id')}")
        print(f"   Name: {restaurant_result.get('name')}")
        print(f"   Verified: {restaurant_result.get('is_verified', False)}")
        print(f"   Website: {restaurant_result.get('website', 'N/A')}")
        print(f"   Social Media: {bool(restaurant_result.get('facebook_url'))}")
        return True
    else:
        print(f"❌ Restaurant creation failed")
        try:
            error_data = restaurant_response.json()
            print(f"   Error details: {error_data}")
        except:
            print(f"   Raw response: {restaurant_response.text}")
        return False

def test_restaurant_fields_validation():
    """Test that all enhanced fields are properly handled"""
    print("\n🔍 Testing Enhanced Fields Support...")
    print("=" * 60)
    
    # Test getting restaurant list to see field structure
    response = requests.get(
        f"{API_BASE_URL}/restaurant/restaurants/",
        headers=HEADERS
    )
    
    if response.status_code == 200:
        restaurants = response.json()
        print(f"📊 Found {len(restaurants)} restaurants")
        
        if restaurants:
            restaurant = restaurants[0]
            print("\n📋 Available Restaurant Fields:")
            
            enhanced_fields = [
                'logo', 'banner', 'website', 'facebook_url', 
                'instagram_url', 'twitter_url', 'average_preparation_time',
                'accepts_cash', 'accepts_card', 'accepts_online_payment',
                'min_order_amount'
            ]
            
            for field in enhanced_fields:
                value = restaurant.get(field, 'NOT_FOUND')
                status = "✅" if field in restaurant else "❌"
                print(f"   {status} {field}: {value}")
        
        return True
    else:
        print(f"❌ Failed to get restaurants: {response.text}")
        return False

def main():
    """Main test function"""
    print("🧪 Enhanced Restaurant Registration Form Test")
    print("=" * 60)
    
    # Test 1: Enhanced restaurant creation
    creation_success = test_enhanced_restaurant_creation()
    
    # Test 2: Field validation
    fields_success = test_restaurant_fields_validation()
    
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    if creation_success and fields_success:
        print("🎉 SUCCESS: Enhanced restaurant form is working!")
        print("✅ All enhanced fields supported")
        print("✅ Restaurant creation with full data works")
        print("✅ Backend properly handles new fields")
    else:
        print("❌ FAILED: Some tests failed")
        print(f"   Creation test: {'✅' if creation_success else '❌'}")
        print(f"   Fields test: {'✅' if fields_success else '❌'}")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
