import React, { useState } from "react";
import {
  Gift,
  Star,
  Clock,
  Check,
  AlertCircle,
  Sparkles,
  Tag,
  TruckIcon,
} from "lucide-react";
import { useLoyalty } from "../../context/LoyaltyContext";
import Card from "../common/Card";
import Button from "../common/Button";
import Badge from "../common/Badge";

const RewardsStore = ({ className = "" }) => {
  const {
    loyaltyData,
    redeemedRewards,
    redeemReward,
    REWARDS,
    LOYALTY_TIERS,
  } = useLoyalty();

  const [selectedReward, setSelectedReward] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [redeemResult, setRedeemResult] = useState(null);

  if (!loyaltyData) {
    return (
      <Card className={className}>
        <div className="p-8 text-center">
          <Gift size={48} className="mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-600 mb-2">
            Rewards Store
          </h3>
          <p className="text-gray-500">
            Join our loyalty program to start earning and redeeming rewards!
          </p>
        </div>
      </Card>
    );
  }

  const currentTier = LOYALTY_TIERS[loyaltyData.tier];

  const handleRedeemClick = (reward) => {
    setSelectedReward(reward);
    setShowConfirmation(true);
  };

  const confirmRedeem = () => {
    const result = redeemReward(selectedReward.id);
    setRedeemResult(result);
    setShowConfirmation(false);
    
    if (result.success) {
      setTimeout(() => {
        setRedeemResult(null);
        setSelectedReward(null);
      }, 3000);
    }
  };

  const cancelRedeem = () => {
    setShowConfirmation(false);
    setSelectedReward(null);
  };

  const getRewardIcon = (type) => {
    switch (type) {
      case "discount":
        return <Tag size={24} className="text-green-500" />;
      case "free_delivery":
        return <TruckIcon size={24} className="text-blue-500" />;
      case "free_item":
        return <Gift size={24} className="text-purple-500" />;
      default:
        return <Star size={24} className="text-orange-500" />;
    }
  };

  const formatExpiryDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const activeRewards = redeemedRewards.filter(
    (reward) => !reward.used && new Date(reward.expiresAt) > new Date()
  );

  const expiredRewards = redeemedRewards.filter(
    (reward) => !reward.used && new Date(reward.expiresAt) <= new Date()
  );

  const usedRewards = redeemedRewards.filter((reward) => reward.used);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Rewards Store</h2>
          <p className="text-gray-600">
            Redeem your points for amazing rewards and discounts
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-500">Available Points</p>
          <p className="text-2xl font-bold text-orange-600">
            {loyaltyData.availablePoints}
          </p>
        </div>
      </div>

      {/* Success/Error Messages */}
      {redeemResult && (
        <div
          className={`p-4 rounded-lg border ${
            redeemResult.success
              ? "bg-green-50 border-green-200"
              : "bg-red-50 border-red-200"
          }`}
        >
          <div className="flex items-center">
            {redeemResult.success ? (
              <Check size={20} className="text-green-600 mr-3" />
            ) : (
              <AlertCircle size={20} className="text-red-600 mr-3" />
            )}
            <div>
              <h4
                className={`font-medium ${
                  redeemResult.success ? "text-green-800" : "text-red-800"
                }`}
              >
                {redeemResult.success
                  ? "Reward Redeemed Successfully!"
                  : "Redemption Failed"}
              </h4>
              <p
                className={`text-sm ${
                  redeemResult.success ? "text-green-600" : "text-red-600"
                }`}
              >
                {redeemResult.success
                  ? `Your ${selectedReward?.name} is now available in your active rewards.`
                  : redeemResult.error}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Available Rewards */}
      <div>
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Sparkles size={20} className="text-orange-500 mr-2" />
          Available Rewards
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.values(REWARDS).map((reward) => {
            const canAfford = loyaltyData.availablePoints >= reward.pointsCost;
            
            return (
              <Card key={reward.id} className="relative overflow-hidden">
                <div className="p-6">
                  {/* Reward Icon */}
                  <div className="flex items-center justify-between mb-4">
                    {getRewardIcon(reward.type)}
                    <Badge
                      variant={canAfford ? "success" : "outline"}
                      className="font-semibold"
                    >
                      {reward.pointsCost} pts
                    </Badge>
                  </div>

                  {/* Reward Details */}
                  <h4 className="font-semibold text-lg mb-2">{reward.name}</h4>
                  <p className="text-gray-600 text-sm mb-4">{reward.description}</p>

                  {/* Additional Info */}
                  <div className="space-y-2 mb-4">
                    {reward.minimumOrder && (
                      <div className="flex items-center text-xs text-gray-500">
                        <Tag size={12} className="mr-1" />
                        <span>Min. order: ${reward.minimumOrder}</span>
                      </div>
                    )}
                    <div className="flex items-center text-xs text-gray-500">
                      <Clock size={12} className="mr-1" />
                      <span>Valid for {reward.validityDays} days</span>
                    </div>
                  </div>

                  {/* Redeem Button */}
                  <Button
                    variant={canAfford ? "primary" : "outline"}
                    size="small"
                    fullWidth
                    disabled={!canAfford}
                    onClick={() => handleRedeemClick(reward)}
                    icon={<Gift size={16} />}
                  >
                    {canAfford ? "Redeem Now" : "Not Enough Points"}
                  </Button>

                  {/* Tier Bonus Indicator */}
                  {loyaltyData.tier !== "BRONZE" && (
                    <div className="mt-3 text-center">
                      <Badge
                        variant="outline"
                        size="small"
                        style={{ color: currentTier.color, borderColor: currentTier.color }}
                      >
                        {currentTier.name} Member
                      </Badge>
                    </div>
                  )}
                </div>

                {/* Shine Effect for Available Rewards */}
                {canAfford && (
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 hover:opacity-20 transform -skew-x-12 transition-all duration-700 hover:translate-x-full" />
                )}
              </Card>
            );
          })}
        </div>
      </div>

      {/* Active Rewards */}
      {activeRewards.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Check size={20} className="text-green-500 mr-2" />
            Your Active Rewards ({activeRewards.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {activeRewards.map((reward) => (
              <Card key={reward.id} className="border-green-200 bg-green-50">
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    {getRewardIcon(reward.type)}
                    <Badge variant="success" size="small">
                      Active
                    </Badge>
                  </div>
                  <h4 className="font-semibold mb-1">{reward.name}</h4>
                  <p className="text-sm text-gray-600 mb-2">{reward.description}</p>
                  <div className="flex items-center text-xs text-gray-500">
                    <Clock size={12} className="mr-1" />
                    <span>Expires: {formatExpiryDate(reward.expiresAt)}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmation && selectedReward && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Confirm Redemption</h3>
              <div className="mb-4">
                <div className="flex items-center mb-2">
                  {getRewardIcon(selectedReward.type)}
                  <span className="ml-2 font-medium">{selectedReward.name}</span>
                </div>
                <p className="text-gray-600 text-sm mb-3">{selectedReward.description}</p>
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Points Required:</span>
                    <span className="font-semibold">{selectedReward.pointsCost}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Your Points:</span>
                    <span className="font-semibold">{loyaltyData.availablePoints}</span>
                  </div>
                  <div className="border-t border-gray-200 mt-2 pt-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Remaining Points:</span>
                      <span className="font-semibold text-orange-600">
                        {loyaltyData.availablePoints - selectedReward.pointsCost}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex space-x-3">
                <Button variant="outline" fullWidth onClick={cancelRedeem}>
                  Cancel
                </Button>
                <Button variant="primary" fullWidth onClick={confirmRedeem}>
                  Confirm Redemption
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Redemption History */}
      {(usedRewards.length > 0 || expiredRewards.length > 0) && (
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <Clock size={20} className="text-gray-500 mr-2" />
            Redemption History
          </h3>
          <Card>
            <div className="p-6">
              <div className="space-y-3">
                {[...usedRewards, ...expiredRewards].map((reward) => (
                  <div key={reward.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                    <div className="flex items-center space-x-3">
                      {getRewardIcon(reward.type)}
                      <div>
                        <p className="font-medium">{reward.name}</p>
                        <p className="text-sm text-gray-500">
                          Redeemed: {formatExpiryDate(reward.redeemedAt)}
                        </p>
                      </div>
                    </div>
                    <Badge
                      variant={reward.used ? "success" : "danger"}
                      size="small"
                    >
                      {reward.used ? "Used" : "Expired"}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default RewardsStore;
