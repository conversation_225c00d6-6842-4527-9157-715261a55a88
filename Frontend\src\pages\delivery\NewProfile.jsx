import React, { useState, useEffect } from "react";
import {
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Star,
  TruckIcon,
  CreditCard,
  Shield,
  Edit,
  Save,
  X,
  Upload,
  Camera,
  CheckCircle,
  AlertCircle,
  Key,
  Award,
  Clock,
  DollarSign,
  FileText,
  Settings,
  Bell,
  Eye,
  EyeOff,
  RefreshCw,
  Download,
  Trash2,
  Plus,
  Minus,
  Activity,
  Target,
  Zap,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import Badge from "../../components/common/Badge";
import Input from "../../components/common/Input";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";

function NewDeliveryProfile() {
  const { user } = useAuth();

  // State management
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({});
  const [avatarPreview, setAvatarPreview] = useState(null);

  // Load profile data
  const loadProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await deliveryAgentApi.getProfile();

      if (result.success) {
        setProfile(result.data);
        setFormData({
          phone_number: result.data.phone_number || "",
          emergency_contact: result.data.emergency_contact || "",
          address: result.data.address || "",
          vehicle_type: result.data.vehicle_type || "motorcycle",
          vehicle_model: result.data.vehicle_model || "",
          vehicle_color: result.data.vehicle_color || "",
          license_plate: result.data.license_plate || "",
          driving_license: result.data.driving_license || "",
          bank_name: result.data.bank_name || "",
          account_number: result.data.account_number || "",
          account_holder_name: result.data.account_holder_name || "",
        });
        setAvatarPreview(result.data.user?.avatar);
      } else {
        setError(result.error?.message || "Failed to load profile");
      }
    } catch (err) {
      console.error("Profile load error:", err);
      setError("Failed to load profile");
    } finally {
      setLoading(false);
    }
  };

  // Save profile changes
  const saveProfile = async () => {
    try {
      setSaving(true);
      setError(null);

      const result = await deliveryAgentApi.updateProfile(formData);

      if (result.success) {
        setProfile(result.data);
        setEditMode(false);
        // Show success message
        alert("Profile updated successfully!");
      } else {
        setError(result.error?.message || "Failed to update profile");
      }
    } catch (err) {
      console.error("Profile save error:", err);
      setError("Failed to update profile");
    } finally {
      setSaving(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle avatar upload
  const handleAvatarUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setAvatarPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  useEffect(() => {
    loadProfile();
  }, [user]);

  // Loading state
  if (loading) {
    return (
      <div className='min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-600 text-lg'>Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50'>
      {/* Header */}
      <div className='bg-white shadow-sm border-b'>
        <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
          <div className='flex justify-between items-center py-4'>
            <div>
              <h1 className='text-2xl font-bold text-gray-900'>Profile</h1>
              <p className='text-sm text-gray-500'>
                Manage your delivery agent profile and settings
              </p>
            </div>

            <div className='flex items-center space-x-3'>
              <Button
                variant='outline'
                onClick={loadProfile}
                disabled={loading}
                className='flex items-center space-x-2'
              >
                <RefreshCw
                  className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
                />
                <span>Refresh</span>
              </Button>

              {!editMode ? (
                <Button
                  onClick={() => setEditMode(true)}
                  className='flex items-center space-x-2'
                >
                  <Edit className='h-4 w-4' />
                  <span>Edit Profile</span>
                </Button>
              ) : (
                <div className='flex space-x-2'>
                  <Button
                    variant='outline'
                    onClick={() => {
                      setEditMode(false);
                      setError(null);
                    }}
                    className='flex items-center space-x-2'
                  >
                    <X className='h-4 w-4' />
                    <span>Cancel</span>
                  </Button>
                  <Button
                    onClick={saveProfile}
                    disabled={saving}
                    className='flex items-center space-x-2'
                  >
                    <Save className='h-4 w-4' />
                    <span>{saving ? "Saving..." : "Save Changes"}</span>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6'>
        {/* Error Message */}
        {error && (
          <Card className='mb-6 p-4 bg-red-50 border-red-200'>
            <div className='flex items-center space-x-2'>
              <AlertCircle className='h-5 w-5 text-red-500' />
              <p className='text-red-700'>{error}</p>
              <Button
                variant='outline'
                size='sm'
                onClick={() => setError(null)}
                className='ml-auto'
              >
                Dismiss
              </Button>
            </div>
          </Card>
        )}

        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
          {/* Profile Overview */}
          <Card className='lg:col-span-1 p-6'>
            <div className='text-center'>
              {/* Avatar */}
              <div className='relative inline-block mb-4'>
                <div className='w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden'>
                  {avatarPreview ? (
                    <img
                      src={avatarPreview}
                      alt='Profile'
                      className='w-full h-full object-cover'
                    />
                  ) : (
                    <User className='h-16 w-16 text-gray-400' />
                  )}
                </div>
                {editMode && (
                  <label className='absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700'>
                    <Camera className='h-4 w-4' />
                    <input
                      type='file'
                      accept='image/*'
                      onChange={handleAvatarUpload}
                      className='hidden'
                    />
                  </label>
                )}
              </div>

              {/* Basic Info */}
              <h2 className='text-xl font-bold text-gray-900 mb-1'>
                {profile?.user?.name || "No name provided"}
              </h2>
              <p className='text-gray-500 mb-2'>
                {profile?.user?.email || "No email provided"}
              </p>
              <p className='text-sm text-gray-500 mb-4'>
                Agent ID: {profile?.agent_id}
              </p>

              {/* Status */}
              <div className='flex justify-center space-x-2 mb-4'>
                <Badge
                  variant={
                    profile?.status === "active" ? "success" : "secondary"
                  }
                >
                  {profile?.status}
                </Badge>
                <Badge variant={profile?.is_verified ? "success" : "warning"}>
                  {profile?.is_verified ? "Verified" : "Pending"}
                </Badge>
              </div>

              {/* Quick Stats */}
              <div className='grid grid-cols-2 gap-4 text-center'>
                <div>
                  <p className='text-2xl font-bold text-blue-600'>
                    {profile?.rating?.toFixed(1) || "0.0"}
                  </p>
                  <p className='text-xs text-gray-500'>Rating</p>
                </div>
                <div>
                  <p className='text-2xl font-bold text-green-600'>
                    {profile?.total_deliveries || 0}
                  </p>
                  <p className='text-xs text-gray-500'>Deliveries</p>
                </div>
              </div>
            </div>
          </Card>

          {/* Profile Details */}
          <div className='lg:col-span-2 space-y-6'>
            {/* Personal Information */}
            <Card className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                Personal Information
              </h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Phone Number
                  </label>
                  {editMode ? (
                    <Input
                      type='tel'
                      value={formData.phone_number}
                      onChange={(e) =>
                        handleInputChange("phone_number", e.target.value)
                      }
                      placeholder='Enter phone number'
                    />
                  ) : (
                    <p className='text-gray-900'>
                      {profile?.phone_number || "Not provided"}
                    </p>
                  )}
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Emergency Contact
                  </label>
                  {editMode ? (
                    <Input
                      type='tel'
                      value={formData.emergency_contact}
                      onChange={(e) =>
                        handleInputChange("emergency_contact", e.target.value)
                      }
                      placeholder='Enter emergency contact'
                    />
                  ) : (
                    <p className='text-gray-900'>
                      {profile?.emergency_contact || "Not provided"}
                    </p>
                  )}
                </div>

                <div className='md:col-span-2'>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Address
                  </label>
                  {editMode ? (
                    <Input
                      type='text'
                      value={formData.address}
                      onChange={(e) =>
                        handleInputChange("address", e.target.value)
                      }
                      placeholder='Enter address'
                    />
                  ) : (
                    <p className='text-gray-900'>
                      {profile?.address || "Not provided"}
                    </p>
                  )}
                </div>
              </div>
            </Card>

            {/* Vehicle Information */}
            <Card className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                Vehicle Information
              </h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Vehicle Type
                  </label>
                  {editMode ? (
                    <select
                      value={formData.vehicle_type}
                      onChange={(e) =>
                        handleInputChange("vehicle_type", e.target.value)
                      }
                      className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                    >
                      <option value='motorcycle'>Motorcycle</option>
                      <option value='bicycle'>Bicycle</option>
                      <option value='car'>Car</option>
                      <option value='scooter'>Scooter</option>
                      <option value='van'>Van</option>
                    </select>
                  ) : (
                    <p className='text-gray-900 capitalize'>
                      {profile?.vehicle_type || "Not provided"}
                    </p>
                  )}
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Vehicle Model
                  </label>
                  {editMode ? (
                    <Input
                      type='text'
                      value={formData.vehicle_model}
                      onChange={(e) =>
                        handleInputChange("vehicle_model", e.target.value)
                      }
                      placeholder='Enter vehicle model'
                    />
                  ) : (
                    <p className='text-gray-900'>
                      {profile?.vehicle_model || "Not provided"}
                    </p>
                  )}
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Vehicle Color
                  </label>
                  {editMode ? (
                    <Input
                      type='text'
                      value={formData.vehicle_color}
                      onChange={(e) =>
                        handleInputChange("vehicle_color", e.target.value)
                      }
                      placeholder='Enter vehicle color'
                    />
                  ) : (
                    <p className='text-gray-900'>
                      {profile?.vehicle_color || "Not provided"}
                    </p>
                  )}
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    License Plate
                  </label>
                  {editMode ? (
                    <Input
                      type='text'
                      value={formData.license_plate}
                      onChange={(e) =>
                        handleInputChange("license_plate", e.target.value)
                      }
                      placeholder='Enter license plate'
                    />
                  ) : (
                    <p className='text-gray-900'>
                      {profile?.license_plate || "Not provided"}
                    </p>
                  )}
                </div>

                <div className='md:col-span-2'>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Driving License
                  </label>
                  {editMode ? (
                    <Input
                      type='text'
                      value={formData.driving_license}
                      onChange={(e) =>
                        handleInputChange("driving_license", e.target.value)
                      }
                      placeholder='Enter driving license number'
                    />
                  ) : (
                    <p className='text-gray-900'>
                      {profile?.driving_license || "Not provided"}
                    </p>
                  )}
                </div>
              </div>
            </Card>

            {/* Banking Information */}
            <Card className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                Banking Information
              </h3>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Bank Name
                  </label>
                  {editMode ? (
                    <Input
                      type='text'
                      value={formData.bank_name}
                      onChange={(e) =>
                        handleInputChange("bank_name", e.target.value)
                      }
                      placeholder='Enter bank name'
                    />
                  ) : (
                    <p className='text-gray-900'>
                      {profile?.bank_name || "Not provided"}
                    </p>
                  )}
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Account Holder Name
                  </label>
                  {editMode ? (
                    <Input
                      type='text'
                      value={formData.account_holder_name}
                      onChange={(e) =>
                        handleInputChange("account_holder_name", e.target.value)
                      }
                      placeholder='Enter account holder name'
                    />
                  ) : (
                    <p className='text-gray-900'>
                      {profile?.account_holder_name || "Not provided"}
                    </p>
                  )}
                </div>

                <div className='md:col-span-2'>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Account Number
                  </label>
                  {editMode ? (
                    <Input
                      type='text'
                      value={formData.account_number}
                      onChange={(e) =>
                        handleInputChange("account_number", e.target.value)
                      }
                      placeholder='Enter account number'
                    />
                  ) : (
                    <p className='text-gray-900'>
                      {profile?.account_number
                        ? "****" + profile.account_number.slice(-4)
                        : "Not provided"}
                    </p>
                  )}
                </div>
              </div>
            </Card>

            {/* Performance Summary */}
            <Card className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4'>
                Performance Summary
              </h3>

              <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
                <div className='text-center'>
                  <div className='flex items-center justify-center w-12 h-12 bg-blue-100 rounded-full mx-auto mb-2'>
                    <Star className='h-6 w-6 text-blue-600' />
                  </div>
                  <p className='text-2xl font-bold text-blue-600'>
                    {profile?.rating?.toFixed(1) || "0.0"}
                  </p>
                  <p className='text-sm text-gray-600'>Rating</p>
                </div>

                <div className='text-center'>
                  <div className='flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto mb-2'>
                    <Target className='h-6 w-6 text-green-600' />
                  </div>
                  <p className='text-2xl font-bold text-green-600'>
                    {profile?.completion_rate?.toFixed(1) || "0.0"}%
                  </p>
                  <p className='text-sm text-gray-600'>Completion</p>
                </div>

                <div className='text-center'>
                  <div className='flex items-center justify-center w-12 h-12 bg-purple-100 rounded-full mx-auto mb-2'>
                    <Activity className='h-6 w-6 text-purple-600' />
                  </div>
                  <p className='text-2xl font-bold text-purple-600'>
                    {profile?.total_deliveries || 0}
                  </p>
                  <p className='text-sm text-gray-600'>Deliveries</p>
                </div>

                <div className='text-center'>
                  <div className='flex items-center justify-center w-12 h-12 bg-yellow-100 rounded-full mx-auto mb-2'>
                    <Award className='h-6 w-6 text-yellow-600' />
                  </div>
                  <p className='text-2xl font-bold text-yellow-600'>
                    {profile?.successful_deliveries || 0}
                  </p>
                  <p className='text-sm text-gray-600'>Successful</p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

export default NewDeliveryProfile;
