import React, { useState, useEffect } from "react";
import { useAuth } from "../../context/AuthContext";
import { ratingApi } from "../../utils/ratingApi";
import {
  Bell,
  Star,
  X,
  Check,
  MessageSquare,
  Clock,
  AlertCircle,
  CheckCircle,
} from "lucide-react";

const RatingNotifications = ({ onClose }) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (user?.role === "restaurant") {
      loadRecentRatings();
    }
  }, [user]);

  const loadRecentRatings = async () => {
    // Double-check user role before making API call
    if (!user || user.role !== "restaurant") {
      console.log(
        "🚫 RatingNotifications: User is not a restaurant owner, skipping API call"
      );
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const ratings = await ratingApi.getMyRestaurantRatings();

      // Convert recent ratings to notifications
      const recentRatings = ratings
        .filter((rating) => {
          // Show ratings from last 7 days
          const ratingDate = new Date(rating.created_at);
          const weekAgo = new Date();
          weekAgo.setDate(weekAgo.getDate() - 7);
          return ratingDate > weekAgo;
        })
        .slice(0, 10) // Limit to 10 most recent
        .map((rating) => ({
          id: rating.id,
          type: "new_rating",
          title: `New ${rating.overall_rating}-star rating`,
          message: `${rating.customer_name} rated your restaurant`,
          rating: rating.overall_rating,
          customerName: rating.customer_name,
          reviewText: rating.review_text,
          restaurantName: rating.restaurant_name,
          timestamp: rating.created_at,
          isRead: false,
        }));

      setNotifications(recentRatings);
    } catch (err) {
      console.error("Error loading rating notifications:", err);
      setError("Failed to load notifications");
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = (notificationId) => {
    setNotifications((prev) =>
      prev.map((notif) =>
        notif.id === notificationId ? { ...notif, isRead: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notif) => ({ ...notif, isRead: true }))
    );
  };

  const getNotificationIcon = (notification) => {
    if (notification.rating >= 4) {
      return <CheckCircle className='text-green-500' size={20} />;
    } else if (notification.rating >= 3) {
      return <Star className='text-yellow-500' size={20} />;
    } else {
      return <AlertCircle className='text-red-500' size={20} />;
    }
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now - date) / (1000 * 60));
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const renderStars = (rating) => {
    return (
      <div className='flex items-center'>
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={12}
            className={`${
              star <= rating ? "text-yellow-400 fill-current" : "text-gray-300"
            }`}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
        <div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
          <div className='animate-pulse'>
            <div className='h-6 bg-gray-200 rounded w-3/4 mb-4'></div>
            <div className='space-y-3'>
              {[1, 2, 3].map((i) => (
                <div key={i} className='h-16 bg-gray-200 rounded'></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden'>
        {/* Header */}
        <div className='flex items-center justify-between p-6 border-b border-gray-200'>
          <div className='flex items-center space-x-3'>
            <Bell className='text-blue-600' size={24} />
            <h2 className='text-xl font-semibold text-gray-900'>
              Rating Notifications
            </h2>
            {notifications.filter((n) => !n.isRead).length > 0 && (
              <span className='bg-red-500 text-white text-xs px-2 py-1 rounded-full'>
                {notifications.filter((n) => !n.isRead).length}
              </span>
            )}
          </div>
          <div className='flex items-center space-x-2'>
            {notifications.some((n) => !n.isRead) && (
              <button
                onClick={markAllAsRead}
                className='text-sm text-blue-600 hover:text-blue-800'
              >
                Mark all read
              </button>
            )}
            <button
              onClick={onClose}
              className='text-gray-400 hover:text-gray-600'
            >
              <X size={24} />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className='overflow-y-auto max-h-[60vh]'>
          {error && (
            <div className='p-6 bg-red-50 border-b border-red-200'>
              <p className='text-red-800'>{error}</p>
            </div>
          )}

          {notifications.length === 0 ? (
            <div className='p-8 text-center'>
              <Bell className='mx-auto mb-4 text-gray-300' size={48} />
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                No recent notifications
              </h3>
              <p className='text-gray-600'>
                New rating notifications will appear here
              </p>
            </div>
          ) : (
            <div className='divide-y divide-gray-200'>
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-6 hover:bg-gray-50 transition-colors ${
                    !notification.isRead ? "bg-blue-50" : ""
                  }`}
                >
                  <div className='flex items-start space-x-4'>
                    <div className='flex-shrink-0'>
                      {getNotificationIcon(notification)}
                    </div>

                    <div className='flex-1 min-w-0'>
                      <div className='flex items-center justify-between mb-2'>
                        <h4
                          className={`text-sm font-medium ${
                            !notification.isRead
                              ? "text-gray-900"
                              : "text-gray-700"
                          }`}
                        >
                          {notification.title}
                        </h4>
                        <div className='flex items-center space-x-2'>
                          <Clock className='text-gray-400' size={12} />
                          <span className='text-xs text-gray-500'>
                            {formatTimestamp(notification.timestamp)}
                          </span>
                        </div>
                      </div>

                      <p className='text-sm text-gray-600 mb-2'>
                        {notification.message}
                      </p>

                      <div className='flex items-center space-x-3 mb-2'>
                        {renderStars(notification.rating)}
                        <span className='text-sm text-gray-600'>
                          for {notification.restaurantName}
                        </span>
                      </div>

                      {notification.reviewText && (
                        <div className='bg-gray-100 p-3 rounded-lg mt-2'>
                          <div className='flex items-center space-x-2 mb-1'>
                            <MessageSquare
                              size={14}
                              className='text-gray-500'
                            />
                            <span className='text-xs text-gray-500'>
                              Review:
                            </span>
                          </div>
                          <p className='text-sm text-gray-700 italic'>
                            "{notification.reviewText}"
                          </p>
                        </div>
                      )}
                    </div>

                    {!notification.isRead && (
                      <button
                        onClick={() => markAsRead(notification.id)}
                        className='flex-shrink-0 text-blue-600 hover:text-blue-800'
                        title='Mark as read'
                      >
                        <Check size={16} />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className='p-4 border-t border-gray-200 bg-gray-50'>
          <div className='flex items-center justify-between'>
            <p className='text-sm text-gray-600'>
              Showing notifications from the last 7 days
            </p>
            <button
              onClick={onClose}
              className='px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm'
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RatingNotifications;
