import React from 'react';
import { X, RotateCcw } from 'lucide-react';
import { useFilters } from '../../context/FiltersContext';

const ActiveFilters = () => {
  const { 
    filters, 
    filterOptions, 
    appliedFiltersCount, 
    clearAllFilters, 
    clearFilter,
    toggleArrayFilter,
    updateFilter
  } = useFilters();

  if (appliedFiltersCount === 0) return null;

  const getActiveFilterChips = () => {
    const chips = [];

    // Price Range
    if (filters.priceRange[0] !== 1 || filters.priceRange[1] !== 4) {
      const minPrice = filterOptions.priceRanges.find(p => p.value === filters.priceRange[0]);
      const maxPrice = filterOptions.priceRanges.find(p => p.value === filters.priceRange[1]);
      
      if (filters.priceRange[0] === filters.priceRange[1]) {
        chips.push({
          id: 'price-range',
          label: `${minPrice?.label} (${minPrice?.description})`,
          onRemove: () => clearFilter('priceRange')
        });
      } else {
        chips.push({
          id: 'price-range',
          label: `${minPrice?.label} - ${maxPrice?.label}`,
          onRemove: () => clearFilter('priceRange')
        });
      }
    }

    // Cuisines
    filters.cuisines.forEach(cuisine => {
      chips.push({
        id: `cuisine-${cuisine}`,
        label: cuisine,
        onRemove: () => toggleArrayFilter('cuisines', cuisine)
      });
    });

    // Dietary Restrictions
    filters.dietaryRestrictions.forEach(diet => {
      const dietOption = filterOptions.dietaryRestrictions.find(d => d.id === diet);
      chips.push({
        id: `diet-${diet}`,
        label: `${dietOption?.icon} ${dietOption?.label}`,
        onRemove: () => toggleArrayFilter('dietaryRestrictions', diet)
      });
    });

    // Delivery Time
    if (filters.deliveryTime) {
      const timeOption = filterOptions.deliveryTimes.find(t => t.value === filters.deliveryTime);
      chips.push({
        id: 'delivery-time',
        label: timeOption?.label,
        onRemove: () => updateFilter('deliveryTime', null)
      });
    }

    // Rating
    if (filters.rating) {
      const ratingOption = filterOptions.ratings.find(r => r.value === filters.rating);
      chips.push({
        id: 'rating',
        label: ratingOption?.label,
        onRemove: () => updateFilter('rating', null)
      });
    }

    // Distance
    if (filters.distance) {
      const distanceOption = filterOptions.distances.find(d => d.value === filters.distance);
      chips.push({
        id: 'distance',
        label: distanceOption?.label,
        onRemove: () => updateFilter('distance', null)
      });
    }

    // Features
    filters.features.forEach(feature => {
      const featureOption = filterOptions.features.find(f => f.id === feature);
      chips.push({
        id: `feature-${feature}`,
        label: `${featureOption?.icon} ${featureOption?.label}`,
        onRemove: () => toggleArrayFilter('features', feature)
      });
    });

    return chips;
  };

  const activeChips = getActiveFilterChips();

  return (
    <div className="flex items-center space-x-2 flex-wrap gap-2">
      <div className="flex items-center space-x-2 text-sm text-gray-600">
        <span className="font-medium">Active filters:</span>
        <span className="bg-primary-100 text-primary-700 px-2 py-1 rounded-full text-xs font-medium">
          {appliedFiltersCount}
        </span>
      </div>

      <div className="flex flex-wrap gap-2">
        {activeChips.map((chip) => (
          <div
            key={chip.id}
            className="flex items-center space-x-1 bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm border border-primary-200"
          >
            <span>{chip.label}</span>
            <button
              onClick={chip.onRemove}
              className="hover:bg-primary-200 rounded-full p-0.5 transition-colors"
            >
              <X size={12} />
            </button>
          </div>
        ))}
      </div>

      <button
        onClick={clearAllFilters}
        className="flex items-center space-x-1 text-gray-500 hover:text-gray-700 text-sm transition-colors"
      >
        <RotateCcw size={14} />
        <span>Clear all</span>
      </button>
    </div>
  );
};

export default ActiveFilters;
