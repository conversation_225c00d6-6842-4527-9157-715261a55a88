import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "./AuthContext";
import { API_BASE_URL } from "../config/api";

const FavoritesContext = createContext();

export const useFavorites = () => {
  const context = useContext(FavoritesContext);
  if (!context) {
    throw new Error("useFavorites must be used within a FavoritesProvider");
  }
  return context;
};

export const FavoritesProvider = ({ children }) => {
  const { user } = useAuth();
  const [favorites, setFavorites] = useState([]);
  const [loading, setLoading] = useState(false);

  // Get authentication token
  const getAuthToken = () => {
    if (user && user.access_token) {
      return user.access_token;
    }

    // Fallback: try to get from localStorage
    const storedUser = localStorage.getItem("afghanSofraUser");

    if (storedUser) {
      try {
        const parsedUser = JSON.parse(storedUser);
        return parsedUser.access_token;
      } catch (e) {
        console.error("Error parsing stored user:", e);
      }
    }

    return null;
  };

  // Fetch favorites from backend API
  const fetchFavorites = async () => {
    if (!user) {
      setFavorites([]);
      return { success: true, data: [] };
    }

    try {
      setLoading(true);
      const token = getAuthToken();
      if (!token) {
        return { success: false, error: "No authentication token found" };
      }

      const response = await fetch(`${API_BASE_URL}/customer/favorites/`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log("🔍 FavoritesContext - Fetched favorites from API:", data);
        setFavorites(data.data || []);
        return { success: true, data: data.data || [] };
      } else {
        console.error(
          "Failed to fetch favorites:",
          data.error || response.status
        );
        setFavorites([]);
        return {
          success: false,
          error: data.error || "Failed to fetch favorites",
        };
      }
    } catch (error) {
      console.error("Error fetching favorites:", error);
      setFavorites([]);
      return { success: false, error: "Network error occurred" };
    } finally {
      setLoading(false);
    }
  };

  // Load favorites when user changes
  useEffect(() => {
    if (user) {
      fetchFavorites();
    } else {
      setFavorites([]);
    }
  }, [user]);

  const addToFavorites = async (restaurant) => {
    if (!user) {
      return { success: false, error: "Please login to add favorites" };
    }

    // Optimistic update
    const optimisticFavorite = {
      id: restaurant.id,
      name: restaurant.name,
      logo: restaurant.logo,
      banner: restaurant.banner,
      rating: restaurant.rating,
      delivery_fee: restaurant.delivery_fee,
      cuisine_types: restaurant.cuisine_types || [],
      is_favorite: true,
    };

    setFavorites((prev) => {
      const exists = prev.some((fav) => fav.id === restaurant.id);
      if (!exists) {
        return [...prev, optimisticFavorite];
      }
      return prev;
    });

    try {
      const token = getAuthToken();
      if (!token) {
        return { success: false, error: "No authentication token found" };
      }

      const response = await fetch(`${API_BASE_URL}/customer/favorites/add/`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          restaurant_id: restaurant.id,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log("🔍 FavoritesContext - Added to favorites:", data);
        // Refresh favorites list to get accurate data
        await fetchFavorites();
        return {
          success: true,
          message: data.message || "Added to favorites!",
          data: data.data,
        };
      } else {
        console.error("Failed to add to favorites:", data);
        // Revert optimistic update
        setFavorites((prev) => prev.filter((fav) => fav.id !== restaurant.id));
        return {
          success: false,
          error: data.error || "Failed to add to favorites",
        };
      }
    } catch (error) {
      console.error("Error adding to favorites:", error);
      // Revert optimistic update
      setFavorites((prev) => prev.filter((fav) => fav.id !== restaurant.id));
      return { success: false, error: "Network error occurred" };
    }
  };

  const removeFromFavorites = async (restaurantId) => {
    if (!user) {
      return { success: false, error: "Please login to manage favorites" };
    }

    // Store the removed item for potential rollback
    const removedFavorite = favorites.find((fav) => fav.id === restaurantId);

    // Optimistic update
    setFavorites((prev) => prev.filter((fav) => fav.id !== restaurantId));

    try {
      const token = getAuthToken();
      if (!token) {
        return { success: false, error: "No authentication token found" };
      }

      const response = await fetch(
        `${API_BASE_URL}/customer/favorites/remove/${restaurantId}/`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        console.log("🔍 FavoritesContext - Removed from favorites:", data);
        return {
          success: true,
          message: data.message || "Removed from favorites!",
          data: data.data,
        };
      } else {
        console.error("Failed to remove from favorites:", data);
        // Revert optimistic update
        if (removedFavorite) {
          setFavorites((prev) => [...prev, removedFavorite]);
        }
        return {
          success: false,
          error: data.error || "Failed to remove from favorites",
        };
      }
    } catch (error) {
      console.error("Error removing from favorites:", error);
      // Revert optimistic update
      if (removedFavorite) {
        setFavorites((prev) => [...prev, removedFavorite]);
      }
      return { success: false, error: "Network error occurred" };
    }
  };

  const toggleFavorite = async (restaurant) => {
    if (!user) {
      return { success: false, error: "Please login to manage favorites" };
    }

    // Validate restaurant object
    if (!restaurant || !restaurant.id) {
      return { success: false, error: "Invalid restaurant data" };
    }

    try {
      const token = getAuthToken();

      if (!token) {
        return { success: false, error: "No authentication token found" };
      }

      const response = await fetch(
        `${API_BASE_URL}/customer/favorites/toggle/`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            restaurant_id: restaurant.id,
          }),
        }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        // Update favorites list based on the action
        if (data.data.action === "added") {
          const optimisticFavorite = {
            id: restaurant.id,
            name: restaurant.name,
            logo: restaurant.logo,
            banner: restaurant.banner,
            rating: restaurant.rating,
            delivery_fee: restaurant.delivery_fee,
            cuisine_types: restaurant.cuisine_types || [],
            is_favorite: true,
          };
          setFavorites((prev) => {
            const exists = prev.some((fav) => fav.id === restaurant.id);
            if (!exists) {
              return [...prev, optimisticFavorite];
            }
            return prev;
          });
        } else {
          setFavorites((prev) =>
            prev.filter((fav) => fav.id !== restaurant.id)
          );
        }

        return {
          success: true,
          message: data.message,
          data: data.data,
        };
      } else {
        console.error("Failed to toggle favorite:", data);

        // Fallback to individual add/remove endpoints
        const isFavoriteNow = favorites.some((fav) => fav.id === restaurant.id);

        if (isFavoriteNow) {
          return await removeFromFavorites(restaurant.id);
        } else {
          return await addToFavorites(restaurant);
        }
      }
    } catch (error) {
      console.error("Error toggling favorite:", error);

      // Fallback to individual add/remove endpoints
      try {
        const isFavoriteNow = favorites.some((fav) => fav.id === restaurant.id);

        if (isFavoriteNow) {
          return await removeFromFavorites(restaurant.id);
        } else {
          return await addToFavorites(restaurant);
        }
      } catch (fallbackError) {
        console.error("Fallback method also failed:", fallbackError);
        return { success: false, error: "Network error occurred" };
      }
    }
  };

  const isFavorite = (restaurantId) => {
    return favorites.some((fav) => fav.id === restaurantId);
  };

  const clearFavorites = async () => {
    if (!user) {
      return { success: false, error: "Please login to manage favorites" };
    }

    // Store current favorites for potential rollback
    const currentFavorites = [...favorites];

    // Optimistic update
    setFavorites([]);

    try {
      const token = getAuthToken();
      if (!token) {
        return { success: false, error: "No authentication token found" };
      }

      const response = await fetch(
        `${API_BASE_URL}/customer/favorites/clear/`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();

      if (response.ok && data.success) {
        console.log("🔍 FavoritesContext - Cleared all favorites:", data);
        return {
          success: true,
          message: data.message || "All favorites cleared!",
          data: data.data,
        };
      } else {
        console.error("Failed to clear favorites:", data);
        // Revert optimistic update
        setFavorites(currentFavorites);
        return {
          success: false,
          error: data.error || "Failed to clear favorites",
        };
      }
    } catch (error) {
      console.error("Error clearing favorites:", error);
      // Revert optimistic update
      setFavorites(currentFavorites);
      return { success: false, error: "Network error occurred" };
    }
  };

  const getFavoritesByCategory = (category) => {
    if (category === "All") return favorites;
    return favorites.filter((fav) => {
      // Handle both old format (cuisine array) and new format (cuisine_types array)
      const cuisines = fav.cuisine_types || fav.cuisine || [];
      return cuisines.some((c) =>
        (typeof c === "string" ? c : c.name || c)
          .toLowerCase()
          .includes(category.toLowerCase())
      );
    });
  };

  const value = {
    favorites,
    loading,
    fetchFavorites,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    isFavorite,
    clearFavorites,
    getFavoritesByCategory,
    favoritesCount: favorites.length,
  };

  return (
    <FavoritesContext.Provider value={value}>
      {children}
    </FavoritesContext.Provider>
  );
};

export default FavoritesContext;
