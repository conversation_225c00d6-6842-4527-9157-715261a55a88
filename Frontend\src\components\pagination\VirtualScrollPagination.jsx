import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Loader2, AlertCircle } from 'lucide-react';

const VirtualScrollPagination = ({
  items = [],
  itemHeight = 80,
  containerHeight = 400,
  renderItem,
  onLoadMore,
  hasMore = false,
  loading = false,
  error = null,
  onRetry,
  bufferSize = 5,
  threshold = 5,
  className = '',
  overscan = 5,
  estimatedItemSize,
}) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 0 });
  const listRef = useRef(null);
  const loadingRef = useRef(false);

  // Memoized item data for performance
  const itemData = useMemo(() => ({
    items,
    renderItem,
  }), [items, renderItem]);

  // <PERSON>le scroll to load more items
  const handleItemsRendered = useCallback(({ visibleStartIndex, visibleStopIndex }) => {
    setVisibleRange({ start: visibleStartIndex, end: visibleStopIndex });
    
    // Check if we need to load more items
    if (
      hasMore &&
      !loading &&
      !loadingRef.current &&
      onLoadMore &&
      visibleStopIndex >= items.length - threshold
    ) {
      loadingRef.current = true;
      onLoadMore().finally(() => {
        loadingRef.current = false;
      });
    }
  }, [hasMore, loading, onLoadMore, items.length, threshold]);

  // Item renderer component
  const ItemRenderer = useCallback(({ index, style, data }) => {
    const { items, renderItem } = data;
    const item = items[index];
    
    if (!item) {
      return (
        <div style={style} className="flex items-center justify-center">
          <div className="animate-pulse bg-gray-200 rounded h-16 w-full mx-4"></div>
        </div>
      );
    }

    return (
      <div style={style}>
        {renderItem(item, index)}
      </div>
    );
  }, []);

  // Scroll to specific item
  const scrollToItem = useCallback((index, align = 'auto') => {
    if (listRef.current) {
      listRef.current.scrollToItem(index, align);
    }
  }, []);

  // Get current scroll position
  const getScrollPosition = useCallback(() => {
    if (listRef.current) {
      return listRef.current.state.scrollOffset;
    }
    return 0;
  }, []);

  // Loading indicator component
  const LoadingIndicator = () => (
    <div className="flex items-center justify-center py-4">
      <Loader2 className="h-5 w-5 animate-spin text-primary-500 mr-2" />
      <span className="text-gray-600">Loading more items...</span>
    </div>
  );

  // Error indicator component
  const ErrorIndicator = () => (
    <div className="flex items-center justify-center py-4">
      <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
      <span className="text-red-600 mr-2">Failed to load items</span>
      {onRetry && (
        <button
          onClick={onRetry}
          className="text-primary-600 hover:text-primary-700 font-medium"
        >
          Retry
        </button>
      )}
    </div>
  );

  // End of list indicator
  const EndIndicator = () => (
    <div className="flex items-center justify-center py-4">
      <span className="text-gray-500 text-sm">
        You've reached the end • {items.length} items total
      </span>
    </div>
  );

  return (
    <div className={`virtual-scroll-container ${className}`}>
      {/* Virtual list */}
      <List
        ref={listRef}
        height={containerHeight}
        itemCount={items.length}
        itemSize={estimatedItemSize || itemHeight}
        itemData={itemData}
        onItemsRendered={handleItemsRendered}
        overscanCount={overscan}
        className="virtual-scroll-list"
      >
        {ItemRenderer}
      </List>

      {/* Loading state */}
      {loading && <LoadingIndicator />}

      {/* Error state */}
      {error && <ErrorIndicator />}

      {/* End of list */}
      {!hasMore && !loading && !error && items.length > 0 && <EndIndicator />}

      {/* Empty state */}
      {items.length === 0 && !loading && !error && (
        <div className="flex items-center justify-center py-8">
          <span className="text-gray-500">No items to display</span>
        </div>
      )}

      {/* Debug info (development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-2 text-xs text-gray-400 space-y-1">
          <div>Visible: {visibleRange.start} - {visibleRange.end}</div>
          <div>Total items: {items.length}</div>
          <div>Has more: {hasMore ? 'Yes' : 'No'}</div>
          <div>Loading: {loading ? 'Yes' : 'No'}</div>
        </div>
      )}
    </div>
  );
};

// Higher-order component for virtual scroll with pagination
export const withVirtualScrollPagination = (WrappedComponent) => {
  return function VirtualScrollWrapper(props) {
    const {
      data = [],
      fetchMore,
      hasNextPage = false,
      isFetchingNextPage = false,
      error,
      refetch,
      itemHeight = 80,
      containerHeight = 400,
      ...otherProps
    } = props;

    const renderItem = useCallback((item, index) => (
      <WrappedComponent
        key={item.id || index}
        item={item}
        index={index}
        {...otherProps}
      />
    ), [otherProps]);

    return (
      <VirtualScrollPagination
        items={data}
        itemHeight={itemHeight}
        containerHeight={containerHeight}
        renderItem={renderItem}
        onLoadMore={fetchMore}
        hasMore={hasNextPage}
        loading={isFetchingNextPage}
        error={error}
        onRetry={refetch}
      />
    );
  };
};

// Hook for virtual scroll pagination
export const useVirtualScrollPagination = ({
  fetchFunction,
  pageSize = 50,
  initialData = [],
}) => {
  const [data, setData] = useState(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasMore, setHasMore] = useState(true);
  const [offset, setOffset] = useState(0);

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetchFunction({
        limit: pageSize,
        offset: offset,
      });

      if (response.success) {
        const newItems = response.data.results || response.data;
        setData(prev => [...prev, ...newItems]);
        setOffset(prev => prev + pageSize);
        setHasMore(response.data.has_more || newItems.length === pageSize);
      } else {
        throw new Error(response.error || 'Failed to load data');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [fetchFunction, pageSize, offset, loading, hasMore]);

  const reset = useCallback(() => {
    setData(initialData);
    setOffset(0);
    setHasMore(true);
    setError(null);
    setLoading(false);
  }, [initialData]);

  const retry = useCallback(() => {
    setError(null);
    loadMore();
  }, [loadMore]);

  // Initial load
  useEffect(() => {
    if (data.length === 0 && !loading) {
      loadMore();
    }
  }, []);

  return {
    data,
    loading,
    error,
    hasMore,
    loadMore,
    reset,
    retry,
  };
};

export default VirtualScrollPagination;
