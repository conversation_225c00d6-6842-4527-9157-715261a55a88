// Test script to verify delivery calculator exports
import { 
  calculateDeliveryFee, 
  calculateDynamicDeliveryFee, 
  calculateStaticDeliveryFee,
  calculateDistance,
  DELIVERY_CONFIG 
} from './src/utils/deliveryCalculator.js';

console.log('✅ All exports are working correctly!');
console.log('Available functions:');
console.log('- calculateDeliveryFee (main function)');
console.log('- calculateDynamicDeliveryFee');
console.log('- calculateStaticDeliveryFee');
console.log('- calculateDistance');
console.log('- DELIVERY_CONFIG');

// Test static calculation
const testResult = calculateStaticDeliveryFee(
  { lat: 34.5553, lng: 69.2075 },
  { lat: 34.5560, lng: 69.2080 },
  25.00
);

console.log('Test static calculation result:', testResult);
