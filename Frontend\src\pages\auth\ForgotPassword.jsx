import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Link } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import Input from "../../components/common/Input";
import Button from "../../components/common/Button";
import { Mail, Lock, ArrowLeft, AlertCircle, CheckCircle } from "lucide-react";
import useFormPersistence from "../../hooks/useFormPersistence";

const ForgotPassword = () => {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm();
  const { requestPasswordReset, resetPassword } = useAuth();
  const [step, setStep] = useState("email"); // 'email', 'reset'
  const [email, setEmail] = useState("");
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [loading, setLoading] = useState(false);

  // Form persistence
  const watchedValues = watch();
  const storageKey =
    step === "email"
      ? "forgot_password_email_form"
      : "forgot_password_reset_form";
  const { clearFormData } = useFormPersistence(
    watchedValues,
    setValue,
    storageKey,
    ["newPassword", "confirmPassword"],
    [step]
  );

  // Step 1: Request password reset
  const onRequestReset = async (data) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await requestPasswordReset(data.email);
      if (result.success) {
        // Clear email form data when moving to reset step
        clearFormData();
        setEmail(data.email);
        setStep("reset");
        setSuccess(result.message);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Step 2: Reset password with OTP
  const onResetPassword = async (data) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await resetPassword(
        email,
        data.oldPassword,
        data.newPassword,
        data.otp
      );

      if (result.success) {
        // Clear reset form data on successful password reset
        clearFormData();
        setSuccess(
          "Password reset successfully! You can now login with your new password."
        );
        setTimeout(() => {
          window.location.href = "/login";
        }, 2000);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (step === "email") {
    return (
      <div className='animate-fade-in'>
        <div className='text-center mb-8'>
          <h1 className='text-2xl font-poppins font-bold text-text-primary mb-2'>
            Forgot Password
          </h1>
          <p className='text-text-secondary'>
            Enter your email address and we'll send you a reset code
          </p>
        </div>

        {error && (
          <div className='flex items-start p-4 mb-6 bg-red-50 border border-red-200 rounded-lg'>
            <AlertCircle
              size={18}
              className='text-accent-red mr-2 mt-0.5 flex-shrink-0'
            />
            <p className='text-accent-red text-sm'>{error}</p>
          </div>
        )}

        {success && (
          <div className='flex items-start p-4 mb-6 bg-green-50 border border-green-200 rounded-lg'>
            <CheckCircle
              size={18}
              className='text-green-600 mr-2 mt-0.5 flex-shrink-0'
            />
            <p className='text-green-600 text-sm'>{success}</p>
          </div>
        )}

        <form onSubmit={handleSubmit(onRequestReset)} className='space-y-6'>
          <div>
            <Input
              label='Email Address'
              type='email'
              placeholder='Enter your email'
              icon={<Mail size={18} />}
              error={errors.email?.message}
              required
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: "Please enter a valid email address",
                },
              })}
            />
          </div>

          <Button type='submit' variant='primary' fullWidth loading={loading}>
            Send Reset Code
          </Button>
        </form>

        <div className='mt-6 text-center'>
          <Link
            to='/login'
            className='inline-flex items-center text-primary-500 hover:text-primary-600 transition-colors'
          >
            <ArrowLeft size={16} className='mr-1' />
            Back to Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='animate-fade-in'>
      <div className='text-center mb-8'>
        <h1 className='text-2xl font-poppins font-bold text-text-primary mb-2'>
          Reset Password
        </h1>
        <p className='text-text-secondary'>
          Enter the code sent to {email} and your new password
        </p>
      </div>

      {error && (
        <div className='flex items-start p-4 mb-6 bg-red-50 border border-red-200 rounded-lg'>
          <AlertCircle
            size={18}
            className='text-accent-red mr-2 mt-0.5 flex-shrink-0'
          />
          <p className='text-accent-red text-sm'>{error}</p>
        </div>
      )}

      {success && (
        <div className='flex items-start p-4 mb-6 bg-green-50 border border-green-200 rounded-lg'>
          <CheckCircle
            size={18}
            className='text-green-600 mr-2 mt-0.5 flex-shrink-0'
          />
          <p className='text-green-600 text-sm'>{success}</p>
        </div>
      )}

      <form onSubmit={handleSubmit(onResetPassword)} className='space-y-6'>
        <div>
          <Input
            label='Verification Code'
            type='text'
            placeholder='Enter 6-digit code'
            maxLength={6}
            error={errors.otp?.message}
            required
            {...register("otp", {
              required: "Verification code is required",
              minLength: {
                value: 6,
                message: "Code must be 6 digits",
              },
            })}
          />
        </div>

        <div>
          <Input
            label='Current Password'
            type='password'
            placeholder='Enter your current password'
            icon={<Lock size={18} />}
            error={errors.oldPassword?.message}
            required
            {...register("oldPassword", {
              required: "Current password is required",
            })}
          />
        </div>

        <div>
          <Input
            label='New Password'
            type='password'
            placeholder='Enter new password'
            icon={<Lock size={18} />}
            error={errors.newPassword?.message}
            required
            {...register("newPassword", {
              required: "New password is required",
              minLength: {
                value: 6,
                message: "Password must be at least 6 characters",
              },
            })}
          />
        </div>

        <Button type='submit' variant='primary' fullWidth loading={loading}>
          Reset Password
        </Button>
      </form>

      <div className='mt-6 text-center'>
        <button
          onClick={() => setStep("email")}
          className='inline-flex items-center text-primary-500 hover:text-primary-600 transition-colors'
        >
          <ArrowLeft size={16} className='mr-1' />
          Back to Email
        </button>
      </div>
    </div>
  );
};

export default ForgotPassword;
