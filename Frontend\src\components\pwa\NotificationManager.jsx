import React, { useState, useEffect } from 'react';
import { <PERSON>, BellOff, X } from 'lucide-react';
import { usePWA } from '../../hooks/usePWA';
import Button from '../common/Button';

const NotificationManager = () => {
  const { requestNotificationPermission, notificationPermission, showNotification } = usePWA();
  const [showPrompt, setShowPrompt] = useState(false);
  const [isDismissed, setIsDismissed] = useState(
    localStorage.getItem('notificationPromptDismissed') === 'true'
  );

  useEffect(() => {
    // Show notification prompt after 30 seconds if not granted and not dismissed
    const timer = setTimeout(() => {
      if (notificationPermission === 'default' && !isDismissed) {
        setShowPrompt(true);
      }
    }, 30000);

    return () => clearTimeout(timer);
  }, [notificationPermission, isDismissed]);

  const handleEnableNotifications = async () => {
    const granted = await requestNotificationPermission();
    if (granted) {
      showNotification('Notifications enabled!', {
        body: 'You\'ll now receive updates about your orders.',
        icon: '/icons/icon-192x192.png'
      });
    }
    setShowPrompt(false);
    setIsDismissed(true);
    localStorage.setItem('notificationPromptDismissed', 'true');
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    setIsDismissed(true);
    localStorage.setItem('notificationPromptDismissed', 'true');
  };

  // Test notification function (for development)
  const sendTestNotification = () => {
    if (notificationPermission === 'granted') {
      showNotification('Test Notification', {
        body: 'This is a test notification from Afghan Sofra!',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        tag: 'test-notification',
        requireInteraction: false
      });
    }
  };

  if (!showPrompt) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-sm animate-slide-in-right">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center mr-3">
            <Bell size={20} className="text-primary-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Stay Updated</h3>
          </div>
        </div>
        <button
          onClick={handleDismiss}
          className="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X size={16} />
        </button>
      </div>
      
      <p className="text-sm text-gray-600 mb-4">
        Get notified about your order status, special offers, and delivery updates.
      </p>
      
      <div className="flex space-x-2">
        <Button
          variant="primary"
          size="small"
          onClick={handleEnableNotifications}
          className="flex-1"
        >
          Enable
        </Button>
        <Button
          variant="outline"
          size="small"
          onClick={handleDismiss}
          className="flex-1"
        >
          Not now
        </Button>
      </div>
      
      {/* Development only - remove in production */}
      {process.env.NODE_ENV === 'development' && notificationPermission === 'granted' && (
        <Button
          variant="secondary"
          size="small"
          onClick={sendTestNotification}
          className="w-full mt-2"
        >
          Test Notification
        </Button>
      )}
    </div>
  );
};

export default NotificationManager;
