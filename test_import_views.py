#!/usr/bin/env python3
"""
Test if the manual assignment views can be imported
"""

import os
import sys
import django

# Setup Django
backend_path = os.path.join(os.path.dirname(__file__), 'Backend')
sys.path.insert(0, backend_path)
os.chdir(backend_path)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

def test_imports():
    print("🔍 Testing Manual Assignment Views Import")
    print("=" * 50)
    
    try:
        from deliveryAgent.manual_assignment_views import AdminOrderAssignmentView
        print("✅ AdminOrderAssignmentView imported successfully")
        
        # Test if the view can be instantiated
        view = AdminOrderAssignmentView()
        print("✅ AdminOrderAssignmentView can be instantiated")
        
        # Check if the view has the required methods
        if hasattr(view, 'get'):
            print("✅ AdminOrderAssignmentView has 'get' method")
        else:
            print("❌ AdminOrderAssignmentView missing 'get' method")
            
        if hasattr(view, 'post'):
            print("✅ AdminOrderAssignmentView has 'post' method")
        else:
            print("❌ AdminOrderAssignmentView missing 'post' method")
            
    except ImportError as e:
        print(f"❌ Failed to import AdminOrderAssignmentView: {e}")
        return False
    except Exception as e:
        print(f"❌ Error with AdminOrderAssignmentView: {e}")
        return False
    
    # Test URL resolution
    try:
        from django.urls import reverse
        from django.test import RequestFactory
        
        print("\n🔍 Testing URL Resolution")
        print("=" * 30)
        
        # This might fail if the URL name doesn't exist
        try:
            url = reverse('delivery_agent:admin_order_assignments')
            print(f"✅ URL resolved: {url}")
        except Exception as e:
            print(f"❌ URL resolution failed: {e}")
        
        # Test direct URL pattern matching
        from django.urls import resolve
        try:
            match = resolve('/delivery-agent/admin/assignments/')
            print(f"✅ URL pattern matches: {match.view_name}")
        except Exception as e:
            print(f"❌ URL pattern matching failed: {e}")
            
    except Exception as e:
        print(f"❌ URL testing failed: {e}")
    
    return True

if __name__ == "__main__":
    test_imports()
