#!/usr/bin/env python3
import os
import django
import requests

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from restaurant.models import MenuItem

def test_api():
    # Get a menu item from database
    items = MenuItem.objects.all()[:1]
    if not items:
        print("❌ No menu items found in database")
        return
    
    item = items[0]
    print(f"🔍 Testing menu item {item.id}: {item.name}")
    print(f"   Restaurant: {item.category.restaurant.name} (ID: {item.category.restaurant.id})")
    
    # Test API endpoint
    try:
        response = requests.get(f'http://127.0.0.1:8000/api/restaurant/menu-items/{item.id}/')
        print(f"API Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API Response:")
            print(f"  - ID: {data.get('id')}")
            print(f"  - Name: {data.get('name')}")
            print(f"  - Restaurant ID: {data.get('restaurant_id')}")
            print(f"  - Restaurant Name: {data.get('restaurant_name')}")
            
            if data.get('restaurant_id') and data.get('restaurant_name'):
                print("✅ Restaurant information is present!")
            else:
                print("❌ Restaurant information is missing!")
                print("Full response keys:", list(data.keys()))
        else:
            print(f"❌ API Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_api()
