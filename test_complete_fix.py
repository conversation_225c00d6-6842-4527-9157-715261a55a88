import requests
import json

def test_complete_fix():
    """Test the complete fix for the admin assignment API"""
    
    print("🔍 Testing Complete Admin Assignment Fix")
    print("=" * 50)
    
    # Login as admin first
    login_url = "http://127.0.0.1:8000/api/auth/login/"
    login_data = {
        "user_name": "admin_user_3602",
        "password": "admin123"
    }
    
    print("1. Logging in as admin...")
    login_response = requests.post(login_url, json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Admin login failed: {login_response.status_code}")
        return
    
    login_result = login_response.json()
    if not login_result.get('success'):
        print(f"❌ Admin login failed: {login_result}")
        return
    
    token = login_result['data']['access_token']
    print("✅ Admin login successful")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Simulate the fixed API call
    print("\n2. Simulating the fixed getOrderAssignmentData() call...")
    
    try:
        # Call both APIs like the fixed function does
        orders_url = "http://127.0.0.1:8000/api/order/orders/?status=ready"
        agents_url = "http://127.0.0.1:8000/api/delivery-agent/admin/employees/"
        
        orders_response = requests.get(orders_url, headers=headers)
        agents_response = requests.get(agents_url, headers=headers)
        
        print(f"   Orders API: {orders_response.status_code}")
        print(f"   Agents API: {agents_response.status_code}")
        
        if orders_response.status_code == 200 and agents_response.status_code == 200:
            orders_data = orders_response.json()
            agents_data = agents_response.json()
            
            # Transform data like the fixed function does
            ready_orders = []
            for order in orders_data:
                ready_orders.append({
                    'id': order.get('id'),
                    'customer_name': order.get('customer', {}).get('name', 'Unknown'),
                    'customer_phone': order.get('customer', {}).get('phone', ''),
                    'restaurant_name': order.get('restaurant', {}).get('name', 'Unknown'),
                    'restaurant_address': order.get('restaurant', {}).get('address', ''),
                    'delivery_address': order.get('delivery_address', ''),
                    'total_amount': float(order.get('total_amount', 0)),
                    'payment_method': order.get('payment_method', ''),
                    'special_instructions': order.get('special_instructions', ''),
                    'created_at': order.get('created_at'),
                    'estimated_prep_time': 30,
                    'priority': 'normal'
                })
            
            # Extract agents from the correct path
            available_agents = []
            if (agents_data and agents_data.get('data') and 
                agents_data['data'].get('employees')):
                available_agents = agents_data['data']['employees']
            
            print(f"\n✅ Data transformation successful!")
            print(f"   Ready orders: {len(ready_orders)}")
            print(f"   Available agents: {len(available_agents)}")
            
            # Check for Order #65
            order_65_found = False
            for order in ready_orders:
                if order['id'] == 65:
                    order_65_found = True
                    print(f"\n✅ Order #65 FOUND!")
                    print(f"   Customer: {order['customer_name']}")
                    print(f"   Restaurant: {order['restaurant_name']}")
                    print(f"   Amount: ${order['total_amount']}")
                    break
            
            if not order_65_found:
                print(f"\n❌ Order #65 NOT found")
                if ready_orders:
                    print(f"Sample orders:")
                    for order in ready_orders[:3]:
                        print(f"   Order #{order['id']}: {order['customer_name']} - ${order['total_amount']}")
            
            # Check agents structure
            if available_agents:
                print(f"\n✅ Agents data structure is correct!")
                print(f"   Sample agent: {available_agents[0].get('full_name', 'Unknown')} ({available_agents[0].get('agent_id', 'No ID')})")
            else:
                print(f"\n❌ No agents found")
            
            print(f"\n🎯 Frontend should now work correctly!")
            print(f"   - availableAgents.map() will work because it's an array")
            print(f"   - Order #65 will appear in the admin assignment page")
            print(f"   - Manual assignment functionality should work")
            
        else:
            print(f"❌ API calls failed")
            
    except Exception as e:
        print(f"❌ Error during simulation: {e}")

if __name__ == "__main__":
    test_complete_fix()
