from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from decimal import Decimal
from .models import DeliveryAgentProfile, DeliveryZone, AgentShift
from orders.models import Order
from restaurant.models import Restaurant, Address

User = get_user_model()


class DeliveryAgentModelTest(TestCase):
    """Test delivery agent models"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            user_name='testagent',
            password='testpass123',
            role='delivery_agent',
            name='Test Agent',
            phone='+**********'
        )

    def test_delivery_agent_profile_creation(self):
        """Test delivery agent profile creation"""
        profile = DeliveryAgentProfile.objects.create(
            user=self.user,
            phone_number='+**********',
            vehicle_type='motorcycle',
            license_plate='TEST123',
            address='Test Address'
        )

        self.assertEqual(profile.user, self.user)
        self.assertTrue(profile.agent_id.startswith('DA'))
        self.assertEqual(profile.status, 'pending')
        self.assertEqual(profile.availability, 'offline')
        self.assertFalse(profile.is_verified)

    def test_completion_rate_calculation(self):
        """Test completion rate calculation"""
        profile = DeliveryAgentProfile.objects.create(
            user=self.user,
            phone_number='+**********',
            vehicle_type='motorcycle',
            license_plate='TEST123',
            address='Test Address',
            total_deliveries=10,
            successful_deliveries=8
        )

        self.assertEqual(profile.completion_rate, 80.0)


class DeliveryAgentAPITest(APITestCase):
    """Test delivery agent API endpoints"""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            user_name='testagent',
            password='testpass123',
            role='delivery_agent',
            name='Test Agent',
            phone='+**********'
        )
        self.profile = DeliveryAgentProfile.objects.create(
            user=self.user,
            phone_number='+**********',
            vehicle_type='motorcycle',
            license_plate='TEST123',
            address='Test Address',
            status='active',
            is_verified=True
        )

        # Create test restaurant and order
        self.restaurant_user = User.objects.create_user(
            email='<EMAIL>',
            user_name='testrestaurant',
            password='testpass123',
            role='restaurant_owner',
            name='Test Restaurant',
            phone='+**********'
        )
        self.address = Address.objects.create(
            user=self.restaurant_user,
            street='123 Test St',
            city='Test City',
            state='Test State',
            postal_code='12345',
            country='Test Country',
            latitude=34.5553,
            longitude=69.2075
        )
        self.restaurant = Restaurant.objects.create(
            owner=self.restaurant_user,
            name='Test Restaurant',
            description='Test restaurant description',
            address=self.address,
            contact_number='+**********',
            delivery_fee=5.00,
            opening_time='09:00',
            closing_time='22:00'
        )

        self.client.force_authenticate(user=self.user)

    def test_dashboard_api(self):
        """Test dashboard API endpoint"""
        url = reverse('delivery_agent:dashboard')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('agent_info', response.data)
        self.assertIn('today_stats', response.data)

    def test_toggle_online_status(self):
        """Test toggle online status API"""
        url = reverse('delivery_agent:toggle-online-status')
        response = self.client.post(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.profile.refresh_from_db()
        self.assertTrue(self.profile.is_online)

    def test_update_location(self):
        """Test location update API"""
        url = reverse('delivery_agent:update-location')
        data = {
            'latitude': 34.5553,
            'longitude': 69.2075,
            'address': 'Test Location'
        }
        response = self.client.post(url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.profile.refresh_from_db()
        self.assertEqual(float(self.profile.current_latitude), 34.5553)
        self.assertEqual(float(self.profile.current_longitude), 69.2075)


class DeliveryAssignmentTest(TestCase):
    """Test delivery assignment functionality"""

    def setUp(self):
        # Create delivery agent
        self.agent_user = User.objects.create_user(
            email='<EMAIL>',
            user_name='testagent2',
            password='testpass123',
            role='delivery_agent',
            name='Test Agent 2',
            phone='+**********'
        )
        self.agent_profile = DeliveryAgentProfile.objects.create(
            user=self.agent_user,
            phone_number='+**********',
            vehicle_type='motorcycle',
            license_plate='TEST123',
            address='Test Address',
            status='active',
            availability='available',
            is_verified=True
        )

        # Create customer
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            user_name='testcustomer',
            password='testpass123',
            role='customer',
            name='Test Customer',
            phone='+**********'
        )

        # Create restaurant
        self.restaurant_user = User.objects.create_user(
            email='<EMAIL>',
            user_name='testrestaurant2',
            password='testpass123',
            role='restaurant_owner',
            name='Test Restaurant 2',
            phone='+**********'
        )
        self.address = Address.objects.create(
            user=self.restaurant_user,
            street='123 Test St',
            city='Test City',
            state='Test State',
            postal_code='12345',
            country='Test Country',
            latitude=34.5553,
            longitude=69.2075
        )
        self.restaurant = Restaurant.objects.create(
            owner=self.restaurant_user,
            name='Test Restaurant',
            description='Test restaurant description',
            address=self.address,
            contact_number='+**********',
            delivery_fee=5.00,
            opening_time='09:00',
            closing_time='22:00'
        )

        self.delivery_address = Address.objects.create(
            user=self.customer,
            street='456 Customer St',
            city='Test City',
            state='Test State',
            postal_code='12345',
            country='Test Country',
            latitude=34.5560,
            longitude=69.2080
        )

    def test_order_assignment(self):
        """Test order assignment to delivery agent"""
        order = Order.objects.create(
            customer=self.customer,
            restaurant=self.restaurant,
            delivery_address=self.delivery_address,
            status='ready',
            total_amount=Decimal('25.00'),
            delivery_fee=Decimal('5.00'),
            tax_amount=Decimal('2.00'),
            payment_method='credit_card'
        )

        # Assign order to agent
        order.assign_agent(self.agent_user)

        self.assertEqual(order.delivery_agent, self.agent_user)
        self.assertEqual(order.status, 'assigned')
        self.assertEqual(order.assignment_attempts, 1)
