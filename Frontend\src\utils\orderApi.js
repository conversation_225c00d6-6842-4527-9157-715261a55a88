import axios from "axios";

// Get API base URL from environment variables
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000/api";

// Create axios instance for order API
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
    if (user.access_token) {
      config.headers.Authorization = `Bearer ${user.access_token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle authentication errors
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Clear invalid token and redirect to login
      localStorage.removeItem("afghanSofraUser");
      if (window.location.pathname !== "/login") {
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

/**
 * Order API Functions
 */
export const orderApi = {
  /**
   * Create a new order
   * @param {Object} orderData - Order data
   * @param {number} orderData.delivery_address - Address ID
   * @param {number} orderData.restaurant - Restaurant ID
   * @param {string} orderData.payment_method - Payment method
   * @param {Array} orderData.items - Array of order items
   * @param {string} orderData.special_instructions - Special instructions (optional)
   * @returns {Promise} API response
   */
  async createOrder(orderData) {
    try {
      // Ensure delivery_address and restaurant are integers
      const sanitizedOrderData = {
        ...orderData,
        delivery_address: parseInt(orderData.delivery_address, 10),
        restaurant: parseInt(orderData.restaurant, 10),
      };

      const response = await apiClient.post(
        "/order/orders/",
        sanitizedOrderData
      );

      return {
        success: true,
        data: response.data,
        message: "Order created successfully",
      };
    } catch (error) {
      console.error("Create order error:", error);

      // Extract detailed error message
      let errorMessage = "Failed to create order";

      if (error.response?.data) {
        const data = error.response.data;

        // Check for validation errors in details
        if (data.details && typeof data.details === "object") {
          const validationErrors = [];
          Object.entries(data.details).forEach(([field, errors]) => {
            if (Array.isArray(errors)) {
              validationErrors.push(`${field}: ${errors.join(", ")}`);
            } else {
              validationErrors.push(`${field}: ${errors}`);
            }
          });
          if (validationErrors.length > 0) {
            errorMessage = `Validation failed: ${validationErrors.join("; ")}`;
          }
        }

        // Fallback to other error messages
        if (errorMessage === "Failed to create order") {
          errorMessage =
            data.message ||
            data.error ||
            error.message ||
            "Failed to create order";
        }
      }

      return {
        success: false,
        error: errorMessage,
        details: error.response?.data,
      };
    }
  },

  /**
   * Get all orders
   * @param {Object} params - Optional query parameters
   * @param {string} params.status - Filter by order status
   * @param {string} params.customer - Filter by customer
   * @param {string} params.restaurant - Filter by restaurant
   * @returns {Promise} API response
   */
  async getOrders(params = {}) {
    try {
      console.log("OrderAPI: Making request to /order/orders/");

      // Build query string from params
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          queryParams.append(key, value);
        }
      });

      const url = queryParams.toString()
        ? `/order/orders/?${queryParams.toString()}`
        : "/order/orders/";

      const response = await apiClient.get(url);
      console.log(
        "OrderAPI: Response received:",
        response.status,
        response.data?.length || 0,
        "orders"
      );

      return {
        success: true,
        data: response.data,
        message: "Orders retrieved successfully",
      };
    } catch (error) {
      console.error("OrderAPI: Get orders error:", error);
      console.error("OrderAPI: Error response:", error.response?.data);
      console.error("OrderAPI: Error status:", error.response?.status);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve orders",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get a single order by ID
   * @param {number} orderId - Order ID
   * @returns {Promise} API response
   */
  async getOrder(orderId) {
    try {
      const response = await apiClient.get(`/order/orders/${orderId}/`);

      return {
        success: true,
        data: response.data,
        message: "Order retrieved successfully",
      };
    } catch (error) {
      console.error("Get order error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve order",
        details: error.response?.data,
      };
    }
  },

  /**
   * Alias for getOrder - Get a single order by ID
   * @param {number} orderId - Order ID
   * @returns {Promise} API response
   */
  async getOrderById(orderId) {
    return this.getOrder(orderId);
  },

  /**
   * Get orders filtered by status
   * @param {string} status - Order status
   * @returns {Promise} API response
   */
  async getOrdersByStatus(status) {
    try {
      const response = await apiClient.get(`/order/orders/?status=${status}`);

      return {
        success: true,
        data: response.data,
        message: "Orders retrieved successfully",
      };
    } catch (error) {
      console.error("Get orders by status error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve orders",
        details: error.response?.data,
      };
    }
  },

  /**
   * Update an order (full update)
   * @param {number} orderId - Order ID
   * @param {Object} orderData - Complete order data
   * @returns {Promise} API response
   */
  async updateOrder(orderId, orderData) {
    try {
      const response = await apiClient.put(
        `/order/orders/${orderId}/`,
        orderData
      );

      return {
        success: true,
        data: response.data,
        message: "Order updated successfully",
      };
    } catch (error) {
      console.error("Update order error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to update order",
        details: error.response?.data,
      };
    }
  },

  /**
   * Partially update an order
   * @param {number} orderId - Order ID
   * @param {Object} updateData - Partial order data
   * @returns {Promise} API response
   */
  async patchOrder(orderId, updateData) {
    try {
      const response = await apiClient.patch(
        `/order/orders/${orderId}/`,
        updateData
      );

      return {
        success: true,
        data: response.data,
        message: "Order updated successfully",
      };
    } catch (error) {
      console.error("Patch order error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to update order",
        details: error.response?.data,
      };
    }
  },

  /**
   * Delete an order
   * @param {number} orderId - Order ID
   * @returns {Promise} API response
   */
  async deleteOrder(orderId) {
    try {
      await apiClient.delete(`/order/orders/${orderId}/`);

      return {
        success: true,
        message: "Order deleted successfully",
      };
    } catch (error) {
      console.error("Delete order error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to delete order",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get order status history
   * @param {number} orderId - Order ID
   * @returns {Promise} API response
   */
  async getOrderStatusHistory(orderId) {
    try {
      const response = await apiClient.get(
        `/order/orders/${orderId}/status-history/`
      );

      return {
        success: true,
        data: response.data,
        message: "Order status history retrieved successfully",
      };
    } catch (error) {
      console.error("Get order status history error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve order status history",
        details: error.response?.data,
      };
    }
  },
};

/**
 * Cart API Functions
 */
export const cartApi = {
  /**
   * Save/Update cart
   * @param {Object} cartData - Cart data
   * @param {number} cartData.restaurant_id - Restaurant ID
   * @param {Array} cartData.items - Array of cart items
   * @returns {Promise} API response
   */
  async saveCart(cartData) {
    try {
      const response = await apiClient.put("/order/carts/mine/", cartData);

      return {
        success: true,
        data: response.data,
        message: "Cart saved successfully",
      };
    } catch (error) {
      console.error("Save cart error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to save cart",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get current user's cart
   * @returns {Promise} API response
   */
  async getCart() {
    try {
      const response = await apiClient.get("/order/carts/mine/");

      return {
        success: true,
        data: response.data,
        message: "Cart retrieved successfully",
      };
    } catch (error) {
      console.error("Get cart error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve cart",
        details: error.response?.data,
      };
    }
  },

  /**
   * Delete current user's cart
   * @returns {Promise} API response
   */
  async deleteCart() {
    try {
      await apiClient.delete("/order/carts/destroy/");

      return {
        success: true,
        message: "Cart deleted successfully",
      };
    } catch (error) {
      console.error("Delete cart error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to delete cart",
        details: error.response?.data,
      };
    }
  },
};

/**
 * Delivery Assignment API Functions
 */
export const deliveryApi = {
  /**
   * Assign order to delivery agent
   * @param {Object} assignmentData - Assignment data
   * @param {number} assignmentData.order_id - Order ID
   * @param {number} assignmentData.agent_id - Agent ID
   * @returns {Promise} API response
   */
  async assignOrder(assignmentData) {
    try {
      const response = await apiClient.post(
        "/order/delivery-assignment/assign/",
        assignmentData
      );

      return {
        success: true,
        data: response.data,
        message: "Order assigned successfully",
      };
    } catch (error) {
      console.error("Assign order error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to assign order",
        details: error.response?.data,
      };
    }
  },

  /**
   * Reject order by delivery agent
   * @param {Object} rejectionData - Rejection data
   * @param {number} rejectionData.order_id - Order ID
   * @param {string} rejectionData.reason - Rejection reason
   * @returns {Promise} API response
   */
  async rejectOrder(rejectionData) {
    try {
      const response = await apiClient.post(
        "/order/delivery-assignment/reject/",
        rejectionData
      );

      return {
        success: true,
        data: response.data,
        message: "Order rejected successfully",
      };
    } catch (error) {
      console.error("Reject order error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to reject order",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get available orders for delivery
   * @returns {Promise} API response
   */
  async getAvailableOrders() {
    try {
      const response = await apiClient.get(
        "/order/delivery-assignment/available_orders/"
      );

      return {
        success: true,
        data: response.data,
        message: "Available orders retrieved successfully",
      };
    } catch (error) {
      console.error("Get available orders error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to retrieve available orders",
        details: error.response?.data,
      };
    }
  },

  /**
   * Trigger auto-assignment for orders
   * @param {number} orderId - Optional specific order ID to assign
   * @returns {Promise} API response
   */
  async autoAssignOrder(orderId = null) {
    try {
      const data = orderId ? { order_id: orderId } : {};
      const response = await apiClient.post(
        "/order/delivery-assignment/auto_assign/",
        data
      );

      return {
        success: true,
        data: response.data,
        message: "Auto-assignment completed successfully",
      };
    } catch (error) {
      console.error("Auto assign error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to auto-assign orders",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get assignment statistics
   * @returns {Promise} API response
   */
  async getAssignmentStats() {
    try {
      const response = await apiClient.get(
        "/order/delivery-assignment/assignment_stats/"
      );

      return {
        success: true,
        data: response.data,
        message: "Assignment statistics retrieved successfully",
      };
    } catch (error) {
      console.error("Get assignment stats error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to get assignment statistics",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get available delivery agents for manual assignment
   * @param {number} orderId - Order ID
   * @returns {Promise} API response with available agents
   */
  async getAvailableAgents(orderId) {
    try {
      const response = await apiClient.get(
        `/order/orders/available_agents/?order_id=${orderId}`
      );
      return {
        success: true,
        data: response.data.data,
        message: "Available agents retrieved successfully",
      };
    } catch (error) {
      console.error("Error getting available agents:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to get available agents",
        details: error.response?.data,
      };
    }
  },

  /**
   * Manually assign delivery agent to order
   * @param {number} orderId - Order ID
   * @param {number} agentId - Agent ID
   * @returns {Promise} API response
   */
  async assignAgent(orderId, agentId) {
    try {
      const response = await apiClient.post("/order/orders/assign/", {
        order_id: orderId,
        agent_id: agentId,
      });
      return {
        success: true,
        data: response.data,
        message: "Agent assigned successfully",
      };
    } catch (error) {
      console.error("Error assigning agent:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to assign agent",
        details: error.response?.data,
      };
    }
  },

  /**
   * Auto-assign delivery agent to order
   * @param {number} orderId - Order ID (optional)
   * @returns {Promise} API response
   */
  async autoAssignOrder(orderId = null) {
    try {
      const url = orderId
        ? `/order/orders/auto_assign/?order_id=${orderId}`
        : "/order/orders/auto_assign/";

      const response = await apiClient.post(url);
      return {
        success: true,
        data: response.data,
        message: "Order auto-assigned successfully",
      };
    } catch (error) {
      console.error("Error auto-assigning order:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to auto-assign order",
        details: error.response?.data,
      };
    }
  },
};

export default orderApi;
