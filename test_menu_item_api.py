#!/usr/bin/env python3
"""
Test script to verify menu item API endpoint returns restaurant information
"""
import requests
import json

def test_menu_item_api():
    """Test the menu item API endpoint"""
    base_url = "http://127.0.0.1:8000/api/restaurant"
    
    # First, get all menu items to see what's available
    print("🔍 Getting all menu items...")
    try:
        response = requests.get(f"{base_url}/menu-items/")
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            items = response.json()
            print(f"Found {len(items)} menu items")
            
            if items:
                # Test the first item
                first_item = items[0]
                item_id = first_item['id']
                print(f"\n🔍 Testing menu item {item_id}: {first_item.get('name', 'Unknown')}")
                
                # Get detailed item info
                detail_response = requests.get(f"{base_url}/menu-items/{item_id}/")
                print(f"Detail Status: {detail_response.status_code}")
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    print("✅ Menu item details:")
                    print(f"  - ID: {detail_data.get('id')}")
                    print(f"  - Name: {detail_data.get('name')}")
                    print(f"  - Restaurant ID: {detail_data.get('restaurant_id')}")
                    print(f"  - Restaurant Name: {detail_data.get('restaurant_name')}")
                    print(f"  - Category: {detail_data.get('category')}")
                    
                    # Check if restaurant info is present
                    if detail_data.get('restaurant_id') and detail_data.get('restaurant_name'):
                        print("✅ Restaurant information is present in menu item response!")
                        return True
                    else:
                        print("❌ Restaurant information is missing from menu item response")
                        print("Full response:", json.dumps(detail_data, indent=2))
                        return False
                else:
                    print(f"❌ Failed to get menu item details: {detail_response.text}")
                    return False
            else:
                print("❌ No menu items found")
                return False
        else:
            print(f"❌ Failed to get menu items: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

if __name__ == "__main__":
    success = test_menu_item_api()
    if success:
        print("\n✅ API test passed!")
    else:
        print("\n❌ API test failed!")
