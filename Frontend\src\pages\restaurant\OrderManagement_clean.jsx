import React, { useState, useEffect, useRef } from "react";
import { useAuth } from "../../context/AuthContext";
import {
  Monitor,
  Grid3X3,
  List,
  Timer,
  Zap,
  TrendingUp,
  Target,
  Users,
  ChefHat,
  Truck,
  Star,
  Send,
  X,
  Plus,
  Minus,
  Play,
  Pause,
  RotateCcw,
  Bell,
  BellOff,
  Volume2,
  VolumeX,
  Clock,
  Package,
  CheckCircle,
  AlertCircle,
  MessageCircle,
  Phone,
  Eye,
  Settings,
  BarChart3,
  Activity,
  RefreshCw,
  Filter,
  Search,
  Calendar,
  Download,
  Printer,
  DollarSign,
} from "lucide-react";

function OrderManagement() {
  const { user } = useAuth();

  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [viewMode, setViewMode] = useState("queue"); // queue, kitchen, analytics
  const [displayMode, setDisplayMode] = useState("grid"); // grid, list
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showSettings, setShowSettings] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sortBy, setSortBy] = useState("date");
  const [sortOrder, setSortOrder] = useState("desc");

  const intervalRef = useRef(null);
  const audioRef = useRef(null);

  // Initialize audio for kitchen alerts
  useEffect(() => {
    audioRef.current = new Audio("/kitchen-alert.mp3");
    audioRef.current.preload = "auto";
  }, []);

  // Load orders from API
  useEffect(() => {
    const loadOrders = async () => {
      // Check authentication before making API call
      if (!user || !user.access_token || user.role !== "restaurant") {
        console.log("OrderManagement_clean: User not authenticated or not restaurant role");
        return;
      }

      try {
        console.log("Loading orders for restaurant user:", user.name);

        const response = await fetch(
          "http://127.0.0.1:8000/api/order/orders/",
          {
            headers: {
              Authorization: `Bearer ${user.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const ordersData = await response.json();
          console.log("Orders loaded:", ordersData.length);
          setOrders(ordersData);
        } else {
          console.error("Failed to load orders:", response.status);
          if (response.status === 401) {
            console.log("OrderManagement_clean: 401 Unauthorized - clearing user data");
            localStorage.removeItem("afghanSofraUser");
          }
        }
        } catch (error) {
          console.error("Error loading orders:", error);
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadOrders();
  }, [user]);

  // Filter and search functionality
  useEffect(() => {
    let filtered = [...orders];

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((order) => order.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (order) =>
          order.id.toString().toLowerCase().includes(query) ||
          (order.customer && order.customer.toString().includes(query)) ||
          (order.special_instructions &&
            order.special_instructions.toLowerCase().includes(query))
      );
    }

    // Sort orders
    filtered.sort((a, b) => {
      if (sortBy === "date") {
        const dateA = new Date(a.created_at);
        const dateB = new Date(b.created_at);
        return sortOrder === "asc" ? dateA - dateB : dateB - dateA;
      } else if (sortBy === "amount") {
        const amountA = parseFloat(a.total_amount);
        const amountB = parseFloat(b.total_amount);
        return sortOrder === "asc" ? amountA - amountB : amountB - amountA;
      }
      return 0;
    });

    setFilteredOrders(filtered);
  }, [orders, statusFilter, searchQuery, sortBy, sortOrder]);

  // Auto-refresh orders
  useEffect(() => {
    if (autoRefresh) {
      intervalRef.current = setInterval(() => {
        handleRefreshOrders();
      }, refreshInterval * 1000);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval]);

  // Refresh orders
  const handleRefreshOrders = async () => {
    setRefreshing(true);
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const response = await fetch(
          "http://127.0.0.1:8000/api/order/orders/",
          {
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const ordersData = await response.json();
          setOrders(ordersData);
        }
      }
    } catch (error) {
      console.error("Error refreshing orders:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Update order status
  const handleUpdateOrderStatus = async (orderId, newStatus) => {
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const response = await fetch(
          `http://127.0.0.1:8000/api/order/orders/${orderId}/`,
          {
            method: "PATCH",
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ status: newStatus }),
          }
        );

        if (response.ok) {
          // Refresh orders to get updated data
          handleRefreshOrders();
        }
      }
    } catch (error) {
      console.error("Error updating order status:", error);
    }
  };

  const playKitchenAlert = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch((e) => console.log("Audio play error:", e));
    }
  };

  const getStatusBadge = (status) => {
    const statusColors = {
      pending: "bg-yellow-100 text-yellow-800",
      confirmed: "bg-blue-100 text-blue-800",
      preparing: "bg-orange-100 text-orange-800",
      ready: "bg-green-100 text-green-800",
      delivered: "bg-gray-100 text-gray-800",
      cancelled: "bg-red-100 text-red-800",
    };

    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          statusColors[status] || "bg-gray-100 text-gray-800"
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (loading) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Restaurant Order Management</h1>
        <p>Loading orders...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Order Management System
              </h1>
              <p className="mt-1 text-sm text-gray-600">
                Professional restaurant order management and kitchen display
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleRefreshOrders}
                disabled={refreshing}
                className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                <RefreshCw
                  size={16}
                  className={refreshing ? "animate-spin" : ""}
                />
                <span>{refreshing ? "Refreshing..." : "Refresh"}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default OrderManagement;
