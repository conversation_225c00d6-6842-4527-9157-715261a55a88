import React, { useState } from "react";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import { useMenu } from "../../context/MenuContext";
import Button from "../../components/common/Button";
import Modal from "../../components/common/Modal";
import {
  Plus,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  X,
  Shield,
  Lock,
} from "lucide-react";

const MenuCategories = () => {
  const { user } = useAuth();
  const { currentRestaurant } = useRestaurant();
  const {
    categories,
    loading,
    error,
    createCategory,
    updateCategory,
    deleteCategory,
    clearError,
    restaurantId,
  } = useMenu();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [formData, setFormData] = useState({ name: "", description: "" });
  const [formLoading, setFormLoading] = useState(false);
  const [success, setSuccess] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});

  // Validate form data
  const validateForm = (data) => {
    const errors = {};

    if (!data.name || data.name.trim().length === 0) {
      errors.name = "Category name is required";
    } else if (data.name.trim().length < 2) {
      errors.name = "Category name must be at least 2 characters";
    } else if (data.name.trim().length > 50) {
      errors.name = "Category name must be less than 50 characters";
    }

    if (data.description && data.description.trim().length > 200) {
      errors.description = "Description must be less than 200 characters";
    }

    return errors;
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  // Handle create category
  const handleCreate = async (e) => {
    e.preventDefault();
    console.log("🚀 Starting category creation with data:", formData);

    // Validate form
    const errors = validateForm(formData);
    if (Object.keys(errors).length > 0) {
      console.log("❌ Validation errors:", errors);
      setValidationErrors(errors);
      return;
    }

    setFormLoading(true);
    setValidationErrors({});

    try {
      console.log("📤 Calling createCategory with:", formData);
      const result = await createCategory(formData);
      console.log("📥 Create category result:", result);

      if (result.success) {
        console.log("✅ Category created successfully!");
        setSuccess("Category created successfully!");
        setShowCreateModal(false);
        setFormData({ name: "", description: "" });

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      } else {
        console.log("❌ Category creation failed:", result.error);
        setValidationErrors({ general: result.error });
      }
    } catch (err) {
      console.error("❌ Create error:", err);
      setValidationErrors({ general: "An unexpected error occurred" });
    } finally {
      setFormLoading(false);
    }
  };

  // Handle edit category
  const handleEdit = async (e) => {
    e.preventDefault();

    // Validate form
    const errors = validateForm(formData);
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return;
    }

    setFormLoading(true);
    setValidationErrors({});

    try {
      const result = await updateCategory(selectedCategory.id, formData);

      if (result.success) {
        setSuccess("Category updated successfully!");
        setShowEditModal(false);
        setSelectedCategory(null);
        setFormData({ name: "", description: "" });

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      }
    } catch (err) {
      console.error("Update error:", err);
    } finally {
      setFormLoading(false);
    }
  };

  // Handle delete category
  const handleDelete = async () => {
    setFormLoading(true);

    try {
      const result = await deleteCategory(selectedCategory.id);

      if (result.success) {
        setSuccess("Category deleted successfully!");
        setShowDeleteModal(false);
        setSelectedCategory(null);

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000);
      }
    } catch (err) {
      console.error("Delete error:", err);
    } finally {
      setFormLoading(false);
    }
  };

  // Open edit modal
  const openEditModal = (category) => {
    setSelectedCategory(category);
    setFormData({
      name: category.name,
      description: category.description || "",
    });
    setShowEditModal(true);
  };

  // Open delete modal
  const openDeleteModal = (category) => {
    setSelectedCategory(category);
    setShowDeleteModal(true);
  };

  // Close modals and reset state
  const closeModals = () => {
    setShowCreateModal(false);
    setShowEditModal(false);
    setShowDeleteModal(false);
    setSelectedCategory(null);
    setFormData({ name: "", description: "" });
    setValidationErrors({});
    clearError();
  };

  return (
    <div className='p-6'>
      {/* Header */}
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Menu Categories</h1>
          <p className='text-gray-600'>
            Manage your restaurant's menu categories
          </p>
        </div>
        <Button
          onClick={() => setShowCreateModal(true)}
          variant='primary'
          className='flex items-center gap-2'
          disabled={
            user?.role === "restaurant" && !currentRestaurant?.is_verified
          }
          title={
            user?.role === "restaurant" && !currentRestaurant?.is_verified
              ? "Restaurant must be verified to add categories"
              : ""
          }
        >
          <Plus size={20} />
          Add Category
        </Button>
      </div>

      {/* Verification Status Check */}
      {user &&
        user.role === "restaurant" &&
        !currentRestaurant?.is_verified && (
          <div className='mb-6 p-4 bg-yellow-50 border-l-4 border-yellow-500 rounded-md'>
            <div className='flex items-start'>
              <Lock
                size={18}
                className='text-yellow-500 mr-2 mt-0.5 flex-shrink-0'
              />
              <div>
                <h3 className='text-sm font-medium text-yellow-800 mb-1'>
                  Restaurant Verification Required
                </h3>
                <p className='text-yellow-700 text-sm mb-2'>
                  Your restaurant must be verified by admin before you can
                  manage menu categories. Category management is currently
                  disabled.
                </p>
                <div className='flex items-center space-x-4 text-xs text-yellow-600'>
                  <div className='flex items-center space-x-1'>
                    <Shield size={12} />
                    <span>Pending Admin Approval</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

      {/* Success Message */}
      {success && (
        <div className='mb-6 p-4 bg-green-50 border-l-4 border-green-500 rounded-md'>
          <div className='flex items-center'>
            <CheckCircle size={20} className='text-green-500 mr-2' />
            <p className='text-green-700'>{success}</p>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className='mb-6 p-4 bg-red-50 border-l-4 border-red-500 rounded-md'>
          <div className='flex items-center'>
            <AlertCircle size={20} className='text-red-500 mr-2' />
            <p className='text-red-700'>{error}</p>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className='flex justify-center items-center py-12'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-primary'></div>
        </div>
      )}

      {/* Categories Grid */}
      {!loading && (
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
          {categories.length === 0 ? (
            <div className='col-span-full text-center py-12'>
              <p className='text-gray-500 text-lg'>No categories found</p>
              <p className='text-gray-400'>
                Create your first menu category to get started
              </p>
            </div>
          ) : (
            categories.map((category) => (
              <div
                key={category.id}
                className='bg-white rounded-lg shadow-md border border-gray-200 p-6'
              >
                <div className='flex justify-between items-start mb-4'>
                  <h3 className='text-lg font-semibold text-gray-900'>
                    {category.name}
                  </h3>
                  <div className='flex gap-2'>
                    <button
                      onClick={() => openEditModal(category)}
                      className='p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors'
                    >
                      <Edit size={16} />
                    </button>
                    <button
                      onClick={() => openDeleteModal(category)}
                      className='p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors'
                    >
                      <Trash2 size={16} />
                    </button>
                  </div>
                </div>

                {category.description && (
                  <p className='text-gray-600 text-sm mb-4'>
                    {category.description}
                  </p>
                )}

                <div className='text-xs text-gray-500'>ID: {category.id}</div>
              </div>
            ))
          )}
        </div>
      )}

      {/* Create Category Modal */}
      <Modal
        isOpen={showCreateModal}
        onClose={closeModals}
        title='Create New Category'
      >
        <form onSubmit={handleCreate} className='space-y-4'>
          {/* General error display */}
          {validationErrors.general && (
            <div className='bg-red-50 border border-red-200 rounded-md p-3'>
              <div className='flex items-center'>
                <AlertCircle className='text-red-500 mr-2' size={16} />
                <p className='text-red-700 text-sm'>
                  {validationErrors.general}
                </p>
              </div>
            </div>
          )}

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Category Name *
            </label>
            <input
              type='text'
              name='name'
              value={formData.name}
              onChange={handleInputChange}
              required
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                validationErrors.name ? "border-red-500" : "border-gray-300"
              }`}
              placeholder='e.g., Pizzas, Appetizers, Desserts'
            />
            {validationErrors.name && (
              <p className='text-red-500 text-sm mt-1'>
                {validationErrors.name}
              </p>
            )}
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Description
            </label>
            <textarea
              name='description'
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                validationErrors.description
                  ? "border-red-500"
                  : "border-gray-300"
              }`}
              placeholder='Brief description of this category'
            />
            {validationErrors.description && (
              <p className='text-red-500 text-sm mt-1'>
                {validationErrors.description}
              </p>
            )}
          </div>

          <div className='flex gap-3 pt-4'>
            <Button
              type='submit'
              variant='primary'
              loading={formLoading}
              className='flex-1'
            >
              Create Category
            </Button>
            <Button
              type='button'
              variant='outline'
              onClick={closeModals}
              className='flex-1'
            >
              Cancel
            </Button>
          </div>
        </form>
      </Modal>

      {/* Edit Category Modal */}
      <Modal isOpen={showEditModal} onClose={closeModals} title='Edit Category'>
        <form onSubmit={handleEdit} className='space-y-4'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Category Name *
            </label>
            <input
              type='text'
              name='name'
              value={formData.name}
              onChange={handleInputChange}
              required
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                validationErrors.name ? "border-red-500" : "border-gray-300"
              }`}
            />
            {validationErrors.name && (
              <p className='text-red-500 text-sm mt-1'>
                {validationErrors.name}
              </p>
            )}
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-2'>
              Description
            </label>
            <textarea
              name='description'
              value={formData.description}
              onChange={handleInputChange}
              rows={3}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                validationErrors.description
                  ? "border-red-500"
                  : "border-gray-300"
              }`}
            />
            {validationErrors.description && (
              <p className='text-red-500 text-sm mt-1'>
                {validationErrors.description}
              </p>
            )}
          </div>

          <div className='flex gap-3 pt-4'>
            <Button
              type='submit'
              variant='primary'
              loading={formLoading}
              className='flex-1'
            >
              Update Category
            </Button>
            <Button
              type='button'
              variant='outline'
              onClick={closeModals}
              className='flex-1'
            >
              Cancel
            </Button>
          </div>
        </form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={closeModals}
        title='Delete Category'
      >
        <div className='space-y-4'>
          <p className='text-gray-600'>
            Are you sure you want to delete the category "
            <span className='font-semibold'>{selectedCategory?.name}</span>"?
          </p>
          <p className='text-sm text-red-600'>
            This action cannot be undone and may affect menu items in this
            category.
          </p>

          <div className='flex gap-3 pt-4'>
            <Button
              onClick={handleDelete}
              variant='danger'
              loading={formLoading}
              className='flex-1'
            >
              Delete Category
            </Button>
            <Button onClick={closeModals} variant='outline' className='flex-1'>
              Cancel
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default MenuCategories;
