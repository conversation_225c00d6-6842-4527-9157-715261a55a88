#!/usr/bin/env python3
"""
Test multipart/form-data submission for restaurant creation
"""

import requests
import json

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"

def test_multipart_form():
    """Test restaurant creation with multipart/form-data"""
    
    # Login with our test user
    login_data = {
        "user_name": "testrestaurant",
        "password": "password123"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print("🔐 Logging in...")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=headers,
            data=json.dumps(login_data)
        )
        
        if response.status_code != 200:
            print("❌ Login failed")
            return False
        
        result = response.json()
        token = result['data']['access_token']
        print("✅ Login successful!")
        
        # Create multipart/form-data request
        auth_headers = {
            "Authorization": f"Bearer {token}"
        }
        
        # Create form data exactly like the frontend does
        form_data = {
            "name": "Multipart Test Restaurant",
            "description": "Testing multipart form data submission",
            "contact_number": "+93701234569",
            "opening_time": "09:00:00",
            "closing_time": "22:00:00",
            "delivery_fee": "60.00",
            "minimum_order": "200",
            "average_preparation_time": "30",
            "accepts_cash": "true",
            "accepts_card": "true",
            "accepts_online_payment": "true",
            "website": "https://multiparttest.com",
            "facebook_url": "https://facebook.com/multiparttest",
            "instagram_url": "https://instagram.com/multiparttest",
            "twitter_url": "https://twitter.com/multiparttest",
            # Address as JSON string
            "address": json.dumps({
                "street": "Multipart Street 123",
                "city": "Kabul",
                "state": "Kabul",
                "postal_code": "1003",
                "country": "Afghanistan",
                "latitude": 34.5553,
                "longitude": 69.2075
            })
        }
        
        print(f"\n🏗️ Creating restaurant with multipart/form-data...")
        
        # Use requests to send multipart/form-data
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            files={
                # Add a dummy file to force multipart/form-data
                'dummy': ('', '', 'application/octet-stream')
            },
            data=form_data
        )
        
        print(f"📡 Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 201:
            print("✅ Restaurant created successfully with multipart/form-data!")
            return True
        else:
            print("❌ Restaurant creation failed with multipart/form-data")
            try:
                error_data = response.json()
                print(f"🔍 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"🔍 Raw Error: {response.text}")
                
            # Try with a different approach - address as string
            print("\n🔄 Trying with address as simple string...")
            
            form_data["address"] = "Multipart Street 123, Kabul, Afghanistan"
            
            response = requests.post(
                f"{API_BASE_URL}/restaurant/restaurants/",
                headers=auth_headers,
                files={
                    'dummy': ('', '', 'application/octet-stream')
                },
                data=form_data
            )
            
            print(f"📡 Status: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
            if response.status_code == 201:
                print("✅ Restaurant created successfully with address as string!")
                return True
            else:
                print("❌ Still failed with address as string")
                return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Multipart Form Data")
    print("=" * 60)
    
    success = test_multipart_form()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: Multipart form data works!")
    else:
        print("❌ FAILED: Multipart form data has issues")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
