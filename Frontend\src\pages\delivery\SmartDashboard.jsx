import React, { useState, useEffect } from "react";
import {
  MapPin,
  Clock,
  DollarSign,
  Star,
  Navigation,
  Phone,
  MessageCircle,
  Camera,
  CheckCircle,
  AlertTriangle,
  Zap,
  Target,
  TrendingUp,
  Award,
  Battery,
  Wifi,
  Signal,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";

const SmartDashboard = () => {
  const [agentStatus, setAgentStatus] = useState("offline");
  const [currentShift, setCurrentShift] = useState(null);
  const [availableOrders, setAvailableOrders] = useState([]);
  const [activeOrder, setActiveOrder] = useState(null);
  const [earnings, setEarnings] = useState({
    today: 0,
    thisWeek: 0,
    projected: 0,
  });
  const [performance, setPerformance] = useState({
    rating: 4.8,
    completionRate: 95,
    onTimeDeliveries: 92,
    totalDeliveries: 156,
  });
  const [smartInsights, setSmartInsights] = useState({
    busyZones: ["Downtown", "University Area"],
    peakHours: ["12:00-14:00", "18:00-21:00"],
    recommendedBreak: "15:30",
    earningsOpportunity: "High demand in Downtown area",
  });

  // Real-time status updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate real-time updates
      loadDashboardData();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      const dashboardData = await deliveryAgentApi.getDashboard();
      if (dashboardData.success) {
        // Update state with real data
        setEarnings(dashboardData.data.earnings || earnings);
        setPerformance(dashboardData.data.performance || performance);
      }
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    }
  };

  const toggleOnlineStatus = async () => {
    try {
      const newStatus = agentStatus === "online" ? "offline" : "online";
      const result = await deliveryAgentApi.toggleOnlineStatus();

      if (result.success) {
        setAgentStatus(newStatus);
        if (newStatus === "online") {
          startShift();
        } else {
          endShift();
        }
      }
    } catch (error) {
      console.error("Error toggling status:", error);
    }
  };

  const startShift = () => {
    setCurrentShift({
      startTime: new Date(),
      orders: 0,
      earnings: 0,
    });
  };

  const endShift = () => {
    setCurrentShift(null);
    setActiveOrder(null);
  };

  const acceptOrder = async (orderId) => {
    try {
      const result = await deliveryAgentApi.acceptOrder(orderId);
      if (result.success) {
        const order = availableOrders.find((o) => o.id === orderId);
        setActiveOrder(order);
        setAvailableOrders((prev) => prev.filter((o) => o.id !== orderId));
      }
    } catch (error) {
      console.error("Error accepting order:", error);
    }
  };

  const StatusCard = () => (
    <Card className='p-8 bg-gradient-to-r from-primary-500 via-primary-600 to-orange-600 text-white shadow-2xl border-0 rounded-2xl'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-4'>
          <div
            className={`w-16 h-16 rounded-full flex items-center justify-center ${
              agentStatus === "online"
                ? "bg-green-400 shadow-lg"
                : "bg-gray-400"
            } transition-all duration-300`}
          >
            <div
              className={`w-8 h-8 rounded-full ${
                agentStatus === "online"
                  ? "bg-green-600 animate-pulse"
                  : "bg-gray-600"
              }`}
            />
          </div>
          <div>
            <h2 className='text-2xl font-bold mb-1'>
              {agentStatus === "online" ? "You're Online" : "You're Offline"}
            </h2>
            <p className='text-blue-100 text-lg'>
              {agentStatus === "online"
                ? "Ready to receive orders"
                : "Go online to start receiving orders"}
            </p>
          </div>
        </div>
        <div className='flex items-center space-x-4'>
          <div className='text-right'>
            <p className='text-sm text-blue-200'>Status</p>
            <div className='flex items-center space-x-2'>
              <div
                className={`w-3 h-3 rounded-full ${
                  agentStatus === "online"
                    ? "bg-green-400 animate-pulse"
                    : "bg-gray-400"
                }`}
              />
              <span className='text-lg font-semibold'>
                {agentStatus === "online" ? "Online" : "Offline"}
              </span>
            </div>
          </div>
          <Button
            onClick={toggleOnlineStatus}
            className={`px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl ${
              agentStatus === "online"
                ? "bg-white text-primary-600 hover:bg-gray-100"
                : "bg-gradient-to-r from-accent-green to-accent-green-dark text-white hover:from-accent-green-dark hover:to-accent-green"
            }`}
          >
            {agentStatus === "online" ? "Go Offline" : "Go Online"}
          </Button>
        </div>
      </div>
    </Card>
  );

  const EarningsCard = () => (
    <Card className='p-6 bg-gradient-to-br from-accent-green-light to-accent-green border-accent-green shadow-lg rounded-2xl'>
      <div className='flex items-center justify-between mb-6'>
        <h3 className='text-xl font-bold text-gray-900'>Today's Earnings</h3>
        <div className='p-3 bg-accent-green-light rounded-full'>
          <TrendingUp className='h-6 w-6 text-accent-green-dark' />
        </div>
      </div>
      <div className='space-y-6'>
        <div>
          <div className='flex items-center justify-between mb-2'>
            <span className='text-3xl font-bold bg-gradient-to-r from-accent-green to-accent-green-dark bg-clip-text text-transparent'>
              ${earnings.today.toFixed(2)}
            </span>
            <Badge
              variant='success'
              className='px-3 py-1 text-sm font-semibold'
            >
              +12%
            </Badge>
          </div>
          <p className='text-sm text-gray-600'>vs yesterday</p>
        </div>
        <div className='grid grid-cols-2 gap-4 pt-4 border-t border-accent-green'>
          <div className='text-center p-3 bg-white rounded-xl shadow-sm'>
            <p className='text-sm text-gray-500 mb-1'>This Week</p>
            <p className='text-xl font-bold text-gray-900'>
              ${earnings.thisWeek.toFixed(2)}
            </p>
          </div>
          <div className='text-center p-3 bg-white rounded-xl shadow-sm'>
            <p className='text-sm text-gray-500 mb-1'>Projected</p>
            <p className='text-xl font-bold text-gray-900'>
              ${earnings.projected.toFixed(2)}
            </p>
          </div>
        </div>
      </div>
    </Card>
  );

  const PerformanceCard = () => (
    <Card className='p-6'>
      <div className='flex items-center justify-between mb-4'>
        <h3 className='text-lg font-semibold text-gray-900'>Performance</h3>
        <Award className='h-5 w-5 text-yellow-500' />
      </div>
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <span className='text-sm text-gray-600'>Rating</span>
          <div className='flex items-center'>
            <Star className='h-4 w-4 text-yellow-400 fill-current' />
            <span className='ml-1 font-semibold'>{performance.rating}</span>
          </div>
        </div>
        <div className='flex items-center justify-between'>
          <span className='text-sm text-gray-600'>Completion Rate</span>
          <span className='font-semibold'>{performance.completionRate}%</span>
        </div>
        <div className='flex items-center justify-between'>
          <span className='text-sm text-gray-600'>On-time Deliveries</span>
          <span className='font-semibold'>{performance.onTimeDeliveries}%</span>
        </div>
        <div className='flex items-center justify-between'>
          <span className='text-sm text-gray-600'>Total Deliveries</span>
          <span className='font-semibold'>{performance.totalDeliveries}</span>
        </div>
      </div>
    </Card>
  );

  const SmartInsightsCard = () => (
    <Card className='p-6 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200'>
      <div className='flex items-center mb-4'>
        <Zap className='h-5 w-5 text-purple-600 mr-2' />
        <h3 className='text-lg font-semibold text-gray-900'>Smart Insights</h3>
      </div>
      <div className='space-y-3'>
        <div className='flex items-start'>
          <Target className='h-4 w-4 text-purple-600 mt-1 mr-2' />
          <div>
            <p className='text-sm font-medium text-gray-900'>
              High Demand Areas
            </p>
            <p className='text-xs text-gray-600'>
              {smartInsights.busyZones.join(", ")} - Consider heading there
            </p>
          </div>
        </div>
        <div className='flex items-start'>
          <Clock className='h-4 w-4 text-purple-600 mt-1 mr-2' />
          <div>
            <p className='text-sm font-medium text-gray-900'>Peak Hours</p>
            <p className='text-xs text-gray-600'>
              {smartInsights.peakHours.join(", ")} - Higher earnings potential
            </p>
          </div>
        </div>
        <div className='flex items-start'>
          <DollarSign className='h-4 w-4 text-purple-600 mt-1 mr-2' />
          <div>
            <p className='text-sm font-medium text-gray-900'>Opportunity</p>
            <p className='text-xs text-gray-600'>
              {smartInsights.earningsOpportunity}
            </p>
          </div>
        </div>
      </div>
    </Card>
  );

  const OrderCard = ({ order, onAccept }) => (
    <Card className='p-6 border-l-4 border-l-primary-500 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-200 hover:scale-105 bg-gradient-to-r from-white to-primary-50'>
      <div className='flex items-center justify-between mb-4'>
        <div className='flex items-center space-x-3'>
          <Badge variant='primary' className='px-3 py-1 text-sm font-bold'>
            #{order.id}
          </Badge>
          <div className='px-3 py-1 bg-green-100 text-green-800 rounded-full'>
            <span className='text-lg font-bold'>${order.earnings}</span>
          </div>
        </div>
        <div className='flex items-center text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full'>
          <Clock className='h-4 w-4 mr-1' />
          <span className='font-medium'>{order.estimatedTime} min</span>
        </div>
      </div>

      <div className='space-y-3 mb-6'>
        <div className='flex items-center text-sm bg-white p-3 rounded-lg shadow-sm'>
          <div className='p-2 bg-primary-100 rounded-full mr-3'>
            <MapPin className='h-4 w-4 text-primary-600' />
          </div>
          <div>
            <p className='text-xs text-gray-500 uppercase tracking-wide'>
              From
            </p>
            <p className='font-medium text-gray-900'>{order.restaurant}</p>
          </div>
        </div>
        <div className='flex items-center text-sm bg-white p-3 rounded-lg shadow-sm'>
          <div className='p-2 bg-orange-100 rounded-full mr-3'>
            <Navigation className='h-4 w-4 text-orange-600' />
          </div>
          <div>
            <p className='text-xs text-gray-500 uppercase tracking-wide'>To</p>
            <p className='font-medium text-gray-900'>{order.customerAddress}</p>
          </div>
        </div>
        <div className='flex items-center text-sm bg-white p-3 rounded-lg shadow-sm'>
          <div className='p-2 bg-yellow-100 rounded-full mr-3'>
            <Star className='h-4 w-4 text-yellow-600' />
          </div>
          <div>
            <p className='text-xs text-gray-500 uppercase tracking-wide'>
              Distance
            </p>
            <p className='font-medium text-gray-900'>{order.distance} km</p>
          </div>
        </div>
      </div>

      <div className='flex space-x-3'>
        <Button
          onClick={() => onAccept(order.id)}
          className='flex-1 bg-gradient-to-r from-accent-green to-accent-green-dark hover:from-accent-green-dark hover:to-accent-green text-white py-3 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105'
        >
          Accept Order
        </Button>
        <Button
          variant='outline'
          className='px-6 py-3 border-2 border-gray-300 hover:border-primary-400 hover:text-primary-600 rounded-xl transition-all duration-200'
        >
          Details
        </Button>
      </div>
    </Card>
  );

  return (
    <div className='min-h-screen bg-gradient-to-br from-primary-50 via-orange-50 to-primary-100 p-4'>
      <div className='max-w-7xl mx-auto space-y-8'>
        {/* Header */}
        <div className='bg-white rounded-2xl p-6 shadow-lg border border-gray-100'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center space-x-4'>
              <div className='p-3 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full'>
                <Zap className='h-8 w-8 text-white' />
              </div>
              <div>
                <h1 className='text-3xl font-bold bg-gradient-to-r from-primary-500 to-primary-600 bg-clip-text text-transparent'>
                  Smart Dashboard
                </h1>
                <p className='text-gray-600 text-lg'>
                  AI-powered delivery management
                </p>
              </div>
            </div>
            <div className='flex items-center space-x-6'>
              <div className='flex items-center space-x-2 bg-green-100 px-4 py-2 rounded-full'>
                <Signal className='h-5 w-5 text-green-600' />
                <span className='text-sm font-semibold text-green-700'>
                  Strong
                </span>
              </div>
              <div className='flex items-center space-x-2 bg-blue-100 px-4 py-2 rounded-full'>
                <Battery className='h-5 w-5 text-blue-600' />
                <span className='text-sm font-semibold text-blue-700'>85%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Status Card */}
        <StatusCard />

        {/* Main Grid */}
        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
          {/* Left Column */}
          <div className='lg:col-span-2 space-y-6'>
            {/* Available Orders */}
            {agentStatus === "online" && (
              <div>
                <h2 className='text-xl font-semibold text-gray-900 mb-4'>
                  Available Orders ({availableOrders.length})
                </h2>
                <div className='space-y-4'>
                  {availableOrders.length > 0 ? (
                    availableOrders.map((order) => (
                      <OrderCard
                        key={order.id}
                        order={order}
                        onAccept={acceptOrder}
                      />
                    ))
                  ) : (
                    <Card className='p-8 text-center'>
                      <Clock className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                      <h3 className='text-lg font-medium text-gray-900 mb-2'>
                        No orders available
                      </h3>
                      <p className='text-gray-600'>
                        New orders will appear here when available
                      </p>
                    </Card>
                  )}
                </div>
              </div>
            )}

            {/* Active Order */}
            {activeOrder && (
              <div>
                <h2 className='text-xl font-semibold text-gray-900 mb-4'>
                  Active Delivery
                </h2>
                <Card className='p-6 border-l-4 border-l-green-500'>
                  {/* Active order content */}
                  <div className='flex items-center justify-between mb-4'>
                    <Badge variant='success'>In Progress</Badge>
                    <span className='text-lg font-bold'>
                      ${activeOrder.earnings}
                    </span>
                  </div>
                  {/* Order details and actions */}
                </Card>
              </div>
            )}
          </div>

          {/* Right Column */}
          <div className='space-y-6'>
            <EarningsCard />
            <PerformanceCard />
            <SmartInsightsCard />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SmartDashboard;
