import logging
from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver
from django.db import transaction

from orders.models import Order, OrderStatusHistory

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=Order)
def track_status_change(sender, instance, **kwargs):
    if instance.pk:  # Only for existing orders
        original = Order.objects.get(pk=instance.pk)
        if original.status != instance.status:
            OrderStatusHistory.objects.create(
                order=instance,
                from_status=original.status,
                to_status=instance.status,
                changed_by=getattr(instance, '_changed_by', None),
                notes=getattr(instance, '_status_change_notes', '')
            )

@receiver(post_save, sender=Order)
def auto_assign_delivery_agent(sender, instance, created, **kwargs):
    """
    DISABLED: Automatic assignment replaced with manual assignment by admin
    Orders with status 'ready' will appear in admin dashboard for manual assignment
    """
    if not created and instance.status == 'ready' and not instance.delivery_agent:
        # Log that order is ready for manual assignment
        logger.info(f"Order {instance.id} is ready for manual assignment by admin")

        # Optional: Send notification to admin about new order ready for assignment
        # This can be implemented with WebSocket, email, or other notification system
        pass

    # LEGACY CODE - Automatic assignment (DISABLED)
    # if not created and instance.status == 'ready' and not instance.delivery_agent:
    #     from .delivery_assignment import delivery_assignment_service
    #     def assign_agent():
    #         try:
    #             logger.info(f"Auto-assigning delivery agent for order {instance.id}")
    #             result = delivery_assignment_service.auto_assign_order(instance)
    #             if result['success']:
    #                 logger.info(f"Successfully auto-assigned order {instance.id} to agent {result['agent_id']}")
    #             else:
    #                 logger.warning(f"Failed to auto-assign order {instance.id}: {result['reason']}")
    #         except Exception as e:
    #             logger.error(f"Error in auto-assignment for order {instance.id}: {str(e)}")
    #     transaction.on_commit(assign_agent)