import React, { useState } from 'react';

function UserRestaurantsTest() {
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);

  const testUserRestaurants = async () => {
    setLoading(true);
    try {
      // Get authentication token
      const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
      const token = user.access_token || user.token;

      console.log("=== USER RESTAURANTS TEST ===");
      console.log("Current user:", user);
      console.log("Token exists:", !!token);

      if (!token) {
        setResults({ error: "No authentication token found. Please log in." });
        setLoading(false);
        return;
      }

      // Test getUserRestaurants API
      const response = await fetch('http://127.0.0.1:8000/api/restaurant/user-restaurants/', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log("API response status:", response.status);

      if (!response.ok) {
        throw new Error(`API failed: ${response.status} - ${await response.text()}`);
      }

      const data = await response.json();
      console.log("User restaurants data:", data);

      setResults({
        success: true,
        user: {
          name: user.name,
          email: user.email,
          role: user.role,
          id: user.id
        },
        restaurants: data,
        count: data.length,
        firstRestaurant: data[0] || null
      });

    } catch (error) {
      console.error("Test error:", error);
      setResults({ success: false, error: error.message });
    }
    setLoading(false);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">User Restaurants Test</h1>
      
      <button
        onClick={testUserRestaurants}
        disabled={loading}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50 mb-6"
      >
        {loading ? "Testing..." : "Test User Restaurants"}
      </button>

      {results && (
        <div className="bg-gray-100 p-4 rounded">
          <h3 className="font-bold mb-2">Test Results:</h3>
          {results.success ? (
            <div>
              <p className="text-green-600 font-bold">✅ Test PASSED</p>
              
              <div className="mt-4">
                <h4 className="font-bold">Current User:</h4>
                <pre className="bg-white p-2 rounded text-sm overflow-auto">
                  {JSON.stringify(results.user, null, 2)}
                </pre>
              </div>

              <div className="mt-4">
                <h4 className="font-bold">Restaurants ({results.count}):</h4>
                <pre className="bg-white p-2 rounded text-sm overflow-auto max-h-96">
                  {JSON.stringify(results.restaurants, null, 2)}
                </pre>
              </div>

              {results.firstRestaurant && (
                <div className="mt-4">
                  <h4 className="font-bold">First Restaurant (Used by Profile):</h4>
                  <p><strong>ID:</strong> {results.firstRestaurant.id}</p>
                  <p><strong>Name:</strong> {results.firstRestaurant.name}</p>
                  <p><strong>Cuisine Types:</strong> {JSON.stringify(results.firstRestaurant.cuisine_types)}</p>
                </div>
              )}
            </div>
          ) : (
            <div>
              <p className="text-red-600 font-bold">❌ Test FAILED</p>
              <p className="text-red-600">{results.error}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default UserRestaurantsTest;
