import React from 'react';
import { 
  Search, 
  ShoppingBag, 
  Heart, 
  Clock, 
  MapPin, 
  Star, 
  Users, 
  Package,
  Filter,
  Wifi,
  AlertCircle
} from 'lucide-react';
import Button from '../common/Button';
import Card from '../common/Card';

const EmptyState = ({
  type = 'default',
  title,
  description,
  icon: CustomIcon,
  actions = [],
  illustration,
  className = "",
  size = 'medium' // 'small', 'medium', 'large'
}) => {
  // Predefined empty state configurations
  const presets = {
    'no-restaurants': {
      icon: Search,
      title: 'No restaurants found',
      description: 'Try adjusting your filters or search in a different area.',
      illustration: '🍽️'
    },
    'no-orders': {
      icon: ShoppingBag,
      title: 'No orders yet',
      description: 'When you place your first order, it will appear here.',
      illustration: '📦'
    },
    'no-favorites': {
      icon: Heart,
      title: 'No favorites yet',
      description: 'Save restaurants you love by tapping the heart icon.',
      illustration: '❤️'
    },
    'no-search-results': {
      icon: Search,
      title: 'No results found',
      description: 'Try different keywords or check your spelling.',
      illustration: '🔍'
    },
    'no-recent-orders': {
      icon: Clock,
      title: 'No recent orders',
      description: 'Your recent orders will appear here once you start ordering.',
      illustration: '⏰'
    },
    'no-delivery-area': {
      icon: MapPin,
      title: 'No delivery to this area',
      description: 'We don\'t deliver to your location yet, but we\'re expanding!',
      illustration: '📍'
    },
    'no-reviews': {
      icon: Star,
      title: 'No reviews yet',
      description: 'Be the first to share your experience with this restaurant.',
      illustration: '⭐'
    },
    'no-menu-items': {
      icon: Package,
      title: 'No menu items',
      description: 'This restaurant hasn\'t added their menu yet.',
      illustration: '🍽️'
    },
    'no-notifications': {
      icon: AlertCircle,
      title: 'No notifications',
      description: 'You\'re all caught up! New notifications will appear here.',
      illustration: '🔔'
    },
    'no-friends': {
      icon: Users,
      title: 'No friends yet',
      description: 'Invite friends to join and share your favorite restaurants.',
      illustration: '👥'
    },
    'filtered-results': {
      icon: Filter,
      title: 'No results match your filters',
      description: 'Try removing some filters to see more options.',
      illustration: '🔽'
    },
    'offline': {
      icon: Wifi,
      title: 'You\'re offline',
      description: 'Check your internet connection to see the latest content.',
      illustration: '📶'
    },
    'error': {
      icon: AlertCircle,
      title: 'Something went wrong',
      description: 'We couldn\'t load this content. Please try again.',
      illustration: '⚠️'
    }
  };

  const config = presets[type] || {};
  const IconComponent = CustomIcon || config.icon || AlertCircle;
  const finalTitle = title || config.title || 'No content available';
  const finalDescription = description || config.description || 'There\'s nothing to show here right now.';
  const finalIllustration = illustration || config.illustration;

  const sizeClasses = {
    small: {
      container: 'py-8',
      icon: 'w-12 h-12',
      iconSize: 24,
      title: 'text-lg',
      description: 'text-sm',
      illustration: 'text-3xl'
    },
    medium: {
      container: 'py-12',
      icon: 'w-16 h-16',
      iconSize: 32,
      title: 'text-xl',
      description: 'text-base',
      illustration: 'text-5xl'
    },
    large: {
      container: 'py-16',
      icon: 'w-20 h-20',
      iconSize: 40,
      title: 'text-2xl',
      description: 'text-lg',
      illustration: 'text-6xl'
    }
  };

  const sizes = sizeClasses[size];

  return (
    <div className={`text-center ${sizes.container} ${className}`}>
      {/* Illustration or Icon */}
      <div className="mb-6">
        {finalIllustration ? (
          <div className={`${sizes.illustration} mb-4`}>
            {finalIllustration}
          </div>
        ) : (
          <div className={`${sizes.icon} bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4`}>
            <IconComponent size={sizes.iconSize} className="text-gray-400" />
          </div>
        )}
      </div>

      {/* Content */}
      <div className="max-w-md mx-auto">
        <h3 className={`font-semibold text-gray-900 mb-2 ${sizes.title}`}>
          {finalTitle}
        </h3>
        
        <p className={`text-gray-600 mb-6 ${sizes.description}`}>
          {finalDescription}
        </p>

        {/* Actions */}
        {actions.length > 0 && (
          <div className="space-y-3">
            {actions.map((action, index) => (
              <Button
                key={index}
                variant={index === 0 ? 'primary' : 'outline'}
                onClick={action.onClick}
                to={action.to}
                icon={action.icon}
                fullWidth={actions.length === 1}
                className={actions.length > 1 ? 'mx-2' : ''}
              >
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Specialized empty state components
export const NoRestaurantsFound = ({ onClearFilters, onChangeLocation }) => (
  <EmptyState
    type="no-restaurants"
    actions={[
      onClearFilters && {
        label: 'Clear Filters',
        onClick: onClearFilters,
        icon: <Filter size={16} />
      },
      onChangeLocation && {
        label: 'Change Location',
        onClick: onChangeLocation,
        icon: <MapPin size={16} />
      }
    ].filter(Boolean)}
  />
);

export const NoSearchResults = ({ searchTerm, onClearSearch }) => (
  <EmptyState
    type="no-search-results"
    title={`No results for "${searchTerm}"`}
    actions={[
      onClearSearch && {
        label: 'Clear Search',
        onClick: onClearSearch,
        icon: <Search size={16} />
      }
    ].filter(Boolean)}
  />
);

export const NoOrders = ({ onBrowseRestaurants }) => (
  <EmptyState
    type="no-orders"
    actions={[
      {
        label: 'Browse Restaurants',
        onClick: onBrowseRestaurants,
        to: '/restaurants',
        icon: <Search size={16} />
      }
    ]}
  />
);

export const NoFavorites = ({ onBrowseRestaurants }) => (
  <EmptyState
    type="no-favorites"
    actions={[
      {
        label: 'Discover Restaurants',
        onClick: onBrowseRestaurants,
        to: '/restaurants',
        icon: <Search size={16} />
      }
    ]}
  />
);

export const OfflineState = ({ onRetry }) => (
  <EmptyState
    type="offline"
    actions={[
      onRetry && {
        label: 'Try Again',
        onClick: onRetry,
        icon: <Wifi size={16} />
      }
    ].filter(Boolean)}
  />
);

// Hook for managing empty states
export const useEmptyState = (data, loading, error) => {
  if (loading) return { isEmpty: false, showEmpty: false };
  if (error) return { isEmpty: true, showEmpty: true, type: 'error' };
  
  const isEmpty = !data || (Array.isArray(data) && data.length === 0);
  return { isEmpty, showEmpty: isEmpty };
};

export default EmptyState;
