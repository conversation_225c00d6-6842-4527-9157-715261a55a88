# system_config/admin.py
from django.contrib import admin
from .models import SystemSetting, ChoiceOption, FilterConfiguration

@admin.register(SystemSetting)
class SystemSettingAdmin(admin.ModelAdmin):
    list_display = ('name', 'key', 'category', 'setting_type', 'is_public', 'is_editable', 'updated_at')
    list_filter = ('category', 'setting_type', 'is_public', 'is_editable')
    search_fields = ('name', 'key', 'description')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('key', 'name', 'description', 'category')
        }),
        ('Value Settings', {
            'fields': ('setting_type', 'value', 'default_value')
        }),
        ('Access Control', {
            'fields': ('is_public', 'is_editable')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(ChoiceOption)
class ChoiceOptionAdmin(admin.ModelAdmin):
    list_display = ('label', 'option_type', 'value', 'icon', 'display_order', 'is_active', 'is_default')
    list_filter = ('option_type', 'is_active', 'is_default')
    search_fields = ('label', 'value', 'description')
    list_editable = ('display_order', 'is_active', 'is_default')
    readonly_fields = ('created_at',)

    fieldsets = (
        ('Basic Information', {
            'fields': ('option_type', 'value', 'label', 'description')
        }),
        ('Display Settings', {
            'fields': ('icon', 'color', 'display_order')
        }),
        ('Status', {
            'fields': ('is_active', 'is_default')
        }),
        ('Timestamps', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )

@admin.register(FilterConfiguration)
class FilterConfigurationAdmin(admin.ModelAdmin):
    list_display = ('name', 'key', 'filter_type', 'display_order', 'is_active', 'is_featured')
    list_filter = ('filter_type', 'is_active', 'is_featured')
    search_fields = ('name', 'key', 'label', 'description')
    list_editable = ('display_order', 'is_active', 'is_featured')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'key', 'filter_type', 'label', 'description', 'icon')
        }),
        ('Range Settings', {
            'fields': ('min_value', 'max_value', 'step'),
            'description': 'Only for range filters'
        }),
        ('Select Settings', {
            'fields': ('options',),
            'description': 'Only for select/multiselect filters'
        }),
        ('Display Settings', {
            'fields': ('display_order', 'is_active', 'is_featured')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
