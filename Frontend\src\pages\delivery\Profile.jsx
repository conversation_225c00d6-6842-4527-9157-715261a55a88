import React, { useState, useEffect, useRef } from "react";
import {
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Star,
  TruckIcon,
  CreditCard,
  Shield,
  Edit,
  Save,
  X,
  Upload,
  Camera,
  CheckCircle,
  AlertCircle,
  Key,
  Award,
  Clock,
  DollarSign,
  FileText,
  Settings,
  Bell,
  Eye,
  EyeOff,
  RefreshCw,
  Download,
  Trash2,
  Plus,
  Minus,
  Activity,
  Target,
  Zap,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";
import { toast } from "react-hot-toast";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import Input from "../../components/common/Input";

function DeliveryProfile() {
  const { user } = useAuth();
  const [agent, setAgent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [avatarFile, setAvatarFile] = useState(null);
  const [avatarPreview, setAvatarPreview] = useState(null);
  const fileInputRef = useRef(null);
  const [showModal, setShowModal] = useState(false);
  const [modalMessage, setModalMessage] = useState("");
  const [modalType, setModalType] = useState("success"); // success or error

  // Password change modal states
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [passwordErrors, setPasswordErrors] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    email: "",
    address: "",
    vehicleType: "",
    licensePlate: "",
    vehicleColor: "",
    deliveryZones: [],
    bankName: "",
    accountNumber: "",
    accountName: "",
  });

  useEffect(() => {
    if (user) {
      loadProfileData();
    }
  }, [user]);

  const loadProfileData = async () => {
    try {
      setLoading(true);
      console.log("=== LOADING DELIVERY PROFILE ===");
      console.log("Current user:", user);

      // Load delivery agent profile from API
      const profileResult = await deliveryAgentApi.getProfile();

      if (profileResult.success) {
        const agentData = profileResult.data;
        setAgent(agentData);
        setAvatarPreview(agentData.profile_photo || agentData.user?.avatar);
        console.log("Agent profile loaded:", agentData);

        // Initialize form data with API data
        setFormData({
          name: agentData.full_name || agentData.user?.name || "",
          phone: agentData.phone_number || agentData.user?.phone || "",
          email: agentData.email || agentData.user?.email || "",
          address: agentData.address || "",
          vehicleType: agentData.vehicle_type || "",
          licensePlate: agentData.license_plate || "",
          vehicleColor: agentData.vehicle_color || "",
          deliveryZones: agentData.delivery_zones || [],
          bankName: agentData.bank_name || "",
          accountNumber: agentData.account_number || "",
          accountName: agentData.account_holder_name || "",
        });
      } else {
        console.error("Failed to load profile:", profileResult.error);
        toast.error("Failed to load profile data");

        // Use fallback data from user object
        setFormData({
          name: user?.name || "",
          phone: user?.phone || "",
          email: user?.email || "",
          address: "",
          vehicleType: "",
          licensePlate: "",
          vehicleColor: "",
          deliveryZones: [],
          bankName: "",
          accountNumber: "",
          accountName: "",
        });
      }

      setLoading(false);
    } catch (err) {
      console.error("Error loading profile data:", err);
      toast.error("Error loading profile data");
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePasswordInputChange = (e) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user types
    if (passwordErrors[name]) {
      setPasswordErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleAvatarClick = () => {
    fileInputRef.current.click();
  };

  const handleAvatarChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const showSuccessModal = (message) => {
    setModalMessage(message);
    setModalType("success");
    setShowModal(true);

    // Auto close after 3 seconds
    setTimeout(() => {
      setShowModal(false);
    }, 3000);
  };

  const showErrorModal = (message) => {
    setModalMessage(message);
    setModalType("error");
    setShowModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      console.log("=== UPDATING DELIVERY PROFILE ===");
      console.log("Form data:", formData);

      // Prepare update data for API
      const updateData = {
        phone: formData.phone,
        vehicle_type: formData.vehicleType,
        vehicle_number: formData.licensePlate,
        is_available: agent?.is_available || true,
      };

      // TODO: Call API to update profile when deliveryAgentApi is implemented
      // const result = await deliveryAgentApi.updateProfile(updateData);
      // For now, simulate successful update
      const result = { success: true, data: updateData };

      if (result.success) {
        console.log("Profile updated successfully:", result.data);

        // Update local agent data
        setAgent((prev) => ({
          ...prev,
          ...result.data,
          user: {
            ...prev?.user,
            name: formData.name,
            email: formData.email,
            phone: formData.phone,
          },
          phone: formData.phone,
          vehicle_type: formData.vehicleType,
          vehicle_number: formData.licensePlate,
          avatar: avatarPreview || prev?.avatar,
          currentLocation: {
            ...prev?.currentLocation,
            address: formData.address,
          },
          vehicle: {
            type: formData.vehicleType,
            licensePlate: formData.licensePlate,
            color: formData.vehicleColor,
          },
          deliveryZones: formData.deliveryZones,
          bankDetails: {
            bankName: formData.bankName,
            accountNumber: formData.accountNumber,
            accountName: formData.accountName,
          },
        }));

        showSuccessModal("Profile updated successfully!");
        setEditMode(false);
      } else {
        console.error("Failed to update profile:", result.error);
        showErrorModal(
          result.error || "Failed to update profile. Please try again."
        );
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      showErrorModal("Failed to update profile. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const validatePasswordForm = () => {
    let isValid = true;
    const errors = {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    };

    if (!passwordData.currentPassword.trim()) {
      errors.currentPassword = "Current password is required";
      isValid = false;
    }

    if (!passwordData.newPassword.trim()) {
      errors.newPassword = "New password is required";
      isValid = false;
    } else if (passwordData.newPassword.length < 6) {
      errors.newPassword = "Password must be at least 6 characters";
      isValid = false;
    }

    if (!passwordData.confirmPassword.trim()) {
      errors.confirmPassword = "Please confirm your new password";
      isValid = false;
    } else if (passwordData.confirmPassword !== passwordData.newPassword) {
      errors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    setPasswordErrors(errors);
    return isValid;
  };

  const handlePasswordSubmit = (e) => {
    e.preventDefault();

    if (!validatePasswordForm()) {
      return;
    }

    setPasswordLoading(true);

    // Simulate API call to change password
    setTimeout(() => {
      try {
        // In a real app, this would verify the current password
        // and update with the new one

        // For demo purposes, let's assume it's successful
        setPasswordLoading(false);
        setShowPasswordModal(false);

        // Reset password form
        setPasswordData({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        });

        // Show success message
        showSuccessModal("Password has been updated successfully!");
      } catch (error) {
        setPasswordLoading(false);
        // Show error in the password modal or close it and show in the main modal
        showErrorModal("Failed to update password. Please try again.");
      }
    }, 1500);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className='p-6 animate-fade-in'>
        <div className='flex justify-center items-center h-96'>
          <div className='text-center'>
            <div className='animate-spin rounded-full h-16 w-16 border-b-2 border-primary-500 mx-auto mb-4'></div>
            <p className='text-gray-600 text-lg'>Loading your profile...</p>
            <p className='text-gray-500 text-sm mt-2'>
              Please wait while we fetch your information
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!agent) {
    return (
      <div className='p-6 animate-fade-in'>
        <div className='text-center py-16'>
          <AlertCircle size={64} className='text-gray-400 mx-auto mb-4' />
          <h2 className='text-2xl font-bold text-gray-900 mb-2'>
            Profile Not Found
          </h2>
          <p className='text-gray-500 mb-6'>
            We couldn't find your delivery agent profile.
          </p>
          <Button
            variant='primary'
            onClick={loadProfileData}
            icon={<RefreshCw size={16} />}
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className='p-6 animate-fade-in'>
      {/* Status Modal */}
      {showModal && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg max-w-md w-full animate-fade-in p-6'>
            <div className='flex items-start mb-4'>
              {modalType === "success" ? (
                <CheckCircle
                  size={24}
                  className='text-green-500 mr-3 flex-shrink-0'
                />
              ) : (
                <AlertCircle
                  size={24}
                  className='text-accent-red mr-3 flex-shrink-0'
                />
              )}
              <div>
                <h3 className='text-lg font-semibold mb-2'>
                  {modalType === "success" ? "Success" : "Error"}
                </h3>
                <p
                  className={`${
                    modalType === "success"
                      ? "text-green-700"
                      : "text-accent-red"
                  } mb-4`}
                >
                  {modalMessage}
                </p>
              </div>
            </div>
            <div className='flex justify-end'>
              <Button
                variant={modalType === "success" ? "primary" : "danger"}
                onClick={() => setShowModal(false)}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Password Change Modal */}
      {showPasswordModal && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg max-w-md w-full animate-fade-in p-6'>
            <div className='flex justify-between items-center mb-4'>
              <h3 className='text-lg font-semibold'>Change Password</h3>
              <button
                className='text-gray-400 hover:text-gray-600'
                onClick={() => setShowPasswordModal(false)}
              >
                <X size={20} />
              </button>
            </div>

            <form onSubmit={handlePasswordSubmit}>
              <div className='space-y-4 mb-6'>
                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Current Password
                  </label>
                  <div className='relative'>
                    <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                      <Key size={16} className='text-gray-400' />
                    </div>
                    <input
                      type='password'
                      name='currentPassword'
                      value={passwordData.currentPassword}
                      onChange={handlePasswordInputChange}
                      className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                        passwordErrors.currentPassword
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      placeholder='Enter your current password'
                    />
                  </div>
                  {passwordErrors.currentPassword && (
                    <p className='mt-1 text-sm text-red-600'>
                      {passwordErrors.currentPassword}
                    </p>
                  )}
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    New Password
                  </label>
                  <div className='relative'>
                    <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                      <Key size={16} className='text-gray-400' />
                    </div>
                    <input
                      type='password'
                      name='newPassword'
                      value={passwordData.newPassword}
                      onChange={handlePasswordInputChange}
                      className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                        passwordErrors.newPassword
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      placeholder='Enter your new password'
                    />
                  </div>
                  {passwordErrors.newPassword && (
                    <p className='mt-1 text-sm text-red-600'>
                      {passwordErrors.newPassword}
                    </p>
                  )}
                </div>

                <div>
                  <label className='block text-sm font-medium text-gray-700 mb-2'>
                    Confirm New Password
                  </label>
                  <div className='relative'>
                    <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                      <Key size={16} className='text-gray-400' />
                    </div>
                    <input
                      type='password'
                      name='confirmPassword'
                      value={passwordData.confirmPassword}
                      onChange={handlePasswordInputChange}
                      className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${
                        passwordErrors.confirmPassword
                          ? "border-red-500"
                          : "border-gray-300"
                      }`}
                      placeholder='Confirm your new password'
                    />
                  </div>
                  {passwordErrors.confirmPassword && (
                    <p className='mt-1 text-sm text-red-600'>
                      {passwordErrors.confirmPassword}
                    </p>
                  )}
                </div>
              </div>

              <div className='flex justify-end space-x-3'>
                <Button
                  type='button'
                  variant='secondary'
                  onClick={() => setShowPasswordModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  type='submit'
                  variant='primary'
                  loading={passwordLoading}
                >
                  Update Password
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className='flex justify-between items-center mb-6'>
        <div>
          <h1 className='text-2xl font-bold'>Delivery Profile</h1>
          <p className='text-gray-600 mt-1'>
            Manage your delivery agent profile and settings
          </p>
        </div>
        <div className='flex space-x-3'>
          <Button
            variant='outline'
            icon={<RefreshCw size={16} />}
            onClick={loadProfileData}
            disabled={loading}
          >
            Refresh
          </Button>
          {!editMode ? (
            <Button
              variant='primary'
              icon={<Edit size={16} />}
              onClick={() => setEditMode(true)}
            >
              Edit Profile
            </Button>
          ) : (
            <Button
              variant='outline'
              icon={<X size={16} />}
              onClick={() => {
                setEditMode(false);
                // Reset form data and avatar preview to original values
                if (agent) {
                  setFormData({
                    name: agent.user?.name || agent.name || "",
                    phone: agent.phone || agent.user?.phone || "",
                    email: agent.user?.email || agent.email || "",
                    address:
                      agent.address || agent.currentLocation?.address || "",
                    vehicleType: agent.vehicleType || agent.vehicle_type || "",
                    licensePlate:
                      agent.vehicleNumber || agent.vehicle_number || "",
                    vehicleColor: agent.vehicleColor || "Black",
                    deliveryZones:
                      agent.delivery_zones || agent.deliveryZones || [],
                    bankName:
                      agent.bank_name || agent.bankDetails?.bankName || "",
                    accountNumber:
                      agent.account_number ||
                      agent.bankDetails?.accountNumber ||
                      "",
                    accountName:
                      agent.account_name ||
                      agent.bankDetails?.accountName ||
                      "",
                  });
                  setAvatarPreview(agent.avatar || agent.user?.avatar);
                  setAvatarFile(null);
                }
              }}
            >
              Cancel
            </Button>
          )}
        </div>
      </div>

      {/* Profile Header */}
      <Card className='mb-6'>
        <div className='p-6'>
          <div className='flex flex-col md:flex-row items-center mb-6'>
            <div className='relative mb-6 md:mb-0 md:mr-6'>
              <div className='w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg'>
                <img
                  src={
                    avatarPreview ||
                    agent?.avatar ||
                    agent?.user?.avatar ||
                    "/default-avatar.png"
                  }
                  alt={agent?.user?.name || agent?.name || "Delivery Agent"}
                  className='w-full h-full object-cover'
                />
              </div>
              {editMode && (
                <>
                  <input
                    type='file'
                    ref={fileInputRef}
                    className='hidden'
                    accept='image/*'
                    onChange={handleAvatarChange}
                  />
                  <button
                    className='absolute bottom-2 right-2 bg-primary-500 text-white p-2 rounded-full shadow-lg hover:bg-primary-600 transition-colors'
                    onClick={handleAvatarClick}
                  >
                    <Camera size={18} />
                  </button>
                </>
              )}
            </div>

            <div className='text-center md:text-left md:flex-1'>
              <h2 className='text-2xl font-bold mb-2'>
                {agent?.full_name || agent?.user?.name || "Delivery Agent"}
              </h2>
              <p className='text-gray-600 mb-3'>
                {agent?.email || agent?.user?.email || "No email provided"}
              </p>

              <div className='flex flex-wrap justify-center md:justify-start gap-4 text-sm'>
                <div className='flex items-center'>
                  <Star size={16} className='text-yellow-500 mr-1' />
                  <span className='font-medium'>{agent?.rating || "5.0"}</span>
                  <span className='text-gray-500 ml-1'>Rating</span>
                </div>
                <div className='flex items-center'>
                  <TruckIcon size={16} className='text-blue-500 mr-1' />
                  <span className='font-medium'>
                    {agent?.total_deliveries || agent?.totalDeliveries || "0"}
                  </span>
                  <span className='text-gray-500 ml-1'>Deliveries</span>
                </div>
                <div className='flex items-center'>
                  <Calendar size={16} className='text-green-500 mr-1' />
                  <span className='text-gray-500'>
                    Joined{" "}
                    {formatDate(
                      agent?.created_at || agent?.joinedDate || new Date()
                    )}
                  </span>
                </div>
              </div>
            </div>

            <div className='mt-4 md:mt-0 flex flex-col items-center md:items-end space-y-2'>
              <Badge
                className={`${
                  agent?.employment_status === "active" ||
                  agent?.availability === "available"
                    ? "bg-green-100 text-green-800"
                    : agent?.availability === "busy"
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                {agent?.employment_status === "active"
                  ? agent?.availability === "available"
                    ? "Available"
                    : agent?.availability === "busy"
                    ? "Busy"
                    : "Offline"
                  : "Pending"}
              </Badge>
              <div className='text-xs text-gray-500 text-center'>
                ID: {agent?.agent_id || agent?.id || "N/A"}
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 border-t'>
            <div className='text-center'>
              <div className='flex items-center justify-center mb-2'>
                <Award size={20} className='text-yellow-500' />
              </div>
              <div className='text-lg font-bold'>{agent?.rating || "5.0"}</div>
              <div className='text-xs text-gray-500'>Average Rating</div>
            </div>
            <div className='text-center'>
              <div className='flex items-center justify-center mb-2'>
                <Clock size={20} className='text-blue-500' />
              </div>
              <div className='text-lg font-bold'>
                {agent?.vehicleType || agent?.vehicle_type || "Motorcycle"}
              </div>
              <div className='text-xs text-gray-500'>Vehicle Type</div>
            </div>
            <div className='text-center'>
              <div className='flex items-center justify-center mb-2'>
                <MapPin size={20} className='text-green-500' />
              </div>
              <div className='text-lg font-bold'>
                {agent?.delivery_zones?.length ||
                  agent?.deliveryZones?.length ||
                  "0"}
              </div>
              <div className='text-xs text-gray-500'>Delivery Zones</div>
            </div>
            <div className='text-center'>
              <div className='flex items-center justify-center mb-2'>
                <Shield size={20} className='text-purple-500' />
              </div>
              <div className='text-lg font-bold'>Verified</div>
              <div className='text-xs text-gray-500'>Account Status</div>
            </div>
          </div>
        </div>
      </Card>

      <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
        {/* Personal Information */}
        <div className='md:col-span-2'>
          <Card>
            <div className='p-6 border-b'>
              <h2 className='text-lg font-semibold'>Personal Information</h2>
            </div>

            <div className='p-6'>
              {editMode ? (
                <form onSubmit={handleSubmit} className='space-y-6'>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        <User size={16} className='inline mr-2' />
                        Full Name
                      </label>
                      <input
                        type='text'
                        name='name'
                        value={formData.name}
                        onChange={handleInputChange}
                        className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 hover:border-gray-400'
                        placeholder='Enter your full name'
                        required
                      />
                    </div>

                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        <Phone size={16} className='inline mr-2' />
                        Phone Number
                      </label>
                      <input
                        type='tel'
                        name='phone'
                        value={formData.phone}
                        onChange={handleInputChange}
                        className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 hover:border-gray-400'
                        placeholder='+93 XXX XXX XXX'
                        required
                      />
                    </div>

                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        <Mail size={16} className='inline mr-2' />
                        Email Address
                      </label>
                      <input
                        type='email'
                        name='email'
                        value={formData.email}
                        onChange={handleInputChange}
                        className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 hover:border-gray-400'
                        placeholder='<EMAIL>'
                        required
                      />
                    </div>

                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        <MapPin size={16} className='inline mr-2' />
                        Current Address
                      </label>
                      <input
                        type='text'
                        name='address'
                        value={formData.address}
                        onChange={handleInputChange}
                        className='w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 hover:border-gray-400'
                        placeholder='Enter your current address'
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className='block text-sm font-medium text-gray-700 mb-2'>
                      Delivery Zones
                    </label>
                    <select
                      multiple
                      className='w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={formData.deliveryZones}
                      onChange={(e) => {
                        const options = [...e.target.selectedOptions];
                        const values = options.map((option) => option.value);
                        setFormData((prev) => ({
                          ...prev,
                          deliveryZones: values,
                        }));
                      }}
                    >
                      <option value='Central Kabul'>Central Kabul</option>
                      <option value='North Kabul'>North Kabul</option>
                      <option value='East Kabul'>East Kabul</option>
                      <option value='West Kabul'>West Kabul</option>
                      <option value='South Kabul'>South Kabul</option>
                      <option value='Shahr-e Naw'>Shahr-e Naw</option>
                      <option value='Wazir Akbar Khan'>Wazir Akbar Khan</option>
                      <option value='Taimani'>Taimani</option>
                    </select>
                    <p className='text-xs text-gray-500 mt-1'>
                      Hold Ctrl/Cmd to select multiple zones
                    </p>
                  </div>

                  <Button
                    type='submit'
                    variant='primary'
                    fullWidth
                    icon={<Save size={16} />}
                  >
                    Save Changes
                  </Button>
                </form>
              ) : (
                <div className='space-y-6'>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                      <h3 className='text-sm font-medium text-gray-500 mb-1'>
                        Full Name
                      </h3>
                      <div className='flex items-center'>
                        <User size={18} className='text-gray-400 mr-2' />
                        <p>
                          {agent?.full_name ||
                            agent?.name ||
                            "No name provided"}
                        </p>
                      </div>
                    </div>

                    <div>
                      <h3 className='text-sm font-medium text-gray-500 mb-1'>
                        Phone Number
                      </h3>
                      <div className='flex items-center'>
                        <Phone size={18} className='text-gray-400 mr-2' />
                        <p>
                          {agent?.phone_number ||
                            agent?.phone ||
                            "Not provided"}
                        </p>
                      </div>
                    </div>

                    <div>
                      <h3 className='text-sm font-medium text-gray-500 mb-1'>
                        Email Address
                      </h3>
                      <div className='flex items-center'>
                        <Mail size={18} className='text-gray-400 mr-2' />
                        <p>{agent?.email || "No email provided"}</p>
                      </div>
                    </div>

                    <div>
                      <h3 className='text-sm font-medium text-gray-500 mb-1'>
                        Current Address
                      </h3>
                      <div className='flex items-center'>
                        <MapPin size={18} className='text-gray-400 mr-2' />
                        <p>
                          {agent?.currentLocation?.address ||
                            agent?.address ||
                            "Kabul, Afghanistan"}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className='text-sm font-medium text-gray-500 mb-1'>
                      Delivery Zones
                    </h3>
                    <div className='flex flex-wrap gap-2 mt-2'>
                      {(agent?.deliveryZones || ["Central Kabul"]).map(
                        (zone, index) => (
                          <Badge
                            key={index}
                            className='bg-gray-100 text-gray-800'
                          >
                            {zone}
                          </Badge>
                        )
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Vehicle Information */}
          <Card className='mt-6'>
            <div className='p-6 border-b'>
              <h2 className='text-lg font-semibold'>Vehicle Information</h2>
            </div>

            <div className='p-6'>
              {editMode ? (
                <div className='space-y-6'>
                  <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        Vehicle Type
                      </label>
                      <select
                        name='vehicleType'
                        value={formData.vehicleType}
                        onChange={handleInputChange}
                        className='w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      >
                        <option value='bicycle'>Bicycle</option>
                        <option value='motorcycle'>Motorcycle</option>
                        <option value='car'>Car</option>
                      </select>
                    </div>

                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        License Plate
                      </label>
                      <input
                        type='text'
                        name='licensePlate'
                        value={formData.licensePlate}
                        onChange={handleInputChange}
                        className='w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      />
                    </div>

                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        Vehicle Color
                      </label>
                      <input
                        type='text'
                        name='vehicleColor'
                        value={formData.vehicleColor}
                        onChange={handleInputChange}
                        className='w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      />
                    </div>
                  </div>

                  <div>
                    <label className='block text-sm font-medium text-gray-700 mb-2'>
                      Vehicle Documents
                    </label>
                    <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center'>
                      <Upload
                        size={24}
                        className='mx-auto text-gray-400 mb-2'
                      />
                      <p className='text-sm text-gray-500'>
                        Drag and drop your vehicle documents here, or{" "}
                        <span className='text-primary-500 cursor-pointer'>
                          browse
                        </span>
                      </p>
                      <p className='text-xs text-gray-400 mt-1'>
                        Supported formats: PDF, JPG, PNG (Max 5MB)
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                  <div>
                    <h3 className='text-sm font-medium text-gray-500 mb-1'>
                      Vehicle Type
                    </h3>
                    <div className='flex items-center'>
                      <TruckIcon size={18} className='text-gray-400 mr-2' />
                      <p className='capitalize'>
                        {agent?.vehicle_type ||
                          agent?.vehicleType ||
                          "Not provided"}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className='text-sm font-medium text-gray-500 mb-1'>
                      Vehicle Model
                    </h3>
                    <p>
                      {agent?.vehicle_model ||
                        agent?.vehicleModel ||
                        "Not provided"}
                    </p>
                  </div>

                  <div>
                    <h3 className='text-sm font-medium text-gray-500 mb-1'>
                      License Plate
                    </h3>
                    <p>
                      {agent?.license_plate ||
                        agent?.vehicleNumber ||
                        "Not provided"}
                    </p>
                  </div>

                  <div>
                    <h3 className='text-sm font-medium text-gray-500 mb-1'>
                      Vehicle Color
                    </h3>
                    <p>
                      {agent?.vehicleColor || agent?.vehicle?.color || "Black"}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Payment Information */}
          <Card className='mt-6'>
            <div className='p-6 border-b'>
              <h2 className='text-lg font-semibold'>Payment Information</h2>
            </div>

            <div className='p-6'>
              {editMode ? (
                <div className='space-y-6'>
                  <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        Bank Name
                      </label>
                      <input
                        type='text'
                        name='bankName'
                        value={formData.bankName}
                        onChange={handleInputChange}
                        className='w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      />
                    </div>

                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        Account Number
                      </label>
                      <input
                        type='text'
                        name='accountNumber'
                        value={formData.accountNumber}
                        onChange={handleInputChange}
                        className='w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      />
                    </div>

                    <div>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        Account Name
                      </label>
                      <input
                        type='text'
                        name='accountName'
                        value={formData.accountName}
                        onChange={handleInputChange}
                        className='w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
                  <div>
                    <h3 className='text-sm font-medium text-gray-500 mb-1'>
                      Bank Name
                    </h3>
                    <div className='flex items-center'>
                      <CreditCard size={18} className='text-gray-400 mr-2' />
                      <p>
                        {agent?.bank_name || agent?.bankName || "Not provided"}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className='text-sm font-medium text-gray-500 mb-1'>
                      Account Holder Name
                    </h3>
                    <p>
                      {agent?.account_holder_name ||
                        agent?.accountName ||
                        "Not provided"}
                    </p>
                  </div>

                  <div>
                    <h3 className='text-sm font-medium text-gray-500 mb-1'>
                      Account Number
                    </h3>
                    <p>
                      {agent?.account_number
                        ? `••••••${agent.account_number.slice(-4)}`
                        : "Not provided"}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Sidebar */}
        <div>
          {/* Performance Stats */}
          <Card className='mb-6'>
            <div className='p-6 border-b'>
              <div className='flex items-center justify-between'>
                <h2 className='text-lg font-semibold'>Performance Stats</h2>
                <Activity size={20} className='text-blue-500' />
              </div>
            </div>

            <div className='p-6'>
              <div className='space-y-6'>
                <div>
                  <div className='flex justify-between items-center mb-2'>
                    <span className='text-sm font-medium flex items-center'>
                      <Star size={14} className='text-yellow-500 mr-1' />
                      Rating
                    </span>
                    <span className='text-sm font-bold'>
                      {agent?.rating || "5.0"}/5
                    </span>
                  </div>
                  <div className='w-full bg-gray-200 rounded-full h-3'>
                    <div
                      className='bg-gradient-to-r from-yellow-400 to-yellow-500 h-3 rounded-full transition-all duration-300'
                      style={{ width: `${((agent?.rating || 5) / 5) * 100}%` }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className='flex justify-between items-center mb-2'>
                    <span className='text-sm font-medium flex items-center'>
                      <Clock size={14} className='text-green-500 mr-1' />
                      On-time Delivery
                    </span>
                    <span className='text-sm font-bold'>92%</span>
                  </div>
                  <div className='w-full bg-gray-200 rounded-full h-3'>
                    <div
                      className='bg-gradient-to-r from-green-400 to-green-500 h-3 rounded-full transition-all duration-300'
                      style={{ width: "92%" }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className='flex justify-between items-center mb-2'>
                    <span className='text-sm font-medium flex items-center'>
                      <CheckCircle size={14} className='text-blue-500 mr-1' />
                      Order Acceptance
                    </span>
                    <span className='text-sm font-bold'>85%</span>
                  </div>
                  <div className='w-full bg-gray-200 rounded-full h-3'>
                    <div
                      className='bg-gradient-to-r from-blue-400 to-blue-500 h-3 rounded-full transition-all duration-300'
                      style={{ width: "85%" }}
                    ></div>
                  </div>
                </div>

                <div>
                  <div className='flex justify-between items-center mb-2'>
                    <span className='text-sm font-medium flex items-center'>
                      <Target size={14} className='text-purple-500 mr-1' />
                      Completion Rate
                    </span>
                    <span className='text-sm font-bold'>96%</span>
                  </div>
                  <div className='w-full bg-gray-200 rounded-full h-3'>
                    <div
                      className='bg-gradient-to-r from-purple-400 to-purple-500 h-3 rounded-full transition-all duration-300'
                      style={{ width: "96%" }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className='mt-6 pt-6 border-t border-gray-100'>
                <div className='grid grid-cols-1 gap-4'>
                  <div className='text-center p-3 bg-blue-50 rounded-lg'>
                    <div className='text-2xl font-bold text-blue-600'>
                      {agent?.total_deliveries || agent?.totalDeliveries || "0"}
                    </div>
                    <div className='text-xs text-gray-600'>
                      Total Deliveries
                    </div>
                  </div>
                  <div className='text-center p-3 bg-green-50 rounded-lg'>
                    <div className='text-2xl font-bold text-green-600'>
                      ${(agent?.totalEarnings || 0).toFixed(2)}
                    </div>
                    <div className='text-xs text-gray-600'>Total Earnings</div>
                  </div>
                  <div className='text-center p-3 bg-purple-50 rounded-lg'>
                    <div className='text-sm font-bold text-purple-600'>
                      {formatDate(
                        agent?.created_at || agent?.joinedDate || new Date()
                      )}
                    </div>
                    <div className='text-xs text-gray-600'>Member Since</div>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Quick Actions */}
          <Card className='mb-6'>
            <div className='p-6 border-b'>
              <div className='flex items-center justify-between'>
                <h2 className='text-lg font-semibold'>Quick Actions</h2>
                <Zap size={20} className='text-yellow-500' />
              </div>
            </div>
            <div className='p-6'>
              <div className='space-y-3'>
                <Button
                  variant='outline'
                  className='w-full justify-start'
                  icon={<Download size={16} />}
                >
                  Download Profile Data
                </Button>
                <Button
                  variant='outline'
                  className='w-full justify-start'
                  icon={<FileText size={16} />}
                >
                  View Delivery History
                </Button>
                <Button
                  variant='outline'
                  className='w-full justify-start'
                  icon={<DollarSign size={16} />}
                >
                  View Earnings Report
                </Button>
                <Button
                  variant='outline'
                  className='w-full justify-start'
                  icon={<Settings size={16} />}
                >
                  Account Settings
                </Button>
              </div>
            </div>
          </Card>

          {/* Account Security */}
          <Card>
            <div className='p-6 border-b'>
              <h2 className='text-lg font-semibold'>Account Security</h2>
            </div>

            <div className='p-6'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center'>
                    <Shield size={18} className='text-gray-400 mr-2' />
                    <span>Change Password</span>
                  </div>
                  <Button
                    variant='outline'
                    size='small'
                    onClick={() => setShowPasswordModal(true)}
                  >
                    Change
                  </Button>
                </div>

                <div className='flex items-center justify-between'>
                  <div className='flex items-center'>
                    <Phone size={18} className='text-gray-400 mr-2' />
                    <span>Two-Factor Authentication</span>
                  </div>
                  <div className='relative inline-block w-10 mr-2 align-middle select-none'>
                    <input
                      type='checkbox'
                      name='toggle'
                      id='toggle'
                      className='toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer'
                    />
                    <label
                      htmlFor='toggle'
                      className='toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer'
                    ></label>
                  </div>
                </div>

                <div className='flex items-center justify-between'>
                  <div className='flex items-center'>
                    <Mail size={18} className='text-gray-400 mr-2' />
                    <span>Email Notifications</span>
                  </div>
                  <div className='relative inline-block w-10 mr-2 align-middle select-none'>
                    <input
                      type='checkbox'
                      name='toggle2'
                      id='toggle2'
                      className='toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer'
                      defaultChecked
                    />
                    <label
                      htmlFor='toggle2'
                      className='toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer'
                    ></label>
                  </div>
                </div>
              </div>

              <div className='mt-6'>
                <Button
                  variant='outline'
                  fullWidth
                  className='text-red-500 hover:bg-red-50'
                >
                  Deactivate Account
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Custom CSS for toggle switches */}
      <style jsx>{`
        .toggle-checkbox:checked {
          right: 0;
          border-color: #3b82f6;
        }
        .toggle-checkbox:checked + .toggle-label {
          background-color: #3b82f6;
        }
        .toggle-checkbox {
          right: 0;
          transition: all 0.3s;
        }
        .toggle-label {
          transition: all 0.3s;
        }
      `}</style>
    </div>
  );
}

export default DeliveryProfile;
