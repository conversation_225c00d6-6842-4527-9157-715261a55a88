import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';

const LoginTest = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  const [testResults, setTestResults] = useState([]);
  const [testing, setTesting] = useState(false);

  // Test credentials
  const testCredentials = [
    {
      username: 'admin',
      password: 'admin123',
      role: 'admin',
      description: 'Admin User',
      expectedRoute: '/admin'
    },
    {
      username: 'EMP001',
      password: 'employee123',
      role: 'delivery_agent',
      description: 'Test Employee',
      expectedRoute: '/delivery'
    },
    {
      username: 'delivery',
      password: 'delivery123',
      role: 'delivery_agent',
      description: 'Delivery Agent',
      expectedRoute: '/delivery'
    }
  ];

  const testLogin = async (credentials) => {
    try {
      console.log(`🧪 Testing login for: ${credentials.description}`);
      
      const result = await login(credentials.username, credentials.password);
      
      if (result.success) {
        const user = result.user;
        const success = user.role === credentials.role;
        
        return {
          ...credentials,
          success: true,
          actualRole: user.role,
          verified: user.is_verified,
          message: success ? 'Login successful' : `Role mismatch: expected ${credentials.role}, got ${user.role}`,
          user: user
        };
      } else {
        return {
          ...credentials,
          success: false,
          message: result.error || 'Login failed',
          error: result.errors
        };
      }
    } catch (error) {
      return {
        ...credentials,
        success: false,
        message: `Error: ${error.message}`,
        error: error
      };
    }
  };

  const runAllTests = async () => {
    setTesting(true);
    setTestResults([]);
    
    const results = [];
    
    for (const cred of testCredentials) {
      const result = await testLogin(cred);
      results.push(result);
      setTestResults([...results]);
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    setTesting(false);
    
    // Show summary
    const successful = results.filter(r => r.success).length;
    const total = results.length;
    
    if (successful === total) {
      toast.success(`All ${total} login tests passed!`);
    } else {
      toast.error(`${successful}/${total} login tests passed`);
    }
  };

  const testSingleLogin = async (credentials) => {
    setTesting(true);
    
    try {
      const result = await testLogin(credentials);
      
      if (result.success) {
        toast.success(`Login successful for ${credentials.description}`);
        
        // Navigate to appropriate route
        const routes = {
          admin: '/admin',
          delivery_agent: '/delivery',
          restaurant: '/restaurant',
          customer: '/'
        };
        
        const route = routes[result.actualRole] || '/';
        setTimeout(() => navigate(route), 1500);
      } else {
        toast.error(`Login failed: ${result.message}`);
      }
      
      setTestResults([result]);
    } catch (error) {
      toast.error(`Test error: ${error.message}`);
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = (result) => {
    if (result.success) {
      return result.actualRole === result.role ? '✅' : '⚠️';
    }
    return '❌';
  };

  const getStatusColor = (result) => {
    if (result.success) {
      return result.actualRole === result.role ? 'text-green-600' : 'text-yellow-600';
    }
    return 'text-red-600';
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🧪 Employee Login Test Suite
          </h1>
          
          <div className="mb-6">
            <p className="text-gray-600 mb-4">
              Test employee login functionality with various credentials.
            </p>
            
            <div className="flex gap-4">
              <button
                onClick={runAllTests}
                disabled={testing}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {testing ? 'Testing...' : 'Run All Tests'}
              </button>
              
              <button
                onClick={() => navigate('/')}
                className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
              >
                Back to Home
              </button>
            </div>
          </div>

          {/* Individual Test Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {testCredentials.map((cred, index) => (
              <div key={index} className="border rounded-lg p-4">
                <h3 className="font-semibold text-gray-900 mb-2">
                  {cred.description}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  Username: <code className="bg-gray-100 px-1 rounded">{cred.username}</code>
                </p>
                <p className="text-sm text-gray-600 mb-3">
                  Role: <span className="font-medium">{cred.role}</span>
                </p>
                <button
                  onClick={() => testSingleLogin(cred)}
                  disabled={testing}
                  className="w-full bg-green-600 text-white py-2 rounded hover:bg-green-700 disabled:opacity-50"
                >
                  Test Login
                </button>
              </div>
            ))}
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="border-t pt-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                Test Results
              </h2>
              
              <div className="space-y-4">
                {testResults.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">
                        {getStatusIcon(result)} {result.description}
                      </h3>
                      <span className={`font-medium ${getStatusColor(result)}`}>
                        {result.success ? 'PASS' : 'FAIL'}
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Username: <code>{result.username}</code></p>
                      <p>Expected Role: <span className="font-medium">{result.role}</span></p>
                      {result.actualRole && (
                        <p>Actual Role: <span className="font-medium">{result.actualRole}</span></p>
                      )}
                      {result.verified !== undefined && (
                        <p>Verified: <span className="font-medium">{result.verified ? 'Yes' : 'No'}</span></p>
                      )}
                      <p className={getStatusColor(result)}>
                        {result.message}
                      </p>
                      {result.error && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-red-600">Error Details</summary>
                          <pre className="mt-1 text-xs bg-red-50 p-2 rounded overflow-auto">
                            {JSON.stringify(result.error, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoginTest;
