import React, { useState, useEffect } from 'react';
import { deliveryAgentApi } from '../../services/deliveryAgentApi';
import { Button } from '../../components/common/Button';
import { Card } from '../../components/common/Card';

const TestOnlineStatus = () => {
  const [isOnline, setIsOnline] = useState(false);
  const [availability, setAvailability] = useState('offline');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [agentInfo, setAgentInfo] = useState(null);

  // Fetch current status
  const fetchStatus = async () => {
    try {
      setLoading(true);
      const result = await deliveryAgentApi.getDashboard();
      if (result.success || result.data) {
        const data = result.data || result;
        setAgentInfo(data);
        setIsOnline(data.is_online || false);
        setAvailability(data.availability || 'offline');
      } else {
        setError('Failed to fetch status');
      }
    } catch (err) {
      setError('Failed to fetch status');
    } finally {
      setLoading(false);
    }
  };

  // Toggle online status
  const toggleOnlineStatus = async () => {
    try {
      setLoading(true);
      setError('');
      setSuccess('');

      console.log('Calling toggleOnlineStatus API...');
      const result = await deliveryAgentApi.toggleOnlineStatus();
      console.log('API Result:', result);

      if (result.success) {
        const newStatus = result.data.data?.is_online;
        const newAvailability = result.data.data?.availability;
        
        console.log('New status:', newStatus, 'New availability:', newAvailability);
        
        setIsOnline(newStatus);
        setAvailability(newAvailability);
        setSuccess(`Successfully ${newStatus ? 'went online' : 'went offline'}`);
        
        // Refresh status
        await fetchStatus();
      } else {
        setError(result.error?.message || 'Failed to toggle online status');
      }
    } catch (err) {
      console.error('Toggle error:', err);
      setError('Failed to toggle online status');
    } finally {
      setLoading(false);
    }
  };

  // Update availability
  const updateAvailability = async (newAvailability) => {
    try {
      setLoading(true);
      setError('');
      setSuccess('');

      console.log('Updating availability to:', newAvailability);
      const result = await deliveryAgentApi.updateAvailability({
        availability: newAvailability,
        is_online: newAvailability !== 'offline'
      });
      console.log('Availability API Result:', result);

      if (result.success) {
        const updatedAvailability = result.data.data?.availability;
        const updatedOnline = result.data.data?.is_online;
        
        setAvailability(updatedAvailability);
        setIsOnline(updatedOnline);
        setSuccess(`Availability updated to ${updatedAvailability}`);
        
        // Refresh status
        await fetchStatus();
      } else {
        setError(result.error?.message || 'Failed to update availability');
      }
    } catch (err) {
      console.error('Availability error:', err);
      setError('Failed to update availability');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Online Status Test Page
          </h1>
          <p className="text-gray-600">
            Test the online status and availability functionality
          </p>
        </div>

        {/* Current Status */}
        <Card className="mb-6 p-6">
          <h2 className="text-lg font-semibold mb-4">Current Status</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="font-medium">Online Status:</span>{' '}
              <span className={`px-2 py-1 rounded text-sm ${
                isOnline ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {isOnline ? 'Online' : 'Offline'}
              </span>
            </div>
            <div>
              <span className="font-medium">Availability:</span>{' '}
              <span className={`px-2 py-1 rounded text-sm ${
                availability === 'available' ? 'bg-green-100 text-green-800' :
                availability === 'busy' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {availability}
              </span>
            </div>
          </div>
          
          {agentInfo && (
            <div className="mt-4 p-4 bg-gray-50 rounded">
              <h3 className="font-medium mb-2">Agent Info:</h3>
              <pre className="text-sm text-gray-600">
                {JSON.stringify(agentInfo, null, 2)}
              </pre>
            </div>
          )}
        </Card>

        {/* Messages */}
        {error && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}
        {success && (
          <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
            {success}
          </div>
        )}

        {/* Controls */}
        <Card className="mb-6 p-6">
          <h2 className="text-lg font-semibold mb-4">Controls</h2>
          
          <div className="space-y-4">
            {/* Toggle Online/Offline */}
            <div>
              <h3 className="font-medium mb-2">Toggle Online Status</h3>
              <Button
                onClick={toggleOnlineStatus}
                disabled={loading}
                className={`${
                  isOnline 
                    ? 'bg-red-600 hover:bg-red-700' 
                    : 'bg-green-600 hover:bg-green-700'
                } text-white`}
              >
                {loading ? 'Loading...' : (isOnline ? 'Go Offline' : 'Go Online')}
              </Button>
            </div>

            {/* Availability Controls */}
            <div>
              <h3 className="font-medium mb-2">Set Availability</h3>
              <div className="flex space-x-2">
                <Button
                  onClick={() => updateAvailability('available')}
                  disabled={loading}
                  variant={availability === 'available' ? 'primary' : 'outline'}
                >
                  Available
                </Button>
                <Button
                  onClick={() => updateAvailability('busy')}
                  disabled={loading}
                  variant={availability === 'busy' ? 'primary' : 'outline'}
                >
                  Busy
                </Button>
                <Button
                  onClick={() => updateAvailability('break')}
                  disabled={loading}
                  variant={availability === 'break' ? 'primary' : 'outline'}
                >
                  On Break
                </Button>
                <Button
                  onClick={() => updateAvailability('offline')}
                  disabled={loading}
                  variant={availability === 'offline' ? 'primary' : 'outline'}
                >
                  Offline
                </Button>
              </div>
            </div>

            {/* Refresh */}
            <div>
              <Button onClick={fetchStatus} disabled={loading} variant="outline">
                {loading ? 'Loading...' : 'Refresh Status'}
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default TestOnlineStatus;
