import React from 'react';
import { Mic } from 'lucide-react';

const VoiceSearchIndicator = ({ isListening }) => {
  if (!isListening) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-8 text-center max-w-sm mx-4">
        <div className="relative mb-6">
          <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto">
            <Mic size={32} className="text-red-500" />
          </div>
          
          {/* Pulse animation */}
          <div className="absolute inset-0 w-20 h-20 mx-auto">
            <div className="w-full h-full bg-red-200 rounded-full animate-ping opacity-75"></div>
          </div>
          <div className="absolute inset-0 w-20 h-20 mx-auto">
            <div className="w-full h-full bg-red-300 rounded-full animate-ping opacity-50 animation-delay-200"></div>
          </div>
        </div>
        
        <h3 className="text-xl font-semibold mb-2">Listening...</h3>
        <p className="text-gray-600 mb-4">
          Speak now to search for restaurants or dishes
        </p>
        
        <div className="flex justify-center space-x-1">
          <div className="w-1 h-4 bg-red-500 rounded-full animate-pulse"></div>
          <div className="w-1 h-6 bg-red-500 rounded-full animate-pulse animation-delay-100"></div>
          <div className="w-1 h-8 bg-red-500 rounded-full animate-pulse animation-delay-200"></div>
          <div className="w-1 h-6 bg-red-500 rounded-full animate-pulse animation-delay-300"></div>
          <div className="w-1 h-4 bg-red-500 rounded-full animate-pulse animation-delay-400"></div>
        </div>
      </div>
    </div>
  );
};

export default VoiceSearchIndicator;
