import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useNavigate, Link, useSearchParams } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import Input from "../../components/common/Input";
import Button from "../../components/common/Button";
import { Mail, Lock, User, AlertCircle, Phone, Users } from "lucide-react";
import toast from "../../utils/toast";
import useFormPersistence from "../../hooks/useFormPersistence";

const Register = () => {
  const [searchParams] = useSearchParams();
  const preSelectedRole = searchParams.get("role");

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    mode: "onChange", // Validate on change for better UX
  });
  const { register: registerUser } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const password = watch("password", "");
  const confirmPassword = watch("confirmPassword", "");

  // Form persistence
  const watchedValues = watch();
  const { clearFormData } = useFormPersistence(
    watchedValues,
    setValue,
    "register_form_data",
    ["password", "confirmPassword"],
    [preSelectedRole]
  );

  // Pre-select role if coming from restaurant partner page
  useEffect(() => {
    if (preSelectedRole === "restaurant") {
      setValue("role", "restaurant");
    }
  }, [preSelectedRole, setValue]);

  // Handle registration form submission
  const onSubmit = async (data) => {
    setLoading(true);

    // Client-side validation
    if (data.password !== data.confirmPassword) {
      toast.validation.mismatch("Password", "Confirm Password");
      setLoading(false);
      return;
    }

    try {
      console.log("🚀 Starting registration for:", data.email);

      // Show loading toast
      const loadingToast = toast.loading("Creating your account...");

      // Prepare registration data
      const registrationData = {
        name: data.name,
        user_name: data.username,
        email: data.email,
        password: data.password,
        confirm_password: data.confirmPassword,
        phone: data.phone,
        role: data.role || "customer",
      };

      const result = await registerUser(registrationData);

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (result.success && result.requiresVerification) {
        console.log("✅ Registration successful, redirecting to verification");

        // Clear saved form data on successful registration
        clearFormData();

        // Show success toast
        toast.auth.registerSuccess(data.email);

        // Determine redirect path based on role
        const redirectPath =
          data.role === "restaurant"
            ? `/verify-email?email=${encodeURIComponent(
                data.email
              )}&redirect=register-restaurant`
            : `/verify-email?email=${encodeURIComponent(data.email)}`;

        // Navigate to email verification page
        navigate(redirectPath, {
          state: {
            message: `Registration successful! Please check ${data.email} for your verification code.`,
            fromRegistration: true,
            userRole: data.role,
          },
        });
      } else {
        console.log("❌ Registration failed:", result.error);

        // Handle specific field errors
        if (result.errors && typeof result.errors === "object") {
          // Show specific field errors
          Object.entries(result.errors).forEach(([field, message]) => {
            if (field === "non_field_errors") {
              toast.error(message);
            } else {
              toast.error(`${field}: ${message}`);
            }
          });
        } else {
          toast.auth.registerError(
            result.error || "Registration failed. Please try again."
          );
        }
      }
    } catch (err) {
      console.error("❌ Registration error:", err);
      toast.error("Something went wrong. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='animate-fade-in'>
      <div className='text-center mb-8'>
        <h1 className='text-2xl font-poppins font-bold text-text-primary mb-2'>
          Create Account
        </h1>
        <p className='text-text-secondary'>
          Join Afghan Sofra to order delicious food
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className='space-y-5'>
        <div>
          <Input
            label='Full Name'
            placeholder='Enter your full name'
            icon={<User size={18} />}
            error={errors.name?.message}
            required
            {...register("name", {
              required: "Full name is required",
              minLength: {
                value: 2,
                message: "Name must be at least 2 characters long",
              },
              maxLength: {
                value: 100,
                message: "Name cannot exceed 100 characters",
              },
              pattern: {
                value: /^[a-zA-Z\s]+$/,
                message: "Name can only contain letters and spaces",
              },
              validate: (value) => {
                const trimmed = value?.trim();
                if (!trimmed) return "Name cannot be empty";
                if (trimmed.length < 2)
                  return "Name must be at least 2 characters long";
                return true;
              },
            })}
          />
        </div>
        <div>
          <Input
            label='Username'
            type='text'
            placeholder='Choose a username'
            icon={<User size={18} />}
            error={errors.username?.message}
            required
            {...register("username", {
              required: "Username is required",
              minLength: {
                value: 3,
                message: "Username must be at least 3 characters long",
              },
              maxLength: {
                value: 100,
                message: "Username cannot exceed 100 characters",
              },
              pattern: {
                value: /^[a-zA-Z0-9_.-]+$/,
                message:
                  "Username can only contain letters, numbers, dots, hyphens, and underscores",
              },
              validate: (value) => {
                const trimmed = value?.trim();
                if (!trimmed) return "Username cannot be empty";
                if (trimmed.length < 3)
                  return "Username must be at least 3 characters long";
                return true;
              },
            })}
          />
        </div>
        <div>
          <Input
            label='Email Address'
            type='email'
            placeholder='Enter your email'
            icon={<Mail size={18} />}
            error={errors.email?.message}
            required
            {...register("email", {
              required: "Email address is required",
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: "Please enter a valid email address",
              },
              validate: (value) => {
                const trimmed = value?.trim().toLowerCase();
                if (!trimmed) return "Email address cannot be empty";
                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(trimmed)) {
                  return "Please enter a valid email address";
                }
                return true;
              },
            })}
          />
        </div>
        <div>
          <Input
            label='Phone Number'
            type='tel'
            placeholder='Enter your phone number'
            icon={<Phone size={18} />}
            error={errors.phone?.message}
            required
            {...register("phone", {
              required: "Phone number is required",
              pattern: {
                value: /^\+?[1-9]\d{1,14}$/,
                message:
                  "Phone number must be in international format (e.g., +1234567890)",
              },
              validate: (value) => {
                const trimmed = value?.trim();
                if (!trimmed) return "Phone number cannot be empty";
                if (!/^\+?[1-9]\d{1,14}$/.test(trimmed)) {
                  return "Phone number must be in international format (e.g., +1234567890)";
                }
                return true;
              },
            })}
          />
        </div>
        <div>
          <label className='block text-sm font-medium text-text-primary mb-2'>
            <Users size={18} className='inline mr-2' />
            Role
          </label>
          <select
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500'
            {...register("role", {
              required: "Please select a role",
            })}
          >
            <option value=''>Select your role</option>
            <option value='customer'>Customer</option>
            <option value='restaurant'>Restaurant Owner</option>
            {/* Delivery Agent role removed - employees are created by admin */}
          </select>
          {errors.role && (
            <p className='text-sm text-accent-red mt-1'>
              {errors.role.message}
            </p>
          )}
        </div>
        <div>
          <Input
            label='Password'
            type='password'
            placeholder='Create a password'
            icon={<Lock size={18} />}
            error={errors.password?.message}
            required
            {...register("password", {
              required: "Password is required",
              minLength: {
                value: 8,
                message: "Password must be at least 8 characters long",
              },
              validate: (value) => {
                if (!value) return "Password is required";
                if (value.length < 8)
                  return "Password must be at least 8 characters long";
                if (!/[A-Za-z]/.test(value))
                  return "Password must contain at least one letter";
                if (!/\d/.test(value))
                  return "Password must contain at least one number";
                return true;
              },
            })}
          />
        </div>
        <div>
          <Input
            label='Confirm Password'
            type='password'
            placeholder='Confirm your password'
            icon={<Lock size={18} />}
            error={errors.confirmPassword?.message}
            required
            {...register("confirmPassword", {
              required: "Please confirm your password",
              validate: (value) => {
                if (!value) return "Please confirm your password";
                if (value !== password) return "Passwords do not match";
                return true;
              },
            })}
          />
        </div>
        <div className='flex items-center'>
          <input
            type='checkbox'
            id='terms'
            className='rounded border-gray-300 text-primary-500 focus:ring-primary-500 mr-2'
            {...register("terms", {
              required: "You must agree to the terms and conditions",
            })}
          />
          <label htmlFor='terms' className='text-sm text-text-secondary'>
            I agree to the{" "}
            <Link
              to='/terms'
              className='text-primary-500 hover:text-primary-600'
            >
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link
              to='/privacy'
              className='text-primary-500 hover:text-primary-600'
            >
              Privacy Policy
            </Link>
          </label>
        </div>
        {errors.terms && (
          <p className='text-sm text-accent-red mt-1'>{errors.terms.message}</p>
        )}
        <Button type='submit' variant='primary' fullWidth loading={loading}>
          Create Account
        </Button>
        <div className='text-center text-sm text-text-secondary mt-6'>
          Already have an account?{" "}
          <Link
            to='/login'
            className='text-primary-500 hover:text-primary-600 font-medium'
          >
            Sign In
          </Link>
        </div>
      </form>
    </div>
  );
};

export default Register;
