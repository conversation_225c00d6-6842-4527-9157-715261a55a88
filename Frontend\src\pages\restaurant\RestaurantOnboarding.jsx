import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  CheckCircle,
  Circle,
  ArrowRight,
  ArrowLeft,
  Star,
  Upload,
  Menu,
  Settings,
  Users,
  DollarSign,
  Clock,
  MapPin,
  Phone,
  Mail,
  Camera,
  FileText,
  Play,
  BookOpen,
  Award,
  Target,
  Zap,
} from "lucide-react";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import Badge from "../../components/common/Badge";
import { useAuth } from "../../context/AuthContext";

const RestaurantOnboarding = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [progress, setProgress] = useState(0);

  const onboardingSteps = [
    {
      id: 1,
      title: "Welcome & Setup",
      description: "Get started with your restaurant profile",
      icon: <Star size={24} />,
      tasks: [
        {
          id: "profile",
          title: "Complete restaurant profile",
          completed: false,
        },
        { id: "hours", title: "Set operating hours", completed: false },
        {
          id: "contact",
          title: "Verify contact information",
          completed: false,
        },
      ],
    },
    {
      id: 2,
      title: "Menu Setup",
      description: "Add your menu categories and items",
      icon: <Menu size={24} />,
      tasks: [
        { id: "categories", title: "Create menu categories", completed: false },
        { id: "items", title: "Add menu items (minimum 5)", completed: false },
        { id: "photos", title: "Upload food photos", completed: false },
        { id: "pricing", title: "Set competitive pricing", completed: false },
      ],
    },
    {
      id: 3,
      title: "Payment & Delivery",
      description: "Configure payment and delivery settings",
      icon: <DollarSign size={24} />,
      tasks: [
        { id: "payment", title: "Set up payment methods", completed: false },
        { id: "delivery", title: "Configure delivery zones", completed: false },
        {
          id: "fees",
          title: "Set delivery fees and minimums",
          completed: false,
        },
      ],
    },
    {
      id: 4,
      title: "Training & Launch",
      description: "Learn the platform and go live",
      icon: <Zap size={24} />,
      tasks: [
        {
          id: "training",
          title: "Complete platform training",
          completed: false,
        },
        { id: "test", title: "Process a test order", completed: false },
        {
          id: "launch",
          title: "Go live and start receiving orders",
          completed: false,
        },
      ],
    },
  ];

  const [steps, setSteps] = useState(onboardingSteps);

  useEffect(() => {
    // Calculate progress based on completed tasks
    const totalTasks = steps.reduce((sum, step) => sum + step.tasks.length, 0);
    const completedTasks = steps.reduce(
      (sum, step) => sum + step.tasks.filter((task) => task.completed).length,
      0
    );
    setProgress((completedTasks / totalTasks) * 100);
  }, [steps]);

  const toggleTask = (stepId, taskId) => {
    setSteps((prevSteps) =>
      prevSteps.map((step) =>
        step.id === stepId
          ? {
              ...step,
              tasks: step.tasks.map((task) =>
                task.id === taskId
                  ? { ...task, completed: !task.completed }
                  : task
              ),
            }
          : step
      )
    );
  };

  const isStepCompleted = (stepId) => {
    const step = steps.find((s) => s.id === stepId);
    return step?.tasks.every((task) => task.completed) || false;
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (stepId) => {
    setCurrentStep(stepId);
  };

  const finishOnboarding = () => {
    // Mark onboarding as complete and redirect to dashboard
    navigate("/restaurant");
  };

  const currentStepData = steps.find((step) => step.id === currentStep);

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <Star className='text-primary-600' size={40} />
              </div>
              <h2 className='text-2xl font-bold mb-2'>
                Welcome to Afghan Sofra!
              </h2>
              <p className='text-text-secondary max-w-2xl mx-auto'>
                Congratulations! Your restaurant has been approved. Let's get
                you set up to start receiving orders.
              </p>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <Card className='p-6 text-center'>
                <Settings className='mx-auto text-blue-500 mb-4' size={32} />
                <h3 className='font-semibold mb-2'>Complete Profile</h3>
                <p className='text-sm text-text-secondary mb-4'>
                  Add your restaurant details, photos, and description
                </p>
                <Button variant='outline' size='small' to='/restaurant/profile'>
                  Update Profile
                </Button>
              </Card>

              <Card className='p-6 text-center'>
                <Clock className='mx-auto text-green-500 mb-4' size={32} />
                <h3 className='font-semibold mb-2'>Set Hours</h3>
                <p className='text-sm text-text-secondary mb-4'>
                  Configure your operating hours and availability
                </p>
                <Button variant='outline' size='small' to='/restaurant/profile'>
                  Set Hours
                </Button>
              </Card>

              <Card className='p-6 text-center'>
                <Phone className='mx-auto text-purple-500 mb-4' size={32} />
                <h3 className='font-semibold mb-2'>Verify Contact</h3>
                <p className='text-sm text-text-secondary mb-4'>
                  Ensure your contact information is accurate
                </p>
                <Button variant='outline' size='small' to='/restaurant/profile'>
                  Verify Info
                </Button>
              </Card>
            </div>

            <div className='bg-blue-50 p-6 rounded-lg'>
              <h3 className='font-semibold text-blue-800 mb-3'>
                Quick Tips for Success:
              </h3>
              <ul className='space-y-2 text-blue-700'>
                <li className='flex items-start'>
                  <CheckCircle
                    size={16}
                    className='mr-2 mt-0.5 flex-shrink-0'
                  />
                  <span>
                    Upload high-quality photos of your restaurant and food
                  </span>
                </li>
                <li className='flex items-start'>
                  <CheckCircle
                    size={16}
                    className='mr-2 mt-0.5 flex-shrink-0'
                  />
                  <span>Write a compelling restaurant description</span>
                </li>
                <li className='flex items-start'>
                  <CheckCircle
                    size={16}
                    className='mr-2 mt-0.5 flex-shrink-0'
                  />
                  <span>
                    Set accurate operating hours to avoid customer confusion
                  </span>
                </li>
              </ul>
            </div>
          </div>
        );

      case 2:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <Menu className='text-green-600' size={40} />
              </div>
              <h2 className='text-2xl font-bold mb-2'>Build Your Menu</h2>
              <p className='text-text-secondary max-w-2xl mx-auto'>
                Create an attractive menu that showcases your best dishes and
                drives sales.
              </p>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <Card className='p-6'>
                <div className='flex items-center mb-4'>
                  <FileText className='text-blue-500 mr-3' size={24} />
                  <h3 className='font-semibold'>Menu Categories</h3>
                </div>
                <p className='text-sm text-text-secondary mb-4'>
                  Organize your menu into clear categories like appetizers,
                  mains, desserts, etc.
                </p>
                <Button
                  variant='primary'
                  size='small'
                  to='/restaurant/menu-categories'
                >
                  Manage Categories
                </Button>
              </Card>

              <Card className='p-6'>
                <div className='flex items-center mb-4'>
                  <Camera className='text-green-500 mr-3' size={24} />
                  <h3 className='font-semibold'>Menu Items</h3>
                </div>
                <p className='text-sm text-text-secondary mb-4'>
                  Add your dishes with descriptions, prices, and mouth-watering
                  photos.
                </p>
                <Button variant='primary' size='small' to='/restaurant/menu'>
                  Add Items
                </Button>
              </Card>
            </div>

            <div className='bg-green-50 p-6 rounded-lg'>
              <h3 className='font-semibold text-green-800 mb-3'>
                Menu Best Practices:
              </h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-green-700'>
                <ul className='space-y-2'>
                  <li className='flex items-start'>
                    <Target size={16} className='mr-2 mt-0.5 flex-shrink-0' />
                    <span>Use clear, appetizing descriptions</span>
                  </li>
                  <li className='flex items-start'>
                    <Target size={16} className='mr-2 mt-0.5 flex-shrink-0' />
                    <span>Include high-quality food photos</span>
                  </li>
                </ul>
                <ul className='space-y-2'>
                  <li className='flex items-start'>
                    <Target size={16} className='mr-2 mt-0.5 flex-shrink-0' />
                    <span>Price competitively with similar restaurants</span>
                  </li>
                  <li className='flex items-start'>
                    <Target size={16} className='mr-2 mt-0.5 flex-shrink-0' />
                    <span>Mark popular items and dietary options</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <DollarSign className='text-yellow-600' size={40} />
              </div>
              <h2 className='text-2xl font-bold mb-2'>
                Payment & Delivery Setup
              </h2>
              <p className='text-text-secondary max-w-2xl mx-auto'>
                Configure your payment methods and delivery settings to start
                accepting orders.
              </p>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
              <Card className='p-6 text-center'>
                <DollarSign className='mx-auto text-green-500 mb-4' size={32} />
                <h3 className='font-semibold mb-2'>Payment Methods</h3>
                <p className='text-sm text-text-secondary mb-4'>
                  Set up how customers can pay for their orders
                </p>
                <div className='space-y-2 text-sm'>
                  <div className='flex items-center justify-between'>
                    <span>Cash on Delivery</span>
                    <Badge variant='success'>Enabled</Badge>
                  </div>
                  <div className='flex items-center justify-between'>
                    <span>Card Payment</span>
                    <Badge variant='success'>Enabled</Badge>
                  </div>
                </div>
              </Card>

              <Card className='p-6 text-center'>
                <MapPin className='mx-auto text-blue-500 mb-4' size={32} />
                <h3 className='font-semibold mb-2'>Delivery Zones</h3>
                <p className='text-sm text-text-secondary mb-4'>
                  Define areas where you deliver
                </p>
                <Button variant='outline' size='small'>
                  Configure Zones
                </Button>
              </Card>

              <Card className='p-6 text-center'>
                <Settings className='mx-auto text-purple-500 mb-4' size={32} />
                <h3 className='font-semibold mb-2'>Delivery Settings</h3>
                <p className='text-sm text-text-secondary mb-4'>
                  Set fees, minimums, and delivery times
                </p>
                <Button variant='outline' size='small'>
                  Set Fees
                </Button>
              </Card>
            </div>

            <div className='bg-yellow-50 p-6 rounded-lg'>
              <h3 className='font-semibold text-yellow-800 mb-3'>
                Recommended Settings:
              </h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-yellow-700'>
                <div>
                  <h4 className='font-medium mb-2'>Delivery Fee: $2-5</h4>
                  <p className='text-sm'>Competitive with local restaurants</p>
                </div>
                <div>
                  <h4 className='font-medium mb-2'>Minimum Order: $15-25</h4>
                  <p className='text-sm'>Ensures profitable deliveries</p>
                </div>
                <div>
                  <h4 className='font-medium mb-2'>Delivery Time: 30-45 min</h4>
                  <p className='text-sm'>Set realistic expectations</p>
                </div>
                <div>
                  <h4 className='font-medium mb-2'>
                    Delivery Radius: 3-5 miles
                  </h4>
                  <p className='text-sm'>Balance reach with quality</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className='space-y-6'>
            <div className='text-center mb-8'>
              <div className='w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                <Zap className='text-purple-600' size={40} />
              </div>
              <h2 className='text-2xl font-bold mb-2'>Ready to Launch!</h2>
              <p className='text-text-secondary max-w-2xl mx-auto'>
                Complete your training and go live to start receiving orders
                from hungry customers.
              </p>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
              <Card className='p-6'>
                <div className='flex items-center mb-4'>
                  <BookOpen className='text-blue-500 mr-3' size={24} />
                  <h3 className='font-semibold'>Platform Training</h3>
                </div>
                <p className='text-sm text-text-secondary mb-4'>
                  Learn how to manage orders, update menu, and use all platform
                  features.
                </p>
                <div className='space-y-2 mb-4'>
                  <div className='flex items-center text-sm'>
                    <Play size={16} className='mr-2 text-green-500' />
                    <span>Order Management (5 min)</span>
                  </div>
                  <div className='flex items-center text-sm'>
                    <Play size={16} className='mr-2 text-green-500' />
                    <span>Menu Updates (3 min)</span>
                  </div>
                  <div className='flex items-center text-sm'>
                    <Play size={16} className='mr-2 text-green-500' />
                    <span>Customer Communication (4 min)</span>
                  </div>
                </div>
                <Button variant='primary' size='small'>
                  Start Training
                </Button>
              </Card>

              <Card className='p-6'>
                <div className='flex items-center mb-4'>
                  <Award className='text-green-500 mr-3' size={24} />
                  <h3 className='font-semibold'>Go Live Checklist</h3>
                </div>
                <div className='space-y-3'>
                  <div className='flex items-center'>
                    <CheckCircle className='text-green-500 mr-3' size={16} />
                    <span className='text-sm'>Profile completed</span>
                  </div>
                  <div className='flex items-center'>
                    <CheckCircle className='text-green-500 mr-3' size={16} />
                    <span className='text-sm'>Menu items added</span>
                  </div>
                  <div className='flex items-center'>
                    <Circle className='text-gray-400 mr-3' size={16} />
                    <span className='text-sm'>Training completed</span>
                  </div>
                  <div className='flex items-center'>
                    <Circle className='text-gray-400 mr-3' size={16} />
                    <span className='text-sm'>Test order processed</span>
                  </div>
                </div>
                <Button
                  variant='success'
                  size='small'
                  className='mt-4'
                  fullWidth
                >
                  Go Live Now!
                </Button>
              </Card>
            </div>

            <div className='bg-purple-50 p-6 rounded-lg text-center'>
              <h3 className='font-semibold text-purple-800 mb-3'>
                🎉 You're Almost Ready!
              </h3>
              <p className='text-purple-700 mb-4'>
                Complete the final steps and start receiving orders from
                customers in your area.
              </p>
              <div className='flex justify-center space-x-4'>
                <Button variant='outline' size='small'>
                  Contact Support
                </Button>
                <Button
                  variant='primary'
                  size='small'
                  onClick={finishOnboarding}
                >
                  Finish Onboarding
                </Button>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='container mx-auto px-4 py-8 max-w-6xl'>
        {/* Header */}
        <div className='text-center mb-8'>
          <h1 className='text-3xl font-bold text-text-primary mb-2'>
            Restaurant Onboarding
          </h1>
          <p className='text-text-secondary'>
            Complete these steps to set up your restaurant and start receiving
            orders
          </p>
        </div>

        {/* Progress Bar */}
        <div className='mb-8'>
          <div className='flex items-center justify-between mb-4'>
            <span className='text-sm font-medium text-text-secondary'>
              Overall Progress
            </span>
            <span className='text-sm font-medium text-primary-600'>
              {Math.round(progress)}% Complete
            </span>
          </div>
          <div className='w-full bg-gray-200 rounded-full h-2'>
            <div
              className='bg-primary-500 h-2 rounded-full transition-all duration-300'
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Step Navigation */}
        <div className='mb-8'>
          <div className='flex items-center justify-between'>
            {steps.map((step, index) => (
              <div key={step.id} className='flex items-center'>
                <button
                  onClick={() => goToStep(step.id)}
                  className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-colors ${
                    currentStep === step.id
                      ? "bg-primary-500 border-primary-500 text-white"
                      : isStepCompleted(step.id)
                      ? "bg-green-500 border-green-500 text-white"
                      : "bg-white border-gray-300 text-gray-500 hover:border-primary-300"
                  }`}
                >
                  {isStepCompleted(step.id) ? (
                    <CheckCircle size={20} />
                  ) : (
                    step.icon
                  )}
                </button>
                {index < steps.length - 1 && (
                  <div
                    className={`w-16 h-0.5 mx-2 transition-colors ${
                      isStepCompleted(step.id) ? "bg-green-500" : "bg-gray-300"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>

          <div className='text-center mt-4'>
            <h2 className='text-xl font-semibold text-text-primary mb-1'>
              {currentStepData?.title}
            </h2>
            <p className='text-text-secondary text-sm'>
              {currentStepData?.description}
            </p>
          </div>
        </div>

        <div className='grid grid-cols-1 lg:grid-cols-4 gap-8'>
          {/* Sidebar - Task Checklist */}
          <div className='lg:col-span-1'>
            <Card className='p-6 sticky top-8'>
              <h3 className='font-semibold mb-4'>Step {currentStep} Tasks</h3>
              <div className='space-y-3'>
                {currentStepData?.tasks.map((task) => (
                  <div key={task.id} className='flex items-start'>
                    <button
                      onClick={() => toggleTask(currentStep, task.id)}
                      className={`mr-3 mt-0.5 flex-shrink-0 ${
                        task.completed ? "text-green-500" : "text-gray-400"
                      }`}
                    >
                      {task.completed ? (
                        <CheckCircle size={16} />
                      ) : (
                        <Circle size={16} />
                      )}
                    </button>
                    <span
                      className={`text-sm ${
                        task.completed
                          ? "text-green-700 line-through"
                          : "text-text-primary"
                      }`}
                    >
                      {task.title}
                    </span>
                  </div>
                ))}
              </div>

              {isStepCompleted(currentStep) && (
                <div className='mt-4 p-3 bg-green-50 rounded-lg'>
                  <div className='flex items-center text-green-700'>
                    <CheckCircle size={16} className='mr-2' />
                    <span className='text-sm font-medium'>Step Complete!</span>
                  </div>
                </div>
              )}
            </Card>
          </div>

          {/* Main Content */}
          <div className='lg:col-span-3'>
            <Card className='p-8'>
              {renderStepContent()}

              {/* Navigation Buttons */}
              <div className='flex justify-between items-center mt-8 pt-6 border-t'>
                <div>
                  {currentStep > 1 && (
                    <Button
                      variant='outline'
                      onClick={prevStep}
                      icon={<ArrowLeft size={18} />}
                    >
                      Previous
                    </Button>
                  )}
                </div>

                <div className='flex items-center space-x-4'>
                  <span className='text-sm text-text-secondary'>
                    Step {currentStep} of {steps.length}
                  </span>

                  {currentStep < steps.length ? (
                    <Button
                      variant='primary'
                      onClick={nextStep}
                      icon={<ArrowRight size={18} />}
                      iconPosition='right'
                    >
                      Next Step
                    </Button>
                  ) : (
                    <Button
                      variant='success'
                      onClick={finishOnboarding}
                      icon={<CheckCircle size={18} />}
                    >
                      Complete Onboarding
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Help Section */}
        <div className='mt-8 text-center'>
          <p className='text-text-secondary text-sm mb-4'>
            Need help getting started?
          </p>
          <div className='flex justify-center space-x-4'>
            <Button variant='outline' size='small'>
              Contact Support
            </Button>
            <Button variant='outline' size='small'>
              Watch Tutorial
            </Button>
            <Button variant='outline' size='small'>
              View Documentation
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RestaurantOnboarding;
