#!/usr/bin/env python3
"""
Detailed test of dashboard data for employee accounts
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_detailed_dashboard(username, password):
    """Test detailed dashboard data"""
    print(f"\n🧪 Testing Detailed Dashboard for: {username}")
    print("-" * 50)
    
    # Login
    login_data = {"user_name": username, "password": password}
    response = requests.post(f"{BASE_URL}/auth/login/", json=login_data)
    access_token = response.json()['data']['access_token']
    user_info = response.json()['data']['user']
    
    print(f"✅ Logged in as: {user_info['name']} (ID: {user_info['id']})")
    
    # Get dashboard data
    headers = {"Authorization": f"Bearer {access_token}"}
    response = requests.get(f"{BASE_URL}/delivery-agent/dashboard/", headers=headers)
    
    if response.status_code == 200:
        data = response.json()['data']
        agent_info = data['agent_info']
        
        print("\n📊 Agent Information:")
        print(f"   Agent ID: {agent_info.get('agent_id')}")
        print(f"   Full Name: {agent_info.get('full_name')}")
        print(f"   Employee Number: {agent_info.get('employee_number')}")
        print(f"   Employment Status: {agent_info.get('employment_status')}")
        print(f"   Application Status: {agent_info.get('application_status')}")
        print(f"   Availability: {agent_info.get('availability')}")
        print(f"   Is Verified: {agent_info.get('is_verified')}")
        print(f"   Is Clocked In: {agent_info.get('is_clocked_in')}")
        print(f"   Is On Duty: {agent_info.get('is_on_duty')}")
        print(f"   Rating: {agent_info.get('rating')}")
        print(f"   Hire Date: {agent_info.get('hire_date')}")
        
        print("\n📈 Today's Stats:")
        today_stats = data['today_stats']
        print(f"   Deliveries: {today_stats.get('deliveries_completed')}")
        print(f"   Earnings: ${today_stats.get('earnings')}")
        print(f"   Hours Worked: {today_stats.get('hours_worked')}")
        print(f"   Completion Rate: {today_stats.get('completion_rate')}%")
        
        print("\n📊 Overall Stats:")
        overall_stats = data['overall_stats']
        print(f"   Total Deliveries: {overall_stats.get('total_deliveries')}")
        print(f"   Successful Deliveries: {overall_stats.get('successful_deliveries')}")
        print(f"   Total Earnings: ${overall_stats.get('total_earnings')}")
        print(f"   Average Rating: {overall_stats.get('average_rating')}")
        
        print("\n🎯 Status Info:")
        status_info = data['status_info']
        print(f"   Can Accept Orders: {status_info.get('can_accept_orders')}")
        print(f"   Next Action: {status_info.get('next_action')}")
        
        # Check if data is properly personalized
        is_personalized = (
            agent_info.get('agent_id') and
            agent_info.get('full_name') and
            agent_info.get('employment_status') == 'active' and
            agent_info.get('application_status') == 'approved'
        )
        
        if is_personalized:
            print("\n✅ Dashboard is properly personalized for this employee!")
            return True
        else:
            print("\n⚠️  Dashboard may not be fully personalized")
            return False
    else:
        print(f"❌ Dashboard API failed: {response.status_code}")
        return False

def main():
    """Test both employee accounts"""
    print("🚀 Detailed Dashboard Test for Employee Accounts")
    print("=" * 60)
    
    employees = [
        ("EMP001", "employee123"),
        ("delivery", "delivery123")
    ]
    
    results = []
    for username, password in employees:
        success = test_detailed_dashboard(username, password)
        results.append((username, success))
    
    print("\n" + "=" * 60)
    print("📊 DETAILED DASHBOARD TEST RESULTS")
    print("=" * 60)
    
    for username, success in results:
        status = "✅ FULLY PERSONALIZED" if success else "⚠️  NEEDS ATTENTION"
        print(f"{status} {username}")
    
    working_count = sum(1 for _, success in results if success)
    print(f"\n🎯 {working_count}/{len(results)} employee dashboards fully personalized")
    
    if working_count == len(results):
        print("\n✅ All employee dashboards are fully personalized!")
        print("The frontend should now display:")
        print("   - Correct employee names and IDs")
        print("   - Proper employment and application status")
        print("   - User-specific statistics and actions")
        print("   - Personalized next action recommendations")
    else:
        print("\n⚠️  Some dashboards need attention")

if __name__ == "__main__":
    main()
