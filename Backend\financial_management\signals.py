# financial_management/signals.py
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from orders.models import Order
from restaurant.models import Restaurant
from .models import RestaurantEarnings, CommissionStructure
from .delivery_agent_payments import DeliveryAgentCommission, DeliveryAgentEarnings
from decimal import Decimal
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


@receiver(post_save, sender=Order)
def create_restaurant_earnings(sender, instance, created, **kwargs):
    """Create restaurant earnings when order is completed"""
    if instance.status == 'delivered' and not hasattr(instance, 'earnings'):
        # Get or create commission structure for restaurant
        commission_structure, created = CommissionStructure.objects.get_or_create(
            restaurant=instance.restaurant,
            defaults={
                'commission_rate': Decimal('15.00'),
                'payment_processing_fee': Decimal('2.50'),
                'delivery_fee_share': Decimal('30.00'),
                'minimum_payout_amount': Decimal('50.00'),
                'payout_frequency': 'weekly'
            }
        )
        
        # Create earnings record
        earnings = RestaurantEarnings.objects.create(
            restaurant=instance.restaurant,
            order=instance,
            order_total=instance.total_amount,
            delivery_fee=instance.delivery_fee,
            tax_amount=instance.tax_amount
        )
        
        # Calculate earnings
        earnings.calculate_earnings()


@receiver(post_save, sender=Order)
def create_delivery_agent_earnings(sender, instance, created, **kwargs):
    """Create delivery agent earnings when order is delivered"""
    if (instance.status == 'delivered' and
        instance.delivery_agent and
        not hasattr(instance, 'delivery_earnings')):

        # Calculate delivery distance (simplified - you can enhance this)
        delivery_distance = 5.0  # Default 5km, implement actual calculation

        # Calculate delivery time (from assignment to delivery)
        delivery_time = 30  # Default 30 minutes, implement actual calculation
        if hasattr(instance, 'last_assigned_at') and instance.actual_delivery_time:
            time_diff = instance.actual_delivery_time - instance.last_assigned_at
            delivery_time = int(time_diff.total_seconds() / 60)

        # Create earnings record
        earnings = DeliveryAgentEarnings.objects.create(
            delivery_agent=instance.delivery_agent,
            order=instance,
            delivery_distance_km=Decimal(str(delivery_distance)),
            delivery_time_minutes=delivery_time,
            tips=Decimal('0.00')  # Tips can be added separately
        )

        # Calculate earnings
        earnings.calculate_earnings()

        logger.info(f"Created delivery earnings for agent {instance.delivery_agent.email}")


@receiver(post_save, sender=Restaurant)
def create_commission_structure(sender, instance, created, **kwargs):
    """Create default commission structure for new restaurants"""
    if created:
        CommissionStructure.objects.get_or_create(
            restaurant=instance,
            defaults={
                'commission_rate': Decimal('15.00'),
                'payment_processing_fee': Decimal('2.50'),
                'delivery_fee_share': Decimal('30.00'),
                'minimum_payout_amount': Decimal('50.00'),
                'payout_frequency': 'weekly'
            }
        )


@receiver(post_save, sender=User)
def create_delivery_agent_commission(sender, instance, created, **kwargs):
    """Create default commission structure for new delivery agents"""
    if created and instance.role == 'delivery_agent':
        DeliveryAgentCommission.objects.get_or_create(
            delivery_agent=instance,
            defaults={
                'base_delivery_fee': Decimal('5.00'),
                'distance_rate_per_km': Decimal('0.50'),
                'time_bonus_per_minute': Decimal('0.10'),
                'platform_commission_rate': Decimal('30.00'),
                'minimum_payout_amount': Decimal('25.00'),
                'payout_frequency': 'weekly',
                'performance_bonus_threshold': 20,
                'performance_bonus_amount': Decimal('10.00')
            }
        )
        logger.info(f"Created delivery commission structure for {instance.email}")
