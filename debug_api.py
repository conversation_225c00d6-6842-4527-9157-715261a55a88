#!/usr/bin/env python3
"""
Debug the API response
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def debug_api():
    # Login
    emp_login = {
        "user_name": "EMP001",
        "password": "employee123"
    }
    
    response = requests.post(f"{BASE_URL}/auth/login/", json=emp_login)
    print(f"Login Status: {response.status_code}")
    print(f"Login Response: {response.text}")
    
    if response.status_code == 200:
        token = response.json()['data']['access_token']
        headers = {"Authorization": f"Bearer {token}"}
        
        # Test orders endpoint
        print("\n" + "="*50)
        print("Testing orders endpoint...")

        response = requests.get(f"{BASE_URL}/delivery-agent/my-orders/", headers=headers)
        print(f"Orders Status: {response.status_code}")
        print(f"Orders Response: {response.text[:500]}...")

        if response.status_code == 200:
            orders_data = response.json()
            orders = orders_data['data']['orders']
            if orders:
                order_id = orders[0]['id']
                print(f"\nTesting accept order for order #{order_id}")

                accept_response = requests.post(
                    f"{BASE_URL}/delivery-agent/accept-order/",
                    json={"order_id": order_id},
                    headers=headers
                )
                print(f"Accept Status: {accept_response.status_code}")
                print(f"Accept Response: {accept_response.text}")
        else:
            print(f"Headers sent: {headers}")

if __name__ == "__main__":
    debug_api()
