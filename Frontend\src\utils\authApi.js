import axios from "axios";

// Base URL for the Afghan Sufra API
const BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000/api";

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 15000, // 15 seconds timeout
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add authentication token
apiClient.interceptors.request.use(
  (config) => {
    const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
    const token = user.access_token;

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Global flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

// Response interceptor to handle authentication errors
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // If already refreshing, queue this request
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return apiClient(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const user = JSON.parse(
          localStorage.getItem("afghanSofraUser") || "{}"
        );

        if (user.refresh_token) {
          const response = await apiClient.post("/auth/refresh/", {
            refresh: user.refresh_token,
          });

          if (response.data && response.data.access_token) {
            const updatedUser = { ...user, ...response.data };
            localStorage.setItem(
              "afghanSofraUser",
              JSON.stringify(updatedUser)
            );

            // Update the authorization header
            apiClient.defaults.headers.common[
              "Authorization"
            ] = `Bearer ${response.data.access_token}`;
            originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;

            processQueue(null, response.data.access_token);

            return apiClient(originalRequest);
          }
        }

        throw new Error("No refresh token available");
      } catch (refreshError) {
        processQueue(refreshError, null);
        localStorage.removeItem("afghanSofraUser");
        if (window.location.pathname !== "/login") {
          window.location.href = "/login";
        }
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

// Helper function to handle API responses
const handleApiResponse = (
  response,
  successMessage = "Operation successful"
) => {
  return {
    success: true,
    data: response.data,
    message: successMessage,
  };
};

// Helper function to handle API errors
const handleApiError = (error, defaultMessage = "Operation failed") => {
  console.error("API Error:", error);

  // Handle different types of errors
  let errorMessage = defaultMessage;
  let details = null;
  let errors = null;

  if (error.response) {
    // Server responded with error status
    const status = error.response.status;
    const data = error.response.data;

    if (status === 500) {
      errorMessage =
        "Server error. Please check your backend API configuration.";
      details = {
        status: 500,
        message: "Internal Server Error",
        suggestion:
          "Check backend logs and ensure the API endpoint is working correctly",
      };
    } else if (status === 404) {
      errorMessage = "API endpoint not found. Please check the URL.";
    } else if (status === 400) {
      // Enhanced 400 error handling for login issues
      if (data?.message) {
        errorMessage = data.message;
      } else if (data?.error) {
        errorMessage = data.error;
      } else if (data?.detail) {
        errorMessage = data.detail;
      } else if (data?.non_field_errors) {
        errorMessage = Array.isArray(data.non_field_errors)
          ? data.non_field_errors.join(", ")
          : data.non_field_errors;
      } else {
        errorMessage = "Invalid credentials or user not found";
      }

      // Pass through specific field errors from backend
      if (data?.errors) {
        errors = data.errors;
      }

      // Log the full error data for debugging
      console.error("❌ 400 Error Details:", data);
    } else {
      errorMessage =
        data?.message ||
        data?.error ||
        data?.detail ||
        `Server error (${status})`;
    }

    details = { status, data };
  } else if (error.request) {
    // Network error
    errorMessage = "Network error. Please check your internet connection.";
    details = { type: "network_error" };
  } else {
    // Other error
    errorMessage = error.message || defaultMessage;
  }

  return {
    success: false,
    error: errorMessage,
    status: error.response?.status,
    details: details,
    errors: errors, // Include specific field errors
  };
};

export const authApi = {
  /**
   * User Login
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.user_name - Username
   * @param {string} credentials.password - Password
   * @returns {Promise} API response
   */
  async login(credentials) {
    try {
      console.log("🔐 Attempting login for:", credentials.user_name);
      console.log("🔐 Login payload:", credentials);
      const response = await apiClient.post("/auth/login/", credentials);
      console.log("✅ Login successful:", response.data);

      return handleApiResponse(response, "Login successful");
    } catch (error) {
      console.error("❌ Login failed:", error);

      // Log detailed error information for debugging
      if (error.response) {
        console.error("❌ Response status:", error.response.status);
        console.error("❌ Response data:", error.response.data);
        console.error("❌ Response headers:", error.response.headers);
      }

      return handleApiError(error, "Login failed");
    }
  },

  /**
   * User Registration
   * @param {Object} userData - Registration data
   * @param {string} userData.name - Full name
   * @param {string} userData.user_name - Username
   * @param {string} userData.phone - Phone number
   * @param {string} userData.email - Email address
   * @param {string} userData.password - Password
   * @param {string} userData.role - User role (customer, restaurant, delivery_agent)
   * @returns {Promise} API response
   */
  async register(userData) {
    try {
      console.log("🚀 Starting registration for:", userData.email);

      // Ensure admin and delivery_agent roles are not allowed in registration
      if (userData.role === "admin") {
        return {
          success: false,
          error: "Admin role registration is not allowed",
        };
      }

      if (userData.role === "delivery_agent") {
        return {
          success: false,
          error:
            "Delivery agent accounts are created by admin only. Please contact support.",
        };
      }

      const response = await apiClient.post("/auth/register/", userData);
      console.log("✅ Registration successful, OTP sent to:", userData.email);

      return {
        ...handleApiResponse(
          response,
          `Registration successful! OTP sent to ${userData.email}`
        ),
        userEmail: userData.email,
        requiresVerification: true,
      };
    } catch (error) {
      console.error("❌ Registration failed:", error);
      return handleApiError(error, "Registration failed");
    }
  },

  /**
   * Verify Email with OTP
   * @param {Object} verificationData - Verification data
   * @param {string} verificationData.email - Email address
   * @param {string} verificationData.otp - OTP code
   * @returns {Promise} API response
   */
  async verifyEmail(verificationData) {
    try {
      console.log("🔐 Verifying email:", verificationData.email);
      const response = await apiClient.post(
        "/auth/verify-email/",
        verificationData
      );
      console.log("✅ Email verification successful");

      return handleApiResponse(response, "Email verified successfully");
    } catch (error) {
      console.error("❌ Email verification failed:", error);
      return handleApiError(error, "Email verification failed");
    }
  },

  /**
   * Resend Email Verification OTP
   * @param {Object} resendData - Resend data
   * @param {string} resendData.email - Email address
   * @returns {Promise} API response
   */
  async resendEmailVerification(resendData) {
    try {
      console.log("📧 Resending OTP to:", resendData.email);
      const response = await apiClient.post(
        "/auth/resend-verification/",
        resendData
      );
      console.log("✅ OTP resent successfully");

      return handleApiResponse(
        response,
        "Verification code sent to your email"
      );
    } catch (error) {
      console.error("❌ Failed to resend OTP:", error);
      return handleApiError(error, "Failed to resend verification code");
    }
  },

  /**
   * Request Password Reset (sends OTP to email)
   * @param {Object} resetData - Reset request data
   * @param {string} resetData.email - Email address
   * @returns {Promise} API response
   */
  async requestPasswordReset(resetData) {
    try {
      console.log("🔑 Requesting password reset for:", resetData.email);
      const response = await apiClient.post(
        "/auth/request-password-reset/",
        resetData
      );
      console.log("✅ Password reset OTP sent");

      return handleApiResponse(response, "Password reset OTP sent to email");
    } catch (error) {
      console.error("❌ Password reset request failed:", error);
      return handleApiError(error, "Failed to send password reset OTP");
    }
  },

  /**
   * Reset Password with OTP
   * @param {Object} resetData - Password reset data
   * @param {string} resetData.email - Email address
   * @param {string} resetData.old_password - Current password
   * @param {string} resetData.new_password - New password
   * @param {string} resetData.otp - OTP code
   * @returns {Promise} API response
   */
  async resetPassword(resetData) {
    try {
      console.log("🔑 Resetting password for:", resetData.email);
      const response = await apiClient.post(
        "/auth/verify-password-change/",
        resetData
      );
      console.log("✅ Password reset successful");

      return handleApiResponse(response, "Password reset successful");
    } catch (error) {
      console.error("❌ Password reset failed:", error);
      return handleApiError(error, "Password reset failed");
    }
  },

  /**
   * Change Password (from profile, without OTP)
   * @param {Object} changeData - Password change data
   * @param {string} changeData.email - Email address
   * @param {string} changeData.current_password - Current password
   * @param {string} changeData.new_password - New password
   * @returns {Promise} API response
   */
  async changePassword(changeData) {
    try {
      console.log("🔑 Changing password for:", changeData.email);
      const response = await apiClient.post(
        "/auth/change-password/",
        changeData
      );
      console.log("✅ Password changed successfully");

      return handleApiResponse(response, "Password changed successfully");
    } catch (error) {
      console.error("❌ Password change failed:", error);
      return handleApiError(error, "Password change failed");
    }
  },
};

export default authApi;
