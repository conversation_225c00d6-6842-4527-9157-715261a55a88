describe("Login Flow", () => {
  it("logs in a customer", () => {
    cy.visit("/login");
    cy.get('input[type="email"]').type("<EMAIL>");
    cy.get('input[type="password"]').type("password123");
    cy.get('button[type="submit"]').click();
    cy.url().should("include", "/"); // Should redirect to home or dashboard
    cy.contains("Welcome Back").should("not.exist"); // Login form should disappear
  });

  it("shows error on invalid login", () => {
    cy.visit("/login");
    cy.get('input[type="email"]').type("<EMAIL>");
    cy.get('input[type="password"]').type("wrongpassword");
    cy.get('button[type="submit"]').click();
    cy.contains("An unexpected error occurred").should("exist");
  });
});
