import React, { useState } from 'react';
import { Share2, Facebook, Twitter, Instagram, MessageCircle, Copy, Check } from 'lucide-react';
import { useSocial } from '../../context/SocialContext';
import Button from '../common/Button';

const ShareButton = ({ 
  type = 'restaurant', 
  data, 
  variant = 'outline', 
  size = 'medium',
  showLabel = true 
}) => {
  const { shareRestaurant, shareOrder } = useSocial();
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleShare = (platform) => {
    let shareData;
    
    if (type === 'restaurant') {
      shareData = shareRestaurant(data, platform);
    } else if (type === 'order') {
      shareData = shareOrder(data, platform);
    }

    if (platform === 'native' && navigator.share) {
      navigator.share({
        title: 'Afghan Sofra',
        text: shareData.text,
        url: shareData.url
      });
    } else if (platform === 'facebook') {
      window.open(
        `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareData.url)}`,
        '_blank',
        'width=600,height=400'
      );
    } else if (platform === 'twitter') {
      window.open(
        `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareData.text)}&url=${encodeURIComponent(shareData.url)}`,
        '_blank',
        'width=600,height=400'
      );
    } else if (platform === 'whatsapp') {
      window.open(
        `https://wa.me/?text=${encodeURIComponent(shareData.text + ' ' + shareData.url)}`,
        '_blank'
      );
    }

    setIsOpen(false);
  };

  const handleCopyLink = async () => {
    let shareData;
    
    if (type === 'restaurant') {
      shareData = shareRestaurant(data, 'copy');
    } else if (type === 'order') {
      shareData = shareOrder(data, 'copy');
    }

    try {
      await navigator.clipboard.writeText(shareData.url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = shareData.url;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const shareOptions = [
    {
      id: 'facebook',
      name: 'Facebook',
      icon: Facebook,
      color: 'text-blue-600',
      bgColor: 'hover:bg-blue-50'
    },
    {
      id: 'twitter',
      name: 'Twitter',
      icon: Twitter,
      color: 'text-blue-400',
      bgColor: 'hover:bg-blue-50'
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: MessageCircle,
      color: 'text-green-600',
      bgColor: 'hover:bg-green-50'
    },
    {
      id: 'instagram',
      name: 'Instagram',
      icon: Instagram,
      color: 'text-pink-600',
      bgColor: 'hover:bg-pink-50'
    }
  ];

  return (
    <div className="relative">
      <Button
        variant={variant}
        size={size}
        icon={<Share2 size={16} />}
        onClick={() => setIsOpen(!isOpen)}
        className="relative"
      >
        {showLabel && 'Share'}
      </Button>

      {isOpen && (
        <>
          {/* Overlay */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          ></div>
          
          {/* Share Menu */}
          <div className="absolute top-full right-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-20 animate-fade-in">
            <div className="p-4">
              <h3 className="font-medium text-gray-900 mb-3">Share this {type}</h3>
              
              {/* Native Share (if supported) */}
              {navigator.share && (
                <button
                  onClick={() => handleShare('native')}
                  className="w-full flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors mb-2"
                >
                  <Share2 size={20} className="text-gray-600 mr-3" />
                  <span className="text-sm font-medium">Share via...</span>
                </button>
              )}

              {/* Social Platforms */}
              <div className="space-y-1">
                {shareOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <button
                      key={option.id}
                      onClick={() => handleShare(option.id)}
                      className={`w-full flex items-center p-3 rounded-lg transition-colors ${option.bgColor}`}
                    >
                      <IconComponent size={20} className={`${option.color} mr-3`} />
                      <span className="text-sm font-medium text-gray-700">
                        Share on {option.name}
                      </span>
                    </button>
                  );
                })}
              </div>

              {/* Copy Link */}
              <div className="mt-3 pt-3 border-t border-gray-100">
                <button
                  onClick={handleCopyLink}
                  className="w-full flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {copied ? (
                    <Check size={20} className="text-green-600 mr-3" />
                  ) : (
                    <Copy size={20} className="text-gray-600 mr-3" />
                  )}
                  <span className="text-sm font-medium text-gray-700">
                    {copied ? 'Link copied!' : 'Copy link'}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ShareButton;
