import React, { useState, useEffect } from "react";
import {
  Users,
  User,
  Plus,
  Search,
  Filter,
  Edit,
  Eye,
  Trash2,
  User<PERSON>heck,
  UserX,
  Clock,
  DollarSign,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  Truck,
  CheckCircle,
  AlertCircle,
  XCircle,
  X,
  Activity,
  FileText,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Input from "../../components/common/Input";
import Badge from "../../components/common/Badge";
import { adminApi } from "../../services/adminApi";
import { useAuth } from "../../context/AuthContext";
import EmployeeCredentialsModal from "../../components/admin/EmployeeCredentialsModal";

const EmployeeManagement = () => {
  const { user } = useAuth();
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [availabilityFilter, setAvailabilityFilter] = useState("all");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showCredentialsModal, setShowCredentialsModal] = useState(false);
  const [newEmployeeData, setNewEmployeeData] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [employeeToDelete, setEmployeeToDelete] = useState(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [employeeToEdit, setEmployeeToEdit] = useState(null);

  useEffect(() => {
    loadEmployees();
  }, [statusFilter, availabilityFilter]);

  const loadEmployees = async () => {
    try {
      setLoading(true);
      const response = await adminApi.getDeliveryEmployees({
        employment_status: statusFilter,
        availability: availabilityFilter,
      });

      if (response.success) {
        setEmployees(response.data.employees);
      }
    } catch (error) {
      console.error("Failed to load employees:", error);
    } finally {
      setLoading(false);
    }
  };

  const filteredEmployees = employees.filter(
    (employee) =>
      employee.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.agent_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.phone_number.includes(searchTerm)
  );

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { variant: "success", label: "Active" },
      inactive: { variant: "secondary", label: "Inactive" },
      terminated: { variant: "danger", label: "Terminated" },
      suspended: { variant: "warning", label: "Suspended" },
    };

    const config = statusConfig[status] || {
      variant: "secondary",
      label: status,
    };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getAvailabilityBadge = (availability, isClocked) => {
    if (!isClocked) {
      return <Badge variant='secondary'>Off Duty</Badge>;
    }

    const availabilityConfig = {
      available: { variant: "success", label: "Available" },
      busy: { variant: "warning", label: "On Delivery" },
      break: { variant: "info", label: "On Break" },
      offline: { variant: "secondary", label: "Offline" },
    };

    const config = availabilityConfig[availability] || {
      variant: "secondary",
      label: availability,
    };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const handleCreateEmployee = () => {
    if (!user || user.role !== "admin") {
      alert(
        "Admin access required to create employees. Please login as admin."
      );
      return;
    }
    setShowCreateModal(true);
  };

  const handleViewEmployee = async (employee) => {
    setLoading(true);
    try {
      // Fetch detailed employee data from backend
      const result = await adminApi.getDeliveryEmployeeDetails(
        employee.agent_id
      );

      if (result.success) {
        setSelectedEmployee(result.data);
        setShowDetailModal(true);
      } else {
        console.error("Failed to fetch employee details:", result.error);
        // Fallback to basic employee data
        setSelectedEmployee(employee);
        setShowDetailModal(true);
      }
    } catch (error) {
      console.error("Error fetching employee details:", error);
      // Fallback to basic employee data
      setSelectedEmployee(employee);
      setShowDetailModal(true);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEmployee = (employee) => {
    setEmployeeToDelete(employee);
    setShowDeleteModal(true);
  };

  const confirmDeleteEmployee = async () => {
    if (!employeeToDelete) return;

    setDeleteLoading(true);
    try {
      const result = await adminApi.deleteDeliveryEmployee(
        employeeToDelete.agent_id
      );

      if (result.success) {
        // Remove employee from local state
        setEmployees(
          employees.filter((emp) => emp.agent_id !== employeeToDelete.agent_id)
        );
        setShowDeleteModal(false);
        setEmployeeToDelete(null);

        // Show success message (you can add a toast notification here)
        console.log("Employee deleted successfully:", result.message);
      } else {
        console.error("Failed to delete employee:", result.error);
        // Show error message (you can add a toast notification here)
      }
    } catch (error) {
      console.error("Error deleting employee:", error);
    } finally {
      setDeleteLoading(false);
    }
  };

  const cancelDeleteEmployee = () => {
    setShowDeleteModal(false);
    setEmployeeToDelete(null);
  };

  const handleEditEmployee = async (employee) => {
    setLoading(true);
    try {
      // Fetch detailed employee data for editing
      const result = await adminApi.getDeliveryEmployeeDetails(
        employee.agent_id
      );

      if (result.success) {
        setEmployeeToEdit(result.data);
        setShowEditModal(true);
      } else {
        console.error(
          "Failed to fetch employee details for editing:",
          result.error
        );
        // Fallback to basic employee data
        setEmployeeToEdit(employee);
        setShowEditModal(true);
      }
    } catch (error) {
      console.error("Error fetching employee details for editing:", error);
      // Fallback to basic employee data
      setEmployeeToEdit(employee);
      setShowEditModal(true);
    } finally {
      setLoading(false);
    }
  };

  const handleEditSuccess = (updatedEmployee) => {
    // Update the employee in the local state
    setEmployees(
      employees.map((emp) =>
        emp.agent_id === updatedEmployee.agent_id
          ? { ...emp, ...updatedEmployee }
          : emp
      )
    );
    setShowEditModal(false);
    setEmployeeToEdit(null);
    // Optionally reload all employees to get fresh data
    loadEmployees();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "AFN",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className='min-h-screen bg-gray-50 py-8'>
      <div className='max-w-7xl mx-auto px-4 sm:px-6 lg:px-8'>
        {/* Header */}
        <div className='mb-8'>
          <div className='flex items-center justify-between'>
            <div>
              <h1 className='text-3xl font-bold text-gray-900'>
                Employee Management
              </h1>
              <p className='text-gray-600 mt-1'>
                Manage delivery agent employees
              </p>
            </div>
            <Button
              variant='primary'
              onClick={handleCreateEmployee}
              icon={<Plus size={16} />}
            >
              Add New Employee
            </Button>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className='mb-6'>
          <div className='p-6'>
            <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
              <div>
                <Input
                  placeholder='Search employees...'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  icon={<Search size={16} />}
                />
              </div>
              <div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                >
                  <option value='all'>All Status</option>
                  <option value='active'>Active</option>
                  <option value='inactive'>Inactive</option>
                  <option value='suspended'>Suspended</option>
                  <option value='terminated'>Terminated</option>
                </select>
              </div>
              <div>
                <select
                  value={availabilityFilter}
                  onChange={(e) => setAvailabilityFilter(e.target.value)}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                >
                  <option value='all'>All Availability</option>
                  <option value='available'>Available</option>
                  <option value='busy'>On Delivery</option>
                  <option value='break'>On Break</option>
                  <option value='offline'>Offline</option>
                </select>
              </div>
              <div>
                <Button
                  variant='outline'
                  onClick={loadEmployees}
                  icon={<Filter size={16} />}
                  className='w-full'
                >
                  Refresh
                </Button>
              </div>
            </div>
          </div>
        </Card>

        {/* Employee Stats */}
        <div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-6'>
          <Card>
            <div className='p-6'>
              <div className='flex items-center'>
                <div className='p-3 bg-blue-100 rounded-lg'>
                  <Users className='h-6 w-6 text-blue-600' />
                </div>
                <div className='ml-4'>
                  <p className='text-sm font-medium text-gray-600'>
                    Total Employees
                  </p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {employees.length}
                  </p>
                </div>
              </div>
            </div>
          </Card>

          <Card>
            <div className='p-6'>
              <div className='flex items-center'>
                <div className='p-3 bg-green-100 rounded-lg'>
                  <UserCheck className='h-6 w-6 text-green-600' />
                </div>
                <div className='ml-4'>
                  <p className='text-sm font-medium text-gray-600'>Active</p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {
                      employees.filter((e) => e.employment_status === "active")
                        .length
                    }
                  </p>
                </div>
              </div>
            </div>
          </Card>

          <Card>
            <div className='p-6'>
              <div className='flex items-center'>
                <div className='p-3 bg-yellow-100 rounded-lg'>
                  <Clock className='h-6 w-6 text-yellow-600' />
                </div>
                <div className='ml-4'>
                  <p className='text-sm font-medium text-gray-600'>On Duty</p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {employees.filter((e) => e.is_clocked_in).length}
                  </p>
                </div>
              </div>
            </div>
          </Card>

          <Card>
            <div className='p-6'>
              <div className='flex items-center'>
                <div className='p-3 bg-purple-100 rounded-lg'>
                  <Briefcase className='h-6 w-6 text-purple-600' />
                </div>
                <div className='ml-4'>
                  <p className='text-sm font-medium text-gray-600'>Available</p>
                  <p className='text-2xl font-bold text-gray-900'>
                    {
                      employees.filter((e) => e.availability === "available")
                        .length
                    }
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Employee List */}
        <Card>
          <div className='p-6'>
            <h2 className='text-lg font-semibold text-gray-900 mb-4'>
              Employees
            </h2>

            {loading ? (
              <div className='text-center py-8'>
                <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto'></div>
                <p className='text-gray-500 mt-2'>Loading employees...</p>
              </div>
            ) : filteredEmployees.length === 0 ? (
              <div className='text-center py-8'>
                <Users className='h-12 w-12 text-gray-400 mx-auto mb-4' />
                <p className='text-gray-500'>No employees found</p>
              </div>
            ) : (
              <div className='overflow-x-auto'>
                <table className='min-w-full divide-y divide-gray-200'>
                  <thead className='bg-gray-50'>
                    <tr>
                      <th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Employee
                      </th>
                      <th className='hidden md:table-cell px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Status
                      </th>
                      <th className='hidden lg:table-cell px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Contact
                      </th>
                      <th className='px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className='bg-white divide-y divide-gray-200'>
                    {filteredEmployees.map((employee) => (
                      <tr key={employee.id} className='hover:bg-gray-50'>
                        {/* Employee Info - Always visible */}
                        <td className='px-4 py-4'>
                          <div className='flex items-center'>
                            <div className='flex-shrink-0 h-10 w-10'>
                              <div className='h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center'>
                                <span className='text-sm font-medium text-blue-600'>
                                  {employee.full_name.charAt(0)}
                                </span>
                              </div>
                            </div>
                            <div className='ml-3 min-w-0 flex-1'>
                              <div className='text-sm font-medium text-gray-900 truncate'>
                                {employee.full_name}
                              </div>
                              <div className='text-xs text-gray-500 truncate'>
                                {employee.agent_id}
                              </div>
                              {/* Mobile-only additional info */}
                              <div className='md:hidden mt-1 flex flex-wrap gap-1'>
                                {getStatusBadge(employee.employment_status)}
                                {getAvailabilityBadge(
                                  employee.availability,
                                  employee.is_clocked_in
                                )}
                              </div>
                            </div>
                          </div>
                        </td>

                        {/* Status - Hidden on mobile */}
                        <td className='hidden md:table-cell px-4 py-4 whitespace-nowrap'>
                          <div className='flex flex-col space-y-1'>
                            {getStatusBadge(employee.employment_status)}
                            {getAvailabilityBadge(
                              employee.availability,
                              employee.is_clocked_in
                            )}
                          </div>
                        </td>

                        {/* Contact - Hidden on mobile and tablet */}
                        <td className='hidden lg:table-cell px-4 py-4 whitespace-nowrap text-sm text-gray-900'>
                          <div>
                            <div className='font-medium'>
                              {employee.phone_number}
                            </div>
                            <div className='text-gray-500 text-xs'>
                              {employee.email}
                            </div>
                          </div>
                        </td>
                        {/* Actions */}
                        <td className='px-4 py-4 whitespace-nowrap text-sm font-medium'>
                          <div className='flex items-center justify-end space-x-1 sm:space-x-2'>
                            {/* View Button - Always visible */}
                            <button
                              onClick={() => handleViewEmployee(employee)}
                              disabled={loading}
                              className='inline-flex items-center px-2 py-1.5 text-xs font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 hover:border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed'
                              title='View Details'
                            >
                              {loading ? (
                                <div className='w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin sm:mr-1'></div>
                              ) : (
                                <Eye size={14} className='sm:mr-1' />
                              )}
                              <span className='hidden sm:inline'>
                                {loading ? "Loading..." : "View"}
                              </span>
                            </button>

                            {/* Edit Button - Hidden on mobile */}
                            <button
                              onClick={() => handleEditEmployee(employee)}
                              disabled={loading}
                              className='hidden sm:inline-flex items-center px-2 py-1.5 text-xs font-medium text-amber-700 bg-amber-50 border border-amber-200 rounded-md hover:bg-amber-100 hover:border-amber-300 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-1 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed'
                              title='Edit Employee'
                            >
                              <Edit size={14} className='mr-1' />
                              <span className='hidden md:inline'>Edit</span>
                            </button>

                            {/* Delete Button - Hidden on mobile */}
                            <button
                              onClick={() => handleDeleteEmployee(employee)}
                              className='hidden sm:inline-flex items-center px-2 py-1.5 text-xs font-medium text-red-700 bg-red-50 border border-red-200 rounded-md hover:bg-red-100 hover:border-red-300 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-1 transition-colors duration-200'
                              title='Delete Employee'
                            >
                              <Trash2 size={14} className='mr-1' />
                              <span className='hidden md:inline'>Delete</span>
                            </button>

                            {/* Mobile Menu Button - Only visible on mobile */}
                            <div className='sm:hidden relative'>
                              <button
                                onClick={() => {
                                  // Toggle mobile menu for this employee
                                  const menu = document.getElementById(
                                    `mobile-menu-${employee.id}`
                                  );
                                  menu.classList.toggle("hidden");
                                }}
                                className='inline-flex items-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-gray-50 border border-gray-200 rounded-md hover:bg-gray-100 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1 transition-colors duration-200'
                                title='More Actions'
                              >
                                <svg
                                  className='w-4 h-4'
                                  fill='currentColor'
                                  viewBox='0 0 20 20'
                                >
                                  <path d='M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z' />
                                </svg>
                              </button>

                              {/* Mobile Dropdown Menu */}
                              <div
                                id={`mobile-menu-${employee.id}`}
                                className='hidden absolute right-0 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-10'
                              >
                                <button
                                  onClick={() => {
                                    handleEditEmployee(employee);
                                    document
                                      .getElementById(
                                        `mobile-menu-${employee.id}`
                                      )
                                      .classList.add("hidden");
                                  }}
                                  disabled={loading}
                                  className='w-full text-left px-3 py-2 text-xs text-amber-700 hover:bg-amber-50 flex items-center disabled:opacity-50'
                                >
                                  <Edit size={12} className='mr-2' />
                                  Edit
                                </button>
                                <button
                                  onClick={() => {
                                    handleDeleteEmployee(employee);
                                    document
                                      .getElementById(
                                        `mobile-menu-${employee.id}`
                                      )
                                      .classList.add("hidden");
                                  }}
                                  className='w-full text-left px-3 py-2 text-xs text-red-700 hover:bg-red-50 flex items-center'
                                >
                                  <Trash2 size={12} className='mr-2' />
                                  Delete
                                </button>
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </Card>

        {/* Create Employee Modal */}
        {showCreateModal && (
          <CreateEmployeeModal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
            onSuccess={(employeeData) => {
              setShowCreateModal(false);
              setNewEmployeeData(employeeData);
              setShowCredentialsModal(true);
              loadEmployees();
            }}
          />
        )}

        {/* Employee Detail Modal */}
        {showDetailModal && selectedEmployee && (
          <EmployeeDetailModal
            employee={selectedEmployee}
            isOpen={showDetailModal}
            onClose={() => {
              setShowDetailModal(false);
              setSelectedEmployee(null);
            }}
          />
        )}

        {/* Edit Employee Modal */}
        {showEditModal && employeeToEdit && (
          <EditEmployeeModal
            employee={employeeToEdit}
            isOpen={showEditModal}
            onClose={() => {
              setShowEditModal(false);
              setEmployeeToEdit(null);
            }}
            onSuccess={handleEditSuccess}
          />
        )}

        {/* Employee Credentials Modal */}
        <EmployeeCredentialsModal
          isOpen={showCredentialsModal}
          onClose={() => {
            setShowCredentialsModal(false);
            setNewEmployeeData(null);
          }}
          employeeData={newEmployeeData}
        />

        {/* Delete Confirmation Modal */}
        {showDeleteModal && employeeToDelete && (
          <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
            <div className='bg-white rounded-lg p-6 max-w-md w-full mx-4'>
              <div className='flex items-center mb-4'>
                <div className='flex-shrink-0'>
                  <div className='w-10 h-10 bg-red-100 rounded-full flex items-center justify-center'>
                    <AlertCircle className='w-6 h-6 text-red-600' />
                  </div>
                </div>
                <div className='ml-4'>
                  <h3 className='text-lg font-medium text-gray-900'>
                    Delete Employee
                  </h3>
                  <p className='text-sm text-gray-500'>
                    This action cannot be undone
                  </p>
                </div>
              </div>

              <div className='mb-6'>
                <p className='text-gray-700'>
                  Are you sure you want to delete{" "}
                  <span className='font-semibold'>
                    {employeeToDelete.full_name}
                  </span>{" "}
                  ({employeeToDelete.agent_id})?
                </p>
                <div className='mt-3 p-3 bg-red-50 rounded-md'>
                  <div className='flex'>
                    <XCircle className='h-5 w-5 text-red-400' />
                    <div className='ml-3'>
                      <h4 className='text-sm font-medium text-red-800'>
                        This will permanently:
                      </h4>
                      <ul className='mt-2 text-sm text-red-700 list-disc list-inside'>
                        <li>Delete the employee profile</li>
                        <li>Remove their user account</li>
                        <li>Unassign any pending orders</li>
                        <li>Remove access to the system</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className='flex justify-end space-x-3'>
                <button
                  onClick={cancelDeleteEmployee}
                  disabled={deleteLoading}
                  className='px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50'
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDeleteEmployee}
                  disabled={deleteLoading}
                  className='px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed'
                >
                  {deleteLoading ? (
                    <div className='flex items-center'>
                      <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2'></div>
                      Deleting...
                    </div>
                  ) : (
                    "Delete Employee"
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Multi-step Employee Creation Modal (As per original plan)
const CreateEmployeeModal = ({ isOpen, onClose, onSuccess }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    // Step 1: Personal Information
    full_name: "",
    father_name: "",
    national_id: "",
    date_of_birth: "",
    gender: "male",
    marital_status: "single",

    // Step 2: Contact & Address
    phone_number: "",
    secondary_phone: "",
    email: "",
    province: "kabul",
    district: "",
    area: "",
    street_address: "",
    nearby_landmark: "",

    // Step 3: Vehicle Information
    vehicle_type: "motorcycle",
    vehicle_model: "",
    vehicle_year: new Date().getFullYear(),
    license_plate: "",
    vehicle_color: "",
    driving_license: "",

    // Step 4: Employment & Compensation
    hire_date: new Date().toISOString().split("T")[0],
    salary_type: "hybrid",
    base_salary: 15000,
    commission_per_delivery: 100,
    hourly_rate: 0,
    work_schedule: "full_time",
    shift_start_time: "09:00",
    shift_end_time: "18:00",
    working_days: [
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
    ],

    // Step 5: Additional Information
    bank_name: "",
    account_number: "",
    account_holder_name: "",
    mobile_wallet: "",
    emergency_contact: "",
    emergency_relation: "",

    // References (Important in Afghan culture)
    reference1_name: "",
    reference1_phone: "",
    reference1_relation: "",
    reference2_name: "",
    reference2_phone: "",
    reference2_relation: "",

    // Employee Status
    training_completed: false,
    documents_complete: false,
    background_check_completed: false,
    medical_clearance: false,

    admin_notes: "",
    performance_notes: "",
  });

  const steps = [
    { number: 1, title: "Personal Information", icon: <User size={20} /> },
    { number: 2, title: "Contact & Address", icon: <MapPin size={20} /> },
    { number: 3, title: "Vehicle Information", icon: <Truck size={20} /> },
    {
      number: 4,
      title: "Employment & Compensation",
      icon: <Briefcase size={20} />,
    },
    {
      number: 5,
      title: "Additional Information",
      icon: <DollarSign size={20} />,
    },
  ];

  const validateStep = (step) => {
    const requiredFields = {
      1: ["full_name", "father_name", "national_id", "date_of_birth"],
      2: ["phone_number", "province", "district", "area"],
      3: ["vehicle_type"],
      4: ["hire_date", "salary_type", "base_salary"],
      5: [
        "reference1_name",
        "reference1_phone",
        "reference1_relation",
        "reference2_name",
        "reference2_phone",
        "reference2_relation",
      ],
    };

    const stepFields = requiredFields[step] || [];
    const missingFields = stepFields.filter(
      (field) => !formData[field] || formData[field] === ""
    );

    // Additional validation for specific fields
    if (step === 1) {
      // Validate National ID format (basic check)
      if (formData.national_id && formData.national_id.length < 8) {
        alert("National ID must be at least 8 characters long");
        return false;
      }
    }

    if (step === 2) {
      // Validate phone number format
      if (
        formData.phone_number &&
        !formData.phone_number.match(/^\+?93[0-9]{9}$/)
      ) {
        alert("Please enter a valid Afghan phone number (e.g., +93701234567)");
        return false;
      }

      // Validate email if provided
      if (
        formData.email &&
        !formData.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
      ) {
        alert("Please enter a valid email address");
        return false;
      }
    }

    if (step === 4) {
      // Validate salary amounts
      if (formData.base_salary && parseInt(formData.base_salary) < 1000) {
        alert("Base salary must be at least 1,000 AFN");
        return false;
      }

      // Validate hire date is not in the future
      if (formData.hire_date && new Date(formData.hire_date) > new Date()) {
        alert("Hire date cannot be in the future");
        return false;
      }
    }

    if (step === 5) {
      // Validate reference phone numbers
      if (
        formData.reference1_phone &&
        !formData.reference1_phone.match(/^\+?93[0-9]{9}$/)
      ) {
        alert("Please enter a valid phone number for first reference");
        return false;
      }

      if (
        formData.reference2_phone &&
        !formData.reference2_phone.match(/^\+?93[0-9]{9}$/)
      ) {
        alert("Please enter a valid phone number for second reference");
        return false;
      }
    }

    if (missingFields.length > 0) {
      const fieldLabels = {
        full_name: "Full Name",
        father_name: "Father's Name",
        national_id: "National ID",
        date_of_birth: "Date of Birth",
        phone_number: "Phone Number",
        province: "Province",
        district: "District",
        area: "Area",
        vehicle_type: "Vehicle Type",
        hire_date: "Hire Date",
        salary_type: "Salary Type",
        base_salary: "Base Salary",
        reference1_name: "First Reference Name",
        reference1_phone: "First Reference Phone",
        reference1_relation: "First Reference Relationship",
        reference2_name: "Second Reference Name",
        reference2_phone: "Second Reference Phone",
        reference2_relation: "Second Reference Relationship",
      };

      const missingLabels = missingFields.map(
        (field) => fieldLabels[field] || field
      );
      alert(
        `Please fill in all required fields:\n• ${missingLabels.join("\n• ")}`
      );
      return false;
    }
    return true;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      if (currentStep < 5) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    // Validate all steps before submitting
    for (let step = 1; step <= 5; step++) {
      if (!validateStep(step)) {
        setCurrentStep(step);
        return;
      }
    }

    setLoading(true);
    try {
      // Prepare data for backend
      const submitData = {
        ...formData,
        // Convert working_days array to string format expected by backend
        working_days: formData.working_days.join(","),
        // Ensure numeric fields are properly formatted
        base_salary: parseInt(formData.base_salary) || 15000,
        commission_per_delivery:
          parseInt(formData.commission_per_delivery) || 100,
        hourly_rate: parseInt(formData.hourly_rate) || 0,
        vehicle_year:
          parseInt(formData.vehicle_year) || new Date().getFullYear(),
      };

      console.log("Submitting employee data:", submitData);

      const response = await adminApi.createDeliveryEmployee(submitData);

      console.log("Backend response:", response);

      if (response.success) {
        onSuccess(response.data);
        onClose();
      } else {
        alert(
          "Failed to create employee: " +
            (response.error?.message || response.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error creating employee:", error);

      let errorMessage = "Network error";

      if (error.response?.status === 403) {
        errorMessage = "Access denied. Admin privileges required.";
      } else if (error.response?.status === 401) {
        errorMessage = "Authentication required. Please login as admin.";
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      alert("Error creating employee: " + errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white rounded-lg w-full max-w-4xl max-h-[95vh] overflow-hidden flex flex-col'>
        {/* Header */}
        <div className='bg-primary-600 text-white p-6'>
          <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-bold'>
              Create New Delivery Agent Employee
            </h2>
            <Button
              variant='outline'
              onClick={onClose}
              className='text-white border-white hover:bg-white hover:text-primary-600'
            >
              ✕
            </Button>
          </div>

          {/* Progress Steps */}
          <div className='flex justify-between mt-6'>
            {steps.map((step) => (
              <div key={step.number} className='flex flex-col items-center'>
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    currentStep >= step.number
                      ? "bg-white text-primary-600"
                      : "bg-primary-500 text-white"
                  }`}
                >
                  {currentStep > step.number ? (
                    <CheckCircle size={20} />
                  ) : (
                    step.icon
                  )}
                </div>
                <span className='text-sm mt-2 text-center'>{step.title}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Form Content */}
        <div className='p-6 overflow-y-auto flex-grow'>
          {currentStep === 1 && (
            <PersonalInformationStep
              formData={formData}
              updateFormData={updateFormData}
            />
          )}
          {currentStep === 2 && (
            <ContactAddressStep
              formData={formData}
              updateFormData={updateFormData}
            />
          )}
          {currentStep === 3 && (
            <VehicleInformationStep
              formData={formData}
              updateFormData={updateFormData}
            />
          )}
          {currentStep === 4 && (
            <EmploymentCompensationStep
              formData={formData}
              updateFormData={updateFormData}
            />
          )}
          {currentStep === 5 && (
            <AdditionalInformationStep
              formData={formData}
              updateFormData={updateFormData}
            />
          )}
        </div>

        {/* Footer */}
        <div className='bg-gray-50 px-6 py-6 flex justify-between border-t flex-shrink-0'>
          <Button
            variant='outline'
            onClick={handlePrevious}
            disabled={currentStep === 1}
          >
            Previous
          </Button>

          <div className='flex gap-2'>
            {currentStep < 5 ? (
              <Button onClick={handleNext}>Next</Button>
            ) : (
              <Button onClick={handleSubmit} disabled={loading}>
                {loading ? "Creating Employee..." : "Create Employee"}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Step 1: Personal Information
const PersonalInformationStep = ({ formData, updateFormData }) => (
  <div className='space-y-6'>
    <h3 className='text-lg font-semibold text-gray-900 mb-4'>
      Personal Information
    </h3>
    <div className='grid grid-cols-2 gap-4'>
      <Input
        label='Full Name *'
        value={formData.full_name}
        onChange={(e) => updateFormData("full_name", e.target.value)}
        placeholder='Enter full name'
        required
      />
      <Input
        label="Father's Name *"
        value={formData.father_name}
        onChange={(e) => updateFormData("father_name", e.target.value)}
        placeholder="Enter father's name"
        required
      />
      <Input
        label='National ID (Tazkira) *'
        value={formData.national_id}
        onChange={(e) => updateFormData("national_id", e.target.value)}
        placeholder='Enter national ID number'
        required
      />
      <Input
        label='Date of Birth *'
        type='date'
        value={formData.date_of_birth}
        onChange={(e) => updateFormData("date_of_birth", e.target.value)}
        required
      />
      <div>
        <label className='block text-sm font-medium text-gray-700 mb-2'>
          Gender *
        </label>
        <select
          value={formData.gender}
          onChange={(e) => updateFormData("gender", e.target.value)}
          className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500'
        >
          <option value='male'>Male</option>
          <option value='female'>Female</option>
        </select>
      </div>
      <div>
        <label className='block text-sm font-medium text-gray-700 mb-2'>
          Marital Status
        </label>
        <select
          value={formData.marital_status}
          onChange={(e) => updateFormData("marital_status", e.target.value)}
          className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500'
        >
          <option value='single'>Single</option>
          <option value='married'>Married</option>
          <option value='divorced'>Divorced</option>
          <option value='widowed'>Widowed</option>
        </select>
      </div>
    </div>
  </div>
);

// Step 2: Contact & Address
const ContactAddressStep = ({ formData, updateFormData }) => (
  <div className='space-y-6'>
    <h3 className='text-lg font-semibold text-gray-900 mb-4'>
      Contact & Address Information
    </h3>
    <div className='grid grid-cols-2 gap-4'>
      <Input
        label='Phone Number *'
        value={formData.phone_number}
        onChange={(e) => {
          // Auto-format phone number
          let value = e.target.value.replace(/\D/g, ""); // Remove non-digits
          if (value.startsWith("93")) {
            value = "+" + value;
          } else if (value.startsWith("0")) {
            value = "+93" + value.substring(1);
          } else if (value.length > 0 && !value.startsWith("93")) {
            value = "+93" + value;
          }
          updateFormData("phone_number", value);
        }}
        placeholder='+93 70 123 4567'
        required
      />
      <Input
        label='Secondary Phone'
        value={formData.secondary_phone}
        onChange={(e) => updateFormData("secondary_phone", e.target.value)}
        placeholder='+93 78 123 4567'
      />
      <Input
        label='Email Address'
        type='email'
        value={formData.email}
        onChange={(e) => updateFormData("email", e.target.value)}
        placeholder='<EMAIL>'
      />
      <div>
        <label className='block text-sm font-medium text-gray-700 mb-2'>
          Province *
        </label>
        <select
          value={formData.province}
          onChange={(e) => updateFormData("province", e.target.value)}
          className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500'
        >
          <option value='kabul'>Kabul</option>
          <option value='herat'>Herat</option>
          <option value='kandahar'>Kandahar</option>
          <option value='balkh'>Balkh</option>
          <option value='nangarhar'>Nangarhar</option>
        </select>
      </div>
      <Input
        label='District *'
        value={formData.district}
        onChange={(e) => updateFormData("district", e.target.value)}
        placeholder='Enter district'
        required
      />
      <Input
        label='Area/Neighborhood *'
        value={formData.area}
        onChange={(e) => updateFormData("area", e.target.value)}
        placeholder='Enter area'
        required
      />
      <Input
        label='Street Address'
        value={formData.street_address}
        onChange={(e) => updateFormData("street_address", e.target.value)}
        placeholder='House number, street name'
      />
      <Input
        label='Nearby Landmark'
        value={formData.nearby_landmark}
        onChange={(e) => updateFormData("nearby_landmark", e.target.value)}
        placeholder='Mosque, school, market, etc.'
      />
    </div>
  </div>
);

// Step 3: Vehicle Information
const VehicleInformationStep = ({ formData, updateFormData }) => (
  <div className='space-y-6'>
    <h3 className='text-lg font-semibold text-gray-900 mb-4'>
      Vehicle Information
    </h3>
    <div className='grid grid-cols-2 gap-4'>
      <div>
        <label className='block text-sm font-medium text-gray-700 mb-2'>
          Vehicle Type *
        </label>
        <select
          value={formData.vehicle_type}
          onChange={(e) => updateFormData("vehicle_type", e.target.value)}
          className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500'
        >
          <option value='motorcycle'>Motorcycle</option>
          <option value='bicycle'>Bicycle</option>
          <option value='car'>Car</option>
          <option value='van'>Van</option>
          <option value='rickshaw'>Rickshaw</option>
        </select>
      </div>
      <Input
        label='Vehicle Model'
        value={formData.vehicle_model}
        onChange={(e) => updateFormData("vehicle_model", e.target.value)}
        placeholder='Honda, Yamaha, Toyota, etc.'
      />
      <Input
        label='Vehicle Year'
        type='number'
        value={formData.vehicle_year}
        onChange={(e) =>
          updateFormData("vehicle_year", parseInt(e.target.value))
        }
        placeholder='2020'
        min='1990'
        max={new Date().getFullYear() + 1}
      />
      <Input
        label='License Plate'
        value={formData.license_plate}
        onChange={(e) => updateFormData("license_plate", e.target.value)}
        placeholder='ABC-123'
      />
      <Input
        label='Vehicle Color'
        value={formData.vehicle_color}
        onChange={(e) => updateFormData("vehicle_color", e.target.value)}
        placeholder='Red, Blue, White, etc.'
      />
      <Input
        label='Driving License Number'
        value={formData.driving_license}
        onChange={(e) => updateFormData("driving_license", e.target.value)}
        placeholder='License number'
      />
    </div>
  </div>
);

// Step 4: Employment & Compensation
const EmploymentCompensationStep = ({ formData, updateFormData }) => (
  <div className='space-y-6'>
    <h3 className='text-lg font-semibold text-gray-900 mb-4'>
      Employment & Compensation
    </h3>
    <div className='grid grid-cols-2 gap-4'>
      <Input
        label='Hire Date *'
        type='date'
        value={formData.hire_date}
        onChange={(e) => updateFormData("hire_date", e.target.value)}
        required
      />
      <div>
        <label className='block text-sm font-medium text-gray-700 mb-2'>
          Salary Type *
        </label>
        <select
          value={formData.salary_type}
          onChange={(e) => updateFormData("salary_type", e.target.value)}
          className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500'
        >
          <option value='monthly'>Monthly Salary</option>
          <option value='hourly'>Hourly Rate</option>
          <option value='commission'>Commission Only</option>
          <option value='hybrid'>Hybrid (Salary + Commission)</option>
        </select>
      </div>
      <Input
        label='Base Salary (AFN) *'
        type='number'
        value={formData.base_salary}
        onChange={(e) =>
          updateFormData("base_salary", parseInt(e.target.value))
        }
        placeholder='15000'
        min='0'
        required
      />
      <Input
        label='Commission per Delivery (AFN)'
        type='number'
        value={formData.commission_per_delivery}
        onChange={(e) =>
          updateFormData("commission_per_delivery", parseInt(e.target.value))
        }
        placeholder='100'
        min='0'
      />
      <Input
        label='Hourly Rate (AFN)'
        type='number'
        value={formData.hourly_rate}
        onChange={(e) =>
          updateFormData("hourly_rate", parseInt(e.target.value))
        }
        placeholder='200'
        min='0'
      />
      <div>
        <label className='block text-sm font-medium text-gray-700 mb-2'>
          Work Schedule *
        </label>
        <select
          value={formData.work_schedule}
          onChange={(e) => updateFormData("work_schedule", e.target.value)}
          className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500'
        >
          <option value='full_time'>Full Time (8 hours)</option>
          <option value='part_time'>Part Time (4-6 hours)</option>
          <option value='flexible'>Flexible Hours</option>
          <option value='shift_based'>Shift Based</option>
        </select>
      </div>
      <Input
        label='Shift Start Time'
        type='time'
        value={formData.shift_start_time}
        onChange={(e) => updateFormData("shift_start_time", e.target.value)}
      />
      <Input
        label='Shift End Time'
        type='time'
        value={formData.shift_end_time}
        onChange={(e) => updateFormData("shift_end_time", e.target.value)}
      />
    </div>

    <div>
      <label className='block text-sm font-medium text-gray-700 mb-2'>
        Working Days
      </label>
      <div className='grid grid-cols-4 gap-2'>
        {[
          "monday",
          "tuesday",
          "wednesday",
          "thursday",
          "friday",
          "saturday",
          "sunday",
        ].map((day) => (
          <label key={day} className='flex items-center space-x-2'>
            <input
              type='checkbox'
              checked={formData.working_days.includes(day)}
              onChange={(e) => {
                const days = e.target.checked
                  ? [...formData.working_days, day]
                  : formData.working_days.filter((d) => d !== day);
                updateFormData("working_days", days);
              }}
              className='rounded border-gray-300 text-primary-600 focus:ring-primary-500'
            />
            <span className='text-sm capitalize'>{day}</span>
          </label>
        ))}
      </div>
    </div>
  </div>
);

// Step 5: Additional Information
const AdditionalInformationStep = ({ formData, updateFormData }) => (
  <div className='space-y-6'>
    <h3 className='text-lg font-semibold text-gray-900 mb-4'>
      Additional Information
    </h3>

    {/* Banking Information */}
    <div>
      <h4 className='text-md font-medium text-gray-800 mb-3'>
        Banking Information
      </h4>
      <div className='grid grid-cols-2 gap-4'>
        <Input
          label='Bank Name'
          value={formData.bank_name}
          onChange={(e) => updateFormData("bank_name", e.target.value)}
          placeholder='Afghanistan Bank, Azizi Bank, etc.'
        />
        <Input
          label='Account Number'
          value={formData.account_number}
          onChange={(e) => updateFormData("account_number", e.target.value)}
          placeholder='**********'
        />
        <Input
          label='Account Holder Name'
          value={formData.account_holder_name}
          onChange={(e) =>
            updateFormData("account_holder_name", e.target.value)
          }
          placeholder='Full name as per bank records'
        />
        <Input
          label='Mobile Wallet'
          value={formData.mobile_wallet}
          onChange={(e) => updateFormData("mobile_wallet", e.target.value)}
          placeholder='M-Paisa, Etisalat Money, etc.'
        />
      </div>
    </div>

    {/* References (Important in Afghan culture) */}
    <div>
      <h4 className='text-md font-medium text-gray-800 mb-3'>
        References (Required for Employment)
      </h4>
      <div className='space-y-4'>
        <div>
          <h5 className='text-sm font-medium text-gray-700 mb-2'>
            First Reference
          </h5>
          <div className='grid grid-cols-3 gap-4'>
            <Input
              label='Reference Name *'
              value={formData.reference1_name}
              onChange={(e) =>
                updateFormData("reference1_name", e.target.value)
              }
              placeholder='Full name'
              required
            />
            <Input
              label='Phone Number *'
              value={formData.reference1_phone}
              onChange={(e) => {
                // Auto-format phone number
                let value = e.target.value.replace(/\D/g, "");
                if (value.startsWith("93")) {
                  value = "+" + value;
                } else if (value.startsWith("0")) {
                  value = "+93" + value.substring(1);
                } else if (value.length > 0 && !value.startsWith("93")) {
                  value = "+93" + value;
                }
                updateFormData("reference1_phone", value);
              }}
              placeholder='+93 70 123 4567'
              required
            />
            <Input
              label='Relationship *'
              value={formData.reference1_relation}
              onChange={(e) =>
                updateFormData("reference1_relation", e.target.value)
              }
              placeholder='Friend, Colleague, etc.'
              required
            />
          </div>
        </div>

        <div>
          <h5 className='text-sm font-medium text-gray-700 mb-2'>
            Second Reference
          </h5>
          <div className='grid grid-cols-3 gap-4'>
            <Input
              label='Reference Name *'
              value={formData.reference2_name}
              onChange={(e) =>
                updateFormData("reference2_name", e.target.value)
              }
              placeholder='Full name'
              required
            />
            <Input
              label='Phone Number *'
              value={formData.reference2_phone}
              onChange={(e) => {
                // Auto-format phone number
                let value = e.target.value.replace(/\D/g, "");
                if (value.startsWith("93")) {
                  value = "+" + value;
                } else if (value.startsWith("0")) {
                  value = "+93" + value.substring(1);
                } else if (value.length > 0 && !value.startsWith("93")) {
                  value = "+93" + value;
                }
                updateFormData("reference2_phone", value);
              }}
              placeholder='+93 70 123 4567'
              required
            />
            <Input
              label='Relationship *'
              value={formData.reference2_relation}
              onChange={(e) =>
                updateFormData("reference2_relation", e.target.value)
              }
              placeholder='Friend, Colleague, etc.'
              required
            />
          </div>
        </div>
      </div>
    </div>

    {/* Emergency Contact */}
    <div>
      <h4 className='text-md font-medium text-gray-800 mb-3'>
        Emergency Contact
      </h4>
      <div className='grid grid-cols-2 gap-4'>
        <Input
          label='Emergency Contact Number'
          value={formData.emergency_contact}
          onChange={(e) => updateFormData("emergency_contact", e.target.value)}
          placeholder='+93 70 123 4567'
        />
        <Input
          label='Relationship'
          value={formData.emergency_relation}
          onChange={(e) => updateFormData("emergency_relation", e.target.value)}
          placeholder='Father, Brother, Wife, etc.'
        />
      </div>
    </div>

    {/* Employee Status Checkboxes */}
    <div>
      <h4 className='text-md font-medium text-gray-800 mb-3'>
        Employee Status
      </h4>
      <div className='grid grid-cols-2 gap-4'>
        <label className='flex items-center space-x-2'>
          <input
            type='checkbox'
            checked={formData.training_completed}
            onChange={(e) =>
              updateFormData("training_completed", e.target.checked)
            }
            className='rounded border-gray-300 text-primary-600 focus:ring-primary-500'
          />
          <span className='text-sm'>Training Completed</span>
        </label>

        <label className='flex items-center space-x-2'>
          <input
            type='checkbox'
            checked={formData.documents_complete}
            onChange={(e) =>
              updateFormData("documents_complete", e.target.checked)
            }
            className='rounded border-gray-300 text-primary-600 focus:ring-primary-500'
          />
          <span className='text-sm'>Documents Complete</span>
        </label>

        <label className='flex items-center space-x-2'>
          <input
            type='checkbox'
            checked={formData.background_check_completed}
            onChange={(e) =>
              updateFormData("background_check_completed", e.target.checked)
            }
            className='rounded border-gray-300 text-primary-600 focus:ring-primary-500'
          />
          <span className='text-sm'>Background Check Completed</span>
        </label>

        <label className='flex items-center space-x-2'>
          <input
            type='checkbox'
            checked={formData.medical_clearance}
            onChange={(e) =>
              updateFormData("medical_clearance", e.target.checked)
            }
            className='rounded border-gray-300 text-primary-600 focus:ring-primary-500'
          />
          <span className='text-sm'>Medical Clearance</span>
        </label>
      </div>
    </div>

    {/* Admin Notes */}
    <div>
      <h4 className='text-md font-medium text-gray-800 mb-3'>Admin Notes</h4>
      <div className='space-y-4'>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Admin Notes
          </label>
          <textarea
            value={formData.admin_notes}
            onChange={(e) => updateFormData("admin_notes", e.target.value)}
            placeholder='Any additional notes about the employee...'
            rows={3}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500'
          />
        </div>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-2'>
            Performance Notes
          </label>
          <textarea
            value={formData.performance_notes}
            onChange={(e) =>
              updateFormData("performance_notes", e.target.value)
            }
            placeholder='Performance expectations, goals, etc...'
            rows={3}
            className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500'
          />
        </div>
      </div>
    </div>
  </div>
);

const EmployeeDetailModal = ({ employee, isOpen, onClose }) => {
  if (!isOpen || !employee) return null;

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount) => {
    if (!amount) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "AFN",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      active: { variant: "success", label: "Active" },
      inactive: { variant: "secondary", label: "Inactive" },
      terminated: { variant: "danger", label: "Terminated" },
      suspended: { variant: "warning", label: "Suspended" },
    };

    const config = statusConfig[status] || {
      variant: "secondary",
      label: status,
    };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getAvailabilityBadge = (availability, isClockedIn) => {
    if (isClockedIn) {
      return <Badge variant='success'>🟢 Clocked In</Badge>;
    }

    const availabilityConfig = {
      available: { variant: "success", label: "Available" },
      busy: { variant: "warning", label: "Busy" },
      break: { variant: "info", label: "On Break" },
      offline: { variant: "secondary", label: "Offline" },
    };

    const config = availabilityConfig[availability] || {
      variant: "secondary",
      label: availability,
    };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white rounded-lg w-full max-w-6xl max-h-[95vh] overflow-hidden flex flex-col'>
        {/* Header */}
        <div className='flex items-center justify-between p-6 border-b border-gray-200'>
          <div className='flex items-center space-x-4'>
            <div className='h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center'>
              <span className='text-lg font-medium text-blue-600'>
                {employee.full_name?.charAt(0) || "E"}
              </span>
            </div>
            <div>
              <h2 className='text-2xl font-bold text-gray-900'>
                {employee.full_name}
              </h2>
              <p className='text-gray-500'>
                {employee.agent_id} • {employee.employee_number}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className='text-gray-400 hover:text-gray-600 p-2'
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className='flex-1 overflow-y-auto p-6'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {/* Personal Information */}
            <Card className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4 flex items-center'>
                <User className='h-5 w-5 mr-2 text-blue-600' />
                Personal Information
              </h3>
              <div className='space-y-3'>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Full Name
                    </label>
                    <p className='text-gray-900'>
                      {employee.full_name || "N/A"}
                    </p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Father\'s Name
                    </label>
                    <p className='text-gray-900'>
                      {employee.father_name || "N/A"}
                    </p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      National ID
                    </label>
                    <p className='text-gray-900 font-mono'>
                      {employee.national_id || "N/A"}
                    </p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Date of Birth
                    </label>
                    <p className='text-gray-900'>
                      {formatDate(employee.date_of_birth)}
                    </p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Gender
                    </label>
                    <p className='text-gray-900 capitalize'>
                      {employee.gender || "N/A"}
                    </p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Marital Status
                    </label>
                    <p className='text-gray-900 capitalize'>
                      {employee.marital_status || "N/A"}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            {/* Contact Information */}
            <Card className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4 flex items-center'>
                <Phone className='h-5 w-5 mr-2 text-green-600' />
                Contact Information
              </h3>
              <div className='space-y-3'>
                <div>
                  <label className='text-sm font-medium text-gray-500'>
                    Primary Phone
                  </label>
                  <p className='text-gray-900'>
                    {employee.phone_number || "N/A"}
                  </p>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>
                    Secondary Phone
                  </label>
                  <p className='text-gray-900'>
                    {employee.secondary_phone || "N/A"}
                  </p>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>
                    Email
                  </label>
                  <p className='text-gray-900'>{employee.email || "N/A"}</p>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>
                    Emergency Contact
                  </label>
                  <p className='text-gray-900'>
                    {employee.emergency_contact || "N/A"}
                  </p>
                </div>
                <div>
                  <label className='text-sm font-medium text-gray-500'>
                    Emergency Relation
                  </label>
                  <p className='text-gray-900'>
                    {employee.emergency_relation || "N/A"}
                  </p>
                </div>
              </div>
            </Card>

            {/* Employment Information */}
            <Card className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4 flex items-center'>
                <Briefcase className='h-5 w-5 mr-2 text-indigo-600' />
                Employment Information
              </h3>
              <div className='space-y-3'>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Employment Status
                    </label>
                    <div className='mt-1'>
                      {getStatusBadge(employee.employment_status)}
                    </div>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Availability
                    </label>
                    <div className='mt-1'>
                      {getAvailabilityBadge(
                        employee.availability,
                        employee.is_clocked_in
                      )}
                    </div>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Hire Date
                    </label>
                    <p className='text-gray-900'>
                      {formatDate(employee.hire_date)}
                    </p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Supervisor
                    </label>
                    <p className='text-gray-900'>
                      {employee.supervisor || "N/A"}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            {/* Performance Metrics */}
            <Card className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4 flex items-center'>
                <Activity className='h-5 w-5 mr-2 text-orange-600' />
                Performance Metrics
              </h3>
              <div className='space-y-3'>
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Total Deliveries
                    </label>
                    <p className='text-gray-900 text-lg font-semibold'>
                      {employee.total_deliveries || 0}
                    </p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Successful Deliveries
                    </label>
                    <p className='text-gray-900 text-lg font-semibold'>
                      {employee.successful_deliveries || 0}
                    </p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Completion Rate
                    </label>
                    <p className='text-gray-900'>
                      {employee.completion_rate || 0}%
                    </p>
                  </div>
                  <div>
                    <label className='text-sm font-medium text-gray-500'>
                      Rating
                    </label>
                    <p className='text-gray-900'>{employee.rating || 0}/5.0</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <div className='flex justify-end items-center p-6 border-t border-gray-200 bg-gray-50'>
          <div className='flex space-x-3'>
            <Button variant='outline' onClick={onClose}>
              Close
            </Button>
            <Button
              onClick={() => {
                onClose(); // Close detail modal first
                handleEditEmployee(employee);
              }}
              className='bg-amber-600 hover:bg-amber-700 text-white'
            >
              <Edit size={16} className='mr-2' />
              Edit Employee
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

const EditEmployeeModal = ({ employee, isOpen, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});

  // Initialize form data when employee changes
  useEffect(() => {
    if (employee) {
      setFormData({
        full_name: employee.full_name || "",
        father_name: employee.father_name || "",
        national_id: employee.national_id || "",
        date_of_birth: employee.date_of_birth || "",
        gender: employee.gender || "male",
        marital_status: employee.marital_status || "single",
        phone_number: employee.phone_number || "",
        secondary_phone: employee.secondary_phone || "",
        email: employee.email || "",
        province: employee.province || "kabul",
        district: employee.district || "",
        area: employee.area || "",
        street_address: employee.street_address || "",
        nearby_landmark: employee.nearby_landmark || "",
        vehicle_type: employee.vehicle_type || "motorcycle",
        vehicle_model: employee.vehicle_model || "",
        vehicle_year: employee.vehicle_year || "",
        license_plate: employee.license_plate || "",
        vehicle_color: employee.vehicle_color || "",
        driving_license: employee.driving_license || "",
        employment_status: employee.employment_status || "active",
        availability: employee.availability || "offline",
        hire_date: employee.hire_date || "",
        salary_type: employee.salary_type || "monthly",
        base_salary: employee.base_salary || "",
        commission_per_delivery: employee.commission_per_delivery || "",
        hourly_rate: employee.hourly_rate || "",
        work_schedule: employee.work_schedule || "full_time",
        shift_start_time: employee.shift_start_time || "08:00",
        shift_end_time: employee.shift_end_time || "17:00",
        working_days: employee.working_days || "monday_to_saturday",
        bank_name: employee.bank_name || "",
        account_number: employee.account_number || "",
        account_holder_name: employee.account_holder_name || "",
        mobile_wallet: employee.mobile_wallet || "",
        emergency_contact: employee.emergency_contact || "",
        emergency_relation: employee.emergency_relation || "",
        admin_notes: employee.admin_notes || "",
        performance_notes: employee.performance_notes || "",
      });
    }
  }, [employee]);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.full_name) newErrors.full_name = "Full name is required";
    if (!formData.phone_number)
      newErrors.phone_number = "Phone number is required";
    if (!formData.hire_date) newErrors.hire_date = "Hire date is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    try {
      const result = await adminApi.updateDeliveryEmployee(
        employee.agent_id,
        formData
      );

      if (result.success) {
        onSuccess(result.data);
      } else {
        setErrors({
          submit: result.error?.message || "Failed to update employee",
        });
      }
    } catch (error) {
      setErrors({ submit: "Failed to update employee. Please try again." });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !employee) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white rounded-lg w-full max-w-4xl max-h-[95vh] overflow-hidden flex flex-col'>
        {/* Header */}
        <div className='flex items-center justify-between p-6 border-b border-gray-200'>
          <div>
            <h2 className='text-2xl font-bold text-gray-900'>Edit Employee</h2>
            <p className='text-gray-500'>
              {employee.full_name} • {employee.agent_id}
            </p>
          </div>
          <button
            onClick={onClose}
            className='text-gray-400 hover:text-gray-600 p-2'
          >
            <X size={24} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className='flex-1 overflow-y-auto p-6'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
            {/* Personal Information */}
            <Card className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4 flex items-center'>
                <User className='h-5 w-5 mr-2 text-blue-600' />
                Personal Information
              </h3>
              <div className='space-y-4'>
                <Input
                  label='Full Name *'
                  value={formData.full_name}
                  onChange={(e) =>
                    handleInputChange("full_name", e.target.value)
                  }
                  error={errors.full_name}
                />
                <Input
                  label="Father's Name"
                  value={formData.father_name}
                  onChange={(e) =>
                    handleInputChange("father_name", e.target.value)
                  }
                />
                <Input
                  label='National ID'
                  value={formData.national_id}
                  onChange={(e) =>
                    handleInputChange("national_id", e.target.value)
                  }
                />
                <Input
                  label='Date of Birth'
                  type='date'
                  value={formData.date_of_birth}
                  onChange={(e) =>
                    handleInputChange("date_of_birth", e.target.value)
                  }
                />
                <div className='grid grid-cols-2 gap-4'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700 mb-1'>
                      Gender
                    </label>
                    <select
                      value={formData.gender}
                      onChange={(e) =>
                        handleInputChange("gender", e.target.value)
                      }
                      className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                    >
                      <option value='male'>Male</option>
                      <option value='female'>Female</option>
                    </select>
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700 mb-1'>
                      Marital Status
                    </label>
                    <select
                      value={formData.marital_status}
                      onChange={(e) =>
                        handleInputChange("marital_status", e.target.value)
                      }
                      className='w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500'
                    >
                      <option value='single'>Single</option>
                      <option value='married'>Married</option>
                      <option value='divorced'>Divorced</option>
                      <option value='widowed'>Widowed</option>
                    </select>
                  </div>
                </div>
              </div>
            </Card>

            {/* Contact Information */}
            <Card className='p-6'>
              <h3 className='text-lg font-semibold text-gray-900 mb-4 flex items-center'>
                <Phone className='h-5 w-5 mr-2 text-green-600' />
                Contact Information
              </h3>
              <div className='space-y-4'>
                <Input
                  label='Primary Phone *'
                  value={formData.phone_number}
                  onChange={(e) =>
                    handleInputChange("phone_number", e.target.value)
                  }
                  error={errors.phone_number}
                />
                <Input
                  label='Secondary Phone'
                  value={formData.secondary_phone}
                  onChange={(e) =>
                    handleInputChange("secondary_phone", e.target.value)
                  }
                />
                <Input
                  label='Email'
                  type='email'
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                />
                <Input
                  label='Emergency Contact'
                  value={formData.emergency_contact}
                  onChange={(e) =>
                    handleInputChange("emergency_contact", e.target.value)
                  }
                />
                <Input
                  label='Emergency Relation'
                  value={formData.emergency_relation}
                  onChange={(e) =>
                    handleInputChange("emergency_relation", e.target.value)
                  }
                />
              </div>
            </Card>
          </div>

          {/* Error Message */}
          {errors.submit && (
            <div className='mt-6 p-4 bg-red-50 border border-red-200 rounded-md'>
              <div className='flex'>
                <AlertCircle className='h-5 w-5 text-red-400' />
                <div className='ml-3'>
                  <p className='text-sm text-red-800'>{errors.submit}</p>
                </div>
              </div>
            </div>
          )}
        </form>

        {/* Footer */}
        <div className='flex justify-end items-center p-6 border-t border-gray-200 bg-gray-50'>
          <div className='flex space-x-3'>
            <Button variant='outline' onClick={onClose} disabled={loading}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={loading}
              className='bg-amber-600 hover:bg-amber-700 text-white'
            >
              {loading ? (
                <div className='flex items-center'>
                  <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2'></div>
                  Updating...
                </div>
              ) : (
                <>
                  <Edit size={16} className='mr-2' />
                  Update Employee
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeManagement;
