# 🎨 All Mermaid Diagrams - Afghan Sofra Cash Flow System

## 1. Complete COD Money Flow Overview

```mermaid
graph TD
    A[👥 Customer<br/>Pays $100 Cash] -->|Cash Payment| B[🚚 Delivery Agent<br/>Collects Cash]
    B -->|$100 - $4.90 = $95.10| C[🏦 Platform Account<br/>Cash Remittance]
    
    C -->|$75.02| D[🏪 Restaurant<br/>Net Earnings]
    C -->|$20.08| E[👨‍💼 Platform<br/>Commission & Fees]
    
    B -->|$4.90| F[🚚 Delivery Agent<br/>Keeps Earnings]
    
    subgraph "💰 Order Breakdown"
        G[📦 Food: $85.00]
        H[🚚 Delivery: $7.00]
        I[💸 Tax: $8.00]
        J[💡 Total: $100.00]
    end
    
    subgraph "🚚 Agent Responsibilities"
        K[💵 Collect $100 from customer]
        L[💰 Keep $4.90 delivery earnings]
        M[🏦 Remit $95.10 to platform]
        N[📱 Report collection via app]
    end
    
    subgraph "🏦 Platform Settlement"
        O[💰 Receive $95.10 from agent]
        P[🏪 Pay $75.02 to restaurant]
        Q[👨‍💼 Keep $20.08 commission]
        R[📊 Track all transactions]
    end
```

## 2. Revenue Distribution Pie Chart

```mermaid
pie title Revenue Distribution per $100 COD Order
    "Restaurant Net Earnings" : 75.02
    "Platform Commission & Fees" : 20.08
    "Delivery Agent Earnings" : 4.90
```

## 3. Detailed COD Collection Process

```mermaid
sequenceDiagram
    participant C as 👥 Customer
    participant DA as 🚚 Delivery Agent
    participant PA as 🏦 Platform Account
    participant R as 🏪 Restaurant
    participant A as 👨‍💼 Admin
    
    Note over C,A: Order Placement (No Payment Required)
    C->>R: Place order (COD selected)
    R->>R: Prepare order
    R->>DA: Order ready for pickup
    
    Note over C,A: Cash Collection Process
    DA->>DA: Pick up order from restaurant
    DA->>C: Deliver order
    C->>DA: Pay $100 cash
    DA->>PA: Report cash collection via app
    
    Note over C,A: Immediate Agent Earnings
    DA->>DA: Keep delivery earnings ($4.90)
    DA->>PA: Schedule remittance ($95.10)
    
    Note over C,A: Daily Settlement Process
    DA->>PA: End-of-day cash settlement
    PA->>PA: Verify cash received
    
    Note over C,A: Revenue Distribution
    PA->>PA: Calculate commissions
    PA->>R: Transfer $75.02 (Net earnings)
    PA->>A: Retain $20.08 (Platform revenue)
    
    Note over C,A: Settlement Confirmation
    PA->>DA: Confirm settlement received
    PA->>R: Process restaurant payout
    PA->>A: Update financial records
```

## 4. Daily Cash Settlement Flow

```mermaid
graph LR
    A[🌅 Start of Day<br/>Agent Balance: $0] -->|Multiple Deliveries| B[📦 Collect Orders<br/>Throughout Day]
    B --> C[💰 End of Day<br/>Total Collected: $500]
    
    C --> D{💵 Settlement Options}
    
    D -->|Option 1| E[🏦 Bank Deposit<br/>Remit to Platform]
    D -->|Option 2| F[📱 Digital Transfer<br/>Via Mobile App]
    D -->|Option 3| G[🏢 Office Drop-off<br/>Physical Cash]
    
    E --> H[✅ Settlement Complete<br/>Agent Keeps $49]
    F --> H
    G --> H
    
    H --> I[📊 Platform Distributes<br/>$451 to Restaurants]
    
    subgraph "💰 Daily Example (10 orders)"
        J[Total Collected: $1,000]
        K[Agent Earnings: $49]
        L[Platform Receives: $951]
        M[Restaurant Payments: $750.20]
        N[Platform Revenue: $200.80]
    end
```

## 5. Restaurant Earnings Calculation Flow

```mermaid
graph TD
    A[📦 Order Total: $85] --> B[💰 Gross Revenue: $92]
    A1[🚚 Delivery Fee: $7] --> B
    
    B --> C[📊 Calculate Deductions]
    
    C --> D[🏪 Commission: 15%<br/>$85 × 15% = $12.75]
    C --> E[💳 Processing: 2.5%<br/>$85 × 2.5% = $2.13]
    C --> F[🚚 Platform Share: 30%<br/>$7 × 30% = $2.10]
    
    D --> G[💰 Net Restaurant Earnings]
    E --> G
    F --> G
    
    G --> H[✅ Final Amount: $75.02]
    
    subgraph "📊 Calculation Details"
        I[Gross: $92.00]
        J[Less Commission: -$12.75]
        K[Less Processing: -$2.13]
        L[Less Platform Share: -$2.10]
        M[Net Earnings: $75.02]
    end
```

## 6. Delivery Agent Earnings Calculation

```mermaid
graph TD
    A[🚚 Delivery Assignment] --> B[📍 Calculate Distance<br/>5 km]
    A --> C[⏱️ Calculate Time<br/>25 minutes]
    
    B --> D[💵 Base Fee: $5.00]
    B --> E[📏 Distance Bonus<br/>5km × $0.50 = $2.50]
    C --> F[⚡ Time Bonus<br/>5min saved × $0.10 = $0.50]
    
    D --> G[💰 Gross Earnings: $8.00]
    E --> G
    F --> G
    
    G --> H[📊 Platform Commission<br/>30% of base+distance = $2.25]
    
    G --> I[💰 Net Agent Earnings: $5.75]
    H --> I
    
    subgraph "💡 Earnings Breakdown"
        J[Base Fee: $5.00]
        K[Distance: $2.50]
        L[Time Bonus: $0.50]
        M[Tips: $0.00]
        N[Gross: $8.00]
        O[Commission: -$2.25]
        P[Net: $5.75]
    end
```

## 7. Platform Revenue Streams

```mermaid
graph TD
    A[🏪 Restaurant Orders] --> B[📊 Platform Revenue Sources]
    
    B --> C[💰 Restaurant Commission<br/>15% of food orders]
    B --> D[💳 Payment Processing<br/>2.5% of transactions]
    B --> E[🚚 Delivery Fee Share<br/>30% of delivery fees]
    B --> F[📈 Additional Services<br/>Premium listings, ads]
    
    C --> G[💵 Monthly Revenue]
    D --> G
    E --> G
    F --> G
    
    G --> H[📊 Revenue Distribution]
    
    H --> I[🏦 Operating Costs<br/>40%]
    H --> J[💼 Business Development<br/>30%]
    H --> K[💰 Profit<br/>30%]
    
    subgraph "📈 Monthly Projections"
        L[Average Order: $25]
        M[Daily Orders: 100]
        N[Monthly Orders: 3,000]
        O[Monthly Revenue: $19,425]
    end
```

## 8. Cash Flow Risk Management

```mermaid
graph TD
    A[🚨 Risk Monitoring] --> B{💰 Cash Flow Risks}
    
    B --> C[🚚 Agent Settlement Delays]
    B --> D[💸 Cash Shortages]
    B --> E[🔍 Fraud Detection]
    B --> F[📊 Reconciliation Issues]
    
    C --> G[⚠️ Mitigation Strategies]
    D --> G
    E --> G
    F --> G
    
    G --> H[📱 Real-time Monitoring]
    G --> I[🔔 Automated Alerts]
    G --> J[📋 Daily Reconciliation]
    G --> K[🛡️ Insurance Coverage]
    
    H --> L[✅ Risk Minimized]
    I --> L
    J --> L
    K --> L
    
    subgraph "🎯 Success Metrics"
        M[Collection Rate: 99%+]
        N[Settlement Rate: 95%+]
        O[Fraud Rate: <1%]
        P[Reconciliation: 100%]
    end
```

## 9. System Architecture Overview

```mermaid
graph TB
    A[📱 Mobile Apps] --> B[🌐 API Gateway]
    
    B --> C[🏪 Restaurant Service]
    B --> D[🚚 Delivery Service]
    B --> E[💰 Financial Service]
    B --> F[👥 User Service]
    
    C --> G[🗄️ Restaurant DB]
    D --> H[🗄️ Delivery DB]
    E --> I[🗄️ Financial DB]
    F --> J[🗄️ User DB]
    
    E --> K[💳 Payment Gateway]
    E --> L[🏦 Banking API]
    E --> M[📊 Analytics Engine]
    
    subgraph "📱 Mobile Applications"
        N[Customer App]
        O[Restaurant App]
        P[Delivery Agent App]
        Q[Admin Dashboard]
    end
    
    subgraph "🔧 Backend Services"
        R[Order Management]
        S[Cash Collection]
        T[Settlement Processing]
        U[Financial Reporting]
    end
    
    subgraph "💾 Data Storage"
        V[Order Data]
        W[Transaction Data]
        X[Settlement Data]
        Y[Analytics Data]
    end
```

## 10. Database Entity Relationship

```mermaid
erDiagram
    USER ||--o{ ORDER : places
    USER ||--o{ COD_TRANSACTION : collects
    USER ||--o{ DAILY_SETTLEMENT : settles
    
    ORDER ||--|| COD_TRANSACTION : has
    ORDER ||--|| DELIVERY_EARNINGS : generates
    ORDER ||--|| RESTAURANT_EARNINGS : creates
    
    COD_TRANSACTION }o--|| DAILY_SETTLEMENT : included_in
    
    RESTAURANT ||--o{ ORDER : receives
    RESTAURANT ||--o{ RESTAURANT_EARNINGS : earns
    RESTAURANT ||--o{ RESTAURANT_PAYOUT : gets_paid
    
    USER {
        int id PK
        string email
        string role
        string full_name
        datetime created_at
    }
    
    ORDER {
        int id PK
        int customer_id FK
        int restaurant_id FK
        int delivery_agent_id FK
        decimal total_amount
        string status
        string payment_method
        datetime created_at
    }
    
    COD_TRANSACTION {
        int id PK
        int order_id FK
        int delivery_agent_id FK
        decimal cash_collected
        decimal agent_earnings
        decimal amount_to_remit
        datetime collection_time
        string settlement_status
    }
    
    DAILY_SETTLEMENT {
        int id PK
        int delivery_agent_id FK
        date settlement_date
        decimal total_collected
        decimal total_remittance
        string status
        datetime settled_at
    }
    
    RESTAURANT_EARNINGS {
        int id PK
        int restaurant_id FK
        int order_id FK
        decimal gross_earnings
        decimal commission
        decimal net_earnings
        datetime created_at
    }
```

## 11. Security & Compliance Flow

```mermaid
graph TD
    A[🔐 Security Measures] --> B[📱 Digital Verification]
    A --> C[📍 GPS Tracking]
    A --> D[📸 Photo Evidence]
    A --> E[🛡️ Insurance Coverage]
    
    B --> F[✅ Secure Collection]
    C --> F
    D --> F
    E --> F
    
    F --> G[🏦 Safe Settlement]
    
    subgraph "🔒 Security Features"
        H[Real-time GPS tracking]
        I[Photo verification]
        J[Digital signatures]
        K[Encrypted data]
        L[Audit trails]
    end
```

---

## 📋 How to Use These Diagrams

### **Copy & Paste Instructions:**
1. Copy any diagram code block above
2. Paste into any Mermaid-compatible editor:
   - GitHub README files
   - GitLab documentation
   - Notion pages
   - Mermaid Live Editor (https://mermaid.live)
   - VS Code with Mermaid extension

### **Customization Tips:**
- Change colors by adding `classDef` definitions
- Modify text and labels to match your branding
- Add or remove nodes as needed
- Adjust layout by changing graph direction (TD, LR, etc.)

### **Example Customization:**
```mermaid
graph TD
    A[Customer] --> B[Agent]
    
    classDef customer fill:#e1f5fe
    classDef agent fill:#f3e5f5
    classDef platform fill:#e8f5e8
    
    class A customer
    class B agent
```

**All diagrams are ready to use in your documentation! 🎨**
