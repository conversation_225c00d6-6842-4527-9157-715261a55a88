import React from "react";
import { cn } from "../../utils/cn";

const SkeletonBase = ({
  className,
  width,
  height,
  rounded = "md",
  animate = true,
  children,
  ...props
}) => {
  const baseClasses = cn(
    "bg-gray-200",
    animate && "animate-shimmer",
    rounded === "none" && "rounded-none",
    rounded === "sm" && "rounded-sm",
    rounded === "md" && "rounded-md",
    rounded === "lg" && "rounded-lg",
    rounded === "xl" && "rounded-xl",
    rounded === "full" && "rounded-full",
    className
  );

  const style = {
    width: width || undefined,
    height: height || undefined,
  };

  return (
    <div className={baseClasses} style={style} {...props}>
      {children}
    </div>
  );
};

// Skeleton Text Component
export const SkeletonText = ({
  lines = 1,
  className,
  lineHeight = "h-4",
  spacing = "space-y-2",
  lastLineWidth = "75%",
  ...props
}) => {
  if (lines === 1) {
    return (
      <SkeletonBase
        className={cn(lineHeight, "w-full", className)}
        {...props}
      />
    );
  }

  return (
    <div className={spacing}>
      {Array.from({ length: lines }).map((_, index) => (
        <SkeletonBase
          key={index}
          className={cn(
            lineHeight,
            index === lines - 1 ? `w-[${lastLineWidth}]` : "w-full",
            className
          )}
          {...props}
        />
      ))}
    </div>
  );
};

// Skeleton Circle Component
export const SkeletonCircle = ({ size = "w-10 h-10", className, ...props }) => {
  return (
    <SkeletonBase className={cn(size, "rounded-full", className)} {...props} />
  );
};

// Skeleton Rectangle Component
export const SkeletonRectangle = ({
  width = "w-full",
  height = "h-32",
  className,
  ...props
}) => {
  return <SkeletonBase className={cn(width, height, className)} {...props} />;
};

// Skeleton Button Component
export const SkeletonButton = ({
  size = "medium",
  variant = "primary",
  className,
  ...props
}) => {
  const sizeClasses = {
    small: "h-8 w-20",
    medium: "h-10 w-24",
    large: "h-12 w-32",
  };

  return (
    <SkeletonBase
      className={cn(sizeClasses[size], "rounded-md", className)}
      {...props}
    />
  );
};

// Skeleton Avatar Component
export const SkeletonAvatar = ({ size = "medium", className, ...props }) => {
  const sizeClasses = {
    small: "w-8 h-8",
    medium: "w-10 h-10",
    large: "w-12 h-12",
    xl: "w-16 h-16",
  };

  return (
    <SkeletonCircle size={sizeClasses[size]} className={className} {...props} />
  );
};

// Skeleton Card Component
export const SkeletonCard = ({
  children,
  className,
  padding = "p-4",
  ...props
}) => {
  return (
    <div
      className={cn(
        "bg-white border border-gray-200 rounded-lg",
        padding,
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export default SkeletonBase;
