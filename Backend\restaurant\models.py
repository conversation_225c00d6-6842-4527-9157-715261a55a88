

# restaurant/models.py
from django.db import models
from users.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.db.models import Avg

class Address(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='addresses')
    street = models.CharField(max_length=255)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=20)
    country = models.CharField(max_length=100)
    latitude = models.DecimalField(max_digits=9, decimal_places=6)
    longitude = models.DecimalField(max_digits=9, decimal_places=6)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['user', 'street', 'city', 'postal_code'],
                name='unique_user_address'
            )
        ]

    def __str__(self):
        return f"{self.street}, {self.city}, {self.country}"


# RESTAURANT BUSINESS DETAILS
class CuisineType(models.Model):
    """Cuisine types for restaurants"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="Icon class or emoji")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']
        indexes = [
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name

class RestaurantCategory(models.Model):
    """Restaurant categories (Fast Food, Fine Dining, etc.)"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    icon = models.CharField(max_length=50, blank=True, help_text="Icon class or emoji")
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']
        verbose_name_plural = "Restaurant Categories"
        indexes = [
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name


class Restaurant(models.Model):
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='restaurants')
    name = models.CharField(max_length=100)
    description = models.TextField()
    address = models.ForeignKey(
        Address,
        on_delete=models.CASCADE,
        related_name='restaurants'
    )
    delivery_fee=models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        help_text="Delivery fee in AFN"
    )
    contact_number = models.CharField(max_length=15)
    logo = models.ImageField(upload_to='restaurant_logos/', blank=True, null=True)
    banner = models.ImageField(upload_to='restaurant_banners/', blank=True, null=True)
    opening_time = models.TimeField()
    closing_time = models.TimeField()
    is_active = models.BooleanField(default=True)
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=0.0)
    is_verified = models.BooleanField(default=False)

    # Enhanced business details
    cuisine_types = models.ManyToManyField(CuisineType, blank=True, related_name='restaurants')
    restaurant_category = models.ForeignKey(
        RestaurantCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='restaurants'
    )
    min_order_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        help_text="Minimum order amount for delivery"
    )
    average_preparation_time = models.PositiveIntegerField(
        default=30,
        help_text="Average preparation time in minutes"
    )
    accepts_cash = models.BooleanField(default=True)
    accepts_card = models.BooleanField(default=True)
    accepts_online_payment = models.BooleanField(default=True)

    # Business hours for different days
    monday_opening = models.TimeField(null=True, blank=True)
    monday_closing = models.TimeField(null=True, blank=True)
    tuesday_opening = models.TimeField(null=True, blank=True)
    tuesday_closing = models.TimeField(null=True, blank=True)
    wednesday_opening = models.TimeField(null=True, blank=True)
    wednesday_closing = models.TimeField(null=True, blank=True)
    thursday_opening = models.TimeField(null=True, blank=True)
    thursday_closing = models.TimeField(null=True, blank=True)
    friday_opening = models.TimeField(null=True, blank=True)
    friday_closing = models.TimeField(null=True, blank=True)
    saturday_opening = models.TimeField(null=True, blank=True)
    saturday_closing = models.TimeField(null=True, blank=True)
    sunday_opening = models.TimeField(null=True, blank=True)
    sunday_closing = models.TimeField(null=True, blank=True)

    # Additional business info
    website = models.URLField(blank=True)
    facebook_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        constraints = [
            # Prevent same owner from creating duplicate restaurants
            models.UniqueConstraint(
                fields=['owner', 'name'],
                name='unique_restaurant_per_owner',
                condition=models.Q(is_active=True)
            ),
            
            # Alternative: Allow same name in different cities
            # models.UniqueConstraint(
            #     fields=['name', 'address__city'],
            #     name='unique_restaurant_name_city'
            # )
        ]

    def __str__(self):
        return self.name

    def update_rating(self):
        """Update restaurant rating based on reviews"""
        avg_rating = self.reviews.filter(is_approved=True).aggregate(
            avg_rating=Avg('rating')
        )['avg_rating']

        if avg_rating is not None:
            self.rating = round(avg_rating, 2)
            self.save(update_fields=['rating'])

        return self.rating

    def get_review_stats(self):
        """Get review statistics"""
        reviews = self.reviews.filter(is_approved=True)
        total_reviews = reviews.count()

        if total_reviews == 0:
            return {
                'total_reviews': 0,
                'average_rating': 0,
                'rating_breakdown': {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}
            }

        avg_rating = reviews.aggregate(avg_rating=Avg('rating'))['avg_rating']

        # Rating breakdown
        rating_breakdown = {}
        for i in range(1, 6):
            rating_breakdown[i] = reviews.filter(rating=i).count()

        return {
            'total_reviews': total_reviews,
            'average_rating': round(avg_rating, 2) if avg_rating else 0,
            'rating_breakdown': rating_breakdown
        }

class MenuCategory(models.Model):
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name='categories')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['restaurant', 'name'],
                name='unique_category_per_restaurant'
            )
        ]

    def __str__(self):
        return f"{self.restaurant.name} - {self.name}"

class MenuItem(models.Model):
    category = models.ForeignKey(MenuCategory, on_delete=models.CASCADE, related_name='items')
    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    preparation_time = models.PositiveIntegerField()  # in minutes
    is_vegetarian = models.BooleanField(default=False)
    image = models.ImageField(upload_to='menu_items/', blank=True, null=True)
    is_available = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['category', 'name'],
                name='unique_item_per_category'
            )
        ]


# REVIEWS & RATINGS SYSTEM
class RestaurantReview(models.Model):
    """Customer reviews for restaurants"""
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name='reviews')
    customer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='restaurant_reviews')
    order = models.ForeignKey('orders.Order', on_delete=models.SET_NULL, null=True, blank=True)

    # Overall rating (1-5 stars)
    rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    title = models.CharField(max_length=200, blank=True)
    comment = models.TextField()

    # Detailed rating breakdown
    food_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True
    )
    service_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True
    )
    delivery_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True
    )

    # Review status
    is_verified = models.BooleanField(default=False)  # Verified purchase
    is_approved = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('restaurant', 'customer', 'order')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['restaurant', '-created_at']),
            models.Index(fields=['rating']),
            models.Index(fields=['is_approved']),
        ]

    def __str__(self):
        return f"{self.customer.name} - {self.restaurant.name} ({self.rating}★)"

class ReviewResponse(models.Model):
    """Restaurant owner responses to reviews"""
    review = models.OneToOneField(RestaurantReview, on_delete=models.CASCADE, related_name='response')
    restaurant_owner = models.ForeignKey(User, on_delete=models.CASCADE)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Response to {self.review}"


# MENU ITEM VARIANTS & CUSTOMIZATIONS
class MenuItemVariant(models.Model):
    """Menu item variants (sizes, options)"""
    menu_item = models.ForeignKey(MenuItem, on_delete=models.CASCADE, related_name='variants')
    name = models.CharField(max_length=100)  # Small, Medium, Large, etc.
    price_adjustment = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0,
        help_text="Price adjustment from base menu item price (can be negative)"
    )
    is_default = models.BooleanField(default=False)
    is_available = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('menu_item', 'name')
        indexes = [
            models.Index(fields=['menu_item', 'is_available']),
            models.Index(fields=['is_default']),
        ]

    def __str__(self):
        return f"{self.menu_item.name} - {self.name}"

    @property
    def final_price(self):
        """Calculate final price including adjustment"""
        return self.menu_item.price + self.price_adjustment

class MenuItemAddon(models.Model):
    """Menu item add-ons and customizations"""
    ADDON_TYPES = [
        ('required', 'Required'),
        ('optional', 'Optional'),
        ('extra', 'Extra'),
    ]

    menu_item = models.ForeignKey(MenuItem, on_delete=models.CASCADE, related_name='addons')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    price = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)]
    )
    addon_type = models.CharField(max_length=20, choices=ADDON_TYPES, default='optional')
    is_available = models.BooleanField(default=True)
    max_quantity = models.PositiveIntegerField(default=1, help_text="Maximum quantity allowed")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('menu_item', 'name')
        indexes = [
            models.Index(fields=['menu_item', 'addon_type']),
            models.Index(fields=['is_available']),
        ]

    def __str__(self):
        return f"{self.menu_item.name} - {self.name} (+${self.price})"

class MenuItemCustomizationGroup(models.Model):
    """Groups for organizing customizations (e.g., 'Size', 'Toppings', 'Spice Level')"""
    menu_item = models.ForeignKey(MenuItem, on_delete=models.CASCADE, related_name='customization_groups')
    name = models.CharField(max_length=100)  # Size, Toppings, Spice Level, etc.
    description = models.TextField(blank=True)
    is_required = models.BooleanField(default=False)
    min_selections = models.PositiveIntegerField(default=0)
    max_selections = models.PositiveIntegerField(default=1)
    display_order = models.PositiveIntegerField(default=0)

    class Meta:
        unique_together = ('menu_item', 'name')
        ordering = ['display_order', 'name']
        indexes = [
            models.Index(fields=['menu_item', 'display_order']),
        ]

    def __str__(self):
        return f"{self.menu_item.name} - {self.name}"

class MenuItemCustomizationOption(models.Model):
    """Individual customization options within a group"""
    group = models.ForeignKey(MenuItemCustomizationGroup, on_delete=models.CASCADE, related_name='options')
    name = models.CharField(max_length=100)
    price_adjustment = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0,
        help_text="Price adjustment for this option"
    )
    is_default = models.BooleanField(default=False)
    is_available = models.BooleanField(default=True)
    display_order = models.PositiveIntegerField(default=0)

    class Meta:
        unique_together = ('group', 'name')
        ordering = ['display_order', 'name']
        indexes = [
            models.Index(fields=['group', 'display_order']),
            models.Index(fields=['is_available']),
        ]

    def __str__(self):
        return f"{self.group.name}: {self.name}"


class ServiceArea(models.Model):
    """Service areas for restaurant delivery"""
    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name='service_areas')
    area_name = models.CharField(max_length=200)
    postal_codes = models.TextField(help_text="Comma-separated postal codes")
    delivery_fee = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        help_text="Delivery fee for this area"
    )
    min_order_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        default=0,
        help_text="Minimum order amount for this area"
    )
    estimated_delivery_time = models.PositiveIntegerField(
        help_text="Estimated delivery time in minutes"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('restaurant', 'area_name')
        indexes = [
            models.Index(fields=['restaurant', 'is_active']),
        ]

    def __str__(self):
        return f"{self.restaurant.name} - {self.area_name}"

    def get_postal_codes_list(self):
        """Return postal codes as a list"""
        return [code.strip() for code in self.postal_codes.split(',') if code.strip()]


# PROMOTIONS & DISCOUNTS SYSTEM
class Promotion(models.Model):
    """Restaurant promotions and discounts"""
    PROMOTION_TYPES = [
        ('percentage', 'Percentage Discount'),
        ('fixed_amount', 'Fixed Amount Discount'),
        ('free_delivery', 'Free Delivery'),
        ('buy_one_get_one', 'Buy One Get One'),
        ('minimum_order', 'Minimum Order Discount'),
    ]

    DISCOUNT_APPLIES_TO = [
        ('order_total', 'Order Total'),
        ('delivery_fee', 'Delivery Fee'),
        ('specific_items', 'Specific Items'),
        ('category', 'Menu Category'),
    ]

    restaurant = models.ForeignKey(Restaurant, on_delete=models.CASCADE, related_name='promotions')
    name = models.CharField(max_length=200)
    description = models.TextField()
    promotion_type = models.CharField(max_length=20, choices=PROMOTION_TYPES)
    discount_applies_to = models.CharField(max_length=20, choices=DISCOUNT_APPLIES_TO, default='order_total')

    # Discount values
    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Percentage discount (0-100)"
    )
    discount_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        help_text="Fixed discount amount"
    )

    # Conditions
    min_order_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        help_text="Minimum order amount to apply promotion"
    )
    max_discount_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[MinValueValidator(0)],
        help_text="Maximum discount amount (for percentage discounts)"
    )

    # Usage limits
    usage_limit = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Maximum number of times this promotion can be used"
    )
    usage_limit_per_customer = models.PositiveIntegerField(
        default=1,
        help_text="Maximum uses per customer"
    )
    current_usage_count = models.PositiveIntegerField(default=0)

    # Time constraints
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()

    # Days of week (for recurring promotions)
    valid_on_monday = models.BooleanField(default=True)
    valid_on_tuesday = models.BooleanField(default=True)
    valid_on_wednesday = models.BooleanField(default=True)
    valid_on_thursday = models.BooleanField(default=True)
    valid_on_friday = models.BooleanField(default=True)
    valid_on_saturday = models.BooleanField(default=True)
    valid_on_sunday = models.BooleanField(default=True)

    # Time of day constraints
    valid_from_time = models.TimeField(null=True, blank=True)
    valid_to_time = models.TimeField(null=True, blank=True)

    # Status
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['restaurant', 'is_active']),
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['is_featured']),
        ]

    def __str__(self):
        return f"{self.restaurant.name} - {self.name}"

    def is_valid_now(self):
        """Check if promotion is currently valid"""
        from django.utils import timezone
        now = timezone.now()

        # Check date range
        if not (self.start_date <= now <= self.end_date):
            return False

        # Check if active
        if not self.is_active:
            return False

        # Check usage limit
        if self.usage_limit and self.current_usage_count >= self.usage_limit:
            return False

        # Check day of week
        weekday = now.weekday()  # 0=Monday, 6=Sunday
        day_checks = [
            self.valid_on_monday, self.valid_on_tuesday, self.valid_on_wednesday,
            self.valid_on_thursday, self.valid_on_friday, self.valid_on_saturday, self.valid_on_sunday
        ]
        if not day_checks[weekday]:
            return False

        # Check time of day
        if self.valid_from_time and self.valid_to_time:
            current_time = now.time()
            if not (self.valid_from_time <= current_time <= self.valid_to_time):
                return False

        return True

    def calculate_discount(self, order_total, delivery_fee=0):
        """Calculate discount amount for given order"""
        if not self.is_valid_now():
            return 0

        if order_total < self.min_order_amount:
            return 0

        discount = 0

        if self.promotion_type == 'percentage':
            if self.discount_applies_to == 'order_total':
                discount = (order_total * self.discount_percentage) / 100
            elif self.discount_applies_to == 'delivery_fee':
                discount = (delivery_fee * self.discount_percentage) / 100

        elif self.promotion_type == 'fixed_amount':
            discount = self.discount_amount or 0

        elif self.promotion_type == 'free_delivery':
            discount = delivery_fee

        # Apply maximum discount limit
        if self.max_discount_amount:
            discount = min(discount, self.max_discount_amount)

        return round(discount, 2)

class PromotionCode(models.Model):
    """Coupon codes for promotions"""
    promotion = models.ForeignKey(Promotion, on_delete=models.CASCADE, related_name='codes')
    code = models.CharField(max_length=50, unique=True)
    usage_count = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.code} - {self.promotion.name}"

    def can_be_used(self, customer=None):
        """Check if code can be used"""
        if not self.is_active:
            return False

        if not self.promotion.is_valid_now():
            return False

        # Check customer usage limit
        if customer and self.promotion.usage_limit_per_customer:
            from orders.models import Order
            customer_usage = Order.objects.filter(
                customer=customer,
                promotion_code=self,
                status__in=['confirmed', 'preparing', 'ready', 'delivered']
            ).count()

            if customer_usage >= self.promotion.usage_limit_per_customer:
                return False

        return True

    def __str__(self):
        return f"{self.category.restaurant.name} - {self.name}"