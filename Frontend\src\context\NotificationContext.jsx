import { createContext, useContext, useState, useEffect, useRef } from "react";
import { useAuth } from "./AuthContext";
import { API_BASE_URL } from "../config/api";
// Import socket.io client for real-time notifications - DISABLED
// import { io } from "socket.io-client";

const NotificationContext = createContext(null);

export const useNotifications = () => useContext(NotificationContext);

export const NotificationProvider = ({ children }) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [lastCheckedTime, setLastCheckedTime] = useState(() => {
    const saved = localStorage.getItem("afghanSofraLastCheckedTime");
    return saved ? new Date(saved) : new Date();
  });

  // Track active orders that are being tracked
  const [trackedOrders, setTrackedOrders] = useState({});

  // Socket connection state and real-time features
  const [isConnected, setIsConnected] = useState(false);
  const [socket, setSocket] = useState(null);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [browserNotificationsEnabled, setBrowserNotificationsEnabled] =
    useState(false);
  const audioRef = useRef(null);
  const socketRef = useRef(null);

  // Initialize audio for notifications
  useEffect(() => {
    audioRef.current = new Audio("/notification-sound.mp3");
    audioRef.current.preload = "auto";
  }, []);

  // Request browser notification permission
  useEffect(() => {
    if ("Notification" in window) {
      if (Notification.permission === "granted") {
        setBrowserNotificationsEnabled(true);
      } else if (Notification.permission !== "denied") {
        Notification.requestPermission().then((permission) => {
          setBrowserNotificationsEnabled(permission === "granted");
        });
      }
    }
  }, []);

  // Socket.IO connection setup - DISABLED
  useEffect(() => {
    if (!user) return;

    // Socket connections disabled to prevent WebSocket errors
    console.log("Socket connections disabled");
    setIsConnected(false);

    return () => {
      // Cleanup disabled
    };
  }, [user]);

  // Setup socket event listeners
  const setupSocketListeners = (socket) => {
    // Listen for order status updates
    socket.on("orderStatusUpdate", (data) => {
      handleOrderStatusUpdate(data);
    });

    // Listen for new orders (for restaurant owners)
    socket.on("newOrder", (data) => {
      handleNewOrder(data);
    });

    // Listen for delivery agent location updates
    socket.on("agentLocationUpdate", (data) => {
      handleAgentLocationUpdate(data);
    });

    // Listen for order assignments (for delivery agents)
    socket.on("orderAssigned", (data) => {
      handleOrderAssignment(data);
    });
  };

  // Simulate polling for new orders
  useEffect(() => {
    if (!user) return;

    // Check if user is a restaurant owner
    if (user.role !== "restaurant") return;

    // Save last checked time to localStorage
    localStorage.setItem(
      "afghanSofraLastCheckedTime",
      lastCheckedTime.toISOString()
    );

    // Function to check for new orders
    const checkForNewOrders = async () => {
      // Only check for orders if user is authenticated and has the right role
      if (!user || !user.access_token) {
        console.log(
          "🚫 NotificationContext: User not authenticated, skipping order check"
        );
        return;
      }

      // Only check for restaurant and delivery users (customers don't need new order notifications)
      if (user.role !== "restaurant" && user.role !== "delivery") {
        console.log(
          "🚫 NotificationContext: User role doesn't need order notifications:",
          user.role
        );
        return;
      }

      // Fetch orders from API
      try {
        console.log(
          "🔔 NotificationContext: Checking for new orders for",
          user.role,
          user.name
        );
        const response = await fetch(`${API_BASE_URL}/order/orders/`, {
          headers: {
            Authorization: `Bearer ${user.access_token}`,
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          console.log(
            "🚫 NotificationContext: Failed to fetch orders:",
            response.status
          );
          return;
        }

        const allOrders = await response.json();

        // Find orders created after last checked time
        const newOrders = allOrders.filter((order) => {
          const orderDate = new Date(order.created_at || order.createdAt);
          return orderDate > lastCheckedTime;
        });

        if (newOrders.length > 0) {
          // Add new notifications
          const newNotifications = newOrders.map((order) => ({
            id: `notification-${Date.now()}-${order.id}`,
            type: "new_order",
            title: "New Order Received",
            message: `Order #${order.id.split("-")[1]} has been placed`,
            orderId: order.id,
            createdAt: new Date().toISOString(),
            read: false,
          }));

          setNotifications((prev) => [...newNotifications, ...prev]);
          setUnreadCount((prev) => prev + newNotifications.length);

          // Play notification sound and show browser notification
          playNotificationSound();
          showBrowserNotification(
            "New Order Received",
            `${newOrders.length} new order(s) received`,
            "new_order"
          );
        }
      } catch (error) {
        console.error("Error checking for new orders:", error);
      }
    };

    // Check immediately on mount
    checkForNewOrders();

    // Set up polling interval (every 30 seconds)
    const intervalId = setInterval(checkForNewOrders, 30000);

    return () => clearInterval(intervalId);
  }, [user, lastCheckedTime]);

  // Enhanced notification functions
  const playNotificationSound = () => {
    if (soundEnabled && audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current
        .play()
        .catch((e) => console.log("Error playing notification sound:", e));
    }
  };

  const showBrowserNotification = (title, body, type = "info") => {
    if (browserNotificationsEnabled && "Notification" in window) {
      const notification = new Notification(title, {
        body,
        icon: "/logo.png", // Add your app logo
        badge: "/logo.png",
        tag: type,
        requireInteraction: type === "new_order",
        silent: false,
      });

      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      // Auto close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);
    }
  };

  // Socket event handlers
  const handleOrderStatusUpdate = (data) => {
    const { orderId, status, timestamp, agentLocation } = data;

    // Update tracked order
    setTrackedOrders((prev) => {
      if (!prev[orderId]) return prev;

      const updatedTracking = {
        ...prev[orderId],
        status,
        updates: [
          ...prev[orderId].updates,
          {
            status,
            timestamp: new Date(timestamp),
          },
        ],
        ...(agentLocation && { deliveryAgentLocation: agentLocation }),
      };

      return {
        ...prev,
        [orderId]: updatedTracking,
      };
    });

    // Add notification
    const notification = {
      id: `order-update-${orderId}-${Date.now()}`,
      type: "order_update",
      title: "Order Status Update",
      message: `Your order #${orderId.split("-")[1]} is now ${status}`,
      orderId,
      createdAt: new Date().toISOString(),
      read: false,
    };

    setNotifications((prev) => [notification, ...prev]);
    setUnreadCount((prev) => prev + 1);

    // Play sound and show browser notification
    playNotificationSound();
    showBrowserNotification(
      "Order Status Update",
      notification.message,
      "order_update"
    );
  };

  const handleNewOrder = (orderData) => {
    const notification = {
      id: `new-order-${orderData.id}-${Date.now()}`,
      type: "new_order",
      title: "New Order Received",
      message: `Order #${orderData.id.split("-")[1]} has been placed`,
      orderId: orderData.id,
      createdAt: new Date().toISOString(),
      read: false,
    };

    setNotifications((prev) => [notification, ...prev]);
    setUnreadCount((prev) => prev + 1);

    playNotificationSound();
    showBrowserNotification(
      "New Order Received",
      notification.message,
      "new_order"
    );
  };

  const handleAgentLocationUpdate = (data) => {
    const { orderId, location } = data;

    setTrackedOrders((prev) => {
      if (!prev[orderId]) return prev;

      return {
        ...prev,
        [orderId]: {
          ...prev[orderId],
          deliveryAgentLocation: location,
        },
      };
    });
  };

  const handleOrderAssignment = (data) => {
    const { orderId, agentId } = data;

    const notification = {
      id: `order-assigned-${orderId}-${Date.now()}`,
      type: "order_assigned",
      title: "Order Assigned",
      message: `You have been assigned order #${orderId.split("-")[1]}`,
      orderId,
      createdAt: new Date().toISOString(),
      read: false,
    };

    setNotifications((prev) => [notification, ...prev]);
    setUnreadCount((prev) => prev + 1);

    playNotificationSound();
    showBrowserNotification(
      "Order Assigned",
      notification.message,
      "order_assigned"
    );
  };

  // Settings functions
  const toggleSound = () => {
    setSoundEnabled((prev) => !prev);
  };

  const toggleBrowserNotifications = () => {
    if (!browserNotificationsEnabled && "Notification" in window) {
      Notification.requestPermission().then((permission) => {
        setBrowserNotificationsEnabled(permission === "granted");
      });
    } else {
      setBrowserNotificationsEnabled(false);
    }
  };

  // Mark notifications as read
  const markAsRead = (notificationId) => {
    setNotifications((prev) => {
      const updated = prev.map((notification) =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      );

      // Update unread count based on updated notifications
      const unreadCount = updated.filter((n) => !n.read).length;
      setUnreadCount(unreadCount);

      return updated;
    });
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, read: true }))
    );
    setUnreadCount(0);
  };

  // Clear a notification
  const clearNotification = (notificationId) => {
    const notification = notifications.find((n) => n.id === notificationId);
    setNotifications((prev) => prev.filter((n) => n.id !== notificationId));

    // Update unread count if needed
    if (notification && !notification.read) {
      setUnreadCount((prev) => Math.max(0, prev - 1));
    }
  };

  // Clear all notifications
  const clearAllNotifications = () => {
    setNotifications([]);
    setUnreadCount(0);
  };

  // Update last checked time
  const updateLastCheckedTime = () => {
    const now = new Date();
    setLastCheckedTime(now);
    localStorage.setItem("afghanSofraLastCheckedTime", now.toISOString());
  };

  // Start tracking an order for real-time updates
  const startOrderTracking = (orderId) => {
    // In a real app, this would subscribe to real-time updates from the server
    // For demo purposes, we'll simulate updates

    if (trackedOrders[orderId]) {
      // Already tracking this order
      return trackedOrders[orderId];
    }

    // Fetch the order from API
    const fetchOrder = async () => {
      try {
        // Get the correct token from localStorage
        const userDataString = localStorage.getItem("afghanSofraUser");
        if (!userDataString) {
          console.log("🚫 NotificationContext: No user data found");
          return null;
        }

        const userData = JSON.parse(userDataString);
        if (!userData.access_token) {
          console.log("🚫 NotificationContext: No access token found");
          return null;
        }

        const response = await fetch(
          `${API_BASE_URL}/order/orders/${orderId}/`,
          {
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) return null;
        return await response.json();
      } catch (error) {
        console.error("Error fetching order:", error);
        return null;
      }
    };

    // For now, return a placeholder until we implement proper async handling
    const order = { id: orderId, status: "pending" };

    // Create a tracking object
    const tracking = {
      orderId,
      status: order.status,
      startTime: new Date(),
      updates: [],
      deliveryAgentLocation: null,
      estimatedArrival: order.estimatedDeliveryTime
        ? new Date(order.estimatedDeliveryTime)
        : null,
    };

    // Store in tracked orders
    setTrackedOrders((prev) => ({
      ...prev,
      [orderId]: tracking,
    }));

    // Simulate order status updates
    simulateOrderUpdates(orderId, order.status);

    return tracking;
  };

  // Stop tracking an order
  const stopOrderTracking = (orderId) => {
    setTrackedOrders((prev) => {
      const newTracked = { ...prev };
      delete newTracked[orderId];
      return newTracked;
    });
  };

  // Simulate order status updates for demo purposes
  const simulateOrderUpdates = (orderId, currentStatus) => {
    const statusFlow = [
      "pending",
      "confirmed",
      "preparing",
      "readyForPickup",
      "outForDelivery",
      "delivered",
    ];

    const currentIndex = statusFlow.indexOf(currentStatus);
    if (currentIndex === -1 || currentIndex >= statusFlow.length - 1) return;

    // Get the next status
    const nextStatus = statusFlow[currentIndex + 1];

    // Calculate a random delay (between 15-30 seconds for demo)
    const delay = Math.floor(Math.random() * 15000) + 15000;

    // Schedule the status update
    setTimeout(() => {
      // Update the tracked order
      setTrackedOrders((prev) => {
        if (!prev[orderId]) return prev;

        const updatedTracking = {
          ...prev[orderId],
          status: nextStatus,
          updates: [
            ...prev[orderId].updates,
            {
              status: nextStatus,
              timestamp: new Date(),
            },
          ],
        };

        // Add a notification for the status change
        const notification = {
          id: `order-update-${orderId}-${Date.now()}`,
          type: "order_update",
          title: "Order Status Update",
          message: `Your order #${orderId.split("-")[1]} is now ${nextStatus}`,
          orderId,
          createdAt: new Date().toISOString(),
          read: false,
        };

        setNotifications((prevNotifications) => [
          notification,
          ...prevNotifications,
        ]);
        setUnreadCount((prevCount) => prevCount + 1);

        // Play sound and show browser notification
        playNotificationSound();
        showBrowserNotification(
          "Order Status Update",
          notification.message,
          "order_update"
        );

        // If not delivered yet, schedule the next update
        if (nextStatus !== "delivered") {
          simulateOrderUpdates(orderId, nextStatus);
        }

        return {
          ...prev,
          [orderId]: updatedTracking,
        };
      });
    }, delay);
  };

  // Simulate delivery agent location updates
  const simulateAgentLocationUpdates = (
    orderId,
    startLocation,
    endLocation
  ) => {
    if (!trackedOrders[orderId]) return;

    // Only simulate for orders that are out for delivery
    if (trackedOrders[orderId].status !== "outForDelivery") return;

    // Update every 10 seconds
    const intervalId = setInterval(() => {
      setTrackedOrders((prev) => {
        if (!prev[orderId] || prev[orderId].status !== "outForDelivery") {
          clearInterval(intervalId);
          return prev;
        }

        // Generate a random movement (in a real app, this would be actual GPS data)
        const randomLat =
          Math.random() * 0.001 * (Math.random() > 0.5 ? 1 : -1);
        const randomLng =
          Math.random() * 0.001 * (Math.random() > 0.5 ? 1 : -1);

        const currentLocation =
          prev[orderId].deliveryAgentLocation || startLocation;

        // Calculate new location
        const newLocation = {
          lat: currentLocation.lat + randomLat,
          lng: currentLocation.lng + randomLng,
        };

        // Update the tracked order
        return {
          ...prev,
          [orderId]: {
            ...prev[orderId],
            deliveryAgentLocation: newLocation,
          },
        };
      });
    }, 10000);

    // Store the interval ID to clear it later
    return intervalId;
  };

  // Get tracking information for an order
  const getOrderTracking = (orderId) => {
    return trackedOrders[orderId] || null;
  };

  const value = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    updateLastCheckedTime,

    // Order tracking
    startOrderTracking,
    stopOrderTracking,
    getOrderTracking,
    trackedOrders,

    // Real-time features
    isConnected,
    socket,

    // Notification settings
    soundEnabled,
    browserNotificationsEnabled,
    toggleSound,
    toggleBrowserNotifications,

    // Manual notification functions
    playNotificationSound,
    showBrowserNotification,

    // Socket event handlers (for manual triggering in demo)
    handleOrderStatusUpdate,
    handleNewOrder,
    handleAgentLocationUpdate,
    handleOrderAssignment,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};
