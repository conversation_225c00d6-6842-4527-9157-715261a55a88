# deliveryAgent/serializers.py
from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import DeliveryAgentProfile, DeliveryAgentDocument
from orders.models import Order

User = get_user_model()

class UserBasicSerializer(serializers.ModelSerializer):
    """Basic user information for delivery agent"""
    class Meta:
        model = User
        fields = ['id', 'name', 'user_name', 'email', 'phone', 'is_verified']
        read_only_fields = ['id', 'user_name']


class DeliveryAgentDocumentSerializer(serializers.ModelSerializer):
    """Serializer for delivery agent documents"""
    class Meta:
        model = DeliveryAgentDocument
        fields = [
            'id', 'document_type', 'document_file', 'is_verified',
            'uploaded_at', 'verified_at'
        ]
        read_only_fields = ['id', 'is_verified', 'uploaded_at', 'verified_at']


class DeliveryAgentProfileSerializer(serializers.ModelSerializer):
    """Complete delivery agent profile serializer"""
    user = UserBasicSerializer(read_only=True)
    documents = DeliveryAgentDocumentSerializer(many=True, read_only=True)
    completion_rate = serializers.ReadOnlyField()
    is_available_for_delivery = serializers.ReadOnlyField()
    
    class Meta:
        model = DeliveryAgentProfile
        fields = [
            'id', 'user', 'vehicle_type', 'vehicle_number', 'vehicle_color',
            'license_number', 'current_location', 'status', 'is_online',
            'last_seen', 'rating', 'total_deliveries', 'completed_orders',
            'cancelled_orders', 'average_delivery_time', 'earnings',
            'bank_details', 'delivery_zones', 'is_verified',
            'verification_documents', 'joined_date', 'created_at',
            'updated_at', 'documents', 'completion_rate',
            'is_available_for_delivery'
        ]
        read_only_fields = [
            'id', 'user', 'last_seen', 'rating', 'total_deliveries',
            'completed_orders', 'cancelled_orders', 'average_delivery_time',
            'is_verified', 'joined_date', 'created_at', 'updated_at',
            'completion_rate', 'is_available_for_delivery'
        ]


class DeliveryAgentProfileUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating delivery agent profile"""
    class Meta:
        model = DeliveryAgentProfile
        fields = [
            'vehicle_type', 'vehicle_number', 'vehicle_color',
            'license_number', 'bank_details', 'delivery_zones'
        ]


class LocationUpdateSerializer(serializers.Serializer):
    """Serializer for updating agent location"""
    latitude = serializers.FloatField()
    longitude = serializers.FloatField()
    address = serializers.CharField(max_length=255, required=False, allow_blank=True)
    
    def validate_latitude(self, value):
        if not -90 <= value <= 90:
            raise serializers.ValidationError("Latitude must be between -90 and 90")
        return value
    
    def validate_longitude(self, value):
        if not -180 <= value <= 180:
            raise serializers.ValidationError("Longitude must be between -180 and 180")
        return value


class StatusUpdateSerializer(serializers.Serializer):
    """Serializer for updating agent status"""
    status = serializers.ChoiceField(choices=DeliveryAgentProfile.STATUS_CHOICES)


class DeliveryAgentOrderSerializer(serializers.ModelSerializer):
    """Serializer for orders from delivery agent perspective"""
    restaurant_name = serializers.CharField(source='restaurant.name', read_only=True)
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    customer_phone = serializers.CharField(source='customer.phone', read_only=True)
    
    class Meta:
        model = Order
        fields = [
            'id', 'restaurant_name', 'customer_name', 'customer_phone',
            'delivery_address', 'status', 'total_amount', 'delivery_fee',
            'special_instructions', 'created_at', 'estimated_delivery_time',
            'payment_method', 'payment_status'
        ]
        read_only_fields = ['id', 'created_at']


class OrderActionSerializer(serializers.Serializer):
    """Serializer for order actions (accept, reject, pickup, deliver)"""
    order_id = serializers.IntegerField()
    reason = serializers.CharField(max_length=255, required=False, allow_blank=True)
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)


class EarningsSerializer(serializers.Serializer):
    """Serializer for earnings data"""
    today = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    this_week = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    this_month = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_orders_today = serializers.IntegerField(read_only=True)
    total_orders_week = serializers.IntegerField(read_only=True)
    total_orders_month = serializers.IntegerField(read_only=True)


class DeliveryAgentStatsSerializer(serializers.Serializer):
    """Serializer for delivery agent statistics"""
    total_deliveries = serializers.IntegerField(read_only=True)
    completed_orders = serializers.IntegerField(read_only=True)
    cancelled_orders = serializers.IntegerField(read_only=True)
    completion_rate = serializers.FloatField(read_only=True)
    average_rating = serializers.DecimalField(max_digits=3, decimal_places=2, read_only=True)
    average_delivery_time = serializers.IntegerField(read_only=True)
    earnings = EarningsSerializer(read_only=True)
