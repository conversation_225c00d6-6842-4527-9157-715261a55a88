import React from "react";
import { Link } from "react-router-dom";
import { Star, Clock, MapPin } from "lucide-react";
import { cn } from "../../utils/cn";
import { useDeliveryFee } from "../../hooks/useDeliveryFee";
import {
  DeliveryFeeBadge,
  DeliveryTimeBadge,
} from "../delivery/DeliveryFeeDisplay";
import Card from "../common/Card";
import Badge from "../common/Badge";
import FavoriteButton from "../common/FavoriteButton";
import RestaurantRating from "../rating/RestaurantRating";

const RestaurantCard = ({
  restaurant,
  className = "",
  showFavoriteButton = true,
  variant = "default", // "default", "compact", "detailed"
}) => {
  const { deliveryInfo, loading: deliveryLoading } = useDeliveryFee(restaurant);

  // Compact variant for lists
  if (variant === "compact") {
    return (
      <Link to={`/restaurants/${restaurant.id}`}>
        <Card
          className={cn("p-4 hover:shadow-md transition-shadow", className)}
          hoverable
        >
          <div className='flex items-center space-x-4'>
            <img
              src={restaurant.logo}
              alt={restaurant.name}
              className='w-16 h-16 rounded-lg object-cover'
            />

            <div className='flex-1 min-w-0'>
              <div className='flex items-center justify-between mb-1'>
                <h3 className='font-semibold text-lg truncate'>
                  {restaurant.name}
                </h3>
                {showFavoriteButton && (
                  <FavoriteButton
                    restaurant={restaurant}
                    size='small'
                    variant='ghost'
                  />
                )}
              </div>

              <div className='flex items-center mb-2'>
                <Star size={14} className='text-yellow-500 mr-1' />
                <span className='text-sm font-medium'>{restaurant.rating}</span>
                <span className='text-sm text-gray-500 ml-2 truncate'>
                  {restaurant.cuisine_types &&
                  restaurant.cuisine_types.length > 0
                    ? restaurant.cuisine_types
                        .map((ct) => ct.name || ct)
                        .join(", ")
                    : restaurant.cuisine
                    ? Array.isArray(restaurant.cuisine)
                      ? restaurant.cuisine.join(", ")
                      : restaurant.cuisine
                    : "Afghan Cuisine"}
                </span>
              </div>

              <div className='flex items-center justify-between text-sm'>
                <DeliveryTimeBadge
                  deliveryInfo={deliveryInfo}
                  loading={deliveryLoading}
                />
                <DeliveryFeeBadge
                  deliveryInfo={deliveryInfo}
                  loading={deliveryLoading}
                />
              </div>
            </div>
          </div>
        </Card>
      </Link>
    );
  }

  // Default variant (grid card)
  return (
    <Link to={`/restaurants/${restaurant.id}`}>
      <Card
        className={cn(
          "h-full transition-transform duration-200 hover:-translate-y-1 relative",
          className
        )}
        hoverable
      >
        {/* Favorite Button */}
        {showFavoriteButton && (
          <div className='absolute top-4 right-4 z-10'>
            <FavoriteButton
              restaurant={restaurant}
              size='default'
              variant='default'
            />
          </div>
        )}

        {/* Restaurant Image */}
        <div className='relative h-48 rounded-t-lg overflow-hidden -mx-5 -mt-5 mb-4'>
          <div className='absolute top-4 left-4 z-10'>
            {restaurant.is_active ? (
              <Badge variant='success' size='small'>
                Open Now
              </Badge>
            ) : (
              <Badge variant='danger' size='small'>
                Closed
              </Badge>
            )}
          </div>
          <img
            src={restaurant.banner || "/placeholder-restaurant.jpg"}
            alt={restaurant.name}
            className='w-full h-full object-cover'
            onError={(e) => {
              e.target.src = "/placeholder-restaurant.jpg";
            }}
          />
        </div>

        {/* Restaurant Info */}
        <div className='space-y-3'>
          <div>
            <h3 className='font-semibold text-lg mb-1'>{restaurant.name}</h3>
            <RestaurantRating
              restaurantId={restaurant.id}
              showDetails={false}
            />
          </div>

          <div className='text-sm text-gray-600'>
            <p className='line-clamp-2'>
              {restaurant.cuisine_types && restaurant.cuisine_types.length > 0
                ? restaurant.cuisine_types
                    .map((ct) => ct.name || ct)
                    .join(" • ")
                : restaurant.cuisine
                ? Array.isArray(restaurant.cuisine)
                  ? restaurant.cuisine.join(" • ")
                  : restaurant.cuisine
                : "Afghan Cuisine"}
            </p>
          </div>

          {/* Delivery Info */}
          <div className='flex items-center justify-between text-sm'>
            <div className='flex items-center space-x-4'>
              <DeliveryTimeBadge
                deliveryInfo={deliveryInfo}
                loading={deliveryLoading}
              />

              {deliveryInfo?.distance && (
                <div className='flex items-center text-gray-600'>
                  <MapPin size={14} className='mr-1' />
                  <span>{deliveryInfo.distance.toFixed(1)} km</span>
                </div>
              )}
            </div>

            <DeliveryFeeBadge
              deliveryInfo={deliveryInfo}
              loading={deliveryLoading}
            />
          </div>

          {/* Additional Info for detailed variant */}
          {variant === "detailed" && (
            <div className='pt-3 border-t space-y-2'>
              {/* Popular dishes */}
              {restaurant.popularDishes && (
                <div>
                  <p className='text-xs text-gray-500 mb-1'>Popular:</p>
                  <p className='text-sm text-gray-700 line-clamp-1'>
                    {restaurant.popularDishes.join(", ")}
                  </p>
                </div>
              )}

              {/* Promotions */}
              {restaurant.promotion && (
                <div className='bg-green-50 p-2 rounded text-xs text-green-800'>
                  🎉 {restaurant.promotion}
                </div>
              )}
            </div>
          )}
        </div>
      </Card>
    </Link>
  );
};

// Grid of restaurant cards
export const RestaurantGrid = ({
  restaurants,
  loading = false,
  className = "",
  variant = "default",
  emptyMessage = "No restaurants found",
}) => {
  if (loading) {
    return (
      <div
        className={cn(
          "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
          className
        )}
      >
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className='animate-pulse'>
            <div className='bg-gray-200 h-48 rounded-lg mb-4'></div>
            <div className='space-y-2'>
              <div className='bg-gray-200 h-4 rounded w-3/4'></div>
              <div className='bg-gray-200 h-3 rounded w-1/2'></div>
              <div className='bg-gray-200 h-3 rounded w-2/3'></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (!restaurants || restaurants.length === 0) {
    return (
      <div className='text-center py-12'>
        <p className='text-gray-500'>{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div
      className={cn(
        variant === "compact"
          ? "space-y-4"
          : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
        className
      )}
    >
      {restaurants.map((restaurant) => (
        <RestaurantCard
          key={restaurant.id}
          restaurant={restaurant}
          variant={variant}
        />
      ))}
    </div>
  );
};

export default RestaurantCard;
