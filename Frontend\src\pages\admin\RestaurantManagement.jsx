import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  MapPin,
  Phone,
  Mail,
  Clock,
  Star,
  Edit,
  Trash2,
  Plus,
  AlertCircle,
  CheckCircle,
  XCircle,
  X,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";

function RestaurantManagement() {
  console.log("🏪 RestaurantManagement component loaded");

  const { user: currentUser } = useAuth();
  const {
    restaurants,
    loading,
    error,
    getRestaurants,
    createRestaurant,
    updateRestaurant,
    deleteRestaurant,
    clearError,
  } = useRestaurant();

  console.log("🏪 RestaurantManagement state:", {
    currentUser,
    restaurants: restaurants?.length,
    loading,
    error,
  });

  const [filteredRestaurants, setFilteredRestaurants] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("active");
  const [selectedRestaurant, setSelectedRestaurant] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showAddRestaurant, setShowAddRestaurant] = useState(false);
  const [newRestaurant, setNewRestaurant] = useState({
    name: "",
    description: "",
    contact_number: "",
    opening_time: "09:00:00",
    closing_time: "22:00:00",
    address: {
      street: "",
      city: "",
      state: "",
      postal_code: "",
      country: "USA",
      latitude: 0,
      longitude: 0,
    },
  });

  const handleInputChange = (field, value) => {
    if (field.startsWith("address.")) {
      const addressField = field.split(".")[1];
      setNewRestaurant((prev) => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value,
        },
      }));
    } else {
      setNewRestaurant((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };
  const [logoFile, setLogoFile] = useState(null);
  const [bannerFile, setBannerFile] = useState(null);

  useEffect(() => {
    loadRestaurants();
  }, [getRestaurants]);

  const loadRestaurants = async () => {
    try {
      console.log("🔄 Loading restaurants...");
      await getRestaurants();
      console.log("✅ Restaurants loaded successfully");
    } catch (error) {
      console.error("❌ Failed to load restaurants:", error);
    }
  };

  useEffect(() => {
    let filtered = [...restaurants];

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (r) =>
          r.name?.toLowerCase().includes(query) ||
          r.description?.toLowerCase().includes(query) ||
          r.contact_number?.toLowerCase().includes(query) ||
          (r.address?.street &&
            r.address.street.toLowerCase().includes(query)) ||
          (r.address?.city && r.address.city.toLowerCase().includes(query))
      );
    }

    setFilteredRestaurants(filtered);
  }, [restaurants, statusFilter, searchQuery]);

  const handleAddRestaurant = async () => {
    try {
      clearError();

      const restaurantData = {
        ...newRestaurant,
        logo: logoFile,
        banner: bannerFile,
      };

      const result = await createRestaurant(restaurantData);

      if (result.success) {
        setShowAddRestaurant(false);
        resetForm();
        // Refresh the list
        await getRestaurants();
      }
    } catch (error) {
      console.error("Error adding restaurant:", error);
    }
  };

  const resetForm = () => {
    setNewRestaurant({
      name: "",
      description: "",
      contact_number: "",
      opening_time: "09:00:00",
      closing_time: "22:00:00",
      address: {
        street: "",
        city: "",
        state: "",
        postal_code: "",
        country: "USA",
        latitude: 0,
        longitude: 0,
      },
    });
    setLogoFile(null);
    setBannerFile(null);
  };

  const handleUpdateRestaurant = async (updatedRestaurant) => {
    try {
      clearError();

      const result = await updateRestaurant(
        updatedRestaurant.id,
        updatedRestaurant
      );

      if (result.success) {
        setShowDetails(false);
        // Refresh the list
        await getRestaurants();
      }
    } catch (error) {
      console.error("Error updating restaurant:", error);
    }
  };

  const handleDeleteRestaurant = async (restaurantId) => {
    if (!window.confirm("Are you sure you want to delete this restaurant?")) {
      return;
    }

    try {
      clearError();

      const result = await deleteRestaurant(restaurantId);

      if (result.success) {
        setShowDetails(false);
        // Refresh the list
        await getRestaurants();
      }
    } catch (error) {
      console.error("Error deleting restaurant:", error);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500'></div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {error && (
        <Card className='border-l-4 border-red-500'>
          <div className='flex items-start p-4'>
            <AlertCircle
              size={18}
              className='text-red-500 mr-2 mt-0.5 flex-shrink-0'
            />
            <div className='flex-1'>
              <p className='text-red-600 text-sm'>{error}</p>
              <Button
                variant='ghost'
                size='sm'
                onClick={clearError}
                className='mt-2 text-red-600 hover:text-red-700'
              >
                Dismiss
              </Button>
            </div>
          </div>
        </Card>
      )}

      <div className='flex justify-between items-center'>
        <h1 className='text-2xl font-bold'>Restaurant Management</h1>
        <div className='flex items-center space-x-4'>
          <div className='relative'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={20}
            />
            <input
              type='text'
              placeholder='Search restaurants...'
              className='pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Button
            variant='primary'
            icon={<Plus size={20} />}
            onClick={() => setShowAddRestaurant(true)}
          >
            Add Restaurant
          </Button>
        </div>
      </div>

      {/* Restaurant List */}
      <div className='grid gap-4'>
        {filteredRestaurants.map((restaurant) => (
          <Card key={restaurant.id}>
            <div className='p-4'>
              <div className='flex justify-between items-start'>
                <div>
                  <div className='flex items-center space-x-3'>
                    <h3 className='text-lg font-semibold'>{restaurant.name}</h3>
                    <Badge className='bg-green-100 text-green-800'>
                      Active
                    </Badge>
                  </div>
                  <div className='mt-2 space-y-1'>
                    <div className='flex items-center text-sm text-gray-600'>
                      <MapPin size={16} className='mr-2' />
                      {restaurant.address?.street
                        ? `${restaurant.address.street}, ${restaurant.address.city}`
                        : "Address not available"}
                    </div>
                    <div className='flex items-center text-sm text-gray-600'>
                      <Phone size={16} className='mr-2' />
                      {restaurant.contact_number || "Phone not available"}
                    </div>
                    <div className='flex items-center text-sm text-gray-600'>
                      <Clock size={16} className='mr-2' />
                      {restaurant.opening_time && restaurant.closing_time
                        ? `${restaurant.opening_time} - ${restaurant.closing_time}`
                        : "Hours not available"}
                    </div>
                  </div>
                  <div className='mt-3 flex items-center space-x-4 text-sm text-gray-600'>
                    <div>Total Orders: {restaurant.totalOrders}</div>
                    <div>
                      Monthly Revenue:{" "}
                      {formatCurrency(restaurant.monthlyRevenue)}
                    </div>
                  </div>
                </div>
                <div className='flex items-center space-x-2'>
                  <Button
                    variant='outline'
                    size='small'
                    icon={<Edit size={16} />}
                    onClick={() => {
                      setSelectedRestaurant(restaurant);
                      setShowDetails(true);
                    }}
                  >
                    Edit
                  </Button>

                  <Button
                    variant='danger'
                    size='small'
                    icon={<Trash2 size={16} />}
                    onClick={() => handleDeleteRestaurant(restaurant.id)}
                  >
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Add Restaurant Modal */}
      {showAddRestaurant && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-auto'>
            <div className='p-6 border-b'>
              <div className='flex justify-between items-center'>
                <h2 className='text-xl font-semibold'>Add New Restaurant</h2>
                <button
                  className='text-gray-400 hover:text-gray-600'
                  onClick={() => setShowAddRestaurant(false)}
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className='p-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-4'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Restaurant Name
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.name}
                      onChange={(e) =>
                        handleInputChange("name", e.target.value)
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Description
                    </label>
                    <textarea
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.description}
                      onChange={(e) =>
                        handleInputChange("description", e.target.value)
                      }
                      rows='3'
                      required
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Contact Number
                    </label>
                    <input
                      type='tel'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.contact_number}
                      onChange={(e) =>
                        handleInputChange("contact_number", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Opening Time
                    </label>
                    <input
                      type='time'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.opening_time}
                      onChange={(e) =>
                        handleInputChange("opening_time", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Closing Time
                    </label>
                    <input
                      type='time'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.closing_time}
                      onChange={(e) =>
                        handleInputChange("closing_time", e.target.value)
                      }
                      required
                    />
                  </div>
                </div>

                <div className='space-y-4'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Street Address
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.address.street}
                      onChange={(e) =>
                        handleInputChange("address.street", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      City
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.address.city}
                      onChange={(e) =>
                        handleInputChange("address.city", e.target.value)
                      }
                      required
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      State
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.address.state}
                      onChange={(e) =>
                        handleInputChange("address.state", e.target.value)
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Postal Code
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.address.postal_code}
                      onChange={(e) =>
                        handleInputChange("address.postal_code", e.target.value)
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Logo
                    </label>
                    <input
                      type='file'
                      accept='image/*'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      onChange={(e) => setLogoFile(e.target.files[0])}
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Banner
                    </label>
                    <input
                      type='file'
                      accept='image/*'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      onChange={(e) => setBannerFile(e.target.files[0])}
                    />
                  </div>
                </div>
              </div>

              <div className='mt-6 flex justify-end space-x-3'>
                <Button
                  variant='outline'
                  onClick={() => setShowAddRestaurant(false)}
                >
                  Cancel
                </Button>
                <Button variant='primary' onClick={handleAddRestaurant}>
                  Add Restaurant
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Restaurant Details Modal */}
      {showDetails && selectedRestaurant && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-auto'>
            <div className='p-6 border-b'>
              <div className='flex justify-between items-start'>
                <div>
                  <h2 className='text-xl font-semibold'>
                    {selectedRestaurant.name}
                  </h2>
                  <div className='flex items-center mt-1'>
                    <Badge
                      className={
                        selectedRestaurant.status === "active"
                          ? "bg-green-100 text-green-800"
                          : "bg-red-100 text-red-800"
                      }
                    >
                      {selectedRestaurant.status}
                    </Badge>
                    <div className='flex items-center text-yellow-500 ml-3'>
                      <Star size={16} className='fill-current' />
                      <span className='ml-1 text-sm'>
                        {selectedRestaurant.rating}
                      </span>
                    </div>
                  </div>
                </div>
                <button
                  className='text-gray-400 hover:text-gray-600'
                  onClick={() => setShowDetails(false)}
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className='p-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <h3 className='text-lg font-semibold mb-4'>
                    Restaurant Information
                  </h3>
                  <div className='space-y-4'>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Owner Name
                      </label>
                      <input
                        type='text'
                        className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                        value={selectedRestaurant.owner}
                        onChange={(e) =>
                          setSelectedRestaurant({
                            ...selectedRestaurant,
                            owner: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Cuisine Type
                      </label>
                      <input
                        type='text'
                        className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                        value={selectedRestaurant.cuisine}
                        onChange={(e) =>
                          setSelectedRestaurant({
                            ...selectedRestaurant,
                            cuisine: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Address
                      </label>
                      <input
                        type='text'
                        className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                        value={selectedRestaurant.address}
                        onChange={(e) =>
                          setSelectedRestaurant({
                            ...selectedRestaurant,
                            address: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Contact
                      </label>
                      <div className='mt-1 space-y-2'>
                        <input
                          type='tel'
                          className='w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                          value={selectedRestaurant.phone}
                          onChange={(e) =>
                            setSelectedRestaurant({
                              ...selectedRestaurant,
                              phone: e.target.value,
                            })
                          }
                        />
                        <input
                          type='email'
                          className='w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                          value={selectedRestaurant.email}
                          onChange={(e) =>
                            setSelectedRestaurant({
                              ...selectedRestaurant,
                              email: e.target.value,
                            })
                          }
                        />
                      </div>
                    </div>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Rating
                      </label>
                      <input
                        type='number'
                        min='0'
                        max='5'
                        step='0.1'
                        className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                        value={selectedRestaurant.rating}
                        onChange={(e) =>
                          setSelectedRestaurant({
                            ...selectedRestaurant,
                            rating: parseFloat(e.target.value),
                          })
                        }
                      />
                    </div>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Status
                      </label>
                      <select
                        className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                        value={selectedRestaurant.status}
                        onChange={(e) =>
                          setSelectedRestaurant({
                            ...selectedRestaurant,
                            status: e.target.value,
                          })
                        }
                      >
                        <option value='active'>Active</option>
                        <option value='inactive'>Inactive</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-semibold mb-4'>Opening Hours</h3>
                  <div className='space-y-4'>
                    {Object.entries(selectedRestaurant.openingHours).map(
                      ([day, hours]) => (
                        <div key={day} className='flex items-center space-x-4'>
                          <label className='w-24 text-sm font-medium text-gray-700 capitalize'>
                            {day}
                          </label>
                          <div className='flex items-center space-x-2'>
                            <input
                              type='time'
                              className='px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                              value={hours.open}
                              onChange={(e) =>
                                setSelectedRestaurant({
                                  ...selectedRestaurant,
                                  openingHours: {
                                    ...selectedRestaurant.openingHours,
                                    [day]: { ...hours, open: e.target.value },
                                  },
                                })
                              }
                            />
                            <span>to</span>
                            <input
                              type='time'
                              className='px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                              value={hours.close}
                              onChange={(e) =>
                                setSelectedRestaurant({
                                  ...selectedRestaurant,
                                  openingHours: {
                                    ...selectedRestaurant.openingHours,
                                    [day]: { ...hours, close: e.target.value },
                                  },
                                })
                              }
                            />
                          </div>
                        </div>
                      )
                    )}
                  </div>

                  <div className='mt-6'>
                    <h3 className='text-lg font-semibold mb-4'>
                      Performance Metrics
                    </h3>
                    <div className='grid grid-cols-2 gap-4'>
                      <div className='p-4 bg-gray-50 rounded-lg'>
                        <div className='text-sm text-gray-600'>
                          Total Orders
                        </div>
                        <div className='text-2xl font-semibold'>
                          {selectedRestaurant.totalOrders}
                        </div>
                      </div>
                      <div className='p-4 bg-gray-50 rounded-lg'>
                        <div className='text-sm text-gray-600'>
                          Monthly Revenue
                        </div>
                        <div className='text-2xl font-semibold'>
                          {formatCurrency(selectedRestaurant.monthlyRevenue)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className='mt-6 pt-6 border-t flex justify-end space-x-3'>
                <Button
                  variant='danger'
                  onClick={() => handleDeleteRestaurant(selectedRestaurant.id)}
                >
                  Delete Restaurant
                </Button>
                <Button
                  variant='primary'
                  onClick={() => handleUpdateRestaurant(selectedRestaurant)}
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default RestaurantManagement;
