import React from 'react';
import { RefreshCw, ArrowDown } from 'lucide-react';
import { usePullToRefresh } from '../../hooks/useTouch';
import { cn } from '../../utils/cn';

const PullToRefresh = ({
  onRefresh,
  children,
  threshold = 80,
  maxPull = 120,
  disabled = false,
  className = "",
  refreshText = "Pull to refresh",
  releaseText = "Release to refresh",
  refreshingText = "Refreshing..."
}) => {
  const {
    touchHandlers,
    pullDistance,
    pullProgress,
    isRefreshing,
    isPulling,
    shouldRefresh
  } = usePullToRefresh({
    onRefresh,
    threshold,
    maxPull,
    disabled
  });

  const getStatusText = () => {
    if (isRefreshing) return refreshingText;
    if (shouldRefresh) return releaseText;
    return refreshText;
  };

  const getIconRotation = () => {
    if (isRefreshing) return 'animate-spin';
    if (shouldRefresh) return 'rotate-180';
    return `rotate-${Math.min(pullProgress * 180, 180)}`;
  };

  return (
    <div className={cn('relative overflow-hidden', className)} {...touchHandlers}>
      {/* Pull indicator */}
      <div
        className="absolute top-0 left-0 right-0 flex items-center justify-center bg-primary-50 transition-all duration-200 ease-out"
        style={{
          height: `${pullDistance}px`,
          transform: `translateY(-${Math.max(0, threshold - pullDistance)}px)`,
          opacity: isPulling || isRefreshing ? 1 : 0
        }}
      >
        <div className="flex items-center space-x-2 text-primary-600">
          {isRefreshing ? (
            <RefreshCw size={20} className="animate-spin" />
          ) : (
            <ArrowDown 
              size={20} 
              className={`transition-transform duration-200 ${
                shouldRefresh ? 'rotate-180' : ''
              }`}
              style={{
                transform: `rotate(${Math.min(pullProgress * 180, 180)}deg)`
              }}
            />
          )}
          <span className="text-sm font-medium">{getStatusText()}</span>
        </div>
      </div>

      {/* Content */}
      <div
        className="transition-transform duration-200 ease-out"
        style={{
          transform: `translateY(${isPulling || isRefreshing ? pullDistance : 0}px)`
        }}
      >
        {children}
      </div>
    </div>
  );
};

// Simplified pull-to-refresh for lists
export const SimplePullToRefresh = ({
  onRefresh,
  children,
  isRefreshing = false,
  className = ""
}) => {
  const {
    touchHandlers,
    pullDistance,
    isPulling,
    shouldRefresh
  } = usePullToRefresh({
    onRefresh,
    threshold: 60,
    maxPull: 100
  });

  return (
    <div className={cn('relative', className)} {...touchHandlers}>
      {/* Simple indicator */}
      {(isPulling || isRefreshing) && (
        <div 
          className="flex items-center justify-center py-4 bg-gray-50"
          style={{ height: `${Math.max(pullDistance, isRefreshing ? 60 : 0)}px` }}
        >
          <RefreshCw 
            size={24} 
            className={`text-primary-500 ${isRefreshing ? 'animate-spin' : ''}`}
          />
        </div>
      )}
      
      <div
        style={{
          transform: `translateY(${isPulling || isRefreshing ? pullDistance : 0}px)`,
          transition: isPulling ? 'none' : 'transform 0.3s ease-out'
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default PullToRefresh;
