# deliveryAgent/urls.py
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    DeliveryAgentProfileViewSet,
    DeliveryAgentOrderViewSet,
    DeliveryAgentDocumentViewSet
)

# Create router for ViewSets
router = DefaultRouter()
router.register(r'profile', DeliveryAgentProfileViewSet, basename='delivery-agent-profile')
router.register(r'orders', DeliveryAgentOrderViewSet, basename='delivery-agent-orders')
router.register(r'documents', DeliveryAgentDocumentViewSet, basename='delivery-agent-documents')

urlpatterns = [
    # Include router URLs
    path('', include(router.urls)),
]

# URL patterns will be:
# GET/PUT /api/delivery-agent/profile/me/ - Get/Update current agent profile
# POST /api/delivery-agent/profile/update_location/ - Update location
# POST /api/delivery-agent/profile/update_status/ - Update status
# GET /api/delivery-agent/profile/stats/ - Get performance stats
# 
# GET /api/delivery-agent/orders/available/ - Get available orders
# GET /api/delivery-agent/orders/assigned/ - Get assigned orders
# POST /api/delivery-agent/orders/accept_order/ - Accept an order
# POST /api/delivery-agent/orders/reject_order/ - Reject an order
# POST /api/delivery-agent/orders/pickup_order/ - Mark order as picked up
# POST /api/delivery-agent/orders/deliver_order/ - Mark order as delivered
#
# GET/POST /api/delivery-agent/documents/ - Manage verification documents
