# customer/models.py
from django.db import models
from users.models import User
from restaurant.models import Restaurant

class CustomerFavorite(models.Model):
    """Model to store customer's favorite restaurants"""
    customer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='favorites',
        limit_choices_to={'role': 'customer'}
    )
    restaurant = models.ForeignKey(
        Restaurant,
        on_delete=models.CASCADE,
        related_name='favorited_by'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('customer', 'restaurant')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', '-created_at']),
            models.Index(fields=['restaurant', '-created_at']),
        ]

    def __str__(self):
        return f"{self.customer.name} - {self.restaurant.name}"
