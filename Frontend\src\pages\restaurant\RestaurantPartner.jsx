import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Store,
  TrendingUp,
  Users,
  Clock,
  Shield,
  DollarSign,
  CheckCircle,
  Star,
  ArrowRight,
  Phone,
  Mail,
  MapPin,
  Utensils,
  BarChart3,
  Smartphone,
  HeadphonesIcon,
} from "lucide-react";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";

const RestaurantPartner = () => {
  const [activeTab, setActiveTab] = useState("benefits");

  const benefits = [
    {
      icon: <TrendingUp className='text-green-500' size={32} />,
      title: "Increase Revenue",
      description:
        "Reach more customers and boost your sales by up to 30% with our delivery platform.",
    },
    {
      icon: <Users className='text-blue-500' size={32} />,
      title: "Expand Customer Base",
      description:
        "Access thousands of hungry customers in your area looking for great food.",
    },
    {
      icon: <Smartphone className='text-purple-500' size={32} />,
      title: "Easy Management",
      description:
        "Manage orders, menu, and analytics through our intuitive restaurant dashboard.",
    },
    {
      icon: <BarChart3 className='text-orange-500' size={32} />,
      title: "Real-time Analytics",
      description:
        "Track your performance with detailed insights and sales reports.",
    },
    {
      icon: <Shield className='text-red-500' size={32} />,
      title: "Secure Payments",
      description:
        "Get paid quickly and securely with our reliable payment processing.",
    },
    {
      icon: <HeadphonesIcon className='text-indigo-500' size={32} />,
      title: "24/7 Support",
      description:
        "Dedicated support team to help you succeed on our platform.",
    },
  ];

  const requirements = [
    "Valid business license and permits",
    "Food safety certification",
    "Professional kitchen facility",
    "Consistent food quality standards",
    "Reliable internet connection",
    "Ability to fulfill orders within 30-45 minutes",
  ];

  const steps = [
    {
      number: "1",
      title: "Apply Online",
      description:
        "Fill out our simple application form with your restaurant details.",
    },
    {
      number: "2",
      title: "Document Review",
      description:
        "Our team reviews your documents and restaurant information.",
    },
    {
      number: "3",
      title: "Quality Check",
      description: "We may visit your restaurant to ensure quality standards.",
    },
    {
      number: "4",
      title: "Onboarding",
      description: "Get trained on our platform and set up your menu.",
    },
    {
      number: "5",
      title: "Go Live",
      description: "Start receiving orders and growing your business!",
    },
  ];

  const testimonials = [
    {
      name: "Ahmad Hassan",
      restaurant: "Kabul Kitchen",
      rating: 5,
      text: "Afghan Sofra helped us reach 200% more customers. The platform is easy to use and support is excellent.",
      image: "/api/placeholder/60/60",
    },
    {
      name: "Fatima Ali",
      restaurant: "Spice Garden",
      rating: 5,
      text: "Our revenue increased by 40% within the first month. The analytics help us understand our customers better.",
      image: "/api/placeholder/60/60",
    },
    {
      name: "Omar Khan",
      restaurant: "Royal Biryani",
      rating: 5,
      text: "Professional platform with great commission rates. Highly recommend for any restaurant owner.",
      image: "/api/placeholder/60/60",
    },
  ];

  const faqs = [
    {
      question: "What commission does Afghan Sofra charge?",
      answer:
        "We charge a competitive commission rate of 15-20% per order, which includes payment processing and delivery coordination.",
    },
    {
      question: "How long does the approval process take?",
      answer:
        "The approval process typically takes 3-5 business days after all required documents are submitted.",
    },
    {
      question: "Do I need my own delivery drivers?",
      answer:
        "No, we have a network of professional delivery partners. You can also use your own drivers if preferred.",
    },
    {
      question: "What support do you provide?",
      answer:
        "We provide 24/7 customer support, marketing assistance, and dedicated account management for our restaurant partners.",
    },
    {
      question: "Can I update my menu anytime?",
      answer:
        "Yes, you can update your menu, prices, and availability in real-time through our restaurant dashboard.",
    },
  ];

  return (
    <div className='min-h-screen bg-background-light'>
      {/* Hero Section */}
      <section className='bg-gradient-to-r from-primary-600 to-primary-700 text-white py-20'>
        <div className='container mx-auto px-4'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-12 items-center'>
            <div>
              <h1 className='text-4xl md:text-5xl font-bold mb-6'>
                Partner with Afghan Sofra
              </h1>
              <p className='text-xl mb-8 opacity-90'>
                Join thousands of successful restaurants growing their business
                with our delivery platform. Reach more customers, increase
                revenue, and focus on what you do best - creating amazing food.
              </p>
              <div className='flex flex-col sm:flex-row gap-4'>
                <Button
                  variant='secondary'
                  size='large'
                  to='/register?role=restaurant'
                  icon={<Store size={20} />}
                >
                  Start Application
                </Button>
                <Button
                  variant='outline'
                  size='large'
                  className='border-white text-white hover:bg-white hover:text-primary-600'
                  onClick={() =>
                    document
                      .getElementById("contact")
                      .scrollIntoView({ behavior: "smooth" })
                  }
                  icon={<Phone size={20} />}
                >
                  Contact Sales
                </Button>
              </div>
            </div>
            <div className='relative'>
              <div className='bg-white bg-opacity-10 rounded-2xl p-8 backdrop-blur-sm'>
                <div className='grid grid-cols-2 gap-6'>
                  <div className='text-center'>
                    <div className='text-3xl font-bold'>5000+</div>
                    <div className='text-sm opacity-80'>
                      Partner Restaurants
                    </div>
                  </div>
                  <div className='text-center'>
                    <div className='text-3xl font-bold'>1M+</div>
                    <div className='text-sm opacity-80'>Monthly Orders</div>
                  </div>
                  <div className='text-center'>
                    <div className='text-3xl font-bold'>30%</div>
                    <div className='text-sm opacity-80'>
                      Average Revenue Increase
                    </div>
                  </div>
                  <div className='text-center'>
                    <div className='text-3xl font-bold'>4.8★</div>
                    <div className='text-sm opacity-80'>
                      Partner Satisfaction
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className='py-16'>
        <div className='container mx-auto px-4'>
          <div className='text-center mb-12'>
            <h2 className='text-3xl font-bold mb-4'>
              Why Choose Afghan Sofra?
            </h2>
            <p className='text-text-secondary max-w-2xl mx-auto'>
              We provide everything you need to succeed in the online food
              delivery market.
            </p>
          </div>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
            {benefits.map((benefit, index) => (
              <Card
                key={index}
                className='p-6 text-center hover:shadow-lg transition-shadow'
              >
                <div className='flex justify-center mb-4'>{benefit.icon}</div>
                <h3 className='text-xl font-semibold mb-3'>{benefit.title}</h3>
                <p className='text-text-secondary'>{benefit.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className='py-16 bg-gray-50'>
        <div className='container mx-auto px-4'>
          <div className='text-center mb-12'>
            <h2 className='text-3xl font-bold mb-4'>How to Get Started</h2>
            <p className='text-text-secondary max-w-2xl mx-auto'>
              Simple 5-step process to join our platform and start growing your
              business.
            </p>
          </div>
          <div className='grid grid-cols-1 md:grid-cols-5 gap-8'>
            {steps.map((step, index) => (
              <div key={index} className='text-center relative'>
                <div className='w-16 h-16 bg-primary-500 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4'>
                  {step.number}
                </div>
                <h3 className='text-lg font-semibold mb-2'>{step.title}</h3>
                <p className='text-text-secondary text-sm'>
                  {step.description}
                </p>
                {index < steps.length - 1 && (
                  <ArrowRight
                    className='hidden md:block absolute top-8 -right-4 text-primary-300'
                    size={24}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Requirements Section */}
      <section className='py-16'>
        <div className='container mx-auto px-4'>
          <div className='grid grid-cols-1 lg:grid-cols-2 gap-12 items-center'>
            <div>
              <h2 className='text-3xl font-bold mb-6'>Requirements to Join</h2>
              <p className='text-text-secondary mb-8'>
                To ensure the best experience for our customers, we have some
                basic requirements for our restaurant partners.
              </p>
              <div className='space-y-4'>
                {requirements.map((requirement, index) => (
                  <div key={index} className='flex items-start'>
                    <CheckCircle
                      className='text-green-500 mr-3 mt-1 flex-shrink-0'
                      size={20}
                    />
                    <span>{requirement}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className='bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl p-8'>
              <h3 className='text-2xl font-bold mb-6 text-center'>
                Commission Structure
              </h3>
              <div className='space-y-4'>
                <div className='flex justify-between items-center p-4 bg-white rounded-lg'>
                  <span className='font-medium'>Commission Rate</span>
                  <span className='text-primary-600 font-bold'>15-20%</span>
                </div>
                <div className='flex justify-between items-center p-4 bg-white rounded-lg'>
                  <span className='font-medium'>Setup Fee</span>
                  <span className='text-green-600 font-bold'>FREE</span>
                </div>
                <div className='flex justify-between items-center p-4 bg-white rounded-lg'>
                  <span className='font-medium'>Monthly Fee</span>
                  <span className='text-green-600 font-bold'>FREE</span>
                </div>
                <div className='flex justify-between items-center p-4 bg-white rounded-lg'>
                  <span className='font-medium'>Payment Cycle</span>
                  <span className='text-primary-600 font-bold'>Weekly</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className='py-16 bg-gray-50'>
        <div className='container mx-auto px-4'>
          <div className='text-center mb-12'>
            <h2 className='text-3xl font-bold mb-4'>What Our Partners Say</h2>
            <p className='text-text-secondary max-w-2xl mx-auto'>
              Hear from successful restaurant owners who have grown their
              business with Afghan Sofra.
            </p>
          </div>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
            {testimonials.map((testimonial, index) => (
              <Card key={index} className='p-6'>
                <div className='flex items-center mb-4'>
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className='w-12 h-12 rounded-full mr-4'
                  />
                  <div>
                    <h4 className='font-semibold'>{testimonial.name}</h4>
                    <p className='text-text-secondary text-sm'>
                      {testimonial.restaurant}
                    </p>
                  </div>
                </div>
                <div className='flex mb-3'>
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className='text-yellow-400 fill-current'
                      size={16}
                    />
                  ))}
                </div>
                <p className='text-text-secondary italic'>
                  "{testimonial.text}"
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className='py-16'>
        <div className='container mx-auto px-4'>
          <div className='text-center mb-12'>
            <h2 className='text-3xl font-bold mb-4'>
              Frequently Asked Questions
            </h2>
            <p className='text-text-secondary max-w-2xl mx-auto'>
              Get answers to common questions about partnering with Afghan
              Sofra.
            </p>
          </div>
          <div className='max-w-3xl mx-auto space-y-4'>
            {faqs.map((faq, index) => (
              <Card key={index} className='p-6'>
                <h3 className='text-lg font-semibold mb-3'>{faq.question}</h3>
                <p className='text-text-secondary'>{faq.answer}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id='contact' className='py-16 bg-primary-600 text-white'>
        <div className='container mx-auto px-4'>
          <div className='text-center mb-12'>
            <h2 className='text-3xl font-bold mb-4'>Ready to Get Started?</h2>
            <p className='text-xl opacity-90 max-w-2xl mx-auto'>
              Join thousands of successful restaurants on Afghan Sofra. Start
              your application today or contact our sales team for more
              information.
            </p>
          </div>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-12 max-w-4xl mx-auto'>
            <div>
              <h3 className='text-2xl font-bold mb-6'>
                Contact Our Sales Team
              </h3>
              <div className='space-y-4'>
                <div className='flex items-center'>
                  <Phone className='mr-3' size={20} />
                  <span>+****************</span>
                </div>
                <div className='flex items-center'>
                  <Mail className='mr-3' size={20} />
                  <span><EMAIL></span>
                </div>
                <div className='flex items-center'>
                  <MapPin className='mr-3' size={20} />
                  <span>123 Business Ave, City, State 12345</span>
                </div>
                <div className='flex items-center'>
                  <Clock className='mr-3' size={20} />
                  <span>Mon-Fri: 9 AM - 6 PM</span>
                </div>
              </div>
            </div>
            <div className='bg-white bg-opacity-10 rounded-2xl p-8 backdrop-blur-sm'>
              <h3 className='text-2xl font-bold mb-6'>Quick Application</h3>
              <p className='mb-6 opacity-90'>
                Ready to start? Fill out our quick application form and we'll
                get back to you within 24 hours.
              </p>
              <div className='space-y-4'>
                <Button
                  variant='secondary'
                  size='large'
                  fullWidth
                  to='/register?role=restaurant'
                  icon={<Store size={20} />}
                >
                  Start Application Now
                </Button>
                <Button
                  variant='outline'
                  size='large'
                  fullWidth
                  className='border-white text-white hover:bg-white hover:text-primary-600'
                  to='/contact'
                  icon={<Mail size={20} />}
                >
                  Contact Support
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default RestaurantPartner;
