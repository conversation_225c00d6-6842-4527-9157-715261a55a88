/**
 * useAddresses Hook - Simplified and robust address management
 * Uses the new AddressService for all operations
 */

import { useState, useEffect, useCallback } from 'react';
import addressService from '../services/addressService';

export const useAddresses = () => {
  const [addresses, setAddresses] = useState([]);
  const [selectedAddress, setSelectedAddressState] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize and load addresses
  const initialize = useCallback(async () => {
    if (isInitialized) return;
    
    try {
      setLoading(true);
      setError(null);
      
      console.log('🏠 useAddresses: Initializing...');
      
      // Initialize the service
      await addressService.initialize();
      
      // Load addresses
      const loadedAddresses = await addressService.getAddresses();
      setAddresses(loadedAddresses);
      
      // Load selected address
      const selected = addressService.getSelectedAddress();
      setSelectedAddressState(selected);
      
      setIsInitialized(true);
      console.log(`✅ useAddresses: Initialized with ${loadedAddresses.length} addresses`);
      
    } catch (err) {
      console.error('❌ useAddresses: Initialization failed:', err);
      setError('Failed to load addresses');
    } finally {
      setLoading(false);
    }
  }, [isInitialized]);

  // Refresh addresses from backend
  const refreshAddresses = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const loadedAddresses = await addressService.syncWithBackend();
      setAddresses(loadedAddresses);
      
      // Verify selected address still exists
      const selected = addressService.getSelectedAddress();
      if (selected) {
        const stillExists = loadedAddresses.find(addr => 
          addr.id === selected.id || addr.backendId === selected.backendId
        );
        if (!stillExists) {
          addressService.clearSelectedAddress();
          setSelectedAddressState(null);
        }
      }
      
    } catch (err) {
      console.error('❌ useAddresses: Refresh failed:', err);
      setError('Failed to refresh addresses');
    } finally {
      setLoading(false);
    }
  }, []);

  // Create new address
  const createAddress = useCallback(async (addressData) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await addressService.createAddress(addressData);
      
      if (result.success) {
        // Refresh addresses to get updated list
        const updatedAddresses = await addressService.getAddresses();
        setAddresses(updatedAddresses);
        
        // Auto-select if it's the first address
        if (updatedAddresses.length === 1) {
          await selectAddress(result.data);
        }
        
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      console.error('❌ useAddresses: Create failed:', err);
      const errorMsg = 'Failed to create address';
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Update address
  const updateAddress = useCallback(async (id, updates) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await addressService.updateAddress(id, updates);
      
      if (result.success) {
        // Refresh addresses
        const updatedAddresses = await addressService.getAddresses();
        setAddresses(updatedAddresses);
        
        // Update selected address if it was the one updated
        const selected = addressService.getSelectedAddress();
        if (selected && (selected.id === id || selected.backendId === id)) {
          setSelectedAddressState(result.data);
        }
        
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      console.error('❌ useAddresses: Update failed:', err);
      const errorMsg = 'Failed to update address';
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete address
  const deleteAddress = useCallback(async (id) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await addressService.deleteAddress(id);
      
      if (result.success) {
        // Refresh addresses
        const updatedAddresses = await addressService.getAddresses();
        setAddresses(updatedAddresses);
        
        // Clear selected address if it was deleted
        const selected = addressService.getSelectedAddress();
        if (!selected || updatedAddresses.length === 0) {
          setSelectedAddressState(null);
        }
        
        return result;
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      console.error('❌ useAddresses: Delete failed:', err);
      const errorMsg = 'Failed to delete address';
      setError(errorMsg);
      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  }, []);

  // Select address
  const selectAddress = useCallback(async (address) => {
    try {
      const validAddress = await addressService.setSelectedAddress(address);
      setSelectedAddressState(validAddress);
      return validAddress;
    } catch (err) {
      console.error('❌ useAddresses: Select failed:', err);
      setError('Failed to select address');
      return null;
    }
  }, []);

  // Clear selected address
  const clearSelectedAddress = useCallback(() => {
    addressService.clearSelectedAddress();
    setSelectedAddressState(null);
  }, []);

  // Get valid address ID for orders
  const getOrderAddressId = useCallback(async () => {
    try {
      return await addressService.getValidOrderAddressId();
    } catch (err) {
      console.error('❌ useAddresses: Failed to get order address ID:', err);
      return null;
    }
  }, []);

  // Validate address for orders
  const validateAddressForOrder = useCallback((address) => {
    return addressService.validateAddressForOrder(address);
  }, []);

  // Clear all data (for debugging)
  const clearAll = useCallback(() => {
    addressService.clearAll();
    setAddresses([]);
    setSelectedAddressState(null);
    setIsInitialized(false);
    setError(null);
  }, []);

  // Initialize on mount
  useEffect(() => {
    initialize();
  }, [initialize]);

  // Auto-refresh every 5 minutes to keep data fresh
  useEffect(() => {
    if (!isInitialized) return;
    
    const interval = setInterval(() => {
      console.log('🔄 useAddresses: Auto-refreshing addresses...');
      refreshAddresses();
    }, 5 * 60 * 1000); // 5 minutes
    
    return () => clearInterval(interval);
  }, [isInitialized, refreshAddresses]);

  return {
    // State
    addresses,
    selectedAddress,
    loading,
    error,
    isInitialized,
    
    // Actions
    refreshAddresses,
    createAddress,
    updateAddress,
    deleteAddress,
    selectAddress,
    clearSelectedAddress,
    getOrderAddressId,
    validateAddressForOrder,
    clearAll,
    
    // Computed properties
    hasAddresses: addresses.length > 0,
    hasSelectedAddress: !!selectedAddress,
    selectedAddressId: selectedAddress?.backendId || selectedAddress?.id,
    
    // Helper methods
    getAddressById: (id) => addresses.find(addr => addr.id === id || addr.backendId === id),
    isAddressSelected: (id) => selectedAddress && (selectedAddress.id === id || selectedAddress.backendId === id)
  };
};

export default useAddresses;
