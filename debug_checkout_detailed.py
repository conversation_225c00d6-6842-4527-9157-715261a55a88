#!/usr/bin/env python3
"""
Detailed debugging of checkout issue
"""

import os
import sys
import django

# Add the Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from restaurant.models import Address, Restaurant
from orders.models import Order

User = get_user_model()

def debug_checkout_detailed():
    """Detailed debugging of checkout state"""
    
    print("🔍 DETAILED CHECKOUT DEBUGGING")
    print("=" * 60)
    
    # 1. Check test customer
    try:
        test_customer = User.objects.get(email='<EMAIL>')
        print(f"✅ Test Customer Found:")
        print(f"   Email: {test_customer.email}")
        print(f"   Name: {test_customer.name}")
        print(f"   Role: {test_customer.role}")
        print(f"   Verified: {test_customer.is_verified}")
        print(f"   Phone: {test_customer.phone}")
    except User.DoesNotExist:
        print("❌ Test customer not found!")
        return
    
    # 2. Check customer addresses
    addresses = Address.objects.filter(user=test_customer)
    print(f"\n📍 Customer Addresses ({addresses.count()}):")
    for addr in addresses:
        print(f"   ID: {addr.id}")
        print(f"   Address: {addr.street}, {addr.city}, {addr.state}")
        print(f"   Country: {addr.country}")
        print(f"   Coordinates: {addr.latitude}, {addr.longitude}")
        print(f"   ---")
    
    # 3. Check if there are restaurants
    restaurants = Restaurant.objects.filter(is_active=True)
    print(f"\n🏪 Active Restaurants ({restaurants.count()}):")
    for rest in restaurants[:3]:
        print(f"   ID: {rest.id} - {rest.name}")
        print(f"   Owner: {rest.owner.email}")
        print(f"   Active: {rest.is_active}")
        print(f"   ---")
    
    # 4. Check recent orders to see the pattern
    recent_orders = Order.objects.all().order_by('-created_at')[:3]
    print(f"\n📦 Recent Orders ({recent_orders.count()}):")
    for order in recent_orders:
        print(f"   Order ID: {order.id}")
        print(f"   Customer: {order.customer.email}")
        print(f"   Restaurant: {order.restaurant.name}")
        print(f"   Delivery Address ID: {order.delivery_address.id}")
        print(f"   Status: {order.status}")
        print(f"   ---")
    
    # 5. Create a simple test to verify address API
    print(f"\n🧪 Testing Address API Response:")
    print(f"   Customer ID: {test_customer.id}")
    print(f"   Available Address IDs: {[addr.id for addr in addresses]}")
    
    if addresses.exists():
        first_addr = addresses.first()
        print(f"\n✅ RECOMMENDED TEST DATA:")
        print(f"   Login as: {test_customer.email}")
        print(f"   Password: testpass123")
        print(f"   Use Address ID: {first_addr.id}")
        print(f"   Address: {first_addr.street}, {first_addr.city}")
        
        # Check if this address format matches what frontend expects
        print(f"\n🔧 Frontend Address Object Should Look Like:")
        print(f"   {{")
        print(f"     id: {first_addr.id},")
        print(f"     backendId: {first_addr.id},")
        print(f"     type: 'saved',")
        print(f"     address: '{first_addr.street}, {first_addr.city}, {first_addr.state}',")
        print(f"     street: '{first_addr.street}',")
        print(f"     city: '{first_addr.city}',")
        print(f"     state: '{first_addr.state}',")
        print(f"     country: '{first_addr.country}'")
        print(f"   }}")
    
    print(f"\n🎯 DEBUGGING STEPS:")
    print(f"   1. Open browser console (F12)")
    print(f"   2. Go to: http://localhost:5174/checkout")
    print(f"   3. Check console for errors")
    print(f"   4. In console, type: localStorage.getItem('afghanSofraCart')")
    print(f"   5. Check if cart has items and restaurant ID")
    print(f"   6. Look for AddressManager component")
    print(f"   7. Check if addresses are loading")
    
    print(f"\n🔍 COMMON ISSUES:")
    print(f"   ❌ Cart is empty")
    print(f"   ❌ User not logged in")
    print(f"   ❌ No saved addresses")
    print(f"   ❌ Address not selected")
    print(f"   ❌ AddressManager component not loading addresses")
    print(f"   ❌ JavaScript errors in console")

if __name__ == '__main__':
    debug_checkout_detailed()
