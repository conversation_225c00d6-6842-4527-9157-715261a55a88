import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import Input from "../../components/common/Input";
import Button from "../../components/common/Button";
import { Mail, Lock, AlertCircle, CheckCircle } from "lucide-react";
import toast from "../../utils/toast";
import useFormPersistence from "../../hooks/useFormPersistence";

const Login = () => {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm({
    mode: "onChange", // Validate on change for better UX
  });
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [backendStatus, setBackendStatus] = useState(null);
  const from = location.state?.from?.pathname || "/";

  // Form persistence
  const watchedValues = watch();
  const { clearFormData } = useFormPersistence(
    watchedValues,
    setValue,
    "login_form_data",
    ["password"]
  );

  // Show success message from location state if available
  React.useEffect(() => {
    if (location.state?.message) {
      toast.success(location.state.message);
    }
  }, [location.state?.message]);

  // Test backend connection
  const testBackendConnection = async () => {
    try {
      setBackendStatus("testing");
      const response = await fetch("http://127.0.0.1:8000/api/auth/login/", {
        method: "OPTIONS",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok || response.status === 405) {
        setBackendStatus("connected");
        toast.success("Backend is connected!");
      } else {
        setBackendStatus("error");
        toast.error("Backend connection error");
      }
    } catch (error) {
      console.error("Backend connection test failed:", error);
      setBackendStatus("disconnected");
      toast.error(
        "Cannot connect to backend. Make sure it's running on port 8000."
      );
    }
  };

  // Auto-fill test credentials
  const fillTestCredentials = (userType) => {
    const credentials = {
      customer: { username: "customer123", password: "customer123" },
      restaurant: { username: "restaurant_owner1", password: "restaurant123" },
      delivery_agent: { username: "delivery_agent1", password: "delivery123" },
      admin: { username: "admin", password: "admin123" },
    };

    const creds = credentials[userType];
    if (creds) {
      setValue("username", creds.username);
      setValue("password", creds.password);
      toast.success(`Filled ${userType} credentials`);
    }
  };

  // Handle login form submission
  const onSubmit = async (data) => {
    setLoading(true);

    try {
      console.log("🔐 Attempting login for:", data.username);

      // Show loading toast
      const loadingToast = toast.loading("Signing you in...");

      const result = await login(data.username, data.password);

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (result.success) {
        console.log("✅ Login successful, redirecting user");
        console.log("✅ User data:", result.user);
        console.log("✅ User role:", result.user.role);

        // Clear saved form data on successful login
        clearFormData();

        // Show success toast
        toast.auth.loginSuccess(result.user.name || result.user.username);

        // Navigate based on user role
        const roleRoutes = {
          customer: from,
          restaurant: "/restaurant",
          delivery_agent: "/delivery",
          admin: "/admin",
        };

        const route = roleRoutes[result.user.role] || from;
        console.log("✅ Redirecting to:", route);
        navigate(route);
      } else {
        console.log("❌ Login failed:", result.error);

        // Handle specific field errors
        if (result.errors && typeof result.errors === "object") {
          // Show specific field errors
          Object.entries(result.errors).forEach(([field, message]) => {
            if (field === "non_field_errors") {
              toast.error(message);
            } else {
              toast.error(`${field}: ${message}`);
            }
          });
        } else {
          toast.auth.loginError(
            result.error || "Login failed. Please try again."
          );
        }
      }
    } catch (err) {
      console.error("❌ Login error:", err);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='animate-fade-in'>
      <div className='text-center mb-8'>
        <h1 className='text-2xl font-poppins font-bold text-text-primary mb-2'>
          Welcome Back
        </h1>
        <p className='text-text-secondary mb-3'>
          Sign in to your account to continue
        </p>

        {/* User Type Indicators */}
        <div className='flex flex-wrap justify-center gap-2 text-xs'>
          <span className='px-2 py-1 bg-blue-100 text-blue-700 rounded-full'>
            👤 Customers
          </span>
          <span className='px-2 py-1 bg-green-100 text-green-700 rounded-full'>
            🏪 Restaurants
          </span>
          <span className='px-2 py-1 bg-orange-100 text-orange-700 rounded-full'>
            🚚 Delivery Agents
          </span>
          <span className='px-2 py-1 bg-purple-100 text-purple-700 rounded-full'>
            ⚙️ Admins
          </span>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
        <div>
          <Input
            label='Username'
            type='text'
            placeholder='Enter your username'
            icon={<Mail size={18} />}
            error={errors.username?.message}
            required
            {...register("username", {
              required: "Username is required",
              minLength: {
                value: 3,
                message: "Username must be at least 3 characters long",
              },
              validate: (value) => {
                const trimmed = value?.trim();
                if (!trimmed) return "Username cannot be empty";
                if (trimmed.length < 3)
                  return "Username must be at least 3 characters long";
                return true;
              },
            })}
          />
        </div>
        <div>
          <Input
            label='Password'
            type='password'
            placeholder='Enter your password'
            icon={<Lock size={18} />}
            error={errors.password?.message}
            required
            {...register("password", {
              required: "Password is required",
              minLength: {
                value: 6,
                message: "Password must be at least 6 characters long",
              },
              validate: (value) => {
                if (!value) return "Password is required";
                if (value.length < 6)
                  return "Password must be at least 6 characters long";
                return true;
              },
            })}
          />
        </div>

        <div className='flex justify-end'>
          <Link
            to='/forgot-password'
            className='text-sm text-primary-500 hover:text-primary-600 transition-colors'
          >
            Forgot password?
          </Link>
        </div>

        <Button type='submit' variant='primary' fullWidth loading={loading}>
          Sign In
        </Button>
      </form>

      <div className='text-center text-sm text-text-secondary mt-6 space-y-2'>
        <div>
          Don't have an account?{" "}
          <Link
            to='/register'
            className='text-primary-500 hover:text-primary-600 font-medium'
          >
            Create One
          </Link>
        </div>
        <div>
          Need to verify your email?{" "}
          <Link
            to='/verify-email'
            className='text-primary-500 hover:text-primary-600 font-medium'
          >
            Verify Email
          </Link>
        </div>
      </div>

      {/* Development Test Credentials - Only show in development */}
      {import.meta.env.DEV && (
        <div className='mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200'>
          <h4 className='text-sm font-bold text-gray-700 mb-3 text-center'>
            🔧 Development Test Accounts
          </h4>
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-3 text-xs'>
            <div className='p-3 bg-blue-50 rounded border border-blue-200'>
              <p className='font-semibold text-blue-700 mb-1'>👤 Customer</p>
              <p>Username: customer123</p>
              <p>Password: customer123</p>
              <button
                onClick={() => fillTestCredentials("customer")}
                className='mt-2 px-3 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 transition-colors'
              >
                Auto-fill
              </button>
            </div>
            <div className='p-3 bg-green-50 rounded border border-green-200'>
              <p className='font-semibold text-green-700 mb-1'>🏪 Restaurant</p>
              <p>Username: restaurant_owner1</p>
              <p>Password: restaurant123</p>
              <button
                onClick={() => fillTestCredentials("restaurant")}
                className='mt-2 px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700 transition-colors'
              >
                Auto-fill
              </button>
            </div>
            <div className='p-3 bg-orange-50 rounded border border-orange-200'>
              <p className='font-semibold text-orange-700 mb-1'>
                🚚 Delivery Agent
              </p>
              <p>Username: delivery_agent1</p>
              <p>Password: delivery123</p>
              <button
                onClick={() => fillTestCredentials("delivery_agent")}
                className='mt-2 px-3 py-1 bg-orange-600 text-white text-xs rounded hover:bg-orange-700 transition-colors'
              >
                Auto-fill
              </button>
            </div>
            <div className='p-3 bg-purple-50 rounded border border-purple-200'>
              <p className='font-semibold text-purple-700 mb-1'>⚙️ Admin</p>
              <p>Username: admin</p>
              <p>Password: admin123</p>
              <button
                onClick={() => fillTestCredentials("admin")}
                className='mt-2 px-3 py-1 bg-purple-600 text-white text-xs rounded hover:bg-purple-700 transition-colors'
              >
                Auto-fill
              </button>
            </div>
          </div>
          <div className='mt-4 pt-3 border-t border-gray-200'>
            <div className='flex items-center justify-center space-x-3 mb-2'>
              <button
                onClick={testBackendConnection}
                className='text-xs text-gray-500 hover:text-blue-600 transition-colors flex items-center space-x-1'
                disabled={backendStatus === "testing"}
              >
                <div
                  className={`w-2 h-2 rounded-full ${
                    backendStatus === "connected"
                      ? "bg-green-500"
                      : backendStatus === "disconnected"
                      ? "bg-red-500"
                      : backendStatus === "error"
                      ? "bg-yellow-500"
                      : backendStatus === "testing"
                      ? "bg-blue-500 animate-pulse"
                      : "bg-gray-400"
                  }`}
                ></div>
                <span>
                  {backendStatus === "connected"
                    ? "Backend Connected"
                    : backendStatus === "disconnected"
                    ? "Backend Disconnected"
                    : backendStatus === "error"
                    ? "Backend Error"
                    : backendStatus === "testing"
                    ? "Testing..."
                    : "Test Backend Connection"}
                </span>
              </button>
            </div>
            <p className='text-center text-gray-500 text-xs'>
              ⚠️ These are test accounts for development only
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Login;
