import React, { useState } from "react";
import {
  Upload,
  FileText,
  Camera,
  CheckCircle,
  AlertCircle,
  X,
} from "lucide-react";

const DocumentUploadStep = ({ data, onChange, errors }) => {
  const [dragOver, setDragOver] = useState(null);
  const [previews, setPreviews] = useState({});

  const documentTypes = [
    {
      key: "nationalId",
      label: "National ID / Passport",
      description: "Clear photo of your government-issued ID",
      required: true,
      accept: "image/*,.pdf",
    },
    {
      key: "drivingLicense",
      label: "Driving License",
      description: "Valid driving license (front and back)",
      required: true,
      accept: "image/*,.pdf",
    },
    {
      key: "vehicleRegistration",
      label: "Vehicle Registration",
      description: "Vehicle registration certificate",
      required: false,
      accept: "image/*,.pdf",
    },
    {
      key: "insurance",
      label: "Vehicle Insurance",
      description: "Current vehicle insurance certificate",
      required: false,
      accept: "image/*,.pdf",
    },
    {
      key: "profilePhoto",
      label: "Profile Photo",
      description: "Clear headshot photo for your profile",
      required: true,
      accept: "image/*",
    },
  ];

  const handleFileSelect = (key, file) => {
    if (file) {
      onChange("documents", key, file);

      // Create preview for images
      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPreviews((prev) => ({
            ...prev,
            [key]: e.target.result,
          }));
        };
        reader.readAsDataURL(file);
      }
    }
  };

  const handleDragOver = (e, key) => {
    e.preventDefault();
    setDragOver(key);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(null);
  };

  const handleDrop = (e, key) => {
    e.preventDefault();
    setDragOver(null);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(key, files[0]);
    }
  };

  const removeFile = (key) => {
    onChange("documents", key, null);
    setPreviews((prev) => {
      const newPreviews = { ...prev };
      delete newPreviews[key];
      return newPreviews;
    });
  };

  const DocumentUploadCard = ({ doc }) => {
    const hasFile = data[doc.key];
    const hasError = errors[doc.key];
    const preview = previews[doc.key];

    return (
      <div
        className={`
        border-2 border-dashed rounded-xl p-6 transition-all duration-300 hover:shadow-lg
        ${
          dragOver === doc.key
            ? "border-primary-500 bg-primary-50 shadow-lg scale-105"
            : hasError
            ? "border-red-300 bg-red-50"
            : hasFile
            ? "border-accent-green bg-green-50 shadow-md"
            : "border-gray-300 hover:border-primary-300 hover:bg-gray-50"
        }
      `}
      >
        <div
          onDragOver={(e) => handleDragOver(e, doc.key)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, doc.key)}
          className='text-center'
        >
          {hasFile ? (
            <div className='space-y-3'>
              {preview ? (
                <div className='relative inline-block'>
                  <img
                    src={preview}
                    alt='Preview'
                    className='w-24 h-24 object-cover rounded-lg mx-auto'
                  />
                  <button
                    onClick={() => removeFile(doc.key)}
                    className='absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600'
                  >
                    <X className='h-3 w-3' />
                  </button>
                </div>
              ) : (
                <div className='flex items-center justify-center'>
                  <FileText className='h-12 w-12 text-green-500' />
                  <button
                    onClick={() => removeFile(doc.key)}
                    className='ml-2 text-red-500 hover:text-red-700'
                  >
                    <X className='h-4 w-4' />
                  </button>
                </div>
              )}
              <div className='flex items-center justify-center text-green-600'>
                <CheckCircle className='h-4 w-4 mr-1' />
                <span className='text-sm font-medium'>
                  {data[doc.key].name}
                </span>
              </div>
            </div>
          ) : (
            <div className='space-y-3'>
              <div className='flex justify-center'>
                {doc.key === "profilePhoto" ? (
                  <Camera className='h-12 w-12 text-gray-400' />
                ) : (
                  <Upload className='h-12 w-12 text-gray-400' />
                )}
              </div>
              <div>
                <p className='text-sm font-medium text-gray-900'>
                  {doc.label}
                  {doc.required && <span className='text-red-500 ml-1'>*</span>}
                </p>
                <p className='text-xs text-gray-500 mt-1'>{doc.description}</p>
              </div>
              <div className='flex justify-center'>
                <label className='cursor-pointer bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-xl text-sm font-medium hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105'>
                  Choose File
                  <input
                    type='file'
                    accept={doc.accept}
                    onChange={(e) =>
                      handleFileSelect(doc.key, e.target.files[0])
                    }
                    className='hidden'
                  />
                </label>
              </div>
              <p className='text-xs text-gray-400'>
                or drag and drop your file here
              </p>
            </div>
          )}
        </div>

        {hasError && (
          <div className='mt-3 flex items-center text-red-600'>
            <AlertCircle className='h-4 w-4 mr-1' />
            <span className='text-sm'>{hasError}</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className='space-y-6'>
      <div className='bg-blue-50 border border-blue-200 rounded-lg p-4'>
        <div className='flex items-start'>
          <FileText className='h-5 w-5 text-blue-600 mt-0.5 mr-3' />
          <div>
            <h3 className='text-sm font-medium text-blue-800'>
              Document Upload Guidelines
            </h3>
            <ul className='mt-2 text-sm text-blue-700 space-y-1'>
              <li>• Ensure all documents are clear and readable</li>
              <li>• Upload high-quality images or PDF files</li>
              <li>• All information should be visible and not cut off</li>
              <li>• Documents should be current and not expired</li>
            </ul>
          </div>
        </div>
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
        {documentTypes.map((doc) => (
          <DocumentUploadCard key={doc.key} doc={doc} />
        ))}
      </div>

      <div className='bg-yellow-50 border border-yellow-200 rounded-lg p-4'>
        <div className='flex items-start'>
          <AlertCircle className='h-5 w-5 text-yellow-600 mt-0.5 mr-3' />
          <div>
            <h3 className='text-sm font-medium text-yellow-800'>
              Privacy & Security
            </h3>
            <p className='mt-1 text-sm text-yellow-700'>
              Your documents are encrypted and stored securely. They will only
              be used for verification purposes and will not be shared with
              third parties.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentUploadStep;
