import { useEffect } from 'react';

/**
 * Custom hook for persisting form data to sessionStorage
 * @param {Object} watchedValues - Form values from react-hook-form watch()
 * @param {Function} setValue - setValue function from react-hook-form
 * @param {string} storageKey - Unique key for storing form data
 * @param {Array} excludeFields - Fields to exclude from persistence (e.g., passwords)
 * @param {Array} dependencies - Additional dependencies for useEffect
 */
export const useFormPersistence = (
  watchedValues,
  setValue,
  storageKey,
  excludeFields = ['password', 'confirmPassword', 'newPassword'],
  dependencies = []
) => {
  // Load saved form data on component mount
  useEffect(() => {
    try {
      const savedData = sessionStorage.getItem(storageKey);
      if (savedData) {
        const parsedData = JSON.parse(savedData);
        // Restore form values except excluded fields
        Object.keys(parsedData).forEach((key) => {
          if (!excludeFields.includes(key)) {
            setValue(key, parsedData[key]);
          }
        });
      }
    } catch (error) {
      console.error(`Error loading saved form data for ${storageKey}:`, error);
      sessionStorage.removeItem(storageKey);
    }
  }, [setValue, storageKey, ...dependencies]);

  // Save form data to sessionStorage on every change (except excluded fields)
  useEffect(() => {
    try {
      // Create a copy without sensitive data
      const dataToSave = { ...watchedValues };
      excludeFields.forEach(field => {
        delete dataToSave[field];
      });
      
      // Only save if there's actual data
      if (Object.values(dataToSave).some(value => 
        value && 
        (typeof value === 'string' ? value.trim() !== '' : true)
      )) {
        sessionStorage.setItem(storageKey, JSON.stringify(dataToSave));
      }
    } catch (error) {
      console.error(`Error saving form data for ${storageKey}:`, error);
    }
  }, [watchedValues, storageKey, excludeFields]);

  // Function to clear saved form data
  const clearFormData = () => {
    try {
      sessionStorage.removeItem(storageKey);
    } catch (error) {
      console.error(`Error clearing form data for ${storageKey}:`, error);
    }
  };

  return { clearFormData };
};

export default useFormPersistence;
