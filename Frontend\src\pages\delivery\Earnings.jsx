import React, { useState, useEffect } from "react";
import {
  Calendar,
  DollarSign,
  TruckIcon,
  Clock,
  ChevronDown,
  ChevronUp,
  Download,
  Filter,
  ArrowUpRight,
  ArrowDownRight,
  CheckCircle,
  AlertCircle,
  X,
  FileText,
} from "lucide-react";
import {
  mockDeliveryAgents,
  mockDeliveryEarnings,
  mockDeliveryOrders,
} from "../../data/deliveryAgents";
import { useAuth } from "../../context/AuthContext";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";

function DeliveryEarnings() {
  const { user } = useAuth();
  const [agent, setAgent] = useState(null);
  const [earnings, setEarnings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState("week");
  const [dateRange, setDateRange] = useState({
    start: new Date(new Date().setDate(new Date().getDate() - 7)),
    end: new Date(),
  });
  const [expandedEarningId, setExpandedEarningId] = useState(null);
  const [stats, setStats] = useState({
    totalEarnings: 0,
    totalDeliveries: 0,
    averagePerDelivery: 0,
    totalHours: 0,
    totalTips: 0,
    totalBonuses: 0,
  });

  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [modalMessage, setModalMessage] = useState("");
  const [modalType, setModalType] = useState("success");
  const [downloadLoading, setDownloadLoading] = useState(false);

  // Filtered earnings based on selected period
  const [filteredEarnings, setFilteredEarnings] = useState([]);
  const [filteredStats, setFilteredStats] = useState({
    totalEarnings: 0,
    totalDeliveries: 0,
    averagePerDelivery: 0,
    totalHours: 0,
    totalTips: 0,
    totalBonuses: 0,
  });

  useEffect(() => {
    // Simulate API call to get agent data and earnings
    setTimeout(() => {
      // Find agent by user ID
      const foundAgent =
        mockDeliveryAgents.find((a) => a.userId === user.id) ||
        mockDeliveryAgents[0];
      setAgent(foundAgent);

      // Get earnings for this agent
      const agentEarnings = mockDeliveryEarnings.filter(
        (e) => e.agentId === foundAgent.id
      );
      setEarnings(agentEarnings);

      // Calculate stats
      const totalEarnings = agentEarnings.reduce(
        (sum, e) => sum + e.totalEarnings,
        0
      );
      const totalDeliveries = agentEarnings.reduce(
        (sum, e) => sum + (e.deliveries || e.totalOrders || 0),
        0
      );
      const totalTips = agentEarnings.reduce(
        (sum, e) => sum + (e.tips || 0),
        0
      );
      const totalBonuses = agentEarnings.reduce(
        (sum, e) =>
          sum +
          ((e.bonuses || 0) + (e.distanceBonus || 0) + (e.timeBonus || 0)),
        0
      );

      setStats({
        totalEarnings,
        totalDeliveries,
        averagePerDelivery:
          totalDeliveries > 0 ? totalEarnings / totalDeliveries : 0,
        totalHours: totalDeliveries * 0.5, // Assuming average 30 minutes per delivery
        totalTips,
        totalBonuses,
      });

      // Set initial filtered earnings based on default period (week)
      filterEarningsByPeriod(agentEarnings, "week");

      setLoading(false);
    }, 1000);
  }, [user]);

  // Filter earnings based on selected period
  const filterEarningsByPeriod = (earningsData, period) => {
    const end = new Date();
    let start = new Date();

    switch (period) {
      case "today":
        start = new Date(end.getFullYear(), end.getMonth(), end.getDate());
        break;
      case "week":
        start = new Date(end);
        start.setDate(end.getDate() - 7);
        break;
      case "month":
        start = new Date(end);
        start.setMonth(end.getMonth() - 1);
        break;
      case "year":
        start = new Date(end);
        start.setFullYear(end.getFullYear() - 1);
        break;
      default:
        start = new Date(end);
        start.setDate(end.getDate() - 7);
    }

    // Filter earnings within the date range
    const filtered = earningsData.filter((earning) => {
      const earningDate = new Date(earning.date);
      return earningDate >= start && earningDate <= end;
    });

    setFilteredEarnings(filtered);

    // Calculate filtered stats
    const totalEarnings = filtered.reduce((sum, e) => sum + e.totalEarnings, 0);
    const totalDeliveries = filtered.reduce((sum, e) => sum + e.deliveries, 0);
    const totalTips = filtered.reduce((sum, e) => sum + e.tips, 0);
    const totalBonuses = filtered.reduce((sum, e) => sum + e.bonuses, 0);

    setFilteredStats({
      totalEarnings,
      totalDeliveries,
      averagePerDelivery:
        totalDeliveries > 0 ? totalEarnings / totalDeliveries : 0,
      totalHours: totalDeliveries * 0.5, // Assuming average 30 minutes per delivery
      totalTips,
      totalBonuses,
    });

    setDateRange({ start, end });
  };

  const handlePeriodChange = (period) => {
    setSelectedPeriod(period);
    filterEarningsByPeriod(earnings, period);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount) => {
    return `$${(amount || 0).toFixed(2)}`;
  };

  const toggleEarningExpand = (earningId) => {
    setExpandedEarningId(expandedEarningId === earningId ? null : earningId);
  };

  const getDeliveryDetails = (orderId) => {
    return mockDeliveryOrders.find((o) => o.id === orderId) || null;
  };

  const showSuccessModal = (message) => {
    setModalMessage(message);
    setModalType("success");
    setShowModal(true);

    // Auto close after 3 seconds
    setTimeout(() => {
      setShowModal(false);
    }, 3000);
  };

  const showErrorModal = (message) => {
    setModalMessage(message);
    setModalType("error");
    setShowModal(true);
  };

  // Generate report data based on filtered earnings
  const generateReportData = () => {
    // Get period name for the report title
    const periodText =
      selectedPeriod === "today"
        ? "Today"
        : selectedPeriod === "week"
        ? "This Week"
        : selectedPeriod === "month"
        ? "This Month"
        : "This Year";

    // Format date range for the report
    const startDateFormatted = formatDate(dateRange.start);
    const endDateFormatted = formatDate(dateRange.end);

    // Create report data object
    const reportData = {
      title: `Earnings Report - ${periodText}`,
      dateRange: `${startDateFormatted} to ${endDateFormatted}`,
      agentName: agent?.name || "Unknown Agent",
      agentId: agent?.id || "Unknown",
      summary: {
        totalEarnings: filteredStats.totalEarnings,
        totalDeliveries: filteredStats.totalDeliveries,
        averagePerDelivery: filteredStats.averagePerDelivery,
        totalHours: filteredStats.totalHours,
        totalTips: filteredStats.totalTips,
        totalBonuses: filteredStats.totalBonuses,
      },
      earnings: filteredEarnings.map((earning) => ({
        date: formatDate(earning.date),
        deliveries: earning.deliveries || earning.totalOrders || 0,
        totalEarnings: earning.totalEarnings,
        tips: earning.tips || 0,
        bonuses:
          (earning.bonuses || 0) +
          (earning.distanceBonus || 0) +
          (earning.timeBonus || 0),
        baseEarnings:
          earning.totalEarnings -
          (earning.tips || 0) -
          ((earning.bonuses || 0) +
            (earning.distanceBonus || 0) +
            (earning.timeBonus || 0)),
      })),
    };

    return reportData;
  };

  const handleDownloadReport = () => {
    setDownloadLoading(true);

    // Simulate API call to generate and download report
    setTimeout(() => {
      try {
        // Generate report data based on selected period
        const reportData = generateReportData();

        // In a real app, you would send this data to the server to generate a PDF/Excel
        console.log("Generating report with data:", reportData);

        // For demo purposes, let's simulate a successful download
        const periodText =
          selectedPeriod === "today"
            ? "Today"
            : selectedPeriod === "week"
            ? "This Week"
            : selectedPeriod === "month"
            ? "This Month"
            : "This Year";

        showSuccessModal(
          `Earnings report for ${periodText} has been downloaded successfully!`
        );

        // In a real app, you would trigger the file download here
        // For example:
        // const jsonString = JSON.stringify(reportData, null, 2);
        // const blob = new Blob([jsonString], { type: 'application/json' });
        // const url = window.URL.createObjectURL(blob);
        // const link = document.createElement('a');
        // link.href = url;
        // link.setAttribute('download', `earnings-report-${selectedPeriod}.json`);
        // document.body.appendChild(link);
        // link.click();
        // link.remove();

        setDownloadLoading(false);
      } catch (error) {
        console.error("Error generating report:", error);
        showErrorModal("Failed to download report. Please try again later.");
        setDownloadLoading(false);
      }
    }, 1500);
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500'></div>
      </div>
    );
  }

  return (
    <div className='p-6 animate-fade-in'>
      {/* Status Modal */}
      {showModal && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg max-w-md w-full animate-fade-in p-6'>
            <div className='flex items-start mb-4'>
              {modalType === "success" ? (
                <CheckCircle
                  size={24}
                  className='text-green-500 mr-3 flex-shrink-0'
                />
              ) : (
                <AlertCircle
                  size={24}
                  className='text-red-500 mr-3 flex-shrink-0'
                />
              )}
              <div className='flex-1'>
                <h3 className='text-lg font-semibold mb-2'>
                  {modalType === "success" ? "Success" : "Error"}
                </h3>
                <p
                  className={`${
                    modalType === "success" ? "text-green-700" : "text-red-600"
                  } mb-4`}
                >
                  {modalMessage}
                </p>
              </div>
              <button
                className='text-gray-400 hover:text-gray-600'
                onClick={() => setShowModal(false)}
              >
                <X size={20} />
              </button>
            </div>
            <div className='flex justify-end'>
              <Button
                variant={modalType === "success" ? "primary" : "danger"}
                onClick={() => setShowModal(false)}
              >
                Close
              </Button>
            </div>
          </div>
        </div>
      )}

      <div className='flex justify-between items-center mb-6'>
        <h1 className='text-2xl font-bold'>Earnings</h1>
        <div className='flex items-center space-x-2'>
          <div className='text-sm text-gray-500'>
            {formatDate(dateRange.start)} - {formatDate(dateRange.end)}
          </div>
          <Button
            variant='outline'
            icon={<Download size={16} />}
            onClick={handleDownloadReport}
            loading={downloadLoading}
          >
            Download Report
          </Button>
        </div>
      </div>

      {/* Period Selector */}
      <div className='flex mb-6 border rounded-lg overflow-hidden'>
        <button
          className={`flex-1 py-3 px-4 text-center ${
            selectedPeriod === "today"
              ? "bg-primary-500 text-white"
              : "bg-white hover:bg-gray-50"
          }`}
          onClick={() => handlePeriodChange("today")}
        >
          Today
        </button>
        <button
          className={`flex-1 py-3 px-4 text-center ${
            selectedPeriod === "week"
              ? "bg-primary-500 text-white"
              : "bg-white hover:bg-gray-50"
          }`}
          onClick={() => handlePeriodChange("week")}
        >
          This Week
        </button>
        <button
          className={`flex-1 py-3 px-4 text-center ${
            selectedPeriod === "month"
              ? "bg-primary-500 text-white"
              : "bg-white hover:bg-gray-50"
          }`}
          onClick={() => handlePeriodChange("month")}
        >
          This Month
        </button>
        <button
          className={`flex-1 py-3 px-4 text-center ${
            selectedPeriod === "year"
              ? "bg-primary-500 text-white"
              : "bg-white hover:bg-gray-50"
          }`}
          onClick={() => handlePeriodChange("year")}
        >
          This Year
        </button>
      </div>

      {/* Stats Cards */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6'>
        <Card>
          <div className='p-6'>
            <div className='flex items-start'>
              <div className='w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center mr-4'>
                <DollarSign size={24} className='text-primary-600' />
              </div>
              <div>
                <h2 className='text-sm font-medium text-gray-600 mb-1'>
                  Total Earnings
                </h2>
                <div className='text-2xl font-bold'>
                  {formatCurrency(filteredStats.totalEarnings)}
                </div>
                <div className='flex items-center text-xs text-green-600 mt-1'>
                  <ArrowUpRight size={14} className='mr-1' />
                  <span>+5.2% from last {selectedPeriod}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className='p-6'>
            <div className='flex items-start'>
              <div className='w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4'>
                <TruckIcon size={24} className='text-blue-600' />
              </div>
              <div>
                <h2 className='text-sm font-medium text-gray-600 mb-1'>
                  Total Deliveries
                </h2>
                <div className='text-2xl font-bold'>
                  {filteredStats.totalDeliveries}
                </div>
                <div className='flex items-center text-xs text-green-600 mt-1'>
                  <ArrowUpRight size={14} className='mr-1' />
                  <span>+2.1% from last {selectedPeriod}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className='p-6'>
            <div className='flex items-start'>
              <div className='w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-4'>
                <DollarSign size={24} className='text-green-600' />
              </div>
              <div>
                <h2 className='text-sm font-medium text-gray-600 mb-1'>
                  Average Per Delivery
                </h2>
                <div className='text-2xl font-bold'>
                  {formatCurrency(filteredStats.averagePerDelivery)}
                </div>
                <div className='flex items-center text-xs text-red-600 mt-1'>
                  <ArrowDownRight size={14} className='mr-1' />
                  <span>-0.8% from last {selectedPeriod}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>

        <Card>
          <div className='p-6'>
            <div className='flex items-start'>
              <div className='w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center mr-4'>
                <Clock size={24} className='text-yellow-600' />
              </div>
              <div>
                <h2 className='text-sm font-medium text-gray-600 mb-1'>
                  Total Hours
                </h2>
                <div className='text-2xl font-bold'>
                  {(filteredStats.totalHours || 0).toFixed(1)}
                </div>
                <div className='flex items-center text-xs text-green-600 mt-1'>
                  <ArrowUpRight size={14} className='mr-1' />
                  <span>+3.2% from last {selectedPeriod}</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Earnings Breakdown */}
      <Card className='mb-6'>
        <div className='p-6 border-b'>
          <h2 className='text-lg font-semibold'>Earnings Breakdown</h2>
        </div>

        <div className='p-6'>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
            <div>
              <div className='text-sm font-medium text-gray-600 mb-2'>
                Base Earnings
              </div>
              <div className='text-2xl font-bold mb-1'>
                {formatCurrency(
                  filteredStats.totalEarnings -
                    filteredStats.totalTips -
                    filteredStats.totalBonuses
                )}
              </div>
              <div className='text-sm text-gray-600'>
                {filteredStats.totalEarnings > 0
                  ? (
                      ((filteredStats.totalEarnings -
                        (filteredStats.totalTips || 0) -
                        (filteredStats.totalBonuses || 0)) /
                        filteredStats.totalEarnings) *
                      100
                    ).toFixed(1)
                  : "0.0"}
                % of total
              </div>
            </div>

            <div>
              <div className='text-sm font-medium text-gray-600 mb-2'>Tips</div>
              <div className='text-2xl font-bold mb-1'>
                {formatCurrency(filteredStats.totalTips)}
              </div>
              <div className='text-sm text-gray-600'>
                {filteredStats.totalEarnings > 0
                  ? (
                      ((filteredStats.totalTips || 0) /
                        filteredStats.totalEarnings) *
                      100
                    ).toFixed(1)
                  : "0.0"}
                % of total
              </div>
            </div>

            <div>
              <div className='text-sm font-medium text-gray-600 mb-2'>
                Bonuses
              </div>
              <div className='text-2xl font-bold mb-1'>
                {formatCurrency(filteredStats.totalBonuses)}
              </div>
              <div className='text-sm text-gray-600'>
                {filteredStats.totalEarnings > 0
                  ? (
                      ((filteredStats.totalBonuses || 0) /
                        filteredStats.totalEarnings) *
                      100
                    ).toFixed(1)
                  : "0.0"}
                % of total
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Earnings History */}
      <Card>
        <div className='p-6 border-b flex justify-between items-center'>
          <h2 className='text-lg font-semibold'>Earnings History</h2>
          <div className='flex items-center'>
            <Filter size={16} className='text-gray-400 mr-2' />
            <select className='border rounded-lg px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500'>
              <option>All Earnings</option>
              <option>Base Pay</option>
              <option>Tips</option>
              <option>Bonuses</option>
            </select>
          </div>
        </div>

        <div className='overflow-x-auto'>
          <table className='min-w-full divide-y divide-gray-200'>
            <thead className='bg-gray-50'>
              <tr>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Date
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Deliveries
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Base Pay
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Tips
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Bonuses
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Total
                </th>
                <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                  Details
                </th>
              </tr>
            </thead>
            <tbody className='bg-white divide-y divide-gray-200'>
              {filteredEarnings.length === 0 ? (
                <tr>
                  <td
                    colSpan='7'
                    className='px-6 py-4 text-center text-gray-500'
                  >
                    <div className='flex items-center justify-center'>
                      <FileText
                        size={48}
                        className='mx-auto mb-4 text-gray-300'
                      />
                      <div>
                        <p className='mb-2'>
                          No earnings found for this period
                        </p>
                        <p className='text-sm'>
                          Try selecting a different time period
                        </p>
                      </div>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredEarnings.map((earning) => (
                  <React.Fragment key={earning.id}>
                    <tr className='hover:bg-gray-50'>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          <Calendar size={16} className='text-gray-400 mr-2' />
                          <span>{formatDate(earning.date)}</span>
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm'>
                          {earning.deliveries || earning.totalOrders || 0}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm'>
                          {formatCurrency(
                            earning.totalEarnings -
                              (earning.tips || 0) -
                              ((earning.bonuses || 0) +
                                (earning.distanceBonus || 0) +
                                (earning.timeBonus || 0))
                          )}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm'>
                          {formatCurrency(earning.tips)}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm'>
                          {formatCurrency(
                            (earning.bonuses || 0) +
                              (earning.distanceBonus || 0) +
                              (earning.timeBonus || 0)
                          )}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm font-medium'>
                          {formatCurrency(earning.totalEarnings)}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <button
                          className='text-primary-600 hover:text-primary-800'
                          onClick={() => toggleEarningExpand(earning.id)}
                        >
                          {expandedEarningId === earning.id ? (
                            <ChevronUp size={20} />
                          ) : (
                            <ChevronDown size={20} />
                          )}
                        </button>
                      </td>
                    </tr>

                    {/* Expanded Details */}
                    {expandedEarningId === earning.id && (
                      <tr>
                        <td colSpan='7' className='px-6 py-4 bg-gray-50'>
                          <div className='text-sm font-medium mb-3'>
                            Delivery Details
                          </div>
                          <div className='space-y-3'>
                            {earning.details.map((detail) => {
                              const delivery = getDeliveryDetails(
                                detail.orderId
                              );
                              return (
                                <div
                                  key={detail.orderId}
                                  className='flex justify-between items-center p-3 bg-white rounded-lg border'
                                >
                                  <div>
                                    <div className='font-medium'>
                                      Order #{detail.orderId.split("-")[1]}
                                    </div>
                                    <div className='text-sm text-gray-600'>
                                      {delivery
                                        ? `${delivery.restaurantName} → ${delivery.customerName}`
                                        : "Order details not available"}
                                    </div>
                                    <div className='text-xs text-gray-500'>
                                      {new Date(detail.time).toLocaleTimeString(
                                        "en-US",
                                        {
                                          hour: "2-digit",
                                          minute: "2-digit",
                                        }
                                      )}
                                    </div>
                                  </div>
                                  <div className='text-right'>
                                    <div className='font-medium'>
                                      {formatCurrency(detail.amount)}
                                    </div>
                                    {delivery && (
                                      <div className='text-xs text-gray-500'>
                                        {delivery.distance} km •{" "}
                                        {delivery.estimatedTime} min
                                      </div>
                                    )}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))
              )}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
}

export default DeliveryEarnings;
