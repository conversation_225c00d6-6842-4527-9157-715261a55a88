import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import { useOrder } from "../../context/OrderContext";
import { Clock, ArrowRight, Search, AlertCircle, Star } from "lucide-react";
import { orderStatuses } from "../../data/orders";
import Card from "../../components/common/Card";
import Badge from "../../components/common/Badge";
import Button from "../../components/common/Button";
import { OrderListSkeleton } from "../../components/skeleton/OrderCardSkeleton";
import { SwipeableOrderCard } from "../../components/mobile/SwipeableCard";
import PullToRefresh from "../../components/mobile/PullToRefresh";
import RatingModal from "../../components/rating/RatingModal";

const Orders = () => {
  const { user } = useAuth();
  const { orders, loading, error, loadOrders, deleteOrder, clearError } =
    useOrder();
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [showRatingModal, setShowRatingModal] = useState(false);
  const [selectedOrderForRating, setSelectedOrderForRating] = useState(null);

  const handleRefresh = async () => {
    await loadOrders();
  };

  const handleFavoriteOrder = (order) => {
    console.log("Favorited order:", order.id);
    // Add to favorites logic
  };

  const handleDeleteOrder = async (order) => {
    if (window.confirm("Are you sure you want to delete this order?")) {
      const result = await deleteOrder(order.id);
      if (!result.success) {
        console.error("Failed to delete order:", result.error);
      }
    }
  };

  const handleShareOrder = (order) => {
    console.log("Shared order:", order.id);
    // Share order logic
  };

  const handleRateOrder = (order) => {
    setSelectedOrderForRating(order);
    setShowRatingModal(true);
  };

  const handleRatingSubmitted = () => {
    setShowRatingModal(false);
    setSelectedOrderForRating(null);
    // Optionally refresh orders to update rating status
    loadOrders();
  };

  // Load orders when component mounts
  useEffect(() => {
    if (user) {
      loadOrders();
    }
  }, [user]);

  const filteredOrders = (orders || []).filter((order) => {
    if (filter !== "all" && order.status !== filter) {
      return false;
    }

    if (searchQuery) {
      return (
        (order.restaurant?.name || order.restaurantName || "")
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        (order.id || "")
          .toString()
          .toLowerCase()
          .includes(searchQuery.toLowerCase())
      );
    }

    return true;
  });

  const getStatusBadge = (status) => {
    const statusInfo = orderStatuses.find((s) => s.value === status);
    if (!statusInfo) return null;

    return (
      <Badge
        className={`${statusInfo.color} text-white`}
        size='small'
        rounded='full'
      >
        {statusInfo.label}
      </Badge>
    );
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className='container mx-auto px-4 py-8 max-w-4xl animate-fade-in'>
        <h1 className='text-2xl font-poppins font-semibold mb-8'>
          Your Orders
        </h1>
        <OrderListSkeleton count={4} variant='default' />
      </div>
    );
  }

  if (error) {
    return (
      <div className='container mx-auto px-4 py-8 max-w-4xl animate-fade-in'>
        <h1 className='text-2xl font-poppins font-semibold mb-8'>
          Your Orders
        </h1>
        <Card className='border-l-4 border-red-500'>
          <div className='flex items-start p-4'>
            <AlertCircle
              size={18}
              className='text-red-500 mr-2 mt-0.5 flex-shrink-0'
            />
            <div>
              <p className='text-red-600 text-sm'>{error}</p>
              <button
                onClick={() => {
                  clearError();
                  loadOrders();
                }}
                className='text-red-500 text-sm underline mt-1'
              >
                Try Again
              </button>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <PullToRefresh onRefresh={handleRefresh}>
      <div className='container mx-auto px-4 py-8 max-w-4xl animate-fade-in'>
        <h1 className='text-2xl font-poppins font-semibold mb-8'>
          Your Orders
        </h1>

        {/* Filters & Search */}
        <div className='mb-6'>
          <div className='flex flex-col md:flex-row md:items-center justify-between gap-4'>
            <div className='flex overflow-x-auto hide-scrollbar space-x-2'>
              <button
                className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                  filter === "all"
                    ? "bg-primary-500 text-white"
                    : "bg-white border border-gray-200 text-text-primary hover:bg-gray-50"
                }`}
                onClick={() => setFilter("all")}
              >
                All Orders
              </button>

              <button
                className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                  filter === "delivered"
                    ? "bg-primary-500 text-white"
                    : "bg-white border border-gray-200 text-text-primary hover:bg-gray-50"
                }`}
                onClick={() => setFilter("delivered")}
              >
                Completed
              </button>

              <button
                className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                  filter === "cancelled"
                    ? "bg-primary-500 text-white"
                    : "bg-white border border-gray-200 text-text-primary hover:bg-gray-50"
                }`}
                onClick={() => setFilter("cancelled")}
              >
                Cancelled
              </button>

              <button
                className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                  filter !== "all" &&
                  filter !== "delivered" &&
                  filter !== "cancelled"
                    ? "bg-primary-500 text-white"
                    : "bg-white border border-gray-200 text-text-primary hover:bg-gray-50"
                }`}
                onClick={() => setFilter("preparing")}
              >
                Active
              </button>
            </div>

            <div className='relative w-full md:w-auto md:min-w-[300px]'>
              <Search
                className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
                size={18}
              />
              <input
                type='text'
                placeholder='Search orders...'
                className='w-full pl-10 pr-4 py-2 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Orders List */}
        {filteredOrders.length > 0 ? (
          <div className='space-y-4'>
            {filteredOrders.map((order) => (
              <div key={order.id} className='md:hidden'>
                <SwipeableOrderCard
                  order={order}
                  onFavorite={handleFavoriteOrder}
                  onDelete={handleDeleteOrder}
                  onShare={handleShareOrder}
                />
              </div>
            ))}

            {/* Desktop view */}
            <div className='hidden md:block space-y-4'>
              {filteredOrders.map((order) => (
                <Card
                  key={order.id}
                  className='flex flex-col md:flex-row md:items-center gap-4'
                  hoverable
                >
                  <div className='flex-grow'>
                    <div className='flex items-center justify-between mb-2'>
                      <h3 className='font-semibold'>
                        {order.restaurant?.name ||
                          order.restaurantName ||
                          "Restaurant"}
                      </h3>
                      {getStatusBadge(order.status)}
                    </div>

                    <p className='text-sm text-text-secondary mb-2'>
                      Order #{order.id} •{" "}
                      {formatDate(order.created_at || order.createdAt)}
                    </p>

                    <div className='text-sm'>
                      <p>
                        {(order.items || order.orderItems || []).map(
                          (item, index) => (
                            <span key={item.id || index}>
                              {item.quantity}x{" "}
                              {item.menu_item?.name || item.name || "Item"}
                              {index <
                              (order.items || order.orderItems || []).length - 1
                                ? ", "
                                : ""}
                            </span>
                          )
                        )}
                      </p>
                      <p className='font-medium mt-1'>
                        Total: $
                        {Number(
                          order.total_amount || order.totalAmount || 0
                        ).toFixed(2)}
                      </p>
                    </div>
                  </div>

                  <div className='flex items-center self-end md:self-center space-x-2'>
                    {(order.status === "pending" ||
                      order.status === "confirmed" ||
                      order.status === "preparing" ||
                      order.status === "readyForPickup" ||
                      order.status === "outForDelivery") && (
                      <div className='flex items-center text-sm text-text-secondary mr-4'>
                        <Clock size={16} className='mr-1' />
                        <span>Delivery in progress</span>
                      </div>
                    )}

                    {/* Rating Button for Delivered Orders */}
                    {order.status === "delivered" && !order.rating && (
                      <Button
                        variant='outline'
                        size='small'
                        icon={<Star size={16} />}
                        onClick={() => handleRateOrder(order)}
                        className='text-yellow-600 border-yellow-600 hover:bg-yellow-50'
                      >
                        Rate
                      </Button>
                    )}

                    <Link
                      to={`/orders/${order.id}`}
                      className='text-primary-500 hover:text-primary-600'
                    >
                      <Button
                        variant='outline'
                        size='small'
                        icon={<ArrowRight size={16} />}
                      >
                        View
                      </Button>
                    </Link>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <div className='text-center py-12 bg-white rounded-lg shadow-sm'>
            <div className='text-6xl mb-4'>📋</div>
            <h3 className='text-xl font-medium mb-2'>No Orders Found</h3>
            <p className='text-text-secondary mb-6'>
              {searchQuery
                ? "No orders match your search criteria."
                : "You haven't placed any orders yet."}
            </p>
            <Button variant='primary' to='/restaurants'>
              Browse Restaurants
            </Button>
          </div>
        )}
      </div>

      {/* Rating Modal */}
      {showRatingModal && selectedOrderForRating && (
        <RatingModal
          isOpen={showRatingModal}
          onClose={() => setShowRatingModal(false)}
          order={selectedOrderForRating}
          onRatingSubmitted={handleRatingSubmitted}
        />
      )}
    </PullToRefresh>
  );
};

export default Orders;
