#!/usr/bin/env python3
"""
Setup test data to make checkout work immediately
"""

import os
import sys
import django

# Add the Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from restaurant.models import Restaurant, Address, MenuCategory, MenuItem
from orders.models import Order

User = get_user_model()

def setup_checkout_test_data():
    """Setup complete test data for checkout"""
    
    print("🛒 Setting Up Checkout Test Data")
    print("=" * 50)
    
    try:
        # 1. Ensure test customer exists with address
        test_customer = User.objects.get(email='<EMAIL>')
        print(f"✅ Test customer: {test_customer.user_name}")
        
        # Ensure customer has an address
        customer_address = Address.objects.filter(user=test_customer).first()
        if not customer_address:
            customer_address = Address.objects.create(
                user=test_customer,
                street='123 Test Checkout Street',
                city='Kabul',
                state='Kabul Province',
                postal_code='1001',
                country='Afghanistan',
                latitude=34.5600,
                longitude=69.2100
            )
            print(f"✅ Created customer address: {customer_address.id}")
        else:
            print(f"✅ Customer address exists: {customer_address.id}")
        
        # 2. Ensure there's an active restaurant with menu items
        restaurant = Restaurant.objects.filter(is_active=True).first()
        if not restaurant:
            # Create a test restaurant
            restaurant_owner = User.objects.filter(role='restaurant').first()
            if not restaurant_owner:
                restaurant_owner = User.objects.create(
                    email='<EMAIL>',
                    name='Test Restaurant Owner',
                    user_name='test_restaurant_checkout',
                    role='restaurant',
                    is_verified=True,
                    phone='+93 70 999 0001'
                )
                restaurant_owner.set_password('testpass123')
                restaurant_owner.save()
            
            restaurant_address = Address.objects.create(
                user=restaurant_owner,
                street='456 Restaurant Street',
                city='Kabul',
                state='Kabul Province',
                postal_code='1001',
                country='Afghanistan',
                latitude=34.5553,
                longitude=69.2075
            )
            
            restaurant = Restaurant.objects.create(
                owner=restaurant_owner,
                name='Test Checkout Restaurant',
                description='Restaurant for testing checkout',
                address=restaurant_address,
                contact_number='+93 70 999 0001',
                delivery_fee=25.00,
                opening_time='08:00',
                closing_time='22:00',
                is_active=True,
                is_verified=True
            )
            print(f"✅ Created test restaurant: {restaurant.id}")
        else:
            print(f"✅ Restaurant exists: {restaurant.name}")
        
        # 3. Ensure restaurant has menu items
        menu_items = MenuItem.objects.filter(category__restaurant=restaurant)
        if menu_items.count() == 0:
            # Create a menu category
            category = MenuCategory.objects.create(
                restaurant=restaurant,
                name='Test Items',
                description='Test menu items for checkout'
            )
            
            # Create test menu items
            MenuItem.objects.create(
                category=category,
                name='Test Burger',
                description='Delicious test burger',
                price=150.00,
                preparation_time=15,
                is_available=True
            )

            MenuItem.objects.create(
                category=category,
                name='Test Pizza',
                description='Amazing test pizza',
                price=300.00,
                preparation_time=25,
                is_available=True
            )
            
            print(f"✅ Created test menu items")
        else:
            print(f"✅ Menu items exist: {menu_items.count()} items")
        
        # 4. Print test instructions
        print(f"\n🎯 CHECKOUT TEST INSTRUCTIONS:")
        print(f"=" * 50)
        print(f"1. Go to: http://localhost:5174/auth/login")
        print(f"   Username: {test_customer.user_name}")
        print(f"   Password: testpass123")
        print(f"")
        print(f"2. Go to restaurant: http://localhost:5174/restaurant/{restaurant.id}")
        print(f"   Add some menu items to cart")
        print(f"")
        print(f"3. Go to checkout: http://localhost:5174/checkout")
        print(f"   Select address: {customer_address.street}, {customer_address.city}")
        print(f"   Place Order button should be ENABLED!")
        print(f"")
        print(f"📋 Test Data Summary:")
        print(f"   Customer: {test_customer.user_name} (ID: {test_customer.id})")
        print(f"   Address: {customer_address.street} (ID: {customer_address.id})")
        print(f"   Restaurant: {restaurant.name} (ID: {restaurant.id})")
        print(f"   Menu Items: {MenuItem.objects.filter(category__restaurant=restaurant).count()}")
        
        # 5. Create JavaScript snippet for browser console
        js_snippet = f"""
// Paste this in browser console to setup cart data
const testCartData = {{
  restaurant: {{
    id: {restaurant.id},
    name: "{restaurant.name}",
    delivery_fee: {restaurant.delivery_fee}
  }},
  items: [
    {{
      id: 1,
      name: "Test Burger",
      price: 150.00,
      quantity: 1
    }}
  ],
  addresses: [
    {{
      id: {customer_address.id},
      backendId: {customer_address.id},
      type: "saved",
      label: "{customer_address.street}, {customer_address.city}",
      address: "{customer_address.street}, {customer_address.city}, {customer_address.state}, {customer_address.country}",
      coordinates: [{float(customer_address.latitude)}, {float(customer_address.longitude)}],
      street: "{customer_address.street}",
      city: "{customer_address.city}",
      state: "{customer_address.state}",
      country: "{customer_address.country}",
      postal_code: "{customer_address.postal_code}",
      source: "api"
    }}
  ],
  selectedAddress: {{
    id: {customer_address.id},
    backendId: {customer_address.id},
    type: "saved",
    label: "{customer_address.street}, {customer_address.city}",
    address: "{customer_address.street}, {customer_address.city}, {customer_address.state}, {customer_address.country}",
    coordinates: [{float(customer_address.latitude)}, {float(customer_address.longitude)}],
    street: "{customer_address.street}",
    city: "{customer_address.city}",
    state: "{customer_address.state}",
    country: "{customer_address.country}",
    postal_code: "{customer_address.postal_code}",
    source: "api"
  }},
  total: 175.00
}};

localStorage.setItem('afghanSofraCart', JSON.stringify(testCartData));
console.log('✅ Test cart data added!');
window.location.reload();
"""
        
        print(f"\n🚀 QUICK FIX - Browser Console Script:")
        print(f"=" * 50)
        print(js_snippet)
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    setup_checkout_test_data()
