import React, { useState, useEffect } from 'react';
import { 
  DollarSign, 
  TrendingUp, 
  Calendar, 
  Package, 
  Clock,
  RefreshCw,
  BarChart3,
  PieChart,
  Download,
  Filter
} from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { deliveryAgentApi } from '../../services/deliveryAgentApi';
import toast from 'react-hot-toast';

const EarningsPage = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [earningsData, setEarningsData] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState('today');
  const [refreshing, setRefreshing] = useState(false);

  // Fetch earnings data
  const fetchEarnings = async () => {
    try {
      setRefreshing(true);
      const response = await deliveryAgentApi.getEarningsSummary();
      
      if (response.data?.status === 'success') {
        setEarningsData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching earnings:', error);
      toast.error('Failed to load earnings data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchEarnings();
    
    // Refresh every 5 minutes
    const interval = setInterval(fetchEarnings, 300000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading earnings data...</p>
        </div>
      </div>
    );
  }

  const periods = [
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'all', label: 'All Time' }
  ];

  const getCurrentPeriodData = () => {
    if (!earningsData) return { earnings: 0, deliveries: 0, avgPerDelivery: 0 };
    
    switch (selectedPeriod) {
      case 'today':
        return {
          earnings: earningsData.today || 0,
          deliveries: earningsData.total_orders_today || 0,
          avgPerDelivery: earningsData.total_orders_today > 0 
            ? (earningsData.today / earningsData.total_orders_today).toFixed(2)
            : 0
        };
      case 'week':
        return {
          earnings: earningsData.this_week || 0,
          deliveries: earningsData.total_orders_week || 0,
          avgPerDelivery: earningsData.total_orders_week > 0 
            ? (earningsData.this_week / earningsData.total_orders_week).toFixed(2)
            : 0
        };
      case 'month':
        return {
          earnings: earningsData.this_month || 0,
          deliveries: earningsData.total_orders_month || 0,
          avgPerDelivery: earningsData.total_orders_month > 0 
            ? (earningsData.this_month / earningsData.total_orders_month).toFixed(2)
            : 0
        };
      default:
        return {
          earnings: earningsData.total_earnings || 0,
          deliveries: earningsData.total_deliveries || 0,
          avgPerDelivery: earningsData.total_deliveries > 0 
            ? (earningsData.total_earnings / earningsData.total_deliveries).toFixed(2)
            : 0
        };
    }
  };

  const currentData = getCurrentPeriodData();

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="mb-4 md:mb-0">
              <h1 className="text-2xl font-bold text-gray-900">Earnings Dashboard</h1>
              <p className="text-gray-600">Track your delivery earnings and performance</p>
            </div>
            
            <div className="flex items-center space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {periods.map(period => (
                  <option key={period.value} value={period.value}>
                    {period.label}
                  </option>
                ))}
              </select>
              
              <button
                onClick={fetchEarnings}
                disabled={refreshing}
                className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  {periods.find(p => p.value === selectedPeriod)?.label} Earnings
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  AFN {currentData.earnings.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">
                  {periods.find(p => p.value === selectedPeriod)?.label} Deliveries
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {currentData.deliveries}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg per Delivery</p>
                <p className="text-2xl font-bold text-gray-900">
                  AFN {currentData.avgPerDelivery}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Detailed Breakdown */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Earnings Breakdown */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Earnings Breakdown</h2>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">Today</p>
                  <p className="text-sm text-gray-600">{earningsData?.total_orders_today || 0} deliveries</p>
                </div>
                <p className="text-lg font-bold text-gray-900">
                  AFN {(earningsData?.today || 0).toFixed(2)}
                </p>
              </div>

              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">This Week</p>
                  <p className="text-sm text-gray-600">{earningsData?.total_orders_week || 0} deliveries</p>
                </div>
                <p className="text-lg font-bold text-gray-900">
                  AFN {(earningsData?.this_week || 0).toFixed(2)}
                </p>
              </div>

              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">This Month</p>
                  <p className="text-sm text-gray-600">{earningsData?.total_orders_month || 0} deliveries</p>
                </div>
                <p className="text-lg font-bold text-gray-900">
                  AFN {(earningsData?.this_month || 0).toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Performance Metrics</h2>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Deliveries</span>
                <span className="font-semibold text-gray-900">
                  {earningsData?.total_deliveries || 0}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600">Success Rate</span>
                <span className="font-semibold text-gray-900">
                  {earningsData?.completion_rate || 0}%
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600">Average Rating</span>
                <span className="font-semibold text-gray-900">
                  {earningsData?.average_rating || '0.0'} ⭐
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total Earnings</span>
                <span className="font-semibold text-green-600">
                  AFN {(earningsData?.total_earnings || 0).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Recent Earnings Activity</h2>
            <button className="flex items-center px-3 py-2 text-sm bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
          </div>

          {/* Placeholder for recent activity */}
          <div className="text-center py-12">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Detailed Activity Coming Soon</h3>
            <p className="text-gray-600">
              Detailed earnings history and analytics will be available here.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EarningsPage;
