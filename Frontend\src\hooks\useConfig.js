// Frontend/src/hooks/useConfig.js
import { useMemo } from "react";
import { useConfig as useConfigContext } from "../context/ConfigContext";

export const useConfig = () => {
  return useConfigContext();
};

export const useSystemSetting = (key) => {
  const { settings, loading, error } = useConfig();

  const value = useMemo(() => {
    if (!key || !settings.length) return null;
    const setting = settings.find((s) => s.key === key);
    return setting ? setting.typed_value : null;
  }, [key, settings]);

  return { value, loading, error };
};

export const useChoiceOptions = (type) => {
  const { choiceOptions, loading, error } = useConfig();

  const options = useMemo(() => {
    if (!type || !choiceOptions) return [];
    return choiceOptions[type] || [];
  }, [type, choiceOptions]);

  return { options, loading, error };
};

// Specific hooks for commonly used choice options
export const useUserRoles = () => useChoiceOptions("user_role");
export const useOrderStatuses = () => useChoiceOptions("order_status");
export const usePaymentMethods = () => useChoiceOptions("payment_method");
export const useDietaryRestrictions = () =>
  useChoiceOptions("dietary_restriction");
export const useVehicleTypes = () => useChoiceOptions("vehicle_type");
export const useCurrencies = () => useChoiceOptions("currency");

// Specific hooks for commonly used settings
export const useSiteName = () => useSystemSetting("site_name");
export const useSiteDescription = () => useSystemSetting("site_description");
export const useContactEmail = () => useSystemSetting("contact_email");
export const useContactPhone = () => useSystemSetting("contact_phone");
export const useDefaultCurrency = () => useSystemSetting("default_currency");
export const useDefaultDeliveryFee = () =>
  useSystemSetting("default_delivery_fee");
export const useFreeDeliveryThreshold = () =>
  useSystemSetting("free_delivery_threshold");
export const useEstimatedDeliveryTime = () =>
  useSystemSetting("estimated_delivery_time");
export const useTaxRate = () => useSystemSetting("tax_rate");
