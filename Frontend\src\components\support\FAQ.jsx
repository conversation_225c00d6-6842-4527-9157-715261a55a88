import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Search, HelpCircle } from 'lucide-react';
import { useSupport } from '../../context/SupportContext';
import Card from '../common/Card';

const FAQ = () => {
  const { faqCategories } = useSupport();
  const [activeCategory, setActiveCategory] = useState('orders');
  const [expandedQuestion, setExpandedQuestion] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');

  const getFilteredQuestions = () => {
    const category = faqCategories.find(cat => cat.id === activeCategory);
    if (!category) return [];

    if (!searchQuery) return category.questions;

    return category.questions.filter(q =>
      q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      q.answer.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const getAllQuestions = () => {
    return faqCategories.flatMap(cat => 
      cat.questions.map(q => ({ ...q, category: cat.name }))
    );
  };

  const getSearchResults = () => {
    if (!searchQuery) return [];
    
    return getAllQuestions().filter(q =>
      q.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      q.answer.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  const toggleQuestion = (questionId) => {
    setExpandedQuestion(expandedQuestion === questionId ? null : questionId);
  };

  const filteredQuestions = searchQuery ? getSearchResults() : getFilteredQuestions();

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <HelpCircle size={32} className="text-primary-600" />
        </div>
        <h1 className="text-3xl font-bold mb-2">Frequently Asked Questions</h1>
        <p className="text-gray-600">
          Find quick answers to common questions about Afghan Sofra
        </p>
      </div>

      {/* Search */}
      <div className="mb-8">
        <div className="relative max-w-md mx-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search FAQs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Categories Sidebar */}
        {!searchQuery && (
          <div className="lg:col-span-1">
            <Card className="p-4">
              <h3 className="font-semibold mb-4">Categories</h3>
              <div className="space-y-2">
                {faqCategories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      activeCategory === category.id
                        ? 'bg-primary-100 text-primary-700 border border-primary-200'
                        : 'hover:bg-gray-50 border border-transparent'
                    }`}
                  >
                    <div className="flex items-center">
                      <span className="text-lg mr-3">{category.icon}</span>
                      <div>
                        <div className="font-medium text-sm">{category.name}</div>
                        <div className="text-xs text-gray-500">
                          {category.questions.length} questions
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </Card>
          </div>
        )}

        {/* Questions */}
        <div className={searchQuery ? 'lg:col-span-4' : 'lg:col-span-3'}>
          {searchQuery && (
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-2">
                Search Results ({filteredQuestions.length})
              </h2>
              <p className="text-gray-600">
                Results for "{searchQuery}"
              </p>
            </div>
          )}

          {filteredQuestions.length === 0 ? (
            <Card className="text-center py-12">
              <HelpCircle size={48} className="mx-auto text-gray-400 mb-4" />
              <h3 className="text-xl font-medium mb-2">
                {searchQuery ? 'No results found' : 'No questions available'}
              </h3>
              <p className="text-gray-600">
                {searchQuery 
                  ? 'Try adjusting your search terms or browse categories'
                  : 'Questions will appear here when available'
                }
              </p>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredQuestions.map((question) => (
                <Card key={question.id} className="overflow-hidden">
                  <button
                    onClick={() => toggleQuestion(question.id)}
                    className="w-full p-6 text-left hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 mb-1">
                          {question.question}
                        </h3>
                        {searchQuery && question.category && (
                          <p className="text-sm text-gray-500">
                            Category: {question.category}
                          </p>
                        )}
                      </div>
                      <div className="ml-4 flex-shrink-0">
                        {expandedQuestion === question.id ? (
                          <ChevronUp size={20} className="text-gray-400" />
                        ) : (
                          <ChevronDown size={20} className="text-gray-400" />
                        )}
                      </div>
                    </div>
                  </button>
                  
                  {expandedQuestion === question.id && (
                    <div className="px-6 pb-6 border-t border-gray-100 bg-gray-50">
                      <div className="pt-4">
                        <p className="text-gray-700 leading-relaxed">
                          {question.answer}
                        </p>
                      </div>
                    </div>
                  )}
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Contact Support CTA */}
      <Card className="mt-12 text-center p-8 bg-gradient-to-r from-primary-50 to-primary-100 border-primary-200">
        <h3 className="text-xl font-semibold mb-2">Still need help?</h3>
        <p className="text-gray-600 mb-6">
          Can't find what you're looking for? Our support team is here to help!
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button className="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
            Start Live Chat
          </button>
          <button className="border border-primary-500 text-primary-600 hover:bg-primary-50 px-6 py-3 rounded-lg font-medium transition-colors">
            Submit a Ticket
          </button>
        </div>
      </Card>
    </div>
  );
};

export default FAQ;
