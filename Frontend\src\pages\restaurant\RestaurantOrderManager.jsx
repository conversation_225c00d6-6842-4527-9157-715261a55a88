import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Clock,
  Package,
  CheckCircle,
  DollarSign,
  Search,
  RefreshCw,
  Eye,
  X,
  User,
  MapPin,
  Phone,
  MessageCircle,
  AlertCircle,
  ChefHat,
  Truck,
  CheckSquare,
  XCircle,
  Filter,
  Calendar,
  TrendingUp,
  Bell,
} from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import Input from "../../components/common/Input";

function RestaurantOrderManager() {
  const { restaurantId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { getRestaurant } = useRestaurant();

  // State management
  const [restaurant, setRestaurant] = useState(null);
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("today");
  const [sortBy, setSortBy] = useState("created_at");
  const [sortOrder, setSortOrder] = useState("desc");

  // Order status options for restaurant
  const orderStatuses = [
    { value: "pending", label: "Pending", color: "yellow", icon: Clock },
    {
      value: "confirmed",
      label: "Confirmed",
      color: "blue",
      icon: CheckSquare,
    },
    { value: "preparing", label: "Preparing", color: "orange", icon: ChefHat },
    {
      value: "ready",
      label: "Ready for Pickup",
      color: "green",
      icon: Package,
    },
    {
      value: "assigned",
      label: "Assigned to Delivery",
      color: "purple",
      icon: Truck,
    },
    { value: "picked_up", label: "Picked Up", color: "indigo", icon: Truck },
    { value: "on_the_way", label: "On the Way", color: "blue", icon: Truck },
    {
      value: "delivered",
      label: "Delivered",
      color: "green",
      icon: CheckCircle,
    },
    { value: "cancelled", label: "Cancelled", color: "red", icon: XCircle },
  ];

  // Load restaurant data
  useEffect(() => {
    const loadRestaurantData = async () => {
      try {
        setLoading(true);

        // If no restaurantId in URL, get user's first restaurant
        if (!restaurantId) {
          const token = localStorage.getItem("afghanSofraUser");
          if (token) {
            const userData = JSON.parse(token);
            const response = await fetch(
              "http://127.0.0.1:8000/api/restaurant/restaurants/my_restaurants/",
              {
                headers: {
                  Authorization: `Bearer ${userData.access_token}`,
                  "Content-Type": "application/json",
                },
              }
            );

            if (response.ok) {
              const restaurants = await response.json();
              if (restaurants.length > 0) {
                // Use the first restaurant
                const firstRestaurant = restaurants[0];
                setRestaurant(firstRestaurant);
                // Redirect to URL with restaurant ID for consistency
                navigate(`/restaurant/order-manager/${firstRestaurant.id}`, {
                  replace: true,
                });
                return;
              } else {
                setError("No restaurants found for this user");
                setLoading(false);
                return;
              }
            } else {
              setError("Failed to load restaurants");
              setLoading(false);
              return;
            }
          } else {
            setError("Authentication required");
            setLoading(false);
            return;
          }
        }

        // Load restaurant details
        const restaurantResult = await getRestaurant(restaurantId);
        if (restaurantResult.success) {
          setRestaurant(restaurantResult.data);
        } else {
          setError("Failed to load restaurant details");
          return;
        }

        // Load orders
        await loadOrders();
      } catch (err) {
        setError("Failed to load restaurant data");
        console.error("Error loading restaurant data:", err);
      } finally {
        setLoading(false);
      }
    };

    loadRestaurantData();
  }, [restaurantId, getRestaurant]);

  // Load orders from API
  const loadOrders = async () => {
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const response = await fetch(
          "http://127.0.0.1:8000/api/order/orders/",
          {
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const ordersData = await response.json();
          console.log("Orders loaded:", ordersData.length);

          // Filter orders for this specific restaurant if restaurantId is provided
          const restaurantOrders = restaurantId
            ? ordersData.filter((order) => {
                // Handle both object and ID formats for restaurant field
                const orderRestaurantId =
                  typeof order.restaurant === "object"
                    ? order.restaurant.id
                    : order.restaurant;
                const matches =
                  orderRestaurantId.toString() === restaurantId.toString();

                // Debug logging for first few orders
                if (ordersData.indexOf(order) < 3) {
                  console.log(
                    `🔍 Order ${order.id}: restaurant=${JSON.stringify(
                      order.restaurant
                    )}, extracted_id=${orderRestaurantId}, target_id=${restaurantId}, matches=${matches}`
                  );
                }

                return matches;
              })
            : ordersData;

          console.log(
            `🎯 Filtered ${restaurantOrders.length} orders for restaurant ${restaurantId}`
          );
          setOrders(restaurantOrders);
        } else {
          console.error("Failed to load orders:", response.status);
          setError("Failed to load orders");
        }
      }
    } catch (error) {
      console.error("Error loading orders:", error);
      setError("Error loading orders");
    }
  };

  // Filter and sort orders
  useEffect(() => {
    let filtered = [...orders];

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((order) => order.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(
        (order) =>
          order.id.toString().includes(searchQuery) ||
          order.customer?.name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          order.customer?.email
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase())
      );
    }

    // Filter by date
    if (dateFilter !== "all") {
      const today = new Date();
      const startOfDay = new Date(today.setHours(0, 0, 0, 0));

      filtered = filtered.filter((order) => {
        const orderDate = new Date(order.created_at);

        switch (dateFilter) {
          case "today":
            return orderDate >= startOfDay;
          case "yesterday":
            const yesterday = new Date(startOfDay);
            yesterday.setDate(yesterday.getDate() - 1);
            return orderDate >= yesterday && orderDate < startOfDay;
          case "week":
            const weekAgo = new Date(startOfDay);
            weekAgo.setDate(weekAgo.getDate() - 7);
            return orderDate >= weekAgo;
          case "month":
            const monthAgo = new Date(startOfDay);
            monthAgo.setMonth(monthAgo.getMonth() - 1);
            return orderDate >= monthAgo;
          default:
            return true;
        }
      });
    }

    // Sort orders
    filtered.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case "created_at":
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        case "total_amount":
          aValue = parseFloat(a.total_amount);
          bValue = parseFloat(b.total_amount);
          break;
        case "status":
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          aValue = a.created_at;
          bValue = b.created_at;
      }

      if (sortOrder === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredOrders(filtered);
  }, [orders, statusFilter, searchQuery, dateFilter, sortBy, sortOrder]);

  // Refresh orders
  const handleRefreshOrders = async () => {
    setRefreshing(true);
    try {
      await loadOrders();
      setSuccess("Orders refreshed successfully");
    } catch (error) {
      setError("Failed to refresh orders");
    } finally {
      setRefreshing(false);
    }
  };

  // Update order status
  const handleUpdateOrderStatus = async (orderId, newStatus) => {
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const response = await fetch(
          `http://127.0.0.1:8000/api/order/orders/${orderId}/`,
          {
            method: "PATCH",
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ status: newStatus }),
          }
        );

        if (response.ok) {
          setSuccess(`Order #${orderId} status updated to ${newStatus}`);
          await loadOrders(); // Refresh orders
        } else {
          setError("Failed to update order status");
        }
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      setError("Error updating order status");
    }
  };

  // Get status badge
  const getStatusBadge = (status) => {
    const statusInfo =
      orderStatuses.find((s) => s.value === status) || orderStatuses[0];
    const Icon = statusInfo.icon;

    return (
      <Badge variant={statusInfo.color} className='flex items-center space-x-1'>
        <Icon size={12} />
        <span>{statusInfo.label}</span>
      </Badge>
    );
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Format date and time
  const formatDateTime = (dateString) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    };
  };

  // Clear messages
  useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess(null);
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success, error]);

  if (loading) {
    return (
      <div className='p-6'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-6'></div>
          <div className='space-y-4'>
            {[1, 2, 3].map((i) => (
              <div key={i} className='h-32 bg-gray-200 rounded'></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error && !restaurant) {
    return (
      <div className='p-6'>
        <Card className='p-8 text-center'>
          <AlertCircle className='mx-auto mb-4 text-red-500' size={48} />
          <h2 className='text-xl font-semibold mb-2'>
            Error Loading Restaurant
          </h2>
          <p className='text-gray-600 mb-4'>{error}</p>
          <Button
            variant='primary'
            onClick={() => navigate("/restaurant/my-restaurants")}
          >
            Back to Restaurants
          </Button>
        </Card>
      </div>
    );
  }

  // Calculate order statistics
  const orderStats = {
    total: orders.length,
    pending: orders.filter((o) => o.status === "pending").length,
    preparing: orders.filter((o) => o.status === "preparing").length,
    ready: orders.filter((o) => o.status === "ready").length,
    delivered: orders.filter((o) => o.status === "delivered").length,
    totalRevenue: orders.reduce(
      (sum, order) => sum + parseFloat(order.total_amount || 0),
      0
    ),
  };

  return (
    <div className='p-6 animate-fade-in'>
      {/* Header */}
      <div className='mb-8'>
        <div className='flex items-center justify-between mb-4'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>
              Order Management - {restaurant?.name}
            </h1>
            <p className='text-gray-600'>
              Manage and track orders for your restaurant
            </p>
          </div>
          <div className='flex items-center space-x-3'>
            <Button
              variant='outline'
              onClick={handleRefreshOrders}
              disabled={refreshing}
            >
              <RefreshCw
                size={18}
                className={refreshing ? "animate-spin" : ""}
              />
              Refresh
            </Button>
            <Button
              variant='outline'
              onClick={() => navigate(`/restaurant/dashboard/${restaurantId}`)}
            >
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>

      {/* Success/Error Messages */}
      {success && (
        <div className='mb-6 p-4 bg-green-50 border border-green-200 rounded-lg'>
          <div className='flex items-center'>
            <CheckCircle className='text-green-500 mr-2' size={20} />
            <span className='text-green-700'>{success}</span>
          </div>
        </div>
      )}

      {error && (
        <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-lg'>
          <div className='flex items-center'>
            <AlertCircle className='text-red-500 mr-2' size={20} />
            <span className='text-red-700'>{error}</span>
          </div>
        </div>
      )}

      {/* Order Statistics */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-8'>
        <Card className='p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Total Orders</p>
              <p className='text-2xl font-bold text-gray-900'>
                {orderStats.total}
              </p>
            </div>
            <Package className='text-blue-600' size={24} />
          </div>
        </Card>

        <Card className='p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Pending</p>
              <p className='text-2xl font-bold text-yellow-600'>
                {orderStats.pending}
              </p>
            </div>
            <Clock className='text-yellow-600' size={24} />
          </div>
        </Card>

        <Card className='p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Preparing</p>
              <p className='text-2xl font-bold text-orange-600'>
                {orderStats.preparing}
              </p>
            </div>
            <ChefHat className='text-orange-600' size={24} />
          </div>
        </Card>

        <Card className='p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Ready</p>
              <p className='text-2xl font-bold text-green-600'>
                {orderStats.ready}
              </p>
            </div>
            <Package className='text-green-600' size={24} />
          </div>
        </Card>

        <Card className='p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Delivered</p>
              <p className='text-2xl font-bold text-gray-600'>
                {orderStats.delivered}
              </p>
            </div>
            <CheckCircle className='text-gray-600' size={24} />
          </div>
        </Card>

        <Card className='p-4'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-sm font-medium text-gray-600'>Revenue</p>
              <p className='text-2xl font-bold text-green-600'>
                {formatCurrency(orderStats.totalRevenue)}
              </p>
            </div>
            <DollarSign className='text-green-600' size={24} />
          </div>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className='p-6 mb-6'>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4'>
          {/* Search */}
          <div className='relative'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={18}
            />
            <input
              type='text'
              placeholder='Search orders...'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className='pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
          >
            <option value='all'>All Statuses</option>
            {orderStatuses.map((status) => (
              <option key={status.value} value={status.value}>
                {status.label}
              </option>
            ))}
          </select>

          {/* Date Filter */}
          <select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
          >
            <option value='all'>All Time</option>
            <option value='today'>Today</option>
            <option value='yesterday'>Yesterday</option>
            <option value='week'>This Week</option>
            <option value='month'>This Month</option>
          </select>

          {/* Sort By */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
          >
            <option value='created_at'>Order Date</option>
            <option value='total_amount'>Amount</option>
            <option value='status'>Status</option>
          </select>

          {/* Sort Order */}
          <select
            value={sortOrder}
            onChange={(e) => setSortOrder(e.target.value)}
            className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
          >
            <option value='desc'>Newest First</option>
            <option value='asc'>Oldest First</option>
          </select>
        </div>
      </Card>

      {/* Orders List */}
      <div className='space-y-4'>
        {filteredOrders.length === 0 ? (
          <Card className='p-8 text-center'>
            <Package className='mx-auto mb-4 text-gray-400' size={48} />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              {searchQuery || statusFilter !== "all" || dateFilter !== "all"
                ? "No orders found"
                : "No orders yet"}
            </h3>
            <p className='text-gray-500 mb-4'>
              {searchQuery || statusFilter !== "all" || dateFilter !== "all"
                ? "Try adjusting your search or filter criteria"
                : "Orders will appear here when customers place them"}
            </p>
          </Card>
        ) : (
          filteredOrders.map((order) => {
            const { date, time } = formatDateTime(order.created_at);

            return (
              <Card key={order.id} className='p-6'>
                <div className='flex items-start justify-between mb-4'>
                  <div className='flex items-start space-x-4'>
                    <div className='flex-shrink-0'>
                      <div className='w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center'>
                        <Package className='text-primary-600' size={20} />
                      </div>
                    </div>

                    <div className='flex-1'>
                      <div className='flex items-center space-x-3 mb-2'>
                        <h3 className='text-lg font-semibold text-gray-900'>
                          Order #{order.id}
                        </h3>
                        {getStatusBadge(order.status)}
                      </div>

                      <div className='grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600'>
                        <div className='flex items-center space-x-2'>
                          <User size={16} />
                          <span>
                            {order.customer?.name || "Unknown Customer"}
                          </span>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <Calendar size={16} />
                          <span>
                            {date} at {time}
                          </span>
                        </div>
                        <div className='flex items-center space-x-2'>
                          <DollarSign size={16} />
                          <span className='font-medium'>
                            {formatCurrency(order.total_amount)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className='flex items-center space-x-2'>
                    <Button
                      variant='outline'
                      size='small'
                      onClick={() => setSelectedOrder(order)}
                    >
                      <Eye size={16} className='mr-1' />
                      View Details
                    </Button>
                  </div>
                </div>

                {/* Order Items Preview */}
                {order.items && order.items.length > 0 && (
                  <div className='border-t pt-4'>
                    <p className='text-sm font-medium text-gray-700 mb-2'>
                      Items ({order.items.length}):
                    </p>
                    <div className='flex flex-wrap gap-2'>
                      {order.items.slice(0, 3).map((item, index) => (
                        <span
                          key={index}
                          className='px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded'
                        >
                          {item.quantity}x{" "}
                          {item.menu_item?.name || "Unknown Item"}
                        </span>
                      ))}
                      {order.items.length > 3 && (
                        <span className='px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded'>
                          +{order.items.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* Quick Actions */}
                <div className='border-t pt-4 mt-4'>
                  <div className='flex items-center justify-between'>
                    <div className='text-sm text-gray-500'>
                      {order.special_instructions && (
                        <div className='flex items-center space-x-1'>
                          <MessageCircle size={14} />
                          <span>Special instructions provided</span>
                        </div>
                      )}
                    </div>

                    <div className='flex items-center space-x-2'>
                      {/* Status Update Buttons */}
                      {order.status === "pending" && (
                        <>
                          <Button
                            variant='success'
                            size='small'
                            onClick={() =>
                              handleUpdateOrderStatus(order.id, "confirmed")
                            }
                          >
                            <CheckSquare size={14} className='mr-1' />
                            Confirm
                          </Button>
                          <Button
                            variant='danger'
                            size='small'
                            onClick={() =>
                              handleUpdateOrderStatus(order.id, "cancelled")
                            }
                          >
                            <XCircle size={14} className='mr-1' />
                            Cancel
                          </Button>
                        </>
                      )}

                      {order.status === "confirmed" && (
                        <Button
                          variant='primary'
                          size='small'
                          onClick={() =>
                            handleUpdateOrderStatus(order.id, "preparing")
                          }
                        >
                          <ChefHat size={14} className='mr-1' />
                          Start Preparing
                        </Button>
                      )}

                      {order.status === "preparing" && (
                        <Button
                          variant='success'
                          size='small'
                          onClick={() =>
                            handleUpdateOrderStatus(order.id, "ready")
                          }
                        >
                          <Package size={14} className='mr-1' />
                          Mark Ready
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </Card>
            );
          })
        )}
      </div>

      {/* Order Details Modal */}
      {selectedOrder && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto'>
            <div className='flex items-center justify-between mb-6'>
              <h3 className='text-xl font-semibold'>
                Order #{selectedOrder.id} Details
              </h3>
              <button
                onClick={() => setSelectedOrder(null)}
                className='text-gray-400 hover:text-gray-600'
              >
                <X size={24} />
              </button>
            </div>

            <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
              {/* Order Information */}
              <div>
                <h4 className='text-lg font-medium mb-4'>Order Information</h4>
                <div className='space-y-3'>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Status:</span>
                    {getStatusBadge(selectedOrder.status)}
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Order Date:</span>
                    <span>{formatDateTime(selectedOrder.created_at).date}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Order Time:</span>
                    <span>{formatDateTime(selectedOrder.created_at).time}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Payment Method:</span>
                    <span className='capitalize'>
                      {selectedOrder.payment_method?.replace("_", " ")}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>Payment Status:</span>
                    <Badge
                      variant={
                        selectedOrder.payment_status === "completed"
                          ? "success"
                          : "warning"
                      }
                    >
                      {selectedOrder.payment_status}
                    </Badge>
                  </div>
                </div>

                {selectedOrder.special_instructions && (
                  <div className='mt-4'>
                    <h5 className='font-medium mb-2'>Special Instructions:</h5>
                    <p className='text-gray-600 bg-gray-50 p-3 rounded'>
                      {selectedOrder.special_instructions}
                    </p>
                  </div>
                )}
              </div>

              {/* Customer Information */}
              <div>
                <h4 className='text-lg font-medium mb-4'>
                  Customer Information
                </h4>
                <div className='space-y-3'>
                  <div className='flex items-center space-x-2'>
                    <User size={16} className='text-gray-400' />
                    <span>
                      {selectedOrder.customer?.name || "Unknown Customer"}
                    </span>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Phone size={16} className='text-gray-400' />
                    <span>
                      {selectedOrder.customer?.phone || "No phone provided"}
                    </span>
                  </div>
                  <div className='flex items-start space-x-2'>
                    <MapPin size={16} className='text-gray-400 mt-1' />
                    <div>
                      <p className='font-medium'>Delivery Address:</p>
                      <p className='text-gray-600'>
                        {selectedOrder.delivery_address?.street_address}
                        <br />
                        {selectedOrder.delivery_address?.city},{" "}
                        {selectedOrder.delivery_address?.state}{" "}
                        {selectedOrder.delivery_address?.postal_code}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className='mt-6'>
              <h4 className='text-lg font-medium mb-4'>Order Items</h4>
              <div className='space-y-3'>
                {selectedOrder.items?.map((item, index) => (
                  <div
                    key={index}
                    className='flex items-center justify-between p-3 bg-gray-50 rounded'
                  >
                    <div className='flex items-center space-x-3'>
                      <div className='w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center'>
                        <Package size={20} className='text-gray-600' />
                      </div>
                      <div>
                        <p className='font-medium'>
                          {item.menu_item?.name || "Unknown Item"}
                        </p>
                        <p className='text-sm text-gray-600'>
                          Quantity: {item.quantity} ×{" "}
                          {formatCurrency(item.price_at_order)}
                        </p>
                        {item.special_requests && (
                          <p className='text-xs text-gray-500 mt-1'>
                            Note: {item.special_requests}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className='text-right'>
                      <p className='font-medium'>
                        {formatCurrency(item.quantity * item.price_at_order)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Summary */}
            <div className='mt-6 border-t pt-4'>
              <h4 className='text-lg font-medium mb-4'>Order Summary</h4>
              <div className='space-y-2'>
                <div className='flex justify-between'>
                  <span>Subtotal:</span>
                  <span>
                    {formatCurrency(
                      selectedOrder.total_amount -
                        selectedOrder.delivery_fee -
                        selectedOrder.tax_amount
                    )}
                  </span>
                </div>
                <div className='flex justify-between'>
                  <span>Delivery Fee:</span>
                  <span>{formatCurrency(selectedOrder.delivery_fee)}</span>
                </div>
                <div className='flex justify-between'>
                  <span>Tax:</span>
                  <span>{formatCurrency(selectedOrder.tax_amount)}</span>
                </div>
                <div className='flex justify-between font-bold text-lg border-t pt-2'>
                  <span>Total:</span>
                  <span>{formatCurrency(selectedOrder.total_amount)}</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className='mt-6 flex items-center justify-end space-x-3'>
              <Button variant='outline' onClick={() => setSelectedOrder(null)}>
                Close
              </Button>

              {/* Status Update Actions */}
              {selectedOrder.status === "pending" && (
                <>
                  <Button
                    variant='success'
                    onClick={() => {
                      handleUpdateOrderStatus(selectedOrder.id, "confirmed");
                      setSelectedOrder(null);
                    }}
                  >
                    <CheckSquare size={16} className='mr-2' />
                    Confirm Order
                  </Button>
                  <Button
                    variant='danger'
                    onClick={() => {
                      handleUpdateOrderStatus(selectedOrder.id, "cancelled");
                      setSelectedOrder(null);
                    }}
                  >
                    <XCircle size={16} className='mr-2' />
                    Cancel Order
                  </Button>
                </>
              )}

              {selectedOrder.status === "confirmed" && (
                <Button
                  variant='primary'
                  onClick={() => {
                    handleUpdateOrderStatus(selectedOrder.id, "preparing");
                    setSelectedOrder(null);
                  }}
                >
                  <ChefHat size={16} className='mr-2' />
                  Start Preparing
                </Button>
              )}

              {selectedOrder.status === "preparing" && (
                <Button
                  variant='success'
                  onClick={() => {
                    handleUpdateOrderStatus(selectedOrder.id, "ready");
                    setSelectedOrder(null);
                  }}
                >
                  <Package size={16} className='mr-2' />
                  Mark as Ready
                </Button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default RestaurantOrderManager;
