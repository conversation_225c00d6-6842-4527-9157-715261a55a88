"""
Cash on Delivery (COD) API Views

This module provides API endpoints for COD financial operations:
- Cash collection reporting
- Daily settlements
- Agent cash management
- COD analytics
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Sum, Count, Avg, Q
from django.utils import timezone
from datetime import timedelta, datetime, date
from decimal import Decimal

from .cod_models import (
    CODTransaction,
    DailyAgentSettlement,
    AgentCashFloat,
    CODFinancialSummary
)
from .cod_serializers import (
    CODTransactionSerializer,
    DailyAgentSettlementSerializer,
    AgentCashFloatSerializer,
    CashCollectionSerializer,
    SettlementRequestSerializer
)
from orders.models import Order


class CODTransactionViewSet(viewsets.ModelViewSet):
    """ViewSet for COD transaction management"""
    serializer_class = CODTransactionSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset based on user role"""
        user = self.request.user
        
        if user.role == 'delivery_agent':
            return CODTransaction.objects.filter(delivery_agent=user)
        elif user.role == 'admin':
            return CODTransaction.objects.all()
        else:
            return CODTransaction.objects.none()
    
    @action(detail=False, methods=['post'])
    def collect_cash(self, request):
        """Report cash collection from customer"""
        agent = request.user
        
        if agent.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can report cash collection'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = CashCollectionSerializer(data=request.data)
        if serializer.is_valid():
            order_id = serializer.validated_data['order_id']
            cash_collected = serializer.validated_data['cash_collected']
            customer_paid = serializer.validated_data.get('customer_paid_amount', cash_collected)
            gps_lat = serializer.validated_data.get('gps_latitude')
            gps_lng = serializer.validated_data.get('gps_longitude')
            notes = serializer.validated_data.get('notes', '')
            
            try:
                # Get the order
                order = Order.objects.get(
                    id=order_id,
                    delivery_agent=agent,
                    status='delivered',
                    payment_method='cash_on_delivery'
                )
                
                # Check if COD transaction already exists
                if hasattr(order, 'cod_transaction'):
                    return Response(
                        {'error': 'Cash collection already reported for this order'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                # Calculate change given
                change_given = customer_paid - cash_collected if customer_paid > cash_collected else Decimal('0.00')
                
                # Get agent's delivery earnings
                try:
                    delivery_earnings = order.delivery_earnings.net_earnings
                except:
                    delivery_earnings = Decimal('5.00')  # Default fallback
                
                # Create COD transaction
                cod_transaction = CODTransaction.objects.create(
                    order=order,
                    delivery_agent=agent,
                    order_total=order.total_amount,
                    cash_collected=cash_collected,
                    customer_paid_amount=customer_paid,
                    change_given=change_given,
                    collection_time=timezone.now(),
                    agent_delivery_earnings=delivery_earnings,
                    amount_to_remit=cash_collected - delivery_earnings,
                    gps_latitude=gps_lat,
                    gps_longitude=gps_lng,
                    collection_notes=notes
                )
                
                return Response({
                    'status': 'success',
                    'transaction_id': cod_transaction.id,
                    'cash_collected': cash_collected,
                    'agent_earnings': delivery_earnings,
                    'amount_to_remit': cod_transaction.amount_to_remit,
                    'change_given': change_given
                }, status=status.HTTP_201_CREATED)
                
            except Order.DoesNotExist:
                return Response(
                    {'error': 'Order not found or not eligible for COD collection'},
                    status=status.HTTP_404_NOT_FOUND
                )
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def daily_collections(self, request):
        """Get agent's daily cash collections"""
        agent = request.user
        
        if agent.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can view collections'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get date from query params (default to today)
        date_str = request.GET.get('date', timezone.now().date().isoformat())
        try:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {'error': 'Invalid date format. Use YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get transactions for the date
        transactions = CODTransaction.objects.filter(
            delivery_agent=agent,
            collection_time__date=target_date
        )
        
        # Calculate summary
        summary = transactions.aggregate(
            total_orders=Count('id'),
            total_collected=Sum('cash_collected'),
            total_earnings=Sum('agent_delivery_earnings'),
            total_to_remit=Sum('amount_to_remit')
        )
        
        # Get settlement status
        try:
            settlement = DailyAgentSettlement.objects.get(
                delivery_agent=agent,
                settlement_date=target_date
            )
            settlement_data = DailyAgentSettlementSerializer(settlement).data
        except DailyAgentSettlement.DoesNotExist:
            settlement_data = None
        
        return Response({
            'date': target_date,
            'summary': {
                'total_orders': summary['total_orders'] or 0,
                'total_collected': summary['total_collected'] or Decimal('0.00'),
                'total_earnings': summary['total_earnings'] or Decimal('0.00'),
                'total_to_remit': summary['total_to_remit'] or Decimal('0.00'),
            },
            'settlement': settlement_data,
            'transactions': CODTransactionSerializer(transactions, many=True).data
        })
    
    @action(detail=False, methods=['post'])
    def settle_cash(self, request):
        """Process end-of-day cash settlement"""
        agent = request.user
        
        if agent.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can settle cash'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = SettlementRequestSerializer(data=request.data)
        if serializer.is_valid():
            settlement_date = serializer.validated_data.get('settlement_date', timezone.now().date())
            settlement_amount = serializer.validated_data['settlement_amount']
            settlement_method = serializer.validated_data['settlement_method']
            reference = serializer.validated_data.get('reference', '')
            notes = serializer.validated_data.get('notes', '')
            
            # Get or create daily settlement record
            settlement, created = DailyAgentSettlement.objects.get_or_create(
                delivery_agent=agent,
                settlement_date=settlement_date,
                defaults={
                    'settlement_deadline': timezone.now() + timedelta(hours=24)
                }
            )
            
            # Calculate daily totals if new settlement
            if created:
                settlement.calculate_daily_totals()
            
            # Process settlement
            settlement.mark_as_settled(
                amount=settlement_amount,
                method=settlement_method,
                reference=reference
            )
            settlement.notes = notes
            settlement.save()
            
            return Response({
                'status': 'success',
                'settlement_id': settlement.id,
                'settlement_amount': settlement_amount,
                'total_due': settlement.total_remittance_due,
                'settlement_status': settlement.status,
                'reference': settlement.settlement_reference
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def agent_balance(self, request):
        """Get agent's current cash balance and pending settlements"""
        agent = request.user
        
        if agent.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can view balance'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get pending settlements
        pending_settlements = DailyAgentSettlement.objects.filter(
            delivery_agent=agent,
            status__in=['pending', 'partial']
        )
        
        # Calculate totals
        total_pending = pending_settlements.aggregate(
            total=Sum('total_remittance_due')
        )['total'] or Decimal('0.00')
        
        # Get today's collections
        today_transactions = CODTransaction.objects.filter(
            delivery_agent=agent,
            collection_time__date=timezone.now().date(),
            settlement_status='pending'
        )
        
        today_summary = today_transactions.aggregate(
            collected=Sum('cash_collected'),
            earnings=Sum('agent_delivery_earnings'),
            to_remit=Sum('amount_to_remit')
        )
        
        # Get current float
        current_float = AgentCashFloat.objects.filter(
            delivery_agent=agent,
            status='active'
        ).aggregate(total=Sum('float_amount'))['total'] or Decimal('0.00')
        
        return Response({
            'agent_id': agent.id,
            'agent_name': agent.get_full_name(),
            'current_float': current_float,
            'today_collections': {
                'total_collected': today_summary['collected'] or Decimal('0.00'),
                'agent_earnings': today_summary['earnings'] or Decimal('0.00'),
                'amount_to_remit': today_summary['to_remit'] or Decimal('0.00'),
                'orders_count': today_transactions.count()
            },
            'pending_settlements': {
                'total_amount': total_pending,
                'settlements_count': pending_settlements.count(),
                'settlements': DailyAgentSettlementSerializer(pending_settlements, many=True).data
            },
            'cash_in_hand': (today_summary['collected'] or Decimal('0.00')) + current_float,
            'net_position': current_float + (today_summary['earnings'] or Decimal('0.00'))
        })


class CODAdminViewSet(viewsets.ViewSet):
    """Admin views for COD management"""
    permission_classes = [IsAuthenticated]
    
    def get_permissions(self):
        """Only allow admin users"""
        permissions = super().get_permissions()
        if self.request.user.role != 'admin':
            from rest_framework.permissions import DenyAll
            permissions.append(DenyAll())
        return permissions
    
    @action(detail=False, methods=['get'])
    def daily_settlements(self, request):
        """Get all daily settlements for admin review"""
        date_str = request.GET.get('date', timezone.now().date().isoformat())
        try:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response(
                {'error': 'Invalid date format. Use YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        settlements = DailyAgentSettlement.objects.filter(
            settlement_date=target_date
        ).select_related('delivery_agent')
        
        # Calculate summary
        summary = settlements.aggregate(
            total_agents=Count('delivery_agent', distinct=True),
            total_collected=Sum('total_cash_collected'),
            total_remittance=Sum('total_remittance_due'),
            total_settled=Sum('settlement_amount'),
            pending_count=Count('id', filter=Q(status='pending')),
            completed_count=Count('id', filter=Q(status='completed'))
        )
        
        return Response({
            'date': target_date,
            'summary': summary,
            'settlements': DailyAgentSettlementSerializer(settlements, many=True).data
        })
    
    @action(detail=False, methods=['post'])
    def verify_settlement(self, request):
        """Verify an agent's settlement"""
        settlement_id = request.data.get('settlement_id')
        verified = request.data.get('verified', True)
        admin_notes = request.data.get('admin_notes', '')
        
        try:
            settlement = DailyAgentSettlement.objects.get(id=settlement_id)
            settlement.is_verified = verified
            settlement.verified_by = request.user
            settlement.verified_at = timezone.now()
            settlement.admin_notes = admin_notes
            settlement.save()
            
            return Response({
                'status': 'success',
                'settlement_id': settlement_id,
                'verified': verified,
                'verified_by': request.user.get_full_name()
            })
            
        except DailyAgentSettlement.DoesNotExist:
            return Response(
                {'error': 'Settlement not found'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=False, methods=['get'])
    def cash_flow_report(self, request):
        """Generate cash flow report for admin"""
        period = request.GET.get('period', 'week')  # week, month, year
        
        if period == 'week':
            start_date = timezone.now() - timedelta(days=7)
        elif period == 'month':
            start_date = timezone.now() - timedelta(days=30)
        elif period == 'year':
            start_date = timezone.now() - timedelta(days=365)
        else:
            start_date = timezone.now() - timedelta(days=7)
        
        # Get COD transactions for the period
        transactions = CODTransaction.objects.filter(
            collection_time__gte=start_date
        )
        
        # Calculate metrics
        metrics = transactions.aggregate(
            total_orders=Count('id'),
            total_collected=Sum('cash_collected'),
            total_agent_earnings=Sum('agent_delivery_earnings'),
            total_remittance=Sum('amount_to_remit'),
            avg_order_value=Avg('cash_collected')
        )
        
        # Settlement metrics
        settlements = DailyAgentSettlement.objects.filter(
            settlement_date__gte=start_date.date()
        )
        
        settlement_metrics = settlements.aggregate(
            total_settlements=Count('id'),
            completed_settlements=Count('id', filter=Q(status='completed')),
            pending_amount=Sum('total_remittance_due', filter=Q(status='pending')),
            settled_amount=Sum('settlement_amount')
        )
        
        return Response({
            'period': period,
            'start_date': start_date.date(),
            'end_date': timezone.now().date(),
            'cod_metrics': metrics,
            'settlement_metrics': settlement_metrics,
            'collection_rate': (metrics['total_collected'] or 0) / max(metrics['total_orders'] or 1, 1) * 100,
            'settlement_rate': (settlement_metrics['completed_settlements'] or 0) / max(settlement_metrics['total_settlements'] or 1, 1) * 100
        })
