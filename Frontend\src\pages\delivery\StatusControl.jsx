import React, { useState, useEffect } from "react";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import {
  CheckCircle,
  Clock,
  XCircle,
  User,
  Phone,
  AlertCircle,
} from "lucide-react";

const StatusControl = () => {
  const [agentData, setAgentData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");
  const [searchType, setSearchType] = useState("agent_id"); // 'agent_id' or 'phone'
  const [searchValue, setSearchValue] = useState("");

  const statusOptions = [
    {
      value: "approved",
      label: "Approved",
      icon: CheckCircle,
      color: "text-green-600",
    },
    {
      value: "active",
      label: "Active",
      icon: CheckCircle,
      color: "text-green-600",
    },
    {
      value: "inactive",
      label: "Inactive",
      icon: XCircle,
      color: "text-red-600",
    },
  ];

  const handleSearch = async () => {
    if (!searchValue.trim()) {
      setError("Please enter a search value");
      return;
    }

    setLoading(true);
    setError("");
    setSuccess("");

    try {
      const result = await deliveryAgentApi.checkApplicationStatus(
        searchType === "agent_id" ? searchValue : null,
        searchType === "phone" ? searchValue : null
      );

      if (result.success) {
        setAgentData(result.data.data);
        setSuccess("Agent found successfully!");
      } else {
        setError(result.error?.message || "Agent not found");
        setAgentData(null);
      }
    } catch (err) {
      setError("Failed to search for agent");
      setAgentData(null);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (newStatus) => {
    if (!agentData) return;

    setLoading(true);
    setError("");
    setSuccess("");

    try {
      const result = await deliveryAgentApi.updateStatus(
        agentData.agent_id,
        newStatus
      );

      if (result.success) {
        setAgentData((prev) => ({
          ...prev,
          application_status: newStatus,
        }));
        setSuccess(`Status updated to ${newStatus} successfully!`);
      } else {
        setError(result.error?.message || "Failed to update status");
      }
    } catch (err) {
      setError("Failed to update status");
    } finally {
      setLoading(false);
    }
  };

  const getCurrentStatusIcon = () => {
    if (!agentData) return null;
    const status = statusOptions.find(
      (s) => s.value === agentData.application_status
    );
    if (!status) return null;
    const IconComponent = status.icon;
    return <IconComponent className={`h-6 w-6 ${status.color}`} />;
  };

  return (
    <div className='min-h-screen bg-gray-50 py-8'>
      <div className='max-w-4xl mx-auto px-4'>
        <div className='text-center mb-8'>
          <h1 className='text-3xl font-bold text-gray-900 mb-2'>
            Delivery Agent Status Control
          </h1>
          <p className='text-gray-600'>
            Search for delivery agents and update their status
          </p>
        </div>

        {/* Search Section */}
        <Card className='mb-8'>
          <div className='p-6'>
            <h2 className='text-xl font-semibold mb-4'>Search Agent</h2>

            <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Search By
                </label>
                <select
                  value={searchType}
                  onChange={(e) => setSearchType(e.target.value)}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                >
                  <option value='agent_id'>Agent ID</option>
                  <option value='phone'>Phone Number</option>
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  {searchType === "agent_id" ? "Agent ID" : "Phone Number"}
                </label>
                <input
                  type='text'
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  placeholder={
                    searchType === "agent_id" ? "DA123456" : "+93701234567"
                  }
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                />
              </div>

              <div className='flex items-end'>
                <Button
                  onClick={handleSearch}
                  disabled={loading}
                  className='w-full'
                >
                  {loading ? "Searching..." : "Search"}
                </Button>
              </div>
            </div>

            {error && (
              <div className='flex items-center gap-2 text-red-600 bg-red-50 p-3 rounded-lg'>
                <AlertCircle className='h-5 w-5' />
                <span>{error}</span>
              </div>
            )}

            {success && (
              <div className='flex items-center gap-2 text-green-600 bg-green-50 p-3 rounded-lg'>
                <CheckCircle className='h-5 w-5' />
                <span>{success}</span>
              </div>
            )}
          </div>
        </Card>

        {/* Agent Information */}
        {agentData && (
          <Card className='mb-8'>
            <div className='p-6'>
              <h2 className='text-xl font-semibold mb-4'>Agent Information</h2>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-6 mb-6'>
                <div className='space-y-3'>
                  <div className='flex items-center gap-3'>
                    <User className='h-5 w-5 text-gray-500' />
                    <div>
                      <p className='text-sm text-gray-500'>Full Name</p>
                      <p className='font-medium'>{agentData.full_name}</p>
                    </div>
                  </div>

                  <div className='flex items-center gap-3'>
                    <Phone className='h-5 w-5 text-gray-500' />
                    <div>
                      <p className='text-sm text-gray-500'>Phone Number</p>
                      <p className='font-medium'>{agentData.phone_number}</p>
                    </div>
                  </div>
                </div>

                <div className='space-y-3'>
                  <div className='flex items-center gap-3'>
                    <div className='w-5 h-5 flex items-center justify-center'>
                      <span className='text-sm font-bold text-gray-500'>#</span>
                    </div>
                    <div>
                      <p className='text-sm text-gray-500'>Agent ID</p>
                      <p className='font-medium'>{agentData.agent_id}</p>
                    </div>
                  </div>

                  <div className='flex items-center gap-3'>
                    {getCurrentStatusIcon()}
                    <div>
                      <p className='text-sm text-gray-500'>Current Status</p>
                      <p className='font-medium capitalize'>
                        {agentData.application_status}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Update Buttons */}
              <div>
                <h3 className='text-lg font-medium mb-3'>Update Status</h3>
                <div className='flex flex-wrap gap-3'>
                  {statusOptions.map((status) => {
                    const IconComponent = status.icon;
                    const isCurrentStatus =
                      agentData.application_status === status.value;

                    return (
                      <Button
                        key={status.value}
                        onClick={() => handleStatusUpdate(status.value)}
                        disabled={loading || isCurrentStatus}
                        variant={isCurrentStatus ? "primary" : "outline"}
                        className={`flex items-center gap-2 ${
                          isCurrentStatus ? "opacity-75 cursor-not-allowed" : ""
                        }`}
                      >
                        <IconComponent className='h-4 w-4' />
                        {status.label}
                        {isCurrentStatus && (
                          <span className='text-xs'>(Current)</span>
                        )}
                      </Button>
                    );
                  })}
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <div className='p-6'>
            <h2 className='text-xl font-semibold mb-4'>Instructions</h2>
            <div className='space-y-2 text-gray-600'>
              <p>
                • Search for delivery agents using their Agent ID or Phone
                Number
              </p>
              <p>
                • Delivery agents can control their own status (approved,
                active, inactive)
              </p>
              <p>
                • All new agents are automatically approved and can start
                working immediately
              </p>
              <p>
                • Status changes are immediate and will be reflected in the
                system
              </p>
              <p>
                • Use this interface to help agents manage their availability
              </p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default StatusControl;
