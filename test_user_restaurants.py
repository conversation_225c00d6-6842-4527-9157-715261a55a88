#!/usr/bin/env python3
"""
Test user restaurants endpoint to see if restaurant owners can see their own restaurants
"""

import requests
import json

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"

def test_user_restaurants():
    """Test the user restaurants functionality"""
    
    print("🧪 Testing User Restaurants Endpoint")
    print("=" * 60)
    
    # Step 1: Login as a restaurant owner
    print("🔐 Step 1: Logging in as restaurant owner...")
    
    # Try to login with restaurant owner credentials
    restaurant_login_data = {
        "user_name": "restaurant_owner",
        "password": "restaurant123"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=headers,
            data=json.dumps(restaurant_login_data)
        )
        
        print(f"   📡 Restaurant Owner Login Status: {response.status_code}")
        
        if response.status_code != 200:
            print("   ❌ Restaurant owner login failed, trying to create restaurant owner...")
            
            # Create restaurant owner user
            restaurant_user_data = {
                "name": "Restaurant Owner",
                "user_name": "restaurant_owner",
                "email": "<EMAIL>",
                "phone": "+93701000001",
                "password": "restaurant123",
                "confirm_password": "restaurant123",
                "role": "restaurant"
            }
            
            response = requests.post(
                f"{API_BASE_URL}/auth/register/",
                headers=headers,
                data=json.dumps(restaurant_user_data)
            )
            
            print(f"   📡 Restaurant Owner Registration Status: {response.status_code}")
            
            if response.status_code == 201:
                print("   ✅ Restaurant owner created, now logging in...")
                
                # Login with new restaurant owner
                response = requests.post(
                    f"{API_BASE_URL}/auth/login/",
                    headers=headers,
                    data=json.dumps(restaurant_login_data)
                )
                
                if response.status_code != 200:
                    print(f"   ❌ Restaurant owner login still failed: {response.text}")
                    return False
            else:
                print(f"   ❌ Restaurant owner creation failed: {response.text}")
                return False
        
        result = response.json()
        restaurant_token = result['data']['access_token']
        print("   ✅ Restaurant owner login successful!")
        
        # Step 2: Test getting user's restaurants
        print(f"\n📋 Step 2: Testing my_restaurants endpoint...")
        
        restaurant_headers = {
            "Authorization": f"Bearer {restaurant_token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(
            f"{API_BASE_URL}/restaurant/restaurants/my_restaurants/",
            headers=restaurant_headers
        )
        
        print(f"   📡 My Restaurants Status: {response.status_code}")
        print(f"   📄 My Restaurants Response: {response.text}")
        
        if response.status_code == 200:
            restaurants = response.json()
            print(f"   ✅ Found {len(restaurants)} restaurants for this user")
            
            # Show restaurant details
            for i, restaurant in enumerate(restaurants):
                print(f"      {i+1}. {restaurant.get('name')} - Active: {restaurant.get('is_active')} - Verified: {restaurant.get('is_verified')}")
                print(f"         Created: {restaurant.get('created_at', 'N/A')}")
                print()
            
            return True
        else:
            print("   ❌ Failed to get user restaurants")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main test function"""
    success = test_user_restaurants()
    
    print("\n" + "=" * 60)
    print("🏁 USER RESTAURANTS TEST RESULTS")
    print("=" * 60)
    
    if success:
        print("🎉 SUCCESS: User restaurants endpoint is working!")
        print("✅ Restaurant owner authentication works")
        print("✅ Restaurant owner can view their own restaurants")
        print("✅ Restaurants are returned regardless of active status")
    else:
        print("❌ FAILED: User restaurants endpoint has issues")
        print("🔍 Check the error messages above for details")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
