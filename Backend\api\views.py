# api/views.py
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import generics, permissions, status

from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from users.models import  User
from .serializers import (
 
    UserLoginSerializer, 
    UserRegistrationSerializer,
    EmailVerificationSerializer,
    PasswordChangeSerializer
)
from users.models import OTPVerification

class UserLoginView(APIView):
    def post(self, request):
        # Debug logging
        print(f"🔍 Login request data: {request.data}")
        print(f"🔍 Request headers: {dict(request.headers)}")

        serializer = UserLoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']

            # Generate JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = str(refresh.access_token)
            refresh_token = str(refresh)

            # Return response with tokens, role, and redirection info
            return Response({
                'success': True,
                'message': 'Login successful',
                'data': {
                    'access_token': access_token,
                    'refresh_token': refresh_token,
                    'user': {
                        'id': user.id,
                        'name': user.name,
                        'username': user.user_name,
                        'email': user.email,
                        'role': user.role,
                        'is_verified': user.is_verified
                    },
                    'redirect_to': f'/dashboard/{user.role}'
                }
            }, status=status.HTTP_200_OK)

        # Debug logging for validation errors
        print(f"🔍 Validation errors: {serializer.errors}")

        # Format validation errors for better frontend handling
        errors = {}
        for field, field_errors in serializer.errors.items():
            if isinstance(field_errors, list):
                errors[field] = field_errors[0] if field_errors else 'Invalid value'
            else:
                errors[field] = str(field_errors)

        return Response({
            'success': False,
            'message': 'Login failed',
            'errors': errors
        }, status=status.HTTP_400_BAD_REQUEST)
    





class UserRegistrationView(APIView):
    def post(self, request):
        serializer = UserRegistrationSerializer(data=request.data)
        if serializer.is_valid():
            try:
                user = serializer.save()

                return Response({
                    'success': True,
                    'message': f'Registration successful! Please check {user.email} for your verification code.',
                    'data': {
                        'email': user.email,
                        'user_id': user.id,
                        'username': user.user_name,
                        'name': user.name,
                        'role': user.role,
                        'requires_verification': True
                    }
                }, status=status.HTTP_201_CREATED)
            except Exception as e:
                return Response({
                    'success': False,
                    'message': 'Registration failed',
                    'errors': {
                        'non_field_errors': f'Failed to create account: {str(e)}'
                    }
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Format validation errors for better frontend handling
        errors = {}
        for field, field_errors in serializer.errors.items():
            if isinstance(field_errors, list):
                errors[field] = field_errors[0] if field_errors else 'Invalid value'
            else:
                errors[field] = str(field_errors)

        return Response({
            'success': False,
            'message': 'Registration failed',
            'errors': errors
        }, status=status.HTTP_400_BAD_REQUEST)

class VerifyEmailView(APIView):
    def post(self, request):
        serializer = EmailVerificationSerializer(data=request.data)
        if serializer.is_valid():
            try:
                user = serializer.validated_data['user']
                otp_record = serializer.validated_data['otp_record']

                # Mark OTP as used
                otp_record.is_used = True
                otp_record.save()

                # Mark user as verified
                user.is_verified = True
                user.save()

                # Generate JWT tokens
                refresh = RefreshToken.for_user(user)
                access_token = str(refresh.access_token)
                refresh_token = str(refresh)

                return Response({
                    'success': True,
                    'message': 'Email verified successfully! You can now login.',
                    'data': {
                        'access_token': access_token,
                        'refresh_token': refresh_token,
                        'user': {
                            'id': user.id,
                            'name': user.name,
                            'username': user.user_name,
                            'email': user.email,
                            'role': user.role,
                            'is_verified': True
                        },
                        'redirect_to': f'/dashboard/{user.role}'
                    }
                }, status=status.HTTP_200_OK)
            except Exception as e:
                return Response({
                    'success': False,
                    'message': 'Verification failed',
                    'errors': {
                        'non_field_errors': 'Failed to verify email. Please try again.'
                    }
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Format validation errors for better frontend handling
        errors = {}
        for field, field_errors in serializer.errors.items():
            if isinstance(field_errors, list):
                errors[field] = field_errors[0] if field_errors else 'Invalid value'
            else:
                errors[field] = str(field_errors)

        return Response({
            'success': False,
            'message': 'Email verification failed',
            'errors': errors
        }, status=status.HTTP_400_BAD_REQUEST)

class ResendOTPView(APIView):
    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({'error': 'User with this email does not exist'}, status=status.HTTP_404_NOT_FOUND)
        
        # Generate and send new OTP
        OTPVerification.generate_otp(user, 'registration').send_otp_email()
        
        return Response({
            'message': 'New OTP has been sent to your email',
            'email': user.email
        }, status=status.HTTP_200_OK)

class PasswordChangeView(APIView):
    def post(self, request):
        serializer = PasswordChangeSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            otp_record = serializer.validated_data['otp_record']
            new_password = serializer.validated_data['new_password']
            
            # Mark OTP as used
            otp_record.is_used = True
            otp_record.save()
            
            # Update password
            user.set_password(new_password)
            user.save()
            
            return Response({
                'message': 'Password changed successfully'
            }, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class RequestPasswordChangeOTPView(APIView):
    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({'error': 'Email is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({'error': 'User with this email does not exist'}, status=status.HTTP_404_NOT_FOUND)
        
        # Generate and send OTP for password change
        OTPVerification.generate_otp(user, 'password_reset').send_otp_email()
        
        return Response({
            'message': 'OTP for password change has been sent to your email',
            'email': user.email
        }, status=status.HTTP_200_OK)


class TokenRefreshAPIView(APIView):
    """
    Custom token refresh view that handles refresh token validation
    and returns new access tokens
    """
    def post(self, request):
        try:
            refresh_token = request.data.get('refresh')

            if not refresh_token:
                return Response(
                    {'error': 'Refresh token is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate and refresh the token
            refresh = RefreshToken(refresh_token)
            access_token = str(refresh.access_token)

            # Get user information
            user = User.objects.get(id=refresh['user_id'])

            return Response({
                'success': True,
                'access_token': access_token,
                'refresh_token': str(refresh),  # Return the same refresh token
                'user': {
                    'id': user.id,
                    'name': user.name,
                    'username': user.user_name,
                    'email': user.email,
                    'role': user.role,
                    'is_verified': user.is_verified
                }
            }, status=status.HTTP_200_OK)

        except TokenError as e:
            return Response(
                {'error': 'Invalid or expired refresh token'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': 'Token refresh failed'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
