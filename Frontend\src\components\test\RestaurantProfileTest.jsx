import React, { useState } from 'react';
import { restaurantApi } from '../../utils/restaurantApi';

const RestaurantProfileTest = () => {
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState(false);
  const [restaurantId, setRestaurantId] = useState('20'); // Default to first restaurant

  const testGetRestaurant = async () => {
    setLoading(true);
    try {
      const result = await restaurantApi.getRestaurant(restaurantId);
      setResults(prev => ({ ...prev, getRestaurant: result }));
    } catch (error) {
      setResults(prev => ({ ...prev, getRestaurant: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  const testUpdateRestaurant = async () => {
    setLoading(true);
    try {
      // First get the restaurant
      const getResult = await restaurantApi.getRestaurant(restaurantId);
      
      if (getResult.success) {
        // Update with new data
        const updateData = {
          ...getResult.data,
          name: `${getResult.data.name} - Updated ${new Date().toLocaleTimeString()}`,
          description: `Updated description at ${new Date().toLocaleString()}`,
        };
        
        const result = await restaurantApi.patchRestaurant(restaurantId, updateData);
        setResults(prev => ({ ...prev, updateRestaurant: result }));
      } else {
        setResults(prev => ({ ...prev, updateRestaurant: { success: false, error: 'Failed to get restaurant first' } }));
      }
    } catch (error) {
      setResults(prev => ({ ...prev, updateRestaurant: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  const testPatchRestaurant = async () => {
    setLoading(true);
    try {
      const patchData = {
        description: `Patched description at ${new Date().toLocaleString()}`,
        contact_number: '+93 70 123 4567',
        opening_time: '10:30:00',
        closing_time: '23:30:00',
        cuisine_type_ids: [2], // Test cuisine type update (Chinese)
      };

      console.log("Testing cuisine type update with data:", patchData);
      const result = await restaurantApi.patchRestaurant(restaurantId, patchData);
      console.log("Patch result:", result);

      setResults(prev => ({ ...prev, patchRestaurant: result }));
    } catch (error) {
      setResults(prev => ({ ...prev, patchRestaurant: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  const testGetUserRestaurants = async () => {
    setLoading(true);
    try {
      const result = await restaurantApi.getUserRestaurants();

      // Also check user context for email
      const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");

      setResults(prev => ({
        ...prev,
        getUserRestaurants: {
          ...result,
          userEmail: user.email,
          userInfo: {
            name: user.name,
            email: user.email,
            role: user.role
          }
        }
      }));
    } catch (error) {
      setResults(prev => ({ ...prev, getUserRestaurants: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  const testCuisineTypes = async () => {
    setLoading(true);
    try {
      console.log("Testing cuisine types API...");

      // Test without authentication first
      const response1 = await fetch('http://127.0.0.1:8000/api/restaurant/cuisine-types/');
      console.log("Response without auth:", response1.status);

      // Test with authentication
      const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
      const token = user.access_token || user.token;

      const response2 = await fetch('http://127.0.0.1:8000/api/restaurant/cuisine-types/', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        },
      });
      console.log("Response with auth:", response2.status);

      const data = response2.ok ? await response2.json() : await response1.json();
      console.log("Cuisine types data:", data);

      setResults(prev => ({
        ...prev,
        cuisineTypes: {
          success: response2.ok || response1.ok,
          data,
          count: Array.isArray(data) ? data.length : 0,
          status: response2.status,
          statusWithoutAuth: response1.status,
          hasAuth: !!token
        }
      }));
    } catch (error) {
      console.error("Cuisine types test error:", error);
      setResults(prev => ({ ...prev, cuisineTypes: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  const testDirectAPI = async () => {
    setLoading(true);
    try {
      // Test direct API call with cuisine type and time updates
      const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
      const token = user.access_token || user.token;

      const response = await fetch(`http://127.0.0.1:8000/api/restaurant/restaurants/${restaurantId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          description: `Direct API test at ${new Date().toLocaleString()}`,
          opening_time: '11:00:00',
          closing_time: '23:00:00',
          cuisine_type_ids: [2], // Test cuisine type update
        }),
      });

      const data = await response.json();

      setResults(prev => ({
        ...prev,
        directAPI: {
          success: response.ok,
          data,
          status: response.status,
          statusText: response.statusText
        }
      }));
    } catch (error) {
      setResults(prev => ({ ...prev, directAPI: { success: false, error: error.message } }));
    }
    setLoading(false);
  };

  return (
    <div className="mx-auto p-6 max-w-4xl">
      <h1 className="mb-6 font-bold text-2xl">Restaurant Profile API Test</h1>
      
      <div className="mb-4">
        <label className="block mb-2 font-medium text-sm">Restaurant ID:</label>
        <input
          type="text"
          value={restaurantId}
          onChange={(e) => setRestaurantId(e.target.value)}
          className="px-3 py-2 border rounded w-32"
          placeholder="Restaurant ID"
        />
      </div>

      <div className="gap-4 grid grid-cols-2 md:grid-cols-3 mb-6">
        <button
          onClick={testGetRestaurant}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 disabled:opacity-50 px-4 py-2 rounded text-white"
        >
          Get Restaurant
        </button>
        <button
          onClick={testUpdateRestaurant}
          disabled={loading}
          className="bg-green-500 hover:bg-green-600 disabled:opacity-50 px-4 py-2 rounded text-white"
        >
          Update Restaurant
        </button>
        <button
          onClick={testPatchRestaurant}
          disabled={loading}
          className="bg-yellow-500 hover:bg-yellow-600 disabled:opacity-50 px-4 py-2 rounded text-white"
        >
          Patch Restaurant
        </button>
        <button
          onClick={testGetUserRestaurants}
          disabled={loading}
          className="bg-purple-500 hover:bg-purple-600 disabled:opacity-50 px-4 py-2 rounded text-white"
        >
          Get User Restaurants
        </button>
        <button
          onClick={testCuisineTypes}
          disabled={loading}
          className="bg-indigo-500 hover:bg-indigo-600 disabled:opacity-50 px-4 py-2 rounded text-white"
        >
          Get Cuisine Types
        </button>
        <button
          onClick={testDirectAPI}
          disabled={loading}
          className="bg-red-500 hover:bg-red-600 disabled:opacity-50 px-4 py-2 rounded text-white"
        >
          Direct API Test
        </button>
      </div>

      {loading && (
        <div className="py-4 text-center">
          <div className="inline-block border-b-2 border-blue-500 rounded-full w-8 h-8 animate-spin"></div>
          <p className="mt-2">Testing API...</p>
        </div>
      )}

      <div className="space-y-6">
        {Object.entries(results).map(([key, result]) => (
          <div key={key} className="p-4 border rounded-lg">
            <h3 className="mb-2 font-semibold text-lg capitalize">{key} Results</h3>
            <div className="bg-gray-100 p-3 rounded max-h-96 overflow-auto">
              <pre className="text-sm">{JSON.stringify(result, null, 2)}</pre>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RestaurantProfileTest;
