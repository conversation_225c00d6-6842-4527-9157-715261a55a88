import React, { useState, useEffect } from "react";
import {
  Star,
  Gift,
  TrendingUp,
  Crown,
  Award,
  Trophy,
  ChevronRight,
  Sparkles,
} from "lucide-react";
import { useLoyalty } from "../../context/LoyaltyContext";
import { useAuth } from "../../context/AuthContext";
import Card from "../common/Card";
import Button from "../common/Button";
import Badge from "../common/Badge";

const LoyaltyWidget = ({ 
  className = "", 
  variant = "full", // "full", "compact", "minimal"
  showActions = true,
  onViewDetails,
  onViewRewards 
}) => {
  const { user } = useAuth();
  const {
    loyaltyData,
    pointsHistory,
    redeemedRewards,
    LOYALTY_TIERS,
    getNextTier,
    getPointsToNextTier,
  } = useLoyalty();

  const [showAnimation, setShowAnimation] = useState(false);

  // Show animation when points increase
  useEffect(() => {
    if (loyaltyData && pointsHistory.length > 0) {
      const latestEntry = pointsHistory[0];
      if (latestEntry.type === "earned") {
        setShowAnimation(true);
        setTimeout(() => setShowAnimation(false), 2000);
      }
    }
  }, [pointsHistory]);

  if (!user || user.role !== "customer" || !loyaltyData) {
    return null;
  }

  const currentTier = LOYALTY_TIERS[loyaltyData.tier];
  const nextTier = getNextTier(loyaltyData.tier);
  const pointsToNext = getPointsToNextTier(loyaltyData.totalPoints, loyaltyData.tier);
  const progressPercentage = nextTier 
    ? ((loyaltyData.totalPoints - currentTier.minPoints) / 
       (LOYALTY_TIERS[nextTier].minPoints - currentTier.minPoints)) * 100
    : 100;

  const activeRewards = redeemedRewards.filter(
    (reward) => !reward.used && new Date(reward.expiresAt) > new Date()
  );

  const TierIcon = ({ tier, size = 20 }) => {
    const icons = {
      BRONZE: <Award className="text-amber-600" size={size} />,
      SILVER: <Star className="text-gray-500" size={size} />,
      GOLD: <Crown className="text-yellow-500" size={size} />,
      PLATINUM: <Trophy className="text-purple-600" size={size} />,
    };
    return icons[tier] || icons.BRONZE;
  };

  if (variant === "minimal") {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <TierIcon tier={loyaltyData.tier} size={16} />
        <span className="text-sm font-medium">{loyaltyData.availablePoints} pts</span>
        {showAnimation && (
          <div className="flex items-center text-green-600 animate-pulse">
            <TrendingUp size={14} />
            <span className="text-xs ml-1">+{pointsHistory[0]?.points}</span>
          </div>
        )}
      </div>
    );
  }

  if (variant === "compact") {
    return (
      <Card className={`p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div
              className="w-10 h-10 rounded-full flex items-center justify-center"
              style={{ backgroundColor: currentTier.bgColor }}
            >
              <TierIcon tier={loyaltyData.tier} />
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <span className="font-semibold">{loyaltyData.availablePoints}</span>
                <span className="text-sm text-gray-500">points</span>
                {showAnimation && (
                  <div className="flex items-center text-green-600 animate-bounce">
                    <Sparkles size={14} />
                    <span className="text-xs ml-1">+{pointsHistory[0]?.points}</span>
                  </div>
                )}
              </div>
              <Badge
                variant="outline"
                size="small"
                style={{ color: currentTier.color, borderColor: currentTier.color }}
              >
                {currentTier.name}
              </Badge>
            </div>
          </div>
          {showActions && (
            <Button
              variant="outline"
              size="small"
              icon={<ChevronRight size={16} />}
              onClick={onViewDetails}
            >
              View
            </Button>
          )}
        </div>
      </Card>
    );
  }

  // Full variant
  return (
    <Card className={className}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div
              className="w-12 h-12 rounded-full flex items-center justify-center"
              style={{ backgroundColor: currentTier.bgColor }}
            >
              <TierIcon tier={loyaltyData.tier} size={24} />
            </div>
            <div>
              <h3 className="font-semibold text-lg">Loyalty Program</h3>
              <Badge
                variant="primary"
                style={{ backgroundColor: currentTier.color, color: "white" }}
              >
                {currentTier.name} Member
              </Badge>
            </div>
          </div>
          {showAnimation && (
            <div className="flex items-center text-green-600 animate-pulse">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-2">
                <TrendingUp size={16} />
              </div>
              <div>
                <p className="text-sm font-medium">Points Earned!</p>
                <p className="text-lg font-bold">+{pointsHistory[0]?.points}</p>
              </div>
            </div>
          )}
        </div>

        {/* Points Display */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center">
            <p className="text-2xl font-bold text-orange-600">
              {loyaltyData.availablePoints}
            </p>
            <p className="text-sm text-gray-500">Available Points</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-gray-900">
              {loyaltyData.totalPoints}
            </p>
            <p className="text-sm text-gray-500">Total Earned</p>
          </div>
        </div>

        {/* Progress to Next Tier */}
        {nextTier && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Progress to {LOYALTY_TIERS[nextTier].name}</span>
              <span className="text-sm text-gray-500">{pointsToNext} points to go</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-orange-400 to-orange-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${Math.min(progressPercentage, 100)}%` }}
              />
            </div>
          </div>
        )}

        {/* Active Rewards */}
        {activeRewards.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium flex items-center">
                <Gift size={16} className="text-green-500 mr-2" />
                Active Rewards
              </h4>
              <Badge variant="success" size="small">
                {activeRewards.length}
              </Badge>
            </div>
            <div className="space-y-2">
              {activeRewards.slice(0, 2).map((reward) => (
                <div key={reward.id} className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                  <span className="text-sm font-medium">{reward.name}</span>
                  <span className="text-xs text-green-600">
                    Expires: {new Date(reward.expiresAt).toLocaleDateString()}
                  </span>
                </div>
              ))}
              {activeRewards.length > 2 && (
                <p className="text-xs text-gray-500 text-center">
                  +{activeRewards.length - 2} more rewards
                </p>
              )}
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-4 mb-6 text-center">
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-lg font-semibold">{loyaltyData.totalOrders}</p>
            <p className="text-xs text-gray-500">Total Orders</p>
          </div>
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-lg font-semibold">${loyaltyData.totalSpent.toFixed(2)}</p>
            <p className="text-xs text-gray-500">Total Spent</p>
          </div>
        </div>

        {/* Current Tier Benefits Preview */}
        <div className="mb-6">
          <h4 className="font-medium mb-2 flex items-center">
            <TierIcon tier={loyaltyData.tier} size={16} />
            <span className="ml-2">{currentTier.name} Benefits</span>
          </h4>
          <div className="space-y-1">
            {currentTier.benefits.slice(0, 2).map((benefit, index) => (
              <div key={index} className="flex items-center text-sm text-gray-600">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2" />
                <span>{benefit}</span>
              </div>
            ))}
            {currentTier.benefits.length > 2 && (
              <p className="text-xs text-gray-500 ml-3">
                +{currentTier.benefits.length - 2} more benefits
              </p>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        {showActions && (
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              size="small"
              fullWidth
              icon={<Star size={16} />}
              onClick={onViewDetails}
            >
              View Details
            </Button>
            <Button
              variant="primary"
              size="small"
              fullWidth
              icon={<Gift size={16} />}
              onClick={onViewRewards}
            >
              Redeem Rewards
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
};

export default LoyaltyWidget;
