<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} - Afghan Sufra</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .form-container {
            padding: 40px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3498db;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .required {
            color: #e74c3c;
        }

        .submit-btn {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
        }

        .info-box {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }
            
            .container {
                margin: 10px;
            }
            
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚚 {{ title }}</h1>
            <p>Join our delivery team and start earning today!</p>
        </div>

        <div class="form-container">
            <div class="info-box">
                <strong>📋 Registration Process:</strong>
                <ol style="margin-left: 20px; margin-top: 10px;">
                    <li>Fill out this registration form</li>
                    <li>Wait for admin review (2-3 business days)</li>
                    <li>Receive SMS with login credentials if approved</li>
                    <li>Start accepting delivery orders!</li>
                </ol>
            </div>

            <div class="success-message" id="successMessage"></div>
            <div class="error-message" id="errorMessage"></div>

            <form id="registrationForm">
                <!-- Personal Information -->
                <div class="form-section">
                    <h3 class="section-title">👤 Personal Information</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="full_name">نوم (Full Name) <span class="required">*</span></label>
                            <input type="text" id="full_name" name="full_name" required>
                        </div>
                        <div class="form-group">
                            <label for="father_name">د پلار نوم (Father's Name) <span class="required">*</span></label>
                            <input type="text" id="father_name" name="father_name" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="national_id">د تذکرې شمیره (National ID) <span class="required">*</span></label>
                            <input type="text" id="national_id" name="national_id" pattern="[0-9]{10,15}" required>
                        </div>
                        <div class="form-group">
                            <label for="date_of_birth">د زیږیدو نیټه (Date of Birth)</label>
                            <input type="date" id="date_of_birth" name="date_of_birth">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="gender">جنس (Gender)</label>
                            <select id="gender" name="gender">
                                <option value="male">نارینه (Male)</option>
                                <option value="female">ښځینه (Female)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="marital_status">د واده حالت (Marital Status)</label>
                            <select id="marital_status" name="marital_status">
                                <option value="single">مجرد (Single)</option>
                                <option value="married">واده شوی (Married)</option>
                                <option value="divorced">طلاق شوی (Divorced)</option>
                                <option value="widowed">کونډه (Widowed)</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="form-section">
                    <h3 class="section-title">📞 Contact Information</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone_primary">اصلي تلیفون (Primary Phone) <span class="required">*</span></label>
                            <input type="tel" id="phone_primary" name="phone_primary" pattern="\+93[0-9]{9}" placeholder="+93701234567" required>
                        </div>
                        <div class="form-group">
                            <label for="secondary_phone">دویمه تلیفون (Secondary Phone)</label>
                            <input type="tel" id="secondary_phone" name="secondary_phone" pattern="\+93[0-9]{9}" placeholder="+93789876543">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="email">بریښنالیک (Email)</label>
                        <input type="email" id="email" name="email" placeholder="<EMAIL>">
                    </div>
                </div>

                <!-- Address Information -->
                <div class="form-section">
                    <h3 class="section-title">🏠 Address Information</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="province">ولایت (Province) <span class="required">*</span></label>
                            <select id="province" name="province" required>
                                <option value="">Select Province</option>
                                {% for value, label in provinces %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="district">ولسوالۍ (District) <span class="required">*</span></label>
                            <input type="text" id="district" name="district" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="area">سیمه (Area) <span class="required">*</span></label>
                            <input type="text" id="area" name="area" required>
                        </div>
                        <div class="form-group">
                            <label for="nearby_landmark">نږدې ځای (Nearby Landmark)</label>
                            <input type="text" id="nearby_landmark" name="nearby_landmark">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="street_address">د کوڅې پته (Street Address) <span class="required">*</span></label>
                        <textarea id="street_address" name="street_address" rows="3" required></textarea>
                    </div>
                </div>

                <!-- Vehicle Information -->
                <div class="form-section">
                    <h3 class="section-title">🏍️ Vehicle Information</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="vehicle_type">د وسایطو ډول (Vehicle Type) <span class="required">*</span></label>
                            <select id="vehicle_type" name="vehicle_type" required>
                                <option value="">Select Vehicle Type</option>
                                {% for value, label in vehicle_types %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="vehicle_model">د وسایطو ماډل (Vehicle Model)</label>
                            <input type="text" id="vehicle_model" name="vehicle_model">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="license_plate">د پلیټ شمیره (License Plate)</label>
                            <input type="text" id="license_plate" name="license_plate">
                        </div>
                        <div class="form-group">
                            <label for="vehicle_color">د وسایطو رنګ (Vehicle Color)</label>
                            <input type="text" id="vehicle_color" name="vehicle_color">
                        </div>
                    </div>
                </div>

                <button type="submit" class="submit-btn">
                    📝 ثبت نوم (Submit Registration)
                </button>
            </form>
        </div>
    </div>

    <script>
        document.getElementById('registrationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            
            // Show loading state
            const submitBtn = document.querySelector('.submit-btn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Submitting...';
            submitBtn.disabled = true;
            
            try {
                const response = await fetch('{{ form_action }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    document.getElementById('successMessage').style.display = 'block';
                    document.getElementById('successMessage').innerHTML = `
                        <strong>✅ Registration Successful!</strong><br>
                        Agent ID: <strong>${result.agent_id}</strong><br>
                        Application Number: <strong>${result.application_number}</strong><br>
                        Status: <strong>Pending Admin Approval</strong><br><br>
                        📱 You will receive SMS updates on your application status.
                    `;
                    document.getElementById('errorMessage').style.display = 'none';
                    this.reset();
                } else {
                    document.getElementById('errorMessage').style.display = 'block';
                    document.getElementById('errorMessage').innerHTML = `
                        <strong>❌ Registration Failed:</strong><br>
                        ${result.message || 'Please check your information and try again.'}
                    `;
                    document.getElementById('successMessage').style.display = 'none';
                }
            } catch (error) {
                document.getElementById('errorMessage').style.display = 'block';
                document.getElementById('errorMessage').innerHTML = `
                    <strong>❌ Error:</strong><br>
                    ${error.message}
                `;
                document.getElementById('successMessage').style.display = 'none';
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
    </script>
</body>
</html>
