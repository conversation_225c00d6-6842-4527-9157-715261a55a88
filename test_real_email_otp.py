#!/usr/bin/env python3
"""
Test real email OTP functionality
"""

import requests
import json
import time

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_real_email_otp():
    """Test OTP with real email address"""
    print("📧 Testing Real Email OTP Functionality...")
    print("=" * 50)
    
    # Get email from user
    email = input("📧 Enter your real email address to receive OTP: ").strip()
    
    if not email or "@" not in email:
        print("❌ Invalid email address")
        return
    
    # Test user data with real email
    test_user = {
        "name": "Real Email Test User",
        "user_name": f"realtest_{int(time.time())}",
        "email": email,
        "phone": f"+111222{int(time.time()) % 10000}",
        "password": "TestPassword123",
        "confirm_password": "TestPassword123",
        "role": "customer"
    }
    
    print(f"📝 Registering user with email: {email}")
    
    try:
        # Register user
        response = requests.post(
            f"{API_BASE_URL}/auth/register/",
            headers=HEADERS,
            data=json.dumps(test_user)
        )
        
        print(f"📡 Response Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                print("✅ Registration successful!")
                print(f"📧 OTP should be sent to: {email}")
                print("📬 Please check your email inbox (and spam folder)")
                
                # Wait for user to get OTP
                print("\n" + "=" * 50)
                otp = input("🔑 Enter the OTP you received in your email: ").strip()
                
                if len(otp) == 6 and otp.isdigit():
                    # Test verification
                    verification_data = {
                        "email": email,
                        "otp": otp
                    }
                    
                    verify_response = requests.post(
                        f"{API_BASE_URL}/auth/verify-email/",
                        headers=HEADERS,
                        data=json.dumps(verification_data)
                    )
                    
                    print(f"\n📡 Verification Status: {verify_response.status_code}")
                    print(f"📄 Verification Response: {verify_response.text}")
                    
                    if verify_response.status_code == 200:
                        verify_result = verify_response.json()
                        if verify_result.get('success'):
                            print("🎉 Email verification successful!")
                            print("✅ Real email OTP system is working!")
                        else:
                            print("❌ Verification failed:", verify_result.get('message'))
                    else:
                        print("❌ Verification request failed")
                else:
                    print("❌ Invalid OTP format. Should be 6 digits.")
            else:
                print("❌ Registration failed:", result.get('message'))
        else:
            print("❌ Registration request failed")
            try:
                error_data = response.json()
                print(f"Error details: {error_data}")
            except:
                print(f"Raw response: {response.text}")
                
    except Exception as e:
        print(f"❌ Exception occurred: {e}")

def test_email_connection():
    """Test if email settings are working"""
    print("🔧 Testing Email Connection...")
    
    try:
        from django.core.mail import send_mail
        from django.conf import settings
        
        # This would test the email connection
        print("📧 Email settings:")
        print(f"   Backend: {settings.EMAIL_BACKEND}")
        print(f"   Host: {settings.EMAIL_HOST}")
        print(f"   Port: {settings.EMAIL_PORT}")
        print(f"   Use TLS: {settings.EMAIL_USE_TLS}")
        print(f"   From Email: {settings.DEFAULT_FROM_EMAIL}")
        
    except Exception as e:
        print(f"❌ Error checking email settings: {e}")

if __name__ == "__main__":
    print("🚀 Real Email OTP Test")
    print("=" * 50)
    
    # Test email connection first
    test_email_connection()
    
    print("\n" + "=" * 50)
    
    # Test real email OTP
    test_real_email_otp()
