#!/usr/bin/env python3
"""
Test script to verify that delivery agents can see orders marked as 'ready' by restaurants
and can accept them properly.
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# Add the Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from orders.models import Order
from restaurant.models import Restaurant, Address
from deliveryAgent.models import DeliveryAgentProfile

User = get_user_model()

def test_delivery_agent_orders_workflow():
    """Test the complete workflow from restaurant marking order as ready to delivery agent accepting it"""
    
    print("🧪 Testing Delivery Agent Orders Fix")
    print("=" * 50)
    
    try:
        # 1. Create test data if not exists
        print("1. Setting up test data...")
        
        # Create a restaurant user and restaurant
        try:
            restaurant_user = User.objects.get(email='<EMAIL>')
        except User.DoesNotExist:
            import random
            phone_suffix = random.randint(1000, 9999)
            restaurant_user = User.objects.create(
                email='<EMAIL>',
                name='Test Restaurant Owner',
                user_name=f'test_restaurant_{phone_suffix}',
                role='restaurant',
                is_verified=True,
                phone=f'+93 70 123 {phone_suffix}'
            )
            restaurant_user.set_password('testpass123')
            restaurant_user.save()
        
        # Create restaurant address first
        restaurant_address, created = Address.objects.get_or_create(
            user=restaurant_user,
            defaults={
                'street': 'Test Restaurant Street',
                'city': 'Kabul',
                'state': 'Kabul',
                'postal_code': '1001',
                'country': 'Afghanistan',
                'latitude': 34.5553,
                'longitude': 69.2075
            }
        )

        restaurant, created = Restaurant.objects.get_or_create(
            owner=restaurant_user,
            defaults={
                'name': 'Test Restaurant',
                'description': 'Test restaurant for delivery agent testing',
                'address': restaurant_address,
                'contact_number': '+93 70 123 4567',
                'delivery_fee': 25.00,
                'opening_time': '08:00',
                'closing_time': '22:00',
                'is_active': True,
                'is_verified': True
            }
        )
        
        # Create a customer user
        try:
            customer_user = User.objects.get(email='<EMAIL>')
        except User.DoesNotExist:
            # Generate unique phone number
            import random
            phone_suffix = random.randint(1000, 9999)
            customer_user = User.objects.create(
                email='<EMAIL>',
                name='Test Customer',
                user_name=f'test_customer_{phone_suffix}',
                role='customer',
                is_verified=True,
                phone=f'+93 78 987 {phone_suffix}'
            )
            customer_user.set_password('testpass123')
            customer_user.save()

        # Create delivery address
        delivery_address, created = Address.objects.get_or_create(
            user=customer_user,
            defaults={
                'street': 'Test Delivery Street',
                'city': 'Kabul',
                'state': 'Kabul',
                'postal_code': '1002',
                'country': 'Afghanistan',
                'latitude': 34.5600,
                'longitude': 69.2100
            }
        )
        
        # Create a delivery agent user and profile
        try:
            agent_user = User.objects.get(email='<EMAIL>')
        except User.DoesNotExist:
            import random
            phone_suffix = random.randint(1000, 9999)
            agent_user = User.objects.create(
                email='<EMAIL>',
                name='Test Delivery Agent',
                user_name=f'test_agent_{phone_suffix}',
                role='delivery_agent',
                is_verified=True,
                phone=f'+93 79 456 {phone_suffix}'
            )
            agent_user.set_password('testpass123')
            agent_user.save()
        
        agent_profile, created = DeliveryAgentProfile.objects.get_or_create(
            user=agent_user,
            defaults={
                'agent_id': 'DA001',
                'status': 'approved',
                'availability': 'available',
                'employment_status': 'active',
                'is_clocked_in': True,
                'current_latitude': 34.5553,
                'current_longitude': 69.2075
            }
        )

        # Ensure agent is available (in case it was created before with different status)
        if not created:
            agent_profile.availability = 'available'
            agent_profile.status = 'approved'
            agent_profile.employment_status = 'active'
            agent_profile.is_clocked_in = True
            agent_profile.save()
        
        print("✅ Test data created successfully")
        
        # 2. Create an order and mark it as ready
        print("\n2. Creating order and marking as ready...")
        
        order = Order.objects.create(
            customer=customer_user,
            restaurant=restaurant,
            delivery_address=delivery_address,
            total_amount=500.00,
            delivery_fee=25.00,
            tax_amount=50.00,  # Add required tax_amount field
            payment_method='cash_on_delivery',
            status='ready',  # Restaurant has marked it as ready
            special_instructions='Test order for delivery agent fix'
        )
        
        print(f"✅ Order {order.id} created with status 'ready'")
        
        # 3. Test API endpoint to get available orders
        print("\n3. Testing available orders API...")
        
        # Simulate API call (we'll test the view directly)
        from deliveryAgent.views import AvailableOrdersView
        from django.test import RequestFactory
        from django.contrib.auth.models import AnonymousUser
        
        factory = RequestFactory()
        request = factory.get('/delivery-agent/available-orders/')
        request.user = agent_user
        
        view = AvailableOrdersView()
        response = view.get(request)
        
        if response.status_code == 200:
            data = response.data
            print(f"   API Response: {data}")
            if data['status'] == 'success' and data['data']['orders']:
                print(f"✅ Available orders API working: Found {len(data['data']['orders'])} orders")
                print(f"   Order ID: {data['data']['orders'][0]['id']}")
                print(f"   Restaurant: {data['data']['orders'][0]['restaurant']['name']}")
                print(f"   Customer: {data['data']['orders'][0]['customer']['name']}")
                print(f"   Total: ${data['data']['orders'][0]['order_details']['total_amount']}")
            else:
                print("❌ No orders found in available orders API")
                if data['status'] == 'error':
                    print(f"   Error: {data.get('message', 'Unknown error')}")
                    print(f"   Agent status: {data.get('agent_status', 'Unknown')}")
                    print(f"   Agent availability: {data.get('availability', 'Unknown')}")
                return False
        else:
            print(f"❌ Available orders API failed: {response.status_code}")
            print(f"   Response: {response.data}")
            return False
        
        # 4. Test order acceptance
        print("\n4. Testing order acceptance...")
        
        from deliveryAgent.views import AcceptOrderView
        
        request = factory.post('/delivery-agent/accept-order/', {
            'order_id': order.id
        }, content_type='application/json')
        request.user = agent_user
        request.data = {'order_id': order.id}
        
        view = AcceptOrderView()
        response = view.post(request)
        
        if response.status_code == 200:
            data = response.data
            if data['status'] == 'success':
                print("✅ Order acceptance working")
                print(f"   Order status: {data['new_status']}")
                
                # Verify order status in database
                order.refresh_from_db()
                if order.status == 'accepted' and order.delivery_agent == agent_user:
                    print("✅ Order correctly assigned and accepted in database")
                else:
                    print(f"❌ Order status mismatch: {order.status}, agent: {order.delivery_agent}")
                    return False
            else:
                print(f"❌ Order acceptance failed: {data['message']}")
                return False
        else:
            print(f"❌ Order acceptance API failed: {response.status_code}")
            print(f"   Response: {response.data}")
            return False
        
        # 5. Verify agent is now busy and cannot see available orders (correct behavior)
        print("\n5. Verifying agent is now busy after accepting order...")

        # Refresh agent profile from database
        agent_profile.refresh_from_db()
        if agent_profile.availability == 'busy':
            print("✅ Agent correctly marked as 'busy' after accepting order")
        else:
            print(f"❌ Agent availability should be 'busy' but is '{agent_profile.availability}'")
            return False

        # Verify that busy agents cannot see available orders (correct behavior)
        request = factory.get('/delivery-agent/available-orders/')
        request.user = agent_user

        view = AvailableOrdersView()
        response = view.get(request)

        if response.status_code == 200:
            data = response.data
            if data['status'] == 'error' and 'not available for orders' in data['message']:
                print("✅ Busy agent correctly cannot see available orders")
            else:
                print(f"❌ Expected busy agent to be blocked from seeing orders, but got: {data}")
                return False
        else:
            print(f"❌ Unexpected response code: {response.status_code}")
            return False
        
        print("\n🎉 All tests passed! The delivery agent orders fix is working correctly.")
        print("\nSummary:")
        print("- Restaurant can mark orders as 'ready'")
        print("- Delivery agents can see ready orders in their available orders list")
        print("- Delivery agents can accept ready orders")
        print("- Accepted orders are properly assigned to the agent")
        print("- Agents become 'busy' after accepting orders (preventing overload)")
        print("- Busy agents cannot see new available orders until they complete current delivery")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_delivery_agent_orders_workflow()
    sys.exit(0 if success else 1)
