import React, { createContext, useContext, useState, useEffect } from 'react';

const SchedulingContext = createContext();

export const useScheduling = () => {
  const context = useContext(SchedulingContext);
  if (!context) {
    throw new Error('useScheduling must be used within a SchedulingProvider');
  }
  return context;
};

export const SchedulingProvider = ({ children }) => {
  const [scheduledOrders, setScheduledOrders] = useState([]);
  const [selectedDateTime, setSelectedDateTime] = useState(null);
  const [isScheduled, setIsScheduled] = useState(false);
  const [recurringSettings, setRecurringSettings] = useState({
    enabled: false,
    frequency: 'weekly', // daily, weekly, monthly
    endDate: null,
    daysOfWeek: [] // for weekly recurring
  });

  // Load scheduled orders from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('scheduledOrders');
    if (saved) {
      setScheduledOrders(JSON.parse(saved));
    }
  }, []);

  // Save to localStorage when scheduled orders change
  useEffect(() => {
    localStorage.setItem('scheduledOrders', JSON.stringify(scheduledOrders));
  }, [scheduledOrders]);

  const getRestaurantAvailability = (restaurantId, date) => {
    // Mock restaurant availability - in real app, this would be an API call
    const dayOfWeek = date.getDay();
    const hour = date.getHours();
    
    // Most restaurants: 10 AM - 11 PM, closed on Mondays
    if (dayOfWeek === 1) return { available: false, reason: 'Restaurant closed on Mondays' };
    if (hour < 10 || hour > 23) return { available: false, reason: 'Restaurant closed at this time' };
    
    return { available: true };
  };

  const getAvailableTimeSlots = (restaurantId, date) => {
    const availability = getRestaurantAvailability(restaurantId, date);
    if (!availability.available) return [];

    const slots = [];
    const startHour = 10; // 10 AM
    const endHour = 23; // 11 PM
    
    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute of [0, 30]) {
        const slotTime = new Date(date);
        slotTime.setHours(hour, minute, 0, 0);
        
        // Don't show past times for today
        if (slotTime > new Date()) {
          slots.push({
            time: slotTime,
            label: slotTime.toLocaleTimeString('en-US', { 
              hour: 'numeric', 
              minute: '2-digit',
              hour12: true 
            }),
            available: true
          });
        }
      }
    }
    
    return slots;
  };

  const scheduleOrder = (orderData, dateTime, recurring = null) => {
    const scheduledOrder = {
      id: `scheduled-${Date.now()}`,
      ...orderData,
      scheduledFor: dateTime,
      recurring: recurring,
      status: 'scheduled',
      createdAt: new Date().toISOString()
    };

    setScheduledOrders(prev => [...prev, scheduledOrder]);
    
    // If recurring, create future orders
    if (recurring?.enabled) {
      const futureOrders = generateRecurringOrders(scheduledOrder, recurring);
      setScheduledOrders(prev => [...prev, ...futureOrders]);
    }

    return scheduledOrder;
  };

  const generateRecurringOrders = (baseOrder, recurring) => {
    const orders = [];
    const startDate = new Date(baseOrder.scheduledFor);
    const endDate = recurring.endDate ? new Date(recurring.endDate) : new Date(Date.now() + 90 * 24 * 60 * 60 * 1000); // 3 months default
    
    let currentDate = new Date(startDate);
    
    while (currentDate <= endDate && orders.length < 52) { // Max 52 recurring orders
      if (recurring.frequency === 'daily') {
        currentDate.setDate(currentDate.getDate() + 1);
      } else if (recurring.frequency === 'weekly') {
        currentDate.setDate(currentDate.getDate() + 7);
      } else if (recurring.frequency === 'monthly') {
        currentDate.setMonth(currentDate.getMonth() + 1);
      }

      if (currentDate <= endDate) {
        orders.push({
          ...baseOrder,
          id: `scheduled-${Date.now()}-${orders.length}`,
          scheduledFor: new Date(currentDate),
          parentOrderId: baseOrder.id
        });
      }
    }
    
    return orders;
  };

  const cancelScheduledOrder = (orderId) => {
    setScheduledOrders(prev => prev.filter(order => 
      order.id !== orderId && order.parentOrderId !== orderId
    ));
  };

  const updateScheduledOrder = (orderId, updates) => {
    setScheduledOrders(prev => prev.map(order => 
      order.id === orderId ? { ...order, ...updates } : order
    ));
  };

  const getUpcomingOrders = (limit = 10) => {
    const now = new Date();
    return scheduledOrders
      .filter(order => new Date(order.scheduledFor) > now && order.status === 'scheduled')
      .sort((a, b) => new Date(a.scheduledFor) - new Date(b.scheduledFor))
      .slice(0, limit);
  };

  const getTodaysScheduledOrders = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return scheduledOrders.filter(order => {
      const orderDate = new Date(order.scheduledFor);
      return orderDate >= today && orderDate < tomorrow && order.status === 'scheduled';
    });
  };

  const isValidScheduleTime = (restaurantId, dateTime) => {
    const now = new Date();
    const minScheduleTime = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
    
    if (dateTime < minScheduleTime) {
      return { valid: false, reason: 'Orders must be scheduled at least 1 hour in advance' };
    }

    const availability = getRestaurantAvailability(restaurantId, dateTime);
    if (!availability.available) {
      return { valid: false, reason: availability.reason };
    }

    return { valid: true };
  };

  const value = {
    scheduledOrders,
    selectedDateTime,
    setSelectedDateTime,
    isScheduled,
    setIsScheduled,
    recurringSettings,
    setRecurringSettings,
    getRestaurantAvailability,
    getAvailableTimeSlots,
    scheduleOrder,
    cancelScheduledOrder,
    updateScheduledOrder,
    getUpcomingOrders,
    getTodaysScheduledOrders,
    isValidScheduleTime
  };

  return (
    <SchedulingContext.Provider value={value}>
      {children}
    </SchedulingContext.Provider>
  );
};

export default SchedulingContext;
