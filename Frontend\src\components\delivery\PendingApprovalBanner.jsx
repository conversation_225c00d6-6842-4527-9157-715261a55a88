import React from "react";
import { AlertCircle, Clock, CheckCircle } from "lucide-react";
import Card from "../common/Card";

const PendingApprovalBanner = ({ status }) => {
  // Only show banners for suspended or blocked accounts
  // All agents are now automatically approved, so no pending approval banner needed

  if (status === "suspended" || status === "blocked") {
    return (
      <Card className='mb-6 p-4 bg-red-50 border-red-200'>
        <div className='flex items-center'>
          <AlertCircle className='h-6 w-6 text-red-500 mr-3' />
          <div>
            <h3 className='font-medium text-red-800'>
              Account {status === "suspended" ? "Suspended" : "Blocked"}
            </h3>
            <p className='text-sm text-red-700'>
              Your delivery agent account has been {status.toLowerCase()}.
              Please contact support for assistance.
            </p>
          </div>
        </div>
      </Card>
    );
  }

  // For 'active', 'approved', or any other status, don't show any banner
  // Agents can start working immediately without approval messages
  return null;
};

export default PendingApprovalBanner;
