# system_config/views.py
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django.shortcuts import get_object_or_404
from .models import SystemSetting, ChoiceOption, FilterConfiguration
from .serializers import (
    SystemSettingSerializer, SystemSettingUpdateSerializer,
    ChoiceOptionSerializer, FilterConfigurationSerializer,
    PublicSystemConfigSerializer
)

class SystemSettingViewSet(viewsets.ModelViewSet):
    queryset = SystemSetting.objects.all()
    serializer_class = SystemSettingSerializer
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        if self.action == 'public_config':
            return []  # Public access
        elif self.action in ['list', 'retrieve']:
            return [IsAuthenticated()]
        return [IsAdminUser()]

    def get_serializer_class(self):
        if self.action in ['update', 'partial_update']:
            return SystemSettingUpdateSerializer
        return SystemSettingSerializer

    def get_queryset(self):
        if self.request.user.is_staff:
            return self.queryset
        return self.queryset.filter(is_public=True)

    @action(detail=False, methods=['get'], permission_classes=[])
    def public_config(self, request):
        """Get public system configuration for frontend"""
        settings = SystemSetting.objects.filter(is_public=True)
        choice_options = ChoiceOption.objects.filter(is_active=True)
        filter_configs = FilterConfiguration.objects.filter(is_active=True)

        # Group choice options by type
        grouped_options = {}
        for option in choice_options:
            option_type = option.option_type
            if option_type not in grouped_options:
                grouped_options[option_type] = []
            grouped_options[option_type].append(ChoiceOptionSerializer(option).data)

        data = {
            'settings': SystemSettingSerializer(settings, many=True).data,
            'choice_options': grouped_options,
            'filter_configurations': FilterConfigurationSerializer(filter_configs, many=True).data
        }

        return Response(data)

    @action(detail=True, methods=['post'])
    def reset_to_default(self, request, pk=None):
        """Reset setting to default value"""
        setting = self.get_object()
        setting.value = setting.default_value
        setting.save()

        serializer = self.get_serializer(setting)
        return Response(serializer.data)

class ChoiceOptionViewSet(viewsets.ModelViewSet):
    queryset = ChoiceOption.objects.all()
    serializer_class = ChoiceOptionSerializer
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        if self.action in ['list', 'retrieve', 'by_type']:
            return []  # Public access
        return [IsAdminUser()]

    def get_queryset(self):
        queryset = self.queryset.filter(is_active=True)
        option_type = self.request.query_params.get('type')
        if option_type:
            queryset = queryset.filter(option_type=option_type)
        return queryset.order_by('display_order', 'label')

    @action(detail=False, methods=['get'], permission_classes=[])
    def by_type(self, request):
        """Get choice options grouped by type"""
        options = self.get_queryset()
        grouped = {}

        for option in options:
            option_type = option.option_type
            if option_type not in grouped:
                grouped[option_type] = []
            grouped[option_type].append(ChoiceOptionSerializer(option).data)

        return Response(grouped)

class FilterConfigurationViewSet(viewsets.ModelViewSet):
    queryset = FilterConfiguration.objects.filter(is_active=True)
    serializer_class = FilterConfigurationSerializer
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        if self.action in ['list', 'retrieve']:
            return [IsAuthenticated()]
        return [IsAdminUser()]

    def get_queryset(self):
        queryset = self.queryset.order_by('display_order', 'name')
        featured_only = self.request.query_params.get('featured')
        if featured_only and featured_only.lower() == 'true':
            queryset = queryset.filter(is_featured=True)
        return queryset
