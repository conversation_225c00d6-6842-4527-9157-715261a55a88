import React, { useState } from 'react';
import {
  MapPin,
  Clock,
  Phone,
  Navigation,
  CheckCircle,
  Package,
  User,
  Store,
  Route,
  Timer,
  DollarSign,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from 'lucide-react';
import Button from '../common/Button';
import Badge from '../common/Badge';

const MobileOrderCard = ({ 
  order, 
  type = 'available', // 'available' or 'active'
  onAccept,
  onUpdateStatus,
  onViewDetails 
}) => {
  const [expanded, setExpanded] = useState(false);

  const getStatusColor = (status) => {
    switch (status) {
      case 'assigned':
        return 'warning';
      case 'picked_up':
        return 'info';
      case 'on_the_way':
        return 'success';
      case 'delivered':
        return 'success';
      default:
        return 'secondary';
    }
  };

  const getNextAction = (status) => {
    switch (status) {
      case 'assigned':
        return { text: 'Mark Picked Up', action: 'picked_up', icon: Package };
      case 'picked_up':
        return { text: 'On The Way', action: 'on_the_way', icon: Navigation };
      case 'on_the_way':
        return { text: 'Mark Delivered', action: 'delivered', icon: CheckCircle };
      default:
        return null;
    }
  };

  const nextAction = type === 'active' ? getNextAction(order.status) : null;

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 mb-4 overflow-hidden">
      {/* Header - Always Visible */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-3 flex-1">
            <div className="p-2 bg-blue-100 rounded-full">
              <Store className="h-5 w-5 text-blue-600" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate">
                {order.restaurant_name || order.restaurant?.name}
              </h3>
              {type === 'active' && (
                <Badge variant={getStatusColor(order.status)} className="mt-1">
                  {order.status?.replace('_', ' ')}
                </Badge>
              )}
            </div>
          </div>
          
          <div className="text-right ml-3">
            <p className="text-xl font-bold text-green-600">
              ${order.total_amount?.toFixed(2)}
            </p>
            {type === 'available' && order.estimated_delivery_fee && (
              <p className="text-xs text-gray-500">
                +${order.estimated_delivery_fee?.toFixed(2)} fee
              </p>
            )}
          </div>
        </div>

        {/* Quick Info */}
        <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
          <div className="flex items-center space-x-1">
            <User className="h-4 w-4" />
            <span className="truncate">
              {order.customer_name || order.customer?.name}
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="h-4 w-4" />
            <span>{new Date(order.created_at).toLocaleTimeString()}</span>
          </div>
        </div>

        {/* Primary Action Buttons */}
        <div className="flex space-x-2">
          {type === 'available' ? (
            <>
              <Button
                onClick={() => onAccept(order.id)}
                className="flex-1 flex items-center justify-center space-x-2 py-3"
              >
                <CheckCircle className="h-5 w-5" />
                <span>Accept Order</span>
              </Button>
              <Button
                variant="outline"
                onClick={() => setExpanded(!expanded)}
                className="px-3 py-3"
              >
                {expanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
              </Button>
            </>
          ) : (
            <>
              {nextAction && (
                <Button
                  onClick={() => onUpdateStatus(order.id, nextAction.action)}
                  className="flex-1 flex items-center justify-center space-x-2 py-3"
                >
                  <nextAction.icon className="h-5 w-5" />
                  <span>{nextAction.text}</span>
                </Button>
              )}
              <Button
                variant="outline"
                onClick={() => setExpanded(!expanded)}
                className="px-3 py-3"
              >
                {expanded ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Expanded Details */}
      {expanded && (
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          {/* Delivery Address */}
          <div className="mb-4">
            <div className="flex items-start space-x-2">
              <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-700">Delivery Address</p>
                <p className="text-sm text-gray-600">{order.delivery_address}</p>
              </div>
            </div>
          </div>

          {/* Restaurant Info */}
          {type === 'available' && order.restaurant && (
            <div className="mb-4">
              <div className="flex items-start space-x-2">
                <Store className="h-4 w-4 text-gray-400 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-700">Restaurant</p>
                  <p className="text-sm text-gray-600">{order.restaurant.address}</p>
                  {order.restaurant.phone && (
                    <p className="text-sm text-blue-600">{order.restaurant.phone}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Customer Contact */}
          {order.customer?.phone && (
            <div className="mb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium text-gray-700">Customer</span>
                </div>
                <a
                  href={`tel:${order.customer.phone}`}
                  className="text-sm text-blue-600 font-medium"
                >
                  {order.customer.phone}
                </a>
              </div>
            </div>
          )}

          {/* Distance & Time (for available orders) */}
          {type === 'available' && (
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="flex items-center space-x-2">
                <Route className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-xs text-gray-500">Distance</p>
                  <p className="text-sm font-medium">{order.estimated_distance || 'N/A'}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Timer className="h-4 w-4 text-gray-400" />
                <div>
                  <p className="text-xs text-gray-500">Est. Time</p>
                  <p className="text-sm font-medium">{order.estimated_time || 'N/A'}</p>
                </div>
              </div>
            </div>
          )}

          {/* Additional Actions */}
          <div className="flex space-x-2">
            {onViewDetails && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails(order)}
                className="flex-1 flex items-center justify-center space-x-2"
              >
                <ExternalLink className="h-4 w-4" />
                <span>View Details</span>
              </Button>
            )}
            
            {/* Navigation Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const address = encodeURIComponent(order.delivery_address);
                window.open(`https://maps.google.com/maps?q=${address}`, '_blank');
              }}
              className="flex-1 flex items-center justify-center space-x-2"
            >
              <Navigation className="h-4 w-4" />
              <span>Navigate</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileOrderCard;
