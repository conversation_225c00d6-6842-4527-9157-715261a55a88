import React from 'react';
import { useAuth } from '../../context/AuthContext';

const TestRoute = () => {
  const { user } = useAuth();

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">🚚 Delivery Route Test</h1>
      
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
        <h2 className="text-lg font-semibold text-green-800 mb-2">✅ Route Working!</h2>
        <p className="text-green-700">
          This page is loading successfully, which means the delivery routes are working.
        </p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <h3 className="text-lg font-semibold text-blue-800 mb-2">User Information</h3>
        <div className="text-blue-700">
          <p><strong>Name:</strong> {user?.name || 'Not available'}</p>
          <p><strong>Email:</strong> {user?.email || 'Not available'}</p>
          <p><strong>Role:</strong> {user?.role || 'Not available'}</p>
          <p><strong>Username:</strong> {user?.username || 'Not available'}</p>
        </div>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">🔧 Next Steps</h3>
        <ul className="text-yellow-700 list-disc list-inside space-y-1">
          <li>Test the dashboard route: <code>/delivery</code></li>
          <li>Test the orders route: <code>/delivery/orders</code></li>
          <li>Test the profile route: <code>/delivery/profile</code></li>
          <li>Test the earnings route: <code>/delivery/earnings</code></li>
        </ul>
      </div>
    </div>
  );
};

export default TestRoute;
