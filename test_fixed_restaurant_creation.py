#!/usr/bin/env python3
"""
Test the fixed restaurant creation
"""

import requests
import json

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"

def test_fixed_restaurant_creation():
    """Test restaurant creation with the fixes applied"""
    
    # Login with our test user
    login_data = {
        "user_name": "testrestaurant",
        "password": "password123"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print("🔐 Logging in...")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=headers,
            data=json.dumps(login_data)
        )
        
        if response.status_code != 200:
            print("❌ Login failed")
            return False
        
        result = response.json()
        token = result['data']['access_token']
        print("✅ Login successful!")
        
        # Test with fixed data structure
        auth_headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        # Test restaurant data without many-to-many issues
        restaurant_data = {
            "name": "Fixed Test Restaurant",
            "description": "Testing the fixed restaurant creation",
            "address": {
                "street": "Fixed Street 789",
                "city": "Kabul",
                "state": "Kabul",
                "postal_code": "1004",
                "country": "Afghanistan",
                "latitude": 34.5553,
                "longitude": 69.2075
            },
            "contact_number": "+93701234570",
            "opening_time": "08:00:00",
            "closing_time": "23:00:00",
            "delivery_fee": "80.00",
            "min_order_amount": "300.00",
            "average_preparation_time": 40,
            "accepts_cash": True,
            "accepts_card": True,
            "accepts_online_payment": True,
            "website": "https://fixedrestaurant.com",
            "facebook_url": "https://facebook.com/fixedrestaurant",
            "instagram_url": "https://instagram.com/fixedrestaurant",
            "twitter_url": "https://twitter.com/fixedrestaurant"
        }
        
        print(f"\n🏗️ Creating fixed restaurant...")
        
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            data=json.dumps(restaurant_data)
        )
        
        print(f"📡 Status: {response.status_code}")
        print(f"📄 Response: {response.text}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ Fixed restaurant created successfully!")
            print(f"   Restaurant ID: {result.get('id')}")
            print(f"   Name: {result.get('name')}")
            print(f"   Website: {result.get('website')}")
            print(f"   Address: {result.get('address', {}).get('street', 'N/A')}")
            return True
        else:
            print("❌ Fixed restaurant creation failed")
            try:
                error_data = response.json()
                print(f"🔍 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"🔍 Raw Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Fixed Restaurant Creation")
    print("=" * 60)
    
    success = test_fixed_restaurant_creation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: Fixed restaurant creation works!")
        print("✅ Many-to-many field issue resolved")
        print("✅ Address uniqueness issue resolved")
        print("✅ Frontend form should now work properly")
    else:
        print("❌ FAILED: Still having issues")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
