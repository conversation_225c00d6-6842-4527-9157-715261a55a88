import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Gift, Users, DollarSign, Share2, Copy, Check, Mail, MessageCircle } from 'lucide-react';
import { useSocial } from '../../context/SocialContext';
import Card from '../common/Card';
import Button from '../common/Button';
import Input from '../common/Input';
import FormControl from '../common/FormControl';
import Badge from '../common/Badge';

const ReferralProgram = () => {
  const { 
    generateReferralCode, 
    sendReferral, 
    referrals, 
    getSocialStats 
  } = useSocial();
  
  const [copied, setCopied] = useState(false);
  const [showInviteForm, setShowInviteForm] = useState(false);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm();

  const referralCode = generateReferralCode();
  const referralLink = `${window.location.origin}/register?ref=${referralCode}`;
  const socialStats = getSocialStats();

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(referralLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = referralLink;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleSendInvite = (data) => {
    sendReferral(data.email, data.method || 'email');
    reset();
    setShowInviteForm(false);
  };

  const shareViaWhatsApp = () => {
    const message = `Hey! I'm loving Afghan Sofra for food delivery. Join using my referral code ${referralCode} and we both get $10 off! ${referralLink}`;
    window.open(`https://wa.me/?text=${encodeURIComponent(message)}`, '_blank');
  };

  const shareViaEmail = () => {
    const subject = 'Join me on Afghan Sofra and get $10 off!';
    const body = `Hi there!\n\nI've been using Afghan Sofra for food delivery and it's amazing! The food is delicious and delivery is super fast.\n\nJoin using my referral code: ${referralCode}\nOr click this link: ${referralLink}\n\nWe'll both get $10 off our next orders!\n\nEnjoy!`;
    window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
          <Gift size={32} className="text-white" />
        </div>
        <h1 className="text-2xl font-bold mb-2">Refer Friends & Earn</h1>
        <p className="text-gray-600">
          Share Afghan Sofra with friends and you both get $10 off your next order!
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="text-center p-6">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <Users size={24} className="text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-blue-600">{socialStats.referrals.completed}</div>
          <div className="text-sm text-gray-600">Friends Joined</div>
        </Card>

        <Card className="text-center p-6">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <DollarSign size={24} className="text-green-600" />
          </div>
          <div className="text-2xl font-bold text-green-600">${socialStats.referrals.totalRewards}</div>
          <div className="text-sm text-gray-600">Total Earned</div>
        </Card>

        <Card className="text-center p-6">
          <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
            <Share2 size={24} className="text-orange-600" />
          </div>
          <div className="text-2xl font-bold text-orange-600">{socialStats.referrals.pending}</div>
          <div className="text-sm text-gray-600">Pending Invites</div>
        </Card>
      </div>

      {/* Referral Code */}
      <Card className="p-6">
        <h3 className="font-semibold mb-4">Your Referral Code</h3>
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-primary-600">{referralCode}</div>
              <div className="text-sm text-gray-600">Share this code with friends</div>
            </div>
            <Button
              variant="outline"
              size="small"
              icon={copied ? <Check size={16} /> : <Copy size={16} />}
              onClick={handleCopyLink}
            >
              {copied ? 'Copied!' : 'Copy Link'}
            </Button>
          </div>
        </div>

        {/* Quick Share Buttons */}
        <div className="flex flex-wrap gap-3">
          <Button
            variant="outline"
            icon={<MessageCircle size={16} />}
            onClick={shareViaWhatsApp}
            className="flex-1 sm:flex-none"
          >
            WhatsApp
          </Button>
          <Button
            variant="outline"
            icon={<Mail size={16} />}
            onClick={shareViaEmail}
            className="flex-1 sm:flex-none"
          >
            Email
          </Button>
          <Button
            variant="primary"
            onClick={() => setShowInviteForm(!showInviteForm)}
            className="flex-1 sm:flex-none"
          >
            Send Invite
          </Button>
        </div>

        {/* Invite Form */}
        {showInviteForm && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-3">Send Personal Invite</h4>
            <form onSubmit={handleSubmit(handleSendInvite)} className="space-y-4">
              <FormControl
                label="Friend's Email"
                error={errors.email?.message}
                required
              >
                <Input
                  type="email"
                  placeholder="Enter your friend's email"
                  error={errors.email?.message}
                  {...register('email', {
                    required: 'Email is required',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: 'Invalid email address'
                    }
                  })}
                />
              </FormControl>

              <div className="flex space-x-3">
                <Button type="submit" variant="primary" size="small">
                  Send Invite
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  size="small"
                  onClick={() => setShowInviteForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        )}
      </Card>

      {/* How it Works */}
      <Card className="p-6">
        <h3 className="font-semibold mb-4">How it Works</h3>
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <span className="text-primary-600 font-bold text-sm">1</span>
            </div>
            <div>
              <h4 className="font-medium">Share your code</h4>
              <p className="text-gray-600 text-sm">Send your referral code to friends via WhatsApp, email, or social media</p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <span className="text-primary-600 font-bold text-sm">2</span>
            </div>
            <div>
              <h4 className="font-medium">Friend signs up</h4>
              <p className="text-gray-600 text-sm">Your friend creates an account using your referral code</p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <span className="text-primary-600 font-bold text-sm">3</span>
            </div>
            <div>
              <h4 className="font-medium">Both get rewards</h4>
              <p className="text-gray-600 text-sm">You both receive $10 off your next orders when they place their first order</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Referral History */}
      {referrals.length > 0 && (
        <Card className="p-6">
          <h3 className="font-semibold mb-4">Referral History</h3>
          <div className="space-y-3">
            {referrals.slice(0, 5).map((referral) => (
              <div key={referral.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium">{referral.email}</div>
                  <div className="text-sm text-gray-600">
                    Sent {new Date(referral.sentAt).toLocaleDateString()}
                  </div>
                </div>
                <Badge 
                  variant={referral.status === 'completed' ? 'success' : 'warning'}
                  size="small"
                >
                  {referral.status === 'completed' ? 'Joined' : 'Pending'}
                </Badge>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default ReferralProgram;
