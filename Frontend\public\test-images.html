<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .image-test {
            margin: 20px 0;
            border: 1px solid #ccc;
            padding: 10px;
        }
        img {
            max-width: 300px;
            height: 200px;
            object-fit: cover;
            border: 1px solid #ddd;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>Image Accessibility Test</h1>
    <p>Testing if images from the Django backend are accessible from the frontend.</p>
    
    <div class="image-test">
        <h3>Real Photo Test (Qurma - JPEG)</h3>
        <img
            src="http://127.0.0.1:8000/media/menu_items/photo_czx6F38.jpg"
            alt="Qurma"
            onload="document.getElementById('status1').innerHTML = '✅ Real photo loaded successfully!'"
            onerror="document.getElementById('status1').innerHTML = '❌ Real photo failed to load - Check console for CORS errors'"
        />
        <p id="status1">Loading real photo...</p>
    </div>

    <div class="image-test">
        <h3>Real Image Test (Chicken Biryani - PNG)</h3>
        <img
            src="http://127.0.0.1:8000/media/menu_items/db_erd_4tNibWB.png"
            alt="Chicken Biryani"
            onload="document.getElementById('status1b').innerHTML = '✅ Real image loaded successfully!'"
            onerror="document.getElementById('status1b').innerHTML = '❌ Real image failed to load - Check console for CORS errors'"
        />
        <p id="status1b">Loading real image...</p>
    </div>

    <div class="image-test">
        <h3>Test Colored Shape (Small Test Image)</h3>
        <img
            src="http://127.0.0.1:8000/media/menu_items/test_dish.jpg"
            alt="Test Shape"
            onload="document.getElementById('status1c').innerHTML = '✅ Test shape loaded (this is just a colored rectangle)'"
            onerror="document.getElementById('status1c').innerHTML = '❌ Test shape failed to load'"
        />
        <p id="status1c">Loading test shape...</p>
    </div>
    
    <div class="image-test">
        <h3>Test Image 2</h3>
        <img 
            src="http://127.0.0.1:8000/media/menu_items/test_dish_1752221520.jpg" 
            alt="Test Dish 2"
            onload="document.getElementById('status2').innerHTML = '✅ Image loaded successfully'"
            onerror="document.getElementById('status2').innerHTML = '❌ Image failed to load'"
        />
        <p id="status2">Loading...</p>
    </div>
    
    <div class="image-test">
        <h3>API Test</h3>
        <button onclick="testAPI()">Test Menu Items API</button>
        <div id="api-result"></div>
    </div>
    
    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = 'Testing API...';
            
            try {
                // First get auth token
                const loginResponse = await fetch('http://127.0.0.1:8000/api/auth/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_name: 'restaurant_owner1',
                        password: 'restaurant123'
                    })
                });
                
                if (!loginResponse.ok) {
                    throw new Error('Login failed');
                }
                
                const loginData = await loginResponse.json();
                const token = loginData.data.access_token;
                
                // Now get menu items
                const menuResponse = await fetch('http://127.0.0.1:8000/api/restaurant/menu-items/?restaurant_id=14', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!menuResponse.ok) {
                    throw new Error('Menu API failed');
                }
                
                const menuData = await menuResponse.json();
                const itemsWithImages = menuData.filter(item => item.image);
                
                resultDiv.innerHTML = `
                    <p>✅ API working!</p>
                    <p>Total items: ${menuData.length}</p>
                    <p>Items with images: ${itemsWithImages.length}</p>
                    ${itemsWithImages.map(item => `
                        <div style="margin: 10px 0; padding: 10px; border: 1px solid #eee;">
                            <strong>${item.name}</strong><br>
                            Image URL: <a href="${item.image}" target="_blank">${item.image}</a><br>
                            <img src="${item.image}" style="max-width: 200px; height: 100px;" 
                                 onload="this.nextSibling.innerHTML = '✅ Loaded'"
                                 onerror="this.nextSibling.innerHTML = '❌ Failed'">
                            <span>Loading...</span>
                        </div>
                    `).join('')}
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
