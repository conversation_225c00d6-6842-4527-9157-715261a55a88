#!/usr/bin/env python3
"""
Backend Setup Script for Afghan Sufra
This script sets up the Django backend with all necessary configurations.
"""

import os
import sys
import subprocess
import django
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(f"Output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print(f"Error: {e.stderr}")
        return False

def setup_django():
    """Setup Django environment"""
    print("🚀 Setting up Afghan Sufra Backend...")
    
    # Change to Backend directory
    backend_dir = Path(__file__).parent
    os.chdir(backend_dir)
    print(f"📁 Working directory: {os.getcwd()}")
    
    # Install dependencies
    if not run_command("pip install -r requirements.txt", "Installing dependencies"):
        return False
    
    # Make migrations
    if not run_command("python manage.py makemigrations", "Creating migrations"):
        return False
    
    # Apply migrations
    if not run_command("python manage.py migrate", "Applying migrations"):
        return False
    
    # Collect static files
    if not run_command("python manage.py collectstatic --noinput", "Collecting static files"):
        print("⚠️ Static files collection failed, but continuing...")
    
    # Create superuser (optional)
    print("\n📝 You can create a superuser account later by running:")
    print("   python manage.py createsuperuser")
    
    print("\n🎉 Backend setup completed successfully!")
    print("\n🚀 To start the development server, run:")
    print("   python manage.py runserver 8000")
    print("\n🌐 The API will be available at: http://localhost:8000/api/")
    
    return True

if __name__ == "__main__":
    setup_django()
