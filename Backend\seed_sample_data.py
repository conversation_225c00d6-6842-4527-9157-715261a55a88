#!/usr/bin/env python3
"""
Comprehensive Data Seeding System
Creates sample data for development and testing purposes
"""

import os
import sys
import django
import json
from decimal import Decimal
from datetime import datetime, timedelta
from django.contrib.auth.hashers import make_password

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from restaurant.models import Restaurant, MenuCategory, MenuItem, Address
from orders.models import Order, OrderItem, SavedCart, SavedCartItem
from system_config.models import SystemSetting, ChoiceOption

User = get_user_model()

def create_users():
    """Create sample users for different roles"""
    print("👥 Creating sample users...")
    
    users_data = [
        {
            'user_name': 'admin',
            'email': '<EMAIL>',
            'name': 'System Administrator',
            'phone': '+93 70 123 0001',
            'role': 'admin',
            'password': 'admin123'
        },
        {
            'user_name': 'restaurant_owner1',
            'email': '<EMAIL>',
            'name': '<PERSON>',
            'phone': '+93 70 123 0002',
            'role': 'restaurant',
            'password': 'restaurant123'
        },
        {
            'user_name': 'restaurant_owner2',
            'email': '<EMAIL>',
            'name': 'Fatima Ahmadi',
            'phone': '+93 70 123 0003',
            'role': 'restaurant',
            'password': 'restaurant123'
        },
        {
            'user_name': 'delivery_agent1',
            'email': '<EMAIL>',
            'name': 'Hassan Ali',
            'phone': '+93 70 123 0004',
            'role': 'delivery_agent',
            'password': 'delivery123'
        },
        {
            'user_name': 'delivery_agent2',
            'email': '<EMAIL>',
            'name': 'Omar Karimi',
            'phone': '+93 70 123 0005',
            'role': 'delivery_agent',
            'password': 'delivery123'
        },
        {
            'user_name': 'customer1',
            'email': '<EMAIL>',
            'name': 'Maryam Nazari',
            'phone': '+93 70 123 0006',
            'role': 'customer',
            'password': 'customer123'
        },
        {
            'user_name': 'customer2',
            'email': '<EMAIL>',
            'name': 'Ali Rezaei',
            'phone': '+93 70 123 0007',
            'role': 'customer',
            'password': 'customer123'
        }
    ]
    
    created_users = {}
    for user_data in users_data:
        user, created = User.objects.get_or_create(
            user_name=user_data['user_name'],
            defaults={
                'email': user_data['email'],
                'name': user_data['name'],
                'phone': user_data['phone'],
                'role': user_data['role'],
                'password': make_password(user_data['password']),
                'is_verified': True,
                'is_active': True
            }
        )
        created_users[user_data['user_name']] = user
        if created:
            print(f"   ✅ Created user: {user.name} ({user.role})")
        else:
            print(f"   ⚠️ User exists: {user.name} ({user.role})")
    
    return created_users

def create_restaurants(users):
    """Create sample restaurants"""
    print("\n🏪 Creating sample restaurants...")
    
    restaurants_data = [
        {
            'name': 'Kabul Palace Restaurant',
            'description': 'Authentic Afghan cuisine with traditional recipes passed down through generations. Experience the rich flavors of Afghanistan in a warm, welcoming atmosphere.',
            'contact_number': '+93 70 123 1001',
            'min_order_amount': Decimal('15.00'),
            'delivery_fee': Decimal('3.99'),
            'average_preparation_time': 35,
            'is_active': True,
            'rating': Decimal('4.7'),
            'opening_time': '09:00:00',
            'closing_time': '23:00:00',
            'owner_key': 'restaurant_owner1'
        },
        {
            'name': 'Herat Spice Garden',
            'description': 'Fresh, aromatic spices and traditional Herati dishes. Known for our signature saffron rice and tender kebabs.',
            'contact_number': '+93 70 123 1002',
            'min_order_amount': Decimal('12.00'),
            'delivery_fee': Decimal('2.99'),
            'average_preparation_time': 40,
            'is_active': True,
            'rating': Decimal('4.5'),
            'opening_time': '08:00:00',
            'closing_time': '22:00:00',
            'owner_key': 'restaurant_owner2'
        },
        {
            'name': 'Mazar Delights',
            'description': 'Northern Afghan specialties including fresh naan, hearty stews, and the famous Mazar-style pilaf.',
            'contact_number': '+93 70 123 1003',
            'min_order_amount': Decimal('10.00'),
            'delivery_fee': Decimal('2.50'),
            'average_preparation_time': 30,
            'is_active': True,
            'rating': Decimal('4.3'),
            'opening_time': '07:00:00',
            'closing_time': '21:00:00',
            'owner_key': 'restaurant_owner1'
        }
    ]
    
    # Address data for restaurants
    address_data = [
        {'street': 'Shar-e-Naw, Kabul', 'city': 'Kabul', 'state': 'Kabul', 'postal_code': '1001', 'country': 'Afghanistan', 'latitude': 34.5553, 'longitude': 69.2075},
        {'street': 'Herat City Center', 'city': 'Herat', 'state': 'Herat', 'postal_code': '3001', 'country': 'Afghanistan', 'latitude': 34.3482, 'longitude': 62.1997},
        {'street': 'Mazar-i-Sharif Center', 'city': 'Mazar-i-Sharif', 'state': 'Balkh', 'postal_code': '1801', 'country': 'Afghanistan', 'latitude': 36.7081, 'longitude': 67.1109}
    ]

    created_restaurants = []
    for i, restaurant_data in enumerate(restaurants_data):
        owner_key = restaurant_data.pop('owner_key')
        owner = users.get(owner_key)

        if not owner:
            print(f"   ❌ Owner not found for {restaurant_data['name']}")
            continue

        # Create address for restaurant
        address, addr_created = Address.objects.get_or_create(
            user=owner,
            street=address_data[i]['street'],
            city=address_data[i]['city'],
            defaults=address_data[i]
        )

        restaurant, created = Restaurant.objects.get_or_create(
            name=restaurant_data['name'],
            defaults={
                **restaurant_data,
                'owner': owner,
                'address': address
            }
        )
        created_restaurants.append(restaurant)

        if created:
            print(f"   ✅ Created restaurant: {restaurant.name}")
        else:
            print(f"   ⚠️ Restaurant exists: {restaurant.name}")

    return created_restaurants

def create_menu_items(restaurants):
    """Create sample menu categories and items"""
    print("\n🍽️ Creating menu categories and items...")
    
    menu_data = {
        'Kabul Palace Restaurant': {
            'Main Dishes': [
                {'name': 'Kabuli Pulao', 'description': 'Traditional Afghan rice dish with tender lamb, carrots, and raisins', 'price': '18.99', 'is_vegetarian': False, 'preparation_time': 25},
                {'name': 'Mantu', 'description': 'Steamed dumplings filled with seasoned ground beef, topped with yogurt and lentil sauce', 'price': '16.99', 'is_vegetarian': False, 'preparation_time': 30},
                {'name': 'Qorma Sabzi', 'description': 'Herb stew with tender chunks of beef and kidney beans', 'price': '17.99', 'is_vegetarian': False, 'preparation_time': 35},
                {'name': 'Vegetarian Pulao', 'description': 'Fragrant basmati rice with mixed vegetables and aromatic spices', 'price': '14.99', 'is_vegetarian': True, 'preparation_time': 20}
            ],
            'Appetizers': [
                {'name': 'Bolani', 'description': 'Crispy flatbread stuffed with potatoes and herbs', 'price': '8.99', 'is_vegetarian': True, 'preparation_time': 15},
                {'name': 'Ashak', 'description': 'Leek-filled dumplings with meat sauce and yogurt', 'price': '12.99', 'is_vegetarian': False, 'preparation_time': 20}
            ],
            'Beverages': [
                {'name': 'Afghan Green Tea', 'description': 'Traditional green tea with cardamom', 'price': '3.99', 'is_vegetarian': True, 'preparation_time': 5},
                {'name': 'Doogh', 'description': 'Refreshing yogurt drink with mint', 'price': '4.99', 'is_vegetarian': True, 'preparation_time': 3}
            ]
        },
        'Herat Spice Garden': {
            'Kebabs': [
                {'name': 'Chapli Kebab', 'description': 'Spiced ground beef patties with fresh herbs', 'price': '15.99', 'is_vegetarian': False, 'preparation_time': 18},
                {'name': 'Seekh Kebab', 'description': 'Grilled minced lamb skewers with aromatic spices', 'price': '17.99', 'is_vegetarian': False, 'preparation_time': 20},
                {'name': 'Chicken Tikka', 'description': 'Marinated chicken pieces grilled to perfection', 'price': '16.99', 'is_vegetarian': False, 'preparation_time': 22}
            ],
            'Rice Dishes': [
                {'name': 'Saffron Rice', 'description': 'Aromatic basmati rice with premium saffron', 'price': '12.99', 'is_vegetarian': True, 'preparation_time': 15},
                {'name': 'Zereshk Polo', 'description': 'Rice with barberries and saffron', 'price': '14.99', 'is_vegetarian': True, 'preparation_time': 18}
            ],
            'Desserts': [
                {'name': 'Firni', 'description': 'Traditional milk pudding with cardamom and pistachios', 'price': '6.99', 'is_vegetarian': True, 'preparation_time': 10},
                {'name': 'Baklava', 'description': 'Layered pastry with nuts and honey', 'price': '7.99', 'is_vegetarian': True, 'preparation_time': 5}
            ]
        },
        'Mazar Delights': {
            'Bread & Naan': [
                {'name': 'Fresh Naan', 'description': 'Warm, fluffy bread baked in traditional tandoor', 'price': '3.99', 'is_vegetarian': True, 'preparation_time': 8},
                {'name': 'Roghani Naan', 'description': 'Sesame-topped naan bread', 'price': '4.99', 'is_vegetarian': True, 'preparation_time': 10}
            ],
            'Stews': [
                {'name': 'Khorak-e Bademjan', 'description': 'Eggplant stew with tomatoes and herbs', 'price': '13.99', 'is_vegetarian': True, 'preparation_time': 25},
                {'name': 'Ghormeh Sabzi', 'description': 'Herb stew with kidney beans and dried lime', 'price': '15.99', 'is_vegetarian': False, 'preparation_time': 30}
            ],
            'Soups': [
                {'name': 'Ash-e Reshteh', 'description': 'Hearty noodle soup with herbs and beans', 'price': '9.99', 'is_vegetarian': True, 'preparation_time': 20},
                {'name': 'Shorba', 'description': 'Traditional Afghan soup with vegetables', 'price': '8.99', 'is_vegetarian': True, 'preparation_time': 15}
            ]
        }
    }
    
    created_items = []
    for restaurant in restaurants:
        if restaurant.name in menu_data:
            restaurant_menu = menu_data[restaurant.name]
            
            for category_name, items in restaurant_menu.items():
                # Create category
                category, created = MenuCategory.objects.get_or_create(
                    restaurant=restaurant,
                    name=category_name,
                    defaults={'description': f'{category_name} from {restaurant.name}'}
                )
                
                if created:
                    print(f"   ✅ Created category: {category_name} for {restaurant.name}")
                
                # Create menu items
                for item_data in items:
                    menu_item, created = MenuItem.objects.get_or_create(
                        category=category,
                        name=item_data['name'],
                        defaults={
                            'description': item_data['description'],
                            'price': Decimal(item_data['price']),
                            'is_vegetarian': item_data['is_vegetarian'],
                            'preparation_time': item_data['preparation_time'],
                            'is_available': True
                        }
                    )
                    created_items.append(menu_item)
                    
                    if created:
                        print(f"     ✅ Created item: {item_data['name']}")
    
    return created_items

def main():
    print("🚀 Starting Comprehensive Data Seeding")
    print("=" * 60)
    
    # Create users
    users = create_users()
    
    # Create restaurants
    restaurants = create_restaurants(users)
    
    # Create menu items
    menu_items = create_menu_items(restaurants)
    
    print("\n" + "=" * 60)
    print("🎉 Data Seeding Complete!")
    print("\n📊 Summary:")
    print(f"   Users: {User.objects.count()}")
    print(f"   Restaurants: {Restaurant.objects.count()}")
    print(f"   Menu Categories: {MenuCategory.objects.count()}")
    print(f"   Menu Items: {MenuItem.objects.count()}")
    print(f"   System Settings: {SystemSetting.objects.count()}")
    print(f"   Choice Options: {ChoiceOption.objects.count()}")
    
    print("\n🔑 Test Credentials:")
    print("   Admin: admin / admin123")
    print("   Restaurant Owner: restaurant_owner1 / restaurant123")
    print("   Delivery Agent: delivery_agent1 / delivery123")
    print("   Customer: customer1 / customer123")
    
    print("\n🌐 System Status:")
    print("   ✅ Dynamic configuration system active")
    print("   ✅ Sample restaurants with menus created")
    print("   ✅ User roles properly configured")
    print("   ✅ Ready for frontend testing")

if __name__ == "__main__":
    main()
