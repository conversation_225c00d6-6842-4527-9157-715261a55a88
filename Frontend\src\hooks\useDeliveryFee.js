import { useState, useEffect, useMemo } from 'react';
import { 
  calculateDeliveryFee, 
  getDeliveryFeeText, 
  getDeliveryTimeText,
  DELIVERY_CONFIG 
} from '../utils/deliveryCalculator';
import { useLocation } from './useLocation';

/**
 * Hook for managing dynamic delivery fees
 */
export const useDeliveryFee = (restaurant, options = {}) => {
  const { selectedAddress } = useLocation();
  const [deliveryInfo, setDeliveryInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const {
    orderAmount = 0,
    isExpress = false,
    weather = 'normal',
    autoCalculate = true
  } = options;

  // Calculate delivery fee when dependencies change
  const calculateFee = useMemo(() => {
    if (!restaurant || !selectedAddress || !autoCalculate) {
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const result = calculateDeliveryFee({
        restaurantLocation: {
          lat: restaurant.location?.lat || restaurant.lat,
          lng: restaurant.location?.lng || restaurant.lng
        },
        customerLocation: {
          lat: selectedAddress.coordinates[0],
          lng: selectedAddress.coordinates[1]
        },
        orderAmount,
        isExpress,
        weather
      });

      setDeliveryInfo(result);
      setLoading(false);
      return result;
    } catch (err) {
      setError(err.message);
      setLoading(false);
      return null;
    }
  }, [restaurant, selectedAddress, orderAmount, isExpress, weather, autoCalculate]);

  // Manual calculation function
  const recalculateFee = (customOptions = {}) => {
    if (!restaurant || !selectedAddress) {
      setError('Missing restaurant or customer location');
      return null;
    }

    setLoading(true);
    setError(null);

    try {
      const result = calculateDeliveryFee({
        restaurantLocation: {
          lat: restaurant.location?.lat || restaurant.lat,
          lng: restaurant.location?.lng || restaurant.lng
        },
        customerLocation: {
          lat: selectedAddress.coordinates[0],
          lng: selectedAddress.coordinates[1]
        },
        orderAmount: customOptions.orderAmount || orderAmount,
        isExpress: customOptions.isExpress || isExpress,
        weather: customOptions.weather || weather
      });

      setDeliveryInfo(result);
      setLoading(false);
      return result;
    } catch (err) {
      setError(err.message);
      setLoading(false);
      return null;
    }
  };

  // Effect to trigger calculation
  useEffect(() => {
    if (autoCalculate) {
      calculateFee;
    }
  }, [calculateFee, autoCalculate]);

  // Computed values
  const deliveryFeeText = useMemo(() => {
    return deliveryInfo ? getDeliveryFeeText(deliveryInfo) : 'Calculating...';
  }, [deliveryInfo]);

  const deliveryTimeText = useMemo(() => {
    return deliveryInfo?.estimatedTime ? getDeliveryTimeText(deliveryInfo.estimatedTime) : 'Calculating...';
  }, [deliveryInfo]);

  const isDeliverable = useMemo(() => {
    return deliveryInfo?.isDeliverable || false;
  }, [deliveryInfo]);

  const isFreeDelivery = useMemo(() => {
    return deliveryInfo?.fee === 0 && deliveryInfo?.isDeliverable;
  }, [deliveryInfo]);

  const deliveryFee = useMemo(() => {
    return deliveryInfo?.fee || 0;
  }, [deliveryInfo]);

  const distance = useMemo(() => {
    return deliveryInfo?.distance || 0;
  }, [deliveryInfo]);

  return {
    // State
    deliveryInfo,
    loading,
    error,
    
    // Computed values
    deliveryFee,
    deliveryFeeText,
    deliveryTimeText,
    distance,
    isDeliverable,
    isFreeDelivery,
    
    // Actions
    recalculateFee,
    
    // Configuration
    config: DELIVERY_CONFIG
  };
};

/**
 * Hook for delivery fee breakdown display
 */
export const useDeliveryBreakdown = (deliveryInfo) => {
  const breakdown = useMemo(() => {
    if (!deliveryInfo?.breakdown) return null;

    const items = [];
    
    // Base fee
    if (deliveryInfo.breakdown.baseFee > 0) {
      items.push({
        label: `Base delivery fee (${deliveryInfo.zone || 'Zone'})`,
        amount: deliveryInfo.breakdown.baseFee,
        type: 'base'
      });
    }
    
    // Distance fee
    if (deliveryInfo.breakdown.distanceFee > 0) {
      items.push({
        label: `Distance fee (${deliveryInfo.distance?.toFixed(1)}km)`,
        amount: deliveryInfo.breakdown.distanceFee,
        type: 'distance'
      });
    }
    
    // Modifiers (surcharges)
    if (deliveryInfo.breakdown.modifiers) {
      deliveryInfo.breakdown.modifiers.forEach(modifier => {
        items.push({
          label: modifier.name,
          amount: modifier.amount,
          type: 'modifier',
          multiplier: modifier.multiplier
        });
      });
    }
    
    // Discount
    if (deliveryInfo.breakdown.discount) {
      items.push({
        label: deliveryInfo.breakdown.discount,
        amount: -deliveryInfo.fee,
        type: 'discount'
      });
    }
    
    return {
      items,
      total: deliveryInfo.fee,
      distance: deliveryInfo.distance,
      zone: deliveryInfo.zone
    };
  }, [deliveryInfo]);

  return breakdown;
};

/**
 * Hook for delivery zones information
 */
export const useDeliveryZones = () => {
  const zones = useMemo(() => {
    return DELIVERY_CONFIG.zones.map(zone => ({
      ...zone,
      description: `Up to ${zone.maxDistance}km - $${zone.fee.toFixed(2)}`
    }));
  }, []);

  const getZoneByDistance = (distance) => {
    return DELIVERY_CONFIG.zones.find(zone => distance <= zone.maxDistance) || 
           DELIVERY_CONFIG.zones[DELIVERY_CONFIG.zones.length - 1];
  };

  return {
    zones,
    getZoneByDistance,
    maxDistance: DELIVERY_CONFIG.maxDeliveryDistance,
    freeDeliveryThreshold: DELIVERY_CONFIG.freeDeliveryThreshold
  };
};

export default useDeliveryFee;
