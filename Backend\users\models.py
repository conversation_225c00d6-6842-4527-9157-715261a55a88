from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager, PermissionsMixin
from django.db import models
import pyotp
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from datetime import timedelta

class UserManager(BaseUserManager):
    def create_user(self, user_name, password=None, **extra_fields):
        
        if not user_name:
            raise ValueError('user_name number is required')
     
        user = self.model(user_name=user_name, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, user_name, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        return self.create_user(user_name, password, **extra_fields)

class User(AbstractBaseUser, PermissionsMixin):
    id = models.AutoField(primary_key=True)
    name = models.Char<PERSON>ield(max_length=100)
    user_name=models.Char<PERSON><PERSON>(max_length=100,unique=True)
    phone = models.CharField(max_length=15, unique=True)
    password = models.CharField(max_length=128)
    role = models.Char<PERSON>ield(max_length=20, choices=[('customer', 'Customer'), ('restaurant', 'Restaurant'), ('admin', 'Admin'),('delivery_agent', 'Delivery_agent')])
    email = models.EmailField(unique=True, null=True, blank=True)  # Add email field
    is_verified = models.BooleanField(default=False)  # Track email verification
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)  # Must be False for regular users
    date_joined=models.DateTimeField(auto_now_add=True)
    objects = UserManager()

    USERNAME_FIELD = 'user_name'
    REQUIRED_FIELDS = ['name','phone']

    def __str__(self):
        return self.name
    


class OTPVerification(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    otp = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    purpose = models.CharField(max_length=20, choices=[
        ('registration', 'Registration'),
        ('password_reset', 'Password Reset')
    ])
    is_used = models.BooleanField(default=False)

    def is_valid(self):
        return not self.is_used and (timezone.now() - self.created_at) < timedelta(minutes=10)
    
    def send_otp_email(self):
        subject = 'Your OTP Verification Code'
        message = f'Your OTP code is: {self.otp}\nThis code is valid for 10 minutes.'
        try:
            send_mail(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [self.user.email],
                fail_silently=False,
            )
            print(f"✅ OTP email sent successfully to {self.user.email}: {self.otp}")
        except Exception as e:
            print(f"❌ Failed to send OTP email to {self.user.email}: {e}")
            print(f"🔑 OTP for testing: {self.otp}")
            # Don't raise the exception to prevent registration failure
            pass
    
    @classmethod
    def generate_otp(cls, user, purpose):
        # Delete any existing OTPs for this user and purpose
        cls.objects.filter(user=user, purpose=purpose).delete()
        
        # Generate a time-based OTP
        totp = pyotp.TOTP(pyotp.random_base32(), digits=6, interval=600)  # 10 minutes validity
        otp = totp.now()
        
        # Create and save the OTP record
        otp_record = cls.objects.create(
            user=user,
            otp=otp,
            purpose=purpose
        )
        
        return otp_record
    


    #----------------------Address----------------------------

