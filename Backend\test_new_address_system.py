#!/usr/bin/env python3
"""
Test the new address system end-to-end
"""
import os
import sys
import django
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from restaurant.models import Address
from orders.models import Order
from orders.serializers import OrderSerializer

User = get_user_model()

def test_address_api():
    """Test the address API endpoints"""
    print("🧪 Testing Address API...")
    
    # Get a customer
    try:
        customer = User.objects.filter(role='customer').first()
        if not customer:
            print("❌ No customer found")
            return False
            
        print(f"Testing with customer: {customer.user_name}")
        
        # Check existing addresses
        addresses = Address.objects.filter(user=customer)
        print(f"Customer has {addresses.count()} existing addresses")
        
        for addr in addresses:
            print(f"  - ID: {addr.id}, Address: {addr.street}, {addr.city}")
        
        return True
        
    except Exception as e:
        print(f"❌ Address API test failed: {e}")
        return False

def test_order_validation():
    """Test order validation with addresses"""
    print("\n🧪 Testing Order Validation...")
    
    try:
        customer = User.objects.filter(role='customer').first()
        if not customer:
            print("❌ No customer found")
            return False
        
        # Get customer's first address
        address = Address.objects.filter(user=customer).first()
        if not address:
            print("❌ No address found for customer")
            return False
        
        print(f"Testing order validation with address ID: {address.id}")
        
        # Create a mock order data
        order_data = {
            'delivery_address': address.id,
            'restaurant': 1,  # Assuming restaurant ID 1 exists
            'payment_method': 'cash_on_delivery',
            'special_instructions': 'Test order',
            'items': []
        }
        
        # Create a mock request
        factory = RequestFactory()
        request = factory.post('/orders/', order_data)
        request.user = customer
        
        # Test serializer validation
        serializer = OrderSerializer(data=order_data, context={'request': request})
        
        if serializer.is_valid():
            print("✅ Order validation passed")
            print(f"   Validated address ID: {serializer.validated_data['delivery_address'].id}")
            return True
        else:
            print("❌ Order validation failed:")
            for field, errors in serializer.errors.items():
                print(f"   {field}: {errors}")
            return False
            
    except Exception as e:
        print(f"❌ Order validation test failed: {e}")
        return False

def test_address_ownership():
    """Test that users can only access their own addresses"""
    print("\n🧪 Testing Address Ownership...")
    
    try:
        customers = User.objects.filter(role='customer')[:2]
        if len(customers) < 2:
            print("⚠️ Need at least 2 customers for ownership test")
            return True
        
        customer1, customer2 = customers[0], customers[1]
        
        # Get address from customer1
        address1 = Address.objects.filter(user=customer1).first()
        if not address1:
            print("❌ No address found for customer1")
            return False
        
        print(f"Customer1 ({customer1.user_name}) address ID: {address1.id}")
        
        # Try to use customer1's address for customer2's order
        order_data = {
            'delivery_address': address1.id,
            'restaurant': 1,
            'payment_method': 'cash_on_delivery',
            'items': []
        }
        
        factory = RequestFactory()
        request = factory.post('/orders/', order_data)
        request.user = customer2  # Different customer
        
        serializer = OrderSerializer(data=order_data, context={'request': request})
        
        if serializer.is_valid():
            print("❌ Security issue: Customer can use another customer's address!")
            return False
        else:
            print("✅ Address ownership validation working correctly")
            print(f"   Error: {serializer.errors.get('delivery_address', ['Unknown error'])[0]}")
            return True
            
    except Exception as e:
        print(f"❌ Address ownership test failed: {e}")
        return False

def create_test_address():
    """Create a test address for testing"""
    print("\n🏗️ Creating test address...")
    
    try:
        customer = User.objects.filter(role='customer').first()
        if not customer:
            print("❌ No customer found")
            return False
        
        # Create a new test address
        test_address = Address.objects.create(
            user=customer,
            street="Test Street 123",
            city="Kabul",
            state="Kabul",
            postal_code="1001",
            country="Afghanistan",
            latitude=34.5553,
            longitude=69.2075
        )
        
        print(f"✅ Created test address with ID: {test_address.id}")
        print(f"   Address: {test_address.street}, {test_address.city}")
        return test_address
        
    except Exception as e:
        print(f"❌ Failed to create test address: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting New Address System Tests...")
    print("=" * 50)
    
    # Test 1: Address API
    api_test = test_address_api()
    
    # Test 2: Order validation
    order_test = test_order_validation()
    
    # Test 3: Address ownership
    ownership_test = test_address_ownership()
    
    # Test 4: Create test address
    create_test = create_test_address()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Address API: {'✅ PASS' if api_test else '❌ FAIL'}")
    print(f"   Order Validation: {'✅ PASS' if order_test else '❌ FAIL'}")
    print(f"   Address Ownership: {'✅ PASS' if ownership_test else '❌ FAIL'}")
    print(f"   Create Test Address: {'✅ PASS' if create_test else '❌ FAIL'}")
    
    all_passed = all([api_test, order_test, ownership_test, create_test])
    
    if all_passed:
        print("\n🎉 All tests passed! The new address system is working correctly.")
        
        # Show available addresses for frontend testing
        print("\n📍 Available addresses for frontend testing:")
        customers = User.objects.filter(role='customer')
        for customer in customers:
            addresses = Address.objects.filter(user=customer)
            if addresses.exists():
                print(f"\n👤 {customer.user_name}:")
                for addr in addresses:
                    print(f"   ID: {addr.id} - {addr.street}, {addr.city}")
    else:
        print("\n⚠️ Some tests failed. Please check the issues above.")
    
    print("\n✅ Address system testing complete!")

if __name__ == '__main__':
    main()
