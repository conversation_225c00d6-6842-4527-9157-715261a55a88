# Restaurant CRUD API Integration

This document outlines how the Restaurant CRUD API endpoints are integrated into the Afghan Sofra platform.

## API Endpoints Overview

The backend provides the following Restaurant CRUD endpoints:

### 1. Create Restaurant
- **Method**: `POST`
- **URL**: `{{base_url}}/restaurant/restaurants/`
- **Headers**: 
  - `Authorization: Bearer {{access_token}}`
  - `Content-Type: multipart/form-data`
- **Body**: Form data with restaurant details, logo, banner, and address as raw JSON

### 2. Get Restaurant Details
- **Method**: `GET`
- **URL**: `{{base_url}}/restaurant/restaurants/{id}/`
- **Headers**: 
  - `Authorization: Bearer {{access_token}}`
  - `Content-Type: application/json`

### 3. Update Restaurant (Full)
- **Method**: `PUT`
- **URL**: `{{base_url}}/restaurant/restaurants/{id}/`
- **Headers**: 
  - `Authorization: Bearer {{access_token}}`
  - `Content-Type: application/json`
- **Body**: Complete restaurant data

### 4. Update Restaurant (Partial)
- **Method**: `PATCH`
- **URL**: `{{base_url}}/restaurant/restaurants/{id}/`
- **Headers**: 
  - `Authorization: Bearer {{access_token}}`
  - `Content-Type: application/json`
- **Body**: Partial restaurant data

### 5. Delete Restaurant
- **Method**: `DELETE`
- **URL**: `{{base_url}}/restaurant/restaurants/{id}/`
- **Headers**: 
  - `Authorization: Bearer {{access_token}}`
  - `Content-Type: application/json`

## Frontend Integration

### API Service Layer (`src/utils/restaurantApi.js`)

The restaurant API is implemented with:

```javascript
export const restaurantApi = {
  createRestaurant(restaurantData),    // POST with multipart/form-data
  getRestaurant(restaurantId),         // GET single restaurant
  updateRestaurant(restaurantId, data), // PUT full update
  patchRestaurant(restaurantId, data), // PATCH partial update
  deleteRestaurant(restaurantId),      // DELETE restaurant
  getRestaurants(params)               // GET all restaurants
};
```

**Key Features:**
- ✅ **Authentication**: Automatic Bearer token injection
- ✅ **File Upload**: Handles multipart/form-data for logo and banner
- ✅ **Address Format**: Sends address as raw JSON string as required
- ✅ **Error Handling**: Comprehensive error handling and response formatting
- ✅ **Token Refresh**: Automatic token refresh on 401 errors

### Context Layer (`src/context/RestaurantContext.jsx`)

The RestaurantContext provides:

```javascript
const {
  restaurants,           // Array of restaurants
  currentRestaurant,     // Currently selected restaurant
  loading,              // Loading state
  error,                // Error state
  createRestaurant,     // Create function
  getRestaurant,        // Get single function
  updateRestaurant,     // Update function
  patchRestaurant,      // Patch function
  deleteRestaurant,     // Delete function
  getRestaurants,       // Get all function
  clearError           // Clear error function
} = useRestaurant();
```

### Component Integration

#### 1. Restaurant Registration (`src/pages/restaurant/RegisterRestaurantEnhanced.jsx`)

**Multi-step registration form** that uses the `createRestaurant` API:

```javascript
const onSubmit = async (data) => {
  const restaurantData = {
    name: data.restaurantName,
    description: data.description,
    contact_number: data.phone,
    email: data.email,
    opening_time: data.openingTime || "09:00:00",
    closing_time: data.closingTime || "21:00:00",
    address: {
      street: data.address,
      city: data.city,
      state: data.state || "",
      postal_code: data.postalCode || "",
      country: "USA",
      latitude: 0,
      longitude: 0,
    },
    logo: logoFile,
    banner: bannerFile,
    // ... other fields
  };

  const result = await createRestaurant(restaurantData);
  // Handle result...
};
```

#### 2. Restaurant Profile Management (`src/pages/restaurant/RestaurantProfile.jsx`)

Uses `getRestaurant`, `updateRestaurant`, and `patchRestaurant` for profile management.

#### 3. Admin Restaurant Management (`src/pages/admin/RestaurantApprovalEnhanced.jsx`)

Uses all CRUD operations for restaurant approval workflow.

## Data Format Examples

### Create Restaurant Request
```javascript
{
  name: "My Awesome Restaurant",
  description: "Best food in town",
  contact_number: "+123456789",
  opening_time: "09:00:00",
  closing_time: "22:00:00",
  address: {
    street: "123 Main St",
    city: "New York",
    state: "NY",
    postal_code: "10001",
    country: "USA",
    latitude: 40.7128,
    longitude: -74.0060
  },
  logo: File, // File object
  banner: File // File object
}
```

### Update Restaurant Request (PUT)
```javascript
{
  name: "Updated Restaurant Name",
  description: "Updated description",
  contact_number: "+123456789",
  opening_time: "10:00:00",
  closing_time: "23:00:00",
  address: {
    street: "456 Updated Street",
    city: "Updated City",
    state: "UP",
    postal_code: "54321",
    country: "USA",
    latitude: 40.7128,
    longitude: -74.0060
  }
}
```

### Patch Restaurant Request (PATCH)
```javascript
{
  name: "New Name Only",
  description: "Updated description only"
  // Only fields that need to be updated
}
```

## Testing

### API Test Component (`src/components/restaurant/RestaurantApiTest.jsx`)

A comprehensive test suite is available at `/admin/restaurant-api-test` that allows you to:

- ✅ **Test CREATE** with file uploads
- ✅ **Test GET** single restaurant
- ✅ **Test GET ALL** restaurants
- ✅ **Test UPDATE (PUT)** full update
- ✅ **Test PATCH** partial update
- ✅ **Test DELETE** restaurant
- ✅ **View responses** and error handling
- ✅ **File upload testing** for logo and banner

### How to Test

1. **Login as Admin**: Navigate to `/admin`
2. **Access Test Suite**: Go to `/admin/restaurant-api-test`
3. **Upload Files** (optional): Select logo and banner images
4. **Run Tests**: Click each test button to verify API functionality
5. **View Results**: See real-time test results and API responses

## Error Handling

The integration includes comprehensive error handling:

- **Network Errors**: Connection timeouts and network failures
- **Authentication Errors**: Token expiration and refresh
- **Validation Errors**: Backend validation error messages
- **File Upload Errors**: File size and format validation
- **Server Errors**: 500+ status code handling

## Security Features

- **Bearer Token Authentication**: All requests include authentication
- **Automatic Token Refresh**: Handles expired tokens seamlessly
- **Secure File Upload**: Validates file types and sizes
- **CORS Handling**: Proper cross-origin request handling

## Usage in Restaurant Flow

1. **Partner Landing** → `/restaurant-partner`
2. **Registration** → `/register-restaurant` (uses CREATE API)
3. **Approval** → Admin reviews via `/admin/restaurant-approvals`
4. **Onboarding** → `/restaurant/onboarding`
5. **Management** → `/restaurant/profile` (uses GET, UPDATE, PATCH APIs)

The Restaurant CRUD API is fully integrated and ready for production use with the Afghan Sofra platform.
