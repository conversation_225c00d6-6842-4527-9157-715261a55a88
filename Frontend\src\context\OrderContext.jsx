import React, { createContext, useContext, useState, useEffect } from "react";
import { orderApi } from "../utils/orderApi";
import { useAuth } from "./AuthContext";

const OrderContext = createContext();

export const useOrder = () => {
  const context = useContext(OrderContext);
  if (!context) {
    throw new Error("useOrder must be used within an OrderProvider");
  }
  return context;
};

export const OrderProvider = ({ children }) => {
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [currentOrder, setCurrentOrder] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load orders when component mounts or user changes
  useEffect(() => {
    // Only load orders if user is authenticated and has access_token
    if (
      user &&
      user.access_token &&
      (user.role === "restaurant" ||
        user.role === "customer" ||
        user.role === "delivery")
    ) {
      console.log(
        "OrderContext: User authenticated, loading orders for role:",
        user.role
      );
      loadOrders();
    } else if (user && !user.access_token) {
      console.log(
        "OrderContext: User exists but no access_token, skipping order load"
      );
    } else if (!user) {
      console.log("OrderContext: No user, clearing orders");
      setOrders([]);
      setCurrentOrder(null);
      setError(null);
    }
  }, [user]);

  /**
   * Load all orders for the current user
   */
  const loadOrders = async () => {
    // Double-check authentication before making API call
    if (!user || !user.access_token) {
      console.log("OrderContext: Cannot load orders - user not authenticated");
      setError("User not authenticated");
      return;
    }

    console.log(
      "OrderContext: Loading orders for user:",
      user?.role,
      user?.name
    );
    setLoading(true);
    setError(null);

    try {
      const result = await orderApi.getOrders();
      console.log("OrderContext: API result:", result);

      if (result.success) {
        console.log(
          "OrderContext: Orders loaded successfully:",
          result.data?.length || 0,
          "orders"
        );
        setOrders(result.data);
      } else {
        console.error("OrderContext: Failed to load orders:", result.error);
        setError(result.error);
      }
    } catch (err) {
      // Handle 401 errors specifically
      if (err.response?.status === 401) {
        console.log("OrderContext: 401 Unauthorized - user needs to login");
        setError("Please login to view orders");
        // Clear user data if token is invalid
        localStorage.removeItem("afghanSofraUser");
      } else {
        const errorMessage = "Failed to load orders";
        console.error("OrderContext: Load orders error:", err);
        setError(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  /**
   * Create a new order
   */
  const createOrder = async (orderData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await orderApi.createOrder(orderData);

      if (result.success) {
        // Add new order to the list
        setOrders((prev) => [result.data, ...prev]);
        setCurrentOrder(result.data);
        return {
          success: true,
          data: result.data,
          message: "Order created successfully",
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to create order";
      setError(errorMessage);
      console.error("Create order error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get a single order by ID
   */
  const getOrder = async (orderId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await orderApi.getOrder(orderId);

      if (result.success) {
        setCurrentOrder(result.data);
        return {
          success: true,
          data: result.data,
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to get order";
      setError(errorMessage);
      console.error("Get order error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Update an order (full update)
   */
  const updateOrder = async (orderId, orderData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await orderApi.updateOrder(orderId, orderData);

      if (result.success) {
        // Update order in the list
        setOrders((prev) =>
          prev.map((order) =>
            order.id === orderId ? { ...order, ...result.data } : order
          )
        );

        // Update current order if it's the same
        if (currentOrder?.id === orderId) {
          setCurrentOrder(result.data);
        }

        return {
          success: true,
          data: result.data,
          message: "Order updated successfully",
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to update order";
      setError(errorMessage);
      console.error("Update order error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get order by ID
   */
  const getOrderById = async (orderId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await orderApi.getOrderById(orderId);

      if (result.success) {
        return {
          success: true,
          data: result.data,
          message: "Order fetched successfully",
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to fetch order";
      setError(errorMessage);
      console.error("Get order error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Partially update an order
   */
  const patchOrder = async (orderId, updateData) => {
    setLoading(true);
    setError(null);

    try {
      const result = await orderApi.patchOrder(orderId, updateData);

      if (result.success) {
        // Update order in the list
        setOrders((prev) =>
          prev.map((order) =>
            order.id === orderId ? { ...order, ...result.data } : order
          )
        );

        // Update current order if it's the same
        if (currentOrder?.id === orderId) {
          setCurrentOrder({ ...currentOrder, ...result.data });
        }

        return {
          success: true,
          data: result.data,
          message: "Order updated successfully",
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to update order";
      setError(errorMessage);
      console.error("Patch order error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Delete an order
   */
  const deleteOrder = async (orderId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await orderApi.deleteOrder(orderId);

      if (result.success) {
        // Remove order from the list
        setOrders((prev) => prev.filter((order) => order.id !== orderId));

        // Clear current order if it's the same
        if (currentOrder?.id === orderId) {
          setCurrentOrder(null);
        }

        return {
          success: true,
          message: "Order deleted successfully",
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to delete order";
      setError(errorMessage);
      console.error("Delete order error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get orders by status
   */
  const getOrdersByStatus = async (status) => {
    setLoading(true);
    setError(null);

    try {
      const result = await orderApi.getOrdersByStatus(status);

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to get orders by status";
      setError(errorMessage);
      console.error("Get orders by status error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Get order status history
   */
  const getOrderStatusHistory = async (orderId) => {
    setLoading(true);
    setError(null);

    try {
      const result = await orderApi.getOrderStatusHistory(orderId);

      if (result.success) {
        return {
          success: true,
          data: result.data,
        };
      } else {
        setError(result.error);
        return result;
      }
    } catch (err) {
      const errorMessage = "Failed to get order status history";
      setError(errorMessage);
      console.error("Get order status history error:", err);
      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Clear error state
   */
  const clearError = () => {
    setError(null);
  };

  /**
   * Refresh orders
   */
  const refreshOrders = () => {
    loadOrders();
  };

  const value = {
    // State
    orders,
    currentOrder,
    loading,
    error,

    // Actions
    createOrder,
    getOrder,
    getOrderById,
    updateOrder,
    patchOrder,
    deleteOrder,
    getOrdersByStatus,
    getOrderStatusHistory,
    loadOrders,
    refreshOrders,
    clearError,
    setCurrentOrder,
  };

  return (
    <OrderContext.Provider value={value}>{children}</OrderContext.Provider>
  );
};

export default OrderContext;
