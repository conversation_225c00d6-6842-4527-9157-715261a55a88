#!/usr/bin/env python3
"""
Create a test address for the test customer to enable checkout
"""

import os
import sys
import django

# Add the Backend directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'Backend'))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from restaurant.models import Address

User = get_user_model()

def create_test_customer_address():
    """Create a test address for the test customer"""
    
    print("🏠 Creating Test Customer Address")
    print("=" * 40)
    
    try:
        # Find or create a test customer
        test_customer = None
        
        # Try to find existing test customer
        try:
            test_customer = User.objects.get(email='<EMAIL>')
            print(f"✅ Found test customer: {test_customer.email}")
        except User.DoesNotExist:
            # Try to find any customer
            customers = User.objects.filter(role='customer')
            if customers.exists():
                test_customer = customers.first()
                print(f"✅ Using existing customer: {test_customer.email}")
            else:
                # Create a new test customer
                import random
                phone_suffix = random.randint(1000, 9999)
                test_customer = User.objects.create(
                    email='<EMAIL>',
                    name='Checkout Test Customer',
                    user_name=f'checkout_test_{phone_suffix}',
                    role='customer',
                    is_verified=True,
                    phone=f'+93 78 888 {phone_suffix}'
                )
                test_customer.set_password('testpass123')
                test_customer.save()
                print(f"✅ Created new test customer: {test_customer.email}")
        
        # Check if customer already has addresses
        existing_addresses = Address.objects.filter(user=test_customer)
        print(f"📍 Existing addresses: {existing_addresses.count()}")
        
        if existing_addresses.exists():
            print("✅ Customer already has addresses:")
            for addr in existing_addresses:
                print(f"   - {addr.street}, {addr.city} (ID: {addr.id})")
        else:
            # Create a test address
            test_address = Address.objects.create(
                user=test_customer,
                street='123 Test Delivery Street',
                city='Kabul',
                state='Kabul Province',
                postal_code='1001',
                country='Afghanistan',
                latitude=34.5600,
                longitude=69.2100
            )
            print(f"✅ Created test address:")
            print(f"   - {test_address.street}, {test_address.city}")
            print(f"   - Address ID: {test_address.id}")
        
        print(f"\n🎯 To test checkout:")
        print(f"   1. Login as: {test_customer.email} / testpass123")
        print(f"   2. Add items to cart from any restaurant")
        print(f"   3. Go to checkout: http://localhost:5174/checkout")
        print(f"   4. Select the saved address in the address section")
        print(f"   5. Place Order button should be enabled!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    create_test_customer_address()
