import React, { useState, useEffect } from "react";
import {
  MapPin,
  Phone,
  Navigation,
  Package,
  Clock,
  DollarSign,
  Wifi,
  WifiOff,
  Battery,
  Signal,
  Menu,
  Home,
  User,
  MessageCircle,
  Settings,
  LogOut,
  CheckCircle,
  AlertCircle,
  Truck,
  Star,
  Calendar,
  TrendingUp,
} from "lucide-react";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";

const AgentMobileApp = () => {
  const [isOnline, setIsOnline] = useState(false);
  const [currentView, setCurrentView] = useState("dashboard");
  const [networkStatus, setNetworkStatus] = useState("online");
  const [batteryLevel, setBatteryLevel] = useState(85);
  const [currentOrder, setCurrentOrder] = useState(null);
  const [todayEarnings, setTodayEarnings] = useState(1850);
  const [completedDeliveries, setCompletedDeliveries] = useState(8);
  const [availableOrders, setAvailableOrders] = useState(mockOrders);
  const [orderStatus, setOrderStatus] = useState("available"); // available, assigned, picked_up, in_transit, delivered
  const [showOrderDetails, setShowOrderDetails] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [cashCollected, setCashCollected] = useState(0);
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  // Simulated data for Afghan context
  const agentData = {
    name: "احمد علی",
    nameEn: "Ahmad Ali",
    id: "DA001",
    vehicle: "Honda 125cc",
    zone: "شهر نو کابل",
    rating: 4.8,
    totalDeliveries: 247,
    todayTarget: 15,
  };

  const mockOrders = [
    {
      id: "ORD001",
      restaurant: "کابل رستوران",
      restaurantEn: "Kabul Restaurant",
      customer: "محمد حسن",
      customerEn: "Mohammad Hassan",
      address: "کارته چهار، کابل",
      addressEn: "Karte Char, Kabul",
      phone: "+93 70 123 4567",
      amount: 850,
      distance: "2.3 km",
      estimatedTime: "15 min",
      status: "available",
      items: ["قابلی پلاو", "مانتو", "چای"],
      paymentMethod: "cash",
      agentEarnings: 65,
      pickupLocation: { lat: 34.5553, lng: 69.2075 },
      deliveryLocation: { lat: 34.556, lng: 69.208 },
    },
    {
      id: "ORD002",
      restaurant: "هرات کباب",
      restaurantEn: "Herat Kebab",
      customer: "فاطمه احمد",
      customerEn: "Fatima Ahmad",
      address: "شهر نو، کابل",
      addressEn: "Shar-e-Naw, Kabul",
      phone: "+93 78 987 6543",
      amount: 1200,
      distance: "1.8 km",
      estimatedTime: "12 min",
      status: "available",
      items: ["کباب", "نان", "سالاد"],
      paymentMethod: "digital",
      agentEarnings: 85,
      pickupLocation: { lat: 34.5555, lng: 69.207 },
      deliveryLocation: { lat: 34.5565, lng: 69.2085 },
    },
  ];

  // Order handling functions
  const handleAcceptOrder = (order) => {
    setCurrentOrder(order);
    setOrderStatus("assigned");
    setAvailableOrders(availableOrders.filter((o) => o.id !== order.id));
    setShowOrderDetails(false);
    alert(`امر منل شو! (Order accepted!) - ${order.restaurant}`);
  };

  const handleDeclineOrder = (order) => {
    alert(`امر رد شو (Order declined) - ${order.restaurant}`);
    setShowOrderDetails(false);
  };

  const handleAcceptOrder = () => {
    setOrderStatus("accepted");
    alert("امر منل شو! (Order accepted!)");
  };

  const handleStartPickup = () => {
    setOrderStatus("en_route_to_restaurant");
    alert("د رستوران ته روان یاست (Heading to restaurant)");
  };

  const handleArrivedAtRestaurant = () => {
    setOrderStatus("arrived_at_restaurant");
    alert("رستوران ته ورسیدم (Arrived at restaurant)");
  };

  const handleOrderPickedUp = () => {
    setOrderStatus("picked_up");
    alert("امر واخیستل شو (Order picked up)");
  };

  const handleHeadToCustomer = () => {
    setOrderStatus("en_route_to_customer");
    alert("پیرودونکي ته روان یاست (Heading to customer)");
  };

  const handleDeliveryComplete = () => {
    if (currentOrder.paymentMethod === "cash") {
      setShowPaymentModal(true);
    } else {
      completeDelivery();
    }
  };

  const completeDelivery = () => {
    setCompletedDeliveries((prev) => prev + 1);
    setTodayEarnings((prev) => prev + currentOrder.agentEarnings);
    setCashCollected(
      (prev) =>
        prev + (currentOrder.paymentMethod === "cash" ? currentOrder.amount : 0)
    );
    setCurrentOrder(null);
    setOrderStatus("available");
    setShowPaymentModal(false);
    alert("ډیلیوری بشپړ شو! (Delivery completed!)");
  };

  const getOrderStatusText = () => {
    const statusTexts = {
      available: "د امرونو انتظار (Waiting for orders)",
      assigned: "امر ټاکل شوی (Order assigned)",
      accepted: "امر منل شوی (Order accepted)",
      en_route_to_restaurant: "رستوران ته روان (En route to restaurant)",
      arrived_at_restaurant: "رستوران ته ورسیدم (Arrived at restaurant)",
      picked_up: "امر واخیستل شو (Order picked up)",
      en_route_to_customer: "پیرودونکي ته روان (En route to customer)",
      delivered: "ورسول شو (Delivered)",
      completed: "بشپړ شو (Completed)",
    };
    return statusTexts[orderStatus] || statusTexts.available;
  };

  // Status bar component
  const StatusBar = () => (
    <div className='bg-gray-900 text-white px-4 py-2 flex justify-between items-center text-xs'>
      <div className='flex items-center space-x-2'>
        <span>09:30</span>
        {networkStatus === "online" ? (
          <Wifi size={12} />
        ) : (
          <WifiOff size={12} />
        )}
        <Signal size={12} />
      </div>
      <div className='flex items-center space-x-2'>
        <Battery size={12} />
        <span>{batteryLevel}%</span>
      </div>
    </div>
  );

  // Header component
  const Header = () => (
    <div className='bg-blue-600 text-white p-4'>
      <div className='flex justify-between items-center'>
        <div>
          <h1 className='text-lg font-bold'>{agentData.name}</h1>
          <p className='text-blue-100 text-sm'>{agentData.zone}</p>
        </div>
        <div className='flex items-center space-x-3'>
          <button
            onClick={() => setIsOnline(!isOnline)}
            className={`px-3 py-1 rounded-full text-xs font-medium ${
              isOnline ? "bg-green-500 text-white" : "bg-gray-300 text-gray-700"
            }`}
          >
            {isOnline ? "آنلاین (Online)" : "آفلاین (Offline)"}
          </button>
          <button className='p-2 rounded-full bg-blue-700'>
            <Menu size={16} />
          </button>
        </div>
      </div>
    </div>
  );

  // Dashboard view
  const DashboardView = () => (
    <div className='p-4 space-y-4'>
      {/* Today's Stats */}
      <div className='grid grid-cols-2 gap-4'>
        <div className='bg-green-50 p-4 rounded-lg border border-green-200'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-green-600 text-sm'>نن ورځ عاید</p>
              <p className='text-green-600 text-xs'>Today's Earnings</p>
              <p className='text-2xl font-bold text-green-700'>
                {todayEarnings} AFN
              </p>
            </div>
            <DollarSign className='text-green-500' size={24} />
          </div>
        </div>

        <div className='bg-blue-50 p-4 rounded-lg border border-blue-200'>
          <div className='flex items-center justify-between'>
            <div>
              <p className='text-blue-600 text-sm'>بشپړ شوي</p>
              <p className='text-blue-600 text-xs'>Completed</p>
              <p className='text-2xl font-bold text-blue-700'>
                {completedDeliveries}/{agentData.todayTarget}
              </p>
            </div>
            <Package className='text-blue-500' size={24} />
          </div>
        </div>
      </div>

      {/* Current Order */}
      {currentOrder && (
        <div className='bg-orange-50 border border-orange-200 rounded-lg p-4'>
          <div className='flex items-center justify-between mb-3'>
            <h3 className='font-semibold text-orange-800'>
              اوسنی امر (Current Order)
            </h3>
            <span className='bg-orange-500 text-white px-2 py-1 rounded text-xs'>
              فعال (Active)
            </span>
          </div>

          <div className='space-y-2 text-sm'>
            <div className='flex justify-between'>
              <span className='text-gray-600'>رستوران:</span>
              <span className='font-medium'>{currentOrder.restaurant}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-600'>پیرودونکی:</span>
              <span className='font-medium'>{currentOrder.customer}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-600'>واټن:</span>
              <span className='font-medium'>{currentOrder.distance}</span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-600'>تادیه:</span>
              <span className='font-medium'>
                {currentOrder.paymentMethod === "cash"
                  ? "نغدي (Cash)"
                  : "ډیجیټل (Digital)"}
              </span>
            </div>
            <div className='flex justify-between'>
              <span className='text-gray-600'>عاید:</span>
              <span className='font-medium text-green-600'>
                {currentOrder.agentEarnings} AFN
              </span>
            </div>
          </div>

          <div className='flex space-x-2 mt-4'>
            {orderStatus === "assigned" && (
              <button
                onClick={handleAcceptOrder}
                className='flex-1 bg-green-600 text-white py-2 rounded-lg text-sm font-medium'
              >
                ومنئ (Accept)
              </button>
            )}
            {orderStatus === "accepted" && (
              <button
                onClick={handleStartPickup}
                className='flex-1 bg-blue-600 text-white py-2 rounded-lg text-sm font-medium'
              >
                پیل کړئ (Start Pickup)
              </button>
            )}
            {orderStatus === "en_route_to_restaurant" && (
              <button
                onClick={handleArrivedAtRestaurant}
                className='flex-1 bg-orange-600 text-white py-2 rounded-lg text-sm font-medium'
              >
                رستوران ته ورسیدم (Arrived)
              </button>
            )}
            {orderStatus === "arrived_at_restaurant" && (
              <button
                onClick={handleOrderPickedUp}
                className='flex-1 bg-yellow-600 text-white py-2 rounded-lg text-sm font-medium'
              >
                واخیستل شو (Picked Up)
              </button>
            )}
            {orderStatus === "picked_up" && (
              <button
                onClick={handleHeadToCustomer}
                className='flex-1 bg-purple-600 text-white py-2 rounded-lg text-sm font-medium'
              >
                پیرودونکي ته (To Customer)
              </button>
            )}
            {orderStatus === "en_route_to_customer" && (
              <button
                onClick={handleDeliveryComplete}
                className='flex-1 bg-green-600 text-white py-2 rounded-lg text-sm font-medium'
              >
                ورسول شو (Delivered)
              </button>
            )}
            <button className='flex-1 bg-gray-600 text-white py-2 rounded-lg text-sm font-medium'>
              نقشه (Map)
            </button>
          </div>
        </div>
      )}

      {/* Available Orders */}
      <div className='bg-white border border-gray-200 rounded-lg'>
        <div className='p-4 border-b border-gray-200'>
          <h3 className='font-semibold text-gray-800'>
            شته امرونه (Available Orders)
          </h3>
        </div>

        {!currentOrder &&
          availableOrders.map((order) => (
            <div
              key={order.id}
              className='p-4 border-b border-gray-100 last:border-b-0'
            >
              <div className='flex justify-between items-start mb-2'>
                <div>
                  <p className='font-medium text-gray-900'>
                    {order.restaurant}
                  </p>
                  <p className='text-sm text-gray-600'>{order.customer}</p>
                  <p className='text-xs text-gray-500'>
                    {order.paymentMethod === "cash" ? "💵 نغدي" : "💳 ډیجیټل"}
                  </p>
                </div>
                <div className='text-right'>
                  <p className='font-bold text-green-600'>{order.amount} AFN</p>
                  <p className='text-xs text-orange-600'>
                    عاید: {order.agentEarnings} AFN
                  </p>
                  <p className='text-xs text-gray-500'>{order.distance}</p>
                </div>
              </div>

              <div className='flex items-center justify-between mb-3'>
                <div className='flex items-center space-x-2 text-xs text-gray-500'>
                  <Clock size={12} />
                  <span>{order.estimatedTime}</span>
                  <MapPin size={12} />
                  <span className='truncate max-w-24'>{order.addressEn}</span>
                </div>
              </div>

              <div className='flex space-x-2'>
                <button
                  onClick={() =>
                    setSelectedOrder(order) || setShowOrderDetails(true)
                  }
                  className='flex-1 bg-gray-100 text-gray-700 px-3 py-1 rounded text-sm'
                >
                  تفصیلات (Details)
                </button>
                <button
                  onClick={() => handleAcceptOrder(order)}
                  className='flex-1 bg-green-600 text-white px-3 py-1 rounded text-sm'
                >
                  ومنئ (Accept)
                </button>
                <button
                  onClick={() => handleDeclineOrder(order)}
                  className='bg-red-500 text-white px-3 py-1 rounded text-sm'
                >
                  رد (Decline)
                </button>
              </div>
            </div>
          ))}

        {!currentOrder && availableOrders.length === 0 && (
          <div className='p-8 text-center text-gray-500'>
            <Package size={48} className='mx-auto mb-4 text-gray-300' />
            <p className='text-lg font-medium'>د امرونو انتظار</p>
            <p className='text-sm'>Waiting for new orders...</p>
          </div>
        )}

        {currentOrder && (
          <div className='p-8 text-center text-gray-500'>
            <Truck size={48} className='mx-auto mb-4 text-blue-300' />
            <p className='text-lg font-medium'>تاسو اوس مصروف یاست</p>
            <p className='text-sm'>You are currently busy with an order</p>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className='grid grid-cols-2 gap-4'>
        <button className='bg-purple-600 text-white p-4 rounded-lg flex items-center justify-center space-x-2'>
          <Navigation size={20} />
          <span>نقشه (Map)</span>
        </button>

        <button className='bg-gray-600 text-white p-4 rounded-lg flex items-center justify-center space-x-2'>
          <MessageCircle size={20} />
          <span>ملاتړ (Support)</span>
        </button>
      </div>
    </div>
  );

  // Earnings view
  const EarningsView = () => (
    <div className='p-4 space-y-4'>
      <div className='bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-lg'>
        <h2 className='text-lg font-bold mb-2'>
          د دغه اونۍ عاید (This Week's Earnings)
        </h2>
        <p className='text-3xl font-bold'>12,450 AFN</p>
        <p className='text-green-100 text-sm'>
          +15% د تیرې اونۍ څخه (vs last week)
        </p>
      </div>

      <div className='grid grid-cols-2 gap-4'>
        <div className='bg-white p-4 rounded-lg border'>
          <p className='text-gray-600 text-sm'>نن ورځ</p>
          <p className='text-xl font-bold'>1,850 AFN</p>
          <p className='text-green-600 text-xs'>+12%</p>
        </div>

        <div className='bg-white p-4 rounded-lg border'>
          <p className='text-gray-600 text-sm'>دغه میاشت</p>
          <p className='text-xl font-bold'>45,200 AFN</p>
          <p className='text-green-600 text-xs'>+8%</p>
        </div>
      </div>

      <div className='bg-white border border-gray-200 rounded-lg'>
        <div className='p-4 border-b'>
          <h3 className='font-semibold'>د تادیاتو تاریخ (Payment History)</h3>
        </div>

        <div className='divide-y'>
          {[
            { date: "2024-01-15", amount: 8500, status: "paid" },
            { date: "2024-01-08", amount: 7200, status: "paid" },
            { date: "2024-01-01", amount: 9100, status: "pending" },
          ].map((payment, index) => (
            <div key={index} className='p-4 flex justify-between items-center'>
              <div>
                <p className='font-medium'>{payment.amount} AFN</p>
                <p className='text-sm text-gray-600'>{payment.date}</p>
              </div>
              <span
                className={`px-2 py-1 rounded text-xs ${
                  payment.status === "paid"
                    ? "bg-green-100 text-green-800"
                    : "bg-yellow-100 text-yellow-800"
                }`}
              >
                {payment.status === "paid" ? "ورکړل شوی" : "انتظار کې"}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Profile view
  const ProfileView = () => (
    <div className='p-4 space-y-4'>
      <div className='bg-white border border-gray-200 rounded-lg p-6 text-center'>
        <div className='w-20 h-20 bg-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center'>
          <User className='text-white' size={32} />
        </div>
        <h2 className='text-xl font-bold'>{agentData.name}</h2>
        <p className='text-gray-600'>{agentData.nameEn}</p>
        <p className='text-sm text-gray-500'>ID: {agentData.id}</p>

        <div className='flex items-center justify-center mt-4 space-x-4'>
          <div className='text-center'>
            <p className='text-2xl font-bold text-blue-600'>
              {agentData.rating}
            </p>
            <p className='text-xs text-gray-600'>ریټنګ</p>
          </div>
          <div className='text-center'>
            <p className='text-2xl font-bold text-green-600'>
              {agentData.totalDeliveries}
            </p>
            <p className='text-xs text-gray-600'>ډیلیوری</p>
          </div>
        </div>
      </div>

      <div className='space-y-3'>
        <button className='w-full bg-white border border-gray-200 rounded-lg p-4 flex items-center justify-between'>
          <div className='flex items-center space-x-3'>
            <Settings className='text-gray-600' size={20} />
            <span>تنظیمات (Settings)</span>
          </div>
          <span className='text-gray-400'>›</span>
        </button>

        <button className='w-full bg-white border border-gray-200 rounded-lg p-4 flex items-center justify-between'>
          <div className='flex items-center space-x-3'>
            <MessageCircle className='text-gray-600' size={20} />
            <span>ملاتړ (Support)</span>
          </div>
          <span className='text-gray-400'>›</span>
        </button>

        <button className='w-full bg-red-50 border border-red-200 rounded-lg p-4 flex items-center justify-between text-red-600'>
          <div className='flex items-center space-x-3'>
            <LogOut size={20} />
            <span>وتل (Logout)</span>
          </div>
          <span className='text-red-400'>›</span>
        </button>
      </div>
    </div>
  );

  // Bottom navigation
  const BottomNav = () => (
    <div className='bg-white border-t border-gray-200 px-4 py-2'>
      <div className='flex justify-around'>
        {[
          { id: "dashboard", icon: Home, label: "کور", labelEn: "Home" },
          {
            id: "earnings",
            icon: DollarSign,
            label: "عاید",
            labelEn: "Earnings",
          },
          { id: "profile", icon: User, label: "پروفایل", labelEn: "Profile" },
        ].map((item) => (
          <button
            key={item.id}
            onClick={() => setCurrentView(item.id)}
            className={`flex flex-col items-center py-2 px-3 rounded-lg ${
              currentView === item.id
                ? "text-blue-600 bg-blue-50"
                : "text-gray-600"
            }`}
          >
            <item.icon size={20} />
            <span className='text-xs mt-1'>{item.label}</span>
          </button>
        ))}
      </div>
    </div>
  );

  const renderCurrentView = () => {
    switch (currentView) {
      case "dashboard":
        return <DashboardView />;
      case "earnings":
        return <EarningsView />;
      case "profile":
        return <ProfileView />;
      default:
        return <DashboardView />;
    }
  };

  return (
    <div className='max-w-sm mx-auto bg-gray-50 min-h-screen flex flex-col'>
      <StatusBar />
      <Header />

      <div className='flex-1 overflow-y-auto'>{renderCurrentView()}</div>

      <BottomNav />

      {/* Payment Collection Modal */}
      {showPaymentModal && currentOrder && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-lg max-w-sm w-full p-6'>
            <h3 className='text-lg font-bold text-gray-900 mb-4'>
              د تادیاتو راټولول (Payment Collection)
            </h3>

            <div className='space-y-4'>
              <div className='bg-gray-50 p-4 rounded-lg'>
                <div className='flex justify-between items-center mb-2'>
                  <span className='text-gray-600'>د امر مجموعه:</span>
                  <span className='font-bold text-lg'>
                    {currentOrder.amount} AFN
                  </span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-gray-600'>ستاسو عاید:</span>
                  <span className='font-bold text-green-600'>
                    {currentOrder.agentEarnings} AFN
                  </span>
                </div>
              </div>

              <div className='space-y-3'>
                <label className='block text-sm font-medium text-gray-700'>
                  د پیرودونکي څخه راټول شوی مقدار:
                </label>
                <input
                  type='number'
                  value={currentOrder.amount}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                  readOnly
                />

                <div className='flex items-center space-x-2'>
                  <input
                    type='checkbox'
                    id='confirm-payment'
                    className='rounded'
                  />
                  <label
                    htmlFor='confirm-payment'
                    className='text-sm text-gray-700'
                  >
                    زه تصدیق کوم چې سمه پیسې راټولې کړې (I confirm correct
                    amount collected)
                  </label>
                </div>
              </div>

              <div className='flex space-x-3 pt-4'>
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className='flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50'
                >
                  لغو (Cancel)
                </button>
                <button
                  onClick={completeDelivery}
                  className='flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700'
                >
                  بشپړ کړئ (Complete)
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Order Details Modal */}
      {showOrderDetails && selectedOrder && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-lg max-w-sm w-full p-6'>
            <div className='flex items-center justify-between mb-4'>
              <h3 className='text-lg font-bold text-gray-900'>
                د امر تفصیلات (Order Details)
              </h3>
              <button
                onClick={() => setShowOrderDetails(false)}
                className='text-gray-400 hover:text-gray-600'
              >
                ✕
              </button>
            </div>

            <div className='space-y-4'>
              <div>
                <h4 className='font-semibold text-gray-800 mb-2'>
                  رستوران (Restaurant)
                </h4>
                <p className='text-gray-600'>{selectedOrder.restaurant}</p>
                <p className='text-sm text-gray-500'>
                  {selectedOrder.restaurantEn}
                </p>
              </div>

              <div>
                <h4 className='font-semibold text-gray-800 mb-2'>
                  پیرودونکی (Customer)
                </h4>
                <p className='text-gray-600'>{selectedOrder.customer}</p>
                <p className='text-sm text-gray-500'>{selectedOrder.phone}</p>
              </div>

              <div>
                <h4 className='font-semibold text-gray-800 mb-2'>
                  د رسولو پته (Delivery Address)
                </h4>
                <p className='text-gray-600'>{selectedOrder.address}</p>
                <p className='text-sm text-gray-500'>
                  {selectedOrder.addressEn}
                </p>
              </div>

              <div>
                <h4 className='font-semibold text-gray-800 mb-2'>
                  د امر توکي (Order Items)
                </h4>
                <ul className='space-y-1'>
                  {selectedOrder.items.map((item, index) => (
                    <li key={index} className='text-gray-600 text-sm'>
                      • {item}
                    </li>
                  ))}
                </ul>
              </div>

              <div className='bg-gray-50 p-3 rounded-lg'>
                <div className='flex justify-between items-center mb-1'>
                  <span className='text-gray-600'>مجموعه:</span>
                  <span className='font-bold'>{selectedOrder.amount} AFN</span>
                </div>
                <div className='flex justify-between items-center mb-1'>
                  <span className='text-gray-600'>واټن:</span>
                  <span className='text-gray-600'>
                    {selectedOrder.distance}
                  </span>
                </div>
                <div className='flex justify-between items-center mb-1'>
                  <span className='text-gray-600'>وخت:</span>
                  <span className='text-gray-600'>
                    {selectedOrder.estimatedTime}
                  </span>
                </div>
                <div className='flex justify-between items-center'>
                  <span className='text-gray-600'>ستاسو عاید:</span>
                  <span className='font-bold text-green-600'>
                    {selectedOrder.agentEarnings} AFN
                  </span>
                </div>
              </div>

              <div className='flex space-x-3 pt-4'>
                <button
                  onClick={() => handleDeclineOrder(selectedOrder)}
                  className='flex-1 px-4 py-2 border border-red-300 text-red-600 rounded-lg hover:bg-red-50'
                >
                  رد (Decline)
                </button>
                <button
                  onClick={() => handleAcceptOrder(selectedOrder)}
                  className='flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700'
                >
                  ومنئ (Accept)
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AgentMobileApp;
