import React, { useState, useRef, useEffect } from 'react';
import { Search, X, Clock, TrendingUp, Mic, MicOff } from 'lucide-react';
import { useSearch } from '../../context/SearchContext';
import { useNavigate } from 'react-router-dom';

const SearchBox = ({ placeholder = "Search restaurants, dishes, cuisines...", className = "" }) => {
  const navigate = useNavigate();
  const {
    searchQuery,
    setSearchQuery,
    searchHistory,
    popularSearches,
    showSuggestions,
    setShowSuggestions,
    addToSearchHistory,
    removeFromHistory,
    getSuggestions
  } = useSearch();

  const [localQuery, setLocalQuery] = useState(searchQuery);
  const [isListening, setIsListening] = useState(false);
  const [recognition, setRecognition] = useState(null);
  const inputRef = useRef(null);
  const suggestionsRef = useRef(null);

  // Initialize speech recognition
  useEffect(() => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;
      recognitionInstance.lang = 'en-US';

      recognitionInstance.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setLocalQuery(transcript);
        setSearchQuery(transcript);
        setIsListening(false);
      };

      recognitionInstance.onerror = () => {
        setIsListening(false);
      };

      recognitionInstance.onend = () => {
        setIsListening(false);
      };

      setRecognition(recognitionInstance);
    }
  }, [setSearchQuery]);

  // Handle click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target) &&
        !inputRef.current.contains(event.target)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [setShowSuggestions]);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setLocalQuery(value);
    setSearchQuery(value);
    setShowSuggestions(true);
  };

  const handleInputFocus = () => {
    setShowSuggestions(true);
  };

  const handleSearch = (query = localQuery) => {
    if (!query.trim()) return;

    addToSearchHistory(query.trim());
    setShowSuggestions(false);
    
    // Navigate to search results page
    navigate(`/search?q=${encodeURIComponent(query.trim())}`);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    setLocalQuery(suggestion.text);
    setSearchQuery(suggestion.text);
    handleSearch(suggestion.text);
  };

  const handleHistoryClick = (query) => {
    setLocalQuery(query);
    setSearchQuery(query);
    handleSearch(query);
  };

  const handleVoiceSearch = () => {
    if (!recognition) {
      alert('Voice search is not supported in your browser');
      return;
    }

    if (isListening) {
      recognition.stop();
      setIsListening(false);
    } else {
      recognition.start();
      setIsListening(true);
    }
  };

  const clearSearch = () => {
    setLocalQuery('');
    setSearchQuery('');
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const suggestions = getSuggestions(localQuery);
  const showHistory = showSuggestions && !localQuery.trim() && searchHistory.length > 0;
  const showPopular = showSuggestions && !localQuery.trim() && searchHistory.length === 0;
  const showAutoComplete = showSuggestions && localQuery.trim() && suggestions.length > 0;

  return (
    <div className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={localQuery}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="block w-full pl-10 pr-20 py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 space-x-2">
          {/* Voice Search Button */}
          {recognition && (
            <button
              onClick={handleVoiceSearch}
              className={`p-1 rounded-full transition-colors ${
                isListening 
                  ? 'text-red-500 bg-red-50' 
                  : 'text-gray-400 hover:text-gray-600'
              }`}
              title="Voice search"
            >
              {isListening ? <MicOff size={18} /> : <Mic size={18} />}
            </button>
          )}
          
          {/* Clear Button */}
          {localQuery && (
            <button
              onClick={clearSearch}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="Clear search"
            >
              <X size={18} />
            </button>
          )}
        </div>
      </div>

      {/* Suggestions Dropdown */}
      {(showHistory || showPopular || showAutoComplete) && (
        <div
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto"
        >
          {/* Search History */}
          {showHistory && (
            <div className="p-3 border-b border-gray-100">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-700">Recent Searches</h3>
                <button
                  onClick={() => removeFromHistory}
                  className="text-xs text-gray-500 hover:text-gray-700"
                >
                  Clear
                </button>
              </div>
              <div className="space-y-1">
                {searchHistory.slice(0, 5).map((query, index) => (
                  <button
                    key={index}
                    onClick={() => handleHistoryClick(query)}
                    className="flex items-center w-full px-2 py-1 text-sm text-gray-700 hover:bg-gray-50 rounded"
                  >
                    <Clock size={14} className="mr-2 text-gray-400" />
                    <span>{query}</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        removeFromHistory(query);
                      }}
                      className="ml-auto p-1 text-gray-400 hover:text-gray-600"
                    >
                      <X size={12} />
                    </button>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Popular Searches */}
          {showPopular && (
            <div className="p-3">
              <div className="flex items-center mb-2">
                <TrendingUp size={14} className="mr-1 text-gray-400" />
                <h3 className="text-sm font-medium text-gray-700">Popular Searches</h3>
              </div>
              <div className="space-y-1">
                {popularSearches.slice(0, 6).map((query, index) => (
                  <button
                    key={index}
                    onClick={() => handleHistoryClick(query)}
                    className="flex items-center w-full px-2 py-1 text-sm text-gray-700 hover:bg-gray-50 rounded"
                  >
                    <span>{query}</span>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Autocomplete Suggestions */}
          {showAutoComplete && (
            <div className="p-1">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="flex items-center w-full px-3 py-2 text-sm hover:bg-gray-50 rounded"
                >
                  <div className="flex-1 text-left">
                    <div className="font-medium text-gray-900">{suggestion.text}</div>
                    <div className="text-xs text-gray-500">{suggestion.subtitle}</div>
                  </div>
                  <div className="text-xs text-gray-400 capitalize">
                    {suggestion.type}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchBox;
