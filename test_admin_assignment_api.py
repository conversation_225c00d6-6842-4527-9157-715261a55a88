import requests
import json

def test_admin_assignment_api():
    """Test the admin assignment API directly"""
    
    print("🔍 Testing Admin Assignment API")
    print("=" * 50)
    
    # Login as admin first
    login_url = "http://127.0.0.1:8000/api/auth/login/"
    login_data = {
        "user_name": "admin",
        "password": "admin123"
    }
    
    print("1. Logging in as admin...")
    login_response = requests.post(login_url, json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Admin login failed: {login_response.status_code}")
        print(f"Response: {login_response.text}")
        return
    
    login_result = login_response.json()
    if not login_result.get('success'):
        print(f"❌ Admin login failed: {login_result}")
        return
    
    token = login_result['data']['access_token']
    print("✅ Admin login successful")
    
    # Test the admin assignment API
    assignment_url = "http://127.0.0.1:8000/api/delivery-agent/admin/assignments/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print("\n2. Testing admin assignment API...")
    assignment_response = requests.get(assignment_url, headers=headers)
    
    print(f"Status Code: {assignment_response.status_code}")
    
    if assignment_response.status_code == 200:
        data = assignment_response.json()
        print("✅ API working")
        
        if data.get('status') == 'success':
            assignment_data = data.get('data', {})
            ready_orders = assignment_data.get('ready_orders', [])
            available_agents = assignment_data.get('available_agents', [])
            
            print(f"📦 Ready orders found: {len(ready_orders)}")
            print(f"👥 Available agents: {len(available_agents)}")
            
            # Look for Order #65
            order_65_found = False
            for order in ready_orders:
                if order.get('id') == 65:
                    order_65_found = True
                    print(f"\n✅ Order #65 FOUND in API response!")
                    print(f"   Customer: {order.get('customer_name')}")
                    print(f"   Restaurant: {order.get('restaurant_name')}")
                    print(f"   Amount: ${order.get('total_amount')}")
                    print(f"   Created: {order.get('created_at')}")
                    break
            
            if not order_65_found:
                print(f"\n❌ Order #65 NOT found in API response")
                print(f"Orders found:")
                for order in ready_orders[:5]:  # Show first 5
                    print(f"   Order #{order.get('id')}: {order.get('customer_name')} - ${order.get('total_amount')}")
        else:
            print(f"❌ API returned error: {data}")
    else:
        print(f"❌ API failed: {assignment_response.status_code}")
        print(f"Response: {assignment_response.text}")
    
    # Test the regular orders API that the frontend uses
    print("\n3. Testing regular orders API...")
    orders_url = "http://127.0.0.1:8000/api/order/orders/?status=ready"
    orders_response = requests.get(orders_url, headers=headers)
    
    print(f"Orders API Status Code: {orders_response.status_code}")
    
    if orders_response.status_code == 200:
        orders_data = orders_response.json()
        print(f"✅ Orders API working - Found {len(orders_data)} orders")
        
        # Look for Order #65
        order_65_found = False
        for order in orders_data:
            if order.get('id') == 65:
                order_65_found = True
                print(f"\n✅ Order #65 FOUND in orders API!")
                print(f"   Status: {order.get('status')}")
                print(f"   Delivery Agent: {order.get('delivery_agent')}")
                break
        
        if not order_65_found:
            print(f"\n❌ Order #65 NOT found in orders API")
            print(f"Sample orders:")
            for order in orders_data[:3]:
                print(f"   Order #{order.get('id')}: {order.get('status')} - Agent: {order.get('delivery_agent')}")

if __name__ == "__main__":
    test_admin_assignment_api()
