// Frontend/src/services/filterService.js
import { API_BASE_URL } from '../config/api';
import configService from './configService';

class FilterService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = new Map();
    this.CACHE_DURATION = 10 * 60 * 1000; // 10 minutes
  }

  // Cache management
  isCacheValid(key) {
    const expiry = this.cacheExpiry.get(key);
    return expiry && Date.now() < expiry;
  }

  setCache(key, data) {
    this.cache.set(key, data);
    this.cacheExpiry.set(key, Date.now() + this.CACHE_DURATION);
  }

  getCache(key) {
    if (this.isCacheValid(key)) {
      return this.cache.get(key);
    }
    this.cache.delete(key);
    this.cacheExpiry.delete(key);
    return null;
  }

  // API calls with authentication
  async fetchWithAuth(url, options = {}) {
    const token = localStorage.getItem('token');
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await fetch(`${API_BASE_URL}${url}`, {
        ...options,
        headers,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`Error fetching ${url}:`, error);
      throw error;
    }
  }

  // Get all restaurants for extracting filter options
  async getRestaurants() {
    const cacheKey = 'restaurants_for_filters';
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const restaurants = await this.fetchWithAuth('/restaurant/restaurants/');
      this.setCache(cacheKey, restaurants);
      return restaurants;
    } catch (error) {
      console.error('Error fetching restaurants:', error);
      return [];
    }
  }

  // Extract unique cuisines from restaurants
  async getCuisineOptions() {
    const cacheKey = 'cuisine_options';
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const restaurants = await this.getRestaurants();
      const cuisines = new Set();
      
      restaurants.forEach(restaurant => {
        if (restaurant.cuisine_type) {
          cuisines.add(restaurant.cuisine_type);
        }
        // Also check if there are tags or categories
        if (restaurant.tags) {
          restaurant.tags.forEach(tag => cuisines.add(tag));
        }
      });

      const cuisineOptions = Array.from(cuisines).map(cuisine => ({
        id: cuisine.toLowerCase().replace(/\s+/g, '-'),
        value: cuisine,
        label: cuisine,
        icon: this.getCuisineIcon(cuisine)
      }));

      this.setCache(cacheKey, cuisineOptions);
      return cuisineOptions;
    } catch (error) {
      console.error('Error extracting cuisine options:', error);
      return this.getFallbackCuisines();
    }
  }

  // Get cuisine icon based on cuisine type
  getCuisineIcon(cuisine) {
    const iconMap = {
      'afghan': '🇦🇫',
      'italian': '🍝',
      'chinese': '🥢',
      'indian': '🍛',
      'mexican': '🌮',
      'japanese': '🍣',
      'thai': '🍜',
      'american': '🍔',
      'mediterranean': '🫒',
      'french': '🥐',
      'korean': '🥘',
      'vietnamese': '🍲',
      'middle eastern': '🧆',
      'pizza': '🍕',
      'seafood': '🦐',
      'vegetarian': '🥗',
      'fast food': '🍟',
      'dessert': '🍰',
      'coffee': '☕',
      'bakery': '🥖'
    };

    const key = cuisine.toLowerCase();
    return iconMap[key] || '🍽️';
  }

  // Extract price ranges from restaurants
  async getPriceRangeOptions() {
    const cacheKey = 'price_range_options';
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const restaurants = await this.getRestaurants();
      const priceRanges = new Set();
      
      restaurants.forEach(restaurant => {
        if (restaurant.price_range) {
          priceRanges.add(restaurant.price_range);
        }
      });

      const priceOptions = [
        { value: 1, label: '$', description: 'Budget-friendly' },
        { value: 2, label: '$$', description: 'Moderate' },
        { value: 3, label: '$$$', description: 'Expensive' },
        { value: 4, label: '$$$$', description: 'Very Expensive' }
      ];

      this.setCache(cacheKey, priceOptions);
      return priceOptions;
    } catch (error) {
      console.error('Error extracting price range options:', error);
      return [
        { value: 1, label: '$', description: 'Budget-friendly' },
        { value: 2, label: '$$', description: 'Moderate' }
      ];
    }
  }

  // Get delivery time options based on system settings
  async getDeliveryTimeOptions() {
    const cacheKey = 'delivery_time_options';
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const estimatedTime = await configService.getEstimatedDeliveryTime();
      const baseTime = estimatedTime || 45;

      const timeOptions = [
        { value: Math.floor(baseTime * 0.6), label: `Under ${Math.floor(baseTime * 0.6)} min`, icon: '⚡' },
        { value: baseTime, label: `${Math.floor(baseTime * 0.6)}-${baseTime} min`, icon: '🚚' },
        { value: Math.floor(baseTime * 1.3), label: `${baseTime}-${Math.floor(baseTime * 1.3)} min`, icon: '🕐' },
        { value: 999, label: `Over ${Math.floor(baseTime * 1.3)} min`, icon: '🕑' }
      ];

      this.setCache(cacheKey, timeOptions);
      return timeOptions;
    } catch (error) {
      console.error('Error creating delivery time options:', error);
      return [
        { value: 30, label: 'Under 30 min', icon: '⚡' },
        { value: 45, label: '30-45 min', icon: '🚚' },
        { value: 60, label: '45-60 min', icon: '🕐' },
        { value: 999, label: 'Over 60 min', icon: '🕑' }
      ];
    }
  }

  // Get rating options
  async getRatingOptions() {
    return [
      { value: 4.5, label: '4.5+ stars', icon: '⭐⭐⭐⭐⭐' },
      { value: 4.0, label: '4.0+ stars', icon: '⭐⭐⭐⭐' },
      { value: 3.5, label: '3.5+ stars', icon: '⭐⭐⭐' },
      { value: 3.0, label: '3.0+ stars', icon: '⭐⭐' }
    ];
  }

  // Get distance options based on system settings
  async getDistanceOptions() {
    const cacheKey = 'distance_options';
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const maxDistance = await configService.getSystemSetting('max_delivery_distance') || 15;

      const distanceOptions = [
        { value: Math.floor(maxDistance * 0.2), label: `Under ${Math.floor(maxDistance * 0.2)} km`, icon: '📍' },
        { value: Math.floor(maxDistance * 0.4), label: `Under ${Math.floor(maxDistance * 0.4)} km`, icon: '🚶' },
        { value: Math.floor(maxDistance * 0.7), label: `Under ${Math.floor(maxDistance * 0.7)} km`, icon: '🚲' },
        { value: maxDistance, label: `Under ${maxDistance} km`, icon: '🚗' }
      ];

      this.setCache(cacheKey, distanceOptions);
      return distanceOptions;
    } catch (error) {
      console.error('Error creating distance options:', error);
      return [
        { value: 2, label: 'Under 2 km', icon: '📍' },
        { value: 5, label: 'Under 5 km', icon: '🚶' },
        { value: 10, label: 'Under 10 km', icon: '🚲' },
        { value: 15, label: 'Under 15 km', icon: '🚗' }
      ];
    }
  }

  // Get feature options based on system settings
  async getFeatureOptions() {
    const cacheKey = 'feature_options';
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    try {
      const freeDeliveryThreshold = await configService.getFreeDeliveryThreshold();

      const features = [
        { 
          id: 'free-delivery', 
          label: 'Free Delivery', 
          icon: '🚚',
          description: freeDeliveryThreshold ? `On orders over $${freeDeliveryThreshold}` : 'Available'
        },
        { id: 'fast-delivery', label: 'Fast Delivery', icon: '⚡', description: 'Under 30 minutes' },
        { id: 'new', label: 'New Restaurant', icon: '🆕', description: 'Recently opened' },
        { id: 'popular', label: 'Popular', icon: '🔥', description: 'Trending now' },
        { id: 'highly-rated', label: 'Highly Rated', icon: '⭐', description: '4.5+ stars' },
        { id: 'promoted', label: 'Promoted', icon: '📢', description: 'Featured restaurants' }
      ];

      this.setCache(cacheKey, features);
      return features;
    } catch (error) {
      console.error('Error creating feature options:', error);
      return [
        { id: 'free-delivery', label: 'Free Delivery', icon: '🚚' },
        { id: 'fast-delivery', label: 'Fast Delivery', icon: '⚡' },
        { id: 'popular', label: 'Popular', icon: '🔥' }
      ];
    }
  }

  // Get sort options
  async getSortOptions() {
    return [
      { value: 'relevance', label: 'Relevance', icon: '🎯' },
      { value: 'rating', label: 'Rating', icon: '⭐' },
      { value: 'delivery-time', label: 'Delivery Time', icon: '⚡' },
      { value: 'price-low', label: 'Price: Low to High', icon: '💰' },
      { value: 'price-high', label: 'Price: High to Low', icon: '💎' },
      { value: 'distance', label: 'Distance', icon: '📍' },
      { value: 'popularity', label: 'Popularity', icon: '🔥' },
      { value: 'newest', label: 'Newest First', icon: '🆕' }
    ];
  }

  // Get all filter options at once
  async getAllFilterOptions() {
    try {
      const [
        cuisines,
        dietaryRestrictions,
        priceRanges,
        deliveryTimes,
        ratings,
        distances,
        features,
        sortOptions
      ] = await Promise.all([
        this.getCuisineOptions(),
        configService.getDietaryRestrictionsForFilters(),
        this.getPriceRangeOptions(),
        this.getDeliveryTimeOptions(),
        this.getRatingOptions(),
        this.getDistanceOptions(),
        this.getFeatureOptions(),
        this.getSortOptions()
      ]);

      return {
        cuisines,
        dietaryRestrictions,
        priceRanges,
        deliveryTimes,
        ratings,
        distances,
        features,
        sortOptions
      };
    } catch (error) {
      console.error('Error loading all filter options:', error);
      return this.getFallbackFilterOptions();
    }
  }

  // Fallback options if API fails
  getFallbackCuisines() {
    return [
      { id: 'afghan', value: 'Afghan', label: 'Afghan', icon: '🇦🇫' },
      { id: 'italian', value: 'Italian', label: 'Italian', icon: '🍝' },
      { id: 'chinese', value: 'Chinese', label: 'Chinese', icon: '🥢' },
      { id: 'american', value: 'American', label: 'American', icon: '🍔' }
    ];
  }

  getFallbackFilterOptions() {
    return {
      cuisines: this.getFallbackCuisines(),
      dietaryRestrictions: [
        { id: 'vegetarian', label: 'Vegetarian', icon: '🥬' },
        { id: 'halal', label: 'Halal', icon: '☪️' }
      ],
      priceRanges: [
        { value: 1, label: '$', description: 'Budget-friendly' },
        { value: 2, label: '$$', description: 'Moderate' }
      ],
      deliveryTimes: [
        { value: 30, label: 'Under 30 min', icon: '⚡' },
        { value: 45, label: '30-45 min', icon: '🚚' }
      ],
      ratings: [
        { value: 4.0, label: '4.0+ stars', icon: '⭐⭐⭐⭐' },
        { value: 3.0, label: '3.0+ stars', icon: '⭐⭐⭐' }
      ],
      distances: [
        { value: 5, label: 'Under 5 km', icon: '🚶' },
        { value: 10, label: 'Under 10 km', icon: '🚲' }
      ],
      features: [
        { id: 'free-delivery', label: 'Free Delivery', icon: '🚚' },
        { id: 'popular', label: 'Popular', icon: '🔥' }
      ],
      sortOptions: [
        { value: 'relevance', label: 'Relevance', icon: '🎯' },
        { value: 'rating', label: 'Rating', icon: '⭐' }
      ]
    };
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    this.cacheExpiry.clear();
  }
}

// Create singleton instance
const filterService = new FilterService();

export default filterService;
