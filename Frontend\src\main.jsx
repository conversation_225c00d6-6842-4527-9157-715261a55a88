import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import App from "./App.jsx";
import "./index.css";
import { AuthProvider } from "./context/AuthContext";
import { CartProvider } from "./context/CartContext";
import { RestaurantProvider } from "./context/RestaurantContext";
import { NotificationProvider } from "./context/NotificationContext";
import { LoyaltyProvider } from "./context/LoyaltyContext";
import { MenuProvider } from "./context/MenuContext";
import { OrderProvider } from "./context/OrderContext";
import { MapProvider } from "./context/MapContext";
import { FavoritesProvider } from "./context/FavoritesContext";
import { SearchProvider } from "./context/SearchContext";
import { SchedulingProvider } from "./context/SchedulingContext";
import { SupportProvider } from "./context/SupportContext";
import { FiltersProvider } from "./context/FiltersContext";
import { SocialProvider } from "./context/SocialContext";
import { ToastProvider } from "./context/ToastContext";
// import { register as registerSW } from "./utils/serviceWorker";

createRoot(document.getElementById("root")).render(
  // <StrictMode> // ⚠️ TEMPORARILY DISABLED - Causing infinite loops in development
  <BrowserRouter
    future={{
      v7_startTransition: true,
      v7_relativeSplatPath: true,
    }}
  >
    <AuthProvider>
      <CartProvider>
        <RestaurantProvider>
          <MenuProvider>
            <OrderProvider>
              <LoyaltyProvider>
                <FavoritesProvider>
                  <SearchProvider>
                    <SchedulingProvider>
                      <SupportProvider>
                        <FiltersProvider>
                          <SocialProvider>
                            <ToastProvider>
                              <NotificationProvider>
                                <MapProvider>
                                  <App />
                                </MapProvider>
                              </NotificationProvider>
                            </ToastProvider>
                          </SocialProvider>
                        </FiltersProvider>
                      </SupportProvider>
                    </SchedulingProvider>
                  </SearchProvider>
                </FavoritesProvider>
              </LoyaltyProvider>
            </OrderProvider>
          </MenuProvider>
        </RestaurantProvider>
      </CartProvider>
    </AuthProvider>
  </BrowserRouter>
  // </StrictMode>
);

// Register service worker for PWA functionality - TEMPORARILY DISABLED
// registerSW({
//   onSuccess: (registration) => {
//     console.log("SW registered: ", registration);
//   },
//   onUpdate: (registration) => {
//     console.log("SW updated: ", registration);
//     // You can show a notification to user about the update
//     if (confirm("New version available! Reload to update?")) {
//       window.location.reload();
//     }
//   },
// });
