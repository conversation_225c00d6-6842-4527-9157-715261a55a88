# financial_management/views.py
from django.shortcuts import get_object_or_404
from django.db.models import Sum, Avg, Count, Q
from django.utils import timezone
from datetime import timedelta, datetime
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from decimal import Decimal

from .models import (
    CommissionStructure, RestaurantEarnings, RestaurantPayout,
    RestaurantBankAccount, FinancialReport
)
from .serializers import (
    CommissionStructureSerializer, RestaurantEarningsSerializer,
    RestaurantPayoutSerializer, RestaurantBankAccountSerializer,
    FinancialReportSerializer, EarningsSummarySerializer,
    PayoutRequestSerializer, FinancialDashboardSerializer
)
from restaurant.models import Restaurant
from orders.models import Order


class RestaurantFinancialViewSet(viewsets.ViewSet):
    """Financial management for restaurants"""
    permission_classes = [IsAuthenticated]

    def get_restaurant(self):
        """Get restaurant for current user"""
        if self.request.user.role != 'restaurant':
            return None
        return get_object_or_404(Restaurant, owner=self.request.user)

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """Get financial dashboard data"""
        restaurant = self.get_restaurant()
        if not restaurant:
            return Response({'error': 'Restaurant not found'}, status=404)

        # Get current period (last 30 days)
        end_date = timezone.now()
        start_date = end_date - timedelta(days=30)
        
        # Previous period for comparison
        prev_end_date = start_date
        prev_start_date = prev_end_date - timedelta(days=30)

        # Current period metrics
        current_earnings = RestaurantEarnings.objects.filter(
            restaurant=restaurant,
            created_at__range=[start_date, end_date]
        )
        
        current_revenue = current_earnings.aggregate(
            total=Sum('order_total')
        )['total'] or Decimal('0')
        
        current_orders = current_earnings.count()
        current_commission = current_earnings.aggregate(
            total=Sum('commission_amount')
        )['total'] or Decimal('0')
        
        current_net_earnings = current_earnings.aggregate(
            total=Sum('net_earnings')
        )['total'] or Decimal('0')

        # Previous period metrics for comparison
        prev_earnings = RestaurantEarnings.objects.filter(
            restaurant=restaurant,
            created_at__range=[prev_start_date, prev_end_date]
        )
        
        prev_revenue = prev_earnings.aggregate(
            total=Sum('order_total')
        )['total'] or Decimal('0')
        
        prev_orders = prev_earnings.count()

        # Calculate growth
        revenue_growth = Decimal('0')
        if prev_revenue > 0:
            revenue_growth = ((current_revenue - prev_revenue) / prev_revenue) * 100

        orders_growth = Decimal('0')
        if prev_orders > 0:
            orders_growth = ((current_orders - prev_orders) / prev_orders) * 100

        # Payout information
        pending_earnings = RestaurantEarnings.objects.filter(
            restaurant=restaurant,
            is_paid=False
        ).aggregate(total=Sum('net_earnings'))['total'] or Decimal('0')

        # Get commission structure for next payout date
        commission_structure = getattr(restaurant, 'commission_structure', None)
        next_payout_date = None
        if commission_structure:
            # Calculate next payout date based on frequency
            if commission_structure.payout_frequency == 'weekly':
                next_payout_date = end_date + timedelta(days=7)
            elif commission_structure.payout_frequency == 'monthly':
                next_payout_date = end_date + timedelta(days=30)

        # Last payout
        last_payout = RestaurantPayout.objects.filter(
            restaurant=restaurant,
            status='completed'
        ).first()

        # Revenue chart data (last 7 days)
        revenue_chart_data = []
        for i in range(7):
            day = end_date - timedelta(days=i)
            day_revenue = RestaurantEarnings.objects.filter(
                restaurant=restaurant,
                created_at__date=day.date()
            ).aggregate(total=Sum('order_total'))['total'] or Decimal('0')
            
            revenue_chart_data.append({
                'date': day.strftime('%Y-%m-%d'),
                'revenue': float(day_revenue)
            })

        # Orders chart data (last 7 days)
        orders_chart_data = []
        for i in range(7):
            day = end_date - timedelta(days=i)
            day_orders = RestaurantEarnings.objects.filter(
                restaurant=restaurant,
                created_at__date=day.date()
            ).count()
            
            orders_chart_data.append({
                'date': day.strftime('%Y-%m-%d'),
                'orders': day_orders
            })

        # Commission breakdown
        commission_breakdown = {
            'platform_commission': float(current_commission),
            'payment_processing': float(current_earnings.aggregate(
                total=Sum('payment_processing_fee')
            )['total'] or Decimal('0')),
            'delivery_fee_share': float(current_earnings.aggregate(
                total=Sum('delivery_fee_platform_share')
            )['total'] or Decimal('0'))
        }

        dashboard_data = {
            'current_period_revenue': current_revenue,
            'current_period_orders': current_orders,
            'current_period_commission': current_commission,
            'current_period_net_earnings': current_net_earnings,
            'revenue_growth': revenue_growth,
            'orders_growth': orders_growth,
            'pending_payout_amount': pending_earnings,
            'next_payout_date': next_payout_date,
            'last_payout_amount': last_payout.payout_amount if last_payout else None,
            'last_payout_date': last_payout.completed_at if last_payout else None,
            'revenue_chart_data': revenue_chart_data,
            'orders_chart_data': orders_chart_data,
            'commission_breakdown': commission_breakdown
        }

        serializer = FinancialDashboardSerializer(dashboard_data)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def earnings(self, request):
        """Get restaurant earnings"""
        restaurant = self.get_restaurant()
        if not restaurant:
            return Response({'error': 'Restaurant not found'}, status=404)

        earnings = RestaurantEarnings.objects.filter(restaurant=restaurant)
        
        # Filter by date range if provided
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        
        if start_date:
            earnings = earnings.filter(created_at__gte=start_date)
        if end_date:
            earnings = earnings.filter(created_at__lte=end_date)

        # Filter by payment status
        is_paid = request.query_params.get('is_paid')
        if is_paid is not None:
            earnings = earnings.filter(is_paid=is_paid.lower() == 'true')

        serializer = RestaurantEarningsSerializer(earnings, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def payouts(self, request):
        """Get restaurant payouts"""
        restaurant = self.get_restaurant()
        if not restaurant:
            return Response({'error': 'Restaurant not found'}, status=404)

        payouts = RestaurantPayout.objects.filter(restaurant=restaurant)
        serializer = RestaurantPayoutSerializer(payouts, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def request_payout(self, request):
        """Request a payout"""
        restaurant = self.get_restaurant()
        if not restaurant:
            return Response({'error': 'Restaurant not found'}, status=404)

        serializer = PayoutRequestSerializer(data=request.data)
        if serializer.is_valid():
            # Check if restaurant has enough pending earnings
            pending_earnings = RestaurantEarnings.objects.filter(
                restaurant=restaurant,
                is_paid=False
            ).aggregate(total=Sum('net_earnings'))['total'] or Decimal('0')

            requested_amount = serializer.validated_data['amount']
            
            if requested_amount > pending_earnings:
                return Response({
                    'error': 'Requested amount exceeds pending earnings',
                    'pending_earnings': pending_earnings
                }, status=400)

            # Check minimum payout amount
            commission_structure = getattr(restaurant, 'commission_structure', None)
            if commission_structure and requested_amount < commission_structure.minimum_payout_amount:
                return Response({
                    'error': f'Minimum payout amount is ${commission_structure.minimum_payout_amount}',
                    'minimum_amount': commission_structure.minimum_payout_amount
                }, status=400)

            # Create payout request
            payout = RestaurantPayout.objects.create(
                restaurant=restaurant,
                payout_amount=requested_amount,
                payout_period_start=timezone.now() - timedelta(days=30),
                payout_period_end=timezone.now(),
                payment_method=serializer.validated_data['payment_method'],
                status='pending'
            )

            return Response({
                'message': 'Payout request submitted successfully',
                'payout_id': payout.id,
                'amount': payout.payout_amount,
                'status': payout.status
            })

        return Response(serializer.errors, status=400)

    @action(detail=False, methods=['get', 'post', 'put'])
    def bank_account(self, request):
        """Manage restaurant bank account"""
        restaurant = self.get_restaurant()
        if not restaurant:
            return Response({'error': 'Restaurant not found'}, status=404)

        if request.method == 'GET':
            try:
                bank_account = restaurant.bank_account
                serializer = RestaurantBankAccountSerializer(bank_account)
                return Response(serializer.data)
            except RestaurantBankAccount.DoesNotExist:
                return Response({'message': 'No bank account found'}, status=404)

        elif request.method in ['POST', 'PUT']:
            try:
                bank_account = restaurant.bank_account
                serializer = RestaurantBankAccountSerializer(bank_account, data=request.data)
            except RestaurantBankAccount.DoesNotExist:
                serializer = RestaurantBankAccountSerializer(data=request.data)

            if serializer.is_valid():
                serializer.save(restaurant=restaurant)
                return Response(serializer.data)
            return Response(serializer.errors, status=400)

    @action(detail=False, methods=['get'])
    def commission_structure(self, request):
        """Get restaurant commission structure"""
        restaurant = self.get_restaurant()
        if not restaurant:
            return Response({'error': 'Restaurant not found'}, status=404)

        try:
            commission_structure = restaurant.commission_structure
            serializer = CommissionStructureSerializer(commission_structure)
            return Response(serializer.data)
        except CommissionStructure.DoesNotExist:
            return Response({'message': 'No commission structure found'}, status=404)
