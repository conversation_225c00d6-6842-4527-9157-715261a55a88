import React, { useState } from "react";
import axios from "axios";

const BackendApiTest = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);

  const addResult = (test, result) => {
    setResults(prev => [...prev, { 
      test, 
      result, 
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testRegistrationAPI = async () => {
    setLoading(true);
    try {
      // Use exact format from backend developer
      const testData = {
        "name": "Test User Frontend",
        "user_name": "testuser" + Date.now(),
        "phone": "+9376666666",
        "email": "<EMAIL>",
        "password": "123",
        "role": "customer"
      };

      console.log("🧪 Testing registration with exact backend format:", testData);

      const response = await axios.post(
        "https://afghansufra.luilala.com/api/auth/register/",
        testData,
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 15000
        }
      );

      addResult("Registration API (Backend Format)", {
        success: true,
        status: response.status,
        message: "✅ Registration successful with backend format!",
        data: response.data,
        sentData: testData
      });

    } catch (error) {
      console.error("Registration test error:", error);
      
      addResult("Registration API (Backend Format)", {
        success: false,
        status: error.response?.status,
        message: `❌ Registration failed: ${error.response?.status || 'Network Error'}`,
        error: error.message,
        responseData: error.response?.data,
        sentData: {
          "name": "Test User Frontend",
          "user_name": "testuser" + Date.now(),
          "phone": "+9376666666", 
          "email": "<EMAIL>",
          "password": "123",
          "role": "customer"
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const testLoginAPI = async () => {
    setLoading(true);
    try {
      // Use exact format from backend developer
      const loginData = {
        "user_name": "ahmad123",
        "password": "123"
      };

      console.log("🧪 Testing login with backend format:", loginData);

      const response = await axios.post(
        "https://afghansufra.luilala.com/api/auth/login/",
        loginData,
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 15000
        }
      );

      addResult("Login API (Backend Format)", {
        success: true,
        status: response.status,
        message: "✅ Login successful with backend format!",
        data: response.data,
        sentData: loginData
      });

    } catch (error) {
      console.error("Login test error:", error);
      
      addResult("Login API (Backend Format)", {
        success: false,
        status: error.response?.status,
        message: `❌ Login failed: ${error.response?.status || 'Network Error'}`,
        error: error.message,
        responseData: error.response?.data,
        sentData: loginData
      });
    } finally {
      setLoading(false);
    }
  };

  const testEmailVerificationAPI = async () => {
    setLoading(true);
    try {
      // Use exact format from backend developer
      const verificationData = {
        "email": "<EMAIL>",
        "otp": "946201"
      };

      console.log("🧪 Testing email verification with backend format:", verificationData);

      const response = await axios.post(
        "https://afghansufra.luilala.com/api/auth/verify-email/",
        verificationData,
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 15000
        }
      );

      addResult("Email Verification API (Backend Format)", {
        success: true,
        status: response.status,
        message: "✅ Email verification successful with backend format!",
        data: response.data,
        sentData: verificationData
      });

    } catch (error) {
      console.error("Email verification test error:", error);
      
      addResult("Email Verification API (Backend Format)", {
        success: false,
        status: error.response?.status,
        message: `❌ Email verification failed: ${error.response?.status || 'Network Error'}`,
        error: error.message,
        responseData: error.response?.data,
        sentData: verificationData
      });
    } finally {
      setLoading(false);
    }
  };

  const testWithRealEmail = async () => {
    const email = prompt("Enter your real email address for testing:");
    if (!email) return;

    setLoading(true);
    try {
      const testData = {
        "name": "Real Test User",
        "user_name": "realtest" + Date.now(),
        "phone": "+9376666666",
        "email": email,
        "password": "123456",
        "role": "customer"
      };

      const response = await axios.post(
        "https://afghansufra.luilala.com/api/auth/register/",
        testData,
        {
          headers: {
            "Content-Type": "application/json",
          },
          timeout: 15000
        }
      );

      addResult("Real Email Registration", {
        success: true,
        status: response.status,
        message: `✅ Registration successful! Check ${email} for OTP`,
        data: response.data,
        email: email
      });

    } catch (error) {
      addResult("Real Email Registration", {
        success: false,
        status: error.response?.status,
        message: `❌ Registration failed: ${error.response?.status || 'Network Error'}`,
        error: error.message,
        responseData: error.response?.data
      });
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => setResults([]);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">🧪 Backend API Test (Exact Format)</h1>
      
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
        <h2 className="font-semibold text-green-800 mb-2">✅ Backend APIs Confirmed:</h2>
        <div className="text-sm text-green-700 space-y-1">
          <p><strong>Registration:</strong> https://afghansufra.luilala.com/api/auth/register/</p>
          <p><strong>Login:</strong> https://afghansufra.luilala.com/api/auth/login/</p>
          <p><strong>Email Verification:</strong> https://afghansufra.luilala.com/api/auth/verify-email/</p>
          <p><strong>Email for OTP:</strong> <EMAIL></p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <button
          onClick={testRegistrationAPI}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          🧪 Test Registration API
        </button>
        
        <button
          onClick={testLoginAPI}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          🧪 Test Login API
        </button>
        
        <button
          onClick={testEmailVerificationAPI}
          disabled={loading}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50"
        >
          🧪 Test Email Verification
        </button>
        
        <button
          onClick={testWithRealEmail}
          disabled={loading}
          className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 disabled:opacity-50"
        >
          📧 Test with Real Email
        </button>
      </div>

      <div className="flex gap-4 mb-6">
        <button
          onClick={clearResults}
          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
        >
          Clear Results
        </button>
      </div>

      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="mt-2">Testing APIs...</p>
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test Results:</h2>
        {results.length === 0 ? (
          <p className="text-gray-500">No tests run yet. Click a test button above.</p>
        ) : (
          results.map((item, index) => (
            <div key={index} className={`border rounded p-4 ${item.result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-semibold">{item.test}</h3>
                <span className="text-sm text-gray-500">{item.timestamp}</span>
              </div>
              <div className="mb-2">
                <span className={`font-semibold ${item.result.success ? 'text-green-700' : 'text-red-700'}`}>
                  {item.result.message}
                </span>
                {item.result.status && (
                  <span className="ml-2 text-sm text-gray-600">
                    (Status: {item.result.status})
                  </span>
                )}
              </div>
              <details className="text-sm">
                <summary className="cursor-pointer">View Details</summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto text-xs">
                  {JSON.stringify(item.result, null, 2)}
                </pre>
              </details>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default BackendApiTest;
