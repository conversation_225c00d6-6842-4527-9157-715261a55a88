import React, { useState } from 'react';
import { X, Filter, RotateCcw, Check } from 'lucide-react';
import { useFilters } from '../../context/FiltersContext';
import Button from '../common/Button';
import Card from '../common/Card';

const FilterPanel = ({ isOpen, onClose }) => {
  const {
    filters,
    filterOptions,
    appliedFiltersCount,
    updateFilter,
    toggleArrayFilter,
    clearAllFilters,
    clearFilter
  } = useFilters();

  const [tempFilters, setTempFilters] = useState(filters);

  const handleApplyFilters = () => {
    // Apply all temp filters at once
    Object.keys(tempFilters).forEach(key => {
      updateFilter(key, tempFilters[key]);
    });
    onClose();
  };

  const handleClearAll = () => {
    clearAllFilters();
    setTempFilters({
      priceRange: [1, 4],
      cuisines: [],
      dietaryRestrictions: [],
      deliveryTime: null,
      rating: null,
      distance: null,
      features: [],
      sortBy: 'relevance'
    });
  };

  const updateTempFilter = (key, value) => {
    setTempFilters(prev => ({ ...prev, [key]: value }));
  };

  const toggleTempArrayFilter = (key, value) => {
    setTempFilters(prev => ({
      ...prev,
      [key]: prev[key].includes(value)
        ? prev[key].filter(item => item !== value)
        : [...prev[key], value]
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex">
      {/* Overlay */}
      <div className="flex-1" onClick={onClose}></div>
      
      {/* Filter Panel */}
      <div className="w-full max-w-md bg-white h-full overflow-y-auto animate-slide-in-right">
        <div className="sticky top-0 bg-white border-b border-gray-200 p-4 z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Filter size={20} className="text-primary-500 mr-2" />
              <h2 className="text-lg font-semibold">Filters</h2>
              {appliedFiltersCount > 0 && (
                <span className="ml-2 bg-primary-500 text-white text-xs rounded-full px-2 py-1">
                  {appliedFiltersCount}
                </span>
              )}
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        <div className="p-4 space-y-6">
          {/* Price Range */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">Price Range</h3>
              {(tempFilters.priceRange[0] !== 1 || tempFilters.priceRange[1] !== 4) && (
                <button
                  onClick={() => updateTempFilter('priceRange', [1, 4])}
                  className="text-primary-500 text-sm hover:underline"
                >
                  Clear
                </button>
              )}
            </div>
            <div className="grid grid-cols-4 gap-2">
              {filterOptions.priceRanges.map((price) => (
                <button
                  key={price.value}
                  onClick={() => {
                    const newRange = tempFilters.priceRange[0] === price.value && tempFilters.priceRange[1] === price.value
                      ? [1, 4] // Reset if clicking same price
                      : [price.value, price.value]; // Set specific price
                    updateTempFilter('priceRange', newRange);
                  }}
                  className={`p-3 rounded-lg border text-center transition-colors ${
                    tempFilters.priceRange[0] <= price.value && tempFilters.priceRange[1] >= price.value
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">{price.label}</div>
                  <div className="text-xs text-gray-500 mt-1">{price.description}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Cuisines */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">Cuisines</h3>
              {tempFilters.cuisines.length > 0 && (
                <button
                  onClick={() => updateTempFilter('cuisines', [])}
                  className="text-primary-500 text-sm hover:underline"
                >
                  Clear
                </button>
              )}
            </div>
            <div className="grid grid-cols-2 gap-2">
              {filterOptions.cuisines.map((cuisine) => (
                <button
                  key={cuisine}
                  onClick={() => toggleTempArrayFilter('cuisines', cuisine)}
                  className={`p-3 rounded-lg border text-sm transition-colors ${
                    tempFilters.cuisines.includes(cuisine)
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {cuisine}
                </button>
              ))}
            </div>
          </div>

          {/* Dietary Restrictions */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">Dietary Options</h3>
              {tempFilters.dietaryRestrictions.length > 0 && (
                <button
                  onClick={() => updateTempFilter('dietaryRestrictions', [])}
                  className="text-primary-500 text-sm hover:underline"
                >
                  Clear
                </button>
              )}
            </div>
            <div className="grid grid-cols-2 gap-2">
              {filterOptions.dietaryRestrictions.map((diet) => (
                <button
                  key={diet.id}
                  onClick={() => toggleTempArrayFilter('dietaryRestrictions', diet.id)}
                  className={`p-3 rounded-lg border text-sm transition-colors flex items-center ${
                    tempFilters.dietaryRestrictions.includes(diet.id)
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{diet.icon}</span>
                  <span>{diet.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Delivery Time */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">Delivery Time</h3>
              {tempFilters.deliveryTime && (
                <button
                  onClick={() => updateTempFilter('deliveryTime', null)}
                  className="text-primary-500 text-sm hover:underline"
                >
                  Clear
                </button>
              )}
            </div>
            <div className="space-y-2">
              {filterOptions.deliveryTimes.map((time) => (
                <button
                  key={time.value}
                  onClick={() => updateTempFilter('deliveryTime', 
                    tempFilters.deliveryTime === time.value ? null : time.value
                  )}
                  className={`w-full p-3 rounded-lg border text-left transition-colors ${
                    tempFilters.deliveryTime === time.value
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {time.label}
                </button>
              ))}
            </div>
          </div>

          {/* Rating */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">Minimum Rating</h3>
              {tempFilters.rating && (
                <button
                  onClick={() => updateTempFilter('rating', null)}
                  className="text-primary-500 text-sm hover:underline"
                >
                  Clear
                </button>
              )}
            </div>
            <div className="space-y-2">
              {filterOptions.ratings.map((rating) => (
                <button
                  key={rating.value}
                  onClick={() => updateTempFilter('rating', 
                    tempFilters.rating === rating.value ? null : rating.value
                  )}
                  className={`w-full p-3 rounded-lg border text-left transition-colors ${
                    tempFilters.rating === rating.value
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {rating.label}
                </button>
              ))}
            </div>
          </div>

          {/* Features */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">Features</h3>
              {tempFilters.features.length > 0 && (
                <button
                  onClick={() => updateTempFilter('features', [])}
                  className="text-primary-500 text-sm hover:underline"
                >
                  Clear
                </button>
              )}
            </div>
            <div className="grid grid-cols-2 gap-2">
              {filterOptions.features.map((feature) => (
                <button
                  key={feature.id}
                  onClick={() => toggleTempArrayFilter('features', feature.id)}
                  className={`p-3 rounded-lg border text-sm transition-colors flex items-center ${
                    tempFilters.features.includes(feature.id)
                      ? 'border-primary-500 bg-primary-50 text-primary-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{feature.icon}</span>
                  <span>{feature.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Actions */}
        <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4">
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleClearAll}
              icon={<RotateCcw size={16} />}
              className="flex-1"
            >
              Clear All
            </Button>
            <Button
              variant="primary"
              onClick={handleApplyFilters}
              icon={<Check size={16} />}
              className="flex-1"
            >
              Apply Filters
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterPanel;
