import React from 'react';
import { useFilters } from '../../context/FiltersContext';

const QuickFilters = () => {
  const { quickFilters, applyQuickFilter, filters } = useFilters();

  const isQuickFilterActive = (quickFilter) => {
    const quickFilterKeys = Object.keys(quickFilter.filters);
    return quickFilterKeys.every(key => {
      const quickValue = quickFilter.filters[key];
      const currentValue = filters[key];

      if (Array.isArray(quickValue)) {
        return Array.isArray(currentValue) && 
               quickValue.every(item => currentValue.includes(item));
      }
      return currentValue === quickValue;
    });
  };

  return (
    <div className="flex space-x-3 overflow-x-auto pb-2 scrollbar-hide">
      {quickFilters.map((quickFilter) => {
        const isActive = isQuickFilterActive(quickFilter);
        
        return (
          <button
            key={quickFilter.id}
            onClick={() => applyQuickFilter(quickFilter)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap transition-all duration-200 ${
              isActive
                ? 'bg-primary-500 text-white shadow-md transform scale-105'
                : 'bg-white border border-gray-200 text-gray-700 hover:border-primary-300 hover:bg-primary-50'
            }`}
          >
            <span className="text-lg">{quickFilter.icon}</span>
            <span className="font-medium text-sm">{quickFilter.label}</span>
          </button>
        );
      })}
    </div>
  );
};

export default QuickFilters;
