#!/usr/bin/env python3
"""
Simple Delete Delivery Users Script
Works with existing database structure
"""

import os
import sys
import django
from pathlib import Path

# Add the Backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.db import transaction

User = get_user_model()

def list_delivery_users():
    """List all delivery users"""
    print("🔍 Finding delivery agent users...")
    
    # Find users with delivery_agent role
    delivery_users = User.objects.filter(role='delivery_agent')
    
    print(f"📊 Found {delivery_users.count()} delivery agent users:")
    print("-" * 60)
    
    for user in delivery_users:
        print(f"ID: {user.id}")
        print(f"Username: {user.user_name}")
        print(f"Name: {user.name}")
        print(f"Phone: {user.phone}")
        print(f"Email: {user.email}")
        print(f"Role: {user.role}")
        print(f"Date Joined: {user.date_joined}")
        print("-" * 60)
    
    return delivery_users

def delete_all_delivery_users():
    """Delete all delivery users"""
    print("🗑️  Starting deletion of delivery users...")
    
    try:
        with transaction.atomic():
            # Get all delivery users
            delivery_users = User.objects.filter(role='delivery_agent')
            
            if not delivery_users.exists():
                print("✅ No delivery users found to delete.")
                return True
            
            print(f"\n⚠️  WARNING: This will delete {delivery_users.count()} delivery users!")
            print("Users to be deleted:")
            for user in delivery_users:
                print(f"   • {user.user_name} ({user.name})")
            
            confirm = input("\nType 'DELETE' to confirm deletion: ")
            
            if confirm != 'DELETE':
                print("❌ Deletion cancelled.")
                return False
            
            # Update orders to remove delivery agent assignments
            try:
                from orders.models import Order
                orders_updated = Order.objects.filter(delivery_agent__in=delivery_users).update(delivery_agent=None)
                print(f"✅ Updated {orders_updated} orders (set delivery_agent to NULL)")
            except Exception as e:
                print(f"⚠️  Could not update orders: {e}")
            
            # Delete the users
            user_count = delivery_users.count()
            delivery_users.delete()
            
            print(f"✅ Successfully deleted {user_count} delivery users!")
            return True
            
    except Exception as e:
        print(f"❌ Error during deletion: {e}")
        return False

def delete_specific_user():
    """Delete a specific delivery user"""
    print("🎯 Delete specific delivery user")
    print("-" * 40)
    
    # List all delivery users first
    delivery_users = list_delivery_users()
    
    if not delivery_users.exists():
        print("No delivery users found.")
        return
    
    # Get user input
    identifier = input("\nEnter user ID or username to delete: ").strip()
    
    try:
        # Try to find by ID first
        if identifier.isdigit():
            user = User.objects.get(id=int(identifier), role='delivery_agent')
        else:
            user = User.objects.get(user_name=identifier, role='delivery_agent')
        
        print(f"\n📋 User to delete:")
        print(f"ID: {user.id}")
        print(f"Username: {user.user_name}")
        print(f"Name: {user.name}")
        print(f"Phone: {user.phone}")
        print(f"Email: {user.email}")
        
        confirm = input(f"\nConfirm deletion of user '{user.user_name}'? (y/N): ")
        
        if confirm.lower() == 'y':
            with transaction.atomic():
                # Update orders
                try:
                    from orders.models import Order
                    orders_updated = Order.objects.filter(delivery_agent=user).update(delivery_agent=None)
                    if orders_updated > 0:
                        print(f"✅ Updated {orders_updated} orders")
                except Exception as e:
                    print(f"⚠️  Could not update orders: {e}")
                
                # Delete user
                user.delete()
                print(f"✅ User '{user.user_name}' deleted successfully!")
        else:
            print("❌ Deletion cancelled.")
            
    except User.DoesNotExist:
        print(f"❌ User '{identifier}' not found or not a delivery agent.")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main function with menu"""
    print("🗑️  Simple Delivery Users Deletion Tool")
    print("=" * 50)
    
    while True:
        print("\nSelect an option:")
        print("1. List all delivery users")
        print("2. Delete ALL delivery users (DANGEROUS)")
        print("3. Delete specific user by ID/username")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            list_delivery_users()
        elif choice == '2':
            delete_all_delivery_users()
        elif choice == '3':
            delete_specific_user()
        elif choice == '4':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-4.")

if __name__ == '__main__':
    main()
