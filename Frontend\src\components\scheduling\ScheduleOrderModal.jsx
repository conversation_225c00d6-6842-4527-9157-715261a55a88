import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Repeat, X, AlertCircle, Check } from 'lucide-react';
import { useScheduling } from '../../context/SchedulingContext';
import Button from '../common/Button';
import Card from '../common/Card';

const ScheduleOrderModal = ({ isOpen, onClose, restaurantId, orderData, onSchedule }) => {
  const {
    selectedDateTime,
    setSelectedDateTime,
    recurringSettings,
    setRecurringSettings,
    getAvailableTimeSlots,
    isValidScheduleTime
  } = useScheduling();

  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [availableSlots, setAvailableSlots] = useState([]);
  const [showRecurring, setShowRecurring] = useState(false);
  const [validationError, setValidationError] = useState('');

  // Get next 14 days for date selection
  const getAvailableDates = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 0; i < 14; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push({
        value: date.toISOString().split('T')[0],
        label: i === 0 ? 'Today' : i === 1 ? 'Tomorrow' : date.toLocaleDateString('en-US', { 
          weekday: 'short', 
          month: 'short', 
          day: 'numeric' 
        }),
        date: date
      });
    }
    
    return dates;
  };

  // Update available time slots when date changes
  useEffect(() => {
    if (selectedDate && restaurantId) {
      const date = new Date(selectedDate);
      const slots = getAvailableTimeSlots(restaurantId, date);
      setAvailableSlots(slots);
      setSelectedTime(''); // Reset time selection
    }
  }, [selectedDate, restaurantId, getAvailableTimeSlots]);

  // Validate selection
  useEffect(() => {
    if (selectedDate && selectedTime) {
      const dateTime = new Date(`${selectedDate}T${selectedTime}`);
      const validation = isValidScheduleTime(restaurantId, dateTime);
      
      if (!validation.valid) {
        setValidationError(validation.reason);
      } else {
        setValidationError('');
        setSelectedDateTime(dateTime);
      }
    }
  }, [selectedDate, selectedTime, restaurantId, isValidScheduleTime, setSelectedDateTime]);

  const handleSchedule = () => {
    if (!selectedDateTime || validationError) return;

    const schedulingData = {
      dateTime: selectedDateTime,
      recurring: showRecurring ? recurringSettings : null
    };

    onSchedule(schedulingData);
    handleClose();
  };

  const handleClose = () => {
    setSelectedDate('');
    setSelectedTime('');
    setShowRecurring(false);
    setValidationError('');
    setRecurringSettings({ enabled: false, frequency: 'weekly', endDate: null, daysOfWeek: [] });
    onClose();
  };

  if (!isOpen) return null;

  const availableDates = getAvailableDates();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold flex items-center">
            <Calendar className="mr-2 text-primary-500" size={24} />
            Schedule Your Order
          </h2>
          <button
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Date Selection */}
        <div className="mb-6">
          <h3 className="font-semibold mb-3">Select Date</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {availableDates.map((date) => (
              <button
                key={date.value}
                onClick={() => setSelectedDate(date.value)}
                className={`p-3 rounded-lg border text-sm font-medium transition-colors ${
                  selectedDate === date.value
                    ? 'border-primary-500 bg-primary-50 text-primary-700'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                {date.label}
              </button>
            ))}
          </div>
        </div>

        {/* Time Selection */}
        {selectedDate && (
          <div className="mb-6">
            <h3 className="font-semibold mb-3 flex items-center">
              <Clock className="mr-2" size={16} />
              Select Time
            </h3>
            {availableSlots.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <AlertCircle className="mx-auto mb-2" size={32} />
                <p>No available time slots for this date</p>
              </div>
            ) : (
              <div className="grid grid-cols-3 md:grid-cols-4 gap-2 max-h-48 overflow-y-auto">
                {availableSlots.map((slot, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedTime(slot.time.toTimeString().slice(0, 5))}
                    disabled={!slot.available}
                    className={`p-2 rounded-lg border text-sm font-medium transition-colors ${
                      selectedTime === slot.time.toTimeString().slice(0, 5)
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : slot.available
                        ? 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        : 'border-gray-100 bg-gray-50 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {slot.label}
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Recurring Options */}
        {selectedDate && selectedTime && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold flex items-center">
                <Repeat className="mr-2" size={16} />
                Recurring Order
              </h3>
              <button
                onClick={() => setShowRecurring(!showRecurring)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                  showRecurring
                    ? 'bg-primary-100 text-primary-700'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {showRecurring ? 'Enabled' : 'Disabled'}
              </button>
            </div>

            {showRecurring && (
              <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <label className="block text-sm font-medium mb-2">Frequency</label>
                  <select
                    value={recurringSettings.frequency}
                    onChange={(e) => setRecurringSettings(prev => ({ ...prev, frequency: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">End Date (Optional)</label>
                  <input
                    type="date"
                    value={recurringSettings.endDate || ''}
                    onChange={(e) => setRecurringSettings(prev => ({ ...prev, endDate: e.target.value }))}
                    min={new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Validation Error */}
        {validationError && (
          <div className="mb-6 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center">
            <AlertCircle className="text-red-500 mr-2" size={16} />
            <span className="text-red-700 text-sm">{validationError}</span>
          </div>
        )}

        {/* Order Summary */}
        {selectedDateTime && !validationError && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center mb-2">
              <Check className="text-green-500 mr-2" size={16} />
              <span className="font-medium text-green-800">Order will be scheduled for:</span>
            </div>
            <p className="text-green-700">
              {selectedDateTime.toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })} at {selectedDateTime.toLocaleTimeString('en-US', { 
                hour: 'numeric', 
                minute: '2-digit',
                hour12: true 
              })}
            </p>
            {showRecurring && (
              <p className="text-green-600 text-sm mt-1">
                Recurring {recurringSettings.frequency}
                {recurringSettings.endDate && ` until ${new Date(recurringSettings.endDate).toLocaleDateString()}`}
              </p>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={handleClose}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSchedule}
            disabled={!selectedDateTime || !!validationError}
            className="flex-1"
          >
            Schedule Order
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default ScheduleOrderModal;
