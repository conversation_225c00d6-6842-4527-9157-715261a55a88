import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "./AuthContext";

const SupportContext = createContext();

export const useSupport = () => {
  const context = useContext(SupportContext);
  if (!context) {
    throw new Error("useSupport must be used within a SupportProvider");
  }
  return context;
};

export const SupportProvider = ({ children }) => {
  const { user } = useAuth();
  const [tickets, setTickets] = useState([]);
  const [chatMessages, setChatMessages] = useState([]);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);

  // Load tickets from localStorage
  useEffect(() => {
    if (user) {
      const savedTickets = localStorage.getItem(`support_tickets_${user.id}`);
      if (savedTickets) {
        setTickets(JSON.parse(savedTickets));
      }

      const savedMessages = localStorage.getItem(`chat_messages_${user.id}`);
      if (savedMessages) {
        setChatMessages(JSON.parse(savedMessages));
      }
    }
  }, [user]);

  // Save tickets to localStorage
  useEffect(() => {
    if (user && tickets.length >= 0) {
      localStorage.setItem(
        `support_tickets_${user.id}`,
        JSON.stringify(tickets)
      );
    }
  }, [tickets, user]);

  // Save chat messages to localStorage
  useEffect(() => {
    if (user && chatMessages.length >= 0) {
      localStorage.setItem(
        `chat_messages_${user.id}`,
        JSON.stringify(chatMessages)
      );
    }
  }, [chatMessages, user]);

  const createTicket = (ticketData) => {
    const newTicket = {
      id: `ticket-${Date.now()}`,
      userId: user?.id,
      userName: user?.name,
      userEmail: user?.email,
      subject: ticketData.subject,
      category: ticketData.category,
      priority: ticketData.priority || "medium",
      description: ticketData.description,
      orderId: ticketData.orderId || null,
      status: "open",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      messages: [
        {
          id: `msg-${Date.now()}`,
          sender: "user",
          senderName: user?.name,
          message: ticketData.description,
          timestamp: new Date().toISOString(),
        },
      ],
    };

    setTickets((prev) => [newTicket, ...prev]);
    return newTicket;
  };

  const updateTicket = (ticketId, updates) => {
    setTickets((prev) =>
      prev.map((ticket) =>
        ticket.id === ticketId
          ? { ...ticket, ...updates, updatedAt: new Date().toISOString() }
          : ticket
      )
    );
  };

  const addTicketMessage = (ticketId, message, sender = "user") => {
    const newMessage = {
      id: `msg-${Date.now()}`,
      sender,
      senderName: sender === "user" ? user?.name : "Support Team",
      message,
      timestamp: new Date().toISOString(),
    };

    setTickets((prev) =>
      prev.map((ticket) =>
        ticket.id === ticketId
          ? {
              ...ticket,
              messages: [...ticket.messages, newMessage],
              updatedAt: new Date().toISOString(),
            }
          : ticket
      )
    );

    return newMessage;
  };

  const sendChatMessage = (message) => {
    const newMessage = {
      id: `chat-${Date.now()}`,
      sender: "user",
      senderName: user?.name,
      message,
      timestamp: new Date().toISOString(),
    };

    setChatMessages((prev) => [...prev, newMessage]);

    // Simulate support response after 2-5 seconds
    setTimeout(() => {
      const responses = [
        "Thank you for contacting Afghan Sofra support! How can I help you today?",
        "I understand your concern. Let me check that for you right away.",
        "I'm here to help! Could you provide more details about the issue?",
        "Thanks for reaching out. I'll look into this immediately.",
        "I appreciate your patience. Let me find the best solution for you.",
      ];

      const supportMessage = {
        id: `chat-${Date.now()}`,
        sender: "support",
        senderName: "Sarah (Support)",
        message: responses[Math.floor(Math.random() * responses.length)],
        timestamp: new Date().toISOString(),
      };

      setChatMessages((prev) => [...prev, supportMessage]);

      if (!isChatOpen) {
        setUnreadCount((prev) => prev + 1);
      }
    }, Math.random() * 3000 + 2000);

    return newMessage;
  };

  const markChatAsRead = () => {
    setUnreadCount(0);
  };

  const getTicketsByStatus = (status) => {
    return tickets.filter((ticket) => ticket.status === status);
  };

  const getTicketsByCategory = (category) => {
    return tickets.filter((ticket) => ticket.category === category);
  };

  const searchTickets = (query) => {
    const lowerQuery = query.toLowerCase();
    return tickets.filter(
      (ticket) =>
        ticket.subject.toLowerCase().includes(lowerQuery) ||
        ticket.description.toLowerCase().includes(lowerQuery) ||
        ticket.category.toLowerCase().includes(lowerQuery)
    );
  };

  // FAQ Data
  const faqCategories = [
    {
      id: "orders",
      name: "Orders & Delivery",
      icon: "📦",
      questions: [
        {
          id: "track-order",
          question: "How can I track my order?",
          answer:
            'You can track your order in real-time by going to "My Orders" section. You\'ll see live updates including preparation, pickup, and delivery status.',
        },
        {
          id: "cancel-order",
          question: "Can I cancel my order?",
          answer:
            "You can cancel your order within 5 minutes of placing it. After that, please contact our support team for assistance.",
        },
        {
          id: "delivery-time",
          question: "How long does delivery take?",
          answer:
            "Typical delivery time is 30-45 minutes. During peak hours, it may take up to 60 minutes. You can see estimated delivery time when placing your order.",
        },
        {
          id: "wrong-order",
          question: "What if I receive the wrong order?",
          answer:
            "If you receive an incorrect order, please contact us immediately through chat or phone. We'll arrange for the correct order to be delivered and handle the return.",
        },
      ],
    },
    {
      id: "payment",
      name: "Payment & Billing",
      icon: "💳",
      questions: [
        {
          id: "payment-methods",
          question: "What payment methods do you accept?",
          answer:
            "Currently, we only accept cash on delivery. Online payment options will be available soon.",
        },
        {
          id: "receipt",
          question: "How do I get a receipt?",
          answer:
            "You'll receive a digital receipt via email after your order is completed. You can also view it in your order history.",
        },
        {
          id: "refund",
          question: "How do refunds work?",
          answer:
            "Refunds are processed within 3-5 business days. For cash orders, refunds are provided in cash during the return pickup.",
        },
      ],
    },
    {
      id: "account",
      name: "Account & Profile",
      icon: "👤",
      questions: [
        {
          id: "reset-password",
          question: "How do I reset my password?",
          answer:
            'Click on "Forgot Password" on the login page and enter your email. You\'ll receive a reset link within a few minutes.',
        },
        {
          id: "update-profile",
          question: "How do I update my profile information?",
          answer:
            'Go to your Profile page and click "Edit Profile". You can update your name, phone number, and delivery addresses.',
        },
        {
          id: "loyalty-points",
          question: "How do loyalty points work?",
          answer:
            "You earn points with every order. Bronze members earn 1x points, Silver 1.5x, and Gold 2x. Points can be redeemed for discounts on future orders.",
        },
      ],
    },
    {
      id: "restaurants",
      name: "Restaurants & Menu",
      icon: "🍽️",
      questions: [
        {
          id: "restaurant-hours",
          question: "What are restaurant operating hours?",
          answer:
            "Most restaurants operate from 10 AM to 11 PM, but hours may vary. Check individual restaurant pages for specific hours.",
        },
        {
          id: "menu-updates",
          question: "Why are some items unavailable?",
          answer:
            "Menu availability depends on the restaurant's current stock. Items may become unavailable during the day if they run out of ingredients.",
        },
        {
          id: "dietary-restrictions",
          question: "Do you have options for dietary restrictions?",
          answer:
            "Yes! Many restaurants offer vegetarian, vegan, and halal options. Use our filters to find restaurants that meet your dietary needs.",
        },
      ],
    },
  ];

  const value = {
    tickets,
    chatMessages,
    isChatOpen,
    setIsChatOpen,
    isOnline,
    unreadCount,
    faqCategories,
    createTicket,
    updateTicket,
    addTicketMessage,
    sendChatMessage,
    markChatAsRead,
    getTicketsByStatus,
    getTicketsByCategory,
    searchTickets,
  };

  return (
    <SupportContext.Provider value={value}>{children}</SupportContext.Provider>
  );
};

export default SupportContext;
