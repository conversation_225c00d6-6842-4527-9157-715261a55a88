import React, { useState } from 'react';
import { Search, Filter, MoreVertical, Edit, Trash2, UserPlus } from 'lucide-react';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Badge from '../../components/common/Badge';

function ManageUsers() {
  const [selectedRole, setSelectedRole] = useState('all');
  
  return (
    <div className="p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <h1 className="text-2xl font-bold mb-4 sm:mb-0">Manage Users</h1>
        <Button
          variant="primary"
          icon={<UserPlus size={18} />}
        >
          Add New User
        </Button>
      </div>
      
      <Card>
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search users..."
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
          
          <div className="flex gap-4">
            <select 
              className="px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              value={selectedRole}
              onChange={(e) => setSelectedRole(e.target.value)}
            >
              <option value="all">All Roles</option>
              <option value="customer">Customers</option>
              <option value="restaurant">Restaurant Owners</option>
              <option value="delivery">Delivery Agents</option>
              <option value="admin">Admins</option>
            </select>
            
            <Button
              variant="secondary"
              icon={<Filter size={18} />}
            >
              Filters
            </Button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-4 px-4 font-semibold">User</th>
                <th className="text-left py-4 px-4 font-semibold">Role</th>
                <th className="text-left py-4 px-4 font-semibold">Email</th>
                <th className="text-left py-4 px-4 font-semibold">Status</th>
                <th className="text-left py-4 px-4 font-semibold">Joined</th>
                <th className="text-right py-4 px-4 font-semibold">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b hover:bg-gray-50">
                <td className="py-4 px-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                      JD
                    </div>
                    <div className="ml-3">
                      <p className="font-medium">John Doe</p>
                      <p className="text-sm text-text-secondary">+1 234-567-8900</p>
                    </div>
                  </div>
                </td>
                <td className="py-4 px-4">
                  <Badge variant="primary" size="small">Customer</Badge>
                </td>
                <td className="py-4 px-4"><EMAIL></td>
                <td className="py-4 px-4">
                  <Badge variant="success" size="small">Active</Badge>
                </td>
                <td className="py-4 px-4">Jan 15, 2024</td>
                <td className="py-4 px-4">
                  <div className="flex justify-end gap-2">
                    <button className="p-1 hover:text-primary-500">
                      <Edit size={18} />
                    </button>
                    <button className="p-1 hover:text-accent-red">
                      <Trash2 size={18} />
                    </button>
                    <button className="p-1">
                      <MoreVertical size={18} />
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div className="mt-4 flex items-center justify-between">
          <p className="text-sm text-text-secondary">
            Showing 1 of 1 users
          </p>
          <div className="flex gap-2">
            <Button variant="secondary" disabled>Previous</Button>
            <Button variant="secondary" disabled>Next</Button>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default ManageUsers;