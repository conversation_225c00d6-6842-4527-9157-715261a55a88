#!/usr/bin/env python3
"""
Test script for admin-created employee immediate login
Tests that admin-created delivery agents can login immediately without email verification
(Regular users still need email verification, but employees don't)
"""

import requests
import json
import time

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"
HEADERS = {
    "Content-Type": "application/json",
}

def test_admin_login():
    """Test admin login to get authentication token"""
    print("🔐 Testing Admin Login...")
    
    login_data = {
        "user_name": "admin",  # Replace with actual admin username
        "password": "admin123"  # Replace with actual admin password
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=HEADERS,
            data=json.dumps(login_data),
            timeout=30
        )
        
        print(f"📡 Login Response Status: {response.status_code}")
        print(f"📄 Login Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Admin login successful!")
                return result['data']['access_token']
            else:
                print("❌ Login failed:", result.get('message'))
                return None
        else:
            print(f"❌ Login failed with status {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Exception during login: {e}")
        return None

def test_employee_creation(admin_token):
    """Test creating a delivery agent employee (no email verification required)"""
    print("\n👷 Testing Employee Creation (No Email Verification Required)...")
    
    # Get test email from user
    test_email = input("📧 Enter email for the test employee: ").strip()
    
    if not test_email or "@" not in test_email:
        print("❌ Invalid email address")
        return None
    
    employee_data = {
        "full_name": "Test Employee Agent",
        "father_name": "Test Father",
        "national_id": f"{int(time.time())}",  # Unique ID
        "date_of_birth": "1990-01-01",
        "gender": "male",
        "marital_status": "single",
        "phone_number": f"+93{int(time.time()) % **********}",  # Unique phone
        "email": test_email,
        "province": "kabul",
        "district": "district_1",
        "area": "area_1",  # Added missing area field
        "address": "Test Address 123",
        "vehicle_type": "motorcycle",
        "vehicle_model": "Honda",
        "vehicle_year": 2020,
        "license_plate": f"TEST{int(time.time()) % 1000}",
        "hire_date": "2025-07-29",
        "salary_type": "fixed",
        "base_salary": "20000.00",
        "commission_per_delivery": "150.00",
        "work_schedule": "full_time",
        "shift_start_time": "08:00",
        "shift_end_time": "17:00",
        "working_days": "monday_to_saturday",
        "bank_name": "Test Bank",
        "account_number": "**********",
        "account_holder_name": "Test Employee Agent",
        "reference1_name": "Reference One",
        "reference1_phone": "+***********",
        "reference1_relation": "friend",
        "reference2_name": "Reference Two",  # Added missing reference2 fields
        "reference2_phone": "+***********",
        "reference2_relation": "family",
        "emergency_contact": "+***********",
        "emergency_relation": "family"
    }
    
    headers_with_auth = {
        **HEADERS,
        "Authorization": f"Bearer {admin_token}"
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/delivery-agent/admin/employees/create/",
            headers=headers_with_auth,
            data=json.dumps(employee_data),
            timeout=30
        )
        
        print(f"📡 Employee Creation Response Status: {response.status_code}")
        print(f"📄 Employee Creation Response: {response.text}")
        
        if response.status_code == 201:
            result = response.json()
            if result.get('status') == 'success':
                print("✅ Employee created successfully!")
                
                # Check verification information
                verification_info = result['data'].get('verification', {})
                if verification_info.get('email_verification_required'):
                    print(f"📧 Email verification required!")
                    print(f"📬 Verification email sent to: {verification_info.get('email_sent_to')}")
                    print(f"💬 Message: {verification_info.get('verification_message')}")
                else:
                    print("ℹ️ No email verification required")
                
                return result['data']
            else:
                print("❌ Employee creation failed:", result.get('message'))
                return None
        else:
            print(f"❌ Employee creation failed with status {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Exception during employee creation: {e}")
        return None

def test_employee_login_immediate(employee_data):
    """Test that admin-created employee can login immediately without verification"""
    print("\n✅ Testing Employee Immediate Login...")

    login_data = {
        "user_name": employee_data['username'],
        "password": employee_data['temporary_password']
    }

    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=HEADERS,
            data=json.dumps(login_data),
            timeout=30
        )

        print(f"📡 Login Response Status: {response.status_code}")
        print(f"📄 Login Response: {response.text}")

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Employee can login immediately - no verification required!")
                return True
            else:
                print("❌ Login failed:", result.get('message'))
                return False
        elif response.status_code == 400:
            result = response.json()
            print("❌ Login blocked when it should succeed:", result)
            return False
        else:
            print(f"❌ Unexpected response status: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Exception during login test: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Employee Immediate Login System")
    print("=" * 50)
    
    # Step 1: Admin login
    admin_token = test_admin_login()
    if not admin_token:
        print("❌ Cannot proceed without admin token")
        return
    
    # Step 2: Create employee with email verification
    employee_data = test_employee_creation(admin_token)
    if not employee_data:
        print("❌ Cannot proceed without employee data")
        return
    
    # Step 3: Test that employee can login immediately
    login_success = test_employee_login_immediate(employee_data)

    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY:")
    print(f"✅ Admin Login: {'PASS' if admin_token else 'FAIL'}")
    print(f"✅ Employee Creation: {'PASS' if employee_data else 'FAIL'}")
    print(f"✅ No Email Verification Required: {'PASS' if employee_data and not employee_data.get('verification', {}).get('email_verification_required') else 'FAIL'}")
    print(f"✅ Employee Can Login Immediately: {'PASS' if login_success else 'FAIL'}")

    if employee_data:
        print(f"\n✅ Employee Ready to Work:")
        print(f"1. Employee can login immediately with:")
        print(f"   Username: {employee_data.get('username')}")
        print(f"   Password: {employee_data.get('temporary_password')}")
        print(f"2. Employee must change password on first login")
        print(f"3. No email verification required for admin-created employees")

if __name__ == "__main__":
    main()
