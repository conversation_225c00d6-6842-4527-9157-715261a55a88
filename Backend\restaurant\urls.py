# restaurant/urls.py
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    RestaurantViewSet, MenuCategoryViewSet, MenuItemViewSet, RestaurantReviewViewSet, ReviewResponseViewSet,
    MenuItemVariantViewSet, MenuItemAddonViewSet, MenuItemCustomizationGroupViewSet, MenuItemCustomizationOptionViewSet,
    CuisineTypeViewSet, RestaurantCategoryViewSet, ServiceAreaViewSet,
    PromotionViewSet, PromotionCodeViewSet, AddressViewSet
)

router = DefaultRouter()
router.register(r'restaurants', RestaurantViewSet, basename='restaurant')
router.register(r'menu-categories', MenuCategoryViewSet, basename='menu-category')
router.register(r'menu-items', MenuItemViewSet, basename='menu-item')
router.register(r'reviews', RestaurantReviewViewSet, basename='review')
router.register(r'review-responses', ReviewResponseViewSet, basename='review-response')

# Menu item variants and customizations
router.register(r'menu-item-variants', MenuItemVariantViewSet, basename='menu-item-variant')
router.register(r'menu-item-addons', MenuItemAddonViewSet, basename='menu-item-addon')
router.register(r'customization-groups', MenuItemCustomizationGroupViewSet, basename='customization-group')
router.register(r'customization-options', MenuItemCustomizationOptionViewSet, basename='customization-option')

# Restaurant business details
router.register(r'cuisine-types', CuisineTypeViewSet, basename='cuisine-type')
router.register(r'restaurant-categories', RestaurantCategoryViewSet, basename='restaurant-category')
router.register(r'service-areas', ServiceAreaViewSet, basename='service-area')

# Promotions and discounts
router.register(r'promotions', PromotionViewSet, basename='promotion')
router.register(r'promotion-codes', PromotionCodeViewSet, basename='promotion-code')

# Addresses
router.register(r'addresses', AddressViewSet, basename='address')

urlpatterns = [
    path('', include(router.urls)),
]