# system_config/urls.py
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import SystemSettingViewSet, ChoiceOptionViewSet, FilterConfigurationViewSet

router = DefaultRouter()
router.register(r'settings', SystemSettingViewSet, basename='system-setting')
router.register(r'choice-options', ChoiceOptionViewSet, basename='choice-option')
router.register(r'filter-configs', FilterConfigurationViewSet, basename='filter-config')

urlpatterns = [
    path('', include(router.urls)),
]
