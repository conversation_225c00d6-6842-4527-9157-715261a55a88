import React from 'react';
import { Shield, CheckCircle, AlertCircle, FileText, Clock } from 'lucide-react';

const BackgroundCheckStep = ({ data, onChange, errors }) => {
  return (
    <div className="space-y-6">
      {/* Background Check Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start">
          <Shield className="h-6 w-6 text-blue-600 mt-1 mr-3" />
          <div>
            <h3 className="text-lg font-medium text-blue-800 mb-2">
              Background Verification Process
            </h3>
            <p className="text-blue-700 mb-4">
              We conduct a comprehensive background check to ensure the safety and security 
              of our platform. This process typically takes 24-48 hours to complete.
            </p>
            <div className="space-y-2">
              <div className="flex items-center text-sm text-blue-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span>Identity verification</span>
              </div>
              <div className="flex items-center text-sm text-blue-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span>Criminal history check</span>
              </div>
              <div className="flex items-center text-sm text-blue-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span>Driving record verification</span>
              </div>
              <div className="flex items-center text-sm text-blue-700">
                <CheckCircle className="h-4 w-4 mr-2" />
                <span>Employment history (if applicable)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Consent Form */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          Background Check Authorization
        </h3>
        
        <div className="space-y-4">
          {/* Consent Checkbox */}
          <div className="flex items-start">
            <input
              type="checkbox"
              id="consent"
              checked={data.consentGiven}
              onChange={(e) => onChange('consentGiven', e.target.checked)}
              className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="consent" className="ml-3 text-sm text-gray-700">
              <span className="font-medium">I authorize and consent</span> to the completion of a 
              background check including but not limited to criminal history, driving record, 
              and identity verification. I understand that this information will be used to 
              determine my eligibility as a delivery agent.
            </label>
          </div>
          
          {errors.consent && (
            <div className="flex items-center text-red-600">
              <AlertCircle className="h-4 w-4 mr-1" />
              <span className="text-sm">{errors.consent}</span>
            </div>
          )}

          {/* Criminal History Declaration */}
          <div className="border-t pt-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">
              Criminal History Declaration
            </h4>
            <div className="space-y-3">
              <div className="flex items-start">
                <input
                  type="checkbox"
                  id="criminalHistory"
                  checked={data.criminalHistory}
                  onChange={(e) => onChange('criminalHistory', e.target.checked)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="criminalHistory" className="ml-3 text-sm text-gray-700">
                  I have been convicted of a criminal offense (excluding minor traffic violations)
                </label>
              </div>
              
              {data.criminalHistory && (
                <div className="ml-7 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-sm text-yellow-800 mb-2">
                    Please provide details about your criminal history. This will not automatically 
                    disqualify you, but we need this information for our review process.
                  </p>
                  <textarea
                    placeholder="Please provide details..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Driving Record Declaration */}
          <div className="border-t pt-4">
            <h4 className="text-md font-medium text-gray-900 mb-3">
              Driving Record Declaration
            </h4>
            <div className="space-y-3">
              <div className="flex items-start">
                <input
                  type="checkbox"
                  id="drivingRecord"
                  checked={data.drivingRecord}
                  onChange={(e) => onChange('drivingRecord', e.target.checked)}
                  className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="drivingRecord" className="ml-3 text-sm text-gray-700">
                  I have had my driving license suspended, revoked, or have serious traffic violations 
                  in the past 3 years
                </label>
              </div>
              
              {data.drivingRecord && (
                <div className="ml-7 p-3 bg-yellow-50 border border-yellow-200 rounded">
                  <p className="text-sm text-yellow-800 mb-2">
                    Please provide details about your driving record issues.
                  </p>
                  <textarea
                    placeholder="Please provide details..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Process Timeline */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          What Happens Next?
        </h3>
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-blue-600">1</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Background Check Initiated</p>
              <p className="text-sm text-gray-600">
                We'll start the verification process immediately after you submit your application
              </p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <Clock className="h-4 w-4 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Processing Time</p>
              <p className="text-sm text-gray-600">
                Most background checks are completed within 24-48 hours
              </p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-blue-600">3</span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Notification</p>
              <p className="text-sm text-gray-600">
                You'll receive an email notification once the background check is complete
              </p>
            </div>
          </div>
          
          <div className="flex items-start">
            <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-4 w-4 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">Account Activation</p>
              <p className="text-sm text-gray-600">
                Upon successful verification, your account will be activated for deliveries
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Privacy Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
          <div>
            <h3 className="text-sm font-medium text-yellow-800">Privacy & Data Protection</h3>
            <p className="mt-1 text-sm text-yellow-700">
              All background check information is handled in accordance with applicable privacy laws. 
              Your personal information is encrypted and stored securely. We only share necessary 
              information with our verified background check partners.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BackgroundCheckStep;
