import toast from 'react-hot-toast';

/**
 * Toast utility functions for consistent notifications across the app
 */

// Success toast
export const showSuccess = (message, options = {}) => {
  return toast.success(message, {
    duration: 3000,
    style: {
      background: '#10B981',
      color: '#fff',
      borderRadius: '8px',
      padding: '16px',
      fontSize: '14px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    iconTheme: {
      primary: '#fff',
      secondary: '#10B981',
    },
    ...options,
  });
};

// Error toast
export const showError = (message, options = {}) => {
  return toast.error(message, {
    duration: 5000,
    style: {
      background: '#EF4444',
      color: '#fff',
      borderRadius: '8px',
      padding: '16px',
      fontSize: '14px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    iconTheme: {
      primary: '#fff',
      secondary: '#EF4444',
    },
    ...options,
  });
};

// Warning toast
export const showWarning = (message, options = {}) => {
  return toast(message, {
    duration: 4000,
    icon: '⚠️',
    style: {
      background: '#F59E0B',
      color: '#fff',
      borderRadius: '8px',
      padding: '16px',
      fontSize: '14px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    ...options,
  });
};

// Info toast
export const showInfo = (message, options = {}) => {
  return toast(message, {
    duration: 4000,
    icon: 'ℹ️',
    style: {
      background: '#3B82F6',
      color: '#fff',
      borderRadius: '8px',
      padding: '16px',
      fontSize: '14px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    ...options,
  });
};

// Loading toast
export const showLoading = (message, options = {}) => {
  return toast.loading(message, {
    style: {
      background: '#6B7280',
      color: '#fff',
      borderRadius: '8px',
      padding: '16px',
      fontSize: '14px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    ...options,
  });
};

// Promise toast - for async operations
export const showPromise = (promise, messages, options = {}) => {
  return toast.promise(
    promise,
    {
      loading: messages.loading || 'Loading...',
      success: messages.success || 'Success!',
      error: messages.error || 'Something went wrong!',
    },
    {
      style: {
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px',
        fontWeight: '500',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      },
      success: {
        duration: 3000,
        style: {
          background: '#10B981',
          color: '#fff',
        },
        iconTheme: {
          primary: '#fff',
          secondary: '#10B981',
        },
      },
      error: {
        duration: 5000,
        style: {
          background: '#EF4444',
          color: '#fff',
        },
        iconTheme: {
          primary: '#fff',
          secondary: '#EF4444',
        },
      },
      loading: {
        style: {
          background: '#6B7280',
          color: '#fff',
        },
      },
      ...options,
    }
  );
};

// Custom toast with custom styling
export const showCustom = (message, options = {}) => {
  return toast(message, {
    duration: 4000,
    style: {
      background: '#363636',
      color: '#fff',
      borderRadius: '8px',
      padding: '16px',
      fontSize: '14px',
      fontWeight: '500',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    },
    ...options,
  });
};

// Dismiss all toasts
export const dismissAll = () => {
  toast.dismiss();
};

// Dismiss specific toast
export const dismiss = (toastId) => {
  toast.dismiss(toastId);
};

// Authentication specific toasts
export const authToasts = {
  loginSuccess: (userName) => showSuccess(`Welcome back, ${userName}!`),
  loginError: (error) => showError(error || 'Login failed. Please try again.'),
  
  registerSuccess: (email) => showSuccess(`Registration successful! Please check ${email} for verification.`),
  registerError: (error) => showError(error || 'Registration failed. Please try again.'),
  
  verificationSuccess: () => showSuccess('Email verified successfully! You can now login.'),
  verificationError: (error) => showError(error || 'Email verification failed. Please try again.'),
  
  logoutSuccess: () => showInfo('You have been logged out successfully.'),
  
  passwordChangeSuccess: () => showSuccess('Password changed successfully!'),
  passwordChangeError: (error) => showError(error || 'Password change failed. Please try again.'),
  
  otpSent: (email) => showInfo(`OTP sent to ${email}. Please check your email.`),
  otpError: (error) => showError(error || 'Failed to send OTP. Please try again.'),
};

// Form validation toasts
export const validationToasts = {
  required: (field) => showWarning(`${field} is required.`),
  invalid: (field) => showWarning(`Please enter a valid ${field}.`),
  mismatch: (field1, field2) => showWarning(`${field1} and ${field2} do not match.`),
  tooShort: (field, minLength) => showWarning(`${field} must be at least ${minLength} characters long.`),
  tooLong: (field, maxLength) => showWarning(`${field} cannot exceed ${maxLength} characters.`),
};

// API operation toasts
export const apiToasts = {
  loading: (operation) => showLoading(`${operation}...`),
  success: (operation) => showSuccess(`${operation} successful!`),
  error: (operation, error) => showError(error || `${operation} failed. Please try again.`),
};

export default {
  success: showSuccess,
  error: showError,
  warning: showWarning,
  info: showInfo,
  loading: showLoading,
  promise: showPromise,
  custom: showCustom,
  dismiss,
  dismissAll,
  auth: authToasts,
  validation: validationToasts,
  api: apiToasts,
};
