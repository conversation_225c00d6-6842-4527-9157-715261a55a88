import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "./AuthContext";

const SocialContext = createContext();

export const useSocial = () => {
  const context = useContext(SocialContext);
  if (!context) {
    throw new Error("useSocial must be used within a SocialProvider");
  }
  return context;
};

export const SocialProvider = ({ children }) => {
  const { user } = useAuth();
  const [friends, setFriends] = useState([]);
  const [referrals, setReferrals] = useState([]);
  const [socialActivity, setSocialActivity] = useState([]);
  const [userReviews, setUserReviews] = useState([]);
  const [sharedContent, setSharedContent] = useState([]);

  // Load social data from localStorage
  useEffect(() => {
    if (user) {
      const savedFriends = localStorage.getItem(`social_friends_${user.id}`);
      if (savedFriends) {
        setFriends(JSON.parse(savedFriends));
      }

      const savedReferrals = localStorage.getItem(
        `social_referrals_${user.id}`
      );
      if (savedReferrals) {
        setReferrals(JSON.parse(savedReferrals));
      }

      const savedActivity = localStorage.getItem(`social_activity_${user.id}`);
      if (savedActivity) {
        setSocialActivity(JSON.parse(savedActivity));
      }

      const savedReviews = localStorage.getItem(`user_reviews_${user.id}`);
      if (savedReviews) {
        setUserReviews(JSON.parse(savedReviews));
      }
    }
  }, [user]);

  // Save social data to localStorage
  useEffect(() => {
    if (user && friends.length >= 0) {
      localStorage.setItem(
        `social_friends_${user.id}`,
        JSON.stringify(friends)
      );
    }
  }, [friends, user]);

  useEffect(() => {
    if (user && referrals.length >= 0) {
      localStorage.setItem(
        `social_referrals_${user.id}`,
        JSON.stringify(referrals)
      );
    }
  }, [referrals, user]);

  useEffect(() => {
    if (user && socialActivity.length >= 0) {
      localStorage.setItem(
        `social_activity_${user.id}`,
        JSON.stringify(socialActivity)
      );
    }
  }, [socialActivity, user]);

  useEffect(() => {
    if (user && userReviews.length >= 0) {
      localStorage.setItem(
        `user_reviews_${user.id}`,
        JSON.stringify(userReviews)
      );
    }
  }, [userReviews, user]);

  // Referral System
  const generateReferralCode = () => {
    if (!user) return null;
    return `${user.name
      .replace(/\s+/g, "")
      .toUpperCase()
      .slice(0, 3)}${user.id.slice(-4)}`;
  };

  const sendReferral = (email, method = "email") => {
    const referralCode = generateReferralCode();
    const referralLink = `${window.location.origin}/register?ref=${referralCode}`;

    const newReferral = {
      id: `ref-${Date.now()}`,
      email,
      method,
      code: referralCode,
      link: referralLink,
      status: "sent",
      sentAt: new Date().toISOString(),
      reward: 10, // $10 reward for both referrer and referee
    };

    setReferrals((prev) => [newReferral, ...prev]);

    // Add to social activity
    addSocialActivity("referral_sent", {
      email,
      code: referralCode,
    });

    return newReferral;
  };

  const processReferralSignup = (referralCode) => {
    const referral = referrals.find((ref) => ref.code === referralCode);
    if (referral) {
      setReferrals((prev) =>
        prev.map((ref) =>
          ref.code === referralCode
            ? {
                ...ref,
                status: "completed",
                completedAt: new Date().toISOString(),
              }
            : ref
        )
      );

      addSocialActivity("referral_completed", {
        code: referralCode,
        reward: referral.reward,
      });

      return referral.reward;
    }
    return 0;
  };

  // Social Activity
  const addSocialActivity = (type, data) => {
    const activity = {
      id: `activity-${Date.now()}`,
      userId: user?.id,
      userName: user?.name,
      userAvatar: user?.avatar,
      type,
      data,
      timestamp: new Date().toISOString(),
    };

    setSocialActivity((prev) => [activity, ...prev.slice(0, 49)]); // Keep last 50 activities
    return activity;
  };

  // Photo Reviews
  const addPhotoReview = (
    restaurantId,
    restaurantName,
    rating,
    comment,
    photos
  ) => {
    const review = {
      id: `review-${Date.now()}`,
      userId: user?.id,
      userName: user?.name,
      userAvatar: user?.avatar,
      restaurantId,
      restaurantName,
      rating,
      comment,
      photos: photos || [],
      likes: 0,
      helpful: 0,
      createdAt: new Date().toISOString(),
    };

    setUserReviews((prev) => [review, ...prev]);

    addSocialActivity("photo_review", {
      restaurantId,
      restaurantName,
      rating,
      photoCount: photos?.length || 0,
    });

    return review;
  };

  const likeReview = (reviewId) => {
    setUserReviews((prev) =>
      prev.map((review) =>
        review.id === reviewId ? { ...review, likes: review.likes + 1 } : review
      )
    );
  };

  const markReviewHelpful = (reviewId) => {
    setUserReviews((prev) =>
      prev.map((review) =>
        review.id === reviewId
          ? { ...review, helpful: review.helpful + 1 }
          : review
      )
    );
  };

  // Social Sharing
  const shareRestaurant = (restaurant, platform) => {
    const shareData = {
      id: `share-${Date.now()}`,
      type: "restaurant",
      restaurantId: restaurant.id,
      restaurantName: restaurant.name,
      platform,
      sharedAt: new Date().toISOString(),
    };

    setSharedContent((prev) => [shareData, ...prev]);

    addSocialActivity("restaurant_shared", {
      restaurantId: restaurant.id,
      restaurantName: restaurant.name,
      platform,
    });

    // Generate share content
    const shareText = `Check out ${restaurant.name} on Afghan Sofra! 🍽️ Rated ${restaurant.rating}⭐ with ${restaurant.deliveryTime} delivery. Order now!`;
    const shareUrl = `${window.location.origin}/restaurants/${restaurant.id}`;

    return {
      text: shareText,
      url: shareUrl,
      ...shareData,
    };
  };

  const shareOrder = (order, platform) => {
    const shareData = {
      id: `share-${Date.now()}`,
      type: "order",
      orderId: order.id,
      restaurantName: order.restaurantName,
      platform,
      sharedAt: new Date().toISOString(),
    };

    setSharedContent((prev) => [shareData, ...prev]);

    addSocialActivity("order_shared", {
      orderId: order.id,
      restaurantName: order.restaurantName,
      platform,
    });

    const shareText = `Just ordered from ${order.restaurantName} on Afghan Sofra! 🍽️ Delicious food delivered fast!`;
    const shareUrl = `${window.location.origin}/restaurants/${order.restaurantId}`;

    return {
      text: shareText,
      url: shareUrl,
      ...shareData,
    };
  };

  // Social Login Integration
  const connectSocialAccount = (provider, accountData) => {
    // This would integrate with actual social login providers
    addSocialActivity("social_connected", {
      provider,
      accountName: accountData.name,
    });
  };

  // Friend System (Basic)
  const addFriend = (friendData) => {
    const friend = {
      id: `friend-${Date.now()}`,
      ...friendData,
      addedAt: new Date().toISOString(),
      status: "connected",
    };

    setFriends((prev) => [friend, ...prev]);

    addSocialActivity("friend_added", {
      friendName: friendData.name,
      friendId: friendData.id,
    });

    return friend;
  };

  // Statistics
  const getSocialStats = () => {
    const completedReferrals = referrals.filter(
      (ref) => ref.status === "completed"
    ).length;
    const totalRewards = completedReferrals * 10; // $10 per referral
    const totalReviews = userReviews.length;
    const totalShares = sharedContent.length;
    const totalFriends = friends.length;

    return {
      referrals: {
        sent: referrals.length,
        completed: completedReferrals,
        pending: referrals.filter((ref) => ref.status === "sent").length,
        totalRewards,
      },
      reviews: {
        total: totalReviews,
        withPhotos: userReviews.filter((review) => review.photos?.length > 0)
          .length,
        averageRating:
          totalReviews > 0
            ? userReviews.reduce((sum, review) => sum + review.rating, 0) /
              totalReviews
            : 0,
      },
      social: {
        shares: totalShares,
        friends: totalFriends,
        activities: socialActivity.length,
      },
    };
  };

  const value = {
    // Data
    friends,
    referrals,
    socialActivity,
    userReviews,
    sharedContent,

    // Referral functions
    generateReferralCode,
    sendReferral,
    processReferralSignup,

    // Social activity
    addSocialActivity,

    // Reviews
    addPhotoReview,
    likeReview,
    markReviewHelpful,

    // Sharing
    shareRestaurant,
    shareOrder,

    // Social login
    connectSocialAccount,

    // Friends
    addFriend,

    // Stats
    getSocialStats,
  };

  return (
    <SocialContext.Provider value={value}>{children}</SocialContext.Provider>
  );
};

export default SocialContext;
