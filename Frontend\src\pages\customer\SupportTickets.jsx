import React, { useState } from 'react';
import { Ticket, MessageSquare, Clock, CheckCircle, AlertCircle, Plus, Search } from 'lucide-react';
import { useSupport } from '../../context/SupportContext';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Badge from '../../components/common/Badge';

const SupportTickets = () => {
  const { tickets, getTicketsByStatus, searchTickets } = useSupport();
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTicket, setSelectedTicket] = useState(null);

  const getDisplayTickets = () => {
    if (searchQuery) {
      return searchTickets(searchQuery);
    }

    switch (activeTab) {
      case 'open':
        return getTicketsByStatus('open');
      case 'closed':
        return getTicketsByStatus('closed');
      case 'pending':
        return getTicketsByStatus('pending');
      default:
        return tickets;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'open':
        return <Badge variant="info">Open</Badge>;
      case 'pending':
        return <Badge variant="warning">Pending</Badge>;
      case 'closed':
        return <Badge variant="success">Closed</Badge>;
      case 'resolved':
        return <Badge variant="success">Resolved</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority) => {
    switch (priority) {
      case 'high':
        return <Badge variant="danger" size="small">High</Badge>;
      case 'medium':
        return <Badge variant="warning" size="small">Medium</Badge>;
      case 'low':
        return <Badge variant="success" size="small">Low</Badge>;
      default:
        return <Badge variant="secondary" size="small">{priority}</Badge>;
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return 'Today';
    } else if (diffDays === 2) {
      return 'Yesterday';
    } else if (diffDays <= 7) {
      return `${diffDays - 1} days ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const displayTickets = getDisplayTickets();
  const openTickets = getTicketsByStatus('open');
  const pendingTickets = getTicketsByStatus('pending');
  const closedTickets = getTicketsByStatus('closed');

  return (
    <div className="container mx-auto px-4 py-8 animate-fade-in">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold mb-2">Support Tickets</h1>
          <p className="text-gray-600">Manage your support requests and track their progress</p>
        </div>
        <Button
          variant="primary"
          icon={<Plus size={16} />}
          to="/support/contact"
        >
          New Ticket
        </Button>
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search tickets..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6 w-fit">
        <button
          onClick={() => setActiveTab('all')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'all'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          All ({tickets.length})
        </button>
        <button
          onClick={() => setActiveTab('open')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'open'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Open ({openTickets.length})
        </button>
        <button
          onClick={() => setActiveTab('pending')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'pending'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Pending ({pendingTickets.length})
        </button>
        <button
          onClick={() => setActiveTab('closed')}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'closed'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          Closed ({closedTickets.length})
        </button>
      </div>

      {/* Tickets List */}
      {displayTickets.length === 0 ? (
        <Card className="text-center py-16">
          <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Ticket size={40} className="text-gray-400" />
          </div>
          <h3 className="text-xl font-medium mb-2">
            {searchQuery ? 'No tickets found' : 'No support tickets'}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchQuery 
              ? 'Try adjusting your search terms'
              : "You haven't created any support tickets yet."
            }
          </p>
          <Button variant="primary" to="/support/contact">
            Create Your First Ticket
          </Button>
        </Card>
      ) : (
        <div className="space-y-4">
          {displayTickets.map((ticket) => (
            <Card key={ticket.id} className="p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center mb-2">
                    <h3 className="font-semibold text-lg mr-3">{ticket.subject}</h3>
                    {getStatusBadge(ticket.status)}
                    {getPriorityBadge(ticket.priority)}
                  </div>
                  
                  <p className="text-gray-600 mb-3 line-clamp-2">
                    {ticket.description}
                  </p>
                  
                  <div className="flex items-center text-sm text-gray-500 space-x-4">
                    <div className="flex items-center">
                      <Ticket size={14} className="mr-1" />
                      <span>#{ticket.id.slice(-8)}</span>
                    </div>
                    <div className="flex items-center">
                      <Clock size={14} className="mr-1" />
                      <span>{formatDate(ticket.createdAt)}</span>
                    </div>
                    <div className="flex items-center">
                      <MessageSquare size={14} className="mr-1" />
                      <span>{ticket.messages?.length || 0} messages</span>
                    </div>
                    {ticket.orderId && (
                      <div className="flex items-center">
                        <span>Order: {ticket.orderId}</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="ml-4">
                  <Button
                    variant="outline"
                    size="small"
                    onClick={() => setSelectedTicket(ticket)}
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Ticket Detail Modal */}
      {selectedTicket && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-bold">{selectedTicket.subject}</h2>
                  <p className="text-gray-600">Ticket #{selectedTicket.id.slice(-8)}</p>
                </div>
                <button
                  onClick={() => setSelectedTicket(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="flex items-center space-x-3 mb-6">
                {getStatusBadge(selectedTicket.status)}
                {getPriorityBadge(selectedTicket.priority)}
                <span className="text-sm text-gray-500">
                  Created {formatDate(selectedTicket.createdAt)}
                </span>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Description</h3>
                  <p className="text-gray-700 bg-gray-50 p-4 rounded-lg">
                    {selectedTicket.description}
                  </p>
                </div>

                {selectedTicket.messages && selectedTicket.messages.length > 0 && (
                  <div>
                    <h3 className="font-medium mb-4">Messages</h3>
                    <div className="space-y-3">
                      {selectedTicket.messages.map((message) => (
                        <div
                          key={message.id}
                          className={`p-4 rounded-lg ${
                            message.sender === 'user'
                              ? 'bg-primary-50 border-l-4 border-primary-500'
                              : 'bg-gray-50 border-l-4 border-gray-400'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium">{message.senderName}</span>
                            <span className="text-sm text-gray-500">
                              {formatDate(message.timestamp)}
                            </span>
                          </div>
                          <p className="text-gray-700">{message.message}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={() => setSelectedTicket(null)}
                  fullWidth
                >
                  Close
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default SupportTickets;
