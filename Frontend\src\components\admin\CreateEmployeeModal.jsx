import React, { useState } from "react";
import {
  X,
  User,
  Phone,
  MapPin,
  Car,
  DollarSign,
  Clock,
  FileText,
  Save,
  AlertCircle,
} from "lucide-react";
import Card from "../common/Card";
import Button from "../common/Button";
import Input from "../common/Input";
import { adminApi } from "../../services/adminApi";

const CreateEmployeeModal = ({ isOpen, onClose, onSuccess }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const [formData, setFormData] = useState({
    // Personal Information
    full_name: "",
    father_name: "",
    national_id: "",
    date_of_birth: "",
    gender: "male",
    marital_status: "single",

    // Contact Information
    phone_number: "",
    secondary_phone: "",
    email: "",

    // Address Information
    province: "",
    district: "",
    area: "",
    street_address: "",
    nearby_landmark: "",

    // Vehicle Information
    vehicle_type: "motorcycle",
    vehicle_model: "",
    vehicle_year: "",
    license_plate: "",
    vehicle_color: "",
    driving_license: "",

    // Employment Information
    hire_date: new Date().toISOString().split("T")[0],
    salary_type: "monthly",
    base_salary: "15000",
    commission_per_delivery: "100",
    hourly_rate: "0",

    // Work Schedule
    work_schedule: "full_time",
    shift_start_time: "08:00",
    shift_end_time: "17:00",
    working_days: "monday_to_saturday",

    // Banking Information
    bank_name: "",
    account_number: "",
    account_holder_name: "",
    mobile_wallet: "",

    // Emergency Contact
    emergency_contact: "",
    emergency_relation: "",

    // References (Required)
    reference1_name: "",
    reference1_phone: "",
    reference1_relation: "",
    reference2_name: "",
    reference2_phone: "",
    reference2_relation: "",

    // Admin Notes
    admin_notes: "",
  });

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};

    switch (step) {
      case 1: // Personal Information
        if (!formData.full_name) newErrors.full_name = "Full name is required";
        if (!formData.father_name)
          newErrors.father_name = "Father name is required";
        if (!formData.national_id)
          newErrors.national_id = "National ID is required";
        if (!formData.date_of_birth)
          newErrors.date_of_birth = "Date of birth is required";
        break;

      case 2: // Contact & Address
        if (!formData.phone_number)
          newErrors.phone_number = "Phone number is required";
        if (!formData.province) newErrors.province = "Province is required";
        if (!formData.district) newErrors.district = "District is required";
        if (!formData.area) newErrors.area = "Area is required";
        break;

      case 3: // Vehicle Information
        if (!formData.vehicle_type)
          newErrors.vehicle_type = "Vehicle type is required";
        break;

      case 4: // Employment Information
        if (!formData.hire_date) newErrors.hire_date = "Hire date is required";
        if (!formData.salary_type)
          newErrors.salary_type = "Salary type is required";
        if (formData.salary_type === "monthly" && !formData.base_salary) {
          newErrors.base_salary = "Base salary is required";
        }
        break;

      case 5: // References
        if (!formData.reference1_name)
          newErrors.reference1_name = "Reference 1 name is required";
        if (!formData.reference1_phone)
          newErrors.reference1_phone = "Reference 1 phone is required";
        if (!formData.reference1_relation)
          newErrors.reference1_relation = "Reference 1 relation is required";
        if (!formData.reference2_name)
          newErrors.reference2_name = "Reference 2 name is required";
        if (!formData.reference2_phone)
          newErrors.reference2_phone = "Reference 2 phone is required";
        if (!formData.reference2_relation)
          newErrors.reference2_relation = "Reference 2 relation is required";
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    try {
      setLoading(true);
      const response = await adminApi.createDeliveryEmployee(formData);

      if (response.success) {
        onSuccess(response.data);
      } else {
        setErrors({
          submit: response.error?.message || "Failed to create employee",
        });
      }
    } catch (error) {
      setErrors({ submit: "Failed to create employee. Please try again." });
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold text-gray-900 flex items-center'>
              <User className='mr-2' size={20} />
              Personal Information
            </h3>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <Input
                label='Full Name *'
                value={formData.full_name}
                onChange={(e) => handleInputChange("full_name", e.target.value)}
                error={errors.full_name}
                placeholder='Ahmad Ali'
              />

              <Input
                label="Father's Name *"
                value={formData.father_name}
                onChange={(e) =>
                  handleInputChange("father_name", e.target.value)
                }
                error={errors.father_name}
                placeholder='Mohammad Ali'
              />

              <Input
                label='National ID (Tazkira) *'
                value={formData.national_id}
                onChange={(e) =>
                  handleInputChange("national_id", e.target.value)
                }
                error={errors.national_id}
                placeholder='**********123'
              />

              <Input
                label='Date of Birth *'
                type='date'
                value={formData.date_of_birth}
                onChange={(e) =>
                  handleInputChange("date_of_birth", e.target.value)
                }
                error={errors.date_of_birth}
              />

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Gender
                </label>
                <select
                  value={formData.gender}
                  onChange={(e) => handleInputChange("gender", e.target.value)}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                >
                  <option value='male'>Male</option>
                  <option value='female'>Female</option>
                </select>
              </div>

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Marital Status
                </label>
                <select
                  value={formData.marital_status}
                  onChange={(e) =>
                    handleInputChange("marital_status", e.target.value)
                  }
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                >
                  <option value='single'>Single</option>
                  <option value='married'>Married</option>
                  <option value='divorced'>Divorced</option>
                  <option value='widowed'>Widowed</option>
                </select>
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold text-gray-900 flex items-center'>
              <Phone className='mr-2' size={20} />
              Contact & Address Information
            </h3>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <Input
                label='Phone Number *'
                value={formData.phone_number}
                onChange={(e) =>
                  handleInputChange("phone_number", e.target.value)
                }
                error={errors.phone_number}
                placeholder='+93 70 123 4567'
              />

              <Input
                label='Secondary Phone'
                value={formData.secondary_phone}
                onChange={(e) =>
                  handleInputChange("secondary_phone", e.target.value)
                }
                placeholder='+93 78 123 4567'
              />

              <Input
                label='Email'
                type='email'
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                placeholder='<EMAIL>'
              />

              <Input
                label='Province *'
                value={formData.province}
                onChange={(e) => handleInputChange("province", e.target.value)}
                error={errors.province}
                placeholder='Kabul'
              />

              <Input
                label='District *'
                value={formData.district}
                onChange={(e) => handleInputChange("district", e.target.value)}
                error={errors.district}
                placeholder='District 1'
              />

              <Input
                label='Area *'
                value={formData.area}
                onChange={(e) => handleInputChange("area", e.target.value)}
                error={errors.area}
                placeholder='Wazir Akbar Khan'
              />

              <Input
                label='Street Address'
                value={formData.street_address}
                onChange={(e) =>
                  handleInputChange("street_address", e.target.value)
                }
                placeholder='Street 15, House 123'
                className='md:col-span-2'
              />

              <Input
                label='Nearby Landmark'
                value={formData.nearby_landmark}
                onChange={(e) =>
                  handleInputChange("nearby_landmark", e.target.value)
                }
                placeholder='Near Blue Mosque'
                className='md:col-span-2'
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold text-gray-900 flex items-center'>
              <Car className='mr-2' size={20} />
              Vehicle Information
            </h3>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Vehicle Type *
                </label>
                <select
                  value={formData.vehicle_type}
                  onChange={(e) =>
                    handleInputChange("vehicle_type", e.target.value)
                  }
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                >
                  <option value='motorcycle'>Motorcycle</option>
                  <option value='bicycle'>Bicycle</option>
                  <option value='car'>Car</option>
                  <option value='van'>Van</option>
                </select>
              </div>

              <Input
                label='Vehicle Model'
                value={formData.vehicle_model}
                onChange={(e) =>
                  handleInputChange("vehicle_model", e.target.value)
                }
                placeholder='Honda 125'
              />

              <Input
                label='Vehicle Year'
                type='number'
                value={formData.vehicle_year}
                onChange={(e) =>
                  handleInputChange("vehicle_year", e.target.value)
                }
                placeholder='2020'
              />

              <Input
                label='License Plate'
                value={formData.license_plate}
                onChange={(e) =>
                  handleInputChange("license_plate", e.target.value)
                }
                placeholder='KBL-123'
              />

              <Input
                label='Vehicle Color'
                value={formData.vehicle_color}
                onChange={(e) =>
                  handleInputChange("vehicle_color", e.target.value)
                }
                placeholder='Red'
              />

              <Input
                label='Driving License Number'
                value={formData.driving_license}
                onChange={(e) =>
                  handleInputChange("driving_license", e.target.value)
                }
                placeholder='DL123456'
              />
            </div>
          </div>
        );

      case 4:
        return (
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold text-gray-900 flex items-center'>
              <DollarSign className='mr-2' size={20} />
              Employment & Compensation
            </h3>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <Input
                label='Hire Date *'
                type='date'
                value={formData.hire_date}
                onChange={(e) => handleInputChange("hire_date", e.target.value)}
                error={errors.hire_date}
              />

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Salary Type *
                </label>
                <select
                  value={formData.salary_type}
                  onChange={(e) =>
                    handleInputChange("salary_type", e.target.value)
                  }
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                >
                  <option value='monthly'>Monthly Salary</option>
                  <option value='hourly'>Hourly Rate</option>
                  <option value='commission'>Commission Only</option>
                  <option value='hybrid'>Salary + Commission</option>
                </select>
              </div>

              {(formData.salary_type === "monthly" ||
                formData.salary_type === "hybrid") && (
                <Input
                  label='Base Salary (AFN) *'
                  type='number'
                  value={formData.base_salary}
                  onChange={(e) =>
                    handleInputChange("base_salary", e.target.value)
                  }
                  error={errors.base_salary}
                  placeholder='15000'
                />
              )}

              {(formData.salary_type === "commission" ||
                formData.salary_type === "hybrid") && (
                <Input
                  label='Commission per Delivery (AFN)'
                  type='number'
                  value={formData.commission_per_delivery}
                  onChange={(e) =>
                    handleInputChange("commission_per_delivery", e.target.value)
                  }
                  placeholder='100'
                />
              )}

              {formData.salary_type === "hourly" && (
                <Input
                  label='Hourly Rate (AFN)'
                  type='number'
                  value={formData.hourly_rate}
                  onChange={(e) =>
                    handleInputChange("hourly_rate", e.target.value)
                  }
                  placeholder='200'
                />
              )}

              <div>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Work Schedule
                </label>
                <select
                  value={formData.work_schedule}
                  onChange={(e) =>
                    handleInputChange("work_schedule", e.target.value)
                  }
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                >
                  <option value='full_time'>Full Time (8 hours)</option>
                  <option value='part_time'>Part Time (4-6 hours)</option>
                  <option value='flexible'>Flexible Hours</option>
                </select>
              </div>

              <Input
                label='Shift Start Time'
                type='time'
                value={formData.shift_start_time}
                onChange={(e) =>
                  handleInputChange("shift_start_time", e.target.value)
                }
              />

              <Input
                label='Shift End Time'
                type='time'
                value={formData.shift_end_time}
                onChange={(e) =>
                  handleInputChange("shift_end_time", e.target.value)
                }
              />
            </div>
          </div>
        );

      case 5:
        return (
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold text-gray-900 flex items-center'>
              <FileText className='mr-2' size={20} />
              Additional Information
            </h3>

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <Input
                label='Bank Name'
                value={formData.bank_name}
                onChange={(e) => handleInputChange("bank_name", e.target.value)}
                placeholder='Afghanistan Bank'
              />

              <Input
                label='Account Number'
                value={formData.account_number}
                onChange={(e) =>
                  handleInputChange("account_number", e.target.value)
                }
                placeholder='**********'
              />

              <Input
                label='Account Holder Name'
                value={formData.account_holder_name}
                onChange={(e) =>
                  handleInputChange("account_holder_name", e.target.value)
                }
                placeholder='Ahmad Ali'
              />

              <Input
                label='Mobile Wallet'
                value={formData.mobile_wallet}
                onChange={(e) =>
                  handleInputChange("mobile_wallet", e.target.value)
                }
                placeholder='M-Paisa: +93 70 123 4567'
              />

              <Input
                label='Emergency Contact'
                value={formData.emergency_contact}
                onChange={(e) =>
                  handleInputChange("emergency_contact", e.target.value)
                }
                placeholder='+93 78 123 4567'
              />

              <Input
                label='Emergency Contact Relation'
                value={formData.emergency_relation}
                onChange={(e) =>
                  handleInputChange("emergency_relation", e.target.value)
                }
                placeholder='Brother'
              />
            </div>

            {/* References Section */}
            <div className='space-y-4'>
              <h4 className='text-md font-semibold text-gray-800'>
                References (Required)
              </h4>

              <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <Input
                  label='Reference 1 Name *'
                  value={formData.reference1_name}
                  onChange={(e) =>
                    handleInputChange("reference1_name", e.target.value)
                  }
                  error={errors.reference1_name}
                  placeholder='Ahmad Shah'
                />

                <Input
                  label='Reference 1 Phone *'
                  value={formData.reference1_phone}
                  onChange={(e) =>
                    handleInputChange("reference1_phone", e.target.value)
                  }
                  error={errors.reference1_phone}
                  placeholder='+93 70 111 1111'
                />

                <Input
                  label='Reference 1 Relation *'
                  value={formData.reference1_relation}
                  onChange={(e) =>
                    handleInputChange("reference1_relation", e.target.value)
                  }
                  error={errors.reference1_relation}
                  placeholder='Friend'
                />

                <Input
                  label='Reference 2 Name *'
                  value={formData.reference2_name}
                  onChange={(e) =>
                    handleInputChange("reference2_name", e.target.value)
                  }
                  error={errors.reference2_name}
                  placeholder='Ali Khan'
                />

                <Input
                  label='Reference 2 Phone *'
                  value={formData.reference2_phone}
                  onChange={(e) =>
                    handleInputChange("reference2_phone", e.target.value)
                  }
                  error={errors.reference2_phone}
                  placeholder='+93 70 222 2222'
                />

                <Input
                  label='Reference 2 Relation *'
                  value={formData.reference2_relation}
                  onChange={(e) =>
                    handleInputChange("reference2_relation", e.target.value)
                  }
                  error={errors.reference2_relation}
                  placeholder='Neighbor'
                />
              </div>
            </div>

            <div className='space-y-4'>
              <div className='md:col-span-2'>
                <label className='block text-sm font-medium text-gray-700 mb-2'>
                  Admin Notes
                </label>
                <textarea
                  value={formData.admin_notes}
                  onChange={(e) =>
                    handleInputChange("admin_notes", e.target.value)
                  }
                  rows={3}
                  className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500'
                  placeholder='Any additional notes about the employee...'
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
      <div className='bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto'>
        <div className='p-6'>
          {/* Header */}
          <div className='flex items-center justify-between mb-6'>
            <h2 className='text-2xl font-bold text-gray-900'>
              Create New Employee
            </h2>
            <button
              onClick={onClose}
              className='text-gray-400 hover:text-gray-600'
            >
              <X size={24} />
            </button>
          </div>

          {/* Progress Steps */}
          <div className='mb-8'>
            <div className='flex items-center justify-between'>
              {[1, 2, 3, 4, 5].map((step) => (
                <div
                  key={step}
                  className={`flex items-center ${step < 5 ? "flex-1" : ""}`}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      step <= currentStep
                        ? "bg-blue-600 text-white"
                        : "bg-gray-200 text-gray-600"
                    }`}
                  >
                    {step}
                  </div>
                  {step < 5 && (
                    <div
                      className={`flex-1 h-1 mx-2 ${
                        step < currentStep ? "bg-blue-600" : "bg-gray-200"
                      }`}
                    />
                  )}
                </div>
              ))}
            </div>
            <div className='flex justify-between mt-2 text-xs text-gray-500'>
              <span>Personal</span>
              <span>Contact</span>
              <span>Vehicle</span>
              <span>Employment</span>
              <span>Additional</span>
            </div>
          </div>

          {/* Form Content */}
          <div className='mb-8'>{renderStepContent()}</div>

          {/* Error Display */}
          {errors.submit && (
            <div className='mb-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center'>
              <AlertCircle className='text-red-500 mr-2' size={16} />
              <span className='text-red-700'>{errors.submit}</span>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className='flex justify-between'>
            <Button
              variant='outline'
              onClick={handlePrevious}
              disabled={currentStep === 1}
            >
              Previous
            </Button>

            <div className='flex space-x-2'>
              <Button variant='outline' onClick={onClose}>
                Cancel
              </Button>

              {currentStep < 5 ? (
                <Button onClick={handleNext}>Next</Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  loading={loading}
                  icon={<Save size={16} />}
                >
                  Create Employee
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateEmployeeModal;
