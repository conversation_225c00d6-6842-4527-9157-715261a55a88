import { useState, useEffect, useCallback } from 'react';

/**
 * Hook for managing customer location and addresses
 */
export const useLocation = () => {
  const [currentLocation, setCurrentLocation] = useState(null);
  const [addresses, setAddresses] = useState([]);
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [locationError, setLocationError] = useState(null);
  const [permissionStatus, setPermissionStatus] = useState('prompt'); // 'granted', 'denied', 'prompt'

  // Load saved addresses and selected address from localStorage
  useEffect(() => {
    const savedAddresses = localStorage.getItem('afghanSofraAddresses');
    const savedSelectedAddress = localStorage.getItem('afghanSofraSelectedAddress');
    
    if (savedAddresses) {
      const parsedAddresses = JSON.parse(savedAddresses);
      setAddresses(parsedAddresses);
      
      // Set default address if no selected address is saved
      if (!savedSelectedAddress) {
        const defaultAddress = parsedAddresses.find(addr => addr.isDefault);
        if (defaultAddress) {
          setSelectedAddress(defaultAddress);
          localStorage.setItem('afghanSofraSelectedAddress', JSON.stringify(defaultAddress));
        }
      } else {
        setSelectedAddress(JSON.parse(savedSelectedAddress));
      }
    }
  }, []);

  // Check geolocation permission status
  const checkPermissionStatus = useCallback(async () => {
    if ('permissions' in navigator) {
      try {
        const permission = await navigator.permissions.query({ name: 'geolocation' });
        setPermissionStatus(permission.state);
        
        permission.addEventListener('change', () => {
          setPermissionStatus(permission.state);
        });
      } catch (error) {
        console.error('Error checking geolocation permission:', error);
      }
    }
  }, []);

  // Get current location
  const getCurrentLocation = useCallback(() => {
    return new Promise((resolve, reject) => {
      setIsGettingLocation(true);
      setLocationError(null);

      if (!navigator.geolocation) {
        const error = new Error('Geolocation is not supported by this browser');
        setLocationError(error.message);
        setIsGettingLocation(false);
        reject(error);
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude, accuracy } = position.coords;
          
          try {
            // Get address from coordinates
            const address = await reverseGeocode(latitude, longitude);
            
            const locationData = {
              coordinates: [latitude, longitude],
              lat: latitude,
              lng: longitude,
              accuracy,
              address,
              timestamp: Date.now()
            };

            setCurrentLocation(locationData);
            setIsGettingLocation(false);
            resolve(locationData);
          } catch (error) {
            console.error('Error getting address:', error);
            const locationData = {
              coordinates: [latitude, longitude],
              lat: latitude,
              lng: longitude,
              accuracy,
              address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
              timestamp: Date.now()
            };
            
            setCurrentLocation(locationData);
            setIsGettingLocation(false);
            resolve(locationData);
          }
        },
        (error) => {
          let errorMessage = 'Unable to get your location';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Location access denied by user';
              setPermissionStatus('denied');
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information is unavailable';
              break;
            case error.TIMEOUT:
              errorMessage = 'Location request timed out';
              break;
            default:
              errorMessage = 'An unknown error occurred while getting location';
              break;
          }
          
          setLocationError(errorMessage);
          setIsGettingLocation(false);
          reject(new Error(errorMessage));
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }, []);

  // Reverse geocoding - get address from coordinates
  const reverseGeocode = useCallback(async (lat, lng) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
      );
      const data = await response.json();
      return data.display_name || `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }
  }, []);

  // Forward geocoding - get coordinates from address
  const geocodeAddress = useCallback(async (address) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1&countrycodes=af`
      );
      const data = await response.json();
      
      if (data && data.length > 0) {
        return {
          lat: parseFloat(data[0].lat),
          lng: parseFloat(data[0].lon),
          coordinates: [parseFloat(data[0].lat), parseFloat(data[0].lon)],
          address: data[0].display_name
        };
      }
      
      throw new Error('Address not found');
    } catch (error) {
      console.error('Geocoding error:', error);
      throw error;
    }
  }, []);

  // Add new address
  const addAddress = useCallback((addressData) => {
    const newAddress = {
      id: Date.now().toString(),
      ...addressData,
      isDefault: addresses.length === 0,
      createdAt: Date.now()
    };

    const updatedAddresses = [...addresses, newAddress];
    setAddresses(updatedAddresses);
    localStorage.setItem('afghanSofraAddresses', JSON.stringify(updatedAddresses));

    // Auto-select if it's the first address
    if (addresses.length === 0) {
      setSelectedAddress(newAddress);
      localStorage.setItem('afghanSofraSelectedAddress', JSON.stringify(newAddress));
    }

    return newAddress;
  }, [addresses]);

  // Update existing address
  const updateAddress = useCallback((addressId, updates) => {
    const updatedAddresses = addresses.map(addr =>
      addr.id === addressId ? { ...addr, ...updates } : addr
    );
    
    setAddresses(updatedAddresses);
    localStorage.setItem('afghanSofraAddresses', JSON.stringify(updatedAddresses));

    // Update selected address if it was the one being updated
    if (selectedAddress?.id === addressId) {
      const updatedSelected = { ...selectedAddress, ...updates };
      setSelectedAddress(updatedSelected);
      localStorage.setItem('afghanSofraSelectedAddress', JSON.stringify(updatedSelected));
    }
  }, [addresses, selectedAddress]);

  // Delete address
  const deleteAddress = useCallback((addressId) => {
    const updatedAddresses = addresses.filter(addr => addr.id !== addressId);
    setAddresses(updatedAddresses);
    localStorage.setItem('afghanSofraAddresses', JSON.stringify(updatedAddresses));

    // If deleted address was selected, select default or first available
    if (selectedAddress?.id === addressId) {
      const newSelected = updatedAddresses.find(addr => addr.isDefault) || updatedAddresses[0] || null;
      setSelectedAddress(newSelected);
      
      if (newSelected) {
        localStorage.setItem('afghanSofraSelectedAddress', JSON.stringify(newSelected));
      } else {
        localStorage.removeItem('afghanSofraSelectedAddress');
      }
    }
  }, [addresses, selectedAddress]);

  // Select address
  const selectAddress = useCallback((address) => {
    setSelectedAddress(address);
    localStorage.setItem('afghanSofraSelectedAddress', JSON.stringify(address));
  }, []);

  // Set default address
  const setDefaultAddress = useCallback((addressId) => {
    const updatedAddresses = addresses.map(addr => ({
      ...addr,
      isDefault: addr.id === addressId
    }));
    
    setAddresses(updatedAddresses);
    localStorage.setItem('afghanSofraAddresses', JSON.stringify(updatedAddresses));
  }, [addresses]);

  // Calculate distance between two points
  const calculateDistance = useCallback((lat1, lng1, lat2, lng2) => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c; // Distance in kilometers
  }, []);

  // Check if location is within delivery area
  const isInDeliveryArea = useCallback((lat, lng, deliveryRadius = 10) => {
    // Kabul center coordinates
    const kabulCenter = [34.526, 69.1776];
    const distance = calculateDistance(kabulCenter[0], kabulCenter[1], lat, lng);
    return distance <= deliveryRadius;
  }, [calculateDistance]);

  // Initialize location checking
  useEffect(() => {
    checkPermissionStatus();
  }, [checkPermissionStatus]);

  return {
    // State
    currentLocation,
    addresses,
    selectedAddress,
    isGettingLocation,
    locationError,
    permissionStatus,

    // Actions
    getCurrentLocation,
    reverseGeocode,
    geocodeAddress,
    addAddress,
    updateAddress,
    deleteAddress,
    selectAddress,
    setDefaultAddress,
    calculateDistance,
    isInDeliveryArea,

    // Computed
    hasAddresses: addresses.length > 0,
    hasSelectedAddress: !!selectedAddress,
    canGetLocation: 'geolocation' in navigator && permissionStatus !== 'denied'
  };
};

export default useLocation;
