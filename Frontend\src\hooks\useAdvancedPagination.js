import { useState, useCallback, useEffect, useRef, useMemo } from 'react';

/**
 * Advanced pagination hook with performance optimizations
 */
export const useAdvancedPagination = ({
  fetchFunction,
  initialPage = 1,
  initialPageSize = 20,
  cacheTimeout = 300000, // 5 minutes
  prefetchPages = 1,
  enableAnalytics = false,
}) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSize] = useState(initialPageSize);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Cache and performance tracking
  const cache = useRef(new Map());
  const analytics = useRef({
    totalRequests: 0,
    cacheHits: 0,
    avgResponseTime: 0,
    totalResponseTime: 0,
  });

  // Generate cache key
  const generateCacheKey = useCallback((page, size, filters = {}) => {
    const key = JSON.stringify({ page, size, filters });
    return btoa(key); // Base64 encode for safe key
  }, []);

  // Check cache for data
  const getCachedData = useCallback((cacheKey) => {
    const cached = cache.current.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < cacheTimeout) {
      return cached.data;
    }
    return null;
  }, [cacheTimeout]);

  // Cache data
  const setCachedData = useCallback((cacheKey, data) => {
    cache.current.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });

    // Limit cache size to prevent memory leaks
    if (cache.current.size > 50) {
      const firstKey = cache.current.keys().next().value;
      cache.current.delete(firstKey);
    }
  }, []);

  // Track analytics
  const trackAnalytics = useCallback((responseTime, cacheHit = false) => {
    if (!enableAnalytics) return;

    analytics.current.totalRequests += 1;
    if (cacheHit) {
      analytics.current.cacheHits += 1;
    } else {
      analytics.current.totalResponseTime += responseTime;
      analytics.current.avgResponseTime = 
        analytics.current.totalResponseTime / 
        (analytics.current.totalRequests - analytics.current.cacheHits);
    }
  }, [enableAnalytics]);

  // Fetch data with caching
  const fetchData = useCallback(async (page, size, filters = {}, options = {}) => {
    const cacheKey = generateCacheKey(page, size, filters);
    
    // Check cache first
    const cachedData = getCachedData(cacheKey);
    if (cachedData && !options.forceRefresh) {
      setData(cachedData.results || cachedData);
      setTotalItems(cachedData.count || cachedData.length);
      setTotalPages(cachedData.total_pages || Math.ceil((cachedData.count || cachedData.length) / size));
      trackAnalytics(0, true);
      return cachedData;
    }

    setLoading(true);
    setError(null);

    const startTime = Date.now();

    try {
      const response = await fetchFunction({
        page,
        page_size: size,
        ...filters,
      });

      const responseTime = Date.now() - startTime;

      if (response.success) {
        const responseData = response.data;
        setData(responseData.results || responseData);
        setTotalItems(responseData.count || responseData.length);
        setTotalPages(responseData.total_pages || Math.ceil((responseData.count || responseData.length) / size));

        // Cache the response
        setCachedData(cacheKey, responseData);
        trackAnalytics(responseTime, false);

        return responseData;
      } else {
        throw new Error(response.error || 'Failed to fetch data');
      }
    } catch (err) {
      setError(err.message);
      trackAnalytics(Date.now() - startTime, false);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchFunction, generateCacheKey, getCachedData, setCachedData, trackAnalytics]);

  // Prefetch adjacent pages
  const prefetchAdjacentPages = useCallback(async (currentPage, filters = {}) => {
    const prefetchPromises = [];

    for (let i = 1; i <= prefetchPages; i++) {
      // Prefetch next pages
      if (currentPage + i <= totalPages) {
        const nextCacheKey = generateCacheKey(currentPage + i, pageSize, filters);
        if (!getCachedData(nextCacheKey)) {
          prefetchPromises.push(
            fetchFunction({
              page: currentPage + i,
              page_size: pageSize,
              ...filters,
            }).then(response => {
              if (response.success) {
                setCachedData(nextCacheKey, response.data);
              }
            }).catch(() => {
              // Silently fail prefetch
            })
          );
        }
      }

      // Prefetch previous pages
      if (currentPage - i >= 1) {
        const prevCacheKey = generateCacheKey(currentPage - i, pageSize, filters);
        if (!getCachedData(prevCacheKey)) {
          prefetchPromises.push(
            fetchFunction({
              page: currentPage - i,
              page_size: pageSize,
              ...filters,
            }).then(response => {
              if (response.success) {
                setCachedData(prevCacheKey, response.data);
              }
            }).catch(() => {
              // Silently fail prefetch
            })
          );
        }
      }
    }

    // Execute prefetch requests
    if (prefetchPromises.length > 0) {
      Promise.allSettled(prefetchPromises);
    }
  }, [prefetchPages, totalPages, pageSize, generateCacheKey, getCachedData, setCachedData, fetchFunction]);

  // Handle page change
  const handlePageChange = useCallback(async (newPage, filters = {}) => {
    setCurrentPage(newPage);
    await fetchData(newPage, pageSize, filters);
    
    // Prefetch adjacent pages
    setTimeout(() => {
      prefetchAdjacentPages(newPage, filters);
    }, 100);
  }, [fetchData, pageSize, prefetchAdjacentPages]);

  // Handle page size change
  const handlePageSizeChange = useCallback(async (newPageSize, filters = {}) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
    
    // Clear cache when page size changes
    cache.current.clear();
    
    await fetchData(1, newPageSize, filters);
  }, [fetchData]);

  // Refresh data
  const refresh = useCallback(async (filters = {}) => {
    await fetchData(currentPage, pageSize, filters, { forceRefresh: true });
  }, [fetchData, currentPage, pageSize]);

  // Clear cache
  const clearCache = useCallback(() => {
    cache.current.clear();
  }, []);

  // Get analytics data
  const getAnalytics = useCallback(() => {
    if (!enableAnalytics) return null;

    const { totalRequests, cacheHits, avgResponseTime } = analytics.current;
    return {
      totalRequests,
      cacheHits,
      cacheHitRate: totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0,
      avgResponseTime,
      cacheSize: cache.current.size,
    };
  }, [enableAnalytics]);

  // Initial load
  useEffect(() => {
    fetchData(currentPage, pageSize);
  }, []);

  // Memoized pagination props
  const paginationProps = useMemo(() => ({
    currentPage,
    pageSize,
    totalItems,
    totalPages,
    hasNext: currentPage < totalPages,
    hasPrevious: currentPage > 1,
    onPageChange: handlePageChange,
    onPageSizeChange: handlePageSizeChange,
  }), [currentPage, pageSize, totalItems, totalPages, handlePageChange, handlePageSizeChange]);

  return {
    data,
    loading,
    error,
    ...paginationProps,
    refresh,
    clearCache,
    getAnalytics,
    fetchData,
  };
};

/**
 * Hook for predictive pagination with machine learning-like behavior
 */
export const usePredictivePagination = ({
  fetchFunction,
  initialPage = 1,
  initialPageSize = 20,
  learningEnabled = true,
}) => {
  const [userBehavior, setUserBehavior] = useState({
    pageVisits: {},
    avgTimeOnPage: {},
    commonPageSizes: {},
    navigationPatterns: [],
  });

  const basePagination = useAdvancedPagination({
    fetchFunction,
    initialPage,
    initialPageSize,
    enableAnalytics: true,
  });

  // Track user behavior
  const trackPageVisit = useCallback((page, timeSpent) => {
    if (!learningEnabled) return;

    setUserBehavior(prev => ({
      ...prev,
      pageVisits: {
        ...prev.pageVisits,
        [page]: (prev.pageVisits[page] || 0) + 1,
      },
      avgTimeOnPage: {
        ...prev.avgTimeOnPage,
        [page]: timeSpent,
      },
    }));
  }, [learningEnabled]);

  // Predict next likely pages
  const predictNextPages = useCallback((currentPage) => {
    if (!learningEnabled) return [];

    const { pageVisits, navigationPatterns } = userBehavior;
    
    // Simple prediction based on visit frequency
    const predictions = [];
    
    // Most visited pages
    const sortedPages = Object.entries(pageVisits)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([page]) => parseInt(page));

    // Adjacent pages (common pattern)
    predictions.push(currentPage + 1, currentPage - 1);
    
    // Add most visited pages
    predictions.push(...sortedPages);

    // Remove duplicates and invalid pages
    return [...new Set(predictions)]
      .filter(page => page > 0 && page !== currentPage)
      .slice(0, 3);
  }, [learningEnabled, userBehavior]);

  // Enhanced page change with learning
  const handlePageChange = useCallback(async (newPage, filters = {}) => {
    const startTime = Date.now();
    
    await basePagination.onPageChange(newPage, filters);
    
    // Track behavior
    const timeSpent = Date.now() - startTime;
    trackPageVisit(newPage, timeSpent);

    // Predictive prefetching
    const predictedPages = predictNextPages(newPage);
    // Implement predictive prefetching here if needed
    
  }, [basePagination.onPageChange, trackPageVisit, predictNextPages]);

  return {
    ...basePagination,
    onPageChange: handlePageChange,
    userBehavior: learningEnabled ? userBehavior : null,
    predictNextPages,
  };
};

/**
 * Hook for real-time pagination with live updates
 */
export const useRealTimePagination = ({
  fetchFunction,
  updateFunction,
  updateInterval = 30000, // 30 seconds
  enableRealTime = true,
  ...paginationOptions
}) => {
  const [lastUpdate, setLastUpdate] = useState(null);
  const intervalRef = useRef(null);

  const basePagination = useAdvancedPagination({
    fetchFunction,
    ...paginationOptions,
  });

  // Start real-time updates
  const startRealTimeUpdates = useCallback(() => {
    if (!enableRealTime || intervalRef.current) return;

    intervalRef.current = setInterval(async () => {
      try {
        if (updateFunction) {
          const updates = await updateFunction();
          if (updates && updates.hasUpdates) {
            // Refresh current page if there are updates
            await basePagination.refresh();
            setLastUpdate(new Date());
          }
        }
      } catch (error) {
        console.warn('Real-time update failed:', error);
      }
    }, updateInterval);
  }, [enableRealTime, updateFunction, updateInterval, basePagination.refresh]);

  // Stop real-time updates
  const stopRealTimeUpdates = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // Start updates on mount
  useEffect(() => {
    startRealTimeUpdates();
    return stopRealTimeUpdates;
  }, [startRealTimeUpdates, stopRealTimeUpdates]);

  return {
    ...basePagination,
    lastUpdate,
    startRealTimeUpdates,
    stopRealTimeUpdates,
    isRealTimeEnabled: enableRealTime && !!intervalRef.current,
  };
};
