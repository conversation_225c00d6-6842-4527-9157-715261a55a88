import requests
import json

# Get admin token first
login_data = {'user_name': 'admin', 'password': 'admin123'}
login_response = requests.post('http://127.0.0.1:8000/api/auth/login/', json=login_data)

if login_response.status_code == 200:
    login_data = login_response.json()
    token = login_data['data']['access_token']
    headers = {'Authorization': f'Bearer {token}'}
    
    # Get restaurants
    print("=== RESTAURANTS ===")
    restaurants_response = requests.get('http://127.0.0.1:8000/api/restaurant/restaurants/', headers=headers)
    if restaurants_response.status_code == 200:
        restaurants = restaurants_response.json()
        print(f'Total restaurants: {len(restaurants)}')
        for restaurant in restaurants[:3]:
            print(f'Restaurant {restaurant["id"]}: {restaurant["name"]}')
    else:
        print(f'Failed to get restaurants: {restaurants_response.status_code}')
    
    # Get menu items
    print("\n=== MENU ITEMS ===")
    menu_items_response = requests.get('http://127.0.0.1:8000/api/restaurant/menu-items/', headers=headers)
    if menu_items_response.status_code == 200:
        menu_items = menu_items_response.json()
        print(f'Total menu items: {len(menu_items)}')
        for item in menu_items[:5]:
            print(f'Menu Item {item["id"]}: {item["name"]} - ${item["price"]}')
    else:
        print(f'Failed to get menu items: {menu_items_response.status_code}')
    
    # Get addresses
    print("\n=== ADDRESSES ===")
    addresses_response = requests.get('http://127.0.0.1:8000/api/restaurant/addresses/', headers=headers)
    if addresses_response.status_code == 200:
        addresses = addresses_response.json()
        print(f'Total addresses: {len(addresses)}')
        for address in addresses[:3]:
            print(f'Address {address["id"]}: {address.get("street_address", "N/A")}')
    else:
        print(f'Failed to get addresses: {addresses_response.status_code}')
        
else:
    print(f'Login failed: {login_response.status_code}')
