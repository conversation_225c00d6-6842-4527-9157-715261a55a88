import React, { useState, useEffect, useRef } from 'react';
import { cn } from '../../utils/cn';

const ProgressiveImage = ({
  src,
  placeholderSrc,
  alt,
  className,
  containerClassName,
  blurAmount = 'blur-sm',
  transitionDuration = 'duration-300',
  onLoad,
  onError,
  lazy = true,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(!lazy);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef(null);
  const containerRef = useRef(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isInView]);

  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };

  // Handle image error
  const handleError = () => {
    setHasError(true);
    onError?.();
  };

  // Generate placeholder with blur effect
  const generatePlaceholder = () => {
    if (placeholderSrc) {
      return placeholderSrc;
    }
    
    // Generate a simple colored placeholder based on alt text
    const colors = [
      '#f3f4f6', '#e5e7eb', '#d1d5db', '#9ca3af',
      '#fef3c7', '#fde68a', '#fed7aa', '#fecaca'
    ];
    const colorIndex = alt ? alt.length % colors.length : 0;
    
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="${colors[colorIndex]}"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#6b7280" font-family="system-ui" font-size="14">
          ${alt || 'Image'}
        </text>
      </svg>
    `)}`;
  };

  return (
    <div 
      ref={containerRef}
      className={cn('relative overflow-hidden', containerClassName)}
    >
      {/* Placeholder/Low quality image */}
      <img
        src={generatePlaceholder()}
        alt={alt}
        className={cn(
          'absolute inset-0 w-full h-full object-cover transition-opacity',
          transitionDuration,
          isLoaded ? 'opacity-0' : 'opacity-100',
          !isLoaded && blurAmount,
          className
        )}
        {...props}
      />

      {/* High quality image */}
      {isInView && !hasError && (
        <img
          ref={imgRef}
          src={src}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'w-full h-full object-cover transition-opacity',
            transitionDuration,
            isLoaded ? 'opacity-100' : 'opacity-0',
            className
          )}
          {...props}
        />
      )}

      {/* Error state */}
      {hasError && (
        <div className={cn(
          'absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-400',
          className
        )}>
          <div className="text-center">
            <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
            <p className="text-xs">Failed to load</p>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {!isLoaded && !hasError && isInView && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-gray-300 border-t-primary-500 rounded-full animate-spin" />
        </div>
      )}
    </div>
  );
};

// Optimized image component with different sizes
export const OptimizedImage = ({
  src,
  alt,
  sizes = {
    small: '150w',
    medium: '300w',
    large: '600w',
    xl: '1200w'
  },
  className,
  ...props
}) => {
  // Generate srcSet for different screen sizes
  const generateSrcSet = () => {
    if (typeof src === 'string') {
      // For external URLs, return as-is
      return src;
    }
    
    // For internal images, generate different sizes
    return Object.entries(sizes)
      .map(([size, width]) => `${src}?w=${width.replace('w', '')} ${width}`)
      .join(', ');
  };

  return (
    <ProgressiveImage
      src={src}
      srcSet={generateSrcSet()}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      alt={alt}
      className={className}
      {...props}
    />
  );
};

// Avatar component with progressive loading
export const ProgressiveAvatar = ({
  src,
  alt,
  size = 'medium',
  fallbackText,
  className,
  ...props
}) => {
  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-10 h-10',
    large: 'w-12 h-12',
    xl: 'w-16 h-16',
    '2xl': 'w-20 h-20'
  };

  const textSizeClasses = {
    small: 'text-xs',
    medium: 'text-sm',
    large: 'text-base',
    xl: 'text-lg',
    '2xl': 'text-xl'
  };

  const [hasError, setHasError] = useState(false);

  if (hasError || !src) {
    return (
      <div className={cn(
        'rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-medium',
        sizeClasses[size],
        textSizeClasses[size],
        className
      )}>
        {fallbackText || alt?.charAt(0)?.toUpperCase() || '?'}
      </div>
    );
  }

  return (
    <ProgressiveImage
      src={src}
      alt={alt}
      onError={() => setHasError(true)}
      containerClassName={cn('rounded-full', sizeClasses[size])}
      className={cn('rounded-full', className)}
      {...props}
    />
  );
};

export default ProgressiveImage;
