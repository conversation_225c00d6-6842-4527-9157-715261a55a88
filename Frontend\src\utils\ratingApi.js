import axios from "axios";

// Get API base URL from environment variables
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000/api";

// Create axios instance for rating API
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
    if (user.access_token) {
      config.headers.Authorization = `Bearer ${user.access_token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error("Rating API Error:", error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const ratingApi = {
  // Create a rating for an order
  createRating: async (ratingData) => {
    try {
      const response = await apiClient.post("/order/ratings/", ratingData);
      return response.data;
    } catch (error) {
      console.error("Error creating rating:", error);
      throw error;
    }
  },

  // Get ratings for current user
  getMyRatings: async () => {
    try {
      const response = await apiClient.get("/order/ratings/my_ratings/");
      return response.data;
    } catch (error) {
      console.error("Error fetching my ratings:", error);
      throw error;
    }
  },

  // Get restaurant rating summary
  getRestaurantRatings: async (restaurantId) => {
    try {
      const response = await apiClient.get(
        `/order/ratings/restaurant_summary/?restaurant_id=${restaurantId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching restaurant ratings:", error);
      throw error;
    }
  },

  // Get all ratings (admin/restaurant owner)
  getAllRatings: async () => {
    try {
      const response = await apiClient.get("/order/ratings/");
      return response.data;
    } catch (error) {
      console.error("Error fetching all ratings:", error);
      throw error;
    }
  },

  // Get ratings for restaurant owner's restaurants (for restaurant dashboard)
  getMyRestaurantRatings: async () => {
    try {
      const response = await apiClient.get(
        "/order/ratings/restaurant_ratings/"
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching my restaurant ratings:", error);
      throw error;
    }
  },

  // Check if user can rate an order
  canRateOrder: async (orderId) => {
    try {
      const response = await apiClient.get(
        `/order/ratings/can_rate_order/?order_id=${orderId}`
      );
      return response.data;
    } catch (error) {
      console.error("Error checking if order can be rated:", error);
      throw error;
    }
  },

  // Update a rating
  updateRating: async (ratingId, ratingData) => {
    try {
      const response = await apiClient.patch(
        `/order/ratings/${ratingId}/`,
        ratingData
      );
      return response.data;
    } catch (error) {
      console.error("Error updating rating:", error);
      throw error;
    }
  },

  // Delete a rating
  deleteRating: async (ratingId) => {
    try {
      const response = await apiClient.delete(`/order/ratings/${ratingId}/`);
      return response.data;
    } catch (error) {
      console.error("Error deleting rating:", error);
      throw error;
    }
  },
};

export default ratingApi;
