#!/usr/bin/env python
"""
Comprehensive test script for dynamic delivery features
Tests all endpoints and services for errors and functionality
"""

import os
import django
import requests
import json
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from restaurant.models import Restaurant, Address
from deliveryAgent.models import DeliveryAgentProfile, DeliveryZone, AgentDeliveryZone
from orders.models import Order
from deliveryAgent.geolocation_service import geolocation_service
from deliveryAgent.delivery_fee_calculator import delivery_fee_calculator
from deliveryAgent.zone_manager import zone_manager
from deliveryAgent.performance_calculator import performance_calculator
from deliveryAgent.realtime_tracking import realtime_tracking_service

User = get_user_model()

class DynamicDeliveryTester:
    def __init__(self):
        self.base_url = 'http://localhost:8000'
        self.errors = []
        self.successes = []
        
    def log_error(self, test_name, error):
        self.errors.append(f"❌ {test_name}: {str(error)}")
        print(f"❌ {test_name}: {str(error)}")
        
    def log_success(self, test_name, message=""):
        self.successes.append(f"✅ {test_name}: {message}")
        print(f"✅ {test_name}: {message}")
        
    def test_geolocation_service(self):
        """Test geolocation service functionality"""
        print("\n🧪 Testing Geolocation Service...")
        
        try:
            # Test distance calculation
            kabul_location = {'lat': 34.5553, 'lng': 69.2075}
            herat_location = {'lat': 34.3482, 'lng': 62.1997}
            
            distance = geolocation_service.calculate_distance(kabul_location, herat_location)
            if distance and 'distance_km' in distance:
                self.log_success("Distance Calculation", f"Distance: {distance['distance_km']} km")
            else:
                self.log_error("Distance Calculation", "Invalid distance result")
                
            # Test travel time calculation
            time_data = geolocation_service.calculate_travel_time(kabul_location, herat_location)
            if time_data and 'duration_minutes' in time_data:
                self.log_success("Travel Time Calculation", f"Time: {time_data['duration_minutes']} mins")
            else:
                self.log_error("Travel Time Calculation", "Invalid time result")
                
            # Test geocoding (will use fallback without API key)
            address = geolocation_service.geocode_address("Kabul, Afghanistan")
            if address:
                self.log_success("Geocoding", f"Geocoded: {address}")
            else:
                self.log_success("Geocoding Fallback", "Using fallback geocoding")
                
        except Exception as e:
            self.log_error("Geolocation Service", str(e))
            
    def test_delivery_fee_calculator(self):
        """Test dynamic delivery fee calculation"""
        print("\n🧪 Testing Delivery Fee Calculator...")
        
        try:
            order_data = {
                'restaurant_location': {'lat': 34.5553, 'lng': 69.2075},
                'delivery_location': {'lat': 34.5560, 'lng': 69.2080},
                'order_value': Decimal('25.00'),
                'order_time': django.utils.timezone.now(),
                'priority': 'normal'
            }
            
            fee_data = delivery_fee_calculator.calculate_delivery_fee(order_data)
            if fee_data and 'total_fee' in fee_data:
                self.log_success("Dynamic Fee Calculation", f"Fee: ${fee_data['total_fee']}")
                
                # Test different priorities
                order_data['priority'] = 'express'
                express_fee = delivery_fee_calculator.calculate_delivery_fee(order_data)
                if express_fee['total_fee'] > fee_data['total_fee']:
                    self.log_success("Express Fee Multiplier", "Express fee is higher")
                else:
                    self.log_error("Express Fee Multiplier", "Express fee not higher than normal")
            else:
                self.log_error("Dynamic Fee Calculation", "Invalid fee result")
                
        except Exception as e:
            self.log_error("Delivery Fee Calculator", str(e))
            
    def test_zone_manager(self):
        """Test zone management functionality"""
        print("\n🧪 Testing Zone Manager...")
        
        try:
            # Create test zone
            zone = DeliveryZone.objects.create(
                name='Test Zone',
                description='Test delivery zone',
                center_latitude=34.5553,
                center_longitude=69.2075,
                radius_km=5.0,
                delivery_fee=3.00
            )
            
            # Test zone coverage
            location = {'lat': 34.5560, 'lng': 69.2080}
            zones = zone_manager.get_zones_for_location(location)
            if zones:
                self.log_success("Zone Coverage", f"Found {len(zones)} zones")
            else:
                self.log_error("Zone Coverage", "No zones found for location")
                
            # Test zone stats
            stats = zone_manager.get_zone_coverage_stats()
            if stats and 'total_zones' in stats:
                self.log_success("Zone Statistics", f"Total zones: {stats['total_zones']}")
            else:
                self.log_error("Zone Statistics", "Invalid stats result")
                
            # Cleanup
            zone.delete()
            
        except Exception as e:
            self.log_error("Zone Manager", str(e))
            
    def test_performance_calculator(self):
        """Test performance calculation"""
        print("\n🧪 Testing Performance Calculator...")
        
        try:
            # Create test user and agent
            user = User.objects.create_user(
                user_name='test_agent_perf',
                name='Test Agent Performance',
                phone='+**********',
                email='<EMAIL>',
                role='delivery_agent'
            )
            
            agent = DeliveryAgentProfile.objects.create(
                user=user,
                phone_number='+**********',
                vehicle_type='motorcycle',
                license_plate='TEST999',
                address='Test Address',
                status='active',
                is_verified=True,
                total_deliveries=10,
                successful_deliveries=8
            )
            
            # Test performance calculation
            performance = performance_calculator.calculate_agent_performance(agent.id, 30)
            if performance and 'overall_score' in performance:
                self.log_success("Performance Calculation", f"Score: {performance['overall_score']}")
            else:
                self.log_error("Performance Calculation", "Invalid performance result")
                
            # Test leaderboard
            leaderboard = performance_calculator.get_leaderboard(30, 5)
            if isinstance(leaderboard, list):
                self.log_success("Performance Leaderboard", f"Generated leaderboard with {len(leaderboard)} agents")
            else:
                self.log_error("Performance Leaderboard", "Invalid leaderboard result")
                
            # Cleanup
            agent.delete()
            user.delete()
            
        except Exception as e:
            self.log_error("Performance Calculator", str(e))
            
    def test_realtime_tracking(self):
        """Test real-time tracking service"""
        print("\n🧪 Testing Real-time Tracking...")
        
        try:
            # Create test user and agent
            user = User.objects.create_user(
                user_name='test_agent_track',
                name='Test Agent Tracking',
                phone='+**********',
                email='<EMAIL>',
                role='delivery_agent'
            )
            
            agent = DeliveryAgentProfile.objects.create(
                user=user,
                phone_number='+**********',
                vehicle_type='motorcycle',
                license_plate='TEST998',
                address='Test Address',
                status='active',
                is_verified=True
            )
            
            # Test location update
            success = realtime_tracking_service.update_agent_location(
                agent.id, 34.5553, 69.2075, 'Test Location', 'driving'
            )
            if success:
                self.log_success("Location Update", "Location updated successfully")
            else:
                self.log_error("Location Update", "Failed to update location")
                
            # Test location retrieval
            location = realtime_tracking_service.get_agent_location(agent.id)
            if location and 'latitude' in location:
                self.log_success("Location Retrieval", f"Retrieved location: {location['latitude']}, {location['longitude']}")
            else:
                self.log_error("Location Retrieval", "Failed to retrieve location")
                
            # Test agents in area
            agents = realtime_tracking_service.get_agents_in_area(34.5553, 69.2075, 10)
            if isinstance(agents, list):
                self.log_success("Agents in Area", f"Found {len(agents)} agents in area")
            else:
                self.log_error("Agents in Area", "Invalid agents result")
                
            # Cleanup
            agent.delete()
            user.delete()
            
        except Exception as e:
            self.log_error("Real-time Tracking", str(e))
            
    def test_api_endpoints(self):
        """Test API endpoints (requires running server)"""
        print("\n🧪 Testing API Endpoints...")
        
        try:
            # Test dynamic delivery fee endpoint
            fee_data = {
                'restaurant_location': {'lat': 34.5553, 'lng': 69.2075},
                'delivery_location': {'lat': 34.5560, 'lng': 69.2080},
                'order_value': 25.00,
                'priority': 'normal'
            }
            
            response = requests.post(
                f'{self.base_url}/api/delivery-agent/dynamic-delivery-fee/',
                json=fee_data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    self.log_success("Dynamic Fee API", f"Fee: ${result['data']['total_fee']}")
                else:
                    self.log_error("Dynamic Fee API", f"API error: {result.get('message', 'Unknown error')}")
            else:
                self.log_error("Dynamic Fee API", f"HTTP {response.status_code}: {response.text}")
                
        except requests.exceptions.ConnectionError:
            self.log_error("API Endpoints", "Server not running - start with 'python manage.py runserver'")
        except Exception as e:
            self.log_error("API Endpoints", str(e))
            
    def run_all_tests(self):
        """Run all tests and generate report"""
        print("🚀 Starting Dynamic Delivery System Tests...\n")
        
        self.test_geolocation_service()
        self.test_delivery_fee_calculator()
        self.test_zone_manager()
        self.test_performance_calculator()
        self.test_realtime_tracking()
        self.test_api_endpoints()
        
        # Generate report
        print("\n" + "="*60)
        print("📊 TEST RESULTS SUMMARY")
        print("="*60)
        
        print(f"\n✅ SUCCESSES ({len(self.successes)}):")
        for success in self.successes:
            print(f"  {success}")
            
        print(f"\n❌ ERRORS ({len(self.errors)}):")
        for error in self.errors:
            print(f"  {error}")
            
        if len(self.errors) == 0:
            print("\n🎉 ALL TESTS PASSED! Dynamic delivery system is working perfectly!")
        else:
            print(f"\n⚠️  {len(self.errors)} issues found. Please review and fix.")
            
        print("\n" + "="*60)

if __name__ == "__main__":
    tester = DynamicDeliveryTester()
    tester.run_all_tests()
