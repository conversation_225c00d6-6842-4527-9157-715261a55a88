#!/usr/bin/env python3
"""
Test OTP resend for existing user to verify real email sending
"""

import requests
import json

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"
HEADERS = {
    "Content-Type": "application/json",
    "Accept": "application/json"
}

def test_otp_resend_existing_user():
    """Test OTP resend for existing user"""
    print("🔄 Testing OTP Resend for Existing User")
    print("=" * 50)
    
    # Use the email that already exists
    email = "<EMAIL>"
    
    print(f"📧 Testing OTP resend for: {email}")
    
    resend_data = {"email": email}
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/resend-otp/",
            headers=HEADERS,
            data=json.dumps(resend_data),
            timeout=30
        )
        
        print(f"\n📡 Resend Response:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Time: {response.elapsed.total_seconds():.2f}s")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ OTP resend successful!")
            print(f"📧 New OTP sent to: {result.get('email', email)}")
            print("\n" + "=" * 50)
            print("📬 CHECK YOUR EMAIL NOW!")
            print("📧 Look for email from: <EMAIL>")
            print("📋 Subject: Your OTP Verification Code")
            print("📁 Check both inbox AND spam folder")
            print("=" * 50)
            
            # Ask user to confirm if they received the email
            print("\n🔍 Did you receive the OTP email?")
            received = input("Enter 'yes' if you received it, 'no' if not: ").strip().lower()
            
            if received == 'yes':
                print("🎉 SUCCESS: Real email OTP sending is working!")
                
                # Optional: Test verification
                test_verification = input("\nDo you want to test OTP verification? (yes/no): ").strip().lower()
                if test_verification == 'yes':
                    otp = input("Enter the 6-digit OTP from your email: ").strip()
                    if len(otp) == 6 and otp.isdigit():
                        test_otp_verification(email, otp)
                    else:
                        print("❌ Invalid OTP format")
            else:
                print("❌ Email not received. Possible issues:")
                print("   - Check spam/junk folder")
                print("   - Email delivery might be delayed")
                print("   - Gmail SMTP settings might need adjustment")
            
            return True
        else:
            print(f"❌ Resend failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during resend: {e}")
        return False

def test_otp_verification(email, otp):
    """Test OTP verification"""
    print(f"\n🔐 Testing OTP Verification...")
    
    verification_data = {
        "email": email,
        "otp": otp
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/auth/verify-email/",
            headers=HEADERS,
            data=json.dumps(verification_data),
            timeout=30
        )
        
        print(f"📡 Verification Response:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("🎉 OTP verification successful!")
                return True
            else:
                print("❌ Verification failed:", result.get('message'))
                return False
        else:
            print(f"❌ Verification failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during verification: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Real Email OTP Test")
    print("=" * 50)
    test_otp_resend_existing_user()
