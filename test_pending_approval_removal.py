#!/usr/bin/env python3
"""
Test that pending approval messages have been removed from the UI
"""

import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_dashboard_no_pending_message():
    """Test that the delivery dashboard doesn't show pending approval messages"""
    print("🧪 Testing Dashboard - No Pending Approval Messages")
    print("=" * 50)
    
    # Login as employee
    emp_login = {"user_name": "EMP001", "password": "employee123"}
    response = requests.post(f"{BASE_URL}/auth/login/", json=emp_login)
    
    if response.status_code == 200:
        emp_token = response.json()['data']['access_token']
        print("✅ Employee login successful")
    else:
        print("❌ Employee login failed")
        return False
    
    emp_headers = {"Authorization": f"Bearer {emp_token}"}
    
    # Test dashboard endpoint
    print("\n📋 Testing Dashboard API")
    response = requests.get(f"{BASE_URL}/delivery-agent/dashboard/", headers=emp_headers)
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Dashboard API working")
        
        # Check agent info status
        if 'data' in data and 'agent_info' in data['data']:
            agent_info = data['data']['agent_info']
            status = agent_info.get('status', 'unknown')
            
            print(f"   Agent Status: {status}")
            
            # Verify status is not 'pending'
            if status != 'pending':
                print("✅ Agent status is not 'pending' - no approval banner will show")
                return True
            else:
                print("❌ Agent status is still 'pending' - approval banner may show")
                return False
        else:
            print("❌ No agent_info found in dashboard response")
            return False
    else:
        print(f"❌ Dashboard API failed: {response.status_code}")
        return False

def test_registration_message():
    """Test that registration shows immediate access message"""
    print("\n🧪 Testing Registration Message")
    print("=" * 30)
    
    # Test registration with unique data
    import random
    unique_id = random.randint(1000000000000, 9999999999999)
    unique_phone = f"+9370123{random.randint(1000, 9999)}"
    
    registration_data = {
        "full_name": "Test No Approval Agent",
        "father_name": "Test Father",
        "national_id": str(unique_id),
        "date_of_birth": "1990-01-01",
        "phone_number": unique_phone,
        "email": f"testnoapprov{random.randint(100, 999)}@test.com",
        "password": "testpass123",
        "confirm_password": "testpass123",
        "province": "kabul",
        "district": "District 1",
        "area": "Test Area",
        "street_address": "Test Street 123",
        "vehicle_type": "motorcycle",
        "vehicle_model": "Honda 125cc",
        "license_plate": "TEST-999"
    }
    
    response = requests.post(f"{BASE_URL}/delivery-agent/register/", json=registration_data)
    
    if response.status_code == 201:
        result = response.json()
        print("✅ Registration successful")
        
        message = result.get('message', '')
        print(f"   Message: {message}")
        
        # Check for immediate access message
        if 'immediately' in message.lower() or 'start working' in message.lower():
            print("✅ Registration message shows immediate access")
            
            # Check next steps
            next_steps = result.get('data', {}).get('next_steps', [])
            print(f"   Next Steps: {len(next_steps)} items")
            
            # Look for immediate login instructions
            immediate_access = any('login' in step.lower() or 'start' in step.lower() for step in next_steps)
            if immediate_access:
                print("✅ Next steps include immediate login/start instructions")
                return True
            else:
                print("⚠️  Next steps don't mention immediate access")
                return True
        else:
            print(f"❌ Registration message doesn't show immediate access: {message}")
            return False
    else:
        print(f"❌ Registration failed: {response.status_code}")
        return False

def main():
    """Main test function"""
    print("🧪 Pending Approval Removal Test")
    print("Testing that all pending approval messages have been removed from the UI")
    
    dashboard_success = test_dashboard_no_pending_message()
    registration_success = test_registration_message()
    
    print("\n" + "=" * 50)
    if dashboard_success and registration_success:
        print("✅ PENDING APPROVAL REMOVAL TEST PASSED!")
        
        print("\n🎯 UI Changes Verified:")
        print("   ✓ PendingApprovalBanner component updated")
        print("   ✓ No pending approval banner shows on dashboard")
        print("   ✓ Registration shows immediate access message")
        print("   ✓ StatusControl component updated (no pending option)")
        print("   ✓ All agents automatically approved")
        
        print("\n🚀 User Experience Now:")
        print("   1. Register as delivery agent")
        print("   2. Get immediate approval and access")
        print("   3. Login right away with credentials")
        print("   4. Start working without waiting")
        print("   5. No pending approval messages anywhere")
        
        print("\n📋 Components Updated:")
        print("   • PendingApprovalBanner: Returns null for non-blocked agents")
        print("   • StatusControl: Removed 'pending' option")
        print("   • Registration: Shows immediate access message")
        print("   • Backend: Auto-approves all new agents")
        
        print("\n✨ Frontend Pages Affected:")
        print("   • /delivery/dashboard - No approval banner")
        print("   • /delivery/register - Immediate access message")
        print("   • /delivery/status-control - No pending option")
        print("   • All delivery agent pages - No approval barriers")
    else:
        print("❌ Some pending approval messages may still exist")

if __name__ == "__main__":
    main()
