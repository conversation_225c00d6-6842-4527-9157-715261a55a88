#!/usr/bin/env python3
"""
Test restaurant creation with the manually created user
"""

import requests
import json

# API Configuration
API_BASE_URL = "http://127.0.0.1:8000/api"

def test_restaurant_creation():
    """Test restaurant creation with the test user we just created"""
    
    # Login with the test user we created
    login_data = {
        "user_name": "testrestaurant",
        "password": "password123"
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print("🔐 Logging in with test user...")
    
    try:
        # Login
        response = requests.post(
            f"{API_BASE_URL}/auth/login/",
            headers=headers,
            data=json.dumps(login_data)
        )
        
        print(f"📡 Login Status: {response.status_code}")
        print(f"📄 Login Response: {response.text}")
        
        if response.status_code != 200:
            print("❌ Login failed")
            return False
        
        result = response.json()
        token = result['data']['access_token']
        print("✅ Login successful!")
        
        # Now test restaurant creation
        auth_headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        # Test restaurant data
        restaurant_data = {
            "name": "Test Afghan Restaurant",
            "description": "A beautiful test restaurant serving authentic Afghan cuisine",
            "address": {
                "street": "123 Main Street",
                "city": "Kabul",
                "state": "Kabul",
                "postal_code": "1001",
                "country": "Afghanistan",
                "latitude": 34.5553,
                "longitude": 69.2075
            },
            "contact_number": "+93701234567",
            "opening_time": "09:00:00",
            "closing_time": "22:00:00",
            "delivery_fee": "50.00"
        }
        
        print(f"\n🏗️ Creating restaurant: {restaurant_data['name']}")
        
        response = requests.post(
            f"{API_BASE_URL}/restaurant/restaurants/",
            headers=auth_headers,
            data=json.dumps(restaurant_data)
        )
        
        print(f"📡 Restaurant Creation Status: {response.status_code}")
        print(f"📄 Restaurant Creation Response: {response.text}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ Restaurant created successfully!")
            print(f"   Restaurant ID: {result.get('id')}")
            print(f"   Name: {result.get('name')}")
            print(f"   Delivery Fee: {result.get('delivery_fee')}")
            print(f"   Verified: {result.get('is_verified', False)}")
            return True
        else:
            print("❌ Restaurant creation failed")
            try:
                error_data = response.json()
                print(f"🔍 Error Details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"🔍 Raw Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Restaurant Creation with Created User")
    print("=" * 60)
    
    success = test_restaurant_creation()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 SUCCESS: Restaurant creation is working!")
        print("✅ The delivery_fee validator fix resolved the issue")
        print("✅ Frontend form should now work properly")
    else:
        print("❌ FAILED: Restaurant creation still has issues")
        print("🔍 Need to investigate further")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
