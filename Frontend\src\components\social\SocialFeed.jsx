import React from 'react';
import { <PERSON>, Share2, <PERSON>, Users, Gift, Camera, MessageCircle } from 'lucide-react';
import { useSocial } from '../../context/SocialContext';
import Card from '../common/Card';
import Badge from '../common/Badge';

const SocialFeed = ({ limit = 10 }) => {
  const { socialActivity } = useSocial();

  const getActivityIcon = (type) => {
    switch (type) {
      case 'photo_review':
        return <Camera size={16} className="text-blue-500" />;
      case 'restaurant_shared':
        return <Share2 size={16} className="text-green-500" />;
      case 'order_shared':
        return <Share2 size={16} className="text-purple-500" />;
      case 'referral_sent':
        return <Users size={16} className="text-orange-500" />;
      case 'referral_completed':
        return <Gift size={16} className="text-red-500" />;
      case 'social_connected':
        return <MessageCircle size={16} className="text-blue-600" />;
      case 'friend_added':
        return <Users size={16} className="text-green-600" />;
      default:
        return <Heart size={16} className="text-gray-500" />;
    }
  };

  const getActivityMessage = (activity) => {
    const { type, data } = activity;
    
    switch (type) {
      case 'photo_review':
        return (
          <span>
            left a <span className="font-medium">{data.rating}-star review</span> with{' '}
            <span className="font-medium">{data.photoCount} photo{data.photoCount !== 1 ? 's' : ''}</span> for{' '}
            <span className="font-medium text-primary-600">{data.restaurantName}</span>
          </span>
        );
      case 'restaurant_shared':
        return (
          <span>
            shared <span className="font-medium text-primary-600">{data.restaurantName}</span> on{' '}
            <span className="font-medium">{data.platform}</span>
          </span>
        );
      case 'order_shared':
        return (
          <span>
            shared their order from <span className="font-medium text-primary-600">{data.restaurantName}</span> on{' '}
            <span className="font-medium">{data.platform}</span>
          </span>
        );
      case 'referral_sent':
        return (
          <span>
            sent a referral invite to <span className="font-medium">{data.email}</span>
          </span>
        );
      case 'referral_completed':
        return (
          <span>
            earned <span className="font-medium text-green-600">${data.reward}</span> from a successful referral!
          </span>
        );
      case 'social_connected':
        return (
          <span>
            connected their <span className="font-medium">{data.provider}</span> account
          </span>
        );
      case 'friend_added':
        return (
          <span>
            added <span className="font-medium">{data.friendName}</span> as a friend
          </span>
        );
      default:
        return <span>had some activity</span>;
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'photo_review':
        return 'border-l-blue-500';
      case 'restaurant_shared':
      case 'order_shared':
        return 'border-l-green-500';
      case 'referral_sent':
      case 'referral_completed':
        return 'border-l-orange-500';
      case 'social_connected':
        return 'border-l-purple-500';
      case 'friend_added':
        return 'border-l-pink-500';
      default:
        return 'border-l-gray-300';
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const activityTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now - activityTime) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return activityTime.toLocaleDateString();
  };

  const recentActivities = socialActivity.slice(0, limit);

  if (recentActivities.length === 0) {
    return (
      <Card className="text-center py-8">
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Heart size={24} className="text-gray-400" />
        </div>
        <h3 className="font-semibold mb-2">No Activity Yet</h3>
        <p className="text-gray-600 text-sm">
          Start sharing restaurants, writing reviews, or referring friends to see your activity here!
        </p>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold text-lg">Recent Activity</h3>
        <Badge variant="secondary" size="small">
          {recentActivities.length} activities
        </Badge>
      </div>

      <div className="space-y-3">
        {recentActivities.map((activity) => (
          <Card key={activity.id} className={`p-4 border-l-4 ${getActivityColor(activity.type)}`}>
            <div className="flex items-start space-x-3">
              <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                {activity.userAvatar ? (
                  <img
                    src={activity.userAvatar}
                    alt={activity.userName}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-gray-600 font-medium text-sm">
                    {activity.userName?.charAt(0) || 'U'}
                  </span>
                )}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  {getActivityIcon(activity.type)}
                  <span className="font-medium text-gray-900">{activity.userName}</span>
                  <span className="text-gray-500 text-sm">
                    {formatTimeAgo(activity.timestamp)}
                  </span>
                </div>
                
                <p className="text-gray-700 text-sm">
                  {getActivityMessage(activity)}
                </p>

                {/* Special content for certain activity types */}
                {activity.type === 'photo_review' && activity.data.rating && (
                  <div className="flex items-center mt-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        size={12}
                        className={`${
                          star <= activity.data.rating
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                )}

                {activity.type === 'referral_completed' && (
                  <div className="mt-2 inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                    <Gift size={12} className="mr-1" />
                    Reward Earned: ${activity.data.reward}
                  </div>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>

      {socialActivity.length > limit && (
        <div className="text-center">
          <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
            View All Activity ({socialActivity.length})
          </button>
        </div>
      )}
    </div>
  );
};

export default SocialFeed;
