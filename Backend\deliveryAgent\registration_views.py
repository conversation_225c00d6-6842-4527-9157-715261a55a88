"""
Delivery Agent Registration Views
Self-registration system for delivery agents
"""

from django.http import JsonResponse
from django.views import View
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.db import transaction
from django.contrib.auth import get_user_model
from .models import DeliveryAgentProfile
from .serializers import AgentRegistrationSerializer

User = get_user_model()


class AgentSelfRegistrationView(APIView):
    """
    Agent self-registration endpoint
    Allows delivery agents to register themselves
    """

    def get(self, request):
        """Show registration form information"""
        return Response({
            'status': 'success',
            'message': 'Delivery agent registration is available',
            'message_dari': 'د ډیلیوری ایجنټ ثبت نوم شته',
            'required_fields': [
                'full_name', 'father_name', 'national_id', 'date_of_birth',
                'phone_number', 'email', 'password', 'confirm_password',
                'province', 'district', 'area', 'street_address',
                'vehicle_type', 'vehicle_model', 'license_plate'
            ]
        })

    def post(self, request):
        """Process registration application"""
        try:
            serializer = AgentRegistrationSerializer(data=request.data)

            if serializer.is_valid():
                with transaction.atomic():
                    # Create agent profile with pending status (controlled by agent)
                    agent_profile = serializer.save()

                    return Response({
                        'status': 'success',
                        'message': 'Registration successful! You can start working immediately.',
                        'message_dari': 'ثبت نوم بریالی وو! تاسو سمدلاسه کار پیل کولی شئ.',
                        'data': {
                            'agent_id': agent_profile.agent_id,
                            'full_name': agent_profile.full_name,
                            'phone_number': agent_profile.phone_number,
                            'status': agent_profile.status,
                            'login_credentials': {
                                'username': agent_profile.phone_number,
                                'password': 'Use the password you provided during registration'
                            },
                            'next_steps': [
                                'Login to the delivery agent portal',
                                'Complete your profile information',
                                'Start accepting delivery orders',
                                'Upload required documents when convenient'
                            ]
                        }
                    }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'status': 'error',
                    'message': 'Registration failed. Please check your information.',
                    'errors': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Registration failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ApplicationStatusView(APIView):
    """
    Check application status for delivery agents
    """

    def get(self, request):
        """Check application status by phone number or agent ID"""
        phone_number = request.query_params.get('phone_number')
        agent_id = request.query_params.get('agent_id')

        if not phone_number and not agent_id:
            return Response({
                'status': 'error',
                'message': 'Phone number or agent ID is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            if agent_id:
                agent_profile = DeliveryAgentProfile.objects.get(agent_id=agent_id)
            else:
                agent_profile = DeliveryAgentProfile.objects.get(phone_number=phone_number)

            return Response({
                'status': 'success',
                'data': {
                    'agent_id': agent_profile.agent_id,
                    'full_name': agent_profile.full_name,
                    'phone_number': agent_profile.phone_number,
                    'application_status': agent_profile.status,
                    'created_at': agent_profile.created_at,
                    'is_verified': agent_profile.is_verified,
                    'can_change_status': True,  # Agents can control their own status
                    'available_statuses': [
                        {'value': 'pending', 'label': 'Pending'},
                        {'value': 'active', 'label': 'Active'},
                        {'value': 'inactive', 'label': 'Inactive'},
                    ]
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Application not found'
            }, status=status.HTTP_404_NOT_FOUND)


class AgentStatusUpdateView(APIView):
    """
    Allow delivery agents to update their own status
    """

    def post(self, request):
        """Update agent status"""
        agent_id = request.data.get('agent_id')
        new_status = request.data.get('status')
        phone_number = request.data.get('phone_number')

        if not agent_id and not phone_number:
            return Response({
                'status': 'error',
                'message': 'Agent ID or phone number is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not new_status:
            return Response({
                'status': 'error',
                'message': 'Status is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate status
        valid_statuses = ['pending', 'active', 'inactive']
        if new_status not in valid_statuses:
            return Response({
                'status': 'error',
                'message': f'Invalid status. Must be one of: {", ".join(valid_statuses)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            if agent_id:
                agent_profile = DeliveryAgentProfile.objects.get(agent_id=agent_id)
            else:
                agent_profile = DeliveryAgentProfile.objects.get(phone_number=phone_number)

            # Update status
            agent_profile.status = new_status
            agent_profile.save()

            return Response({
                'status': 'success',
                'message': f'Status updated to {new_status}',
                'data': {
                    'agent_id': agent_profile.agent_id,
                    'full_name': agent_profile.full_name,
                    'new_status': agent_profile.status,
                    'updated_at': agent_profile.updated_at
                }
            })

        except DeliveryAgentProfile.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Agent not found'
            }, status=status.HTTP_404_NOT_FOUND)
