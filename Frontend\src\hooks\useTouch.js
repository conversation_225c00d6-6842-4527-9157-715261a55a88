import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Hook for handling touch gestures
 */
export const useTouch = ({
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  onTap,
  onDoubleTap,
  onLongPress,
  threshold = 50,
  longPressDelay = 500,
  doubleTapDelay = 300
} = {}) => {
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const [isPressed, setIsPressed] = useState(false);
  const longPressTimer = useRef(null);
  const doubleTapTimer = useRef(null);
  const tapCount = useRef(0);

  const handleTouchStart = useCallback((e) => {
    setTouchStart({
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY,
      time: Date.now()
    });
    setIsPressed(true);

    // Start long press timer
    if (onLongPress) {
      longPressTimer.current = setTimeout(() => {
        onLongPress(e);
        setIsPressed(false);
      }, longPressDelay);
    }
  }, [onLongPress, longPressDelay]);

  const handleTouchMove = useCallback((e) => {
    if (!touchStart) return;

    const currentTouch = {
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY
    };

    const deltaX = Math.abs(currentTouch.x - touchStart.x);
    const deltaY = Math.abs(currentTouch.y - touchStart.y);

    // Cancel long press if moved too much
    if ((deltaX > 10 || deltaY > 10) && longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }
  }, [touchStart]);

  const handleTouchEnd = useCallback((e) => {
    if (!touchStart) return;

    setIsPressed(false);
    
    // Clear long press timer
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }

    const touchEndPoint = {
      x: e.changedTouches[0].clientX,
      y: e.changedTouches[0].clientY,
      time: Date.now()
    };

    setTouchEnd(touchEndPoint);

    const deltaX = touchEndPoint.x - touchStart.x;
    const deltaY = touchEndPoint.y - touchStart.y;
    const deltaTime = touchEndPoint.time - touchStart.time;

    const absX = Math.abs(deltaX);
    const absY = Math.abs(deltaY);

    // Check for swipe gestures
    if (absX > threshold || absY > threshold) {
      if (absX > absY) {
        // Horizontal swipe
        if (deltaX > 0 && onSwipeRight) {
          onSwipeRight(e, { deltaX, deltaY, deltaTime });
        } else if (deltaX < 0 && onSwipeLeft) {
          onSwipeLeft(e, { deltaX, deltaY, deltaTime });
        }
      } else {
        // Vertical swipe
        if (deltaY > 0 && onSwipeDown) {
          onSwipeDown(e, { deltaX, deltaY, deltaTime });
        } else if (deltaY < 0 && onSwipeUp) {
          onSwipeUp(e, { deltaX, deltaY, deltaTime });
        }
      }
    } else if (deltaTime < 200 && absX < 10 && absY < 10) {
      // Tap gesture
      tapCount.current += 1;

      if (tapCount.current === 1) {
        doubleTapTimer.current = setTimeout(() => {
          if (onTap) onTap(e);
          tapCount.current = 0;
        }, doubleTapDelay);
      } else if (tapCount.current === 2) {
        clearTimeout(doubleTapTimer.current);
        if (onDoubleTap) onDoubleTap(e);
        tapCount.current = 0;
      }
    }

    setTouchStart(null);
  }, [touchStart, threshold, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, onTap, onDoubleTap, doubleTapDelay]);

  const touchHandlers = {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd
  };

  return {
    touchHandlers,
    isPressed,
    touchStart,
    touchEnd
  };
};

/**
 * Hook for swipeable cards/items
 */
export const useSwipeableCard = ({
  onSwipeLeft,
  onSwipeRight,
  threshold = 100,
  snapBackThreshold = 50
} = {}) => {
  const [offset, setOffset] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);

  const handleStart = useCallback((clientX) => {
    setStartX(clientX);
    setIsDragging(true);
  }, []);

  const handleMove = useCallback((clientX) => {
    if (!isDragging) return;
    
    const deltaX = clientX - startX;
    setOffset(deltaX);
  }, [isDragging, startX]);

  const handleEnd = useCallback(() => {
    if (!isDragging) return;
    
    setIsDragging(false);
    
    if (Math.abs(offset) > threshold) {
      if (offset > 0 && onSwipeRight) {
        onSwipeRight();
      } else if (offset < 0 && onSwipeLeft) {
        onSwipeLeft();
      }
    }
    
    // Snap back to center
    setOffset(0);
  }, [isDragging, offset, threshold, onSwipeLeft, onSwipeRight]);

  const touchHandlers = {
    onTouchStart: (e) => handleStart(e.touches[0].clientX),
    onTouchMove: (e) => handleMove(e.touches[0].clientX),
    onTouchEnd: handleEnd,
    onMouseDown: (e) => handleStart(e.clientX),
    onMouseMove: (e) => isDragging && handleMove(e.clientX),
    onMouseUp: handleEnd,
    onMouseLeave: handleEnd
  };

  const cardStyle = {
    transform: `translateX(${offset}px)`,
    transition: isDragging ? 'none' : 'transform 0.3s ease-out'
  };

  return {
    touchHandlers,
    cardStyle,
    offset,
    isDragging,
    isSwipedLeft: offset < -snapBackThreshold,
    isSwipedRight: offset > snapBackThreshold
  };
};

/**
 * Hook for pull-to-refresh functionality
 */
export const usePullToRefresh = ({
  onRefresh,
  threshold = 80,
  maxPull = 120,
  disabled = false
} = {}) => {
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [startY, setStartY] = useState(0);
  const [isPulling, setIsPulling] = useState(false);

  const handleStart = useCallback((clientY) => {
    if (disabled || window.scrollY > 0) return;
    setStartY(clientY);
  }, [disabled]);

  const handleMove = useCallback((clientY) => {
    if (disabled || startY === 0 || window.scrollY > 0) return;
    
    const deltaY = clientY - startY;
    
    if (deltaY > 0) {
      setIsPulling(true);
      const distance = Math.min(deltaY * 0.5, maxPull);
      setPullDistance(distance);
    }
  }, [disabled, startY, maxPull]);

  const handleEnd = useCallback(async () => {
    if (!isPulling) return;
    
    setIsPulling(false);
    
    if (pullDistance >= threshold && onRefresh) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } catch (error) {
        console.error('Refresh failed:', error);
      } finally {
        setIsRefreshing(false);
      }
    }
    
    setPullDistance(0);
    setStartY(0);
  }, [isPulling, pullDistance, threshold, onRefresh]);

  const touchHandlers = {
    onTouchStart: (e) => handleStart(e.touches[0].clientY),
    onTouchMove: (e) => handleMove(e.touches[0].clientY),
    onTouchEnd: handleEnd
  };

  const pullProgress = Math.min(pullDistance / threshold, 1);
  const shouldRefresh = pullDistance >= threshold;

  return {
    touchHandlers,
    pullDistance,
    pullProgress,
    isRefreshing,
    isPulling,
    shouldRefresh
  };
};

export default useTouch;
