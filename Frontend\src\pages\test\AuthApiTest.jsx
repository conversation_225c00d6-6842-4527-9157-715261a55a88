import React, { useState } from "react";
import { authApi } from "../../utils/authApi";
import Button from "../../components/common/Button";
import Input from "../../components/common/Input";

const AuthApiTest = () => {
  const [results, setResults] = useState({});
  const [loading, setLoading] = useState({});

  const testLogin = async () => {
    setLoading((prev) => ({ ...prev, login: true }));
    try {
      const result = await authApi.login({
        user_name: "ahmad123",
        password: "123",
      });
      setResults((prev) => ({ ...prev, login: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        login: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, login: false }));
  };

  const testRegister = async () => {
    setLoading((prev) => ({ ...prev, register: true }));
    try {
      const result = await authApi.register({
        name: "Test User",
        user_name: "testuser123",
        phone: "+1234567890",
        email: "<EMAIL>", // Use the test email that receives OTP
        password: "123456",
        role: "customer",
      });
      setResults((prev) => ({ ...prev, register: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        register: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, register: false }));
  };

  const testVerifyEmail = async () => {
    setLoading((prev) => ({ ...prev, verify: true }));
    try {
      // Prompt user for OTP since it's dynamic
      const otp = prompt(
        "Enter the OTP code from your email (<EMAIL>):"
      );
      if (!otp) {
        setResults((prev) => ({
          ...prev,
          verify: { success: false, error: "OTP is required" },
        }));
        setLoading((prev) => ({ ...prev, verify: false }));
        return;
      }

      const result = await authApi.verifyEmail({
        email: "<EMAIL>",
        otp: otp.trim(),
      });
      setResults((prev) => ({ ...prev, verify: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        verify: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, verify: false }));
  };

  const testPasswordReset = async () => {
    setLoading((prev) => ({ ...prev, passwordReset: true }));
    try {
      const result = await authApi.requestPasswordReset({
        email: "<EMAIL>",
      });
      setResults((prev) => ({ ...prev, passwordReset: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        passwordReset: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, passwordReset: false }));
  };

  const testChangePassword = async () => {
    setLoading((prev) => ({ ...prev, changePassword: true }));
    try {
      const result = await authApi.changePassword({
        email: "<EMAIL>",
        current_password: "123456",
        new_password: "newpassword123",
      });
      setResults((prev) => ({ ...prev, changePassword: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        changePassword: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, changePassword: false }));
  };

  const testTokenRefresh = async () => {
    setLoading((prev) => ({ ...prev, tokenRefresh: true }));
    try {
      // Get current user data
      const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");

      if (!user.refresh_token) {
        setResults((prev) => ({
          ...prev,
          tokenRefresh: { success: false, error: "No refresh token available" },
        }));
        return;
      }

      const result = await authApi.refreshToken(user.refresh_token);
      setResults((prev) => ({ ...prev, tokenRefresh: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        tokenRefresh: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, tokenRefresh: false }));
  };

  const testResendVerification = async () => {
    setLoading((prev) => ({ ...prev, resend: true }));
    try {
      const result = await authApi.resendEmailVerification({
        email: "<EMAIL>",
      });
      setResults((prev) => ({ ...prev, resend: result }));
    } catch (error) {
      setResults((prev) => ({
        ...prev,
        resend: { success: false, error: error.message },
      }));
    }
    setLoading((prev) => ({ ...prev, resend: false }));
  };

  const renderResult = (key) => {
    const result = results[key];
    if (!result) return null;

    return (
      <div
        className={`p-4 rounded-lg border ${
          result.success
            ? "bg-green-50 border-green-200"
            : "bg-red-50 border-red-200"
        }`}
      >
        <h4
          className={`font-medium ${
            result.success ? "text-green-800" : "text-red-800"
          }`}
        >
          {key.toUpperCase()} Result:
        </h4>
        <pre className='mt-2 text-sm overflow-auto'>
          {JSON.stringify(result, null, 2)}
        </pre>
      </div>
    );
  };

  return (
    <div className='max-w-4xl mx-auto p-6'>
      <h1 className='text-2xl font-bold mb-6'>Auth API Test</h1>

      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8'>
        <Button onClick={testLogin} loading={loading.login} variant='primary'>
          Test Login
        </Button>

        <Button
          onClick={testRegister}
          loading={loading.register}
          variant='secondary'
        >
          Test Register
        </Button>

        <Button
          onClick={testResendVerification}
          loading={loading.resend}
          variant='info'
        >
          Test Resend OTP
        </Button>

        <Button
          onClick={testVerifyEmail}
          loading={loading.verify}
          variant='outline'
        >
          Test Verify Email
        </Button>

        <Button
          onClick={testPasswordReset}
          loading={loading.passwordReset}
          variant='danger'
        >
          Test Password Reset
        </Button>

        <Button
          onClick={testChangePassword}
          loading={loading.changePassword}
          variant='warning'
        >
          Test Change Password
        </Button>

        <Button
          onClick={testTokenRefresh}
          loading={loading.tokenRefresh}
          variant='info'
        >
          Test Token Refresh
        </Button>
      </div>

      <div className='space-y-4'>
        {renderResult("login")}
        {renderResult("register")}
        {renderResult("verify")}
        {renderResult("resend")}
        {renderResult("passwordReset")}
        {renderResult("changePassword")}
        {renderResult("tokenRefresh")}
      </div>

      <div className='mt-8 p-4 bg-gray-50 rounded-lg'>
        <h3 className='font-medium text-gray-800 mb-2'>API Endpoints:</h3>
        <ul className='text-sm text-gray-600 space-y-1'>
          <li>• Login: POST /auth/login/</li>
          <li>• Register: POST /auth/register/</li>
          <li>• Verify Email: POST /auth/verify-email/</li>
          <li>• Change Password: POST /auth/change-password/</li>
          <li>• Verify Password Change: POST /auth/verify-password-change/</li>
        </ul>
      </div>

      <div className='mt-4 p-4 bg-blue-50 rounded-lg'>
        <h3 className='font-medium text-blue-800 mb-2'>Test Data:</h3>
        <ul className='text-sm text-blue-600 space-y-1'>
          <li>• Username: ahmad123</li>
          <li>• Password: 123</li>
          <li>• Email: <EMAIL></li>
          <li>• Base URL: https://afghansufra.luilala.com/api</li>
        </ul>
      </div>
    </div>
  );
};

export default AuthApiTest;
