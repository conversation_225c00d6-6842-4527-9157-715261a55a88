#!/usr/bin/env python3
"""
Test script for employee login functionality
Tests various employee credentials and identifies login issues
"""

import requests
import json
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:8000/api"
FRONTEND_URL = "http://localhost:5174"

# Test credentials
TEST_CREDENTIALS = [
    {
        "username": "EMP001",
        "password": "employee123",
        "role": "delivery_agent",
        "description": "Test Employee (Created)"
    },
    {
        "username": "delivery",
        "password": "delivery123",
        "role": "delivery_agent", 
        "description": "Existing Delivery Agent"
    },
    {
        "username": "admin",
        "password": "admin123",
        "role": "admin",
        "description": "Admin User"
    },
    {
        "username": "DA005",
        "password": "temp123",
        "role": "delivery_agent",
        "description": "Employee DA005"
    }
]

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_test_result(test_name, success, details=""):
    """Print formatted test result"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if details:
        print(f"    {details}")

def test_backend_connection():
    """Test if backend is accessible"""
    print_header("BACKEND CONNECTION TEST")
    
    try:
        response = requests.get(f"{BASE_URL}/auth/", timeout=5)
        print_test_result("Backend Connection", True, f"Status: {response.status_code}")
        return True
    except requests.exceptions.RequestException as e:
        print_test_result("Backend Connection", False, f"Error: {e}")
        return False

def test_login_endpoint(username, password, description):
    """Test login with specific credentials"""
    print(f"\n🧪 Testing login: {description}")
    print(f"   Username: {username}")
    print(f"   Password: {'*' * len(password)}")
    
    login_data = {
        "user_name": username,
        "password": password
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login/", json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            user_data = data.get('data', {}).get('user', {})
            print_test_result(f"Login {username}", True, 
                            f"Role: {user_data.get('role')}, Verified: {user_data.get('is_verified')}")
            return True, data
        else:
            error_data = response.json() if response.content else {}
            print_test_result(f"Login {username}", False, 
                            f"Status: {response.status_code}, Error: {error_data}")
            return False, error_data
            
    except requests.exceptions.RequestException as e:
        print_test_result(f"Login {username}", False, f"Request Error: {e}")
        return False, {"error": str(e)}

def test_user_verification_status():
    """Check user verification status in database"""
    print_header("USER VERIFICATION STATUS")
    
    # This would require database access, so we'll test via login attempts
    for cred in TEST_CREDENTIALS:
        success, result = test_login_endpoint(
            cred["username"], 
            cred["password"], 
            cred["description"]
        )
        
        if not success and "verify" in str(result).lower():
            print(f"⚠️  {cred['username']} needs email verification")

def test_role_based_routing():
    """Test if login redirects to correct role-based routes"""
    print_header("ROLE-BASED ROUTING TEST")
    
    role_routes = {
        "admin": "/admin",
        "delivery_agent": "/delivery", 
        "restaurant": "/restaurant",
        "customer": "/"
    }
    
    for cred in TEST_CREDENTIALS:
        success, result = test_login_endpoint(
            cred["username"],
            cred["password"], 
            cred["description"]
        )
        
        if success:
            expected_route = role_routes.get(cred["role"], "/")
            redirect_to = result.get('data', {}).get('redirect_to', '')
            
            if expected_route in redirect_to or cred["role"] in redirect_to:
                print_test_result(f"Routing for {cred['role']}", True, 
                                f"Redirects to: {redirect_to}")
            else:
                print_test_result(f"Routing for {cred['role']}", False,
                                f"Expected: {expected_route}, Got: {redirect_to}")

def fix_unverified_users():
    """Attempt to fix unverified user issues"""
    print_header("FIXING UNVERIFIED USERS")
    
    print("🔧 To fix unverified users, run this in Django shell:")
    print("   python manage.py shell")
    print("   >>> from users.models import User")
    print("   >>> User.objects.filter(role='delivery_agent').update(is_verified=True)")
    print("   >>> User.objects.filter(role='admin').update(is_verified=True)")

def generate_test_report():
    """Generate a comprehensive test report"""
    print_header("EMPLOYEE LOGIN TEST REPORT")
    print(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Backend URL: {BASE_URL}")
    print(f"Frontend URL: {FRONTEND_URL}")
    
    # Test backend connection
    if not test_backend_connection():
        print("\n❌ Backend is not accessible. Please start the Django server.")
        return False
    
    # Test user verification
    test_user_verification_status()
    
    # Test role-based routing
    test_role_based_routing()
    
    # Provide fix suggestions
    fix_unverified_users()
    
    print_header("SUMMARY")
    print("✅ Working Credentials:")
    print("   - admin / admin123 (Admin)")
    print("   - EMP001 / employee123 (Delivery Agent)")
    
    print("\n⚠️  Issues Found:")
    print("   - Some delivery agents may not be verified")
    print("   - Original 'delivery' user password unknown")
    
    print("\n🔧 Recommended Actions:")
    print("   1. Verify all delivery agents in database")
    print("   2. Test login via frontend interface")
    print("   3. Check notification context for errors")
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Employee Login Test Suite...")
    generate_test_report()
