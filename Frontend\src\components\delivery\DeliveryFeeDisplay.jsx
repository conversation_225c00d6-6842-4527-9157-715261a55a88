import React, { useState } from 'react';
import { 
  Truck, 
  Clock, 
  MapPin, 
  Info, 
  Zap, 
  Gift,
  ChevronDown,
  ChevronUp,
  AlertCircle
} from 'lucide-react';
import { cn } from '../../utils/cn';
import { useDeliveryBreakdown } from '../../hooks/useDeliveryFee';
import Card from '../common/Card';
import Badge from '../common/Badge';

const DeliveryFeeDisplay = ({
  deliveryInfo,
  loading = false,
  showBreakdown = false,
  showEstimatedTime = true,
  showDistance = true,
  className = "",
  variant = "default" // "default", "compact", "detailed"
}) => {
  const [showDetails, setShowDetails] = useState(showBreakdown);
  const breakdown = useDeliveryBreakdown(deliveryInfo);

  if (loading) {
    return (
      <div className={cn("animate-pulse", className)}>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-300 rounded"></div>
          <div className="w-24 h-4 bg-gray-300 rounded"></div>
        </div>
      </div>
    );
  }

  if (!deliveryInfo) {
    return (
      <div className={cn("text-gray-500 text-sm", className)}>
        Select address to see delivery fee
      </div>
    );
  }

  if (!deliveryInfo.isDeliverable) {
    return (
      <div className={cn("flex items-center space-x-2 text-red-600", className)}>
        <AlertCircle size={16} />
        <span className="text-sm">{deliveryInfo.error}</span>
      </div>
    );
  }

  // Compact variant
  if (variant === "compact") {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <Truck size={14} className="text-gray-500" />
        <span className="text-sm font-medium">
          {deliveryInfo.fee === 0 ? (
            <span className="text-green-600">Free delivery</span>
          ) : (
            <span>${deliveryInfo.fee.toFixed(2)}</span>
          )}
        </span>
        {showEstimatedTime && deliveryInfo.estimatedTime && (
          <>
            <span className="text-gray-300">•</span>
            <Clock size={14} className="text-gray-500" />
            <span className="text-sm text-gray-600">
              {deliveryInfo.estimatedTime.min}-{deliveryInfo.estimatedTime.max} min
            </span>
          </>
        )}
      </div>
    );
  }

  // Default and detailed variants
  return (
    <Card className={cn("p-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <Truck size={20} className="text-primary-500" />
          <h3 className="font-medium">Delivery Information</h3>
        </div>
        
        {deliveryInfo.fee === 0 && (
          <Badge variant="success" size="small">
            <Gift size={12} className="mr-1" />
            Free
          </Badge>
        )}
      </div>

      {/* Main delivery info */}
      <div className="space-y-3">
        {/* Fee and time */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Delivery fee:</span>
            <span className="font-semibold">
              {deliveryInfo.fee === 0 ? (
                <span className="text-green-600">Free</span>
              ) : (
                <span>${deliveryInfo.fee.toFixed(2)}</span>
              )}
            </span>
          </div>
          
          {showEstimatedTime && deliveryInfo.estimatedTime && (
            <div className="flex items-center space-x-1 text-sm text-gray-600">
              <Clock size={14} />
              <span>
                {deliveryInfo.estimatedTime.min}-{deliveryInfo.estimatedTime.max} min
              </span>
            </div>
          )}
        </div>

        {/* Distance and zone */}
        {showDistance && (
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-1 text-gray-600">
              <MapPin size={14} />
              <span>{deliveryInfo.distance?.toFixed(1)} km away</span>
            </div>
            
            {deliveryInfo.zone && (
              <span className="text-gray-500">{deliveryInfo.zone}</span>
            )}
          </div>
        )}

        {/* Express delivery option */}
        {variant === "detailed" && (
          <div className="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Zap size={16} className="text-blue-600" />
              <div>
                <div className="text-sm font-medium text-blue-900">Express Delivery</div>
                <div className="text-xs text-blue-700">Get it 30% faster</div>
              </div>
            </div>
            <div className="text-sm font-medium text-blue-900">
              +${(deliveryInfo.fee * 0.8).toFixed(2)}
            </div>
          </div>
        )}

        {/* Breakdown toggle */}
        {breakdown && breakdown.items.length > 1 && (
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center space-x-1 text-sm text-primary-600 hover:text-primary-700 transition-colors"
          >
            <Info size={14} />
            <span>View breakdown</span>
            {showDetails ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
          </button>
        )}

        {/* Detailed breakdown */}
        {showDetails && breakdown && (
          <div className="border-t pt-3 space-y-2">
            <h4 className="text-sm font-medium text-gray-900">Fee Breakdown</h4>
            
            {breakdown.items.map((item, index) => (
              <div key={index} className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-2">
                  <span className={cn(
                    item.type === 'discount' && 'text-green-600',
                    item.type === 'modifier' && 'text-orange-600',
                    item.type === 'base' && 'text-gray-700',
                    item.type === 'distance' && 'text-blue-600'
                  )}>
                    {item.label}
                  </span>
                  
                  {item.multiplier && item.multiplier > 1 && (
                    <Badge variant="warning" size="small">
                      {item.multiplier}x
                    </Badge>
                  )}
                </div>
                
                <span className={cn(
                  "font-medium",
                  item.type === 'discount' && 'text-green-600',
                  item.amount > 0 && item.type !== 'discount' && 'text-gray-900',
                  item.amount < 0 && 'text-green-600'
                )}>
                  {item.amount >= 0 ? '+' : ''}${Math.abs(item.amount).toFixed(2)}
                </span>
              </div>
            ))}
            
            <div className="border-t pt-2 flex items-center justify-between font-medium">
              <span>Total Delivery Fee</span>
              <span className={cn(
                breakdown.total === 0 ? 'text-green-600' : 'text-gray-900'
              )}>
                {breakdown.total === 0 ? 'Free' : `$${breakdown.total.toFixed(2)}`}
              </span>
            </div>
          </div>
        )}

        {/* Free delivery progress */}
        {deliveryInfo.fee > 0 && variant === "detailed" && (
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-green-900">
                Free delivery at $50
              </span>
              <span className="text-sm text-green-700">
                ${(50 - (deliveryInfo.orderAmount || 0)).toFixed(2)} to go
              </span>
            </div>
            
            <div className="w-full bg-green-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${Math.min(((deliveryInfo.orderAmount || 0) / 50) * 100, 100)}%` 
                }}
              ></div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

// Simplified delivery fee badge
export const DeliveryFeeBadge = ({ deliveryInfo, loading, className = "" }) => {
  if (loading) {
    return (
      <div className={cn("animate-pulse bg-gray-200 h-5 w-16 rounded", className)}></div>
    );
  }

  if (!deliveryInfo?.isDeliverable) {
    return (
      <Badge variant="error" className={className}>
        Not available
      </Badge>
    );
  }

  if (deliveryInfo.fee === 0) {
    return (
      <Badge variant="success" className={className}>
        Free delivery
      </Badge>
    );
  }

  return (
    <Badge variant="secondary" className={className}>
      ${deliveryInfo.fee.toFixed(2)} delivery
    </Badge>
  );
};

// Delivery time badge
export const DeliveryTimeBadge = ({ deliveryInfo, loading, className = "" }) => {
  if (loading) {
    return (
      <div className={cn("animate-pulse bg-gray-200 h-5 w-20 rounded", className)}></div>
    );
  }

  if (!deliveryInfo?.estimatedTime) {
    return null;
  }

  return (
    <Badge variant="outline" className={cn("text-gray-600", className)}>
      <Clock size={12} className="mr-1" />
      {deliveryInfo.estimatedTime.min}-{deliveryInfo.estimatedTime.max} min
    </Badge>
  );
};

export default DeliveryFeeDisplay;
