import React from 'react';
import { Star, TrendingUp, Users, MessageSquare } from 'lucide-react';

const RatingDisplay = ({ 
  rating = 0, 
  totalRatings = 0, 
  size = 'medium',
  showCount = true,
  showText = true,
  variant = 'default',
  className = ''
}) => {
  const renderStars = () => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

    const starSize = {
      small: 12,
      medium: 16,
      large: 20,
      xl: 24
    }[size];

    return (
      <div className="flex items-center">
        {/* Full stars */}
        {[...Array(fullStars)].map((_, i) => (
          <Star
            key={`full-${i}`}
            size={starSize}
            className="text-yellow-400 fill-current"
          />
        ))}
        
        {/* Half star */}
        {hasHalfStar && (
          <div className="relative">
            <Star
              size={starSize}
              className="text-gray-300"
            />
            <div 
              className="absolute inset-0 overflow-hidden"
              style={{ width: '50%' }}
            >
              <Star
                size={starSize}
                className="text-yellow-400 fill-current"
              />
            </div>
          </div>
        )}
        
        {/* Empty stars */}
        {[...Array(emptyStars)].map((_, i) => (
          <Star
            key={`empty-${i}`}
            size={starSize}
            className="text-gray-300"
          />
        ))}
      </div>
    );
  };

  const getRatingText = () => {
    if (rating === 0) return 'No ratings yet';
    if (rating >= 4.5) return 'Excellent';
    if (rating >= 4.0) return 'Very Good';
    if (rating >= 3.5) return 'Good';
    if (rating >= 3.0) return 'Average';
    if (rating >= 2.0) return 'Below Average';
    return 'Poor';
  };

  const getRatingColor = () => {
    if (rating >= 4.0) return 'text-green-600';
    if (rating >= 3.0) return 'text-yellow-600';
    return 'text-red-600';
  };

  const baseClasses = `flex items-center space-x-2 ${className}`;

  if (variant === 'compact') {
    return (
      <div className={baseClasses}>
        {renderStars()}
        {showCount && totalRatings > 0 && (
          <span className={`text-sm ${size === 'small' ? 'text-xs' : ''} text-gray-600`}>
            ({totalRatings})
          </span>
        )}
      </div>
    );
  }

  if (variant === 'detailed') {
    return (
      <div className={`${baseClasses} flex-col items-start space-x-0 space-y-2`}>
        <div className="flex items-center space-x-2">
          {renderStars()}
          <span className={`font-semibold ${getRatingColor()}`}>
            {rating.toFixed(1)}
          </span>
        </div>
        
        {showText && (
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span className={getRatingColor()}>
              {getRatingText()}
            </span>
            {showCount && totalRatings > 0 && (
              <div className="flex items-center space-x-1">
                <Users size={14} />
                <span>{totalRatings} review{totalRatings !== 1 ? 's' : ''}</span>
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-semibold text-gray-900">Customer Rating</h4>
          <TrendingUp className="text-green-500" size={20} />
        </div>
        
        <div className="flex items-center space-x-3 mb-3">
          <span className={`text-3xl font-bold ${getRatingColor()}`}>
            {rating.toFixed(1)}
          </span>
          <div>
            {renderStars()}
            <p className={`text-sm ${getRatingColor()} mt-1`}>
              {getRatingText()}
            </p>
          </div>
        </div>
        
        {showCount && totalRatings > 0 && (
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <MessageSquare size={14} />
            <span>Based on {totalRatings} review{totalRatings !== 1 ? 's' : ''}</span>
          </div>
        )}
      </div>
    );
  }

  // Default variant
  return (
    <div className={baseClasses}>
      {renderStars()}
      <span className={`font-medium ${getRatingColor()}`}>
        {rating.toFixed(1)}
      </span>
      {showText && (
        <span className={`text-sm ${getRatingColor()}`}>
          {getRatingText()}
        </span>
      )}
      {showCount && totalRatings > 0 && (
        <span className="text-sm text-gray-600">
          ({totalRatings})
        </span>
      )}
    </div>
  );
};

export default RatingDisplay;
