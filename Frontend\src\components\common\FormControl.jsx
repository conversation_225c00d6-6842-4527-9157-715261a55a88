import React from 'react';
import { cn } from '../../utils/cn';

const FormControl = ({ 
  label, 
  htmlFor, 
  error, 
  helperText, 
  required = false, 
  className,
  children 
}) => {
  return (
    <div className={cn('mb-4', className)}>
      {label && (
        <label
          htmlFor={htmlFor}
          className="block mb-2 text-sm font-medium text-text-primary"
        >
          {label}
          {required && <span className="ml-1 text-accent-red">*</span>}
        </label>
      )}
      
      {children}
      
      {(error || helperText) && (
        <p className={`mt-1 text-sm ${error ? 'text-accent-red' : 'text-text-secondary'}`}>
          {error || helperText}
        </p>
      )}
    </div>
  );
};

export default FormControl;