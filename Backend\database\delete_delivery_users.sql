-- Delete Delivery Users SQL Script
-- WARNING: This will permanently delete delivery users and related data
-- Make sure to backup your database before running this script

-- Connect to the database
-- psql -h localhost -p 5432 -U delivery_admin -d afghan_delivery_system

-- Show current delivery users
SELECT 
    id, 
    user_name, 
    name, 
    phone, 
    email, 
    role,
    date_joined
FROM users_user 
WHERE role = 'delivery_agent'
ORDER BY date_joined DESC;

-- Count delivery users
SELECT COUNT(*) as delivery_user_count 
FROM users_user 
WHERE role = 'delivery_agent';

-- Show Afghan delivery agents
SELECT 
    ada.id,
    ada.agent_code,
    ada.full_name_dari,
    ada.phone_primary,
    ada.status,
    u.user_name,
    u.name
FROM afghan_delivery_agents ada
LEFT JOIN users_user u ON ada.user_id = u.id;

-- Count Afghan delivery agents
SELECT COUNT(*) as afghan_agent_count 
FROM afghan_delivery_agents;

-- Show regular delivery agent profiles
SELECT 
    dap.id,
    dap.full_name,
    dap.phone_number,
    dap.status,
    u.user_name,
    u.name
FROM deliveryagent_deliveryagentprofile dap
LEFT JOIN users_user u ON dap.user_id = u.id;

-- Count regular delivery agent profiles
SELECT COUNT(*) as regular_agent_count 
FROM deliveryagent_deliveryagentprofile;

-- ============================================================================
-- DELETION SCRIPT (UNCOMMENT TO EXECUTE)
-- ============================================================================

-- STEP 1: Delete training records for Afghan agents
-- DELETE FROM agent_training 
-- WHERE agent_id IN (
--     SELECT id FROM afghan_delivery_agents 
--     WHERE user_id IN (
--         SELECT id FROM users_user WHERE role = 'delivery_agent'
--     )
-- );

-- STEP 2: Delete payment records for Afghan agents
-- DELETE FROM agent_payments 
-- WHERE agent_id IN (
--     SELECT id FROM afghan_delivery_agents 
--     WHERE user_id IN (
--         SELECT id FROM users_user WHERE role = 'delivery_agent'
--     )
-- );

-- STEP 3: Update orders to remove delivery agent assignments
-- UPDATE orders_order 
-- SET delivery_agent_id = NULL 
-- WHERE delivery_agent_id IN (
--     SELECT id FROM users_user WHERE role = 'delivery_agent'
-- );

-- STEP 4: Delete Afghan delivery agent profiles
-- DELETE FROM afghan_delivery_agents 
-- WHERE user_id IN (
--     SELECT id FROM users_user WHERE role = 'delivery_agent'
-- );

-- STEP 5: Delete regular delivery agent profiles
-- DELETE FROM deliveryagent_deliveryagentprofile 
-- WHERE user_id IN (
--     SELECT id FROM users_user WHERE role = 'delivery_agent'
-- );

-- STEP 6: Finally delete the users
-- DELETE FROM users_user 
-- WHERE role = 'delivery_agent';

-- ============================================================================
-- VERIFICATION QUERIES (Run after deletion)
-- ============================================================================

-- Verify no delivery users remain
-- SELECT COUNT(*) as remaining_delivery_users 
-- FROM users_user 
-- WHERE role = 'delivery_agent';

-- Verify no orphaned Afghan agents
-- SELECT COUNT(*) as orphaned_afghan_agents 
-- FROM afghan_delivery_agents 
-- WHERE user_id NOT IN (SELECT id FROM users_user);

-- Verify no orphaned regular agents
-- SELECT COUNT(*) as orphaned_regular_agents 
-- FROM deliveryagent_deliveryagentprofile 
-- WHERE user_id NOT IN (SELECT id FROM users_user);

-- Show orders with NULL delivery agents (should be updated)
-- SELECT COUNT(*) as orders_with_null_agents 
-- FROM orders_order 
-- WHERE delivery_agent_id IS NULL;

-- ============================================================================
-- SAFE DELETION SCRIPT (Delete specific users by ID)
-- ============================================================================

-- Example: Delete specific user by ID (replace 123 with actual user ID)
-- BEGIN;
-- 
-- -- Delete training records
-- DELETE FROM agent_training 
-- WHERE agent_id IN (
--     SELECT id FROM afghan_delivery_agents WHERE user_id = 123
-- );
-- 
-- -- Delete payment records
-- DELETE FROM agent_payments 
-- WHERE agent_id IN (
--     SELECT id FROM afghan_delivery_agents WHERE user_id = 123
-- );
-- 
-- -- Update orders
-- UPDATE orders_order SET delivery_agent_id = NULL WHERE delivery_agent_id = 123;
-- 
-- -- Delete agent profiles
-- DELETE FROM afghan_delivery_agents WHERE user_id = 123;
-- DELETE FROM deliveryagent_deliveryagentprofile WHERE user_id = 123;
-- 
-- -- Delete user
-- DELETE FROM users_user WHERE id = 123;
-- 
-- COMMIT;

-- ============================================================================
-- BACKUP SCRIPT (Run before deletion)
-- ============================================================================

-- Create backup tables
-- CREATE TABLE backup_users_delivery AS 
-- SELECT * FROM users_user WHERE role = 'delivery_agent';

-- CREATE TABLE backup_afghan_agents AS 
-- SELECT * FROM afghan_delivery_agents 
-- WHERE user_id IN (SELECT id FROM users_user WHERE role = 'delivery_agent');

-- CREATE TABLE backup_regular_agents AS 
-- SELECT * FROM deliveryagent_deliveryagentprofile 
-- WHERE user_id IN (SELECT id FROM users_user WHERE role = 'delivery_agent');

-- CREATE TABLE backup_training AS 
-- SELECT * FROM agent_training 
-- WHERE agent_id IN (
--     SELECT id FROM afghan_delivery_agents 
--     WHERE user_id IN (SELECT id FROM users_user WHERE role = 'delivery_agent')
-- );

-- CREATE TABLE backup_payments AS 
-- SELECT * FROM agent_payments 
-- WHERE agent_id IN (
--     SELECT id FROM afghan_delivery_agents 
--     WHERE user_id IN (SELECT id FROM users_user WHERE role = 'delivery_agent')
-- );

-- ============================================================================
-- RESTORE SCRIPT (If you need to restore from backup)
-- ============================================================================

-- Restore users
-- INSERT INTO users_user SELECT * FROM backup_users_delivery;

-- Restore Afghan agents
-- INSERT INTO afghan_delivery_agents SELECT * FROM backup_afghan_agents;

-- Restore regular agents
-- INSERT INTO deliveryagent_deliveryagentprofile SELECT * FROM backup_regular_agents;

-- Restore training records
-- INSERT INTO agent_training SELECT * FROM backup_training;

-- Restore payment records
-- INSERT INTO agent_payments SELECT * FROM backup_payments;

-- Drop backup tables after successful restore
-- DROP TABLE backup_users_delivery;
-- DROP TABLE backup_afghan_agents;
-- DROP TABLE backup_regular_agents;
-- DROP TABLE backup_training;
-- DROP TABLE backup_payments;
