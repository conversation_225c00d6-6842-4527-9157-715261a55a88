import React from "react";
import { useNavigate } from "react-router-dom";
import {
  CheckCircle,
  Clock,
  Mail,
  Phone,
  FileText,
  ArrowRight,
  Home,
  MessageCircle,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";

const RegistrationSuccess = () => {
  const navigate = useNavigate();

  const nextSteps = [
    {
      icon: <FileText className='h-5 w-5' />,
      title: "Document Review",
      description: "Our team will review your uploaded documents",
      timeframe: "2-4 hours",
      status: "pending",
    },
    {
      icon: <CheckCircle className='h-5 w-5' />,
      title: "Background Check",
      description: "Comprehensive background verification process",
      timeframe: "24-48 hours",
      status: "pending",
    },
    {
      icon: <Mail className='h-5 w-5' />,
      title: "Approval Notification",
      description: "You'll receive an email once approved",
      timeframe: "48-72 hours",
      status: "pending",
    },
    {
      icon: <ArrowRight className='h-5 w-5' />,
      title: "Start Delivering",
      description: "Begin accepting delivery orders",
      timeframe: "Immediately after approval",
      status: "upcoming",
    },
  ];

  return (
    <div className='min-h-screen bg-gradient-to-br from-accent-green-light via-primary-50 to-orange-50 py-8'>
      <div className='max-w-5xl mx-auto px-4'>
        {/* Success Header */}
        <div className='text-center mb-10'>
          <div className='w-24 h-24 bg-gradient-to-r from-accent-green to-accent-green-dark rounded-full flex items-center justify-center mx-auto mb-6 shadow-2xl animate-pulse'>
            <CheckCircle className='h-14 w-14 text-white' />
          </div>
          <h1 className='text-4xl font-bold bg-gradient-to-r from-accent-green to-accent-green-dark bg-clip-text text-transparent mb-4'>
            Application Submitted Successfully!
          </h1>
          <p className='text-xl text-gray-600 max-w-3xl mx-auto'>
            Thank you for applying to become a delivery agent. Your application
            is now under review and you'll hear from us soon.
          </p>
        </div>

        {/* Application Details */}
        <Card className='p-8 mb-10 shadow-xl border-0 bg-white rounded-2xl'>
          <h2 className='text-2xl font-bold text-gray-900 mb-6 flex items-center'>
            <FileText className='h-6 w-6 mr-3 text-blue-600' />
            Application Details
          </h2>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
            <div className='bg-primary-50 p-4 rounded-xl border border-primary-200'>
              <p className='text-sm text-primary-600 font-medium uppercase tracking-wide mb-1'>
                Application ID
              </p>
              <p className='text-xl font-bold text-primary-900'>#DA2024001</p>
            </div>
            <div className='bg-orange-50 p-4 rounded-xl border border-orange-200'>
              <p className='text-sm text-orange-600 font-medium uppercase tracking-wide mb-1'>
                Submitted On
              </p>
              <p className='text-xl font-bold text-orange-900'>
                {new Date().toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </p>
            </div>
            <div className='bg-yellow-50 p-4 rounded-xl border border-yellow-200'>
              <p className='text-sm text-yellow-600 font-medium uppercase tracking-wide mb-1'>
                Status
              </p>
              <div className='flex items-center'>
                <div className='w-3 h-3 bg-yellow-500 rounded-full mr-3 animate-pulse'></div>
                <span className='text-xl font-bold text-yellow-700'>
                  Under Review
                </span>
              </div>
            </div>
            <div className='bg-accent-green-light p-4 rounded-xl border border-accent-green'>
              <p className='text-sm text-accent-green-dark font-medium uppercase tracking-wide mb-1'>
                Expected Approval
              </p>
              <p className='text-xl font-bold text-accent-green-dark'>
                2-3 business days
              </p>
            </div>
          </div>
        </Card>

        {/* Next Steps */}
        <Card className='p-6 mb-8'>
          <h2 className='text-xl font-semibold text-gray-900 mb-6'>
            What Happens Next?
          </h2>
          <div className='space-y-6'>
            {nextSteps.map((step, index) => (
              <div key={index} className='flex items-start'>
                <div
                  className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4 ${
                    step.status === "pending"
                      ? "bg-yellow-100 text-yellow-600"
                      : "bg-gray-100 text-gray-400"
                  }`}
                >
                  {step.icon}
                </div>
                <div className='flex-grow'>
                  <div className='flex items-center justify-between'>
                    <h3 className='text-lg font-medium text-gray-900'>
                      {step.title}
                    </h3>
                    <span className='text-sm text-gray-500'>
                      {step.timeframe}
                    </span>
                  </div>
                  <p className='text-gray-600 mt-1'>{step.description}</p>
                  {step.status === "pending" && (
                    <div className='flex items-center mt-2'>
                      <Clock className='h-4 w-4 text-yellow-500 mr-1' />
                      <span className='text-sm text-yellow-600'>
                        In Progress
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Important Information */}
        <Card className='p-6 mb-8 bg-blue-50 border-blue-200'>
          <h2 className='text-xl font-semibold text-blue-900 mb-4'>
            Important Information
          </h2>
          <div className='space-y-3'>
            <div className='flex items-start'>
              <Mail className='h-5 w-5 text-blue-600 mt-0.5 mr-3' />
              <div>
                <p className='text-blue-900 font-medium'>Check Your Email</p>
                <p className='text-blue-800 text-sm'>
                  We'll send updates about your application status to your
                  registered email address.
                </p>
              </div>
            </div>
            <div className='flex items-start'>
              <Phone className='h-5 w-5 text-blue-600 mt-0.5 mr-3' />
              <div>
                <p className='text-blue-900 font-medium'>
                  Keep Your Phone Handy
                </p>
                <p className='text-blue-800 text-sm'>
                  We may call you if we need additional information or
                  clarification.
                </p>
              </div>
            </div>
            <div className='flex items-start'>
              <FileText className='h-5 w-5 text-blue-600 mt-0.5 mr-3' />
              <div>
                <p className='text-blue-900 font-medium'>
                  Additional Documents
                </p>
                <p className='text-blue-800 text-sm'>
                  If we need any additional documents, we'll contact you with
                  specific instructions.
                </p>
              </div>
            </div>
          </div>
        </Card>

        {/* Contact Support */}
        <Card className='p-6 mb-8'>
          <h2 className='text-xl font-semibold text-gray-900 mb-4'>
            Need Help?
          </h2>
          <p className='text-gray-600 mb-4'>
            If you have any questions about your application or the process,
            don't hesitate to contact us.
          </p>
          <div className='flex flex-col sm:flex-row gap-4'>
            <Button
              variant='outline'
              icon={<MessageCircle className='h-4 w-4' />}
              className='flex-1'
            >
              Live Chat Support
            </Button>
            <Button
              variant='outline'
              icon={<Mail className='h-4 w-4' />}
              className='flex-1'
            >
              Email Support
            </Button>
            <Button
              variant='outline'
              icon={<Phone className='h-4 w-4' />}
              className='flex-1'
            >
              Call Support
            </Button>
          </div>
        </Card>

        {/* Action Buttons */}
        <div className='flex flex-col sm:flex-row gap-6 justify-center'>
          <Button
            onClick={() => navigate("/")}
            variant='outline'
            icon={<Home className='h-5 w-5' />}
            className='flex-1 sm:flex-none px-8 py-4 border-2 border-gray-300 hover:border-primary-400 hover:text-primary-600 hover:bg-primary-50 rounded-xl text-lg font-semibold transition-all duration-200'
          >
            Go to Homepage
          </Button>
          <Button
            onClick={() => navigate("/delivery-login-test")}
            icon={<ArrowRight className='h-5 w-5' />}
            iconPosition='right'
            className='flex-1 sm:flex-none px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105'
          >
            Check Application Status
          </Button>
        </div>

        {/* Footer Note */}
        <div className='text-center mt-10 p-6 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl border border-gray-200 shadow-lg'>
          <p className='text-gray-700 text-lg'>
            <span className='font-bold text-primary-600'>
              Application Reference:
            </span>{" "}
            DA2024001 |
            <span className='font-bold text-orange-600'> Submitted:</span>{" "}
            {new Date().toLocaleDateString()} |
            <span className='font-bold text-yellow-600'> Status:</span> Under
            Review
          </p>
        </div>
      </div>
    </div>
  );
};

export default RegistrationSuccess;
