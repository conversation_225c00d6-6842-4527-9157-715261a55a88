import React from 'react';
import { cn } from '../../utils/cn';

const Loader = ({ size = 'medium', color = 'primary', fullPage = false }) => {
  const sizeClasses = {
    small: 'w-5 h-5',
    medium: 'w-8 h-8',
    large: 'w-12 h-12'
  };

  const colorClasses = {
    primary: 'border-primary-500',
    white: 'border-white'
  };

  const loaderClasses = cn(
    'border-4 rounded-full animate-spin',
    sizeClasses[size],
    `border-t-transparent ${colorClasses[color]}`,
  );

  if (fullPage) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-background-light bg-opacity-75 z-50">
        <div className={loaderClasses}></div>
      </div>
    );
  }

  return <div className={loaderClasses}></div>;
};

export default Loader;