import React from 'react';
import { SkeletonCard, SkeletonRectangle, SkeletonText, SkeletonCircle } from './SkeletonBase';

// Stats Card Skeleton
export const StatsCardSkeleton = ({ className }) => {
  return (
    <SkeletonCard className={className}>
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <SkeletonText lineHeight="h-4" className="w-24" />
          <SkeletonText lineHeight="h-8" className="w-16" />
          <SkeletonText lineHeight="h-3" className="w-20" />
        </div>
        <SkeletonCircle size="w-12 h-12" />
      </div>
    </SkeletonCard>
  );
};

// Chart Skeleton
export const ChartSkeleton = ({ height = 'h-64', className }) => {
  return (
    <SkeletonCard className={className}>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <SkeletonText lineHeight="h-5" className="w-32" />
          <SkeletonText lineHeight="h-4" className="w-20" />
        </div>
        <SkeletonRectangle height={height} />
        <div className="flex justify-center space-x-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-2">
              <SkeletonCircle size="w-3 h-3" />
              <SkeletonText lineHeight="h-3" className="w-16" />
            </div>
          ))}
        </div>
      </div>
    </SkeletonCard>
  );
};

// Table Skeleton
export const TableSkeleton = ({ rows = 5, columns = 4, className }) => {
  return (
    <SkeletonCard className={className}>
      <div className="space-y-4">
        {/* Table Header */}
        <div className="flex items-center justify-between">
          <SkeletonText lineHeight="h-5" className="w-32" />
          <SkeletonText lineHeight="h-4" className="w-20" />
        </div>
        
        {/* Table Headers */}
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, index) => (
            <SkeletonText key={index} lineHeight="h-4" className="w-20" />
          ))}
        </div>
        
        {/* Table Rows */}
        <div className="space-y-3">
          {Array.from({ length: rows }).map((_, rowIndex) => (
            <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
              {Array.from({ length: columns }).map((_, colIndex) => (
                <SkeletonText key={colIndex} lineHeight="h-4" className={colIndex === 0 ? "w-32" : "w-16"} />
              ))}
            </div>
          ))}
        </div>
      </div>
    </SkeletonCard>
  );
};

// Activity Feed Skeleton
export const ActivityFeedSkeleton = ({ count = 5, className }) => {
  return (
    <SkeletonCard className={className}>
      <div className="space-y-4">
        <SkeletonText lineHeight="h-5" className="w-32" />
        <div className="space-y-3">
          {Array.from({ length: count }).map((_, index) => (
            <div key={index} className="flex items-start space-x-3">
              <SkeletonCircle size="w-8 h-8" />
              <div className="flex-1 space-y-1">
                <SkeletonText lineHeight="h-4" className="w-full" />
                <SkeletonText lineHeight="h-3" className="w-3/4" />
                <SkeletonText lineHeight="h-3" className="w-16" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </SkeletonCard>
  );
};

// Menu Item Skeleton
export const MenuItemSkeleton = ({ className }) => {
  return (
    <SkeletonCard className={className}>
      <div className="flex space-x-4">
        <SkeletonRectangle width="w-20" height="h-20" rounded="lg" />
        <div className="flex-1 space-y-2">
          <SkeletonText lineHeight="h-5" className="w-3/4" />
          <SkeletonText lineHeight="h-4" className="w-full" />
          <SkeletonText lineHeight="h-4" className="w-1/2" />
          <div className="flex items-center justify-between">
            <SkeletonText lineHeight="h-5" className="w-16" />
            <SkeletonRectangle width="w-20" height="h-8" rounded="md" />
          </div>
        </div>
      </div>
    </SkeletonCard>
  );
};

// Complete Dashboard Skeleton
export const DashboardSkeleton = ({ userRole = 'customer' }) => {
  if (userRole === 'restaurant') {
    return (
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <StatsCardSkeleton key={index} />
          ))}
        </div>
        
        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ChartSkeleton />
          <ChartSkeleton />
        </div>
        
        {/* Recent Orders */}
        <TableSkeleton rows={5} columns={5} />
      </div>
    );
  }

  if (userRole === 'delivery') {
    return (
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <StatsCardSkeleton key={index} />
          ))}
        </div>
        
        {/* Active Orders */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <SkeletonCard key={index}>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <SkeletonText lineHeight="h-5" className="w-32" />
                    <SkeletonRectangle width="w-16" height="h-6" rounded="full" />
                  </div>
                  <SkeletonText lineHeight="h-4" className="w-full" />
                  <div className="flex space-x-2">
                    <SkeletonRectangle width="w-20" height="h-8" rounded="md" />
                    <SkeletonRectangle width="w-20" height="h-8" rounded="md" />
                  </div>
                </div>
              </SkeletonCard>
            ))}
          </div>
          <ChartSkeleton height="h-48" />
        </div>
      </div>
    );
  }

  // Customer dashboard
  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <SkeletonCard key={index} className="text-center">
            <SkeletonCircle size="w-12 h-12" className="mx-auto mb-2" />
            <SkeletonText lineHeight="h-4" className="w-20 mx-auto" />
          </SkeletonCard>
        ))}
      </div>
      
      {/* Recent Orders */}
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <SkeletonCard key={index}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <SkeletonRectangle width="w-12" height="h-12" rounded="lg" />
                <div className="space-y-1">
                  <SkeletonText lineHeight="h-4" className="w-32" />
                  <SkeletonText lineHeight="h-3" className="w-24" />
                </div>
              </div>
              <div className="text-right space-y-1">
                <SkeletonRectangle width="w-16" height="h-5" rounded="full" />
                <SkeletonText lineHeight="h-3" className="w-12" />
              </div>
            </div>
          </SkeletonCard>
        ))}
      </div>
      
      {/* Activity Feed */}
      <ActivityFeedSkeleton />
    </div>
  );
};

export default DashboardSkeleton;
