import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  MapPin,
  Phone,
  Mail,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Upload,
  X,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { useAuth } from "../../context/AuthContext";
import { useRestaurant } from "../../context/RestaurantContext";

function RestaurantApproval() {
  const { user: currentUser } = useAuth();
  const {
    restaurants,
    loading,
    error,
    getRestaurants,
    updateRestaurant,
    deleteRestaurant,
    clearError,
  } = useRestaurant();

  const [filteredRestaurants, setFilteredRestaurants] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("pending");
  const [selectedRestaurant, setSelectedRestaurant] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showAddRestaurant, setShowAddRestaurant] = useState(false);
  const [newRestaurant, setNewRestaurant] = useState({
    name: "",
    owner: "",
    email: "",
    phone: "",
    address: "",
    cuisine: "",
    documents: {
      businessLicense: null,
      taxCertificate: null,
      menu: null,
    },
  });

  useEffect(() => {
    loadRestaurants();
  }, []);

  const loadRestaurants = async () => {
    await getRestaurants();
  };

  useEffect(() => {
    let filtered = [...restaurants];

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((r) => r.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (r) =>
          r.name.toLowerCase().includes(query) ||
          r.owner.toLowerCase().includes(query) ||
          r.email.toLowerCase().includes(query) ||
          r.address.toLowerCase().includes(query)
      );
    }

    setFilteredRestaurants(filtered);
  }, [restaurants, statusFilter, searchQuery]);

  const handleApprove = async (restaurant) => {
    try {
      clearError();

      // For now, we'll just update the restaurant with a note
      // In the future, you might have a specific approval API endpoint
      const updateData = {
        ...restaurant,
        notes: "Approved by admin",
      };

      const result = await updateRestaurant(restaurant.id, updateData);

      if (result.success) {
        setShowDetails(false);
        // Refresh the list
        await getRestaurants();
      }
    } catch (error) {
      console.error("Error approving restaurant:", error);
    }
  };

  const handleReject = async (restaurant) => {
    if (!window.confirm("Are you sure you want to reject this restaurant?")) {
      return;
    }

    try {
      clearError();

      // For rejection, you might want to delete the restaurant or mark it as rejected
      // For now, we'll delete it - you can change this based on your business logic
      const result = await deleteRestaurant(restaurant.id);

      if (result.success) {
        setShowDetails(false);
        // Refresh the list
        await getRestaurants();
      }
    } catch (error) {
      console.error("Error rejecting restaurant:", error);
    }
  };

  const handleAddRestaurant = async () => {
    try {
      // In a real app, this would be an API call
      const restaurantToAdd = {
        id: Date.now(),
        ...newRestaurant,
        status: "pending",
        submittedAt: new Date(),
        notes: "",
      };

      setRestaurants([...restaurants, restaurantToAdd]);
      setShowAddRestaurant(false);
      setNewRestaurant({
        name: "",
        owner: "",
        email: "",
        phone: "",
        address: "",
        cuisine: "",
        documents: {
          businessLicense: null,
          taxCertificate: null,
          menu: null,
        },
      });
    } catch (error) {
      console.error("Error adding restaurant:", error);
    }
  };

  const handleDeleteRestaurant = async (restaurantId) => {
    try {
      // In a real app, this would be an API call
      setRestaurants(restaurants.filter((r) => r.id !== restaurantId));
      setShowDetails(false);
    } catch (error) {
      console.error("Error deleting restaurant:", error);
    }
  };

  const handleUpdateRestaurant = async (updatedRestaurant) => {
    try {
      // In a real app, this would be an API call
      setRestaurants(
        restaurants.map((r) =>
          r.id === updatedRestaurant.id ? updatedRestaurant : r
        )
      );
      setShowDetails(false);
    } catch (error) {
      console.error("Error updating restaurant:", error);
    }
  };

  const handleFileUpload = (type, file) => {
    // In a real app, this would upload the file to a server
    setNewRestaurant({
      ...newRestaurant,
      documents: {
        ...newRestaurant.documents,
        [type]: file.name,
      },
    });
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "pending":
        return <Badge className='bg-yellow-100 text-yellow-800'>Pending</Badge>;
      case "approved":
        return <Badge className='bg-green-100 text-green-800'>Approved</Badge>;
      case "rejected":
        return <Badge className='bg-red-100 text-red-800'>Rejected</Badge>;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className='flex justify-center items-center h-[300px]'>
        <div className='animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500'></div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex justify-between items-center'>
        <h1 className='text-2xl font-bold'>Restaurant Approvals</h1>
        <div className='flex items-center space-x-4'>
          <div className='relative'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={20}
            />
            <input
              type='text'
              placeholder='Search restaurants...'
              className='pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <select
            className='px-4 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value='all'>All Status</option>
            <option value='pending'>Pending</option>
            <option value='approved'>Approved</option>
            <option value='rejected'>Rejected</option>
          </select>
          <Button
            variant='primary'
            icon={<Upload size={20} />}
            onClick={() => setShowAddRestaurant(true)}
          >
            Add Restaurant
          </Button>
        </div>
      </div>

      {/* Restaurant List */}
      <div className='grid gap-4'>
        {filteredRestaurants.map((restaurant) => (
          <Card key={restaurant.id}>
            <div className='p-4'>
              <div className='flex justify-between items-start'>
                <div>
                  <div className='flex items-center space-x-3'>
                    <h3 className='text-lg font-semibold'>{restaurant.name}</h3>
                    {getStatusBadge(restaurant.status)}
                  </div>
                  <div className='mt-2 space-y-1'>
                    <div className='flex items-center text-sm text-gray-600'>
                      <MapPin size={16} className='mr-2' />
                      {restaurant.address}
                    </div>
                    <div className='flex items-center text-sm text-gray-600'>
                      <Phone size={16} className='mr-2' />
                      {restaurant.phone}
                    </div>
                    <div className='flex items-center text-sm text-gray-600'>
                      <Mail size={16} className='mr-2' />
                      {restaurant.email}
                    </div>
                  </div>
                </div>
                <div className='flex items-center space-x-2'>
                  <Button
                    variant='outline'
                    size='small'
                    onClick={() => {
                      setSelectedRestaurant(restaurant);
                      setShowDetails(true);
                    }}
                  >
                    View Details
                  </Button>
                  {restaurant.status === "pending" && (
                    <>
                      <Button
                        variant='success'
                        size='small'
                        icon={<CheckCircle size={16} />}
                        onClick={() => handleApprove(restaurant)}
                      >
                        Approve
                      </Button>
                      <Button
                        variant='danger'
                        size='small'
                        icon={<XCircle size={16} />}
                        onClick={() => handleReject(restaurant)}
                      >
                        Reject
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Add Restaurant Modal */}
      {showAddRestaurant && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg w-full max-w-2xl'>
            <div className='p-6 border-b'>
              <div className='flex justify-between items-center'>
                <h2 className='text-xl font-semibold'>Add New Restaurant</h2>
                <button
                  className='text-gray-400 hover:text-gray-600'
                  onClick={() => setShowAddRestaurant(false)}
                >
                  <X size={24} />
                </button>
              </div>
            </div>

            <div className='p-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div className='space-y-4'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Restaurant Name
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.name}
                      onChange={(e) =>
                        setNewRestaurant({
                          ...newRestaurant,
                          name: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Owner Name
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.owner}
                      onChange={(e) =>
                        setNewRestaurant({
                          ...newRestaurant,
                          owner: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Email
                    </label>
                    <input
                      type='email'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.email}
                      onChange={(e) =>
                        setNewRestaurant({
                          ...newRestaurant,
                          email: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Phone
                    </label>
                    <input
                      type='tel'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.phone}
                      onChange={(e) =>
                        setNewRestaurant({
                          ...newRestaurant,
                          phone: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>

                <div className='space-y-4'>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Address
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.address}
                      onChange={(e) =>
                        setNewRestaurant({
                          ...newRestaurant,
                          address: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Cuisine Type
                    </label>
                    <input
                      type='text'
                      className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      value={newRestaurant.cuisine}
                      onChange={(e) =>
                        setNewRestaurant({
                          ...newRestaurant,
                          cuisine: e.target.value,
                        })
                      }
                    />
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Business License
                    </label>
                    <div className='mt-1'>
                      <input
                        type='file'
                        className='hidden'
                        id='businessLicense'
                        onChange={(e) =>
                          handleFileUpload("businessLicense", e.target.files[0])
                        }
                      />
                      <label
                        htmlFor='businessLicense'
                        className='cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50'
                      >
                        <Upload size={16} className='mr-2' />
                        Upload File
                      </label>
                      {newRestaurant.documents.businessLicense && (
                        <span className='ml-2 text-sm text-gray-600'>
                          {newRestaurant.documents.businessLicense}
                        </span>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className='block text-sm font-medium text-gray-700'>
                      Tax Certificate
                    </label>
                    <div className='mt-1'>
                      <input
                        type='file'
                        className='hidden'
                        id='taxCertificate'
                        onChange={(e) =>
                          handleFileUpload("taxCertificate", e.target.files[0])
                        }
                      />
                      <label
                        htmlFor='taxCertificate'
                        className='cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50'
                      >
                        <Upload size={16} className='mr-2' />
                        Upload File
                      </label>
                      {newRestaurant.documents.taxCertificate && (
                        <span className='ml-2 text-sm text-gray-600'>
                          {newRestaurant.documents.taxCertificate}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className='mt-6 flex justify-end space-x-3'>
                <Button
                  variant='outline'
                  onClick={() => setShowAddRestaurant(false)}
                >
                  Cancel
                </Button>
                <Button variant='primary' onClick={handleAddRestaurant}>
                  Add Restaurant
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Restaurant Details Modal */}
      {showDetails && selectedRestaurant && (
        <div className='fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50'>
          <div className='bg-white rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-auto'>
            <div className='p-6 border-b'>
              <div className='flex justify-between items-start'>
                <div>
                  <h2 className='text-xl font-semibold'>
                    {selectedRestaurant.name}
                  </h2>
                  <div className='flex items-center mt-1'>
                    <Clock size={16} className='text-gray-500 mr-1' />
                    <span className='text-sm text-gray-500'>
                      Submitted{" "}
                      {new Date(
                        selectedRestaurant.submittedAt
                      ).toLocaleString()}
                    </span>
                  </div>
                </div>
                <button
                  className='text-gray-400 hover:text-gray-600'
                  onClick={() => setShowDetails(false)}
                >
                  <XCircle size={24} />
                </button>
              </div>
            </div>

            <div className='p-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <h3 className='text-lg font-semibold mb-4'>
                    Restaurant Information
                  </h3>
                  <div className='space-y-4'>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Owner Name
                      </label>
                      <input
                        type='text'
                        className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                        value={selectedRestaurant.owner}
                        onChange={(e) =>
                          setSelectedRestaurant({
                            ...selectedRestaurant,
                            owner: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Cuisine Type
                      </label>
                      <input
                        type='text'
                        className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                        value={selectedRestaurant.cuisine}
                        onChange={(e) =>
                          setSelectedRestaurant({
                            ...selectedRestaurant,
                            cuisine: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Address
                      </label>
                      <input
                        type='text'
                        className='mt-1 w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                        value={selectedRestaurant.address}
                        onChange={(e) =>
                          setSelectedRestaurant({
                            ...selectedRestaurant,
                            address: e.target.value,
                          })
                        }
                      />
                    </div>
                    <div>
                      <label className='block text-sm font-medium text-gray-700'>
                        Contact
                      </label>
                      <div className='mt-1 space-y-2'>
                        <input
                          type='tel'
                          className='w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                          value={selectedRestaurant.phone}
                          onChange={(e) =>
                            setSelectedRestaurant({
                              ...selectedRestaurant,
                              phone: e.target.value,
                            })
                          }
                        />
                        <input
                          type='email'
                          className='w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                          value={selectedRestaurant.email}
                          onChange={(e) =>
                            setSelectedRestaurant({
                              ...selectedRestaurant,
                              email: e.target.value,
                            })
                          }
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-semibold mb-4'>Documents</h3>
                  <div className='space-y-4'>
                    <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                      <div className='flex items-center'>
                        <FileText size={20} className='text-gray-500 mr-2' />
                        <span>Business License</span>
                      </div>
                      <Button variant='outline' size='small'>
                        View
                      </Button>
                    </div>
                    <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                      <div className='flex items-center'>
                        <FileText size={20} className='text-gray-500 mr-2' />
                        <span>Tax Certificate</span>
                      </div>
                      <Button variant='outline' size='small'>
                        View
                      </Button>
                    </div>
                    <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                      <div className='flex items-center'>
                        <FileText size={20} className='text-gray-500 mr-2' />
                        <span>Menu</span>
                      </div>
                      <Button variant='outline' size='small'>
                        View
                      </Button>
                    </div>
                  </div>

                  <div className='mt-6'>
                    <label className='block text-sm font-medium text-gray-700 mb-2'>
                      Admin Notes
                    </label>
                    <textarea
                      className='w-full p-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                      rows='3'
                      value={selectedRestaurant.notes}
                      onChange={(e) =>
                        setSelectedRestaurant({
                          ...selectedRestaurant,
                          notes: e.target.value,
                        })
                      }
                      placeholder='Add notes about this restaurant...'
                    />
                  </div>
                </div>
              </div>

              <div className='mt-6 pt-6 border-t flex justify-end space-x-3'>
                {selectedRestaurant.status === "pending" && (
                  <>
                    <Button
                      variant='success'
                      icon={<CheckCircle size={16} />}
                      onClick={() => handleApprove(selectedRestaurant)}
                    >
                      Approve Restaurant
                    </Button>
                    <Button
                      variant='danger'
                      icon={<XCircle size={16} />}
                      onClick={() => handleReject(selectedRestaurant)}
                    >
                      Reject Application
                    </Button>
                  </>
                )}
                <Button
                  variant='danger'
                  onClick={() => handleDeleteRestaurant(selectedRestaurant.id)}
                >
                  Delete Restaurant
                </Button>
                <Button
                  variant='primary'
                  onClick={() => handleUpdateRestaurant(selectedRestaurant)}
                >
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default RestaurantApproval;
