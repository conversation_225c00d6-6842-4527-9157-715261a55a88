import React, { useState } from "react";
import { authApi } from "../../utils/authApi";

const EmailAuthTest = () => {
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [testEmail, setTestEmail] = useState("");

  const addResult = (test, result) => {
    setResults(prev => [...prev, { 
      test, 
      result, 
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testEmailRegistration = async () => {
    if (!testEmail) {
      alert("Please enter an email address first");
      return;
    }

    setLoading(true);
    try {
      const registrationData = {
        name: "Email Test User",
        user_name: "emailtest" + Date.now(),
        phone: "+93700000000",
        email: testEmail,
        password: "123456",
        role: "customer"
      };

      console.log("🧪 Testing email registration:", registrationData);

      const result = await authApi.register(registrationData);
      
      addResult("Email Registration Test", {
        success: result.success,
        message: result.success ? 
          `✅ Registration successful! Check ${testEmail} for <NAME_EMAIL>` : 
          `❌ Registration failed: ${result.error}`,
        email: testEmail,
        details: result,
        nextStep: result.success ? "Check your email for OTP code" : "Fix the error and try again"
      });

    } catch (error) {
      addResult("Email Registration Test", {
        success: false,
        message: `❌ Registration error: ${error.message}`,
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  const testEmailVerification = async () => {
    if (!testEmail) {
      alert("Please enter an email address first");
      return;
    }

    const otp = prompt("Enter the 6-digit OTP code you received:");
    if (!otp) return;

    setLoading(true);
    try {
      const result = await authApi.verifyEmail({
        email: testEmail,
        otp: otp
      });

      addResult("Email Verification Test", {
        success: result.success,
        message: result.success ? 
          "✅ Email verified successfully! You can now login." : 
          `❌ Verification failed: ${result.error}`,
        email: testEmail,
        otp: otp,
        details: result
      });

    } catch (error) {
      addResult("Email Verification Test", {
        success: false,
        message: `❌ Verification error: ${error.message}`,
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  const testResendOTP = async () => {
    if (!testEmail) {
      alert("Please enter an email address first");
      return;
    }

    setLoading(true);
    try {
      const result = await authApi.resendEmailVerification({
        email: testEmail
      });

      addResult("Resend OTP Test", {
        success: result.success,
        message: result.success ? 
          `✅ OTP resent to ${testEmail}` : 
          `❌ Failed to resend: ${result.error}`,
        email: testEmail,
        details: result
      });

    } catch (error) {
      addResult("Resend OTP Test", {
        success: false,
        message: `❌ Resend error: ${error.message}`,
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => setResults([]);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">📧 Email Authentication Test</h1>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h2 className="font-semibold text-blue-800 mb-2">✅ Email Authentication Flow:</h2>
        <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
          <li>Enter your email address below</li>
          <li>Click "Test Email Registration"</li>
          <li>Check your email for OTP from <strong><EMAIL></strong></li>
          <li>Click "Test Email Verification" and enter the OTP</li>
          <li>Registration complete - you can now login!</li>
        </ol>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <h3 className="font-semibold text-yellow-800 mb-2">📋 Requirements:</h3>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>✅ Valid email address (Gmail, Yahoo, Outlook, etc.)</li>
          <li>✅ Access to your email inbox</li>
          <li>✅ Backend API: https://afghansufra.luilala.com/api</li>
          <li>✅ OTP sender: <EMAIL></li>
        </ul>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Your Email Address:
        </label>
        <input
          type="email"
          value={testEmail}
          onChange={(e) => setTestEmail(e.target.value)}
          placeholder="Enter your email address"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <button
          onClick={testEmailRegistration}
          disabled={loading || !testEmail}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          📧 Test Email Registration
        </button>
        
        <button
          onClick={testEmailVerification}
          disabled={loading || !testEmail}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          🔐 Test Email Verification
        </button>
        
        <button
          onClick={testResendOTP}
          disabled={loading || !testEmail}
          className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600 disabled:opacity-50"
        >
          🔄 Test Resend OTP
        </button>
      </div>

      <div className="flex gap-4 mb-6">
        <button
          onClick={clearResults}
          className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
        >
          Clear Results
        </button>
      </div>

      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="mt-2">Testing email authentication...</p>
        </div>
      )}

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Test Results:</h2>
        {results.length === 0 ? (
          <p className="text-gray-500">No tests run yet. Enter your email and click a test button above.</p>
        ) : (
          results.map((item, index) => (
            <div key={index} className={`border rounded p-4 ${item.result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-semibold">{item.test}</h3>
                <span className="text-sm text-gray-500">{item.timestamp}</span>
              </div>
              <div className="mb-2">
                <span className={`font-semibold ${item.result.success ? 'text-green-700' : 'text-red-700'}`}>
                  {item.result.message}
                </span>
              </div>
              {item.result.nextStep && (
                <div className="mb-2 p-2 bg-blue-100 border border-blue-300 rounded text-sm">
                  <strong>Next Step:</strong> {item.result.nextStep}
                </div>
              )}
              <details className="text-sm">
                <summary className="cursor-pointer">View Details</summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto text-xs">
                  {JSON.stringify(item.result, null, 2)}
                </pre>
              </details>
            </div>
          ))
        )}
      </div>

      <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 className="font-semibold mb-2">🎯 After Successful Testing:</h3>
        <ol className="text-sm space-y-1 list-decimal list-inside">
          <li>Go to <strong>/register</strong> to use the actual registration form</li>
          <li>Go to <strong>/login</strong> to login with your verified account</li>
          <li>Access protected routes based on your user role</li>
        </ol>
      </div>
    </div>
  );
};

export default EmailAuthTest;
