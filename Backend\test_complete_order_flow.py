import requests
import json

def test_complete_order_flow():
    """Test the complete order flow from customer perspective"""
    
    print("=== TESTING COMPLETE ORDER FLOW ===\n")
    
    # 1. Customer Login
    print("1. Testing Customer Login...")
    customer_login = {'user_name': 'customer1', 'password': 'customer123'}
    login_response = requests.post('http://127.0.0.1:8000/api/auth/login/', json=customer_login)
    
    if login_response.status_code != 200:
        print(f"❌ Customer login failed: {login_response.status_code}")
        return False
    
    customer_data = login_response.json()
    customer_token = customer_data['data']['access_token']
    customer_headers = {'Authorization': f'Bearer {customer_token}'}
    customer_id = customer_data['data']['user']['id']
    
    print(f"✅ Customer login successful: {customer_data['data']['user']['name']} (ID: {customer_id})")
    
    # 2. Get Customer Orders
    print("\n2. Testing Get Customer Orders...")
    orders_response = requests.get('http://127.0.0.1:8000/api/order/orders/', headers=customer_headers)
    
    if orders_response.status_code != 200:
        print(f"❌ Failed to get orders: {orders_response.status_code}")
        return False
    
    orders = orders_response.json()
    print(f"✅ Customer can access {len(orders)} orders")
    
    # Show recent orders
    if orders:
        print("Recent orders:")
        for order in orders[:3]:
            print(f"  - Order #{order['id']}: {order['status']} - ${order['total_amount']}")
    
    # 3. Test Cart API
    print("\n3. Testing Cart API...")
    
    # Get current cart
    cart_response = requests.get('http://127.0.0.1:8000/api/order/carts/mine/', headers=customer_headers)
    print(f"Get cart status: {cart_response.status_code}")
    
    # Test cart save/update
    cart_data = {
        "restaurant_id": 2,  # Use existing restaurant ID
        "items": [
            {"menu_item_id": 5, "quantity": 2},  # chicken biryani
            {"menu_item_id": 6, "quantity": 1}   # Qurma
        ]
    }
    
    save_cart_response = requests.put('http://127.0.0.1:8000/api/order/carts/mine/', 
                                     json=cart_data, headers=customer_headers)
    print(f"Save cart status: {save_cart_response.status_code}")
    
    if save_cart_response.status_code in [200, 201]:
        print("✅ Cart save/update working")
    else:
        print(f"❌ Cart save failed: {save_cart_response.text}")
    
    # 4. Test Order Creation
    print("\n4. Testing Order Creation...")
    
    # Create a test order
    order_data = {
        "delivery_address": 24,  # Customer's address ID
        "restaurant": 2,  # Use existing restaurant ID
        "payment_method": "cash_on_delivery",
        "special_instructions": "Test order from API",
        "items": [
            {"menu_item_id": 5, "quantity": 2},  # chicken biryani
            {"menu_item_id": 6, "quantity": 1}   # Qurma
        ]
    }
    
    create_order_response = requests.post('http://127.0.0.1:8000/api/order/orders/', 
                                         json=order_data, headers=customer_headers)
    
    if create_order_response.status_code in [200, 201]:
        new_order = create_order_response.json()
        order_id = new_order['id']
        print(f"✅ Order created successfully: Order #{order_id}")
        
        # 5. Test Order Status Update (Restaurant perspective)
        print("\n5. Testing Order Status Updates...")
        
        # Login as restaurant
        restaurant_login = {'user_name': 'Samim', 'password': 'restaurant123'}
        restaurant_response = requests.post('http://127.0.0.1:8000/api/auth/login/', json=restaurant_login)
        
        if restaurant_response.status_code == 200:
            restaurant_data = restaurant_response.json()
            restaurant_token = restaurant_data['data']['access_token']
            restaurant_headers = {'Authorization': f'Bearer {restaurant_token}'}
            
            # Update order status
            status_update = {"status": "confirmed"}
            update_response = requests.patch(f'http://127.0.0.1:8000/api/order/orders/{order_id}/', 
                                           json=status_update, headers=restaurant_headers)
            
            if update_response.status_code == 200:
                print(f"✅ Order status updated to 'confirmed'")
                
                # Update to preparing
                status_update = {"status": "preparing"}
                update_response = requests.patch(f'http://127.0.0.1:8000/api/order/orders/{order_id}/', 
                                               json=status_update, headers=restaurant_headers)
                
                if update_response.status_code == 200:
                    print(f"✅ Order status updated to 'preparing'")
                else:
                    print(f"❌ Failed to update to preparing: {update_response.status_code}")
            else:
                print(f"❌ Failed to update order status: {update_response.status_code}")
        else:
            print(f"❌ Restaurant login failed: {restaurant_response.status_code}")
        
        # 6. Test Order Retrieval
        print("\n6. Testing Order Retrieval...")
        
        # Get specific order
        get_order_response = requests.get(f'http://127.0.0.1:8000/api/order/orders/{order_id}/', 
                                         headers=customer_headers)
        
        if get_order_response.status_code == 200:
            order_details = get_order_response.json()
            print(f"✅ Order retrieval successful: Status = {order_details['status']}")
        else:
            print(f"❌ Failed to get order details: {get_order_response.status_code}")
        
    else:
        print(f"❌ Order creation failed: {create_order_response.status_code}")
        print(f"Error: {create_order_response.text}")
        return False
    
    print("\n=== ORDER FLOW TEST COMPLETED ===")
    return True

if __name__ == "__main__":
    test_complete_order_flow()
