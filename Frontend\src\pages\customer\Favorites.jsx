import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { Heart, Star, Clock, MapPin, X, Search } from "lucide-react";
import { useFavorites } from "../../context/FavoritesContext";
import { useAuth } from "../../context/AuthContext";
import { useToast } from "../../context/ToastContext";
// Simple cuisine categories for filtering favorites
const cuisineCategories = [
  "All",
  "Afghan",
  "Traditional",
  "Middle Eastern",
  "Mediterranean",
  "Asian",
  "International",
];
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import FavoriteButton from "../../components/common/FavoriteButton";
import ConfirmDialog from "../../components/common/ConfirmDialog";

// Simple error boundary for this page
class FavoritesErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }
  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }
  componentDidCatch(error, errorInfo) {
    this.setState({ errorInfo });
    // Optionally log error to a service
    // console.error("Favorites page error:", error, errorInfo);
  }
  render() {
    if (this.state.hasError) {
      return (
        <div className='container mx-auto px-4 py-16 text-center'>
          <div className='w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6'>
            <Heart size={40} className='text-gray-400' />
          </div>
          <h3 className='text-xl font-medium mb-2'>Something went wrong</h3>
          <p className='text-text-secondary mb-6 max-w-md mx-auto'>
            An error occurred while loading your favorites.
            <br />
            <span style={{ color: "red" }}>
              {this.state.error && this.state.error.toString()}
            </span>
            <br />
            <details
              style={{ whiteSpace: "pre-wrap", color: "#555", marginTop: 10 }}
            >
              {this.state.errorInfo && this.state.errorInfo.componentStack}
            </details>
          </p>
          <button
            className='btn btn-primary'
            onClick={() => window.location.reload()}
          >
            Reload Page
          </button>
        </div>
      );
    }
    return this.props.children;
  }
}

const Favorites = () => {
  const { user } = useAuth();
  const { success, error } = useToast();
  const {
    favorites,
    removeFromFavorites,
    clearFavorites,
    getFavoritesByCategory,
    loading,
  } = useFavorites();
  const [activeCategory, setActiveCategory] = useState("All");
  const [searchQuery, setSearchQuery] = useState("");
  const [showClearDialog, setShowClearDialog] = useState(false);
  const [clearLoading, setClearLoading] = useState(false);

  // Handle clear all favorites with confirmation
  const handleClearFavorites = async () => {
    setClearLoading(true);
    try {
      const result = await clearFavorites();
      if (result.success) {
        success("Favorites Cleared", result.message);
      } else {
        error("Error", result.error);
      }
    } catch (err) {
      error("Error", "Failed to clear favorites");
    } finally {
      setClearLoading(false);
    }
  };

  // If not logged in, show a message
  if (!user) {
    return (
      <div className='container mx-auto px-4 py-16 text-center'>
        <div className='w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6'>
          <Heart size={40} className='text-gray-400' />
        </div>
        <h3 className='text-xl font-medium mb-2'>Please Log In</h3>
        <p className='text-text-secondary mb-6 max-w-md mx-auto'>
          You need to be logged in to view your favorite restaurants.
        </p>
        <Button variant='primary' to='/login'>
          Go to Login
        </Button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className='container mx-auto px-4 py-16 text-center'>
        <div className='w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6 animate-spin'>
          <Heart size={40} className='text-gray-400' />
        </div>
        <h3 className='text-xl font-medium mb-2'>Loading your favorites...</h3>
      </div>
    );
  }

  // Filter favorites based on search and category
  const filteredFavorites = favorites.filter((restaurant) => {
    if (!restaurant) return false;

    // Filter by category
    if (activeCategory !== "All") {
      const cuisineTypes = restaurant.cuisine_types || restaurant.cuisine || [];
      const matchesCategory = cuisineTypes.some((c) => {
        const cuisineName = typeof c === "string" ? c : c.name || c;
        return cuisineName.toLowerCase().includes(activeCategory.toLowerCase());
      });
      if (!matchesCategory) return false;
    }

    // Filter by search query
    if (searchQuery) {
      const name = restaurant.name || "";
      const cuisineTypes = restaurant.cuisine_types || restaurant.cuisine || [];
      const cuisineNames = cuisineTypes
        .map((c) => (typeof c === "string" ? c : c.name || c))
        .join(" ");

      const matchesSearch =
        name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cuisineNames.toLowerCase().includes(searchQuery.toLowerCase());

      if (!matchesSearch) return false;
    }

    return true;
  });

  const handleRemoveFavorite = async (restaurantId, event) => {
    event.preventDefault();
    event.stopPropagation();

    const result = await removeFromFavorites(restaurantId);
    if (result.success) {
      success("Removed from Favorites", result.message);
    } else {
      error("Error", result.error);
    }
  };

  return (
    <div className='container mx-auto px-4 py-8 animate-fade-in'>
      <div className='flex items-center justify-between mb-8'>
        <div>
          <h1 className='text-2xl font-poppins font-semibold'>
            Your Favorites
          </h1>
          <p className='text-text-secondary mt-1'>
            {favorites.length} restaurant
            {favorites.length !== 1 ? "s" : ""} saved
          </p>
        </div>

        {favorites.length > 0 && (
          <Button
            variant='outline'
            size='small'
            onClick={() => setShowClearDialog(true)}
            className='text-accent-red border-accent-red hover:bg-accent-red hover:text-white'
          >
            Clear All
          </Button>
        )}
      </div>

      {favorites.length === 0 ? (
        <div className='text-center py-16'>
          <div className='w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6'>
            <Heart size={40} className='text-gray-400' />
          </div>
          <h3 className='text-xl font-medium mb-2'>No Favorites Yet</h3>
          <p className='text-text-secondary mb-6 max-w-md mx-auto'>
            Start exploring restaurants and add your favorites by clicking the
            heart icon.
          </p>
          <Button variant='primary' to='/restaurants'>
            Browse Restaurants
          </Button>
        </div>
      ) : (
        <>
          {/* Search & Filter */}
          <div className='mb-6'>
            <div className='flex flex-col md:flex-row gap-4 items-center'>
              <div className='relative w-full'>
                <Search
                  className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
                  size={20}
                />
                <input
                  type='text'
                  placeholder='Search your favorites...'
                  className='w-full pl-10 pr-4 py-3 rounded-md border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500'
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {/* Cuisine Categories */}
            <div className='mt-6'>
              <div className='flex overflow-x-auto py-2 hide-scrollbar'>
                <div className='flex space-x-3'>
                  {cuisineCategories.map((category) => (
                    <button
                      key={category}
                      className={`px-4 py-2 rounded-full whitespace-nowrap transition-colors ${
                        activeCategory === category
                          ? "bg-primary-500 text-white"
                          : "bg-white border border-gray-200 text-text-primary hover:bg-gray-50"
                      }`}
                      onClick={() => setActiveCategory(category)}
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Favorites Grid */}
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {filteredFavorites.length === 0 ? (
              <div className='col-span-3 text-center py-12'>
                <div className='text-6xl mb-4'>🔍</div>
                <h3 className='text-xl font-medium mb-2'>No Results Found</h3>
                <p className='text-text-secondary'>
                  No favorites match your search criteria.
                </p>
              </div>
            ) : (
              filteredFavorites.map((restaurant) => {
                // Fallbacks for missing data
                const logo = restaurant.logo || "/placeholder-restaurant.jpg";
                const coverImage =
                  restaurant.coverImage || "/placeholder-restaurant.jpg";
                const name = restaurant.name || "Unnamed Restaurant";
                const cuisine =
                  Array.isArray(restaurant.cuisine) &&
                  restaurant.cuisine.length > 0
                    ? restaurant.cuisine.join(", ")
                    : "No cuisine info";
                const rating =
                  typeof restaurant.rating === "number"
                    ? restaurant.rating
                    : "N/A";
                const deliveryTime = restaurant.deliveryTime || "N/A";
                const deliveryFee =
                  typeof restaurant.deliveryFee === "number"
                    ? `$${restaurant.deliveryFee.toFixed(2)}`
                    : "N/A";
                const isOpen =
                  typeof restaurant.isOpen === "boolean"
                    ? restaurant.isOpen
                    : false;
                const distance = restaurant.distance
                  ? `${restaurant.distance} km`
                  : "- km";
                return (
                  <Link
                    to={`/restaurants/${restaurant.id}`}
                    key={restaurant.id}
                    title={`View details for ${name}`}
                  >
                    <Card
                      className='h-full transition-transform duration-200 hover:-translate-y-1 relative group shadow-md border border-gray-100 hover:shadow-lg'
                      hoverable
                    >
                      {/* Favorite button */}
                      <div className='absolute top-4 right-4 z-10'>
                        <FavoriteButton
                          restaurant={restaurant}
                          size='default'
                          variant='default'
                        />
                      </div>

                      <div className='relative h-48 rounded-t-lg overflow-hidden -mx-5 -mt-5 mb-4 bg-gray-50'>
                        <div className='absolute top-4 left-4 z-10'>
                          {isOpen ? (
                            <Badge variant='success' size='small'>
                              Open Now
                            </Badge>
                          ) : (
                            <Badge variant='danger' size='small'>
                              Closed
                            </Badge>
                          )}
                        </div>
                        <img
                          src={coverImage}
                          alt={name + " cover"}
                          className='w-full h-full object-cover'
                          onError={(e) => {
                            e.target.onerror = null;
                            e.target.src = "/placeholder-restaurant.jpg";
                          }}
                        />
                      </div>

                      <div className='flex items-start'>
                        <div className='w-16 h-16 rounded-lg overflow-hidden bg-gray-100 mr-4 flex-shrink-0'>
                          <img
                            src={logo}
                            alt={name + " logo"}
                            className='w-full h-full object-cover'
                            onError={(e) => {
                              e.target.onerror = null;
                              e.target.src = "/placeholder-restaurant.jpg";
                            }}
                          />
                        </div>

                        <div className='flex-1'>
                          <h3
                            className='font-semibold text-lg truncate'
                            title={name}
                          >
                            {name}
                          </h3>
                          <div className='flex items-center mt-1 text-text-secondary text-sm'>
                            <Star
                              size={16}
                              className='text-yellow-500 mr-1'
                              title='Rating'
                            />
                            <span>{rating}</span>
                            <span className='mx-2'>•</span>
                            <span>{cuisine}</span>
                          </div>
                        </div>
                      </div>

                      <div className='mt-4 flex items-center justify-between text-sm text-text-secondary'>
                        <div
                          className='flex items-center'
                          title='Estimated delivery time'
                        >
                          <Clock size={16} className='mr-1' />
                          <span>{deliveryTime}</span>
                        </div>
                        <div className='flex items-center' title='Distance'>
                          <MapPin size={16} className='mr-1' />
                          <span>{distance}</span>
                        </div>
                        <div title='Delivery fee'>{deliveryFee} delivery</div>
                      </div>

                      {/* Favorite indicator */}
                      <div className='absolute top-4 left-4 z-10'>
                        <div
                          className='bg-accent-red text-white rounded-full p-1.5'
                          title='Favorite'
                        >
                          <Heart size={14} className='fill-current' />
                        </div>
                      </div>
                    </Card>
                  </Link>
                );
              })
            )}
          </div>
        </>
      )}

      {/* Clear Favorites Confirmation Dialog */}
      <ConfirmDialog
        isOpen={showClearDialog}
        onClose={() => setShowClearDialog(false)}
        onConfirm={handleClearFavorites}
        title='Clear All Favorites'
        message={`Are you sure you want to remove all ${favorites.length} favorite restaurants? This action cannot be undone.`}
        confirmText='Clear All'
        cancelText='Cancel'
        variant='danger'
        loading={clearLoading}
      />
    </div>
  );
};

export default function FavoritesWithBoundary(props) {
  return (
    <FavoritesErrorBoundary>
      <Favorites {...props} />
    </FavoritesErrorBoundary>
  );
}
