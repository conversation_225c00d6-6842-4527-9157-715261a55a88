from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User, OTPVerification

class CustomUserAdmin(UserAdmin):
    # The fields to be used in displaying the User model
    list_display = ('id','user_name','name', 'is_active', 'is_staff', 'is_verified', 'date_joined','last_login')
    list_filter = ('role', 'is_staff', 'is_active', 'is_verified')
    fieldsets = (
        (None, {'fields': ( 'password','is_verified')}),
        ('Personal info', {'fields': ('user_name','name', 'email','phone', 'role')}),
        ('Permissions', {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('phone', 'name', 'email', 'role', 'password1', 'password2'),
        }),
    )
    search_fields = ('phone', 'name', 'email')
    ordering = ('phone',)
    filter_horizontal = ('groups', 'user_permissions',)

# Register your models here
admin.site.register(User, CustomUserAdmin)
admin.site.register(OTPVerification)