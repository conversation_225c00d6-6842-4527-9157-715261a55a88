import sqlite3
import os

def check_orders():
    """Check orders in the database directly using SQLite"""
    
    # Find the database file
    db_path = os.path.join('Backend', 'db.sqlite3')
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return
    
    print(f"✅ Database found at {db_path}")
    
    # Connect to the database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Check if order #65 exists
    print("\n🔍 Checking Order #65")
    print("=" * 50)
    
    cursor.execute("""
        SELECT o.id, o.status, o.total_amount, o.created_at, 
               c.name as customer_name, r.name as restaurant_name,
               u.email as agent_email
        FROM orders_order o
        LEFT JOIN users_user c ON o.customer_id = c.id
        LEFT JOIN restaurant_restaurant r ON o.restaurant_id = r.id
        LEFT JOIN users_user u ON o.delivery_agent_id = u.id
        WHERE o.id = 65
    """)
    
    order = cursor.fetchone()
    
    if order:
        id, status, total_amount, created_at, customer_name, restaurant_name, agent_email = order
        print(f"✅ Order #65 EXISTS!")
        print(f"   Customer: {customer_name}")
        print(f"   Restaurant: {restaurant_name}")
        print(f"   Status: {status}")
        print(f"   Total Amount: ${total_amount}")
        print(f"   Delivery Agent: {agent_email or 'None'}")
        print(f"   Created: {created_at}")
        
        # Check why it might not show in admin assignment
        print(f"\n🔍 Assignment Eligibility Check:")
        print(f"   Status is 'ready': {status == 'ready'}")
        print(f"   No delivery agent assigned: {agent_email is None}")
        print(f"   Should show in admin: {status == 'ready' and agent_email is None}")
    else:
        print("❌ Order #65 does NOT exist in the database")
        
        # Show the highest order ID
        cursor.execute("SELECT MAX(id) FROM orders_order")
        max_id = cursor.fetchone()[0]
        print(f"   Highest order ID: #{max_id}")
    
    # Show orders from khan sab
    print(f"\n📦 Orders from 'khan sab':")
    cursor.execute("""
        SELECT o.id, o.status, o.total_amount, o.created_at, r.name as restaurant_name
        FROM orders_order o
        JOIN users_user c ON o.customer_id = c.id
        JOIN restaurant_restaurant r ON o.restaurant_id = r.id
        WHERE c.name LIKE '%khan%'
        ORDER BY o.created_at DESC
        LIMIT 5
    """)
    
    khan_orders = cursor.fetchall()
    for order in khan_orders:
        id, status, total_amount, created_at, restaurant_name = order
        print(f"   Order #{id}: ${total_amount} - {status} - {created_at[:16]} - {restaurant_name}")
    
    # Show all ready orders for comparison
    print(f"\n📋 All Ready Orders (unassigned):")
    cursor.execute("""
        SELECT o.id, o.status, o.total_amount, o.created_at, 
               c.name as customer_name, r.name as restaurant_name
        FROM orders_order o
        JOIN users_user c ON o.customer_id = c.id
        JOIN restaurant_restaurant r ON o.restaurant_id = r.id
        WHERE o.status = 'ready' AND o.delivery_agent_id IS NULL
        ORDER BY o.created_at DESC
        LIMIT 10
    """)
    
    ready_orders = cursor.fetchall()
    for order in ready_orders:
        id, status, total_amount, created_at, customer_name, restaurant_name = order
        print(f"   Order #{id}: {customer_name} - ${total_amount} - {created_at[:16]} - {restaurant_name}")
    
    conn.close()

if __name__ == "__main__":
    check_orders()
