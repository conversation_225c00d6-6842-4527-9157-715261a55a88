import React from 'react';
import { cn } from '../../utils/cn';

const Card = ({ 
  children,
  className,
  hoverable = false,
  bordered = false,
  shadow = 'medium',
  padding = 'medium',
  ...props
}) => {
  const shadowClasses = {
    none: '',
    small: 'shadow-sm',
    medium: 'shadow-card',
    large: 'shadow-lg'
  };

  const paddingClasses = {
    none: 'p-0',
    small: 'p-3',
    medium: 'p-5',
    large: 'p-8'
  };

  const classes = cn(
    'bg-white rounded-lg',
    shadowClasses[shadow],
    paddingClasses[padding],
    bordered && 'border border-gray-200',
    hoverable && 'transition-all duration-200 hover:shadow-lg',
    className
  );

  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
};

export default Card;