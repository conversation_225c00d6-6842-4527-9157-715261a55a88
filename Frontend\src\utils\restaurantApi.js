import axios from "axios";

// Base URL for the API
const BASE_URL =
  (typeof process !== "undefined" && process.env?.REACT_APP_API_BASE_URL) ||
  import.meta.env?.VITE_API_BASE_URL ||
  "http://127.0.0.1:8000/api";

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 30000, // 30 seconds timeout
});

// Request interceptor to add authentication token
apiClient.interceptors.request.use(
  (config) => {
    // Get token from localStorage or your auth context
    const user = JSON.parse(localStorage.getItem("afghanSofraUser") || "{}");
    const token = user.access_token || user.token;

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle authentication errors
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Clear invalid token and redirect to login
      localStorage.removeItem("afghanSofraUser");
      if (window.location.pathname !== "/login") {
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

/**
 * Restaurant API Service
 */
export const restaurantApi = {
  /**
   * Create a new restaurant
   * @param {Object} restaurantData - Restaurant data including files
   * @returns {Promise} API response
   */
  async createRestaurant(restaurantData) {
    try {
      const formData = new FormData();

      // Add text fields
      formData.append("name", restaurantData.name);
      formData.append("description", restaurantData.description);
      formData.append("contact_number", restaurantData.contact_number);

      // Format time fields to include seconds (HH:MM:SS)
      const formatTime = (time) => {
        if (!time) return null;
        return time.includes(":") && time.split(":").length === 2
          ? `${time}:00`
          : time;
      };

      formData.append("opening_time", formatTime(restaurantData.opening_time));
      formData.append("closing_time", formatTime(restaurantData.closing_time));

      // Add delivery fee if provided
      if (restaurantData.delivery_fee !== undefined) {
        formData.append("delivery_fee", restaurantData.delivery_fee);
      }

      // Add minimum order amount
      if (restaurantData.minimum_order !== undefined) {
        formData.append("min_order_amount", restaurantData.minimum_order);
      }

      // Add cuisine type
      if (restaurantData.cuisine_type) {
        // Handle cuisine type as a string or array
        if (Array.isArray(restaurantData.cuisine_type)) {
          // If it's an array, append each ID
          restaurantData.cuisine_type.forEach((id) => {
            formData.append("cuisine_type_ids", id);
          });
        } else {
          // If it's a single value, append it directly
          formData.append("cuisine_type_ids", restaurantData.cuisine_type);
        }
      }

      // Add average preparation time
      if (restaurantData.average_preparation_time !== undefined) {
        formData.append(
          "average_preparation_time",
          restaurantData.average_preparation_time
        );
      }

      // Add payment method preferences
      if (restaurantData.accepts_cash !== undefined) {
        formData.append("accepts_cash", restaurantData.accepts_cash);
      }
      if (restaurantData.accepts_card !== undefined) {
        formData.append("accepts_card", restaurantData.accepts_card);
      }
      if (restaurantData.accepts_online_payment !== undefined) {
        formData.append(
          "accepts_online_payment",
          restaurantData.accepts_online_payment
        );
      }

      // Add social media URLs
      if (restaurantData.website) {
        formData.append("website", restaurantData.website);
      }
      if (restaurantData.facebook_url) {
        formData.append("facebook_url", restaurantData.facebook_url);
      }
      if (restaurantData.instagram_url) {
        formData.append("instagram_url", restaurantData.instagram_url);
      }
      if (restaurantData.twitter_url) {
        formData.append("twitter_url", restaurantData.twitter_url);
      }

      // Add address as JSON string with proper field mapping
      if (restaurantData.address) {
        // Add a unique identifier to the street to avoid unique constraint violations
        const timestamp = new Date().getTime();
        const addressData = {
          street: `${restaurantData.address} #${timestamp}`,
          city: "Kabul", // Default city
          state: "Kabul", // Default state
          postal_code: `${timestamp % 10000}`, // Generate a unique postal code
          country: "Afghanistan",
          latitude: 0,
          longitude: 0,
        };
        formData.append("address", JSON.stringify(addressData));
      }

      // Add files
      if (restaurantData.logo) {
        formData.append("logo", restaurantData.logo);
      }
      if (restaurantData.banner) {
        formData.append("banner", restaurantData.banner);
      }

      const response = await apiClient.post(
        "/restaurant/restaurants/",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      return {
        success: true,
        data: response.data,
        message: "Restaurant created successfully",
      };
    } catch (error) {
      console.error("Create restaurant error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to create restaurant",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get restaurant details by ID
   * @param {string} restaurantId - Restaurant ID
   * @returns {Promise} API response
   */
  async getRestaurant(restaurantId) {
    try {
      const response = await apiClient.get(
        `/restaurant/restaurants/${restaurantId}/`
      );

      return {
        success: true,
        data: response.data,
        message: "Restaurant fetched successfully",
      };
    } catch (error) {
      console.error("Get restaurant error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch restaurant",
        details: error.response?.data,
      };
    }
  },

  /**
   * Update restaurant (full update)
   * @param {string} restaurantId - Restaurant ID
   * @param {Object} restaurantData - Complete restaurant data
   * @returns {Promise} API response
   */
  async updateRestaurant(restaurantId, restaurantData) {
    try {
      const response = await apiClient.put(
        `/restaurant/restaurants/${restaurantId}/`,
        restaurantData,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      return {
        success: true,
        data: response.data,
        message: "Restaurant updated successfully",
      };
    } catch (error) {
      console.error("Update restaurant error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to update restaurant",
        details: error.response?.data,
      };
    }
  },

  /**
   * Partially update restaurant
   * @param {string} restaurantId - Restaurant ID
   * @param {Object} partialData - Partial restaurant data to update
   * @returns {Promise} API response
   */
  async patchRestaurant(restaurantId, partialData) {
    try {
      const response = await apiClient.patch(
        `/restaurant/restaurants/${restaurantId}/`,
        partialData,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      return {
        success: true,
        data: response.data,
        message: "Restaurant updated successfully",
      };
    } catch (error) {
      console.error("Patch restaurant error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to update restaurant",
        details: error.response?.data,
      };
    }
  },

  /**
   * Delete restaurant
   * @param {string} restaurantId - Restaurant ID
   * @returns {Promise} API response
   */
  async deleteRestaurant(restaurantId) {
    try {
      await apiClient.delete(`/restaurant/restaurants/${restaurantId}/`);

      return {
        success: true,
        message: "Restaurant deleted successfully",
      };
    } catch (error) {
      console.error("Delete restaurant error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to delete restaurant",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get all restaurants (if endpoint exists)
   * @param {Object} params - Query parameters
   * @returns {Promise} API response
   */
  async getRestaurants(params = {}) {
    try {
      const response = await apiClient.get("/restaurant/restaurants/", {
        params,
      });

      return {
        success: true,
        data: response.data,
        message: "Restaurants fetched successfully",
      };
    } catch (error) {
      console.error("Get restaurants error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch restaurants",
        details: error.response?.data,
      };
    }
  },

  /**
   * Get restaurants owned by the current user
   * @returns {Promise} API response
   */
  async getUserRestaurants() {
    try {
      const response = await apiClient.get(
        "/restaurant/restaurants/my_restaurants/"
      );

      return {
        success: true,
        data: response.data,
        message: "User restaurants fetched successfully",
      };
    } catch (error) {
      console.error("Get user restaurants error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch your restaurants",
      };
    }
  },

  /**
   * Get all restaurants for admin management
   * @param {Object} params - Query parameters (status, search)
   * @returns {Promise} API response
   */
  async getAdminRestaurants(params = {}) {
    try {
      const response = await apiClient.get(
        "/restaurant/restaurants/admin_restaurants/",
        {
          params,
        }
      );

      return {
        success: true,
        data: response.data,
        message: "Admin restaurants fetched successfully",
      };
    } catch (error) {
      console.error("Get admin restaurants error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to fetch restaurants for admin",
      };
    }
  },

  /**
   * Admin verify/reject restaurant
   * @param {number} restaurantId - Restaurant ID
   * @param {string} action - 'approve' or 'reject'
   * @param {string} notes - Optional notes
   * @returns {Promise} API response
   */
  async adminVerifyRestaurant(restaurantId, action, notes = "") {
    try {
      const response = await apiClient.patch(
        `/restaurant/restaurants/${restaurantId}/admin_verify/`,
        {
          action,
          notes,
        }
      );

      return {
        success: true,
        data: response.data,
        message:
          response.data.message || "Restaurant status updated successfully",
      };
    } catch (error) {
      console.error("Admin verify restaurant error:", error);
      return {
        success: false,
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to update restaurant status",
      };
    }
  },
};

export default restaurantApi;
