import React, { useState } from "react";
import { Download, X, Smartphone } from "lucide-react";
import { usePWA } from "../../hooks/usePWA";
import Button from "../common/Button";
import { useSiteName } from "../../hooks/useConfig";

const InstallBanner = () => {
  const { isInstallable, isInstalled, installApp } = usePWA();
  const [isDismissed, setIsDismissed] = useState(
    localStorage.getItem("installBannerDismissed") === "true"
  );
  const { value: siteName } = useSiteName();

  const handleInstall = async () => {
    const success = await installApp();
    if (success) {
      setIsDismissed(true);
      localStorage.setItem("installBannerDismissed", "true");
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    localStorage.setItem("installBannerDismissed", "true");
  };

  // Don't show if not installable, already installed, or dismissed
  if (!isInstallable || isInstalled || isDismissed) {
    return null;
  }

  return (
    <div className='fixed bottom-0 left-0 right-0 z-50 bg-gradient-to-r from-primary-500 to-primary-600 text-white p-4 shadow-lg animate-slide-up'>
      <div className='container mx-auto flex items-center justify-between'>
        <div className='flex items-center'>
          <div className='w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-4'>
            <Smartphone size={24} />
          </div>
          <div>
            <h3 className='font-semibold text-lg'>
              Install {siteName || "Afghan Sufra"}
            </h3>
            <p className='text-sm opacity-90'>
              Get the app for faster ordering and offline access
            </p>
          </div>
        </div>

        <div className='flex items-center space-x-2'>
          <Button
            variant='secondary'
            size='small'
            onClick={handleInstall}
            icon={<Download size={16} />}
            className='bg-white text-primary-600 hover:bg-gray-100'
          >
            Install
          </Button>
          <button
            onClick={handleDismiss}
            className='p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors'
            aria-label='Dismiss install banner'
          >
            <X size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default InstallBanner;
