import React, { useState, useRef, useEffect } from 'react';
import { Search, X, Clock, TrendingUp, MapPin } from 'lucide-react';
import { cn } from '../../utils/cn';
import TouchFeedback from './TouchFeedback';

const MobileSearch = ({
  placeholder = "Search restaurants, dishes...",
  onSearch,
  onFocus,
  onBlur,
  className = "",
  showRecentSearches = true,
  showTrendingSearches = true,
  autoFocus = false
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [query, setQuery] = useState('');
  const [recentSearches, setRecentSearches] = useState([
    'Afghan Kabab House',
    'Biryani',
    'Pizza',
    'Burger'
  ]);
  const [trendingSearches] = useState([
    'Kabuli Pulao',
    'Mantu',
    'Qorma',
    'Ashak',
    'Bolani'
  ]);
  
  const inputRef = useRef(null);
  const overlayRef = useRef(null);

  useEffect(() => {
    if (isExpanded && autoFocus) {
      inputRef.current?.focus();
    }
  }, [isExpanded, autoFocus]);

  const handleExpand = () => {
    setIsExpanded(true);
    onFocus?.();
  };

  const handleCollapse = () => {
    setIsExpanded(false);
    setQuery('');
    onBlur?.();
  };

  const handleSearch = (searchQuery) => {
    if (searchQuery.trim()) {
      // Add to recent searches
      setRecentSearches(prev => {
        const filtered = prev.filter(item => item !== searchQuery);
        return [searchQuery, ...filtered].slice(0, 5);
      });
      
      onSearch?.(searchQuery);
      setQuery(searchQuery);
      setIsExpanded(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    handleSearch(query);
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
  };

  if (!isExpanded) {
    // Collapsed search bar
    return (
      <TouchFeedback
        feedbackType="highlight"
        onClick={handleExpand}
        className={cn(
          'flex items-center space-x-3 p-3 bg-gray-100 rounded-lg cursor-pointer',
          className
        )}
      >
        <Search size={20} className="text-gray-400" />
        <span className="text-gray-500 flex-1">{placeholder}</span>
      </TouchFeedback>
    );
  }

  // Expanded search overlay
  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40"
        onClick={handleCollapse}
      />
      
      {/* Search overlay */}
      <div 
        ref={overlayRef}
        className="fixed inset-0 z-50 bg-white flex flex-col"
      >
        {/* Search header */}
        <div className="flex items-center p-4 border-b border-gray-200">
          <form onSubmit={handleSubmit} className="flex-1 flex items-center">
            <div className="relative flex-1">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder={placeholder}
                className="w-full pl-10 pr-4 py-3 bg-gray-100 rounded-lg border-0 focus:outline-none focus:ring-2 focus:ring-primary-500 text-base"
                autoFocus
              />
              {query && (
                <button
                  type="button"
                  onClick={() => setQuery('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X size={20} />
                </button>
              )}
            </div>
          </form>
          
          <button
            onClick={handleCollapse}
            className="ml-4 text-gray-600 hover:text-gray-800 font-medium"
          >
            Cancel
          </button>
        </div>

        {/* Search content */}
        <div className="flex-1 overflow-y-auto">
          {/* Recent searches */}
          {showRecentSearches && recentSearches.length > 0 && (
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-semibold text-gray-900 flex items-center">
                  <Clock size={16} className="mr-2" />
                  Recent Searches
                </h3>
                <button
                  onClick={clearRecentSearches}
                  className="text-sm text-primary-600 hover:text-primary-700"
                >
                  Clear
                </button>
              </div>
              
              <div className="space-y-2">
                {recentSearches.map((search, index) => (
                  <TouchFeedback
                    key={index}
                    feedbackType="highlight"
                    onClick={() => handleSearch(search)}
                    className="flex items-center p-3 rounded-lg cursor-pointer"
                  >
                    <Clock size={16} className="text-gray-400 mr-3" />
                    <span className="flex-1">{search}</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setRecentSearches(prev => prev.filter((_, i) => i !== index));
                      }}
                      className="text-gray-400 hover:text-gray-600 ml-2"
                    >
                      <X size={16} />
                    </button>
                  </TouchFeedback>
                ))}
              </div>
            </div>
          )}

          {/* Trending searches */}
          {showTrendingSearches && (
            <div className="p-4 border-t border-gray-100">
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                <TrendingUp size={16} className="mr-2" />
                Trending Searches
              </h3>
              
              <div className="grid grid-cols-2 gap-2">
                {trendingSearches.map((search, index) => (
                  <TouchFeedback
                    key={index}
                    feedbackType="scale"
                    onClick={() => handleSearch(search)}
                    className="p-3 bg-gray-50 rounded-lg text-center cursor-pointer"
                  >
                    <span className="text-sm font-medium">{search}</span>
                  </TouchFeedback>
                ))}
              </div>
            </div>
          )}

          {/* Quick filters */}
          <div className="p-4 border-t border-gray-100">
            <h3 className="font-semibold text-gray-900 mb-3">Quick Filters</h3>
            
            <div className="grid grid-cols-2 gap-3">
              <TouchFeedback
                feedbackType="scale"
                onClick={() => handleSearch('nearby restaurants')}
                className="flex items-center p-3 bg-blue-50 rounded-lg cursor-pointer"
              >
                <MapPin size={16} className="text-blue-600 mr-2" />
                <span className="text-sm font-medium text-blue-800">Nearby</span>
              </TouchFeedback>
              
              <TouchFeedback
                feedbackType="scale"
                onClick={() => handleSearch('fast delivery')}
                className="flex items-center p-3 bg-green-50 rounded-lg cursor-pointer"
              >
                <Clock size={16} className="text-green-600 mr-2" />
                <span className="text-sm font-medium text-green-800">Fast Delivery</span>
              </TouchFeedback>
              
              <TouchFeedback
                feedbackType="scale"
                onClick={() => handleSearch('top rated')}
                className="flex items-center p-3 bg-yellow-50 rounded-lg cursor-pointer"
              >
                <TrendingUp size={16} className="text-yellow-600 mr-2" />
                <span className="text-sm font-medium text-yellow-800">Top Rated</span>
              </TouchFeedback>
              
              <TouchFeedback
                feedbackType="scale"
                onClick={() => handleSearch('free delivery')}
                className="flex items-center p-3 bg-purple-50 rounded-lg cursor-pointer"
              >
                <span className="text-sm font-medium text-purple-800">Free Delivery</span>
              </TouchFeedback>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileSearch;
