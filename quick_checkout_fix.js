// Quick checkout fix - Add this to browser console to enable the button temporarily

// Method 1: Force enable the Place Order button
function forceEnablePlaceOrderButton() {
  const buttons = document.querySelectorAll('button');
  buttons.forEach(button => {
    if (button.textContent.includes('Place Order')) {
      button.disabled = false;
      button.style.opacity = '1';
      button.style.cursor = 'pointer';
      console.log('✅ Place Order button enabled!');
    }
  });
}

// Method 2: Add a test address to localStorage
function addTestAddressToCart() {
  const testAddress = {
    id: 46,
    backendId: 46,
    type: "saved",
    label: "Test Delivery Street, Kabul",
    address: "Test Delivery Street, Kabul, Kabul, Afghanistan",
    coordinates: [34.560000, 69.210000],
    isDefault: false,
    street: "Test Delivery Street",
    city: "Kabul",
    state: "Kabul",
    country: "Afghanistan",
    postal_code: "1002",
    source: "api"
  };
  
  // Get current cart
  let cart = JSON.parse(localStorage.getItem('afghanSofraCart') || '{}');
  
  // Add address to cart
  cart.addresses = [testAddress];
  cart.selectedAddress = testAddress;
  
  // Save back to localStorage
  localStorage.setItem('afghanSofraCart', JSON.stringify(cart));
  
  console.log('✅ Test address added to cart!');
  console.log('Cart:', cart);
  
  // Refresh the page
  window.location.reload();
}

// Method 3: Check current state
function debugCheckoutState() {
  console.log('=== CHECKOUT DEBUG ===');
  
  // Check user
  const user = JSON.parse(localStorage.getItem('afghanSofraUser') || '{}');
  console.log('User:', user);
  
  // Check cart
  const cart = JSON.parse(localStorage.getItem('afghanSofraCart') || '{}');
  console.log('Cart:', cart);
  
  // Check addresses
  const addresses = JSON.parse(localStorage.getItem('afghanSofraAddresses') || '[]');
  console.log('Addresses:', addresses);
  
  // Check if button exists
  const buttons = document.querySelectorAll('button');
  const placeOrderButton = Array.from(buttons).find(btn => btn.textContent.includes('Place Order'));
  console.log('Place Order Button:', placeOrderButton);
  console.log('Button disabled:', placeOrderButton?.disabled);
  
  return {
    user,
    cart,
    addresses,
    placeOrderButton
  };
}

// Run debug first
console.log('🔍 Running checkout debug...');
const state = debugCheckoutState();

// If no addresses in cart, add test address
if (!state.cart.addresses || state.cart.addresses.length === 0) {
  console.log('🔧 Adding test address...');
  addTestAddressToCart();
} else {
  console.log('🔧 Force enabling button...');
  forceEnablePlaceOrderButton();
}

console.log('📋 Instructions:');
console.log('1. Copy and paste this entire script into browser console (F12)');
console.log('2. Press Enter to run');
console.log('3. The Place Order button should be enabled');
console.log('4. If not, try refreshing the page and running again');
