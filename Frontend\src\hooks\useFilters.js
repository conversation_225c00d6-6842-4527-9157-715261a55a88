// Frontend/src/hooks/useFilters.js
import { useState, useEffect, useCallback } from 'react';
import filterService from '../services/filterService';

export const useFilters = () => {
  const [filterOptions, setFilterOptions] = useState({
    cuisines: [],
    dietaryRestrictions: [],
    priceRanges: [],
    deliveryTimes: [],
    ratings: [],
    distances: [],
    features: [],
    sortOptions: []
  });

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadFilterOptions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const options = await filterService.getAllFilterOptions();
      setFilterOptions(options);
    } catch (err) {
      console.error('Error loading filter options:', err);
      setError(err.message);
      // Set fallback options
      setFilterOptions(filterService.getFallbackFilterOptions());
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadFilterOptions();
  }, [loadFilterOptions]);

  const refreshFilters = useCallback(() => {
    filterService.clearCache();
    loadFilterOptions();
  }, [loadFilterOptions]);

  return {
    filterOptions,
    loading,
    error,
    refreshFilters,
    reload: loadFilterOptions
  };
};

export const useCuisineOptions = () => {
  const [cuisines, setCuisines] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadCuisines = async () => {
      try {
        setLoading(true);
        setError(null);
        const cuisineOptions = await filterService.getCuisineOptions();
        setCuisines(cuisineOptions);
      } catch (err) {
        console.error('Error loading cuisine options:', err);
        setError(err.message);
        setCuisines(filterService.getFallbackCuisines());
      } finally {
        setLoading(false);
      }
    };

    loadCuisines();
  }, []);

  return { cuisines, loading, error };
};

export const useDeliveryTimeOptions = () => {
  const [deliveryTimes, setDeliveryTimes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadDeliveryTimes = async () => {
      try {
        setLoading(true);
        setError(null);
        const timeOptions = await filterService.getDeliveryTimeOptions();
        setDeliveryTimes(timeOptions);
      } catch (err) {
        console.error('Error loading delivery time options:', err);
        setError(err.message);
        setDeliveryTimes([
          { value: 30, label: 'Under 30 min', icon: '⚡' },
          { value: 45, label: '30-45 min', icon: '🚚' }
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadDeliveryTimes();
  }, []);

  return { deliveryTimes, loading, error };
};

export const useFeatureOptions = () => {
  const [features, setFeatures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadFeatures = async () => {
      try {
        setLoading(true);
        setError(null);
        const featureOptions = await filterService.getFeatureOptions();
        setFeatures(featureOptions);
      } catch (err) {
        console.error('Error loading feature options:', err);
        setError(err.message);
        setFeatures([
          { id: 'free-delivery', label: 'Free Delivery', icon: '🚚' },
          { id: 'popular', label: 'Popular', icon: '🔥' }
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadFeatures();
  }, []);

  return { features, loading, error };
};

export const usePriceRangeOptions = () => {
  const [priceRanges, setPriceRanges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadPriceRanges = async () => {
      try {
        setLoading(true);
        setError(null);
        const priceOptions = await filterService.getPriceRangeOptions();
        setPriceRanges(priceOptions);
      } catch (err) {
        console.error('Error loading price range options:', err);
        setError(err.message);
        setPriceRanges([
          { value: 1, label: '$', description: 'Budget-friendly' },
          { value: 2, label: '$$', description: 'Moderate' }
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadPriceRanges();
  }, []);

  return { priceRanges, loading, error };
};

export const useDistanceOptions = () => {
  const [distances, setDistances] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadDistances = async () => {
      try {
        setLoading(true);
        setError(null);
        const distanceOptions = await filterService.getDistanceOptions();
        setDistances(distanceOptions);
      } catch (err) {
        console.error('Error loading distance options:', err);
        setError(err.message);
        setDistances([
          { value: 5, label: 'Under 5 km', icon: '🚶' },
          { value: 10, label: 'Under 10 km', icon: '🚲' }
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadDistances();
  }, []);

  return { distances, loading, error };
};

export const useSortOptions = () => {
  const [sortOptions, setSortOptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadSortOptions = async () => {
      try {
        setLoading(true);
        setError(null);
        const options = await filterService.getSortOptions();
        setSortOptions(options);
      } catch (err) {
        console.error('Error loading sort options:', err);
        setError(err.message);
        setSortOptions([
          { value: 'relevance', label: 'Relevance', icon: '🎯' },
          { value: 'rating', label: 'Rating', icon: '⭐' }
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadSortOptions();
  }, []);

  return { sortOptions, loading, error };
};

// Hook for restaurant filtering logic
export const useRestaurantFiltering = (restaurants = []) => {
  const [filteredRestaurants, setFilteredRestaurants] = useState(restaurants);

  const applyFilters = useCallback((filters) => {
    if (!restaurants.length) {
      setFilteredRestaurants([]);
      return;
    }

    let filtered = [...restaurants];

    // Apply cuisine filter
    if (filters.cuisines && filters.cuisines.length > 0) {
      filtered = filtered.filter(restaurant => 
        filters.cuisines.some(cuisine => 
          restaurant.cuisine_type?.toLowerCase().includes(cuisine.toLowerCase()) ||
          restaurant.tags?.some(tag => tag.toLowerCase().includes(cuisine.toLowerCase()))
        )
      );
    }

    // Apply dietary restrictions filter
    if (filters.dietaryRestrictions && filters.dietaryRestrictions.length > 0) {
      filtered = filtered.filter(restaurant =>
        filters.dietaryRestrictions.some(restriction =>
          restaurant.dietary_options?.includes(restriction) ||
          restaurant.tags?.includes(restriction)
        )
      );
    }

    // Apply price range filter
    if (filters.priceRange && filters.priceRange.length === 2) {
      const [minPrice, maxPrice] = filters.priceRange;
      filtered = filtered.filter(restaurant =>
        restaurant.price_range >= minPrice && restaurant.price_range <= maxPrice
      );
    }

    // Apply delivery time filter
    if (filters.deliveryTime) {
      filtered = filtered.filter(restaurant =>
        (restaurant.estimated_delivery_time || 45) <= filters.deliveryTime
      );
    }

    // Apply rating filter
    if (filters.rating) {
      filtered = filtered.filter(restaurant =>
        (restaurant.rating || 0) >= filters.rating
      );
    }

    // Apply distance filter (if location data is available)
    if (filters.distance && filters.userLocation) {
      filtered = filtered.filter(restaurant => {
        if (!restaurant.location) return true;
        const distance = calculateDistance(
          filters.userLocation,
          restaurant.location
        );
        return distance <= filters.distance;
      });
    }

    // Apply features filter
    if (filters.features && filters.features.length > 0) {
      filtered = filtered.filter(restaurant => {
        return filters.features.every(feature => {
          switch (feature) {
            case 'free-delivery':
              return restaurant.free_delivery || restaurant.minimum_order_for_free_delivery;
            case 'fast-delivery':
              return (restaurant.estimated_delivery_time || 45) <= 30;
            case 'new':
              return restaurant.is_new || isNewRestaurant(restaurant.created_at);
            case 'popular':
              return restaurant.is_popular || restaurant.order_count > 100;
            case 'highly-rated':
              return (restaurant.rating || 0) >= 4.5;
            case 'promoted':
              return restaurant.is_promoted;
            default:
              return true;
          }
        });
      });
    }

    // Apply sorting
    if (filters.sortBy) {
      filtered = sortRestaurants(filtered, filters.sortBy);
    }

    setFilteredRestaurants(filtered);
  }, [restaurants]);

  return {
    filteredRestaurants,
    applyFilters
  };
};

// Helper functions
const calculateDistance = (location1, location2) => {
  // Simple distance calculation (you might want to use a more accurate method)
  const lat1 = location1.latitude;
  const lon1 = location1.longitude;
  const lat2 = location2.latitude;
  const lon2 = location2.longitude;

  const R = 6371; // Radius of the Earth in km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c;

  return distance;
};

const isNewRestaurant = (createdAt) => {
  if (!createdAt) return false;
  const created = new Date(createdAt);
  const now = new Date();
  const diffTime = Math.abs(now - created);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= 30; // Consider new if created within 30 days
};

const sortRestaurants = (restaurants, sortBy) => {
  const sorted = [...restaurants];

  switch (sortBy) {
    case 'rating':
      return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0));
    case 'delivery-time':
      return sorted.sort((a, b) => 
        (a.estimated_delivery_time || 45) - (b.estimated_delivery_time || 45)
      );
    case 'price-low':
      return sorted.sort((a, b) => (a.price_range || 1) - (b.price_range || 1));
    case 'price-high':
      return sorted.sort((a, b) => (b.price_range || 1) - (a.price_range || 1));
    case 'popularity':
      return sorted.sort((a, b) => (b.order_count || 0) - (a.order_count || 0));
    case 'newest':
      return sorted.sort((a, b) => new Date(b.created_at || 0) - new Date(a.created_at || 0));
    case 'distance':
      // This would require location data
      return sorted;
    case 'relevance':
    default:
      return sorted;
  }
};
