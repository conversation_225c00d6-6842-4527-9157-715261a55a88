import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  MapPin,
  Phone,
  Mail,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  Eye,
  Calendar,
  Star,
  Building,
  DollarSign,
  Shield,
  RefreshCw,
  User,
} from "lucide-react";
import Card from "../../components/common/Card";
import Button from "../../components/common/Button";
import Badge from "../../components/common/Badge";
import { useAuth } from "../../context/AuthContext";
import restaurantApi from "../../utils/restaurantApi";

function RestaurantApprovalReal() {
  const { user: currentUser } = useAuth();

  // State for restaurant data
  const [restaurants, setRestaurants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [filteredRestaurants, setFilteredRestaurants] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("pending");
  const [selectedRestaurant, setSelectedRestaurant] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [approvalNotes, setApprovalNotes] = useState("");
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  // Fetch restaurants from API
  const fetchRestaurants = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await restaurantApi.getAdminRestaurants({
        status: statusFilter === "all" ? undefined : statusFilter,
        search: searchQuery || undefined,
      });

      if (result.success) {
        setRestaurants(result.data);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError("Failed to fetch restaurants");
      console.error("Error fetching restaurants:", err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch restaurants on component mount and when filters change
  useEffect(() => {
    fetchRestaurants();
  }, [statusFilter]);

  // Filter restaurants based on search
  useEffect(() => {
    let filtered = restaurants;

    if (searchQuery) {
      filtered = filtered.filter(
        (restaurant) =>
          restaurant.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          restaurant.owner?.name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          restaurant.owner?.email
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          restaurant.contact_number?.includes(searchQuery)
      );
    }

    setFilteredRestaurants(filtered);
  }, [restaurants, searchQuery]);

  const getStatusBadge = (restaurant) => {
    if (restaurant.is_verified) {
      return <Badge variant='success'>Approved</Badge>;
    } else {
      return <Badge variant='warning'>Pending Review</Badge>;
    }
  };

  const handleApprove = async (restaurant) => {
    setActionLoading(true);
    try {
      const result = await restaurantApi.adminVerifyRestaurant(
        restaurant.id,
        "approve",
        approvalNotes
      );

      if (result.success) {
        // Refresh the list
        await fetchRestaurants();
        setApprovalNotes("");
        setShowDetails(false);
        alert("Restaurant approved successfully!");
      } else {
        alert("Failed to approve restaurant: " + result.error);
      }
    } catch (error) {
      console.error("Error approving restaurant:", error);
      alert("Error approving restaurant");
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = async (restaurant) => {
    setActionLoading(true);
    try {
      const result = await restaurantApi.adminVerifyRestaurant(
        restaurant.id,
        "reject",
        rejectionReason
      );

      if (result.success) {
        // Refresh the list
        await fetchRestaurants();
        setRejectionReason("");
        setShowRejectModal(false);
        setShowDetails(false);
        alert("Restaurant rejected successfully!");
      } else {
        alert("Failed to reject restaurant: " + result.error);
      }
    } catch (error) {
      console.error("Error rejecting restaurant:", error);
      alert("Error rejecting restaurant");
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className='p-6'>
        <div className='animate-pulse'>
          <div className='h-8 bg-gray-200 rounded w-1/4 mb-6'></div>
          <div className='space-y-4'>
            {[1, 2, 3].map((i) => (
              <div key={i} className='h-32 bg-gray-200 rounded'></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='p-6 animate-fade-in'>
      {/* Header */}
      <div className='mb-8'>
        <h1 className='text-3xl font-bold text-gray-900 mb-2'>
          Restaurant Approvals
        </h1>
        <p className='text-text-secondary'>
          Review and manage restaurant applications
        </p>
      </div>

      {/* Filters */}
      <div className='mb-6 flex flex-col sm:flex-row gap-4'>
        <div className='flex-1'>
          <div className='relative'>
            <Search
              className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400'
              size={20}
            />
            <input
              type='text'
              placeholder='Search restaurants, owners, or emails...'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className='w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
            />
          </div>
        </div>

        <div className='flex gap-2'>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className='px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
          >
            <option value='all'>All Status</option>
            <option value='pending'>Pending</option>
            <option value='approved'>Approved</option>
          </select>

          <Button
            variant='outline'
            onClick={fetchRestaurants}
            disabled={loading}
          >
            <RefreshCw size={18} className={loading ? "animate-spin" : ""} />
          </Button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-lg'>
          <div className='flex items-center'>
            <AlertCircle className='text-red-500 mr-2' size={20} />
            <span className='text-red-700'>{error}</span>
          </div>
        </div>
      )}

      {/* Restaurant List */}
      {filteredRestaurants.length === 0 ? (
        <div className='text-center py-12'>
          <Building className='mx-auto text-gray-400 mb-4' size={48} />
          <h3 className='text-lg font-medium text-gray-900 mb-2'>
            No restaurants found
          </h3>
          <p className='text-gray-500'>
            {searchQuery
              ? "Try adjusting your search criteria"
              : "No restaurant applications to review"}
          </p>
        </div>
      ) : (
        <div className='space-y-4'>
          {filteredRestaurants.map((restaurant) => (
            <Card key={restaurant.id} className='p-6'>
              <div className='flex items-start justify-between'>
                <div className='flex-1'>
                  <div className='flex items-center gap-3 mb-2'>
                    <h3 className='text-xl font-semibold text-gray-900'>
                      {restaurant.name}
                    </h3>
                    {getStatusBadge(restaurant)}
                  </div>

                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600'>
                    <div className='flex items-center'>
                      <User className='mr-2' size={16} />
                      <span>Owner: {restaurant.owner?.name || "N/A"}</span>
                    </div>
                    <div className='flex items-center'>
                      <Mail className='mr-2' size={16} />
                      <span>{restaurant.owner?.email || "N/A"}</span>
                    </div>
                    <div className='flex items-center'>
                      <Phone className='mr-2' size={16} />
                      <span>{restaurant.contact_number || "N/A"}</span>
                    </div>
                    <div className='flex items-center'>
                      <Calendar className='mr-2' size={16} />
                      <span>Applied: {formatDate(restaurant.created_at)}</span>
                    </div>
                    <div className='flex items-center'>
                      <MapPin className='mr-2' size={16} />
                      <span>
                        {restaurant.address?.street || "No address"},{" "}
                        {restaurant.address?.city || ""}
                      </span>
                    </div>
                    <div className='flex items-center'>
                      <Clock className='mr-2' size={16} />
                      <span>
                        {restaurant.opening_time || "N/A"} -{" "}
                        {restaurant.closing_time || "N/A"}
                      </span>
                    </div>
                  </div>

                  {restaurant.description && (
                    <p className='mt-3 text-gray-700 line-clamp-2'>
                      {restaurant.description}
                    </p>
                  )}
                </div>

                <div className='flex flex-col gap-2 ml-4'>
                  <Button
                    variant='outline'
                    size='small'
                    onClick={() => {
                      setSelectedRestaurant(restaurant);
                      setShowDetails(true);
                    }}
                  >
                    <Eye size={16} className='mr-1' />
                    View Details
                  </Button>

                  {!restaurant.is_verified && (
                    <div className='flex gap-2'>
                      <Button
                        variant='success'
                        size='small'
                        onClick={() => handleApprove(restaurant)}
                        disabled={actionLoading}
                      >
                        <CheckCircle size={16} className='mr-1' />
                        Approve
                      </Button>
                      <Button
                        variant='danger'
                        size='small'
                        onClick={() => {
                          setSelectedRestaurant(restaurant);
                          setShowRejectModal(true);
                        }}
                        disabled={actionLoading}
                      >
                        <XCircle size={16} className='mr-1' />
                        Reject
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Restaurant Details Modal */}
      {showDetails && selectedRestaurant && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto'>
            <div className='p-6 border-b border-gray-200'>
              <div className='flex items-center justify-between'>
                <h2 className='text-2xl font-bold text-gray-900'>
                  {selectedRestaurant.name}
                </h2>
                <Button variant='outline' onClick={() => setShowDetails(false)}>
                  Close
                </Button>
              </div>
            </div>

            <div className='p-6'>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                <div>
                  <h3 className='text-lg font-semibold mb-3'>
                    Restaurant Information
                  </h3>
                  <div className='space-y-2 text-sm'>
                    <p>
                      <strong>Name:</strong> {selectedRestaurant.name}
                    </p>
                    <p>
                      <strong>Description:</strong>{" "}
                      {selectedRestaurant.description || "N/A"}
                    </p>
                    <p>
                      <strong>Contact:</strong>{" "}
                      {selectedRestaurant.contact_number || "N/A"}
                    </p>
                    <p>
                      <strong>Opening Hours:</strong>{" "}
                      {selectedRestaurant.opening_time || "N/A"} -{" "}
                      {selectedRestaurant.closing_time || "N/A"}
                    </p>
                    <p>
                      <strong>Delivery Fee:</strong> $
                      {selectedRestaurant.delivery_fee || "N/A"}
                    </p>
                    <p>
                      <strong>Minimum Order:</strong> $
                      {selectedRestaurant.min_order_amount || "N/A"}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-semibold mb-3'>
                    Owner Information
                  </h3>
                  <div className='space-y-2 text-sm'>
                    <p>
                      <strong>Name:</strong>{" "}
                      {selectedRestaurant.owner?.name || "N/A"}
                    </p>
                    <p>
                      <strong>Email:</strong>{" "}
                      {selectedRestaurant.owner?.email || "N/A"}
                    </p>
                    <p>
                      <strong>Phone:</strong>{" "}
                      {selectedRestaurant.owner?.phone || "N/A"}
                    </p>
                    <p>
                      <strong>Verified:</strong>{" "}
                      {selectedRestaurant.owner?.is_verified ? "Yes" : "No"}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-semibold mb-3'>Address</h3>
                  <div className='space-y-2 text-sm'>
                    <p>
                      <strong>Street:</strong>{" "}
                      {selectedRestaurant.address?.street || "N/A"}
                    </p>
                    <p>
                      <strong>City:</strong>{" "}
                      {selectedRestaurant.address?.city || "N/A"}
                    </p>
                    <p>
                      <strong>State:</strong>{" "}
                      {selectedRestaurant.address?.state || "N/A"}
                    </p>
                    <p>
                      <strong>Postal Code:</strong>{" "}
                      {selectedRestaurant.address?.postal_code || "N/A"}
                    </p>
                    <p>
                      <strong>Country:</strong>{" "}
                      {selectedRestaurant.address?.country || "N/A"}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className='text-lg font-semibold mb-3'>Status</h3>
                  <div className='space-y-2 text-sm'>
                    <p>
                      <strong>Verified:</strong>{" "}
                      {selectedRestaurant.is_verified ? "Yes" : "No"}
                    </p>
                    <p>
                      <strong>Active:</strong>{" "}
                      {selectedRestaurant.is_active ? "Yes" : "No"}
                    </p>
                    <p>
                      <strong>Created:</strong>{" "}
                      {formatDate(selectedRestaurant.created_at)}
                    </p>
                    <p>
                      <strong>Updated:</strong>{" "}
                      {formatDate(selectedRestaurant.updated_at)}
                    </p>
                  </div>
                </div>
              </div>

              {!selectedRestaurant.is_verified && (
                <div className='mt-6 pt-6 border-t border-gray-200'>
                  <div className='flex gap-4'>
                    <div className='flex-1'>
                      <label className='block text-sm font-medium text-gray-700 mb-2'>
                        Approval Notes (Optional)
                      </label>
                      <textarea
                        value={approvalNotes}
                        onChange={(e) => setApprovalNotes(e.target.value)}
                        placeholder='Add any notes for the restaurant owner...'
                        className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent'
                        rows={3}
                      />
                    </div>
                  </div>

                  <div className='flex gap-3 mt-4'>
                    <Button
                      variant='success'
                      onClick={() => handleApprove(selectedRestaurant)}
                      disabled={actionLoading}
                    >
                      <CheckCircle size={16} className='mr-2' />
                      {actionLoading ? "Approving..." : "Approve Restaurant"}
                    </Button>
                    <Button
                      variant='danger'
                      onClick={() => {
                        setShowRejectModal(true);
                      }}
                      disabled={actionLoading}
                    >
                      <XCircle size={16} className='mr-2' />
                      Reject Restaurant
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Reject Modal */}
      {showRejectModal && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4'>
          <div className='bg-white rounded-lg shadow-lg max-w-md w-full p-6'>
            <h3 className='text-xl font-semibold mb-4'>Reject Restaurant</h3>
            <p className='mb-4'>
              Are you sure you want to reject "{selectedRestaurant?.name}"?
              Please provide a reason:
            </p>
            <textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder='Reason for rejection...'
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent mb-4'
              rows={4}
              required
            />
            <div className='flex justify-end space-x-3'>
              <Button
                variant='outline'
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectionReason("");
                }}
              >
                Cancel
              </Button>
              <Button
                variant='danger'
                onClick={() => handleReject(selectedRestaurant)}
                disabled={actionLoading || !rejectionReason.trim()}
              >
                {actionLoading ? "Rejecting..." : "Reject"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default RestaurantApprovalReal;
