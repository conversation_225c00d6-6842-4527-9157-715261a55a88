import React, { useState } from "react";
import { useRestaurant } from "../../context/RestaurantContext";
import Button from "../common/Button";
import Card from "../common/Card";
import Input from "../common/Input";
import {
  Play,
  CheckCircle,
  XCircle,
  AlertCircle,
  Upload,
  Trash2,
  Edit,
  Eye,
} from "lucide-react";

const RestaurantApiTest = () => {
  const {
    createRestaurant,
    getRestaurant,
    updateRestaurant,
    patchRestaurant,
    deleteRestaurant,
    getRestaurants,
    loading,
    error,
    clearError,
  } = useRestaurant();

  const [testResults, setTestResults] = useState([]);
  const [testRestaurantId, setTestRestaurantId] = useState("");
  const [logoFile, setLogoFile] = useState(null);
  const [bannerFile, setBannerFile] = useState(null);

  const addTestResult = (operation, success, message, data = null) => {
    const result = {
      id: Date.now(),
      operation,
      success,
      message,
      data,
      timestamp: new Date().toLocaleTimeString(),
    };
    setTestResults((prev) => [result, ...prev]);
  };

  const handleFileChange = (e, type) => {
    const file = e.target.files[0];
    if (type === "logo") {
      setLogoFile(file);
    } else if (type === "banner") {
      setBannerFile(file);
    }
  };

  // Test Create Restaurant
  const testCreateRestaurant = async () => {
    try {
      const testData = {
        name: `Test Restaurant ${Date.now()}`,
        description: "This is a test restaurant created via API",
        contact_number: "+1234567890",
        opening_time: "09:00:00",
        closing_time: "22:00:00",
        address: {
          street: "123 Test Street",
          city: "Test City",
          state: "TS",
          postal_code: "12345",
          country: "USA",
          latitude: 40.7128,
          longitude: -74.0060,
        },
        logo: logoFile,
        banner: bannerFile,
      };

      const result = await createRestaurant(testData);
      
      if (result.success) {
        addTestResult(
          "CREATE",
          true,
          "Restaurant created successfully",
          result.data
        );
        setTestRestaurantId(result.data.id);
      } else {
        addTestResult("CREATE", false, result.error);
      }
    } catch (error) {
      addTestResult("CREATE", false, error.message);
    }
  };

  // Test Get Restaurant
  const testGetRestaurant = async () => {
    if (!testRestaurantId) {
      addTestResult("GET", false, "No restaurant ID available. Create a restaurant first.");
      return;
    }

    try {
      const result = await getRestaurant(testRestaurantId);
      
      if (result.success) {
        addTestResult(
          "GET",
          true,
          "Restaurant fetched successfully",
          result.data
        );
      } else {
        addTestResult("GET", false, result.error);
      }
    } catch (error) {
      addTestResult("GET", false, error.message);
    }
  };

  // Test Update Restaurant (PUT)
  const testUpdateRestaurant = async () => {
    if (!testRestaurantId) {
      addTestResult("UPDATE", false, "No restaurant ID available. Create a restaurant first.");
      return;
    }

    try {
      const updateData = {
        name: `Updated Restaurant ${Date.now()}`,
        description: "This restaurant has been updated via PUT API",
        contact_number: "+1234567890",
        opening_time: "10:00:00",
        closing_time: "23:00:00",
        address: {
          street: "456 Updated Street",
          city: "Updated City",
          state: "UP",
          postal_code: "54321",
          country: "USA",
          latitude: 40.7128,
          longitude: -74.0060,
        },
      };

      const result = await updateRestaurant(testRestaurantId, updateData);
      
      if (result.success) {
        addTestResult(
          "UPDATE",
          true,
          "Restaurant updated successfully (PUT)",
          result.data
        );
      } else {
        addTestResult("UPDATE", false, result.error);
      }
    } catch (error) {
      addTestResult("UPDATE", false, error.message);
    }
  };

  // Test Patch Restaurant (PATCH)
  const testPatchRestaurant = async () => {
    if (!testRestaurantId) {
      addTestResult("PATCH", false, "No restaurant ID available. Create a restaurant first.");
      return;
    }

    try {
      const patchData = {
        name: `Patched Restaurant ${Date.now()}`,
        description: "This restaurant has been partially updated via PATCH API",
      };

      const result = await patchRestaurant(testRestaurantId, patchData);
      
      if (result.success) {
        addTestResult(
          "PATCH",
          true,
          "Restaurant patched successfully (PATCH)",
          result.data
        );
      } else {
        addTestResult("PATCH", false, result.error);
      }
    } catch (error) {
      addTestResult("PATCH", false, error.message);
    }
  };

  // Test Get All Restaurants
  const testGetAllRestaurants = async () => {
    try {
      const result = await getRestaurants();
      
      if (result.success) {
        addTestResult(
          "GET_ALL",
          true,
          `Fetched ${result.data?.length || 0} restaurants`,
          result.data
        );
      } else {
        addTestResult("GET_ALL", false, result.error);
      }
    } catch (error) {
      addTestResult("GET_ALL", false, error.message);
    }
  };

  // Test Delete Restaurant
  const testDeleteRestaurant = async () => {
    if (!testRestaurantId) {
      addTestResult("DELETE", false, "No restaurant ID available. Create a restaurant first.");
      return;
    }

    try {
      const result = await deleteRestaurant(testRestaurantId);
      
      if (result.success) {
        addTestResult("DELETE", true, "Restaurant deleted successfully");
        setTestRestaurantId(""); // Clear the ID since restaurant is deleted
      } else {
        addTestResult("DELETE", false, result.error);
      }
    } catch (error) {
      addTestResult("DELETE", false, error.message);
    }
  };

  const clearResults = () => {
    setTestResults([]);
    clearError();
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">Restaurant API Test Suite</h1>
        <p className="text-text-secondary">
          Test all Restaurant CRUD operations with the backend API
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="mb-6 border-l-4 border-red-500">
          <div className="flex items-start p-4">
            <AlertCircle size={18} className="text-red-500 mr-2 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-red-600 text-sm">{error}</p>
              <button
                onClick={clearError}
                className="text-red-500 text-sm underline mt-1"
              >
                Dismiss
              </button>
            </div>
          </div>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Test Controls */}
        <div>
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">API Test Controls</h2>
            
            {/* File Upload Section */}
            <div className="mb-6">
              <h3 className="font-medium mb-3">Upload Test Files (Optional)</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Logo</label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange(e, "logo")}
                    className="w-full text-sm"
                  />
                  {logoFile && (
                    <p className="text-xs text-green-600 mt-1">✓ {logoFile.name}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Banner</label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleFileChange(e, "banner")}
                    className="w-full text-sm"
                  />
                  {bannerFile && (
                    <p className="text-xs text-green-600 mt-1">✓ {bannerFile.name}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Current Restaurant ID */}
            <div className="mb-6">
              <Input
                label="Test Restaurant ID"
                value={testRestaurantId}
                onChange={(e) => setTestRestaurantId(e.target.value)}
                placeholder="Will be set automatically after creation"
                disabled={loading}
              />
            </div>

            {/* Test Buttons */}
            <div className="space-y-3">
              <Button
                variant="primary"
                fullWidth
                onClick={testCreateRestaurant}
                loading={loading}
                icon={<Upload size={16} />}
              >
                Test CREATE Restaurant
              </Button>

              <Button
                variant="secondary"
                fullWidth
                onClick={testGetRestaurant}
                loading={loading}
                icon={<Eye size={16} />}
                disabled={!testRestaurantId}
              >
                Test GET Restaurant
              </Button>

              <Button
                variant="secondary"
                fullWidth
                onClick={testGetAllRestaurants}
                loading={loading}
                icon={<Eye size={16} />}
              >
                Test GET All Restaurants
              </Button>

              <Button
                variant="warning"
                fullWidth
                onClick={testUpdateRestaurant}
                loading={loading}
                icon={<Edit size={16} />}
                disabled={!testRestaurantId}
              >
                Test UPDATE (PUT) Restaurant
              </Button>

              <Button
                variant="warning"
                fullWidth
                onClick={testPatchRestaurant}
                loading={loading}
                icon={<Edit size={16} />}
                disabled={!testRestaurantId}
              >
                Test PATCH Restaurant
              </Button>

              <Button
                variant="danger"
                fullWidth
                onClick={testDeleteRestaurant}
                loading={loading}
                icon={<Trash2 size={16} />}
                disabled={!testRestaurantId}
              >
                Test DELETE Restaurant
              </Button>

              <Button
                variant="outline"
                fullWidth
                onClick={clearResults}
                disabled={loading}
              >
                Clear Results
              </Button>
            </div>
          </Card>
        </div>

        {/* Test Results */}
        <div>
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Test Results</h2>
            
            {testResults.length === 0 ? (
              <div className="text-center py-8 text-text-secondary">
                <Play size={48} className="mx-auto mb-4 opacity-50" />
                <p>No tests run yet. Click a test button to start.</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {testResults.map((result) => (
                  <div
                    key={result.id}
                    className={`p-3 rounded-lg border-l-4 ${
                      result.success
                        ? "bg-green-50 border-green-500"
                        : "bg-red-50 border-red-500"
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-center">
                        {result.success ? (
                          <CheckCircle size={16} className="text-green-500 mr-2" />
                        ) : (
                          <XCircle size={16} className="text-red-500 mr-2" />
                        )}
                        <span className="font-medium text-sm">
                          {result.operation}
                        </span>
                      </div>
                      <span className="text-xs text-text-secondary">
                        {result.timestamp}
                      </span>
                    </div>
                    <p className="text-sm mt-1 ml-6">{result.message}</p>
                    {result.data && (
                      <details className="mt-2 ml-6">
                        <summary className="text-xs text-text-secondary cursor-pointer">
                          View Response Data
                        </summary>
                        <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                ))}
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RestaurantApiTest;
