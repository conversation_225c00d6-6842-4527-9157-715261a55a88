from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
import json
import secrets
import string
from .models import DeliveryAgentProfile

User = get_user_model()

class AdminAgentApprovalView(View):
    """Admin interface for agent approval"""
    
    @method_decorator(login_required)
    def dispatch(self, *args, **kwargs):
        # Check if user is admin
        if not self.request.user.role == 'admin':
            return JsonResponse({'error': 'Admin access required'}, status=403)
        return super().dispatch(*args, **kwargs)
    
    def get(self, request):
        """Get pending agent applications"""
        status_filter = request.GET.get('status', 'pending')
        
        agents = DeliveryAgentProfile.objects.filter(status=status_filter).order_by('-application_date')
        
        agents_data = []
        for agent in agents:
            agents_data.append({
                'id': agent.id,
                'agent_id': agent.agent_id,
                'full_name': agent.full_name,
                'father_name': agent.father_name,
                'national_id': agent.national_id,
                'phone_number': agent.phone_number,
                'province': agent.province,
                'district': agent.district,
                'vehicle_type': agent.vehicle_type,
                'vehicle_model': agent.vehicle_model,
                'application_date': agent.application_date.isoformat(),
                'status': agent.status,
                'documents': {
                    'tazkira_front': bool(agent.tazkira_front_image),
                    'tazkira_back': bool(agent.tazkira_back_image),
                    'driving_license': bool(agent.driving_license_image),
                    'vehicle_registration': bool(agent.vehicle_registration_image),
                    'profile_photo': bool(agent.profile_photo)
                },
                'references': [
                    {
                        'name': agent.reference1_name,
                        'phone': agent.reference1_phone,
                        'relation': agent.reference1_relation
                    },
                    {
                        'name': agent.reference2_name,
                        'phone': agent.reference2_phone,
                        'relation': agent.reference2_relation
                    }
                ]
            })
        
        return JsonResponse({
            'status': 'success',
            'agents': agents_data,
            'total_count': len(agents_data)
        })

class AgentApprovalActionView(View):
    """Handle agent approval/rejection actions"""
    
    @method_decorator(login_required)
    @method_decorator(csrf_exempt)
    def dispatch(self, *args, **kwargs):
        if not self.request.user.role == 'admin':
            return JsonResponse({'error': 'Admin access required'}, status=403)
        return super().dispatch(*args, **kwargs)
    
    def post(self, request, agent_id):
        """Approve or reject agent application"""
        try:
            data = json.loads(request.body)
            action = data.get('action')  # 'approve' or 'reject'
            notes = data.get('notes', '')
            
            agent = get_object_or_404(DeliveryAgentProfile, agent_id=agent_id)
            
            if action == 'approve':
                return self.approve_agent(agent, notes)
            elif action == 'reject':
                return self.reject_agent(agent, notes)
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid action. Use "approve" or "reject"'
                }, status=400)
                
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Action failed: {str(e)}'
            }, status=500)
    
    def approve_agent(self, agent, notes):
        """Approve agent application"""
        try:
            with transaction.atomic():
                # Generate secure password
                password = self.generate_secure_password()
                
                # Activate user account
                user = agent.user
                user.is_active = True
                user.set_password(password)
                user.save()
                
                # Update agent profile
                agent.status = 'approved'
                agent.is_verified = True
                agent.approval_date = timezone.now()
                agent.approved_by = self.request.user
                agent.admin_notes = notes
                agent.save()
                
                # Send approval notification
                self.send_approval_notification(agent, password)
                
                # Log approval action
                self.log_admin_action(agent, 'approved', notes)
                
                return JsonResponse({
                    'status': 'success',
                    'message': f'Agent {agent.agent_id} approved successfully',
                    'agent_id': agent.agent_id,
                    'login_credentials': {
                        'username': user.user_name,
                        'password': password,
                        'note': 'Password sent to agent via SMS'
                    },
                    'next_steps': [
                        'Agent will receive login credentials via SMS',
                        'Schedule training session',
                        'Prepare equipment for distribution',
                        'Add agent to active roster'
                    ]
                })
                
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Approval failed: {str(e)}'
            }, status=500)
    
    def reject_agent(self, agent, notes):
        """Reject agent application"""
        try:
            with transaction.atomic():
                # Update agent profile
                agent.status = 'rejected'
                agent.rejection_date = timezone.now()
                agent.rejected_by = self.request.user
                agent.admin_notes = notes
                agent.save()
                
                # Deactivate user account
                user = agent.user
                user.is_active = False
                user.save()
                
                # Send rejection notification
                self.send_rejection_notification(agent, notes)
                
                # Log rejection action
                self.log_admin_action(agent, 'rejected', notes)
                
                return JsonResponse({
                    'status': 'success',
                    'message': f'Agent {agent.agent_id} application rejected',
                    'agent_id': agent.agent_id,
                    'rejection_reason': notes
                })
                
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Rejection failed: {str(e)}'
            }, status=500)
    
    def generate_secure_password(self):
        """Generate secure password for agent"""
        # Generate 8-character password with letters and numbers
        alphabet = string.ascii_letters + string.digits
        password = ''.join(secrets.choice(alphabet) for _ in range(8))
        return password
    
    def send_approval_notification(self, agent, password):
        """Send approval notification to agent"""
        # TODO: Implement SMS sending
        message = f"""
        تبریک! ستاسو د ډیلیوری ایجنټ غوښتنه تصویب شوه.
        
        د ننوتلو معلومات:
        کارن نوم: {agent.user.user_name}
        پټ نوم: {password}
        
        د موبایل اپلیکیشن ډاونلوډ کړئ او پیل وکړئ!
        
        Congratulations! Your delivery agent application has been approved.
        
        Login Details:
        Username: {agent.user.user_name}
        Password: {password}
        
        Download the mobile app and start working!
        """
        
        # Send SMS to agent.phone_number with message
        print(f"SMS to {agent.phone_number}: {message}")
    
    def send_rejection_notification(self, agent, reason):
        """Send rejection notification to agent"""
        # TODO: Implement SMS sending
        message = f"""
        بخښنه غواړو، ستاسو د ډیلیوری ایجنټ غوښتنه رد شوه.
        
        دلیل: {reason}
        
        د نورو معلوماتو لپاره زموږ د ملاتړ ټیم سره اړیکه ونیسئ.
        
        Sorry, your delivery agent application has been rejected.
        
        Reason: {reason}
        
        Contact our support team for more information.
        """
        
        # Send SMS to agent.phone_number with message
        print(f"SMS to {agent.phone_number}: {message}")
    
    def log_admin_action(self, agent, action, notes):
        """Log admin action for audit trail"""
        # TODO: Implement audit logging
        log_entry = {
            'timestamp': timezone.now().isoformat(),
            'admin_user': self.request.user.user_name,
            'agent_id': agent.agent_id,
            'action': action,
            'notes': notes
        }
        print(f"Admin Action Log: {log_entry}")

class AgentDetailView(View):
    """Get detailed agent information for admin review"""
    
    @method_decorator(login_required)
    def dispatch(self, *args, **kwargs):
        if not self.request.user.role == 'admin':
            return JsonResponse({'error': 'Admin access required'}, status=403)
        return super().dispatch(*args, **kwargs)
    
    def get(self, request, agent_id):
        """Get detailed agent information"""
        try:
            agent = get_object_or_404(DeliveryAgentProfile, agent_id=agent_id)
            
            agent_detail = {
                'basic_info': {
                    'agent_id': agent.agent_id,
                    'full_name': agent.full_name,
                    'father_name': agent.father_name,
                    'national_id': agent.national_id,
                    'date_of_birth': agent.date_of_birth.isoformat() if agent.date_of_birth else None,
                    'gender': agent.gender,
                    'marital_status': agent.marital_status,
                    'phone_number': agent.phone_number,
                    'secondary_phone': agent.secondary_phone,
                    'email': agent.email
                },
                'address_info': {
                    'province': agent.province,
                    'district': agent.district,
                    'area': agent.area,
                    'street_address': agent.street_address,
                    'nearby_landmark': agent.nearby_landmark
                },
                'vehicle_info': {
                    'type': agent.vehicle_type,
                    'model': agent.vehicle_model,
                    'year': agent.vehicle_year,
                    'license_plate': agent.license_plate,
                    'color': agent.vehicle_color,
                    'driving_license': agent.driving_license
                },
                'references': [
                    {
                        'name': agent.reference1_name,
                        'phone': agent.reference1_phone,
                        'relation': agent.reference1_relation,
                        'verified': False  # TODO: Track verification status
                    },
                    {
                        'name': agent.reference2_name,
                        'phone': agent.reference2_phone,
                        'relation': agent.reference2_relation,
                        'verified': False
                    }
                ],
                'banking_info': {
                    'bank_name': agent.bank_name,
                    'account_number': agent.account_number,
                    'account_holder_name': agent.account_holder_name,
                    'mobile_wallet': agent.mobile_wallet
                },
                'emergency_contact': {
                    'name': agent.emergency_contact,
                    'relation': agent.emergency_relation
                },
                'documents': {
                    'tazkira_front': agent.tazkira_front_image.url if agent.tazkira_front_image else None,
                    'tazkira_back': agent.tazkira_back_image.url if agent.tazkira_back_image else None,
                    'driving_license': agent.driving_license_image.url if agent.driving_license_image else None,
                    'vehicle_registration': agent.vehicle_registration_image.url if agent.vehicle_registration_image else None,
                    'profile_photo': agent.profile_photo.url if agent.profile_photo else None
                },
                'application_info': {
                    'status': agent.status,
                    'application_date': agent.application_date.isoformat(),
                    'approval_date': agent.approval_date.isoformat() if agent.approval_date else None,
                    'rejection_date': agent.rejection_date.isoformat() if agent.rejection_date else None,
                    'admin_notes': agent.admin_notes,
                    'is_verified': agent.is_verified
                }
            }
            
            return JsonResponse({
                'status': 'success',
                'agent': agent_detail
            })
            
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Failed to get agent details: {str(e)}'
            }, status=500)

class BulkApprovalView(View):
    """Handle bulk approval of multiple agents"""
    
    @method_decorator(login_required)
    @method_decorator(csrf_exempt)
    def dispatch(self, *args, **kwargs):
        if not self.request.user.role == 'admin':
            return JsonResponse({'error': 'Admin access required'}, status=403)
        return super().dispatch(*args, **kwargs)
    
    def post(self, request):
        """Bulk approve multiple agents"""
        try:
            data = json.loads(request.body)
            agent_ids = data.get('agent_ids', [])
            action = data.get('action', 'approve')
            notes = data.get('notes', 'Bulk approval')
            
            if not agent_ids:
                return JsonResponse({
                    'status': 'error',
                    'message': 'No agent IDs provided'
                }, status=400)
            
            results = []
            
            for agent_id in agent_ids:
                try:
                    agent = DeliveryAgentProfile.objects.get(agent_id=agent_id)
                    
                    if action == 'approve':
                        # Generate password and approve
                        password = self.generate_secure_password()
                        
                        with transaction.atomic():
                            user = agent.user
                            user.is_active = True
                            user.set_password(password)
                            user.save()
                            
                            agent.status = 'approved'
                            agent.is_verified = True
                            agent.approval_date = timezone.now()
                            agent.approved_by = request.user
                            agent.admin_notes = notes
                            agent.save()
                        
                        results.append({
                            'agent_id': agent_id,
                            'status': 'success',
                            'message': 'Approved successfully'
                        })
                    
                except DeliveryAgentProfile.DoesNotExist:
                    results.append({
                        'agent_id': agent_id,
                        'status': 'error',
                        'message': 'Agent not found'
                    })
                except Exception as e:
                    results.append({
                        'agent_id': agent_id,
                        'status': 'error',
                        'message': str(e)
                    })
            
            return JsonResponse({
                'status': 'success',
                'message': f'Bulk {action} completed',
                'results': results
            })
            
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Bulk operation failed: {str(e)}'
            }, status=500)
    
    def generate_secure_password(self):
        """Generate secure password"""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(8))
