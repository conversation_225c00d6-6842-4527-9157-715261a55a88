from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal

User = get_user_model()

class DeliveryAgentProfile(models.Model):
    """
    Extended profile for delivery agents with vehicle, location, and performance data
    """
    VEHICLE_CHOICES = [
        ('motorcycle', 'Motorcycle'),
        ('bicycle', 'Bicycle'),
        ('car', 'Car'),
        ('scooter', 'Scooter'),
    ]

    STATUS_CHOICES = [
        ('available', 'Available'),
        ('busy', 'Busy'),
        ('offline', 'Offline'),
    ]

    # Basic Information
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='delivery_profile')

    # Vehicle Information
    vehicle_type = models.CharField(max_length=20, choices=VEHICLE_CHOICES, default='motorcycle')
    vehicle_number = models.CharField(max_length=20, help_text="License plate number")
    vehicle_color = models.CharField(max_length=30, blank=True)
    license_number = models.CharField(max_length=50, help_text="Driver's license number")

    # Location and Status
    current_location = models.JSONField(
        null=True,
        blank=True,
        help_text="Current GPS coordinates and address"
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='offline')
    is_online = models.BooleanField(default=False)
    last_seen = models.DateTimeField(auto_now=True)

    # Performance Metrics
    rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(0), MaxValueValidator(5)]
    )
    total_deliveries = models.PositiveIntegerField(default=0)
    completed_orders = models.PositiveIntegerField(default=0)
    cancelled_orders = models.PositiveIntegerField(default=0)
    average_delivery_time = models.PositiveIntegerField(default=0, help_text="Average delivery time in minutes")

    # Financial Information
    earnings = models.JSONField(
        default=dict,
        help_text="Earnings breakdown: today, this_week, this_month"
    )
    bank_details = models.JSONField(
        null=True,
        blank=True,
        help_text="Bank account information for payments"
    )

    # Service Areas
    delivery_zones = models.JSONField(
        default=list,
        help_text="List of areas where agent can deliver"
    )

    # Verification and Status
    is_verified = models.BooleanField(default=False)
    verification_documents = models.JSONField(
        default=dict,
        help_text="Uploaded verification documents"
    )

    # Timestamps
    joined_date = models.DateField(auto_now_add=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'delivery_agent_profiles'
        verbose_name = 'Delivery Agent Profile'
        verbose_name_plural = 'Delivery Agent Profiles'

    def __str__(self):
        return f"{self.user.name} - {self.vehicle_type} ({self.status})"

    @property
    def completion_rate(self):
        """Calculate order completion rate"""
        total = self.completed_orders + self.cancelled_orders
        if total == 0:
            return 0
        return round((self.completed_orders / total) * 100, 2)

    @property
    def is_available_for_delivery(self):
        """Check if agent is available for new deliveries"""
        return self.is_online and self.status == 'available' and self.is_verified

    def update_earnings(self, amount, period='today'):
        """Update earnings for specified period"""
        if not self.earnings:
            self.earnings = {'today': 0, 'this_week': 0, 'this_month': 0}

        self.earnings[period] = self.earnings.get(period, 0) + float(amount)
        self.save(update_fields=['earnings'])

    def update_location(self, latitude, longitude, address=None):
        """Update current location"""
        self.current_location = {
            'lat': latitude,
            'lng': longitude,
            'address': address or '',
            'updated_at': models.DateTimeField().now().isoformat()
        }
        self.save(update_fields=['current_location', 'last_seen'])

    def set_status(self, status):
        """Update agent status"""
        if status in dict(self.STATUS_CHOICES):
            self.status = status
            self.is_online = status != 'offline'
            self.save(update_fields=['status', 'is_online', 'last_seen'])


class DeliveryAgentDocument(models.Model):
    """
    Model to store delivery agent verification documents
    """
    DOCUMENT_TYPES = [
        ('license', 'Driver License'),
        ('id_card', 'National ID Card'),
        ('vehicle_registration', 'Vehicle Registration'),
        ('insurance', 'Insurance Certificate'),
        ('photo', 'Profile Photo'),
    ]

    agent = models.ForeignKey(DeliveryAgentProfile, on_delete=models.CASCADE, related_name='documents')
    document_type = models.CharField(max_length=30, choices=DOCUMENT_TYPES)
    document_file = models.FileField(upload_to='delivery_agent_docs/')
    is_verified = models.BooleanField(default=False)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    verified_at = models.DateTimeField(null=True, blank=True)
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_documents')

    class Meta:
        db_table = 'delivery_agent_documents'
        unique_together = ('agent', 'document_type')

    def __str__(self):
        return f"{self.agent.user.name} - {self.document_type}"
