"""
<PERSON><PERSON><PERSON> to create sample restaurants with complete menu data
Run with: python manage.py shell < create_restaurants.py
"""
from restaurant.models import Restaurant, MenuCategory, MenuItem
from django.core.files.base import ContentFile
from PIL import Image
import io

def create_image(width, height, color, text=""):
    """Create a simple colored image with optional text"""
    img = Image.new('RGB', (width, height), color=color)
    if text:
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(img)
        try:
            font = ImageFont.load_default()
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            draw.text((x, y), text, fill='white', font=font)
        except:
            pass
    
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG', quality=90)
    img_bytes.seek(0)
    return img_bytes.getvalue()

def create_restaurant_with_menu(restaurant_data, categories_data, menu_items_data):
    """Create a restaurant with complete menu structure"""
    
    # Create restaurant
    restaurant = Restaurant.objects.create(
        name=restaurant_data['name'],
        description=restaurant_data['description'],
        contact_number=restaurant_data['contact_number'],
        opening_time=restaurant_data['opening_time'],
        closing_time=restaurant_data['closing_time'],
        delivery_fee=restaurant_data['delivery_fee'],
        min_order_amount=restaurant_data['min_order_amount'],
        average_preparation_time=restaurant_data['average_preparation_time'],
        is_active=True,
        is_verified=True,
        rating=restaurant_data['rating'],
        accepts_cash=True,
        accepts_card=True,
        accepts_online_payment=True,
        address_detail={
            "street": restaurant_data['address']['street'],
            "city": restaurant_data['address']['city'],
            "state": restaurant_data['address']['state'],
            "postal_code": restaurant_data['address']['postal_code'],
            "country": restaurant_data['address']['country'],
            "coordinates": restaurant_data['address']['coordinates']
        }
    )
    
    # Add logo and banner
    logo_img = create_image(200, 200, restaurant_data['logo_color'], restaurant_data['name'][:4])
    banner_img = create_image(800, 400, restaurant_data['banner_color'], restaurant_data['name'])
    
    restaurant.logo.save(
        f"{restaurant_data['name'].lower().replace(' ', '_')}_logo.jpg",
        ContentFile(logo_img),
        save=False
    )
    restaurant.banner.save(
        f"{restaurant_data['name'].lower().replace(' ', '_')}_banner.jpg", 
        ContentFile(banner_img),
        save=False
    )
    restaurant.save()
    
    print(f"✅ Created restaurant: {restaurant.name}")
    
    # Create menu categories
    created_categories = {}
    for cat_data in categories_data:
        category = MenuCategory.objects.create(
            restaurant=restaurant,
            name=cat_data['name'],
            description=cat_data['description'],
            is_active=True
        )
        created_categories[cat_data['name']] = category
        print(f"  📂 Created category: {category.name}")
    
    # Create menu items
    for item_data in menu_items_data:
        category = created_categories[item_data['category']]
        
        menu_item = MenuItem.objects.create(
            category=category,
            name=item_data['name'],
            description=item_data['description'],
            price=item_data['price'],
            preparation_time=item_data['preparation_time'],
            is_vegetarian=item_data['is_vegetarian'],
            is_available=True
        )
        
        # Add item image if specified
        if 'image_color' in item_data:
            item_img = create_image(400, 300, item_data['image_color'], item_data['name'][:8])
            menu_item.image.save(
                f"{item_data['name'].lower().replace(' ', '_')}.jpg",
                ContentFile(item_img),
                save=False
            )
            menu_item.save()
        
        print(f"    🍽️ Created menu item: {menu_item.name} - ${menu_item.price}")
    
    return restaurant

print("🏪 Creating sample restaurants with complete menu data...")

# Restaurant 1: Kabul Palace
kabul_palace_data = {
    'name': 'Kabul Palace',
    'description': 'Authentic Afghan cuisine in the heart of the city. Experience traditional flavors with modern presentation.',
    'contact_number': '+93 70 123 4567',
    'opening_time': '10:00:00',
    'closing_time': '23:00:00',
    'delivery_fee': '5.99',
    'min_order_amount': '15.00',
    'average_preparation_time': 35,
    'rating': '4.7',
    'logo_color': '#8B4513',
    'banner_color': '#D2691E',
    'address': {
        'street': '123 Afghan Street',
        'city': 'Kabul',
        'state': 'Kabul Province',
        'postal_code': '1001',
        'country': 'Afghanistan',
        'coordinates': {'lat': 34.5553, 'lng': 69.2075}
    }
}

kabul_categories = [
    {'name': 'Appetizers', 'description': 'Traditional Afghan starters'},
    {'name': 'Main Dishes', 'description': 'Hearty Afghan main courses'},
    {'name': 'Rice Dishes', 'description': 'Aromatic rice specialties'},
    {'name': 'Beverages', 'description': 'Traditional and modern drinks'},
    {'name': 'Desserts', 'description': 'Sweet Afghan treats'}
]

kabul_menu_items = [
    # Appetizers
    {'category': 'Appetizers', 'name': 'Mantu', 'description': 'Steamed dumplings filled with seasoned ground beef, topped with yogurt and lentil sauce', 'price': '12.99', 'preparation_time': 25, 'is_vegetarian': False, 'image_color': '#FFE4B5'},
    {'category': 'Appetizers', 'name': 'Bolani', 'description': 'Crispy flatbread stuffed with potatoes and herbs, served with chutney', 'price': '8.99', 'preparation_time': 15, 'is_vegetarian': True, 'image_color': '#DEB887'},
    {'category': 'Appetizers', 'name': 'Ashak', 'description': 'Leek-filled dumplings topped with meat sauce and yogurt', 'price': '13.99', 'preparation_time': 30, 'is_vegetarian': False, 'image_color': '#F0E68C'},
    
    # Main Dishes
    {'category': 'Main Dishes', 'name': 'Kabuli Pulao', 'description': 'Traditional Afghan rice dish with tender lamb, carrots, and raisins', 'price': '18.99', 'preparation_time': 45, 'is_vegetarian': False, 'image_color': '#CD853F'},
    {'category': 'Main Dishes', 'name': 'Lamb Karahi', 'description': 'Tender lamb cooked with tomatoes, onions, and aromatic spices', 'price': '22.99', 'preparation_time': 40, 'is_vegetarian': False, 'image_color': '#A0522D'},
    {'category': 'Main Dishes', 'name': 'Chicken Tikka', 'description': 'Marinated chicken grilled to perfection with Afghan spices', 'price': '16.99', 'preparation_time': 35, 'is_vegetarian': False, 'image_color': '#D2691E'},
    
    # Rice Dishes
    {'category': 'Rice Dishes', 'name': 'Qabili Palau', 'description': 'Fragrant basmati rice with lamb, carrots, and almonds', 'price': '19.99', 'preparation_time': 50, 'is_vegetarian': False, 'image_color': '#DAA520'},
    {'category': 'Rice Dishes', 'name': 'Vegetable Biryani', 'description': 'Aromatic rice with mixed vegetables and traditional spices', 'price': '14.99', 'preparation_time': 35, 'is_vegetarian': True, 'image_color': '#228B22'},
    
    # Beverages
    {'category': 'Beverages', 'name': 'Afghan Tea', 'description': 'Traditional green tea with cardamom and sugar', 'price': '3.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#8FBC8F'},
    {'category': 'Beverages', 'name': 'Doogh', 'description': 'Refreshing yogurt drink with mint and cucumber', 'price': '4.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#F0FFFF'},
    
    # Desserts
    {'category': 'Desserts', 'name': 'Firni', 'description': 'Creamy rice pudding with cardamom and pistachios', 'price': '6.99', 'preparation_time': 10, 'is_vegetarian': True, 'image_color': '#FFF8DC'},
    {'category': 'Desserts', 'name': 'Baklava', 'description': 'Layers of phyllo pastry with nuts and honey syrup', 'price': '7.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#F4A460'}
]

create_restaurant_with_menu(kabul_palace_data, kabul_categories, kabul_menu_items)

print("\n" + "="*50)
print("✅ Kabul Palace created successfully!")
print("🎯 Restaurant has 5 categories and 12 menu items")
print("="*50)
