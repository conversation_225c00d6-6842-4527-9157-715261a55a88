import React, { useState, useEffect } from "react";
import {
  Package,
  Clock,
  MapPin,
  CheckCircle,
  RefreshCw,
  Search,
  Filter,
} from "lucide-react";
import Button from "../../components/common/Button";
import Card from "../../components/common/Card";
import Badge from "../../components/common/Badge";
import Input from "../../components/common/Input";
import { deliveryAgentApi } from "../../services/deliveryAgentApi";
import { useAuth } from "../../context/AuthContext";

function SimpleOrders() {
  const { user } = useAuth();

  // State management
  const [availableOrders, setAvailableOrders] = useState([]);
  const [myOrders, setMyOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("available");
  const [searchQuery, setSearchQuery] = useState("");
  const [refreshing, setRefreshing] = useState(false);

  // Load orders data
  const loadOrders = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load available orders
      const availableResult = await deliveryAgentApi.getAvailableOrders();

      if (
        availableResult.success &&
        availableResult.data.status === "success"
      ) {
        // Available orders API returns {status: "success", data: {orders: [...]}}
        setAvailableOrders(availableResult.data.data?.orders || []);
      } else {
        // No available orders or agent not available
        setAvailableOrders([]);
      }

      // Load my orders
      const myOrdersResult = await deliveryAgentApi.getMyOrders();

      if (myOrdersResult.success && myOrdersResult.data.status === "success") {
        // My orders API returns {status: "success", data: {orders: [...], total_count: 1, agent_info: {...}}}
        const ordersData = myOrdersResult.data.data || {};
        setMyOrders(ordersData.orders || []);
      } else {
        console.error(
          "Failed to load my orders:",
          myOrdersResult.error || myOrdersResult.data?.message
        );
        setMyOrders([]);
      }
    } catch (err) {
      console.error("Orders load error:", err);
      setError("Failed to load orders");
    } finally {
      setLoading(false);
    }
  };

  // Accept order
  const acceptOrder = async (orderId) => {
    try {
      const result = await deliveryAgentApi.acceptOrder(orderId);
      if (result.success) {
        alert("Order accepted successfully!");
        await loadOrders(); // Refresh orders
      } else {
        alert(
          "Failed to accept order: " +
            (result.error?.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Accept order error:", error);
      alert("Error accepting order");
    }
  };

  // Update order status
  const updateOrderStatus = async (orderId, status) => {
    try {
      const result = await deliveryAgentApi.updateOrderStatus(orderId, status);
      if (result.success) {
        alert(`Order status updated to ${status}!`);
        await loadOrders(); // Refresh orders
      } else {
        alert(
          "Failed to update order status: " +
            (result.error?.message || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Update order status error:", error);
      alert("Error updating order status");
    }
  };

  // Refresh orders
  const refreshOrders = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  // Load orders on component mount
  useEffect(() => {
    loadOrders();
  }, []);

  // Filter orders based on search query (with safety checks)
  const filteredAvailableOrders = Array.isArray(availableOrders)
    ? availableOrders.filter(
        (order) =>
          order.restaurant?.name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          order.customer?.name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          order.delivery_address
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase())
      )
    : [];

  const filteredMyOrders = Array.isArray(myOrders)
    ? myOrders.filter(
        (order) =>
          order.restaurant?.name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          order.customer?.name
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          order.delivery_address
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase())
      )
    : [];

  if (loading) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
          <p className='text-gray-600'>Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Orders</h1>
          <p className='text-gray-600'>Manage your delivery orders</p>
        </div>
        <Button
          onClick={refreshOrders}
          disabled={refreshing}
          variant='outline'
          className='mt-4 sm:mt-0 flex items-center space-x-2'
        >
          <RefreshCw
            className={`h-4 w-4 ${refreshing ? "animate-spin" : ""}`}
          />
          <span>Refresh</span>
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <Card className='p-4 bg-red-50 border-red-200'>
          <div className='flex items-center space-x-2'>
            <div className='text-red-600'>⚠️</div>
            <div>
              <h3 className='font-medium text-red-900'>Error</h3>
              <p className='text-red-700'>{error}</p>
            </div>
          </div>
        </Card>
      )}

      {/* Search and Filter */}
      <Card className='p-4'>
        <div className='flex flex-col sm:flex-row gap-4'>
          <div className='flex-1'>
            <Input
              type='text'
              placeholder='Search orders by restaurant, customer, or address...'
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className='w-full'
              icon={<Search />}
            />
          </div>
        </div>
      </Card>

      {/* Tabs */}
      <div className='border-b border-gray-200'>
        <nav className='-mb-px flex space-x-8'>
          <button
            onClick={() => setActiveTab("available")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "available"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Available Orders ({filteredAvailableOrders.length})
          </button>
          <button
            onClick={() => setActiveTab("my-orders")}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === "my-orders"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            My Orders ({filteredMyOrders.length})
          </button>
        </nav>
      </div>

      {/* Orders List */}
      <div className='space-y-4'>
        {activeTab === "available" ? (
          // Available Orders
          filteredAvailableOrders.length > 0 ? (
            filteredAvailableOrders.map((order) => (
              <Card key={order.id} className='p-6'>
                <div className='flex justify-between items-start mb-4'>
                  <div>
                    <h3 className='text-lg font-semibold text-gray-900'>
                      {order.restaurant_name || "Unknown Restaurant"}
                    </h3>
                    <p className='text-gray-600'>
                      Customer: {order.customer_name || "Unknown Customer"}
                    </p>
                  </div>
                  <div className='text-right'>
                    <p className='text-2xl font-bold text-green-600'>
                      ${order.total_amount?.toFixed(2) || "0.00"}
                    </p>
                    <Badge variant='info'>Available</Badge>
                  </div>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
                  <div className='flex items-center space-x-2'>
                    <MapPin className='h-4 w-4 text-gray-400' />
                    <span className='text-sm text-gray-600'>
                      {order.delivery_address || "No address provided"}
                    </span>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <Clock className='h-4 w-4 text-gray-400' />
                    <span className='text-sm text-gray-600'>
                      {new Date(order.created_at).toLocaleString()}
                    </span>
                  </div>
                </div>

                <div className='flex justify-end'>
                  <Button
                    onClick={() => acceptOrder(order.id)}
                    className='flex items-center space-x-2'
                  >
                    <CheckCircle className='h-4 w-4' />
                    <span>Accept Order</span>
                  </Button>
                </div>
              </Card>
            ))
          ) : (
            <Card className='p-8 text-center'>
              <Package className='h-16 w-16 text-gray-400 mx-auto mb-4' />
              <h3 className='text-lg font-medium text-gray-900 mb-2'>
                No Available Orders
              </h3>
              <p className='text-gray-600'>
                There are no orders available for delivery at the moment.
              </p>
            </Card>
          )
        ) : // My Orders
        filteredMyOrders.length > 0 ? (
          filteredMyOrders.map((order) => (
            <Card key={order.id} className='p-6'>
              <div className='flex justify-between items-start mb-4'>
                <div>
                  <h3 className='text-lg font-semibold text-gray-900'>
                    {order.restaurant_name || "Unknown Restaurant"}
                  </h3>
                  <p className='text-gray-600'>
                    Customer: {order.customer_name || "Unknown Customer"}
                  </p>
                </div>
                <div className='text-right'>
                  <p className='text-2xl font-bold text-green-600'>
                    ${order.total_amount?.toFixed(2) || "0.00"}
                  </p>
                  <Badge
                    variant={
                      order.status === "delivered"
                        ? "success"
                        : order.status === "on_the_way"
                        ? "info"
                        : order.status === "picked_up"
                        ? "warning"
                        : "secondary"
                    }
                  >
                    {order.status?.replace("_", " ") || "Unknown"}
                  </Badge>
                </div>
              </div>

              <div className='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
                <div className='flex items-center space-x-2'>
                  <MapPin className='h-4 w-4 text-gray-400' />
                  <span className='text-sm text-gray-600'>
                    {order.delivery_address || "No address provided"}
                  </span>
                </div>
                <div className='flex items-center space-x-2'>
                  <Clock className='h-4 w-4 text-gray-400' />
                  <span className='text-sm text-gray-600'>
                    {new Date(order.created_at).toLocaleString()}
                  </span>
                </div>
              </div>

              {order.status !== "delivered" && order.status !== "completed" && (
                <div className='flex justify-end space-x-2'>
                  {order.status === "assigned" && (
                    <Button
                      onClick={() => updateOrderStatus(order.id, "accepted")}
                      variant='outline'
                    >
                      Accept Order
                    </Button>
                  )}
                  {order.status === "accepted" && (
                    <Button
                      onClick={() =>
                        updateOrderStatus(order.id, "en_route_to_restaurant")
                      }
                      variant='outline'
                    >
                      Head to Restaurant
                    </Button>
                  )}
                  {order.status === "en_route_to_restaurant" && (
                    <Button
                      onClick={() =>
                        updateOrderStatus(order.id, "arrived_at_restaurant")
                      }
                      variant='outline'
                    >
                      Arrived at Restaurant
                    </Button>
                  )}
                  {order.status === "arrived_at_restaurant" && (
                    <Button
                      onClick={() => updateOrderStatus(order.id, "picked_up")}
                      variant='outline'
                    >
                      Mark Picked Up
                    </Button>
                  )}
                  {order.status === "picked_up" && (
                    <Button
                      onClick={() =>
                        updateOrderStatus(order.id, "en_route_to_customer")
                      }
                      variant='outline'
                    >
                      Head to Customer
                    </Button>
                  )}
                  {(order.status === "en_route_to_customer" ||
                    order.status === "arrived_at_customer") && (
                    <Button
                      onClick={() => updateOrderStatus(order.id, "delivered")}
                    >
                      Mark Delivered
                    </Button>
                  )}
                  {order.status === "delivered" && (
                    <Button
                      onClick={() => updateOrderStatus(order.id, "completed")}
                    >
                      Complete Order
                    </Button>
                  )}
                </div>
              )}
            </Card>
          ))
        ) : (
          <Card className='p-8 text-center'>
            <CheckCircle className='h-16 w-16 text-gray-400 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>
              No Orders Yet
            </h3>
            <p className='text-gray-600'>
              You haven't accepted any orders yet. Check the available orders
              tab.
            </p>
          </Card>
        )}
      </div>
    </div>
  );
}

export default SimpleOrders;
