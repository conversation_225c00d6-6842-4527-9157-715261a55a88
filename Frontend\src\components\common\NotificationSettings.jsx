import React, { useState } from "react";
import {
  Bell,
  Volume2,
  VolumeX,
  Monitor,
  Smartphone,
  Settings,
  Check,
  X,
} from "lucide-react";
import { useNotifications } from "../../context/NotificationContext";
import Card from "./Card";
import Button from "./Button";

const NotificationSettings = ({ className = "" }) => {
  const {
    soundEnabled,
    browserNotificationsEnabled,
    toggleSound,
    toggleBrowserNotifications,
    playNotificationSound,
    showBrowserNotification,
  } = useNotifications();

  const [showTestNotification, setShowTestNotification] = useState(false);

  const handleTestSound = () => {
    playNotificationSound();
    setShowTestNotification(true);
    setTimeout(() => setShowTestNotification(false), 2000);
  };

  const handleTestBrowserNotification = () => {
    showBrowserNotification(
      "Test Notification",
      "This is a test notification from Afghan Sofra",
      "test"
    );
    setShowTestNotification(true);
    setTimeout(() => setShowTestNotification(false), 2000);
  };

  return (
    <Card className={className}>
      <div className="p-6">
        <div className="flex items-center mb-6">
          <Settings size={24} className="text-gray-600 mr-3" />
          <h3 className="text-lg font-semibold">Notification Settings</h3>
        </div>

        <div className="space-y-6">
          {/* Sound Notifications */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {soundEnabled ? (
                <Volume2 size={20} className="text-green-500" />
              ) : (
                <VolumeX size={20} className="text-gray-400" />
              )}
              <div>
                <h4 className="font-medium">Sound Notifications</h4>
                <p className="text-sm text-gray-500">
                  Play sound when receiving notifications
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="small"
                onClick={handleTestSound}
                disabled={!soundEnabled}
              >
                Test
              </Button>
              <button
                onClick={toggleSound}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  soundEnabled ? "bg-orange-500" : "bg-gray-300"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    soundEnabled ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Browser Notifications */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {browserNotificationsEnabled ? (
                <Monitor size={20} className="text-blue-500" />
              ) : (
                <Monitor size={20} className="text-gray-400" />
              )}
              <div>
                <h4 className="font-medium">Browser Notifications</h4>
                <p className="text-sm text-gray-500">
                  Show desktop notifications
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="small"
                onClick={handleTestBrowserNotification}
                disabled={!browserNotificationsEnabled}
              >
                Test
              </Button>
              <button
                onClick={toggleBrowserNotifications}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  browserNotificationsEnabled ? "bg-blue-500" : "bg-gray-300"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    browserNotificationsEnabled ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Browser notification permission status */}
          {!browserNotificationsEnabled && "Notification" in window && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <Bell size={20} className="text-yellow-600 mr-3 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-800">
                    Enable Browser Notifications
                  </h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    To receive desktop notifications, please allow notifications
                    in your browser settings.
                  </p>
                  <Button
                    variant="outline"
                    size="small"
                    className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                    onClick={toggleBrowserNotifications}
                  >
                    Enable Notifications
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Test notification feedback */}
          {showTestNotification && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center">
                <Check size={20} className="text-green-600 mr-3" />
                <div>
                  <h4 className="font-medium text-green-800">
                    Test Notification Sent
                  </h4>
                  <p className="text-sm text-green-700">
                    Check if you received the notification
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Notification Types */}
          <div className="border-t border-gray-100 pt-6">
            <h4 className="font-medium mb-4">Notification Types</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Bell size={16} className="text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium">New Orders</p>
                    <p className="text-sm text-gray-500">
                      When you receive new orders
                    </p>
                  </div>
                </div>
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <Check size={14} className="text-white" />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <Bell size={16} className="text-orange-600" />
                  </div>
                  <div>
                    <p className="font-medium">Order Status Updates</p>
                    <p className="text-sm text-gray-500">
                      When order status changes
                    </p>
                  </div>
                </div>
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <Check size={14} className="text-white" />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                    <Bell size={16} className="text-purple-600" />
                  </div>
                  <div>
                    <p className="font-medium">Delivery Updates</p>
                    <p className="text-sm text-gray-500">
                      When delivery agent is assigned
                    </p>
                  </div>
                </div>
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <Check size={14} className="text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Browser Support Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-start">
              <Smartphone size={20} className="text-gray-600 mr-3 mt-0.5" />
              <div>
                <h4 className="font-medium text-gray-800">
                  Mobile App Coming Soon
                </h4>
                <p className="text-sm text-gray-600 mt-1">
                  Get push notifications on your mobile device with our upcoming
                  mobile app for iOS and Android.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default NotificationSettings;
