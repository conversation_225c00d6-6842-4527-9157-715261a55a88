import React, { useState, useEffect } from 'react';
import {
  Users,
  Package,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  TrendingUp,
  MapPin,
  User,
  Activity
} from 'lucide-react';
import Card from '../../components/common/Card';
import Button from '../../components/common/Button';
import Badge from '../../components/common/Badge';
import { orderApi } from '../../utils/orderApi';

const DeliveryAssignments = () => {
  const [stats, setStats] = useState(null);
  const [readyOrders, setReadyOrders] = useState([]);
  const [assignedOrders, setAssignedOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load assignment statistics
      const statsResponse = await orderApi.getAssignmentStats();
      if (statsResponse.success) {
        setStats(statsResponse.data);
      }

      // Load ready orders
      const readyResponse = await orderApi.getOrders({ status: 'ready' });
      if (readyResponse.success) {
        setReadyOrders(readyResponse.data.results || readyResponse.data);
      }

      // Load assigned orders
      const assignedResponse = await orderApi.getOrders({ status: 'assigned' });
      if (assignedResponse.success) {
        setAssignedOrders(assignedResponse.data.results || assignedResponse.data);
      }

    } catch (err) {
      console.error('Error loading assignment data:', err);
      setError('Failed to load assignment data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleAutoAssign = async (orderId = null) => {
    try {
      const response = await orderApi.autoAssignOrder(orderId);
      if (response.success) {
        // Refresh data after assignment
        await loadData();
      } else {
        setError(response.error || 'Assignment failed');
      }
    } catch (err) {
      console.error('Error in auto assignment:', err);
      setError('Failed to assign orders');
    }
  };

  const formatDateTime = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading assignment data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Delivery Assignments</h1>
              <p className="text-gray-600 mt-1">Monitor and manage delivery agent assignments</p>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleRefresh}
                loading={refreshing}
                icon={<RefreshCw size={16} />}
              >
                {refreshing ? 'Refreshing...' : 'Refresh'}
              </Button>
              <Button
                variant="primary"
                onClick={() => handleAutoAssign()}
                icon={<Users size={16} />}
              >
                Auto Assign All
              </Button>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <AlertCircle size={20} className="text-red-500 mr-2" />
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        )}

        {/* Statistics Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Package className="h-8 w-8 text-orange-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Ready for Assignment</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats.orders_ready_for_assignment || 0}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CheckCircle className="h-8 w-8 text-green-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Currently Assigned</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats.orders_currently_assigned || 0}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Users className="h-8 w-8 text-blue-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Available Agents</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats.available_agents || 0}
                    </p>
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Activity className="h-8 w-8 text-purple-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Assignment Rate</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {stats.available_agents > 0 ? '95%' : '0%'}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Ready Orders */}
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Orders Ready for Assignment</h2>
                <Badge variant="warning" className="bg-orange-100 text-orange-800">
                  {readyOrders.length} Orders
                </Badge>
              </div>

              <div className="space-y-4">
                {readyOrders.length === 0 ? (
                  <div className="text-center py-8">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No orders ready for assignment</p>
                  </div>
                ) : (
                  readyOrders.map((order) => (
                    <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-gray-900">Order #{order.id}</h3>
                          <p className="text-sm text-gray-600">
                            {order.restaurant?.name || 'Unknown Restaurant'}
                          </p>
                          <p className="text-sm text-gray-500">
                            Created: {formatDateTime(order.created_at)}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">${order.total_amount}</p>
                          <Button
                            size="sm"
                            onClick={() => handleAutoAssign(order.id)}
                            icon={<User size={14} />}
                          >
                            Assign
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </Card>

          {/* Assigned Orders */}
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Recently Assigned Orders</h2>
                <Badge variant="success" className="bg-green-100 text-green-800">
                  {assignedOrders.length} Assigned
                </Badge>
              </div>

              <div className="space-y-4">
                {assignedOrders.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No assigned orders</p>
                  </div>
                ) : (
                  assignedOrders.map((order) => (
                    <div key={order.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-gray-900">Order #{order.id}</h3>
                          <p className="text-sm text-gray-600">
                            Agent: {order.delivery_agent?.email || 'Unknown Agent'}
                          </p>
                          <p className="text-sm text-gray-500">
                            Assigned: {formatDateTime(order.updated_at)}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-gray-900">${order.total_amount}</p>
                          <Badge variant="success">Assigned</Badge>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DeliveryAssignments;
