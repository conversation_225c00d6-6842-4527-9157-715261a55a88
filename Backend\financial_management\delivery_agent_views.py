"""
Delivery Agent Financial Management API Views

This module provides API endpoints for delivery agent financial operations:
- Earnings tracking
- Payout requests
- Financial summaries
- Performance analytics
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Sum, Avg, Count, Q
from django.utils import timezone
from datetime import timedelta, datetime
from decimal import Decimal

from .delivery_agent_payments import (
    DeliveryAgentCommission,
    DeliveryAgentEarnings,
    DeliveryAgentPayout,
    DeliveryAgentFinancialSummary
)
from .serializers import (
    DeliveryAgentEarningsSerializer,
    DeliveryAgentPayoutSerializer,
    DeliveryAgentCommissionSerializer,
    PayoutRequestSerializer
)


class DeliveryAgentFinancialViewSet(viewsets.ModelViewSet):
    """ViewSet for delivery agent financial operations"""
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Filter queryset to current delivery agent"""
        if self.request.user.role == 'delivery_agent':
            return DeliveryAgentEarnings.objects.filter(
                delivery_agent=self.request.user
            )
        elif self.request.user.role == 'admin':
            return DeliveryAgentEarnings.objects.all()
        else:
            return DeliveryAgentEarnings.objects.none()
    
    def get_serializer_class(self):
        if self.action == 'request_payout':
            return PayoutRequestSerializer
        return DeliveryAgentEarningsSerializer
    
    @action(detail=False, methods=['get'])
    def earnings_summary(self, request):
        """Get earnings summary for the delivery agent"""
        agent = request.user
        
        if agent.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can access earnings'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get date range from query params
        period = request.GET.get('period', 'week')  # week, month, year
        
        if period == 'week':
            start_date = timezone.now() - timedelta(days=7)
        elif period == 'month':
            start_date = timezone.now() - timedelta(days=30)
        elif period == 'year':
            start_date = timezone.now() - timedelta(days=365)
        else:
            start_date = timezone.now() - timedelta(days=7)
        
        # Get earnings for the period
        earnings = DeliveryAgentEarnings.objects.filter(
            delivery_agent=agent,
            created_at__gte=start_date
        )
        
        # Calculate summary statistics
        summary = earnings.aggregate(
            total_earnings=Sum('net_earnings'),
            total_tips=Sum('tips'),
            total_deliveries=Count('id'),
            avg_earnings_per_delivery=Avg('net_earnings'),
            total_distance=Sum('delivery_distance_km'),
            avg_delivery_time=Avg('delivery_time_minutes')
        )
        
        # Calculate pending earnings (unpaid)
        pending_earnings = earnings.filter(is_paid=False).aggregate(
            pending_amount=Sum('net_earnings')
        )['pending_amount'] or Decimal('0.00')
        
        # Get commission structure
        try:
            commission = agent.delivery_commission
            commission_data = DeliveryAgentCommissionSerializer(commission).data
        except DeliveryAgentCommission.DoesNotExist:
            commission_data = None
        
        return Response({
            'period': period,
            'start_date': start_date.date(),
            'end_date': timezone.now().date(),
            'summary': {
                'total_earnings': summary['total_earnings'] or Decimal('0.00'),
                'total_tips': summary['total_tips'] or Decimal('0.00'),
                'total_deliveries': summary['total_deliveries'] or 0,
                'avg_earnings_per_delivery': summary['avg_earnings_per_delivery'] or Decimal('0.00'),
                'total_distance_km': summary['total_distance'] or Decimal('0.00'),
                'avg_delivery_time_minutes': summary['avg_delivery_time'] or 0,
                'pending_earnings': pending_earnings,
            },
            'commission_structure': commission_data,
        })
    
    @action(detail=False, methods=['get'])
    def earnings_history(self, request):
        """Get detailed earnings history"""
        agent = request.user
        
        if agent.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can access earnings'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get pagination parameters
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 20))
        
        # Get earnings with pagination
        earnings = DeliveryAgentEarnings.objects.filter(
            delivery_agent=agent
        ).order_by('-created_at')
        
        # Apply pagination
        start = (page - 1) * page_size
        end = start + page_size
        paginated_earnings = earnings[start:end]
        
        serializer = DeliveryAgentEarningsSerializer(paginated_earnings, many=True)
        
        return Response({
            'count': earnings.count(),
            'page': page,
            'page_size': page_size,
            'results': serializer.data
        })
    
    @action(detail=False, methods=['post'])
    def request_payout(self, request):
        """Request payout for accumulated earnings"""
        agent = request.user
        
        if agent.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can request payouts'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        serializer = PayoutRequestSerializer(data=request.data)
        if serializer.is_valid():
            # Check if agent has enough pending earnings
            pending_earnings = DeliveryAgentEarnings.objects.filter(
                delivery_agent=agent,
                is_paid=False
            ).aggregate(total=Sum('net_earnings'))['total'] or Decimal('0')
            
            requested_amount = serializer.validated_data['amount']
            
            if requested_amount > pending_earnings:
                return Response({
                    'error': 'Requested amount exceeds pending earnings',
                    'pending_earnings': pending_earnings
                }, status=400)
            
            # Check minimum payout amount
            try:
                commission = agent.delivery_commission
                if requested_amount < commission.minimum_payout_amount:
                    return Response({
                        'error': f'Minimum payout amount is ${commission.minimum_payout_amount}',
                        'minimum_amount': commission.minimum_payout_amount
                    }, status=400)
            except DeliveryAgentCommission.DoesNotExist:
                # Use default minimum
                if requested_amount < Decimal('25.00'):
                    return Response({
                        'error': 'Minimum payout amount is $25.00',
                        'minimum_amount': Decimal('25.00')
                    }, status=400)
            
            # Get earnings to include in payout
            earnings_to_pay = DeliveryAgentEarnings.objects.filter(
                delivery_agent=agent,
                is_paid=False
            ).order_by('created_at')
            
            # Calculate payout details
            total_amount = Decimal('0.00')
            total_tips = Decimal('0.00')
            total_commission = Decimal('0.00')
            deliveries_count = 0
            
            for earning in earnings_to_pay:
                if total_amount + earning.net_earnings <= requested_amount:
                    total_amount += earning.net_earnings
                    total_tips += earning.tips
                    total_commission += earning.platform_commission
                    deliveries_count += 1
                else:
                    break
            
            # Create payout request
            payout = DeliveryAgentPayout.objects.create(
                delivery_agent=agent,
                payout_amount=total_amount,
                payout_period_start=timezone.now() - timedelta(days=30),
                payout_period_end=timezone.now(),
                deliveries_count=deliveries_count,
                total_gross_earnings=total_amount + total_commission,
                total_commission=total_commission,
                total_tips=total_tips,
                payment_method=serializer.validated_data['payment_method'],
                status='pending'
            )
            
            # Calculate performance bonus
            payout.calculate_performance_bonus()
            
            return Response({
                'status': 'success',
                'payout_id': payout.id,
                'amount': payout.payout_amount,
                'deliveries_count': deliveries_count,
                'performance_bonus': payout.performance_bonus,
                'estimated_processing_time': '1-3 business days'
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def payout_history(self, request):
        """Get payout history for the delivery agent"""
        agent = request.user
        
        if agent.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can access payout history'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        payouts = DeliveryAgentPayout.objects.filter(
            delivery_agent=agent
        ).order_by('-created_at')
        
        serializer = DeliveryAgentPayoutSerializer(payouts, many=True)
        
        return Response({
            'count': payouts.count(),
            'results': serializer.data
        })
    
    @action(detail=False, methods=['post'])
    def add_tip(self, request):
        """Add tip to a specific delivery"""
        agent = request.user
        
        if agent.role != 'delivery_agent':
            return Response(
                {'error': 'Only delivery agents can add tips'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        order_id = request.data.get('order_id')
        tip_amount = request.data.get('tip_amount', 0)
        
        try:
            tip_amount = Decimal(str(tip_amount))
        except (ValueError, TypeError):
            return Response(
                {'error': 'Invalid tip amount'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            earnings = DeliveryAgentEarnings.objects.get(
                delivery_agent=agent,
                order_id=order_id
            )
            
            # Update tip amount
            earnings.tips = tip_amount
            earnings.gross_earnings = (
                earnings.base_fee + 
                earnings.distance_bonus + 
                earnings.time_bonus + 
                earnings.tips
            )
            earnings.net_earnings = earnings.gross_earnings - earnings.platform_commission
            earnings.save()
            
            return Response({
                'status': 'success',
                'order_id': order_id,
                'tip_amount': tip_amount,
                'new_net_earnings': earnings.net_earnings
            })
            
        except DeliveryAgentEarnings.DoesNotExist:
            return Response(
                {'error': 'Earnings record not found for this order'},
                status=status.HTTP_404_NOT_FOUND
            )
