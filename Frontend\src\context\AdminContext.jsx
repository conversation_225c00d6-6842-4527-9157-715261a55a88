import React, { createContext, useContext, useState, useEffect } from "react";
import { adminApi } from "../services/adminApi";

const AdminContext = createContext();

export const useAdmin = () => {
  const context = useContext(AdminContext);
  if (!context) {
    throw new Error("useAdmin must be used within an AdminProvider");
  }
  return context;
};

export const AdminProvider = ({ children }) => {
  const [pendingDeliveryApprovals, setPendingDeliveryApprovals] = useState(0);
  const [pendingRestaurantApprovals, setPendingRestaurantApprovals] =
    useState(0);
  const [loading, setLoading] = useState(false);

  // Load pending approvals count
  const loadPendingApprovals = async () => {
    try {
      setLoading(true);

      // Load delivery agent stats
      const deliveryStats = await adminApi.getDeliveryAgentStats();
      if (deliveryStats.success) {
        setPendingDeliveryApprovals(deliveryStats.data.pending);
      }

      // Load restaurant stats (if available)
      // const restaurantStats = await adminApi.getRestaurantStats();
      // if (restaurantStats.success) {
      //   setPendingRestaurantApprovals(restaurantStats.data.pending);
      // }
    } catch (error) {
      console.error("Error loading pending approvals:", error);
    } finally {
      setLoading(false);
    }
  };

  // Refresh approvals count
  const refreshApprovals = () => {
    loadPendingApprovals();
  };

  // Load on mount and set up periodic refresh
  useEffect(() => {
    loadPendingApprovals();

    // Refresh every 5 minutes
    const interval = setInterval(loadPendingApprovals, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  const value = {
    pendingDeliveryApprovals,
    pendingRestaurantApprovals,
    loading,
    refreshApprovals,
    loadPendingApprovals,
  };

  return (
    <AdminContext.Provider value={value}>{children}</AdminContext.Provider>
  );
};

export default AdminContext;
