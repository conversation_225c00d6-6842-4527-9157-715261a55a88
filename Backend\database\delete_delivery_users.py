#!/usr/bin/env python3
"""
Delete Delivery Users Script
Safely removes delivery agent users from the database
"""

import os
import sys
import django
from pathlib import Path

# Add the Backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.append(str(backend_dir))

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghanSufra.settings')
django.setup()

from django.contrib.auth import get_user_model
from deliveryAgent.afghan_agent_models import AfghanDeliveryAgent, AgentTraining, AgentPayment
from deliveryAgent.models import DeliveryAgentProfile
from django.db import transaction

User = get_user_model()

def list_delivery_users():
    """List all delivery agent users"""
    print("🔍 Finding delivery agent users...")
    
    # Find users with delivery_agent role
    delivery_users = User.objects.filter(role='delivery_agent')
    
    # Find users associated with Afghan delivery agents
    afghan_agents = AfghanDeliveryAgent.objects.all()
    afghan_user_ids = [agent.user.id for agent in afghan_agents if agent.user]
    
    # Find users associated with regular delivery agent profiles
    regular_agents = DeliveryAgentProfile.objects.all()
    regular_user_ids = [agent.user.id for agent in regular_agents if agent.user]
    
    # Combine all delivery user IDs
    all_delivery_user_ids = set()
    all_delivery_user_ids.update([user.id for user in delivery_users])
    all_delivery_user_ids.update(afghan_user_ids)
    all_delivery_user_ids.update(regular_user_ids)
    
    # Get all delivery users
    all_delivery_users = User.objects.filter(id__in=all_delivery_user_ids)
    
    print(f"📊 Found {len(all_delivery_users)} delivery agent users:")
    print("-" * 60)
    
    for user in all_delivery_users:
        print(f"ID: {user.id}")
        print(f"Username: {user.user_name}")
        print(f"Name: {user.name}")
        print(f"Phone: {user.phone}")
        print(f"Role: {user.role}")
        print(f"Email: {user.email}")
        
        # Check associated profiles
        try:
            afghan_agent = AfghanDeliveryAgent.objects.get(user=user)
            print(f"Afghan Agent: {afghan_agent.full_name_dari} ({afghan_agent.agent_code})")
        except AfghanDeliveryAgent.DoesNotExist:
            pass
        
        try:
            regular_agent = DeliveryAgentProfile.objects.get(user=user)
            print(f"Regular Agent: {regular_agent.full_name}")
        except DeliveryAgentProfile.DoesNotExist:
            pass
        
        print("-" * 60)
    
    return all_delivery_users

def delete_delivery_users_safe():
    """Safely delete delivery users and related data"""
    print("🗑️  Starting safe deletion of delivery users...")
    
    try:
        with transaction.atomic():
            # Get all delivery users
            delivery_users = list_delivery_users()
            
            if not delivery_users:
                print("✅ No delivery users found to delete.")
                return True
            
            # Confirm deletion
            print(f"\n⚠️  WARNING: This will delete {len(delivery_users)} delivery users and ALL related data!")
            print("This includes:")
            print("- User accounts")
            print("- Afghan delivery agent profiles")
            print("- Regular delivery agent profiles") 
            print("- Training records")
            print("- Payment records")
            print("- Order assignments (will be set to NULL)")
            
            confirm = input("\nType 'DELETE' to confirm deletion: ")
            
            if confirm != 'DELETE':
                print("❌ Deletion cancelled.")
                return False
            
            # Delete related data first
            deleted_counts = {}
            
            # Delete Afghan agent training records
            training_count = 0
            for user in delivery_users:
                try:
                    agent = AfghanDeliveryAgent.objects.get(user=user)
                    training_records = AgentTraining.objects.filter(agent=agent)
                    training_count += training_records.count()
                    training_records.delete()
                except AfghanDeliveryAgent.DoesNotExist:
                    pass
            deleted_counts['training_records'] = training_count
            
            # Delete Afghan agent payment records
            payment_count = 0
            for user in delivery_users:
                try:
                    agent = AfghanDeliveryAgent.objects.get(user=user)
                    payment_records = AgentPayment.objects.filter(agent=agent)
                    payment_count += payment_records.count()
                    payment_records.delete()
                except AfghanDeliveryAgent.DoesNotExist:
                    pass
            deleted_counts['payment_records'] = payment_count
            
            # Delete Afghan delivery agent profiles
            afghan_agents = AfghanDeliveryAgent.objects.filter(user__in=delivery_users)
            afghan_count = afghan_agents.count()
            afghan_agents.delete()
            deleted_counts['afghan_agents'] = afghan_count
            
            # Delete regular delivery agent profiles
            regular_agents = DeliveryAgentProfile.objects.filter(user__in=delivery_users)
            regular_count = regular_agents.count()
            regular_agents.delete()
            deleted_counts['regular_agents'] = regular_count
            
            # Update orders to remove delivery agent assignments
            from orders.models import Order
            orders_updated = Order.objects.filter(delivery_agent__in=delivery_users).update(delivery_agent=None)
            deleted_counts['orders_updated'] = orders_updated
            
            # Finally delete the users
            user_count = delivery_users.count()
            delivery_users.delete()
            deleted_counts['users'] = user_count
            
            # Print deletion summary
            print("\n✅ Deletion completed successfully!")
            print("📊 Deletion Summary:")
            print(f"   • Users deleted: {deleted_counts['users']}")
            print(f"   • Afghan agents deleted: {deleted_counts['afghan_agents']}")
            print(f"   • Regular agents deleted: {deleted_counts['regular_agents']}")
            print(f"   • Training records deleted: {deleted_counts['training_records']}")
            print(f"   • Payment records deleted: {deleted_counts['payment_records']}")
            print(f"   • Orders updated (delivery_agent set to NULL): {deleted_counts['orders_updated']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error during deletion: {e}")
        return False

def delete_specific_user():
    """Delete a specific delivery user by ID or username"""
    print("🎯 Delete specific delivery user")
    print("-" * 40)
    
    # List all delivery users first
    delivery_users = list_delivery_users()
    
    if not delivery_users:
        print("No delivery users found.")
        return
    
    # Get user input
    identifier = input("\nEnter user ID or username to delete: ").strip()
    
    try:
        # Try to find by ID first
        if identifier.isdigit():
            user = User.objects.get(id=int(identifier), role='delivery_agent')
        else:
            user = User.objects.get(user_name=identifier, role='delivery_agent')
        
        print(f"\n📋 User to delete:")
        print(f"ID: {user.id}")
        print(f"Username: {user.user_name}")
        print(f"Name: {user.name}")
        print(f"Phone: {user.phone}")
        
        confirm = input(f"\nConfirm deletion of user '{user.user_name}'? (y/N): ")
        
        if confirm.lower() == 'y':
            with transaction.atomic():
                # Delete related data
                try:
                    agent = AfghanDeliveryAgent.objects.get(user=user)
                    AgentTraining.objects.filter(agent=agent).delete()
                    AgentPayment.objects.filter(agent=agent).delete()
                    agent.delete()
                    print(f"✅ Deleted Afghan agent profile for {user.name}")
                except AfghanDeliveryAgent.DoesNotExist:
                    pass
                
                try:
                    agent = DeliveryAgentProfile.objects.get(user=user)
                    agent.delete()
                    print(f"✅ Deleted regular agent profile for {user.name}")
                except DeliveryAgentProfile.DoesNotExist:
                    pass
                
                # Update orders
                from orders.models import Order
                orders_updated = Order.objects.filter(delivery_agent=user).update(delivery_agent=None)
                if orders_updated > 0:
                    print(f"✅ Updated {orders_updated} orders")
                
                # Delete user
                user.delete()
                print(f"✅ User '{user.user_name}' deleted successfully!")
        else:
            print("❌ Deletion cancelled.")
            
    except User.DoesNotExist:
        print(f"❌ User '{identifier}' not found or not a delivery agent.")
    except Exception as e:
        print(f"❌ Error: {e}")

def delete_by_role_only():
    """Delete users with delivery_agent role only (keep profiles)"""
    print("🔄 Deleting users with 'delivery_agent' role only...")
    
    delivery_role_users = User.objects.filter(role='delivery_agent')
    
    if not delivery_role_users:
        print("✅ No users with 'delivery_agent' role found.")
        return
    
    print(f"📊 Found {delivery_role_users.count()} users with 'delivery_agent' role:")
    for user in delivery_role_users:
        print(f"   • {user.user_name} ({user.name})")
    
    confirm = input(f"\nDelete {delivery_role_users.count()} users with 'delivery_agent' role? (y/N): ")
    
    if confirm.lower() == 'y':
        try:
            with transaction.atomic():
                # Update orders first
                from orders.models import Order
                orders_updated = Order.objects.filter(delivery_agent__in=delivery_role_users).update(delivery_agent=None)
                
                # Delete users
                count = delivery_role_users.count()
                delivery_role_users.delete()
                
                print(f"✅ Deleted {count} users with 'delivery_agent' role")
                if orders_updated > 0:
                    print(f"✅ Updated {orders_updated} orders")
                    
        except Exception as e:
            print(f"❌ Error: {e}")
    else:
        print("❌ Deletion cancelled.")

def main():
    """Main function with menu"""
    print("🗑️  Delivery Users Deletion Tool")
    print("=" * 50)
    
    while True:
        print("\nSelect an option:")
        print("1. List all delivery users")
        print("2. Delete ALL delivery users and related data (DANGEROUS)")
        print("3. Delete specific user by ID/username")
        print("4. Delete users with 'delivery_agent' role only")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            list_delivery_users()
        elif choice == '2':
            delete_delivery_users_safe()
        elif choice == '3':
            delete_specific_user()
        elif choice == '4':
            delete_by_role_only()
        elif choice == '5':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-5.")

if __name__ == '__main__':
    main()
