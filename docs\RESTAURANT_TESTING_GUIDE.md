# Restaurant Registration Testing Guide

## 🧪 **Complete Testing Process**

### **Phase 1: Restaurant Registration Flow**

#### **Step 1: Start Registration**
1. **Visit Home Page**: `http://localhost:3000/`
2. **Click "Join as Partner"** button in the hero section
3. **Verify redirect** to `/restaurant-partner`

#### **Step 2: Restaurant Partner Landing**
1. **Review landing page** content:
   - Benefits section (Revenue, Customer Base, Analytics, etc.)
   - Requirements section (Business License, Food Safety, etc.)
   - Success testimonials
   - FAQ section
   - Commission structure (15-20%)
2. **Click "Start Application"** button
3. **Verify redirect** to `/register?role=restaurant`

#### **Step 3: User Registration**
1. **Fill registration form**:
   ```
   Name: <PERSON>e
   Email: <EMAIL>
   Phone: +**********
   Password: SecurePass123
   Role: Restaurant Owner (should be pre-selected)
   ```
2. **Submit registration**
3. **Verify redirect** to email verification with restaurant redirect

#### **Step 4: Email Verification**
1. **Check email** for OTP code
2. **Enter OTP** in verification form
3. **Verify redirect** to login with restaurant application redirect
4. **Login** with credentials
5. **Verify auto-redirect** to `/register-restaurant`

#### **Step 5: Restaurant Application (5 Steps)**

**Step 1: Basic Information**
```
Restaurant Name: Test Afghan Kitchen
Email: <EMAIL>
Phone: +**********
Owner Name: John Doe
Description: Authentic Afghan cuisine with traditional recipes passed down through generations. We serve fresh, flavorful dishes made with high-quality ingredients.
```

**Step 2: Location & Hours**
```
Street Address: 123 Main Street
City: New York
State: NY
Postal Code: 10001
Opening Time: 09:00
Closing Time: 22:00
```

**Step 3: Business Documents**
- Upload **business license** (PDF/JPG file)
- Upload **food safety license** (PDF/JPG file)
- Verify file upload indicators show green checkmarks

**Step 4: Menu & Photos**
- Upload **restaurant logo** (JPG/PNG file)
- Upload **banner/cover photo** (JPG/PNG file)
- Select **cuisine types**: Afghan, Middle Eastern
- Optionally upload menu file

**Step 5: Payment & Review**
```
Bank Name: Test Bank
Account Number: **********
Routing Number: *********
Account Holder Name: John Doe
```
- Review application summary
- **Submit application**

#### **Step 6: Registration Success**
1. **Verify success page** appears
2. **Check application status**: "Pending Review"
3. **Note restaurant ID** for admin testing

### **Phase 2: Admin Verification Process**

#### **Step 1: Admin Login**
1. **Login as admin** user
2. **Navigate to**: `/admin/restaurant-approvals`

#### **Step 2: Review Application**
1. **Find "Test Afghan Kitchen"** in pending applications
2. **Click "View Details"** to see full application
3. **Review all sections**:
   - Restaurant information
   - Business details
   - Uploaded documents
   - Images (logo and banner)
4. **Verify all data** matches what was submitted

#### **Step 3: Test Approval Process**
1. **Click "Approve"** button
2. **Add approval notes** (optional)
3. **Confirm approval**
4. **Verify status** changes to "Approved"
5. **Check email notification** sent to restaurant owner

### **Phase 3: Post-Approval Testing**

#### **Step 1: Restaurant Onboarding**
1. **Login as restaurant owner**
2. **Navigate to**: `/restaurant/onboarding`
3. **Complete 4-step onboarding**:
   - Step 1: Welcome & Setup
   - Step 2: Menu Setup
   - Step 3: Payment & Delivery
   - Step 4: Training & Launch
4. **Mark tasks as complete** in each step
5. **Click "Complete Onboarding"**

#### **Step 2: Restaurant Operations**
1. **Navigate to**: `/restaurant` (main dashboard)
2. **Test menu management**: `/restaurant/menu`
   - Add menu categories
   - Add menu items with photos and prices
3. **Test profile management**: `/restaurant/profile`
   - Update restaurant information
   - Change operating hours
   - Upload new photos
4. **Test order management**: `/restaurant/orders`
   - View order interface
   - Test order status updates

### **Phase 4: API Testing (Optional)**

#### **Step 1: Access API Test Suite**
1. **Navigate to**: `/admin/restaurant-api-test`
2. **Upload test files** (logo and banner)
3. **Run API tests**:
   - Test CREATE restaurant
   - Test GET restaurant
   - Test GET all restaurants
   - Test UPDATE (PUT) restaurant
   - Test PATCH restaurant
   - Test DELETE restaurant

#### **Step 2: Verify API Responses**
1. **Check test results** for each operation
2. **Verify success/error messages**
3. **Review response data** in expandable sections
4. **Test file upload functionality**

## 🔍 **What to Verify During Testing**

### **User Experience**
- ✅ Smooth navigation between steps
- ✅ Progress indicators work correctly
- ✅ Form validation prevents invalid submissions
- ✅ Error messages are clear and helpful
- ✅ File uploads work and show previews
- ✅ Mobile responsiveness on all screens

### **Security & Access Control**
- ✅ Non-restaurant users redirected from `/register-restaurant`
- ✅ Role-based access control works
- ✅ Authentication required for protected routes
- ✅ File upload security (file type validation)

### **API Integration**
- ✅ All CRUD operations work correctly
- ✅ File uploads include logo and banner
- ✅ Address stored as raw JSON format
- ✅ Error handling for API failures
- ✅ Loading states during API calls

### **Email & Notifications**
- ✅ Registration confirmation emails
- ✅ Email verification with OTP
- ✅ Approval/rejection notifications
- ✅ Proper email formatting and content

## 🚨 **Common Issues to Watch For**

### **Registration Issues**
- Role not pre-selected when coming from partner page
- Email verification not redirecting to restaurant application
- File uploads failing or not showing previews
- Form validation not working on step transitions

### **Admin Issues**
- Applications not appearing in admin dashboard
- Document downloads not working
- Approval/rejection emails not sending
- Status updates not reflecting in database

### **API Issues**
- Authentication token not included in requests
- File uploads not using multipart/form-data
- Address not formatted as raw JSON
- Error responses not handled gracefully

## ✅ **Success Criteria**

The restaurant registration system is working correctly when:

1. **Complete Flow**: User can go from discovery to operations without issues
2. **Role Protection**: Access control works as expected
3. **API Integration**: All CRUD operations function properly
4. **File Uploads**: Documents and images upload successfully
5. **Email System**: All notifications are sent and received
6. **Admin Workflow**: Approval process works smoothly
7. **Mobile Experience**: All features work on mobile devices

## 🎯 **Next Steps After Testing**

1. **Fix any issues** found during testing
2. **Clean up unused code** (see cleanup guide)
3. **Optimize performance** if needed
4. **Add analytics tracking** for conversion optimization
5. **Prepare for production deployment**
