#!/usr/bin/env python3
"""
<PERSON>ript to add sample restaurants with complete menu data for testing
"""
import os
import sys
import django
from django.core.files.base import ContentFile
from PIL import Image
import io
import json

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'afghansufra.settings')
django.setup()

from restaurant.models import Restaurant, MenuCategory, MenuItem
from django.contrib.auth.models import User

def create_image(width, height, color, text=""):
    """Create a simple colored image with optional text"""
    img = Image.new('RGB', (width, height), color=color)
    if text:
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(img)
        try:
            font = ImageFont.load_default()
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            x = (width - text_width) // 2
            y = (height - text_height) // 2
            draw.text((x, y), text, fill='white', font=font)
        except:
            pass
    
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG', quality=90)
    img_bytes.seek(0)
    return img_bytes.getvalue()

def create_restaurant_with_menu(restaurant_data, categories_data, menu_items_data):
    """Create a restaurant with complete menu structure"""
    
    # Create restaurant
    restaurant = Restaurant.objects.create(
        name=restaurant_data['name'],
        description=restaurant_data['description'],
        contact_number=restaurant_data['contact_number'],
        opening_time=restaurant_data['opening_time'],
        closing_time=restaurant_data['closing_time'],
        delivery_fee=restaurant_data['delivery_fee'],
        min_order_amount=restaurant_data['min_order_amount'],
        average_preparation_time=restaurant_data['average_preparation_time'],
        is_active=True,
        is_verified=True,
        rating=restaurant_data['rating'],
        accepts_cash=True,
        accepts_card=True,
        accepts_online_payment=True,
        address_detail={
            "street": restaurant_data['address']['street'],
            "city": restaurant_data['address']['city'],
            "state": restaurant_data['address']['state'],
            "postal_code": restaurant_data['address']['postal_code'],
            "country": restaurant_data['address']['country'],
            "coordinates": restaurant_data['address']['coordinates']
        }
    )
    
    # Add logo and banner
    logo_img = create_image(200, 200, restaurant_data['logo_color'], restaurant_data['name'][:4])
    banner_img = create_image(800, 400, restaurant_data['banner_color'], restaurant_data['name'])
    
    restaurant.logo.save(
        f"{restaurant_data['name'].lower().replace(' ', '_')}_logo.jpg",
        ContentFile(logo_img),
        save=False
    )
    restaurant.banner.save(
        f"{restaurant_data['name'].lower().replace(' ', '_')}_banner.jpg", 
        ContentFile(banner_img),
        save=False
    )
    restaurant.save()
    
    print(f"✅ Created restaurant: {restaurant.name}")
    
    # Create menu categories
    created_categories = {}
    for cat_data in categories_data:
        category = MenuCategory.objects.create(
            restaurant=restaurant,
            name=cat_data['name'],
            description=cat_data['description'],
            is_active=True
        )
        created_categories[cat_data['name']] = category
        print(f"  📂 Created category: {category.name}")
    
    # Create menu items
    for item_data in menu_items_data:
        category = created_categories[item_data['category']]
        
        menu_item = MenuItem.objects.create(
            category=category,
            name=item_data['name'],
            description=item_data['description'],
            price=item_data['price'],
            preparation_time=item_data['preparation_time'],
            is_vegetarian=item_data['is_vegetarian'],
            is_available=True
        )
        
        # Add item image if specified
        if 'image_color' in item_data:
            item_img = create_image(400, 300, item_data['image_color'], item_data['name'][:8])
            menu_item.image.save(
                f"{item_data['name'].lower().replace(' ', '_')}.jpg",
                ContentFile(item_img),
                save=False
            )
            menu_item.save()
        
        print(f"    🍽️ Created menu item: {menu_item.name} - ${menu_item.price}")
    
    return restaurant

def main():
    print("🏪 Adding sample restaurants with complete menu data...")
    
    # Restaurant 1: Kabul Palace
    kabul_palace_data = {
        'name': 'Kabul Palace',
        'description': 'Authentic Afghan cuisine in the heart of the city. Experience traditional flavors with modern presentation.',
        'contact_number': '+93 70 123 4567',
        'opening_time': '10:00:00',
        'closing_time': '23:00:00',
        'delivery_fee': '5.99',
        'min_order_amount': '15.00',
        'average_preparation_time': 35,
        'rating': '4.7',
        'logo_color': '#8B4513',
        'banner_color': '#D2691E',
        'address': {
            'street': '123 Afghan Street',
            'city': 'Kabul',
            'state': 'Kabul Province',
            'postal_code': '1001',
            'country': 'Afghanistan',
            'coordinates': {'lat': 34.5553, 'lng': 69.2075}
        }
    }
    
    kabul_categories = [
        {'name': 'Appetizers', 'description': 'Traditional Afghan starters'},
        {'name': 'Main Dishes', 'description': 'Hearty Afghan main courses'},
        {'name': 'Rice Dishes', 'description': 'Aromatic rice specialties'},
        {'name': 'Beverages', 'description': 'Traditional and modern drinks'},
        {'name': 'Desserts', 'description': 'Sweet Afghan treats'}
    ]
    
    kabul_menu_items = [
        # Appetizers
        {'category': 'Appetizers', 'name': 'Mantu', 'description': 'Steamed dumplings filled with seasoned ground beef, topped with yogurt and lentil sauce', 'price': '12.99', 'preparation_time': 25, 'is_vegetarian': False, 'image_color': '#FFE4B5'},
        {'category': 'Appetizers', 'name': 'Bolani', 'description': 'Crispy flatbread stuffed with potatoes and herbs, served with chutney', 'price': '8.99', 'preparation_time': 15, 'is_vegetarian': True, 'image_color': '#DEB887'},
        {'category': 'Appetizers', 'name': 'Ashak', 'description': 'Leek-filled dumplings topped with meat sauce and yogurt', 'price': '13.99', 'preparation_time': 30, 'is_vegetarian': False, 'image_color': '#F0E68C'},
        
        # Main Dishes
        {'category': 'Main Dishes', 'name': 'Kabuli Pulao', 'description': 'Traditional Afghan rice dish with tender lamb, carrots, and raisins', 'price': '18.99', 'preparation_time': 45, 'is_vegetarian': False, 'image_color': '#CD853F'},
        {'category': 'Main Dishes', 'name': 'Lamb Karahi', 'description': 'Tender lamb cooked with tomatoes, onions, and aromatic spices', 'price': '22.99', 'preparation_time': 40, 'is_vegetarian': False, 'image_color': '#A0522D'},
        {'category': 'Main Dishes', 'name': 'Chicken Tikka', 'description': 'Marinated chicken grilled to perfection with Afghan spices', 'price': '16.99', 'preparation_time': 35, 'is_vegetarian': False, 'image_color': '#D2691E'},
        
        # Rice Dishes
        {'category': 'Rice Dishes', 'name': 'Qabili Palau', 'description': 'Fragrant basmati rice with lamb, carrots, and almonds', 'price': '19.99', 'preparation_time': 50, 'is_vegetarian': False, 'image_color': '#DAA520'},
        {'category': 'Rice Dishes', 'name': 'Vegetable Biryani', 'description': 'Aromatic rice with mixed vegetables and traditional spices', 'price': '14.99', 'preparation_time': 35, 'is_vegetarian': True, 'image_color': '#228B22'},
        
        # Beverages
        {'category': 'Beverages', 'name': 'Afghan Tea', 'description': 'Traditional green tea with cardamom and sugar', 'price': '3.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#8FBC8F'},
        {'category': 'Beverages', 'name': 'Doogh', 'description': 'Refreshing yogurt drink with mint and cucumber', 'price': '4.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#F0FFFF'},
        
        # Desserts
        {'category': 'Desserts', 'name': 'Firni', 'description': 'Creamy rice pudding with cardamom and pistachios', 'price': '6.99', 'preparation_time': 10, 'is_vegetarian': True, 'image_color': '#FFF8DC'},
        {'category': 'Desserts', 'name': 'Baklava', 'description': 'Layers of phyllo pastry with nuts and honey syrup', 'price': '7.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#F4A460'}
    ]
    
    create_restaurant_with_menu(kabul_palace_data, kabul_categories, kabul_menu_items)

    # Restaurant 2: Herat Garden
    herat_garden_data = {
        'name': 'Herat Garden',
        'description': 'Fresh and healthy Afghan cuisine with a modern twist. Farm-to-table ingredients.',
        'contact_number': '+93 70 987 6543',
        'opening_time': '11:00:00',
        'closing_time': '22:00:00',
        'delivery_fee': '4.99',
        'min_order_amount': '12.00',
        'average_preparation_time': 30,
        'rating': '4.5',
        'logo_color': '#228B22',
        'banner_color': '#32CD32',
        'address': {
            'street': '456 Garden Avenue',
            'city': 'Herat',
            'state': 'Herat Province',
            'postal_code': '3001',
            'country': 'Afghanistan',
            'coordinates': {'lat': 34.3482, 'lng': 62.1997}
        }
    }

    herat_categories = [
        {'name': 'Salads & Appetizers', 'description': 'Fresh and healthy starters'},
        {'name': 'Grilled Specialties', 'description': 'Perfectly grilled meats and vegetables'},
        {'name': 'Traditional Stews', 'description': 'Hearty Afghan stews and curries'},
        {'name': 'Fresh Juices', 'description': 'Freshly squeezed fruit juices'},
        {'name': 'Healthy Desserts', 'description': 'Light and nutritious sweet treats'}
    ]

    herat_menu_items = [
        # Salads & Appetizers
        {'category': 'Salads & Appetizers', 'name': 'Afghan Salad', 'description': 'Fresh tomatoes, cucumbers, onions with pomegranate dressing', 'price': '7.99', 'preparation_time': 10, 'is_vegetarian': True, 'image_color': '#90EE90'},
        {'category': 'Salads & Appetizers', 'name': 'Hummus Platter', 'description': 'Creamy hummus with fresh vegetables and Afghan bread', 'price': '9.99', 'preparation_time': 8, 'is_vegetarian': True, 'image_color': '#F5DEB3'},

        # Grilled Specialties
        {'category': 'Grilled Specialties', 'name': 'Seekh Kebab', 'description': 'Spiced ground beef skewers grilled over open flame', 'price': '15.99', 'preparation_time': 25, 'is_vegetarian': False, 'image_color': '#CD5C5C'},
        {'category': 'Grilled Specialties', 'name': 'Grilled Vegetables', 'description': 'Seasonal vegetables marinated and grilled to perfection', 'price': '11.99', 'preparation_time': 20, 'is_vegetarian': True, 'image_color': '#9ACD32'},

        # Traditional Stews
        {'category': 'Traditional Stews', 'name': 'Qorma Sabzi', 'description': 'Herb stew with tender lamb and kidney beans', 'price': '17.99', 'preparation_time': 40, 'is_vegetarian': False, 'image_color': '#6B8E23'},
        {'category': 'Traditional Stews', 'name': 'Dal Chawal', 'description': 'Lentil curry served with fragrant basmati rice', 'price': '12.99', 'preparation_time': 25, 'is_vegetarian': True, 'image_color': '#DAA520'},

        # Fresh Juices
        {'category': 'Fresh Juices', 'name': 'Pomegranate Juice', 'description': 'Fresh pomegranate juice, rich in antioxidants', 'price': '5.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#DC143C'},
        {'category': 'Fresh Juices', 'name': 'Mango Lassi', 'description': 'Creamy mango yogurt drink with cardamom', 'price': '4.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#FFD700'},

        # Healthy Desserts
        {'category': 'Healthy Desserts', 'name': 'Date & Nut Rolls', 'description': 'Natural dates stuffed with almonds and pistachios', 'price': '6.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#8B4513'},
    ]

    create_restaurant_with_menu(herat_garden_data, herat_categories, herat_menu_items)

    # Restaurant 3: Mazar Spice House
    mazar_data = {
        'name': 'Mazar Spice House',
        'description': 'Bold flavors and aromatic spices from Northern Afghanistan. Authentic recipes passed down through generations.',
        'contact_number': '+93 70 555 1234',
        'opening_time': '12:00:00',
        'closing_time': '24:00:00',
        'delivery_fee': '6.99',
        'min_order_amount': '20.00',
        'average_preparation_time': 40,
        'rating': '4.8',
        'logo_color': '#B22222',
        'banner_color': '#DC143C',
        'address': {
            'street': '789 Spice Market Road',
            'city': 'Mazar-i-Sharif',
            'state': 'Balkh Province',
            'postal_code': '1701',
            'country': 'Afghanistan',
            'coordinates': {'lat': 36.7081, 'lng': 67.1109}
        }
    }

    mazar_categories = [
        {'name': 'Spicy Appetizers', 'description': 'Fiery starters to awaken your palate'},
        {'name': 'Signature Curries', 'description': 'Rich and aromatic curry dishes'},
        {'name': 'Tandoor Specialties', 'description': 'Clay oven cooked delicacies'},
        {'name': 'Cool Beverages', 'description': 'Refreshing drinks to balance the spice'},
        {'name': 'Sweet Endings', 'description': 'Traditional desserts to cool the palate'}
    ]

    mazar_menu_items = [
        # Spicy Appetizers
        {'category': 'Spicy Appetizers', 'name': 'Spicy Samosas', 'description': 'Crispy pastries filled with spiced potatoes and peas', 'price': '6.99', 'preparation_time': 15, 'is_vegetarian': True, 'image_color': '#FF6347'},
        {'category': 'Spicy Appetizers', 'name': 'Chili Chicken', 'description': 'Tender chicken pieces in spicy tomato sauce', 'price': '11.99', 'preparation_time': 20, 'is_vegetarian': False, 'image_color': '#FF4500'},

        # Signature Curries
        {'category': 'Signature Curries', 'name': 'Beef Vindaloo', 'description': 'Fiery beef curry with potatoes in tangy sauce', 'price': '19.99', 'preparation_time': 45, 'is_vegetarian': False, 'image_color': '#8B0000'},
        {'category': 'Signature Curries', 'name': 'Paneer Makhani', 'description': 'Creamy tomato curry with cottage cheese', 'price': '15.99', 'preparation_time': 30, 'is_vegetarian': True, 'image_color': '#FF6347'},

        # Tandoor Specialties
        {'category': 'Tandoor Specialties', 'name': 'Tandoori Chicken', 'description': 'Whole chicken marinated in yogurt and spices, cooked in clay oven', 'price': '24.99', 'preparation_time': 50, 'is_vegetarian': False, 'image_color': '#FF8C00'},
        {'category': 'Tandoor Specialties', 'name': 'Naan Bread', 'description': 'Fresh baked bread from the tandoor oven', 'price': '3.99', 'preparation_time': 10, 'is_vegetarian': True, 'image_color': '#F5DEB3'},

        # Cool Beverages
        {'category': 'Cool Beverages', 'name': 'Sweet Lassi', 'description': 'Cooling yogurt drink with rose water', 'price': '4.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#FFB6C1'},
        {'category': 'Cool Beverages', 'name': 'Mint Lemonade', 'description': 'Fresh lemon juice with mint and soda', 'price': '3.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#98FB98'},

        # Sweet Endings
        {'category': 'Sweet Endings', 'name': 'Kulfi', 'description': 'Traditional Afghan ice cream with pistachios', 'price': '5.99', 'preparation_time': 5, 'is_vegetarian': True, 'image_color': '#F0E68C'},
    ]

    create_restaurant_with_menu(mazar_data, mazar_categories, mazar_menu_items)

    print("\n" + "="*50)
    print("✅ Sample restaurants created successfully!")
    print("🏪 Created 3 restaurants:")
    print("   1. Kabul Palace - Traditional Afghan cuisine")
    print("   2. Herat Garden - Fresh & healthy options")
    print("   3. Mazar Spice House - Bold & spicy flavors")
    print("\n🎯 You can now test:")
    print("   - Restaurant browsing & search")
    print("   - Menu categories & items")
    print("   - Menu items with images")
    print("   - Add to cart functionality")
    print("   - Order placement & tracking")
    print("   - Different cuisine types")
    print("   - Price filtering")
    print("="*50)

if __name__ == "__main__":
    main()
