import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "../../context/AuthContext";
import {
  Package,
  Clock,
  CheckCircle,
  DollarSign,
  Search,
  RefreshCw,
  Bell,
  Eye,
  User,
  X,
  MessageCircle,
  Users,
  Truck,
} from "lucide-react";
import orderApi from "../../utils/orderApi";
import ManualAgentSelector from "../../components/restaurant/ManualAgentSelector";
// Using regular HTML buttons instead of custom Button component

function Orders() {
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // State management
  const [orders, setOrders] = useState([]);
  const [filteredOrders, setFilteredOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [sortBy, setSortBy] = useState("date");
  const [sortOrder, setSortOrder] = useState("desc");
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [orderDetailsModal, setOrderDetailsModal] = useState(null);
  const [notificationModal, setNotificationModal] = useState(null);
  const [customerChatModal, setCustomerChatModal] = useState(null);
  const [receiptModal, setReceiptModal] = useState(null);
  const [manualAgentSelector, setManualAgentSelector] = useState(null);

  // Simple version with basic API call
  useEffect(() => {
    const loadOrders = async () => {
      // Check authentication before making API call
      if (!user || !user.access_token || user.role !== "restaurant") {
        console.log("Orders: User not authenticated or not restaurant role");
        setLoading(false);
        return;
      }

      try {
        console.log("Loading orders for restaurant user:", user.name);

        const response = await fetch(
          "http://127.0.0.1:8000/api/order/orders/",
          {
            headers: {
              Authorization: `Bearer ${user.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const ordersData = await response.json();
          console.log("Orders loaded:", ordersData.length);
          setOrders(ordersData);
        } else {
          console.error("Failed to load orders:", response.status);
          if (response.status === 401) {
            console.log("Orders: 401 Unauthorized - clearing user data");
            localStorage.removeItem("afghanSofraUser");
          }
        }
      } catch (error) {
        console.error("Error loading orders:", error);
      } finally {
        setLoading(false);
      }
    };

    loadOrders();
  }, [user]);

  // Filter and search functionality
  useEffect(() => {
    let filtered = [...orders];

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter((order) => order.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (order) =>
          order.id.toString().toLowerCase().includes(query) ||
          (order.customer && order.customer.toString().includes(query)) ||
          (order.special_instructions &&
            order.special_instructions.toLowerCase().includes(query))
      );
    }

    // Sort orders
    filtered.sort((a, b) => {
      if (sortBy === "date") {
        const dateA = new Date(a.created_at);
        const dateB = new Date(b.created_at);
        return sortOrder === "asc" ? dateA - dateB : dateB - dateA;
      } else if (sortBy === "amount") {
        const amountA = parseFloat(a.total_amount);
        const amountB = parseFloat(b.total_amount);
        return sortOrder === "asc" ? amountA - amountB : amountB - amountA;
      }
      return 0;
    });

    setFilteredOrders(filtered);
  }, [orders, statusFilter, searchQuery, sortBy, sortOrder]);

  // Refresh orders
  const handleRefreshOrders = async () => {
    setRefreshing(true);
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const response = await fetch(
          "http://127.0.0.1:8000/api/order/orders/",
          {
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const ordersData = await response.json();
          setOrders(ordersData);
        }
      }
    } catch (error) {
      console.error("Error refreshing orders:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // Update order status
  const handleUpdateOrderStatus = async (orderId, newStatus) => {
    try {
      const token = localStorage.getItem("afghanSofraUser");
      if (token) {
        const userData = JSON.parse(token);
        const response = await fetch(
          `http://127.0.0.1:8000/api/order/orders/${orderId}/`,
          {
            method: "PATCH",
            headers: {
              Authorization: `Bearer ${userData.access_token}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ status: newStatus }),
          }
        );

        if (response.ok) {
          // Refresh orders to get updated data
          handleRefreshOrders();
        }
      }
    } catch (error) {
      console.error("Error updating order status:", error);
    }
  };

  // Handle manual agent assignment success
  const handleManualAssignSuccess = (assignmentData) => {
    console.log('Agent assigned successfully:', assignmentData);
    // Refresh orders to show updated assignment
    handleRefreshOrders();
    // Show success message
    alert(`Order assigned to ${assignmentData.agent_name} successfully!`);
  };

  if (loading) {
    return (
      <div className='p-6'>
        <h1 className='mb-4 font-bold text-2xl'>Restaurant Orders</h1>
        <p>Loading orders...</p>
      </div>
    );
  }

  const getStatusBadge = (status) => {
    const statusColors = {
      pending: "bg-yellow-100 text-yellow-800",
      confirmed: "bg-blue-100 text-blue-800",
      preparing: "bg-orange-100 text-orange-800",
      ready: "bg-green-100 text-green-800",
      delivered: "bg-gray-100 text-gray-800",
      cancelled: "bg-red-100 text-red-800",
    };

    return (
      <span
        className={`px-2 py-1 rounded-full text-xs font-medium ${
          statusColors[status] || "bg-gray-100 text-gray-800"
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatTime = (dateString) => {
    return new Date(dateString).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className='bg-gray-50 min-h-screen'>
      {/* Header */}
      <div className='bg-white shadow-sm border-gray-200 border-b'>
        <div className='mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl'>
          <div className='flex justify-between items-center py-6'>
            <div>
              <h1 className='font-bold text-gray-900 text-3xl'>
                Orders Management
              </h1>
              <p className='mt-1 text-gray-600 text-sm'>
                Manage and track all restaurant orders
              </p>
            </div>
            <div className='flex items-center space-x-4'>
              <button
                onClick={handleRefreshOrders}
                disabled={refreshing}
                className='flex items-center space-x-2 hover:bg-gray-50 disabled:opacity-50 px-4 py-2 border border-gray-300 rounded-md'
              >
                <RefreshCw
                  size={16}
                  className={refreshing ? "animate-spin" : ""}
                />
                <span>{refreshing ? "Refreshing..." : "Refresh"}</span>
              </button>
              <button
                onClick={() => setNotificationModal(true)}
                className='flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md text-white'
              >
                <Bell size={16} />
                <span>Notifications</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className='mx-auto px-4 sm:px-6 lg:px-8 py-8 max-w-7xl'>
        {/* Stats Cards */}
        <div className='gap-6 grid grid-cols-1 md:grid-cols-4 mb-8'>
          <div className='bg-white shadow p-6 rounded-lg'>
            <div className='flex items-center'>
              <div className='flex-shrink-0'>
                <Package className='w-8 h-8 text-blue-600' />
              </div>
              <div className='ml-4'>
                <p className='font-medium text-gray-600 text-sm'>
                  Total Orders
                </p>
                <p className='font-bold text-gray-900 text-2xl'>
                  {orders.length}
                </p>
              </div>
            </div>
          </div>

          <div className='bg-white shadow p-6 rounded-lg'>
            <div className='flex items-center'>
              <div className='flex-shrink-0'>
                <Clock className='w-8 h-8 text-yellow-600' />
              </div>
              <div className='ml-4'>
                <p className='font-medium text-gray-600 text-sm'>Pending</p>
                <p className='font-bold text-gray-900 text-2xl'>
                  {orders.filter((o) => o.status === "pending").length}
                </p>
              </div>
            </div>
          </div>

          <div className='bg-white shadow p-6 rounded-lg'>
            <div className='flex items-center'>
              <div className='flex-shrink-0'>
                <CheckCircle className='w-8 h-8 text-green-600' />
              </div>
              <div className='ml-4'>
                <p className='font-medium text-gray-600 text-sm'>Completed</p>
                <p className='font-bold text-gray-900 text-2xl'>
                  {orders.filter((o) => o.status === "delivered").length}
                </p>
              </div>
            </div>
          </div>

          <div className='bg-white shadow p-6 rounded-lg'>
            <div className='flex items-center'>
              <div className='flex-shrink-0'>
                <DollarSign className='w-8 h-8 text-green-600' />
              </div>
              <div className='ml-4'>
                <p className='font-medium text-gray-600 text-sm'>Revenue</p>
                <p className='font-bold text-gray-900 text-2xl'>
                  $
                  {orders
                    .reduce(
                      (sum, order) => sum + parseFloat(order.total_amount),
                      0
                    )
                    .toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className='bg-white shadow mb-6 rounded-lg'>
          <div className='p-6'>
            <div className='gap-4 grid grid-cols-1 md:grid-cols-4'>
              {/* Search */}
              <div className='relative'>
                <Search
                  className='top-1/2 left-3 absolute text-gray-400 -translate-y-1/2 transform'
                  size={20}
                />
                <input
                  type='text'
                  placeholder='Search orders...'
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className='py-2 pr-4 pl-10 border border-gray-300 focus:border-transparent rounded-md focus:ring-2 focus:ring-blue-500 w-full'
                />
              </div>

              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className='px-3 py-2 border border-gray-300 focus:border-transparent rounded-md focus:ring-2 focus:ring-blue-500'
              >
                <option value='all'>All Status</option>
                <option value='pending'>Pending</option>
                <option value='confirmed'>Confirmed</option>
                <option value='preparing'>Preparing</option>
                <option value='ready'>Ready</option>
                <option value='delivered'>Delivered</option>
                <option value='cancelled'>Cancelled</option>
              </select>

              {/* Sort By */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className='px-3 py-2 border border-gray-300 focus:border-transparent rounded-md focus:ring-2 focus:ring-blue-500'
              >
                <option value='date'>Sort by Date</option>
                <option value='amount'>Sort by Amount</option>
              </select>

              {/* Sort Order */}
              <select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value)}
                className='px-3 py-2 border border-gray-300 focus:border-transparent rounded-md focus:ring-2 focus:ring-blue-500'
              >
                <option value='desc'>Newest First</option>
                <option value='asc'>Oldest First</option>
              </select>
            </div>

            {/* Results Summary */}
            <div className='flex justify-between items-center mt-4 text-gray-600 text-sm'>
              <span>
                Showing {filteredOrders.length} of {orders.length} orders
              </span>
              {selectedOrders.length > 0 && (
                <div className='flex items-center space-x-2'>
                  <span>{selectedOrders.length} selected</span>
                  <button className='hover:bg-gray-50 px-3 py-1 border border-gray-300 rounded text-sm'>
                    Bulk Actions
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Orders Table */}
        {filteredOrders.length > 0 ? (
          <div className='bg-white shadow rounded-lg overflow-hidden'>
            <div className='overflow-x-auto'>
              <table className='divide-y divide-gray-200 min-w-full'>
                <thead className='bg-gray-50'>
                  <tr>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      <input
                        type='checkbox'
                        className='border-gray-300 rounded focus:ring-blue-500 text-blue-600'
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedOrders(filteredOrders.map((o) => o.id));
                          } else {
                            setSelectedOrders([]);
                          }
                        }}
                      />
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Order Details
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Customer
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Date & Time
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Amount
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-left uppercase tracking-wider'>
                      Status
                    </th>
                    <th className='px-6 py-3 font-medium text-gray-500 text-xs text-right uppercase tracking-wider'>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {filteredOrders.map((order) => (
                    <tr
                      key={order.id}
                      className='hover:bg-gray-50 transition-colors duration-200'
                    >
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <input
                          type='checkbox'
                          className='border-gray-300 rounded focus:ring-blue-500 text-blue-600'
                          checked={selectedOrders.includes(order.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedOrders([...selectedOrders, order.id]);
                            } else {
                              setSelectedOrders(
                                selectedOrders.filter((id) => id !== order.id)
                              );
                            }
                          }}
                        />
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          <div className='flex-shrink-0 w-10 h-10'>
                            <div className='flex justify-center items-center bg-blue-100 rounded-full w-10 h-10'>
                              <Package className='w-5 h-5 text-blue-600' />
                            </div>
                          </div>
                          <div className='ml-4'>
                            <div className='font-medium text-gray-900 text-sm'>
                              Order #{order.id}
                            </div>
                            <div className='text-gray-500 text-sm'>
                              {order.items?.length || 0} items
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          <div className='flex-shrink-0 w-8 h-8'>
                            <div className='flex justify-center items-center bg-gray-200 rounded-full w-8 h-8'>
                              <User className='w-4 h-4 text-gray-600' />
                            </div>
                          </div>
                          <div className='ml-3'>
                            <div className='font-medium text-gray-900 text-sm'>
                              {order.customer?.name ||
                                `Customer #${order.customer}`}
                            </div>
                            <div className='text-gray-500 text-sm'>
                              {order.customer?.phone || `ID: ${order.customer}`}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-gray-900 text-sm'>
                          {formatDate(order.created_at)}
                        </div>
                        <div className='text-gray-500 text-xs'>
                          {formatTime(order.created_at)}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='font-medium text-gray-900 text-sm'>
                          ${parseFloat(order.total_amount).toFixed(2)}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        {getStatusBadge(order.status)}
                      </td>
                      <td className='px-6 py-4 font-medium text-sm text-right whitespace-nowrap'>
                        <div className='flex justify-end items-center space-x-2'>
                          <button
                            onClick={() => setOrderDetailsModal(order)}
                            className='flex items-center space-x-1 hover:bg-gray-50 px-3 py-1 border border-gray-300 rounded text-sm'
                          >
                            <Eye size={14} />
                            <span>View</span>
                          </button>

                          {order.status === "pending" && (
                            <button
                              onClick={() =>
                                handleUpdateOrderStatus(order.id, "confirmed")
                              }
                              className='flex items-center space-x-1 bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-white text-sm'
                            >
                              <CheckCircle size={14} />
                              <span>Accept</span>
                            </button>
                          )}

                          {order.status === "confirmed" && (
                            <button
                              onClick={() =>
                                handleUpdateOrderStatus(order.id, "preparing")
                              }
                              className='flex items-center space-x-1 bg-orange-600 hover:bg-orange-700 px-3 py-1 rounded text-white text-sm'
                            >
                              <Clock size={14} />
                              <span>Prepare</span>
                            </button>
                          )}

                          {order.status === "preparing" && (
                            <button
                              onClick={() =>
                                handleUpdateOrderStatus(order.id, "ready")
                              }
                              className='flex items-center space-x-1 bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-white text-sm'
                            >
                              <CheckCircle size={14} />
                              <span>Ready</span>
                            </button>
                          )}

                          {order.status === "ready" && !order.delivery_agent && (
                            <button
                              onClick={() => setManualAgentSelector(order)}
                              className='flex items-center space-x-1 bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded text-white text-sm'
                              title="Manually select delivery agent"
                            >
                              <Users size={14} />
                              <span>Assign Agent</span>
                            </button>
                          )}

                          {order.status === "ready" && order.delivery_agent && (
                            <div className='flex items-center space-x-1 bg-green-100 px-3 py-1 rounded text-green-800 text-sm'>
                              <Truck size={14} />
                              <span>Agent: {order.delivery_agent.name || 'Assigned'}</span>
                            </div>
                          )}

                          <button
                            onClick={() => setCustomerChatModal(order)}
                            className='flex items-center space-x-1 hover:bg-gray-50 px-3 py-1 border border-gray-300 rounded text-sm'
                          >
                            <MessageCircle size={14} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className='bg-white shadow p-12 rounded-lg text-center'>
            <div className='mb-4 text-gray-400'>
              <Package size={64} className='mx-auto' />
            </div>
            <h3 className='mb-2 font-medium text-gray-900 text-xl'>
              No orders found
            </h3>
            <p className='mb-6 text-gray-600'>
              {searchQuery || statusFilter !== "all"
                ? "Try adjusting your filters to see more orders."
                : "Orders from customers will appear here when they place orders."}
            </p>
            {(searchQuery || statusFilter !== "all") && (
              <button
                onClick={() => {
                  setSearchQuery("");
                  setStatusFilter("all");
                }}
                className='hover:bg-gray-50 px-4 py-2 border border-gray-300 rounded-md'
              >
                Clear Filters
              </button>
            )}
          </div>
        )}
      </div>

      {/* Order Details Modal */}
      {orderDetailsModal && (
        <div className='z-50 fixed inset-0 flex justify-center items-center bg-black bg-opacity-50'>
          <div className='bg-white mx-4 p-6 rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto'>
            <div className='flex justify-between items-center mb-4'>
              <h2 className='font-bold text-xl'>
                Order Details #{orderDetailsModal.id}
              </h2>
              <button
                onClick={() => setOrderDetailsModal(null)}
                className='text-gray-400 hover:text-gray-600'
              >
                <X size={24} />
              </button>
            </div>

            <div className='space-y-4'>
              <div className='gap-4 grid grid-cols-2'>
                <div>
                  <label className='block font-medium text-gray-700 text-sm'>
                    Status
                  </label>
                  <div className='mt-1'>
                    {getStatusBadge(orderDetailsModal.status)}
                  </div>
                </div>
                <div>
                  <label className='block font-medium text-gray-700 text-sm'>
                    Total Amount
                  </label>
                  <p className='mt-1 font-semibold text-lg'>
                    ${parseFloat(orderDetailsModal.total_amount).toFixed(2)}
                  </p>
                </div>
              </div>

              <div>
                <label className='block font-medium text-gray-700 text-sm'>
                  Order Date
                </label>
                <p className='mt-1'>
                  {formatDate(orderDetailsModal.created_at)} at{" "}
                  {formatTime(orderDetailsModal.created_at)}
                </p>
              </div>

              <div>
                <label className='block font-medium text-gray-700 text-sm'>
                  Customer
                </label>
                <p className='mt-1'>
                  {orderDetailsModal.customer?.name ||
                    `Customer #${orderDetailsModal.customer}`}
                  {orderDetailsModal.customer?.phone && (
                    <span className='ml-2 text-gray-500'>
                      ({orderDetailsModal.customer.phone})
                    </span>
                  )}
                </p>
              </div>

              <div>
                <label className='block font-medium text-gray-700 text-sm'>
                  Items
                </label>
                <div className='space-y-2 mt-1'>
                  {orderDetailsModal.items?.map((item, index) => (
                    <div
                      key={index}
                      className='flex justify-between items-center bg-gray-50 p-3 rounded'
                    >
                      <div>
                        <p className='font-medium'>
                          {item.menu_item?.name || "Item"}
                        </p>
                        <p className='text-gray-600 text-sm'>
                          Quantity: {item.quantity}
                        </p>
                      </div>
                      <p className='font-semibold'>
                        $
                        {parseFloat(
                          item.price_at_order || item.price || 0
                        ).toFixed(2)}
                      </p>
                    </div>
                  )) || (
                    <p className='text-gray-500'>No item details available</p>
                  )}
                </div>
              </div>

              {orderDetailsModal.special_instructions && (
                <div>
                  <label className='block font-medium text-gray-700 text-sm'>
                    Special Instructions
                  </label>
                  <p className='bg-gray-50 mt-1 p-3 rounded'>
                    {orderDetailsModal.special_instructions}
                  </p>
                </div>
              )}
            </div>

            <div className='flex justify-end space-x-3 mt-6'>
              <button
                onClick={() => setOrderDetailsModal(null)}
                className='hover:bg-gray-50 px-4 py-2 border border-gray-300 rounded-md'
              >
                Close
              </button>
              <button
                onClick={() => setReceiptModal(orderDetailsModal)}
                className='bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-md text-white'
              >
                Print Receipt
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Manual Agent Selector Modal */}
      <ManualAgentSelector
        order={manualAgentSelector}
        isOpen={!!manualAgentSelector}
        onClose={() => setManualAgentSelector(null)}
        onAssignSuccess={handleManualAssignSuccess}
      />
    </div>
  );
}

export default Orders;
